<?php

namespace App\Charts;

use ConsoleTVs\Charts\Classes\Chartjs\Chart;

class ReferralsChart extends Chart
{

    public static $colors = [
        'red' => 'rgb(255, 99, 132)',
	    'orange' => 'rgb(255, 159, 64)',
	    'yellow' => 'rgb(255, 205, 86)',
	    'green' => 'rgb(75, 192, 192)',
	    'blue' => 'rgb(54, 162, 235)',
	    'purple' => 'rgb(153, 102, 255)',
	    'grey' => 'rgb(201, 203, 207)'
    ];

    /**
     * Initializes the chart.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }
}
