<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateInsightsPostEngagementsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.insights'))->create('post_engagements', function (Blueprint $table) {
            $table->increments('id');

            $table->integer('post_id')->unsigned()->index(); // internal socialbu post id

            $table->integer('account_id')->unsigned()->index(); // internal socialbu account id

            $table->string('type')->index(); // click, reaction, reach, comments, likes, etc

            $table->unsignedDecimal('value');

            $table->timestamp('timestamp')->useCurrent()->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(config('database.insights'))->dropIfExists('post_engagements');
    }
}
