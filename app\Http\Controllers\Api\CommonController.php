<?php

namespace App\Http\Controllers\Api;

use App\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class CommonController extends Controller
{
    public function ping(Request $request) {
        return 'pong';
    }

    public function logout(Request $request) {
        /** @var User $user */
        $user = $request->user();

        $user->token()->revoke();

        return [
            'success' => true
        ];
    }

    public function user(Request $request) {
        $user = $request->user();
        return [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'company' => $user->company,
            'verified' => $user->verified,
        ];
    }

    public function recentBlogs() {
        $simplepie = new \SimplePie();
        $simplepie->set_feed_url('https://socialbu.com/blog/feed');
        $simplepie->init();

        $get_string_between = function($string, $start, $end){
            $string = ' ' . $string;
            $ini = strpos($string, $start);
            if ($ini == 0) return '';
            $ini += strlen($start);
            $len = strpos($string, $end, $ini) - $ini;
            return substr($string, $ini, $len);
        };

        $data = [];
        foreach ($simplepie->get_items() as $item){
            $description = $item->get_description();
            $imgLink = $get_string_between($description, 'src="', '"');

            $data[] = [
                'title' => (string) $item->get_title(),
                'link' => (string) $item->get_link(),
                'pubDate' => (string) $item->get_date(),
                'img' => $imgLink,
            ];

        }
        return response()->json($data);
    }

}
