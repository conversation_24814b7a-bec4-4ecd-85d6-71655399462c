<?php

namespace App\Http\Controllers\Webhook;

use App\Jobs\ProcessWebhookInstagram;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class InstagramController extends Controller
{
    public function verify(Request $request){
        if ($request->get('hub_verify_token') == config('services.instagram.verification_token')) {
            return $request->get('hub_challenge');
        } else if ($request->get('hub_verify_token') == config('services.facebook.verification_token')) {
            return $request->get('hub_challenge');
        }
        abort(403, 'Invalid token');
    }
    public function handle(Request $request){
        // Validate the integrity and payload and it's origin
        $payload = $request->getContent();
        $hub_sign = $request->header('X-Hub-Signature', 'sha1=null');
        if (hash_equals(explode('=', $hub_sign)[1], hash_hmac('sha1', $payload, config('services.instagram.client_secret')))) {
            dispatch((new ProcessWebhookInstagram($request->input()))->onQueue('fb_webhook')); // will be processed by dedicated queue
            return 'ok';
        } else if (hash_equals(explode('=', $hub_sign)[1], hash_hmac('sha1', $payload, config('services.facebook.client_secret')))) {
            dispatch((new ProcessWebhookInstagram($request->input()))->onQueue('fb_webhook')); // will be processed by dedicated queue
            return 'ok';
        } else {
            \Log::warning('webhook request couldnt be validated: ' . json_encode($request->input()) . ':' . json_encode($request->headers));
        }

        // todo: uncomment this abort() line
        // abort(400);
    }
}
