<?php

namespace App\Respond\Handlers;

use App\InboxConversation;
use App\InboxConversationItem;
use App\Respond\BaseInboxHandler;
use App\SocialContact;
use Illuminate\Database\Eloquent\Collection;

class GoogleYoutube extends BaseInboxHandler
{

    const MAX_POLL_REQUESTS = 100; // max limit of requests for fetching new top-level comments per poll session

    // take note of the processed replies so that we don't process them again
    private $processedReplies = [];

    private $cache = [];

    public function shouldPoll(): bool
    {
        // we can ask to not poll by returning false
        // if we return 'never', then we will not poll again
        return true;
    }

    /**
     * @throws \Exception
     */
    public function poll(): bool
    {
        // we need to fetch new comments.
        // however, YouTube's api doesn't have a way to fetch new comments,
        // so we need to fetch all top-level comments on the channel
        // for new nested comments, we poll by each stored conversation
        $api = $this->getApi();

        $lastThreadTimestamp = $this->getAccount()->getOption('inbox.last_thread_timestamp', 0);

        $pollRequests = 0;

        $pollingGotData = false;

        // if we are supposed to resume our last polling session
        $nextPageToken = $this->getAccount()->getOption('inbox.next_page_token');
        do {
            $commentThreads = $api->commentThreads->listCommentThreads('id,replies,snippet', array_filter([
                'allThreadsRelatedToChannelId' => $this->getAccount()->account_id,
                'maxResults' => 100,
                'moderationStatus' => 'published',
                'order' => 'time',
                'textFormat' => 'html',
                'pageToken' => $nextPageToken,
            ]));

            foreach($commentThreads->getItems() as $commentThread){
                // check if the thread timestamp is less than the last fetched timestamp
                // if it is, then we break out of the loop
                // this is to avoid fetching old comments that we have already processed
                $threadTimestamp = $this->convertTimestamp($commentThread->getSnippet()->getTopLevelComment()->getSnippet()->getPublishedAt());
                if($threadTimestamp <= $lastThreadTimestamp){
                    // we have reached the last thread timestamp, so we can break out of the loop
                    // no need to process any more threads
                    break 2;
                }

                $pollingGotData = true;

                $this->processThread($commentThread, true);

                $lastThreadTimestamp = $threadTimestamp;
            }

            $nextPageToken = $commentThreads->getNextPageToken();

            $pollRequests++;

        } while ($nextPageToken && $pollRequests < self::MAX_POLL_REQUESTS);

        if($nextPageToken){
            // we have more pages to fetch, so we need to save the next page token
            $this->getAccount()->setOption('inbox.next_page_token', $nextPageToken);
        } else {
            // we have fetched all pages, so we need to reset the next page token
            if($this->getAccount()->getOption('inbox.next_page_token')){
                // we only remove the option if it is set
                // this is to avoid unnecessary database calls
                $this->getAccount()->removeOption('inbox.next_page_token');
            }

            if($lastThreadTimestamp) {
                // we also need to update the last thread timestamp
                $this->getAccount()->setOption('inbox.last_thread_timestamp', $lastThreadTimestamp);
            }
        }

        // now, we need to check for new replies to the existing convo items (top-level comments)
        $this->pollToplevelComments($pollingGotData);

        return $pollingGotData;
    }

    /**
     * @throws \Exception
     */
    private function pollToplevelComments(&$pollingGotData)
    {
        $api = $this->getApi();

        $MAX_COMMENTS_PER_BATCH = 100;
        // now, we need to check for new replies to the existing convo items (top-level comments)
        // top-level items will not have their parent_id set
        // first, we use fetched_at timestamp to get videos that need their comments re-polled
        $query = InboxConversation::whereAccountId($this->getAccount()->id)
            ->whereType('video')
            ->where('status', '<>', 'blocked')
            // fetched_at is the timestamp when polling last fetched the conversation items,
            // we should update the conversations if that timestamp is at-least 5 minutes old
            ->where('fetched_at', '<=', now()->subMinutes(1));

        $countQuery = (clone $query)->count();

        if($countQuery > $MAX_COMMENTS_PER_BATCH){
            // we have more than x conversations to process,
            // so we can say we got data; this will ensure the polling is run again asap
            $pollingGotData = true;
        }

        /** @var InboxConversation[]|Collection $conversationsToPoll */
        $conversationsToPoll = $query->limit($MAX_COMMENTS_PER_BATCH)->get();

        foreach($conversationsToPoll as $conversation){

            $conversationGotData = false;

            // now, we have conversations
            // for these, we have to check each of the top-level comments
            // and see if there are any new replies to them
            // conversation items also have fetched_at so use that
            // to check if we need to re-poll
            $query = $conversation->items()
                ->where('type', 'comment')
                ->where('parent_id', null) // this is a top-level comment
                ->where('fetched_at', '<=', now()->subMinutes(1));

            $countQuery = (clone $query)->count();

            if($countQuery > $MAX_COMMENTS_PER_BATCH){
                // we will need to run for this convo again
                $pollingGotData = true;
            }

            /** @var InboxConversationItem[]|Collection $itemsToPoll */
            $itemsToPoll = $query->limit($MAX_COMMENTS_PER_BATCH)->get();

            foreach($itemsToPoll as $item){
                // now, we have the top-level comment
                // we need to check for new replies to this comment
                $commentId = $item->external_id;

                $gotData = false;

                // get replies of $comment
                $pagesFetched = 0;
                $nextPageToken = null;
                do {
                    $replies = $api->comments->listComments('id,snippet', array_filter([
                        'parentId' => $commentId,
                        'maxResults' => 100,
                        'pageToken' => $nextPageToken,
                        'textFormat' => 'html',
                    ]));

                    $pagesFetched++;

                    foreach ($replies->getItems() as $childComment) {

                        $childCommentTimestamp = $this->convertTimestamp($childComment->getSnippet()->getPublishedAt());

                        if($childCommentTimestamp <= $item->fetched_at){
                            // we have reached the last item timestamp, so we can break out of the loop
                            // no need to process any more items
                            break 2;
                        } else if(InboxConversationItem::findItem('comment', $childComment->getId())){
                            // we already have this comment
                            // and comments are ordered by time,
                            // so we can break out of the loop
                            break 2;
                        }

                        $gotData = true;
                        $conversationGotData = true;
                        $pollingGotData = true;
                        $this->processReply($childComment, $item, $conversation);
                    }

                    // get next page token
                    $nextPageToken = $replies->getNextPageToken();
                } while ($nextPageToken && $pagesFetched < 100);

                // if we got data, then we need to update the fetched_at timestamp
                if ($gotData) {
                    // we have new replies to this comment
                    // so reset attempts if needed
                    if ($item->getOption('fetch_attempts')) {
                        $item->removeOption('fetch_attempts');
                    }
                    // update the fetched_at timestamp for the item
                    $item->fetched_at = now()->addMinutes(5);
                    $item->save();
                } else {
                    $attempts = $item->getOption('fetch_attempts', 0);

                    $delayMinutes = $attempts * 60;

                    if ($delayMinutes < 60){
                        $delayMinutes = 60;
                    } else if($delayMinutes > 60 * 24 * 30){ // we fetch it again after 30 days max
                        $delayMinutes = 60 * 24 * 30;
                    }

                    // set the fetched_at timestamp to future
                    // so that we don't poll again for this item for some time
                    $item->fetched_at = now()->addMinutes($delayMinutes);
                    $item->save();

                    // increment attempts
                    $attempts++;
                    $item->setOption('fetch_attempts', $attempts);
                }
            }

            // for conversation, we only update the fetched_at timestamp to now
            // if we have processed all the items or if we have new replies
            if ($countQuery >= $MAX_COMMENTS_PER_BATCH || $conversationGotData) {
                if ($conversation->getOption('fetch_attempts')) {
                    $conversation->removeOption('fetch_attempts');
                }
                // update the fetched_at timestamp for the conversation
                $conversation->fetched_at = now()->addMinutes(5);
                $conversation->save();
            } else {
                $attempts = $conversation->getOption('fetch_attempts', 0);

                $delayMinutes = $attempts * 5;

                if ($delayMinutes < 5){
                    $delayMinutes = 5;
                } else if($delayMinutes > 60 * 24 * 30){ // we fetch it again after 30 days max
                    $delayMinutes = 60 * 24 * 30;
                }

                // set the fetched_at timestamp to future
                // so that we don't poll again for some time
                $conversation->fetched_at = now()->addMinutes($delayMinutes);
                $conversation->save();

                // increment attempts
                $attempts++;
                $conversation->setOption('fetch_attempts', $attempts);
            }

            // at this point, we have processed this conversation and all its top-level items for new replies
        }
    }

    /**
     * This is only called for backfilling or for new comment threads
     * @throws \Exception
     */
    private function processThread(\Google\Service\YouTube\CommentThread $commentThread, bool $processAllReplies = false){

        $video = $this->getVideo($commentThread->getSnippet()->getVideoId());

        $comment = $commentThread->getSnippet()->getTopLevelComment();

        $sender = [
            'external_id' => $comment->getSnippet()->authorChannelId,
            'type' => 'channel',
            'name' => $comment->getSnippet()->authorDisplayName,
            'profile_pic' => $comment->getSnippet()->authorProfileImageUrl,
            'options' => [ // options are used to store additional data
                'permalink' => $comment->getSnippet()->authorChannelUrl,
            ],
        ];

        // if blocked, we skip
        if (SocialContact::shouldIgnore($sender['type'], $sender['external_id'], $this->getAccount()->id)) {
            return;
        }

        // since this is a top-level comment, we can create a new conversation for each comment
        // note that we treat each top-level comment as a new conversation
        $conversation = InboxConversation::findOrCreateConversation([
            'external_id' => $video->getId(),
            'status' => 'closed',
            'type' => 'video', // this is a video that is the object of the comment
            'data' => [
                'video_id' => $comment->getSnippet()->getVideoId() ?? '',
                'original' => $video->toSimpleObject(), // always include original data
                'can_embed' => $video->getStatus()->getEmbeddable(),
            ],
        ], $sender, $this->getAccount());

        // now, let's insert the first item
        $mainItem = InboxConversationItem::createItem('comment', [
            'account' => $this->getAccount(), // we need to pass the account here
            'conversation' => $conversation, // optional but saves a query
            'conversation_type' => 'video',
            'conversation_external_id' => $conversation->external_id,
            'conversation_status' => 'closed', // this is a closed conversation

            'external_id' => $comment->getId(),
            'type' => 'comment',
            'data' => [
                'text' => $comment->getSnippet()->textDisplay, // text should always be present
                'original' => $comment->toSimpleObject(), // always include original data

                // additional data
                // 'video_id' => $comment->getSnippet()->videoId ?? '',
            ],
            'timestamp' => $this->convertTimestamp($comment->getSnippet()->publishedAt),
        ], $sender);

        // at this point, we have the main item, let's insert the replies now
        $replies = $commentThread->getReplies();

        $shouldFetchAllReplies = $commentThread->getSnippet()->getTotalReplyCount() > $replies->count() && $processAllReplies;

        foreach ($replies->getComments() as $childComment) {
            $this->processReply($childComment, $mainItem, $conversation);
        }

        $api = $this->getApi();

        // if we should fetch all replies, we need to fetch them
        if ($shouldFetchAllReplies) {
            // get replies of $comment
            $pagesFetched = 0;
            $nextPageToken = null;
            do {
                $replies = $api->comments->listComments('id,snippet', array_filter([
                    'parentId' => $comment->getId(),
                    'maxResults' => 100,
                    'pageToken' => $nextPageToken,
                    'textFormat' => 'html',
                ]));

                $pagesFetched++;

                foreach ($replies->getItems() as $childComment) {
                    $this->processReply($childComment, $mainItem, $conversation);
                }

                // get next page token
                $nextPageToken = $replies->getNextPageToken();
            } while ($nextPageToken && $pagesFetched < 100);
        }
    }

    /**
     * @throws \Exception
     */
    private function processReply(\Google\Service\YouTube\Comment $childComment, InboxConversationItem $parentItem, InboxConversation $conversation)
    {
        if (in_array($childComment->getId(), $this->processedReplies)) {
            return;
        }

        // take note of the processed replies so that we don't process them again
        $this->processedReplies[] = $childComment->getId();

        $sender = [
            'external_id' => $childComment->getSnippet()->authorChannelId,
            'type' => 'channel',
            'name' => $childComment->getSnippet()->authorDisplayName,
            'profile_pic' => $childComment->getSnippet()->authorProfileImageUrl,
            'options' => [
                'permalink' => $childComment->getSnippet()->authorChannelUrl,
            ],
        ];

        // if blocked, we skip
        if (SocialContact::shouldIgnore($sender['type'], $sender['external_id'], $this->getAccount()->id)) {
            return;
        }

        InboxConversationItem::createItem('comment', [
            'account' => $this->getAccount(), // we need to pass the account here
            'conversation' => $conversation, // optional but saves a query
            'conversation_type' => 'video',
            'conversation_external_id' => $conversation->external_id,
            'conversation_status' => 'closed', // this is a closed conversation

            'parent_id' => $parentItem->id, // this is the parent id of the comment
            'external_id' => $childComment->getId(),
            'type' => 'comment',
            'data' => [
                'text' => $childComment->getSnippet()->textDisplay, // text should always be present
                'original' => $childComment->toSimpleObject(), // always include original data

                // additional data
                'video_id' => $childComment->getSnippet()->videoId ?? '',
                'like_count' => $childComment->getSnippet()->likeCount ?? 0,
            ],
            'timestamp' => $this->convertTimestamp($childComment->getSnippet()->publishedAt),
        ], $sender);
    }

    /**
     * @return \Google\Service\YouTube\Video
     * @throws \Exception
     */
    private function getVideo($videoId)
    {
        if(isset($this->cache[$videoId])){
            return $this->cache[$videoId];
        }

        $api = $this->getApi();

        $videoList = $api->videos->listVideos('id,snippet', [
            'id' => $videoId,
        ]);

        if($videoList->count() > 0){
            $this->cache[$videoId] = $videoList->getItems()[0];

            return $this->cache[$videoId];
        }

        return null;
    }

    /**
     * @throws \Exception
     */
    public function backFill(): void
    {
        // we can get recent 100 comment threads and save them
        $api = $this->getApi();

        $commentThreads = $api->commentThreads->listCommentThreads('id,replies,snippet', [
            'allThreadsRelatedToChannelId' => $this->getAccount()->account_id,
            'maxResults' => 100,
            'moderationStatus' => 'published',
            'order' => 'time',
            'textFormat' => 'html',
        ]);

        // now, let's insert these
        foreach ($commentThreads->getItems() as $commentThread) {
            $this->processThread($commentThread);
        }
    }

    /**
     * @throws \Exception
     */
    public function handle($webhookData): bool
    {
        throw new \Exception('Not supported');
    }

    /**
     * @return \Google\Service\YouTube
     * @throws \Exception
     */
    private function getApi()
    {
        return $this->getAccount()->getApi();
    }

    private function convertTimestamp($timestamp)
    {
        // $timestamp is ISO 8601 date format
        return strtotime($timestamp);
    }
}
