<?php

namespace App\Console\Commands;

use App\CurationItem;
use Illuminate\Console\Command;

class CurationDeleteItems extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'curation:delete_items';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete old items';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     * @throws \Exception
     */
    public function handle()
    {
        // we currently only delete items that are older than 3 months
        CurationItem::where('created_at', '<', now()->subMonths(3))->delete();
    }
}
