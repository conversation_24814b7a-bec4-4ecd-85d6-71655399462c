<?php

namespace App\Jobs;

use App\Account;
use App\Automation;
use App\Feed;
use Facebook\Exceptions\FacebookResponseException;
use Facebook\Facebook;
use GuzzleHttp\Client;
use Illuminate\Bus\Queueable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\File;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Psr\SimpleCache\InvalidArgumentException;

class ProcessWebhookFacebook implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 20;

    protected $data = null; // webhook event object / data

    protected $retryCount = 0;

    /**
     * Create a new job instance.
     *
     * @param array $data
     * @param int $retryCount
     */
    public function __construct(array $data, int $retryCount = 0)
    {
        $this->data = $data;
        $this->retryCount = $retryCount;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        if (!isset($this->data['entry'])) {
            // something is wrong
            $e = new \Exception('`entry` not present in facebook webhook data: ' . json_encode($this->data));
            report($e);
            $this->fail($e);
            return;
        }

        $triggerEvent = function ($method_name, $update, $pageId){
            $method = camel_case($method_name);
            if (method_exists($this, $method)) {
                $this->{$method}($update, $pageId);
                return true;
            } else {
                // something is wrong
                $e = new \Exception('`' . $method . '` method not found. facebook webhook data: ' . json_encode($this->data));
                //not handling pageMention right now, will be handled in new respond code
                if($method !== 'pageMention'){
                    report($e); 
                    $this->fail($e);
                }
                return false;
            }
        };

        // we get all page ids and if any page is pending webhook execution, then we delay this job to prevent race conditions
        $pageIds = [];
        foreach ($this->data['entry'] as $entry) {
            $pageIds[] = $entry['id'];
        }
        foreach ($pageIds as $id){
            if(\Cache::get('FB_WEBHOOK_PROCESSING_PAGE_' . $id)){
                // currently processing by another instance
                // so we delay this job
                dispatch((new self($this->data, $this->retryCount))->onQueue('fb_webhook')->delay(now()->addSeconds( 15 ))); // retry after x seconds
                return;
            }
        }

        // mark processing
        foreach ($pageIds as $id){
            if($id === '153691998150511'){
                // log for aroonima
                \Log::debug('aroonima: ' . json_encode($this->data));
            }
            \Cache::put('FB_WEBHOOK_PROCESSING_PAGE_' . $id, true, now()->addMinutes(2));
        }

        $removeCache = function() use($pageIds){
            foreach ($pageIds as $id){
                \Cache::forget('FB_WEBHOOK_PROCESSING_PAGE_' . $id);
            }
        };

        // process
        foreach ($this->data['entry'] as $entry) {
            if(isset($entry['changes'])) {
                foreach ($entry['changes'] as $change) {
                    $ret = $triggerEvent($this->data['object'] . '_' . $change['field'], $change['value'], $entry['id']);
                    if(!$ret){
                        // unsuccessful, stop
                        $removeCache();
                        return;
                    }
                }
            } else if(isset($entry['messaging'])){
                // for messenger webhooks
                foreach ($entry['messaging'] as $item){
                    $ret = $triggerEvent($this->data['object'] . '_messaging', $item, $entry['id']);
                    if(!$ret){
                        // unsuccessful, stop
                        $removeCache();
                        return;
                    }
                }
            }
        }

        // remove cache
        $removeCache();
    }

    /**
     * Process page rating updates
     *
     * @param array $update
     * @param string $pageId
     * @throws \Exception
     */
    private function pageRatings($update, $pageId){

        /** @var Builder $accountsQuery */
        $accountsQuery = Account::where('account_id', $pageId)->where('active', true);

        if($accountsQuery->count() === 0) {
            // why will this happen?
            //report(new \Exception('Received update for page which doesn\'t exist in database.'));
            return;
        }

        // trigger automation for rating
        if ( $update['verb'] === 'add' && $update['item'] === 'rating' ) {

            /** @var Builder $automationsQuery */
            $automationsQuery = Automation::where('event', 'facebook.review')->where('active', true)->whereIn('tag', ($accountsQuery->get(['id']))->map(function ($account) {
                return 'facebook:' . $account->id;
            }));

            $automationsQuery->chunk(200, function ($automations) use ($update) {
                /** @var Automation[] $automations */

                // trigger automations
                foreach ($automations as $automation) {
                    $automation->execute([
                        'content' => isset($update['review_text']) ? $update['review_text'] : '',
                        'is_positive' => $update['recommendation_type'] !== 'NEGATIVE',
                        'facebook' => [
                            'name' => $update['reviewer_name'],
                        ],
                    ], [
                        'id' => $update['open_graph_story_id'],
                        'type' => 'post',
                        'network' => 'facebook',
                    ]);
                }

            });

            $update['message'] = isset($update['review_text']) ? $update['review_text'] : '';

        }


        // now process it for feed
        if(in_array($update['item'], ['rating', 'comment', 'like', 'reaction'])){
            // process for feed
            // make sure correct post_id is set
            // PAGEID_storyID doesnt work for GET request with fb api. Have to use only og story id
            if($update['item'] === 'rating') {
                $update['_post_id'] = isset($update['post_id']) ? $update['post_id'] : null;
                $update['post_id'] = $update['open_graph_story_id'];
            } else {
                if(!isset($update['post_id'])) $update['post_id'] = $update['open_graph_story_id'];
            }
            if(!isset($update['from'])){
                if(isset($update['reviewer_id'])){
                    if(!isset($update['reviewer_name'])) $update['reviewer_name'] = 'A person';
                    $update['from'] = [
                        'id' => $update['reviewer_id'],
                        'name' => $update['reviewer_name'],
                    ];
                } else if(isset($update['sender_id'])) {
                    if(!isset($update['sender_name'])) $update['sender_name'] = 'A person';
                    $update['from'] = [
                        'id' => $update['sender_id'],
                        'name' => $update['sender_name'],
                    ];
                }
            }
            $this->feedsForPageFeed($accountsQuery, $update, $pageId);
        }
    }

    /**
     * Process automations for pageFeed updates
     *
     * @param Builder $accountsQuery
     * @param array $update
     * @param string $pageId
     * @throws \Exception
     */
    private function automationsForPageFeed(Builder $accountsQuery, $update, $pageId){

        if(in_array($update['item'], ['album', 'like', 'reaction'])){
            return; // no need to process these items
        }

        if ( (isset($update['published']) && !$update['published']) || $update['verb'] !== 'add' || (isset($update['post']) && !$update['post']['is_published']) ) {
            return; // don't process
        }

        if(!isset($update['post_id'])){
            //\Log::info('post id not set');
            return;
        }

        if(!isset($update['from'], $update['from']['id'])){
            return; // example: for new event webhook data
        }
        if(isset($update['comment_id']) && $update['from']['id'] === $pageId){
            //comment from page, so no need to reply
            return;
        }

        $event = isset($update['comment_id']) ? 'facebook.post_reply' : ($update['from']['id'] == $pageId ? 'account_post' : 'facebook.user_post');

        if($event === 'account_post'){
            // no more trigger automation from here
            // new post published automation is only triggered for posts published by us
            return;
        }

        if($event === 'facebook.user_post'){
            return; // temporary: it is buggy, also gets executed for comments (app posts unlimited comments again and again)
        }

        /** @var Builder $automationsQuery */
        $automationsQuery = Automation::where('event', $event)->where('active', true)->whereIn('tag', ($accountsQuery->get(['id']))->map(function ($account) {
            return 'facebook:' . $account->id;
        }));

        // do we need to process this
        if ($automationsQuery->count() === 0) {
            return;
        }

        // get account matching page id
        /** @var Account $account */
        $account = $accountsQuery->latest('updated_at')->first();

        $object_id = $update['post_id']; // primary object id
        $fbFields = 'id,from,message,application';
        $eventData = ['network' => 'facebook', 'type' => 'post', 'id' => $object_id];
        if ($update['item'] === 'comment') {
            $object_id = $update['comment_id'];
            $fbFields = 'id,from,message,application,attachment,can_comment';
            $eventData = ['network' => 'facebook', 'type' => 'comment', 'id' => $object_id];
        }

        // if media is attached
        $media = isset($update['photo']) ? $update['photo'] : (isset($update['photos']) ? $update['photos'][0] : null);
        if (!$media && isset($update['video']))
            $media = $update['video'];

        if(!$media && isset($update['link']) && ($update['item'] === 'photo' || $update['item'] === 'video'))
            $media = $update['link'];

        /** @var Facebook $fb */
        $fb = $account->getApi();
        try {
            $fb_node = $fb->get('/' . $object_id . '?fields=' . $fbFields);
            $res = $fb_node->getGraphNode()->asArray();

            if (isset($res['application']) && $res['application']['id'] == config('services.facebook.client_id')) {
                // don't process posts/comments sent from our own app
                return;
            }

            // gif comments
            if (!$media && isset($res['attachment']))
                $media = $res['attachment']['url'];

            // if comment, set id of comment where we must reply
            if ($update['item'] === 'comment' && !$res['can_comment']) {
                $eventData['id'] = $update['parent_id']; // if we cannot reply to child comment, set parent comment id for reply
            }

        } catch(FacebookResponseException $exception){
            $httpCode = $exception->getHttpStatusCode();
            if($httpCode === 404 || $httpCode === 400){
                return;
            } else if(Str::contains(strtolower($exception->getMessage()), ['missing permissions'])) {
                return;
            }else{
                if ($account->testConnection(true)) {
                    report($exception);
                    if($this->retryCount >= 20){
                        $this->fail($exception);
                    } else {
                        dispatch((new ProcessWebhookFacebook($this->data, $this->retryCount + 1))->onQueue('fb_webhook')->delay(now()->addSeconds(($this->retryCount + 1) * 60)));
                    }
                }
                return;
            }
        } catch (\Exception $e) {
            if ($account->testConnection(true)) {
                report($e);
                if($this->retryCount >= 20){
                    $this->fail($e);
                } else {
                    dispatch((new ProcessWebhookFacebook($this->data, $this->retryCount + 1))->onQueue('fb_webhook')->delay(now()->addSeconds(($this->retryCount + 1) * 60)));
                }
            }
            return;
        }

        $update['message'] = isset($update['message']) ? $update['message'] : '';

        $data = [
            'content' => $update['message'],
            'has_media' => !empty($media),
            'media_url' => $media,
            'post_id' => $update['post_id'],
        ];

        if($event === 'facebook.post_reply' || $event === 'facebook.user_post'){
            $data['facebook'] = [
                'name' => isset($res, $res['from'], $res['from']['name']) && !isset($update['from']['name']) ? $res['from']['name'] : $update['from']['name'],
            ];
        }

        if($event === 'facebook.post_reply'){
            $data['is_account_post'] = $update['from']['id'] == $pageId;
        }


        $automationsQuery->chunk(200, function ($automations) use ($data, $eventData) {
            /** @var Automation[] $automations */

            // trigger automations
            foreach ($automations as $automation) {
                $automation->execute($data, $eventData);
            }

        });
    }

    /**
     * Process feeds for page feed updates
     *
     * @param Builder $accountsQuery
     * @param array $update
     * @param string $pageId
     * @throws \Exception
     */
    private function feedsForPageFeed(Builder $accountsQuery, $update, $pageId){

        // logging / debugging for makesyoulocal pages
        /** @var Account $logAccount */
        $logAccount = Account::where('account_id', $pageId)->first();
        if($logAccount && in_array($logAccount->user_id, [ /* 7226, 7587 */ ])){
            // log
            \Log::info($update);
            report(new \Exception('Debug: ' . json_encode($update)));
        }

        if(in_array($update['item'], ['album'])){
            return; // no need to process these items
        }

        if ( (isset($update['published']) && !$update['published']) || (isset($update['post']) && isset($update['post']['is_published']) && !$update['post']['is_published']) ) {
            //\Log::info('fbwebhook: post not published');
            return; // don't process
        }

        if(!isset($update['post_id'])){
            //\Log::info('fbwebhook: post id not set');
            return;
        }

        if(!isset($update['from']['id'])){
            $update['from']['id'] = null;
        }

        if(in_array($update['item'], ['rating', 'post', 'status', 'comment'])) { // also allow 'rating' because it is for page reviews
            $type = isset($update['comment_id']) ? 'comment' : ($update['from']['id'] == $pageId ? 'account_post' : 'user_post');
        } else {
            $type = $update['item'] === 'like' || $update['item'] === 'reaction' ? 'reaction' : null;
            if(!$type) {
                return;
            } else if($type === 'reaction'){
                // only process event from page

                if(!isset($update['from'], $update['from']['id'])){ // may not be set
                    return;
                }

                if($update['from']['id'] !== $pageId){
                    return;
                }
                if($update['item'] === 'like'){
                    $update['reaction_type'] = 'like'; // standardize
                }
            }
        }

        /** @var Builder $feedsQuery */
        $feedsQuery = Feed::whereHas('accounts', function ($query) use($pageId) {
            $query->where('accounts.account_id', $pageId)->where('accounts.active', true);
        })->orWhereHas('team.accounts', function ($query) use($pageId) {
            $query->where('accounts.account_id', $pageId)->where('accounts.active', true);
        });

        if($feedsQuery->count() === 0){
            // no feeds to process
            return;
        }

        // get account matching page id
        /** @var Account $account */
        $account = $accountsQuery->latest('updated_at')->first();

        $hasPhoto = false;

        if($type === 'comment' || $type === 'user_post'){

            $object_id = $update['post_id']; // primary object id
            $fbFields = 'id,from,message,application,attachments,link';
            if ($update['item'] === 'comment') {
                $object_id = $update['comment_id'];
                $fbFields = 'id,from,message,application,attachment,can_comment';
                if(isset($update['parent_comment_id']) && $update['post_id'] == $update['parent_comment_id']){
                    // fb bug
                    unset($update['parent_comment_id']);
                }
            }

            // if media is attached
            $media = isset($update['photo']) ? $update['photo'] : (isset($update['photos']) ? $update['photos'][0] : null);

            if (!$media && isset($update['video']))
                $media = $update['video'];
            else if($media)
                $hasPhoto = true;

            if(!$media && isset($update['link']) && ($update['item'] === 'photo' || $update['item'] === 'video')) {
                $media = $update['link'];
                if($update['item'] === 'photo'){
                    $hasPhoto = true;
                }
            }

            /** @var Facebook $fb */
            $fb = $account->getApi();

            if($update['item'] === 'rating' && $type === 'user_post'){
                // no need to fetch from api for reviews/ratings
                if(!isset($update['message'])){
                    $update['message'] = isset($update['review_text']) ? $update['review_text'] : '';
                }
            } else {

                try {
                    $fb_node = $fb->get('/' . $object_id . '?fields=' . $fbFields);
                    $res = $fb_node->getGraphNode()->asArray();

                    if(isset($res['attachment']))
                        $update['attachment'] = $res['attachment'];

                    // gif comments
                    if (!$media && isset($res['attachment'])) {
                        $media = $res['attachment']['url'];
                        $hasPhoto = true;
                    }

                    // if comment, set id of comment where we must reply
                    if($update['item'] === 'comment' && !$res['can_comment']){
                        $update['parent_comment_id'] = $update['parent_id']; // if we cannot reply to child comment, set parent comment id for reply
                    }

                    if(!isset($res['message'])) {
                        if(isset($res['link'])) {
                            $res['message'] = $res['link'];
                        } else {
                            $res['message'] = ''; // cases when user shares 'link' etc
                        }
                    }

                    // set from name if not set
                    if(isset($res, $res['from'], $res['from']['name']) && isset($update['from']) && !isset($update['from']['name'])){
                        $update['from']['name'] = $res['from']['name'];
                    }

                    $update['message'] = $res['message'];

                } catch(FacebookResponseException $exception){
                    $httpCode = $exception->getHttpStatusCode();
                    if($httpCode === 404 || $httpCode === 400){
                        // \Log::info('fbwebhook: 400 or 404 for ' . $type . ': feedUpdate(' . json_encode($update) . ', ' . $pageId . ')');
                        return;
                    } else if(Str::contains(strtolower($exception->getMessage()), ['missing permissions'])) {
                        return;
                    } else {
                        if ($account->testConnection(true)) {
                            report($exception);
                            if($this->retryCount >= 20){
                                $this->fail($exception);
                            } else {
                                dispatch((new ProcessWebhookFacebook($this->data, $this->retryCount + 1))->onQueue('fb_webhook')->delay(now()->addSeconds(($this->retryCount + 1) * 60)));
                            }
                        }
                        return;
                    }
                } catch (\Exception $e) {
                    if ($account->testConnection(true)) {
                        report($e);
                        if($this->retryCount >= 20){
                            $this->fail($e);
                        } else {
                            dispatch((new ProcessWebhookFacebook($this->data, $this->retryCount + 1))->onQueue('fb_webhook')->delay(now()->addSeconds(($this->retryCount + 1) * 60)));
                        }
                    }
                    return;
                }
            }

            $update['message'] = isset($update['message']) && !empty($update['message']) ? $update['message'] : '** not available **';

            $update['media'] = $media;

        }

        $feedsQuery->chunk(200, function($feeds) use($pageId, $update, $type, $hasPhoto){
            /** @var Feed[]|Collection $feeds */
            $feeds->each(function($feed) use($pageId, $update, $type, $hasPhoto){
                /** @var Feed $feed */

                $itemKey = null;
                if($type === 'comment'){
                    $itemKey = 'comments';
                } else if($type === 'user_post'){
                    $itemKey = 'posts';
                } else if($update['item'] === 'rating'){
                    $itemKey = 'ratings';
                }
                if($itemKey && !in_array($itemKey, $feed->getItemsToCatch())){
                    // user doesn't want to catch this item so skip
                    return;
                }

                $accounts = $feed->accounts()->where('accounts.account_id', $pageId)->get();
                $accounts->each(function($account) use($type, $update, $feed, $hasPhoto) {

                    if($type === 'comment' || $type === 'user_post'){

                        $postType = $type === 'comment' ? 'comment' : 'post';

                        // fetch attachments
                        $client = new Client();

                        // we grab the attached media, since facebook cdn links expire so we store locally ONLY PHOTOS
                        // we embed facebook video using facebook's own video embed plugin FOR VIDEO
                        if($hasPhoto && $update['media']){
                            $temp = tempnam(sys_get_temp_dir(), config('app.name'));
                            $tries = 0;
                            $done = false;
                            $e = null;
                            do {
                                $e = null;
                                try {
                                    $client->get($update['media'], [
                                        'sink' => $temp,
                                        'allow_redirects' => [
                                            'max' => 10,
                                        ],
                                    ]);
                                    $file = new File($temp);
                                    $path = \Storage::putFile('feeds/' . $feed->id, $file);
                                    $update['attachments'] = [
                                        (object)[
                                            'path' => $path,
                                            'type' => $file->extension(),
                                        ]
                                    ];
                                    @unlink($temp);
                                    $done = true;
                                } catch (\Exception $exception) {
                                    $e = $exception;
                                    if(Str::contains(strtolower($exception->getMessage()), ['not supported'])){
                                        // ignore
                                        $e = null;
                                        break;
                                    }
                                }
                                ++$tries;
                            } while(!$done && $tries < 5);
                            if($e){
                                report($e);
                            }
                            if (empty($update['message'])) {
                                $update['message'] = '(This attachment cannot be displayed)';
                            }
                        }

                        if($update['verb'] === 'remove'){
                            $feed->findAndDeletePost($account, $postType, array_to_object($update));
                        } else if($update['verb'] === 'edit' || $update['verb'] === 'edited'){
                            if($type === 'account_post'){
                                // no need to process post by account
                                return;
                            }
                            $feed->findAndUpdatePost($account, $postType, array_to_object($update));
                        } else {
                            if($type === 'account_post'){
                                // no need to process post by account
                                return;
                            }
                            // probably verb = 'add'
                            $feed->addPost($account, $postType, array_to_object($update));
                        }

                    } else if($type === 'reaction'){

                        // get post
                        $feedPost = $feed->findPost($account, false, array_to_object($update));
                        if(!$feedPost) return; // we don't have a post to apply the reaction to

                        // update / set reaction
                        if($update['verb'] === 'remove'){
                            $feedPost->removeOption('reaction');
                        } else {
                            $feedPost->setOption('reaction', $update['reaction_type']);
                        }
                    }

                });
            });
        });
    }

    /**
     * Handle page feed updates
     *
     * @param array $update
     * @param string $pageId
     * @throws \Exception
     */
    private function pageFeed($update, $pageId)
    {

        if (!isset($update['item'], $update['verb'])) {
            $e = new \Exception('Incomplete data: ' . json_encode($this->data));
            report($e);
            $this->fail($e);
            return;
        }

        if( in_array($update['verb'], ['hide', 'unhide',]) ) {
            // not something we should process
            return;
        }

        /** @var Builder $accountsQuery */
        $accountsQuery = Account::where('account_id', $pageId)->where('active', true);

        if($accountsQuery->count() === 0) {
            // why will this happen?
            //report(new \Exception('Received update for page which doesn\'t exist in database.'));
            return;
        }

        $this->automationsForPageFeed($accountsQuery, $update, $pageId);
        $this->feedsForPageFeed($accountsQuery, $update, $pageId);
    }

    /**
     * Handle page messaging updates
     *
     * @param array $msg
     * @param string $pageId
     * @throws \Exception
     */
    private function pageMessaging($msg, $pageId)
    {
        if(isset($msg['reaction'])){
            return; // we dont show reactions so no need to process
        }
        /** @var Builder $accountsQuery */
        $accountsQuery = Account::where('account_id', $pageId)->where('active', true);

        if($accountsQuery->count() === 0){
            //report(new \Exception('Received update for page which doesn\'t exist in database.'));
            return;
        }

        /** @var Builder $automationsQuery */
        $automationsQuery = Automation::where('event', 'incoming_message')->where('active', true)->whereIn('tag', ($accountsQuery->get(['id']))->map(function ($account) {
            return 'facebook:' . $account->id;
        }));

        /** @var Builder $feedsQuery */
        $feedsQuery = Feed::whereHas('accounts', function ($query) use($pageId) {
            $query->where('accounts.account_id', $pageId)->where('accounts.active', true);
        })->orWhereHas('team.accounts', function ($query) use($pageId) {
            $query->where('accounts.account_id', $pageId)->where('accounts.active', true);
        });

        // check if we should process this page
        if($automationsQuery->count() + $feedsQuery->count() === 0) return; // no need to process this

        // get account matching page id
        /** @var Account $account_ */
        $account_ = $accountsQuery->latest('updated_at')->first();

        // we were using a collection; not needed any more but still untouched
        $newMessages = new Collection();

        $fb = $account_->getApi();

        // last timestamp
        $last_msg_timestamp = $account_->getOption('last_msg_timestamp');
        if (!$last_msg_timestamp) $last_msg_timestamp = time() - (5 * 60);
        $new_last_msg_timestamp = null;

        if(isset($msg['timestamp'])){
            // not sure if we really need this timestamp any more
            $new_last_msg_timestamp = $msg['timestamp'];
        }

        $isSentByPage = $msg['sender']['id'] === $pageId;
        $secondPartyId = $isSentByPage ? $msg['recipient']['id'] : $msg['sender']['id'];

        // get second party details
        try {
            $secondPartyDetails = $fb->get('/' . $secondPartyId . '?fields=name,id')->getGraphNode()->asArray();
        } catch (\Exception $e) {
            $secondPartyDetails = [
                'id' => $secondPartyId,
                'name' => 'A person',
            ];
        }

        $newMessages->push($msg);

        // $newMessages = $newMessages->reverse(); // order by old to recent (process older msgs first)

        if($newMessages->count() > 0){ // count to ho ga hi greater than 0, because that is the reason fb sent webhook

            // normalize
            $newMessages = $newMessages->map(function($msg) use($isSentByPage, $account_, $secondPartyDetails) {
                $medias = collect(isset($msg['message']['attachments']) ? $msg['message']['attachments'] : [])->map(function ($itm) {
                    $itm['_data'] = isset($itm['payload']) ? $itm['payload'] : []; // not sure why; scared to remove
                    $itm['sticker_url'] = isset($itm['payload']['sticker_id']) ? $itm['payload']['sticker_id'] : null; // backwards compatibility
                    return $itm;
                });
                $msg['media_url'] = $medias->first() && isset($medias->first()['_data']['url']) ? $medias->first()['_data']['url'] : null;

                // set data that we expect
                $msg['created_time'] = time();

                $firstPartyDetails = [
                    'id' => $account_->account_id,
                    'name' => $account_->name,
                ];

                $msg['from'] = $isSentByPage ? $firstPartyDetails : $secondPartyDetails;
                $msg['to'] = $isSentByPage ? $secondPartyDetails : $firstPartyDetails;
                $msg['thread_id'] = $secondPartyDetails['id']; // always the id of the other party

                $msg['id'] = $msg['message']['mid'];
                $msg['_message'] = $msg['message'];
                $msg['message'] = isset($msg['message']['text']) ? $msg['message']['text'] : '';
                $msg['sticker_url'] = null;

                return array_to_object($msg);
            });

            $automationsQuery->chunk(200, function ($automations) use ($newMessages, $pageId) {
                /** @var Automation[] $automations */

                // trigger automations
                $userMsgs = $newMessages->filter(function($msg) use($pageId) {
                    return $msg->from->id != $pageId;
                });
                foreach ($automations as $automation) {
                    foreach ($userMsgs as $message) {
                        $automation->execute([
                            'content' => $message->message,
                            'has_media' => !is_null($message->media_url),
                            'media_url' => $message->media_url,
                            'has_sticker' => !is_null($message->sticker_url),
                            'sticker_url' => $message->sticker_url,
                            'facebook' => [
                                'name' => $message->from->name,
                            ],
                        ], [
                            'network' => 'facebook',
                            'type' => 'conversation',
                            'id' => $message->from->id,
                            'from' => [
                                'id' => $message->from->id,
                                'name' => isset($message->from->name) ? $message->from->name : '',
                            ]
                        ]);
                    }
                }

            });

            $feedsQuery->chunk(200, function($feeds) use($newMessages, $pageId){
                /** @var Feed[]|Collection $feeds */

                $feeds->each(function($feed) use($newMessages, $pageId) {
                    /** @var Feed $feed */

                    if(!in_array('messages', $feed->getItemsToCatch())){
                        // skip
                        return;
                    }

                    $messagesToSave = $newMessages->map(function($msg) use($feed){

                        $msg->attachments = [];

                        $client = new Client();

                        if($msg->media_url){
                            $temp = tempnam(sys_get_temp_dir(), config('app.name'));
                            $tries = 0;
                            $done = false;
                            $e = null;
                            do {
                                $e = null;
                                try {
                                    $client->get($msg->media_url, [
                                        'sink' => $temp,
                                    ]);
                                    $file = new File($temp);
                                    $path = \Storage::putFile('feeds/' . $feed->id, $file);
                                    $msg->attachments[] = (object) [
                                        'path' => $path,
                                        'type' => $file->extension(),
                                    ];
                                    @unlink($temp);
                                    $done = true;
                                } catch (\Exception $exception){
                                    $e = $exception;
                                    if(Str::contains(strtolower($exception->getMessage()), ['not supported'])){
                                        // ignore
                                        $e = null;
                                        break;
                                    }
                                }
                                ++$tries;
                            } while(!$done && $tries < 5);
                            if($e){
                                report($e);
                            }
                        }

                        return $msg;
                    });

                    $account = $feed->accounts()->where('accounts.account_id', $pageId)->first();
                    $feed->addPosts($account, 'message', $messagesToSave->toArray());

                });

            });

        }

        // update last msg timestamp
        if($new_last_msg_timestamp) {
            $accountsQuery->chunk(200, function ($accounts) use ($new_last_msg_timestamp) {
                /** @var Account[]|Collection $accounts */
                // update last timestamp of account
                foreach ($accounts as $account) {
                    $account->setOption('last_msg_timestamp', $new_last_msg_timestamp);
                }
            });
        }
    }


}
