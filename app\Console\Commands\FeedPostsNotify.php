<?php

namespace App\Console\Commands;

use App\Feed;
use App\Notifications\NewFeedItem;
use App\User;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class FeedPostsNotify extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'feedposts:notify';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Notify for new feed posts when needed';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        Feed::whereHas('posts', function($query){
            /** @var Builder $query */
            // find only feeds which have unread posts
            return $query->where('read', false)->where('root_id', null);
        })->chunk(200, function ($feeds) {
            /** @var Feed[]|Collection $feeds */
            $feeds->each(function($feed){
                /** @var Feed $feed */

                $config = $feed->getOption('config');

                if(isset($config['notify']) && !empty($config['notify'])) {

                    // check last notification
                    $lastUnreadTimestamp = $feed->getOption('last_unread_email_timestamp');

                    $unreadNestedPostsCount = $feed->posts()
                        ->where('root_id', '<>', null) // find child posts and not root posts/threads
                        ->where('read', false)
                        ->where('created_at', '<', Carbon::now()->subMinutes(30)) // is unread for min. last 30 minutes
                        ->when($lastUnreadTimestamp, function($query, $timestamp){ // check after last email timestamp
                            /** @var Builder $query */
                            return $query->where('created_at', '>', Carbon::createFromTimestamp($timestamp));
                        })
                        ->count();


                    $unreadPostsCount = $feed->posts()
                        ->where('root_id', null) // find root posts/threads
                        ->where('read', false)
                        ->where('created_at', '<', Carbon::now()->subMinutes(30)) // is unread for min. last 30 minutes
                        ->when($lastUnreadTimestamp, function($query, $timestamp){ // check after last email timestamp
                            /** @var Builder $query */
                            return $query->where('created_at', '>', Carbon::createFromTimestamp($timestamp));
                        })
                        ->count();

                    $totalUnread = $unreadNestedPostsCount + $unreadPostsCount;

                    if(!isset($config['notifyFrequency'])){
                        $config['notifyFrequency'] = 'daily';
                    }

                    $notifyFrequency = $config['notifyFrequency'];

                    if ($totalUnread > 0) {

                        $lastEmailCarbon = Carbon::createFromTimestamp($lastUnreadTimestamp);
                        $now = now();

                        if( $notifyFrequency === 'daily' && $now->diffInDays($lastEmailCarbon) < 1 ){
                            return;
                        }

                        foreach ((array) $config['notify'] as $userId) {
                            if ($feed->getUsers()->firstWhere('id', $userId)) { // make sure user is part of the feed
                                /** @var User $user */
                                $user = User::find($userId);
                                if(!$user) continue;
                                $user->notify(new NewFeedItem($feed, $totalUnread)); // notify the user
                            }
                        }

                        // update last timestamp
                        $feed->setOption('last_unread_email_timestamp', time());
                    }
                }

            });
        });

    }
}
