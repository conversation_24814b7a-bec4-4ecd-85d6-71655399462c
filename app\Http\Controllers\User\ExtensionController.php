<?php

namespace App\Http\Controllers\User;

use GuzzleHttp\Exception\ClientException;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Mimey\MimeTypes;
use App\Post;

class ExtensionController extends Controller
{
    /**
     * Load editor for browser extension.
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Http\Response|\Illuminate\View\View
     */
    public function getEditor()
    {
        return view('user.publish.extension_editor');
    }

    public function getMedia(Request $request){

        $media = $request->input('media');

        if(!$media) return abort(400);

        $mimes = new MimeTypes;

        $client = guzzle_client();

        try {
            $response = $client->head($media);
        } catch (\Exception $exception){
            $statusCode = null;
            if($exception instanceof ClientException && $exception->getResponse()){
                $statusCode = $exception->getResponse()->getStatusCode();
            }
            return abort($statusCode ? $statusCode : 400, 'Unable to fetch media at ' . $media . ': ' . $exception->getMessage());
        }

        $mediaMime = $response->getHeader('content-type');
        if(!empty($mediaMime)) $mediaMime = $mediaMime[0];
        if(!$mediaMime){
            abort(400, 'Unable to determine type of media ' . $media);
        }
        $mediaExt = $mimes->getExtension($mediaMime);

        $item_type = null;
        if(in_array($mediaExt, ['mp4', 'm4v', 'mov', 'avi', ])){
            $item_type = 'video';
        } else if(in_array($mediaExt, ['jpg', 'png', 'jpeg', 'gif', ])){
            $item_type = 'image';
        }

        if(!$item_type){
            abort(400, 'Invalid media type: ' . $mediaExt);
        }

        $temp = tempnam(sys_get_temp_dir(), config('app.name'));
        $client = guzzle_client();
        try {
            $client->get($media, [
                'sink' => $temp,
            ]);
            return response()->download($temp, 'media.' . $mediaExt)->deleteFileAfterSend(true);
        } catch (\Exception $exception) {
            @unlink($temp);
            abort(500, $exception->getMessage());
        }

        return abort(400);
    }

    public function getExtensionModalStatus(Request $request)
    {
        $user = $request->user();

        //check extension header
        $extensionHeader = $request->header('X-SocialBu-Extension');
        $extensionModalShown = $user->getOption('extension_modal_shown', false);

        $postCount = Post::where('user_id', $user->id)->count();
        if(!$extensionHeader && !$extensionModalShown && $postCount >= 3){
            $show_extension_modal = true;
        }else {
            $show_extension_modal = false;
        }

        return response()->json(['show_extension_modal' => $show_extension_modal]);
    }

    public function updateExtensionModalStatus(Request $request)
    {
        $user = $request->user();
        $user->setOption('extension_modal_shown', $request->get('extension_modal_shown', true));
        $user->save();

        return response()->json(['message' => 'Extension modal status updated']);
    }

}
