<?php

namespace App\Console\Commands;

use App\Automation;
use Carbon\Carbon;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ClientException;
use GuzzleHttp\Exception\ServerException;
use GuzzleHttp\Pool;
use Guz<PERSON>Http\Psr7\Request;
use Guz<PERSON>Http\Psr7\Response;
use GuzzleHttp\Psr7\UriResolver;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class PollRSS extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'poll:rss';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Poll rss/atom feeds for automations';

    /**
     * The maximum items to process. This also means it will store this much hashes in automation `options`
     *
     * @var int
     */
    protected $maxItems = 20;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     */
    public function handle()
    {
        $this->info('Checking automations to poll feeds');

        $this->processFeeds();

        $this->info('All automations done.');
    }

    /**
     * Process feeds
     */
    private function processFeeds(){
        // TO DO: in the future, minimize processing / requests by increasing the time between checks for accounts which receive messages very rarely
        Automation::where('event', 'rss_new_item')->where('active', true)->chunk(200, function ($automations) {
            /** @var Automation[]|Collection $automations */

            // only process these
            $filteredAutomations = $automations->filter(function($automation){
                return $this->shouldPoll($automation);
            });

            // get all feed urls which we should poll
            $urls = $filteredAutomations->map(function ($automation) {
                return $automation->event_data['url'];
            })->unique();

            $urlsToAutomations = $filteredAutomations->keyBy(function ($automation) {
                return $automation->event_data['url'];
            });

            $this->info('Got ' . $filteredAutomations->count() . ' automations (' . $urls->count() . ' unique urls) to process.');

            $client = new Client([
                'connect_timeout' => 120,
                'read_timeout' => 120,
                'timeout' => 120,
                'headers' => [
                    'User-Agent' => config('app.name') . ' HTTP Agent/1.0',
                    'Accept' => 'application/atom+xml, application/rss+xml, application/rdf+xml;q=0.9, application/xml;q=0.8, text/xml;q=0.8, text/html;q=0.7, unknown/unknown;q=0.1, application/unknown;q=0.1, */*;q=0.1',
                ],
                'verify' => false, // we don't need ssl check, do we?
            ]);

            $requests = function () use ($urls) {
                foreach ($urls as $url) {
                    yield $url => new Request('GET', $url);
                }
            };
            $results = new Collection();
            $pool = new Pool($client, $requests(), [
                'concurrency' => 100,
                'fulfilled' => function ($response, $url) use ($results) {
                    $results->put($url, $response);
                },
                'rejected' => function ($reason, $url) use ($results) {
                    $results->put($url, $reason);
                },
            ]);

            // Initiate the transfers and create a promise
            $promise = $pool->promise();

            // Force the pool of requests to complete.
            try {
                $promise->wait();
            } catch (\Throwable $e) {
                if(!Str::contains($e->getMessage(), ['Cannot change a rejected promise to fulfilled', 'not valid header name'])) {
                    report($e);
                }
                $this->error('Failed to fetch some feeds: ' . $e->getMessage());
            }

            $filteredAutomations->each(function ($automation) use ($results) {
                /** @var Automation $automation */

                $this->info('Processing automation#' . $automation->id);

                $options = $automation->options;

                /** @var Response|\Exception $response */
                $response = $results->get($automation->event_data['url']);

                if (!$response) {
                    // failed
                    $this->onFailure($automation, 'Failed to access ' . $automation->event_data['url'] . '. Please ensure the feed is accessible and re-activate the automation.');
                    return;
                }

                if ($response instanceof \Exception) {
                    if ($response instanceof ServerException || $response instanceof ClientException) {
                        $httpRes = $response->getResponse();
                        if($httpRes) {
                            if(!$automation->getOption('ignore_http_errors')){
                                // automation failure, may be also disable automation if it fails too many times
                                $this->onFailure($automation, 'HTTP Error ' . $httpRes->getStatusCode() . ' ' . $httpRes->getReasonPhrase() . ' while accessing ' . $automation->event_data['url'] . '. Please ensure the feed is accessible and re-activate the automation.');
                            }
                        } else {
                            if(!$automation->getOption('ignore_http_errors')){
                                // automation failure, may be also disable automation if it fails too many times
                                $this->onFailure($automation, 'Connection Error ' . $response->getMessage() . ' while accessing ' . $automation->event_data['url'] . '. Please ensure the feed is accessible and re-activate the automation.');
                            }
                        }
                    } else { // seems abnormal
                        if(Str::contains(strtolower($response->getMessage()), ['could not resolve'])){
                            $this->onFailure($automation, $automation->event_data['url'] . ' is not accessible. Please ensure the feed is accessible and re-activate the automation.', true);
                        } else if(!Str::contains(strtolower($response->getMessage()), ['empty reply from server'])){
                            if(!$automation->getOption('ignore_http_errors')){
                                // automation failure, may be also disable automation if it fails too many times
                                $this->onFailure($automation, 'Connection Error ' . $response->getMessage() . ' while accessing ' . $automation->event_data['url'] . '. Please ensure the feed is accessible and re-activate the automation.');
                            }
                        }
                    }
                    return;
                }

                $body = (string) $response->getBody();

                $feed = new \SimplePie();
                $feed->set_timeout(60); // not really needed if not using url

                $feed->set_raw_data($body);
                $feed->init();

                if ($feed->error()) { // check if its a valid feed
                    $err = $feed->error();
                    if(is_array($err))
                        $err = $err[0];

                    if(empty($feed->raw_data)){
                        \Log::error('Empty RSS: ' .  $body);
//                        report(new \Exception('RSS feed data is empty. HTTP status ' . $response->getStatusCode()));
                    }

                    // failed
                    $this->onFailure($automation, 'The feed at ' . $automation->event_data['url'] . ' is invalid (' . $err . '). Please make sure the feed is a valid RSS/Atom feed and re-activate the automation.');

                    return;
                }

                // if we are here, we have valid feed and ready to process items
                $items = $feed->get_items(0, $this->maxItems);
                $hasDate = true;
                foreach ($items as $item) {
                    if (!$item->get_gmdate('U')) {
                        $hasDate = false;
                    }
                }

                $this->info(count($items) . ' items total in feed' . ($hasDate ? '; has date':''));

                $newItems = new Collection();

                $curTimestamp = time();

                $last_hashes = isset($options['last_hashes']) ? $options['last_hashes'] : null;
                $new_last_hashes = $last_hashes ? (array) $last_hashes : [];

                $last_timestamp = isset($options['last_timestamp']) ? $options['last_timestamp'] : null;
                $new_last_timestamp = null;

                foreach ($items as $item) {
                    if ($hasDate) { // skip all older items
                        if ($last_timestamp && $item->get_gmdate('U') <= $last_timestamp) {
                            continue; // skip the old items
                        } else if (!$last_timestamp && $item->get_gmdate('U') <= $curTimestamp - (5 * 60)) {
                            continue; // first time run and the item is older then current time
                        }
                    } else {
                        // compare hashes
                        if ($last_hashes && in_array($item->get_id(), $last_hashes)) {
                            // skip the old item
                            continue;
                        } else if (!$last_hashes) {
                            // first time run, we only process the first item
                            $newItems->push($item);
                            $new_last_hashes = [];
                            foreach ($items as $itm) {
                                $new_last_hashes[] = $itm->get_id();
                            }
                            break;
                        } else {
                            $new_last_hashes[] = $item->get_id();
                        }
                    }

                    // if we reach here, then this is a new item
                    $newItems->push($item);
                }

                if ($newItems->count() > 0) {

                    $this->info('Found ' . $newItems->count() . ' new items.');

                    if ($hasDate) {
                        // order by date (from old to new)
                        $newItems = $newItems->sortBy(function ($item) {
                            /** @var \SimplePie_Item $item */

                            return $item->get_gmdate('U');
                        });
                        $last_timestamp = $newItems->last()->get_gmdate('U');
                    } else {
                        // order from old to new
                        $newItems = $newItems->reverse();
                    }

                    $this->onHasNewItems($automation, $newItems);

                } else {
                    $this->onNoNewItems($automation);
                }

                $automation->refresh(); // re-sync from db just in case

                if ($hasDate) {
                    if(!$last_timestamp){
                        // set it so next time this will be used to compare
                        $last_timestamp = time();
                    }
                    if ($last_timestamp && (!isset($automation->options['last_timestamp']) || $automation->options['last_timestamp'] != $last_timestamp)) {
                        $options['last_timestamp'] = $last_timestamp;
                        $automation->options = $options;
                        $automation->save();
                    }
                } else {
                    if (($new_last_hashes && !$last_hashes) || ($last_hashes != $new_last_hashes)) {
                        $new_last_hashes = array_slice($new_last_hashes, -100, 100); // only save last 100 entries
                        $options['last_hashes'] = $new_last_hashes;
                        $automation->options = $options;
                        $automation->save();
                    }
                }


            });

        });
    }

    /**
     * @param Automation $automation
     * @param Collection $newItems
     */
    private function onHasNewItems(Automation $automation, Collection $newItems){

        $this->line($newItems->count() . ' new items found');

        // process executions
        $n = 0;
        $newItems->each(function ($item) use ($automation, &$n) {
            /** @var \SimplePie_Item $item */

            $image = '';

            if((!$image || empty($image)) && $item->get_enclosure()){
                if( Str::contains($item->get_enclosure()->get_type(), 'image') ) {
                    $image = $item->get_enclosure()->get_link();
                }
            }

            // load image from meta tags of link
            if( (!$image || empty($image)) && $item->get_link()) {
                // load image from link's meta
                try {
                    $meta_tags = extract_meta_tags($item->get_link());
                    $getTag = function($tag) use($meta_tags, $item){
                        if(isset($meta_tags[$tag]) && !empty($meta_tags[$tag])){
                            /** @var string $image */
                            $image = $meta_tags[$tag];
                            // convert to abs url if it is relative
                            if(!starts_with($image, 'http')){
                                $image = (string) UriResolver::resolve( \GuzzleHttp\Psr7\uri_for($item->get_link()), \GuzzleHttp\Psr7\uri_for($image) );
                            }
                            return $image;
                        }
                        return null;
                    };

                    // first we try the fb tag
                    $image = $getTag('og:image');

                    if(!$image || empty($image)){
                        // then we try the twitter tag
                        $image = $getTag('twitter:image');
                    }

                } catch (\Exception $exception){
                    report($exception);
                }
            }

            // load img from content
            if($item->get_content() && strlen($item->get_content()) > 0 && (!$image || empty($image)) ){
                // load image from content of item if possible
                try {
                    $htmlDOM = new \DOMDocument();
                    $htmlDOM->loadHTML($item->get_content());

                    foreach ($htmlDOM->getElementsByTagName('img') as $tag) {
                        /** @var \DOMElement $tag */
                        $image = $tag->getAttribute('src');
                        break;
                    }
                } catch (\Exception $exception){}
            }

            if((!$image || empty($image)) && $item->get_thumbnail()){
                // load image from item if possible
                $image = is_array($item->get_thumbnail()) ? array_first($item->get_thumbnail()) : $item->get_thumbnail();
            }

            if($image && strlen($image) > 0){
                $decodedImg = htmlspecialchars_decode($image);
                if( $decodedImg !== $image ){
                    $image = $decodedImg;
                }
            }


            if($image && !is_string($image)){
                // something wrong
                report(new \Exception('Not an image: ' . json_encode($image)));
                $image = '';
            }

            $media = $image;

            // check if we have a video, if so, set $media to video url
            if($item->get_enclosure()){
                if( $item->get_enclosure()->get_medium() === 'video' ||  Str::contains($item->get_enclosure()->get_type(), 'video') ) {
                    // have a video url
                    $media = $item->get_enclosure()->get_link();
                }
            }

            ++$n;

            $automation->execute([
                'title' => htmlspecialchars_decode($item->get_title()),
                'description' => htmlspecialchars_decode($item->get_description()),
                'stripped_description' => strip_tags(htmlspecialchars_decode($item->get_description())),
                'link' => $item->get_link(),
                'image' => $image,
                'media_url' => $media,
                'has_image' => ($image && !empty($image)),
                'author' => $item->get_author() ? $item->get_author()->get_name() : '',
                'category' => collect($item->get_categories())->map(function ($category) {
                    /** @var \SimplePie_Category $category */

                    return htmlspecialchars_decode($category->get_label());
                })->toArray(),
            ], [
                'count' => $n * 5, // this is for delaying post by 5 minutes
            ]);
        });

        // reset the attempt count, failures, and everything
        $automation->removeOption('rss_polling');

        // set new values
        $automation->setOption('rss_polling.polled_at', time());
    }

    /**
     * @param Automation $automation
     */
    private function onNoNewItems(Automation $automation){
        // increment the attempt count; used for polling mechanism
        $count = $automation->getOption('rss_polling.attempt');
        $count = $count ? $count : 0;
        ++$count;

        // reset the attempt count, failures, and everything
        $automation->removeOption('rss_polling');

        // set new values
        $automation->setOption('rss_polling.attempt', $count);
        $automation->setOption('rss_polling.polled_at', time());
    }

    /**
     * @param Automation $automation
     * @return int
     */
    private function getCurrentFailures (Automation $automation){
        $fails = $automation->getOption('rss_polling.failures');
        return $fails ? $fails : 0;
    }

    /**
     * @param Automation $automation
     * @return bool
     */
    private function shouldPoll(Automation $automation){

        // check if rss feed is temporarily not available
        $lastFailure = $automation->getOption('rss_polling.failed_at');
        if($lastFailure){ // has failures
            $fails = $this->getCurrentFailures($automation); // existing fails
            if(!should_poll_linear(Carbon::createFromTimestamp($lastFailure), $fails, 10 * 60)){
                return false;
            }
        }

        // check normal last req time
        $lastCheck = $automation->getOption('rss_polling.polled_at');
        if($lastCheck){
            $curAttempt = $automation->getOption('rss_polling.attempt');
            if(!should_poll_linear(Carbon::createFromTimestamp($lastCheck), $curAttempt ? $curAttempt : 0, 2 * 60, 2 * 60 * 60)){
                return false;
            }
        }

        return true;
    }

    /**
     * @param Automation $automation
     * @param $errorMsg
     * @param bool $markFailed
     */
    private function onFailure(Automation $automation, $errorMsg, bool $markFailed = false){
        $fails = $this->getCurrentFailures($automation);
        if($fails >= 10 || $markFailed){

            if(!$automation->getOption('ignore_failure')){

                $deactivate = true;

                $atmUser = $automation->user;
                if($atmUser){
                    if($atmUser->getOption('automation_ignore_failure')){
                        $deactivate = false;
                    }
                }

                if($deactivate) {
                    // deactivate only if the ignore failure option is not set; used for some automations on the request of customers
                    $automation->deactivate($errorMsg);
                }
            }

            // reset the attempt count, failures, and everything
            $automation->removeOption('rss_polling');
        } else {
            ++$fails; // increment the failures
            // save the fails
            $automation->setOption('rss_polling.failures', $fails);
            $automation->setOption('rss_polling.failed_at', time());
            activity('automations')
                ->performedOn($automation)
                ->withProperties([
                    'type' => 'info',
                ])
                ->log('Unable to access the feed. Will retry later.');
        }
    }

}
