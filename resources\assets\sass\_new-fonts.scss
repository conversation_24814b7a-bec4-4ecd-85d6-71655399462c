
// Plus Jakarta Sans
@font-face{
    font-family: "Plus Jakarta Sans";
    src: url("/fonts/plus_jakarta_sans/static/PlusJakartaSans-Regular.ttf") format("truetype");
    font-weight: 400; /* Regular */
    font-style: normal;
}
@font-face{
    font-family: "Plus Jakarta Sans";
    src: url("/fonts/plus_jakarta_sans/static/PlusJakartaSans-Medium.ttf") format("truetype");
    font-weight: 500; /* Medium */
    font-style: normal;
} 
@font-face{
    font-family: "Plus Jakarta Sans";
    src: url("/fonts/plus_jakarta_sans/static/PlusJakartaSans-SemiBold.ttf") format("truetype");
    font-weight: 600; /* semibold */
    font-style: normal;
}
@font-face{
    font-family: "Plus Jakarta Sans";
    src: url("/fonts/plus_jakarta_sans/static/PlusJakartaSans-Bold.ttf") format("truetype");
    font-weight: 700; /* bold */
    font-style: normal;
}
@font-face{
    font-family: "Plus Jakarta Sans";
    src: url("/fonts/plus_jakarta_sans/static/PlusJakartaSans-ExtraBold.ttf") format("truetype");
    font-weight: 800; /* extrabold */
    font-style: normal;
}

// DM Sans
@font-face{
    font-family: "DM Sans";
    src: url("/fonts/dm_sans/static/DMSans-Regular.ttf") format("truetype");
    font-weight: 400; /* Regular */
    font-style: normal;
}
@font-face{
    font-family: "DM Sans";
    src: url("/fonts/dm_sans/static/DMSans-Medium.ttf") format("truetype");
    font-weight: 500; /* Medium */
    font-style: normal;
}
@font-face{
    font-family: "DM Sans";
    src: url("/fonts/dm_sans/static/DMSans-SemiBold.ttf") format("truetype");
    font-weight: 600; /* semibold */
    font-style: normal;
}
@font-face{
    font-family: "DM Sans";
    src: url("/fonts/dm_sans/static/DMSans-Bold.ttf") format("truetype");
    font-weight: 700; /* bold */
    font-style: normal;
}
@font-face{
    font-family: "DM Sans";
    src: url("/fonts/dm_sans/static/DMSans-ExtraBold.ttf") format("truetype");
    font-weight: 800; /* extrabold */
    font-style: normal;
}