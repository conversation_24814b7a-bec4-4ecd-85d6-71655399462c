<?php

namespace App\Console\Commands;

use App\Post;
use Illuminate\Console\Command;

class DeleteOldPosts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'posts:delete_old';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete old posts';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        // delete posts older than 1 yr based on publish_at (and created_at)
        Post::where('publish_at', '<', now()->subYear(2))
            ->where('created_at', '<', now()->subYear(2))
            ->chunk(500, function($posts){

                /** @var Post $post */
                foreach ($posts as $post) {
                    $post->delete(); // so that we can also do post cleanup
                }

                return false; // only one chunk
            });

        // delete posts with error and older than 6 months
        Post::where('published_at', null) // not published yet
            ->where('created_at', '<', now()->subMonths(6))
            ->where('publish_at', '<', now()->subMonths(6))
            ->chunk(500, function($posts){

                /** @var Post $post */
                foreach ($posts as $post) {
                    $post->delete(); // so that we can also do post cleanup
                }

                return false; // only one chunk
            });
    }
}
