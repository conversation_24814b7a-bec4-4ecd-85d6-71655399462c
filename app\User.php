<?php

namespace App;

use App\Helpers\EmailListHelper;
use App\Notifications\PasswordReset;
use App\Notifications\UserResourcesStopped;
use App\Notifications\VerifyEmail;
use App\Notifications\VerifyEmailReminder;
use App\Traits\HasOptions;
use Calebporzio\Onboard\GetsOnboarded;
use Carbon\Carbon;
use Gabievi\Promocodes\Traits\Rewardable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Notifications\Notifiable;
use Illuminate\Contracts\Auth\CanResetPassword as CanResetPasswordContract;
use Illuminate\Auth\Passwords\CanResetPassword;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Lara<PERSON>\Cashier\Billable;
use LVR\Phone\Phone;
use Malahierba\PublicId\PublicId;
use Laravel\Passport\HasApiTokens;

/**
 * App\User
 *
 * @property int $id
 * @property string $name
 * @property string $email
 * @property string $password
 * @property int $verified
 * @property string|null $token
 * @property string|null $company
 * @property array|null $options
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property string|null $stripe_id
 * @property string|null $card_brand
 * @property string|null $card_last_four
 * @property \Illuminate\Support\Carbon|null $trial_ends_at
 * @property int|null $referred_by
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Account[] $accounts
 * @property-read int|null $accounts_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\Laravel\Passport\Client[] $clients
 * @property-read int|null $clients_count
 * @property-read string $public_id
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Team[] $joinedTeams
 * @property-read int|null $joined_teams_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection|\Illuminate\Notifications\DatabaseNotification[] $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Team[] $pendingTeams
 * @property-read int|null $pending_teams_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\Gabievi\Promocodes\Models\Promocode[] $promocodes
 * @property-read int|null $promocodes_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\User[] $referrals
 * @property-read int|null $referrals_count
 * @property-read \App\User $referrer
 * @property-read \Illuminate\Database\Eloquent\Collection|\Laravel\Cashier\Subscription[] $subscriptions
 * @property-read int|null $subscriptions_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Team[] $teams
 * @property-read int|null $teams_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\Laravel\Passport\Token[] $tokens
 * @property-read int|null $tokens_count
 * @method static bool|null forceDelete()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\User newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\User newQuery()
 * @method static \Illuminate\Database\Query\Builder|\App\User onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\User query()
 * @method static bool|null restore()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\User whereCardBrand($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\User whereCardLastFour($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\User whereCompany($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\User whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\User whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\User whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\User whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\User whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\User whereOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\User wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\User whereReferredBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\User whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\User whereStripeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\User whereToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\User whereTrialEndsAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\User whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\User whereVerified($value)
 * @method static \Illuminate\Database\Query\Builder|\App\User withTrashed()
 * @method static \Illuminate\Database\Query\Builder|\App\User withoutTrashed()
 * @mixin \Eloquent
 */
class User extends Authenticatable implements CanResetPasswordContract
{
    use HasApiTokens, Notifiable, SoftDeletes, CanResetPassword, HasOptions, Billable, GetsOnboarded, PublicId, Rewardable;

    static protected $public_id_salt = 'user-glxsftsocial--@';

    static protected $public_id_min_length = 16; // min length for your generated short ids.

    static protected $public_id_alphabet = 'abcdefghijklmnopqrstuvwxyz0123456789';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name', 'email', 'password', 'referred_by', 'verified',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'password', 'remember_token',
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
        'trial_ends_at',
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'options' => 'array',
    ];

    static public function transform(User $user){
        return collect([
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'image' => $user->getPic(),
        ]);
    }


    /**
     * Get the accounts added by this user.
     */
    public function accounts()
    {
        return $this->hasMany('App\Account');
    }

    /**
     * Get the accounts available to the user from teams (grouped by team id).
     * @param bool $simplify
     * @param $include_inactive
     * @return Collection
     */
    public function accountsFromTeams($simplify = false, $include_inactive = false)
    {
        if ($simplify) {
            $teams = $this->joinedTeams()->with('accounts')->get(['id']);
            $accs = new Collection();
            $teams->each(function ($team) use ($accs, $include_inactive) {
                /** @var Team $team */
                if(!$include_inactive)
                    $accounts = $team->accounts->where('active', true);
                else
                    $accounts = $team->accounts;
                foreach ($accounts as $account) {
                    /** @var $account Account */
                    $account->team_id = $team->id;
                    $accs->put($account->id, $account);
                }
            });
            return $accs;
        } else {
            return $this->joinedTeams()->with('accounts')->get(['id', 'name', 'teams.user_id as user_id'])->mapWithKeys(function ($team) use ($include_inactive) {
                /** @var Team $team */
                if(!$include_inactive)
                    $accounts = $team->accounts->where('active', true);
                else
                    $accounts = $team->accounts;
                return [
                    $team->id => $accounts,
                ];
            });
        }
    }

    /**
     * Get all available accounts
     *
     * @param array $cols
     * @param bool $include_inactive
     * @return Collection
     */
    public function getAvailableAccounts(array $cols = ['*'], bool $include_inactive = false){
        return $this
            ->accounts()
            ->when(!$include_inactive, function (/** @var $query Builder */ $query){
                return $query->where('active', true);
            })
            ->get($cols)
            ->merge($this->accountsFromTeams(true, $include_inactive))
            ->mapWithKeys(function ($a){
                return [
                    $a->id => $a
                ];
            });
    }

    /**
     * Get the teams user is part of.
     * @return BelongsToMany
     */
    public function joinedTeams()
    {
        return $this->belongsToMany(Team::class)->wherePivot('approved', true);
    }

    /**
     * Get the teams added by this user.
     */
    public function teams()
    {
        return $this->hasMany(Team::class);
    }

    /**
     * Get pending team invites
     */
    public function pendingTeams()
    {
        return $this->belongsToMany(Team::class)->wherePivot('approved', false);
    }


    public function getFirstName(){
        $parts = explode(' ', $this->name);
        return $parts[0];
    }

    /**
     * Get the user that referred this user.
     */
    public function referrer()
    {
        return $this->hasOne(self::class, 'id', 'referred_by');
    }

    /**
     * Get all referred users.
     * @return HasMany
     */
    public function referrals()
    {
        return $this->hasMany(self::class, 'referred_by')->where('verified', true);
    }

    public function getReferralLink()
    {
        return url('/').'/?ref='.$this->getPublicIdAttribute();
    }

    /**
     * Send the password reset notification.
     *
     * @param  string $token
     * @return void
     */
    public function sendPasswordResetNotification($token)
    {
        $this->notify(new PasswordReset($token));
    }

    /**
     * Send the email verification.
     * @param boolean $invalidate_current_email
     * @param bool $isReminder
     * @return void
     */
    public function sendEmailVerification($invalidate_current_email = false, $isReminder = false)
    {
        if ($invalidate_current_email) {
            $this->verified = false;
            $this->token = str_random(30);
            $this->save();
        }
        if (!$this->verified) {

            // send new user email
            $this->notify($isReminder ? new VerifyEmailReminder() : new VerifyEmail());

            try {
                EmailListHelper::getInstance()->sendEvent($this, 'verification_email_sent', $this->email);
            } catch (\Exception $exception){
                report($exception);
            }

        }
    }

    /**
     * Confirm the user.
     *
     * @return void
     */
    public function confirmEmail()
    {
        $this->verified = true;
        $this->token = null;
        $this->save();
    }

    /**
     * @param $email
     * @throws \Exception
     */
    public function setEmail($email)
    {
        // validate email
        if(\Validator::make(['email' => $email], ['email' => 'required|email'])->fails()){
            throw new \Exception('Invalid email address');
        }

        if($this->email !== $email){
            // log old email
            $pending_email_change = (array) $this->getOption('internal.pending_email_change');
            $pending_email_change[] = $this->email; // logs old email for sec purposes
            $pending_email_change = array_slice($pending_email_change, -50, 50, true); // only keep last 50 emails

            if( count($pending_email_change) >= 15 ){
                // prevent spam behavior
                throw new \Exception('Please contact our support for changing your email.');
            }

            // set updated options
            $this->setOption('internal.pending_email_change', $pending_email_change);

            $this->email = $email;

            $this->save();

            // email changed, send verification email
            $this->sendEmailVerification(true);
        }

    }

    /**
     * Set new password
     * @throws \Exception
     */
    public function setPassword(string $newPassword, string $currentPassword = null){

        if(($currentPassword && \Hash::check($currentPassword, $this->password)) || $this->password == 'no_password'){

            if($this->password !== 'no_password' && $currentPassword === $newPassword){
                throw new \Exception(trans('settings.no_same_password_msg'));
            }

            $this->password = \Hash::make($newPassword);
            $this->save();

            try {
                EmailListHelper::getInstance()->sendEvent($this, 'password_updated');
            } catch (\Exception $exception){
                report($exception);
            }

        } else {
            throw new \Exception(trans('settings.current_password_invalid_msg'));
        }
    }

    public function canApprovePostsForTeam($team_id){
        /** @var Team $team */
        $team = Team::find($team_id);
        if(!$team) return false;

        return $team->hasPermission($this, 'approvals.approve');
    }

    public function canApprovePostsForAccount($account_id){

        /** @var Account $account */
        $account = Account::find($account_id);

        if($account->user_id === $this->id) return true;

        /** @var Team[]|Collection $teams */
        $teams = $this->joinedTeams()->whereHas('accounts', function($query) use($account_id){
            /** @var Builder $query */
            return $query->where('id', $account_id);
        })->get();

        foreach($teams as $team){
            if($this->canApprovePostsForTeam($team->id)){
                return true;
            }
        }
        return false;
    }

    /**
     * Get user timezone
     * @param string $defaultTz
     * @return string
     */
    public function getTimezone($defaultTz = 'America/Los_Angeles')
    {
        return (($tz = $this->getOption('timezone')) ? $tz : $defaultTz);
    }

    /**
     * Set user timezone
     * @param string $timezone
     * @return string
     */
    public function setTimezone($timezone)
    {
        $this->setOption("timezone", $timezone);
        return $this;
    }

    /**
     * Get user phone
     * @return string
     */
    public function getPhone()
    {
        return $this->getOption('phone');
    }

    /**
     * Set user phone
     * @param $phone
     * @throws \Exception
     */
    public function setPhone($phone)
    {
        $example = '******-567-8912';
        if(!$phone){
            $this->removeOption('phone');
            return;
        }
        /*
        // validate phone
        if(!starts_with($phone, '+')){
            throw new \Exception('Phone number should start with the country code. Example: ' . $example);
        }
        */

        if(\Validator::make(['phone' => $phone], ['phone' => new Phone])->passes()){
            $this->setOption('phone', $phone);
        } else {
            throw new \Exception('Invalid phone number. Example format: ' . $example);
        }

    }

    /**
     * Get deal details if user is on deal
     * @return null
     */
    public function getActiveDeal(){
        $planFromSettings = $this->getOption('subscription_plan');
        if($planFromSettings){
            if(now() < Carbon::createFromTimestamp($planFromSettings['valid_until'])) {
                // user is set to a custom plan
                // so use that plan as user's plan
                return $planFromSettings['id'];
            }
        }
        return null;
    }

    /**
     * If we can call this a lifetime deal
     * @return boolean
     */
    public function isLifetimeDeal(){
        $planFromSettings = $this->getOption('subscription_plan');
        if($planFromSettings){
            if(now()->diffInYears(Carbon::createFromTimestamp($planFromSettings['valid_until'])) > 2) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if user has a feature available depending on their plan
     * @param $featureKey
     * @return boolean
     */
    public function planHasFeature($featureKey){
        $planConfig = $this->getPlan();
        return isset($planConfig['features'][$featureKey]) && $planConfig['features'][$featureKey];
    }

    /**
     * Get current plan details
     *
     * @param bool $onlyId
     * @return \Illuminate\Config\Repository|mixed
     */
    public function getPlan(bool $onlyId = false)
    {
        $dealPlan = $this->getActiveDeal();
        if($dealPlan){
            // user is set to a custom plan
            // so use that plan as user's plan
            $planConfig = getPlan($dealPlan, $this);
            if ($planConfig) {
                if ($onlyId) return $dealPlan;

                $customPlanConfig = $this->getOption('plan_config', []);

                // merge limits and features
                $planConfig['limits'] = array_merge($planConfig['limits'], $customPlanConfig['limits'] ?? []);
                $planConfig['features'] = array_merge($planConfig['features'], $customPlanConfig['features'] ?? []);

                return $planConfig;
            }
        }
        if ($this->subscribed('default') && $this->subscription('default')->active()) {
            if ($onlyId) return str_replace_last('-yearly', '', $this->subscription()->stripe_plan);
            return getPlan(str_replace_last('-yearly', '', $this->subscription()->stripe_plan), $this);
        }
        if ($onlyId) return 'free';
        return getPlan('free', $this);
    }

    /**
     * Get current plan usage
     *
     * @param string|null $metric
     * @param null $planId
     * @return \Illuminate\Config\Repository|mixed
     */
    public function getPlanUsage(string $metric = null, $planId = null)
    {

        if(!$planId){
            $planId = $this->getPlan(true);
        }

        $finalLimits = getPlan($planId, $this)['limits'];

        if($planId === 'custom'){
            // merge limits
            $customPlanConfig = $this->getOption('plan_config', []);
            $finalLimits = array_merge($finalLimits, $customPlanConfig['limits'] ?? []);
        }

        if($metric){
            return isset($finalLimits[$metric]) ? $finalLimits[$metric] : 0;
        }
        return $finalLimits;
    }

    /**
     * Check if current plan is yearly
     *
     * @return bool
     */
    public function planIsYearly()
    {
        if ($this->getPlan(true) === 'free') return false;
        if($this->subscription('default')) {
            return Str::contains($this->subscription('default')->stripe_plan, 'yearly');
        }
        return false;
    }

    /**
     * Get subscription
     */
    public function getSubscription()
    {
        return $this->subscription('default');
    }

    /**
     * Get current usage stats
     * @param string $metric
     * @return array|int
     */
    public function getUsage($metric = null)
    {
        /*
         * Required things [
            'scheduled_posts' => 5000,
            'teams' => 5,
            'team_members' => 4, // max members in a team
            'accounts' => 10,
            'feeds' => 8,
            'automations' => 20,
           ]
         */
        $all = [
            'monthly_posts' => function () {
                return Post::whereIn('account_id', $this->accounts()->get(['id'])->map(function($a){return $a->id;}))->where(\DB::raw('DATE_FORMAT(`published_at`, "%M %Y")'), now()->format('F Y'))->count();
            },
            'teams' => function () {
                return Team::ofUser($this->id)->count();
            },
            'team_members' => function () {
                return $this->teams()->get(['id'])->max(function ($team) {
                    /** @var Team $team */
                    return $team->members()->count();
                });
            },
            'accounts' => function () {
                return Account::ofUser($this->id)->count();
            },
            'feeds' => function () {
                return Feed::ofUser($this->id)->count();
            },
            'queues' => function () {
                return PublishQueue::ofUser($this->id)->count();
            },
            // get automations of user and all involving user accounts
            'automations' => function () {
                return Automation::userAutomations($this->id)->count();
            },
            'generated_content' => function () {
                return GeneratedContent::where('is_bad', false)->whereIn('account_id', $this->accounts()->get(['id'])->map(function($a){return $a->id;}))->where(\DB::raw('DATE_FORMAT(`created_at`, "%M %Y")'), now()->format('F Y'))->count();
            },
        ];
        if($metric){
            return isset($all[$metric]) ? $all[$metric]() : 0;
        }
        return collect($all)->map(function($cb){
            return $cb();
        })->toArray();
    }

    /**
     * Check if user can use $plan
     * @param string $plan
     * @param bool $getError
     * @return bool|string
     */
    public function canUsePlan($plan, $getError = false)
    {
        if (!config('plans.' . $plan)) {
            if ($getError) return 'Invalid plan';
            return false;
        }

        if(in_array($plan, ['basic', 'standard', 'plus', 'plus-ultra'])){
            if($getError) return 'This plan is not offered anymore.';
            return false;
        }

        $planDetails = getPlan($plan, $this);
        $usage = $this->getUsage();
        foreach ($planDetails['limits'] as $key => $value) {
            if (isset($usage[$key]) && $usage[$key] > $value) {
                // user usage is more than the plan's allowed maximum
                if ($getError)
                    return 'Your current usage of ' . $usage[$key] . ' ' . title_case($key) . ' is more than the ' . title_case($plan) . ' plan\'s allowed usage of maximum ' . $value . ' ' . title_case($key);
                return false;
            }
        }
        return true;
    }

    /**
     * Apply Stripe coupon if needed (automatically)
     * @return void
     */
    public function applyCouponIfNeeded()
    {
        if(!$this->stripe_id){
            // not created in stripe yet, so can't apply coupon
            return;
        }

        $code = $this->getOption('stripe_coupon'); // coupon code

        if(!$code) return;

        $appliedCodes = $this->getOption('applied_stripe_coupons', []); // applied coupon codes if any

        if(in_array($code, $appliedCodes)){
            // was already applied
            return;
        }

        // actually apply coupon in stripe
        try {
            $this->applyCoupon($code);

            // save coupon history
            $appliedCodes[] = $code;
            $this->setOption('applied_stripe_coupons', $appliedCodes);

        } catch (\Exception $e) {
            // failed to apply coupon
            // probably coupon is invalid or doesn't exist in stripe
            // log error
            \Log::error('Failed to apply coupon for user: ' . $this->id . ' - ' . $e->getMessage());
        }

        // remove coupon from user
        $this->removeOption('stripe_coupon');
    }

    /**
     * Get Stripe Coupon object if any
     * @return \Stripe\Coupon|null
     */
    public function getCoupon()
    {
        $code = $this->getOption('stripe_coupon');

        $couponObject = null;

        if(!$code) {
            // fetch from stripe subscription (only for users with stripe subscription)
            if($this->subscription('default')){
                $stripeSub = $this->subscription('default')->asStripeSubscription();
                if($stripeSub->discount){
                    $code = $stripeSub->discount->coupon->id;
                    $couponObject = $stripeSub->discount->coupon;
                }
                if(!$code){
                    // fetch from stripe customer
                    $stripeCus = $this->asStripeCustomer();
                    if($stripeCus->discount){
                        $code = $stripeCus->discount->coupon->id;
                        $couponObject = $stripeCus->discount->coupon;
                    }
                }
            }
        } else {
            try {
                $couponObject = \Stripe\Coupon::retrieve($code, [
                    'api_key' => config('services.stripe.secret'),
                ]);
            } catch (\Exception $e) { }
        }

        return $couponObject;
    }

    /**
     * Disable or delete extra resources (those exceeding current plan limits)
     */
    public function downgradeExtraResources(){
        /*
         *
            'scheduled_posts' => 5000,
            'teams' => 5,
            'team_members' => 4, // max members in a team
            'accounts' => 10,
            'feeds' => 8,
            'automations' => 20,
         */

        $this->notify(new UserResourcesStopped());

        $planDetails = $this->getPlan();
        $usage = $this->getUsage();
        foreach ($planDetails['limits'] as $key => $value) {
            if (isset($usage[$key]) && $usage[$key] > $value) {
                // user usage is more than the plan's allowed maximum
                if($key === 'scheduled_posts'){
                    // disable/fail extra posts
                    // no longer doing this
                    /*
                    Post::ofUser($this->id)->pending()->offset($value)->limit($usage[$key] - $value)->update([
                        'result' => json_encode([
                            '_success' => false,
                            '_errorMsg' => 'Post disabled due to issue with user account',
                            '_timestamp' => time(),
                        ])
                    ]);
                    */
                } else if($key === 'teams'){
                    // delete all extra teams
                    /** @var Team[] $teams */
                    $teams = Team::ofUser($this->id)->offset($value)->limit($usage[$key] - $value)->get();
                    foreach($teams as $team){
                        $team->allMembers()->detach($team->membersWithoutAdmin()->get(['id'])->map(function($user){
                            return $user->id;
                        }));
                        try {
                            $team->delete();
                        } catch (\Exception $exception){

                        }
                    }
                } else if($key === 'team_members'){
                    // delete all extra members of teams
                    /** @var Team[] $teams */
                    $teams = Team::ofUser($this->id)->get();
                    foreach($teams as $team){
                        $team->allMembers()->detach($team->membersWithoutAdmin()->offset($value)->limit($usage[$key] - $value)->get(['id'])->map(function($user){
                            return $user->id;
                        }));
                    }
                } else if($key === 'accounts') {
                    // disable extra accounts
                    Account::ofUser($this->id)->offset($value)->limit($usage[$key] - $value)->update([
                        'active' => false,
                    ]);
                } else if($key === 'automations') {
                    // disable extra automations
                    Automation::userAutomations($this->id)->offset($value)->limit($usage[$key] - $value)->update([
                        'active' => false,
                    ]);
                } else if($key === 'feeds') {
                    // not sure what to do with feeds
                    // TODO: do something for feeds
                } else if($key === 'queues') {
                    PublishQueue::ofUser($this->id)->offset($value)->limit($usage[$key] - $value)->update([
                        'active' => false,
                    ]);
                }
            }
        }

    }

    /**
     * Get user's long-term api token they can readily use with our API endpoints
     * @return string
     * @throws \Exception
     */
    public function getApiToken($force = false, $noException = false)
    {
        $user = user();

        if(!$user->verified){
            if($noException) return null;
            throw new \Exception('Please complete your email address verification first.');
        }

        $token = null;
        if(!$force){
            $token = $user->getOption('internal.api_token');
        } else {
            // delete existing token
            /** @var \Laravel\Passport\Token $existing */
            $existing = $user->tokens()->where('name', 'Personal Access Token')->first();

            if ($existing) {
                $existing->revoke();
            }
        }

        if(!$token){
            $token = $user->createToken('Personal Access Token')->accessToken;
            $user->setOption('internal.api_token', $token);
        }

        return $token;
    }

    /**
     * Get user image via gravatar
     *
     * @return string
     */
    public function getPic(){
        return 'https://www.gravatar.com/avatar/' . md5($this->email) . '.jpg?s=48&d=mp';
    }

    private function getOptions()
    {
        return (array) $this->options;
    }

    private function setOptions($options)
    {
        $this->options = $options;
        $this->save();
        return $this;
    }

}
