import $ from "jquery";
import { axios, appConfig, alertify, getIconForAccount, sanitizeHtml } from "../components";
import lang from "../lang";
import Slideout_modal from "../slideout_modal";
import copy from "copy-to-clipboard";

const $addModal = $("#add_account_modal");

const $modal = $("#edit_account_modal");

$modal
    .on("show.bs.modal", event => {
        const $button = $(event.relatedTarget);
        const id = $button.data("id");
        (async function loadData() {
            $modal.spin({
                color: "#fff"
            });
            try {
                const res = await axios.get("/app/json/data/account/" + id);
                const account = res.data;
                $modal
                    .find(".modal-title").empty()
                    .append(`
                        <div class="d-flex align-items-center">
                            <img alt="avatar" src="${account.image}" class="avatar rounded-circle border border-secondary mr-2" />
                            <div class="line-height-3">
                                ${sanitizeHtml(account.name)}
                                <small class="d-flex align-items-center small-2"><img class="lozad" style="width: 22px; height: 22px" src="${getIconForAccount(account.type)}"> <span class='pl-2'> ${account._type} </span></small>
                            </div>
                        </div>
                    `);
                $modal.find("form").attr("action", "/app/accounts/" + id);
                const $shared_with = $("#shared_with").hide();
                const $shared_with_count = $("#shared_with_count").empty();
                if (account.shared_with_count > 0) {
                    $shared_with_count.append(account.shared_with_count);
                    $shared_with.show();
                }
                $modal.find(".input_account_name").val(account.name);
                $.each(account.shared_with, function(i, item) {
                    $shared_with.append(
                        $("<li/>")
                            .text(item.name)
                            .prop("selected", true)
                    );
                });

                $modal.find(".modal-content").removeClass("invisible");

                if (account.user_id === appConfig.userId) {
                    $modal
                        .find("._test")
                        .off("click")
                        .on("click", async function(e) {
                            e.preventDefault();
                            const btn = this;
                            btn.disabled = true;
                            try {
                                const res = await axios.post(account.test_url);
                                const b = res.data;
                                if (b.success) {
                                    alertify.success(lang.get("accounts.connection_success"));
                                } else {
                                    alertify.error(lang.get("accounts.connection_failed"));
                                }
                            } catch (e) {
                                alert(e);
                            }
                            btn.disabled = false;
                        });
                    $modal.find("._reconnect").on("click", function() {
                        document.location.href = account.reconnect_url;
                        return false;
                    });
                    $modal.find("._delete").on("click", function() {
                        alertify.delete(lang.get("accounts.delete_confirmation_msg"), function() {
                            // delete
                            axios
                                .delete("/app/accounts/" + id)
                                .then(() => {
                                    document.location.reload();
                                })
                                .catch(e => {
                                    alert(e);
                                });
                        });
                        return false;
                    });
                    $modal.find("._buttons").show();
                } else {
                    $modal.find("._buttons").hide();
                    $modal.find("form > :input").prop("disabled", true);
                }
            } catch (e) {
                alert(e);
                $modal.modal("hide");
            }
            $modal.spin(false);
        })();
    })
    .on("hidden.bs.modal", function() {
        $modal.find(".modal-content").addClass("invisible");
        $modal.find("._delete").off("click");
    });
$addModal.on("click._modal_hide", ".hide_modal_onclick", function() {
    $addModal.modal("hide");
});

// connect account using link
let connectUsingLinkModal;
$addModal.find("#connect_using_link").on("click", function() {

    console.log("test");

    if(!connectUsingLinkModal){

        connectUsingLinkModal = Slideout_modal.create("Connect account using link", {
            onOpen(el){

                const $allBtns = $addModal.find("[data-provider]");

                // clone the buttons and add each in <li>
                const $ul = $("<ul class='list-group list-group-flush'/>");

                $allBtns.each(function(){
                    const $btn = $(this).clone();

                    const provider = $btn.attr("data-provider");
                    if(!provider) return;

                    // remove all data-attributes
                    const $newEl = $("<div/>").append($btn.html());

                    let loading = false;

                    $newEl.addClass("cursor-pointer").on("click", function(e){
                        e.preventDefault();

                        if(loading) return;

                        if ($newEl.hasClass("---done")) {
                            return;
                        }

                        $newEl.spin();
                        loading = true;

                        (async ()=>{
                            try {
                                const { data } = await axios.post("/api/v1/accounts", {
                                    provider
                                });

                                // append an input after $newEl
                                const $input = $("<input type='text' class='form-control mt-2' readonly />");
                                $input.val(data.connect_url);

                                $input.on("click", function() {
                                    this.select();
                                });

                                $newEl.addClass("---done").after($input);

                                copy(data.connect_url);

                                alertify.success("Link copied to clipboard");

                            } catch (e) {
                                alert(e.message);
                            } finally {
                                loading = false;
                                $newEl.spin(false);
                            }
                        })();

                    });

                    $ul.append(
                        $("<li class='list-group-item py-4 px-0'/>")
                            .append($newEl)
                    );

                });

                const $disclaimer = $(`<div class="text-muted small mt-3" />`).text("Note: Using the link, anyone can connect their social accounts to your SocialBu account. Be careful who you share this link with.");

                $(el).find(".modal-body").empty().append($ul).append($disclaimer);

            },
            onHide(el){
                $(el).find(".modal-body").empty();
            }
        });

    }

    connectUsingLinkModal.open();

});

$(() => {
    // mastodon set domain
    $(".set_mastodon_domain").on("click", function() {
        const $mastodonDomain = $("#mastodon_domain");
        const $this = $(this);
        const domain = $this.attr("data-value");
        $mastodonDomain.val(domain);
        // close the dropdown
        $this.closest(".dropdown").find("button").dropdown("toggle");
        // keep the input focused
        $mastodonDomain.focus();
        // prevent default
        return false;
    });
});

export function showAddModal() {
    $addModal.modal("show");
}

export function showAddMastodonModal(){
    $("#add_mastodon_modal")
        .on("hide.bs.modal", function() {
            $("#cancel_acc_add_form").submit();
            return false;
        })
        .modal("show");
}

export function showAddBlueskyModal(){
    $("#add_bluesky_modal")
        .on("hide.bs.modal", function() {
            $("#cancel_acc_add_form").submit();
            return false;
        })
        .modal("show");
}

export function onAuthFacebook() {
    $("#fb_auth_facebook_accounts")
        .on("hide.bs.modal", function() {
            $("#cancel_acc_add_form").submit();
            return false;
        })
        .modal("show");
}

export function onAuthLinkedin() {
    $("#linkedin_auth_accounts")
        .on("hide.bs.modal", function() {
            $("#cancel_acc_add_form").submit();
            return false;
        })
        .modal("show");
}

export function onAuthGoogle() {
    $("#google_auth_accounts")
        .on("hide.bs.modal", function() {
            $("#cancel_acc_add_form").submit();
            return false;
        })
        .modal("show");
}

export function onAuthReddit() {
    $("#reddit_auth_accounts")
        .on("hide.bs.modal", function() {
            $("#cancel_acc_add_form").submit();
            return false;
        })
        .modal("show");
}
