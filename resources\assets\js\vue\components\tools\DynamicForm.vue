<template>
    <div>
        <div class="form-row">
            <div class="form-group" :class="[field.class ? field.class : '']" v-for="field in fields" :key="field.id">                
                <label class="flex justify-content-between" :for="field.id + 'inp'" v-if="field.type !== 'checkbox'">
                    <span>{{ field.label }}</span>
                    <span v-if="isFieldRequired(field)" class="text-danger">*</span>
                </label>

                <template v-if="field.type === 'text' || field.type === 'number'">
                    <input :type="field.type" class="form-control" :id="field.id + 'inp'"
                        :placeholder="field.placeholder ? field.placeholder : ''"
                        :aria-describedby="field.description ? field.id + 'inp' : undefined"
                        v-model="input[field.id]" />
                </template>

                <template v-if="field.type === 'checkbox'">
                    <div class="form-check">
                        <input class="form-check-input"
                            name="" type="checkbox"
                            :id="field.id + 'inp'"
                            v-model="input[field.id]"/>

                        <label class="flex justify-content-between form-check-label" :for="field.id + 'inp'">
                            <span>{{ field.label }}</span>
                            <span v-if="isFieldRequired(field)" class="text-danger">*</span>
                        </label>
                    </div>
                </template>

                <template v-else-if="field.type === 'select'"> 
                    <select class="form-control" :id="field.id + 'inp'" 
                        :aria-describedby="field.description ? field.id + 'inp' : undefined"
                        @change="setOptionValue(field.id, $event.target.value)"
                        ref="selectField">
                        <option disabled selected value="">Select an option</option>
                        <option v-for="(option) in field.options" :selected="option.selected" :key="option.value" :value="option.value">
                            {{ option.label }}
                        </option>
                    </select>
                </template>

                <template v-if="field.type === 'textarea'">
                    <textarea class="form-control" :id="field.id + 'inp'"
                        :placeholder="field.placeholder ? field.placeholder : ''"
                        :aria-describedby="field.description ? field.id + 'inp' : undefined"
                        v-model="input[field.id]"></textarea>
                </template>
                               
                <small :id="field.id + 'inp'" class="form-text text-muted" v-if="field.description">
                    {{ field.description }}
                </small>
            </div>
        </div>
        <div class="form-row">
            <div class="col">
                <button @click.prevent="submit" class="btn btn-primary">Generate</button>
            </div>
            <div class="col text-right">
                <button @click.prevent="resetForm" class="btn btn-outline-light">
                    <i class="ph ph-arrow-clockwise ph-md mr-2"></i>
                    Reset
                </button>
            </div>
        </div>
    </div>
</template>

<script>
import * as Validator from 'validatorjs';
import { axiosErrorHandler } from '../../../components';
export default {
    name: "DynamicForm",
    props: {
        fields: {
            type: Array,
            required: true,
        },
        outputComponents: { // not implemented yet
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            input: {},
            errors: {},
        }
    },
    computed: {
        rules() {
            const rules = {};
            this.fields.forEach(field => {            
                if (field.rules && field.rules.length) {
                    rules[field.id] = field.rules;
                }
            });
            return rules;
        },
        hasErrors() {
            return Object.keys(this.errors).length > 0;
        },
    },
    methods: {
        isFieldRequired(field) {
            return field.required || field.rules?.split('|').includes('required');
        },
        validate() {
            const validation = new Validator(this.input, this.rules);

            this.errors = {};

            if (validation.fails(null)) {
                const errs = {};

                Object.keys(validation.errors.all()).forEach(key => {
                    errs[key] = validation.errors.get(key);
                });

                this.errors = errs;
            }
        },
        submit() {
            this.validate();

            if (!this.hasErrors) {
                this.$emit('submit', this.input);
            } else {
                axiosErrorHandler({
                    response: {
                        data: {
                            message: 'Validation failed',
                            errors: this.errors,
                        }
                    }
                });
            }
        },
        resetForm(){
            this.input = {};
            if(this.$refs.selectField) {
                this.$refs.selectField.forEach(select => {
                    select.value = ''; // Reset select fields
                });
            }
        },
        setOptionValue(fieldId, value) {
            this.$set(this.input, fieldId, value);
        }
    }
}
</script>

<style scoped lang="scss"></style>
