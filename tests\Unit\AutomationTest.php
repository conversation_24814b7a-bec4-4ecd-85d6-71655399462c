<?php

namespace Tests\Unit;

use App\Automation;
use App\Helpers\AutomationCondition;
use Tests\TestCase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class AutomationTest extends TestCase
{
    /**
     * A basic test example.
     *
     * @return void
     */
    public function testConditionsMatching()
    {
        $this->assertTrue((new AutomationCondition([], [
            'has_image' => false,
        ]))->matches());
        $this->assertTrue((new AutomationCondition([
            [
                'conditions' => [
                    [
                        'key' => 'has_image',
                        'operator' => 'YES',
                    ],
                    [
                        'key' => 'has_image',
                        'operator' => 'NO',
                    ]
                ],
                'operator' => 'OR',
            ]
        ], [
            'has_image' => false,
        ]))->matches());
        $this->assertFalse((new AutomationCondition([
            [
                'key' => 'has_image',
                'operator' => 'YES',
            ],
            [
                'key' => 'has_image',
                'operator' => 'NO',
            ]
        ], [
            'has_image' => false,
        ]))->matches());
        $this->assertFalse((new AutomationCondition([
            [
                'key' => 'content',
                'operator' => 'this_method-doesnt exists',
                'value' => 'test',
            ]
        ], [
            'content' => 'test',
        ]))->matches());
        $this->assertFalse((new AutomationCondition([
            [
                'conditions' => [
                    [
                        'key' => 'content',
                        'operator' => 'IS',
                        'value' => 'test',
                    ],
                    [
                        'key' => 'content',
                        'operator' => 'IS',
                        'value' => 'test2',
                    ]
                ],
                'operator' => 'AND'
            ],
            [
                'key' => 'content',
                'operator' => 'IS',
                'value' => 'test',
            ]
        ], [
            'content' => 'test',
        ]))->matches());
        $this->assertTrue((new AutomationCondition([
            [

                'conditions' => [
                    [
                        'key' => 'content',
                        'operator' => 'IS',
                        'value' => 'test',
                    ],
                    [
                        'key' => 'content',
                        'operator' => 'IS',
                        'value' => 'test2',
                    ]
                ],
                'operator' => 'OR'
            ],
            [
                'key' => 'content',
                'operator' => 'IS',
                'value' => 'test',
            ],
            [
                'key' => 'content',
                'operator' => 'IS_NOT',
                'value' => 'test2',
            ],
            [
                'key' => 'content',
                'operator' => 'STARTS_WITH',
                'value' => 'tes',
            ],
            [
                'key' => 'content',
                'operator' => 'ENDS_WITH',
                'value' => 'st',
            ]
        ], [
            'content' => 'test',
        ]))->matches());
        $this->assertFalse((new AutomationCondition([
            [
                'conditions' => [
                    [
                        'key' => 'content',
                        'operator' => 'CONTAINS',
                        'value' => 'free?',
                    ],
                    [
                        'key' => 'content',
                        'operator' => 'CONTAINS',
                        'value' => 'mufta?',
                    ]
                ],
                'operator' => 'OR'
            ],
            [
                'key' => 'id',
                'operator' => 'IS',
                'value' => '1',
            ],
        ], [
            'content' => 'free mufta hy?',
            'id' => '1',
        ]))->matches());
        $this->assertTrue((new AutomationCondition([
            [
                'conditions' => [
                    [
                        'key' => 'content',
                        'operator' => 'CONTAINS',
                        'value' => 'free mufta',
                    ],
                    [
                        'key' => 'content',
                        'operator' => 'CONTAINS',
                        'value' => 'mufta?',
                    ]
                ],
                'operator' => 'OR'
            ],
            [
                'key' => 'id',
                'operator' => 'IS',
                'value' => '1',
            ],
        ], [
            'content' => 'free mufta hy?',
            'id' => '1',
        ]))->matches());
    }
}
