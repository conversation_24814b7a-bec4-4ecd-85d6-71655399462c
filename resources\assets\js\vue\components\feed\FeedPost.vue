<template>
    <div class="h-100" :class="{'deleting': deleting}">
        <template v-if="loading || error">
            <div class="bg-light card border-0 h-100" v-if="loading">
                <div class="card-body h-100 d-flex align-items-center justify-content-center bg-light text-muted">
                    Loading
                </div>
            </div>
            <div class="alert alert-danger text-center" v-else>
                <div class="card-body">
                    {{ error }}
                </div>
            </div>
        </template>
        <template v-else>
            <button type="button" class="btn btn-sm btn-light unread-indicator"
                    @click="callRoot('jump_to_unread_item')" v-if="isRoot && hasUnreadItems">
                Has new item
            </button>
            <div ref="msgsArea" :is="scrollbarComponent"  class="msg-container" :class="{'p-3 is-root mb-5': isRoot}">
                <div :id="'msg' + feedPost.id" class="msg d-flex"
                     :class="{'indent ml-4 pl-3 border-left': indent, 'mb-4': !indent || (!isRoot && index === $parent.feedPost.children.length - 1), 'pb-4': indent && !(!isRoot && index === $parent.feedPost.children.length - 1),  'received text-dark mr-auto': !isSent, 'sent bg-light ml-auto justify-content-end': isSent && feedPost.type === 'message', 'chat': feedPost.type === 'message'}">
                    <div class="content-container w-100" v-if="feedPost.type === 'private_note'">
                        <div class="d-flex">
                            <div class="content-wrapper shadow w-100 border-warning has-user-image" style="border-style: dashed">
                                <div class="d-flex align-items-start pl-3 pt-3 pb-3">
                                    <div class="sender-img mr-3">
                                        <img :src="getUser(feedPost.user_id).image" alt="user image" />
                                    </div>
                                    <div class="name-and-timestamp">
                                        <div>
                                            <span class="font-weight-bold">
                                                {{ getUser(feedPost.user_id).name }}
                                            </span>
                                            added a private note
                                        </div>
                                        <div>
                                            <small class="text-muted"
                                                   :title="timestamp.format('dddd, MMMM Do YYYY, h:mm a')">
                                                <template v-if="timestamp.format('DMY') === $momentUTC().format('DMY')">
                                                    {{ timestamp.format('h:mm a') }}
                                                </template>
                                                <template v-else>
                                                    {{ timestamp.fromNow() }}
                                                </template>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <div class="content font-weight-400 pl-3 pr-3 pb-3">
                                    <div class="content-text" v-text="feedPost.data.content || feedPost.data.text"></div>
                                </div>
                                <div class="post-footer d-flex justify-content-end">
                                    <div class="btn-group options">
                                        <button type="button" class="btn btn-sm"
                                                @click="replyToPost" :title="'Reply'">
                                            <i class="ph ph-arrow-bend-up-left ph-md"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <template v-else>
                        <div class="sender-img mr-3" :class="{'mt-4': !isSent && showImage}" v-if="feedPost.type === 'message' && (!isSent || (isSent && feedPost.type !== 'message'))">
                            <img class="shadow"
                                 :src="feedPost.data.user.profile_image_url_https" :class="{'invisible': !showImage}" v-if="account.type === 'twitter.profile'" @error="setAltImage"/>
                            <img class="shadow"
                                 :src="feedPost.data.user.image" :class="{'invisible': !showImage}" v-else-if="account.type === 'instagram.direct'" @error="setAltImage"/>
                            <img class="shadow"
                                 :src="feedPost.data.from.profile_pic" :class="{'invisible': !showImage}" v-else-if="account.type === 'instagram.api'" @error="setAltImage"/>
                            <img class="shadow"
                                 :src="'/app/_facebook/picture/' + account.public_id + '/' + post.data.from.id" :class="{'invisible': !showImage}" @error="setAltImage" v-else />
                        </div>
                        <div class="content-container" :class="{'w-100': feedPost.type !== 'message'}">
                            <div class="sender-name" v-if="feedPost.type === 'message' && (!isSent && showImage)">
                                <span v-if="account.type === 'twitter.profile'">
                                    {{ feedPost.data.user.name }}
                                </span>
                                <span v-else-if="account.type === 'instagram.direct'">
                                    {{ feedPost.data.user.username }}
                                </span>
                                <span v-else>
                                    {{ feedPost.data.from.name }}
                                </span>
                                <small class="text-muted" v-if="account.type === 'twitter.profile'">
                                    @{{ feedPost.data.user.screen_name }}
                                </small>
                            </div>
                            <div class="d-flex">
                                <div class="d-flex align-items-start mr-4 timestamp" v-if="feedPost.type === 'message' && isSent">
                                    <div>
                                        <small class="text-muted"
                                               :title="timestamp.format('dddd, MMMM Do YYYY, h:mm a')">
                                            <template v-if="timestamp.format('DMY') === $momentUTC().format('DMY')">
                                                {{ timestamp.format('h:mm a') }}
                                            </template>
                                            <template v-else>
                                                {{ timestamp.fromNow() }}
                                            </template>
                                        </small>
                                    </div>
                                </div>
                                <div class="content-wrapper shadow"
                                     :class="{'w-100': feedPost.type !=='message', 'bg-white': !isSent, 'bg-secondary': isSent, 'has-user-image': showImage}" @click="removeNewIndicator">
                                    <div class="d-flex align-items-start pl-3 pt-3 pb-3" v-if="feedPost.type !== 'message'">
                                        <!-- name, image, timestamp for non-msgs -->
                                        <div class="sender-img mr-3" v-if="showImage">
                                            <img
                                                    :src="feedPost.data.user.profile_image_url_https" v-if="account.type === 'twitter.profile'" @error="setAltImage"/>
                                            <template v-else-if="account.type === 'instagram.direct'">
                                                <img
                                                    :src="feedPost.data.user.image || feedPost.data.user.profile_pic_url" v-if="!isSent" @error="setAltImage"/>
                                                <img
                                                    :src="account.image" @error="setAltImage" v-else />
                                            </template>
                                            <template v-else-if="account.type === 'instagram.api'">
                                                <img
                                                    src="/images/no-image.png" @error="setAltImage" v-if="!isSent"/>
                                                <img
                                                    :src="account.image" @error="setAltImage" v-else />
                                            </template>
                                            <img
                                                    :src="'/app/_facebook/picture/' + account.public_id + '/' + post.data.from.id" @error="setAltImage" v-else />
                                        </div>
                                        <div class="name-and-timestamp">
                                            <div>
                                                <span class="font-weight-bold" v-if="account.type === 'twitter.profile'">
                                                    {{ isSent ? account.name : feedPost.data.user.name }}
                                                </span>
                                                <span class="font-weight-bold" v-else-if="account.type === 'instagram.direct'">
                                                    {{ isSent ? account.name : feedPost.data.user.username }}
                                                </span>
                                                <span class="font-weight-bold" v-else-if="account.type === 'instagram.api'">
                                                    {{ isSent ? account.name : feedPost.data.user.username }}
                                                </span>
                                                <span class="font-weight-bold" v-else>
                                                    {{ isSent ? account.name : feedPost.data.from.name }}
                                                </span>
                                                <small v-if="!isSent && account.type === 'twitter.profile'">
                                                    <a class="text-muted" target="_blank"
                                                       :href="'https://twitter.com/' + feedPost.data.user.screen_name">
                                                        @{{ feedPost.data.user.screen_name }}
                                                    </a>
                                                </small>
                                                <span class="text-muted font-weight-bold" v-else-if="isRoot && !isSent && account.type === 'facebook.page' && isRating">
                                                    <template v-if="feedPost.data.recommendation_type === 'NEGATIVE'">
                                                        <i class="ph ph-fill ph-thumbs-down"></i> left a NEGATIVE review
                                                    </template>
                                                    <template v-else>
                                                        <i class="ph ph-fill ph-thumbs-up"></i> left a POSITIVE review
                                                    </template>
                                                </span>
                                            </div>
                                            <div v-if="(feedPost.type === 'tweet'||feedPost.type === 'post') && feedPost.data.from_search">
                                                <small :title="feedPost.data.keyword" class="small-5 badge badge-secondary badge-pill">
                                                    {{ truncate(feedPost.data.keyword, 20) }}
                                                </small>
                                            </div>
                                            <div>
                                                <a :href="permalink" target="_blank" v-if="permalink">
                                                    <small
                                                            :class="{'text-muted': !isSent}" :title="timestamp.format('dddd, MMMM Do YYYY, h:mm a')">
                                                        <template v-if="timestamp.format('DMY') === $momentUTC().format('DMY')">
                                                            {{ timestamp.format('h:mm a') }}
                                                        </template>
                                                        <template v-else>
                                                            {{ timestamp.fromNow() }}
                                                        </template>
                                                        <span class="small-4" v-if="feedPost.user_id && feedHasTeam && isSent && getUser(feedPost.user_id)">(by {{ getUser(feedPost.user_id).name }})</span>
                                                    </small>
                                                </a>
                                                <small
                                                        :class="{'text-muted': !isSent}" :title="timestamp.format('dddd, MMMM Do YYYY, h:mm a')" v-else>
                                                    <template v-if="timestamp.format('DMY') === $momentUTC().format('DMY')">
                                                        {{ timestamp.format('h:mm a') }}
                                                    </template>
                                                    <template v-else>
                                                        {{ timestamp.fromNow() }}
                                                    </template>
                                                    <span class="small-4" v-if="feedPost.user_id && feedHasTeam && isSent && getUser(feedPost.user_id)">(by {{ getUser(feedPost.user_id).name }})</span>
                                                </small>
                                                <span class="unread-badge badge badge-danger ml-2" v-if="!isRoot && !isSent && !feedPost.read">NEW</span>
                                            </div>
                                        </div>
                                        <div class="ml-auto mr-3 bg-white"
                                             v-if="!isSent && !(account.type === 'instagram.direct' && feedPost.type === 'post')">
                                            <a target="_blank"
                                               :href="permalink" v-if="permalink" v-tooltip="'View on ' + (account.type.split('.')[0])">
                                                <i class="fa social-icon active"
                                                   :class="{'fa-instagram instagram': ['instagram.direct', 'instagram.api'].includes(account.type), 'fa-facebook-square facebook': account.type === 'facebook.page', 'fa-twitter-square twitter': account.type === 'twitter.profile'}"></i>
                                            </a>
                                        </div>
                                    </div>
                                    <div class="content font-weight-400 pl-3 pr-3 pb-3" :class="{'pt-3': feedPost.type === 'message', 'lead': !isSent}">
                                        <!-- text content -->
                                        <div class="content-text" v-html="textContent" v-if="textContent.length"></div>
                                         <!-- attachment (if any), can be photo, gif or even a video -->
                                        <template v-if="feedPost.data.attachments.length">
                                            <div class="text-center bg-light px-4"
                                             :class="{'mt-4': textContent.length}" v-for="(att,i) in feedPost.data.attachments" :key="i" >
                                            <video class="w-100" controls preload="none"
                                                   v-if="att.mime.indexOf('video') > -1">
                                                <source
                                                        :src="att.url"/>
                                            </video>
                                            <div v-else-if="att.mime.indexOf('text') > -1">
                                                <a :href="att.url" target="_blank">Link to Attachment</a>
                                            </div>
                                            <img
                                                    :src="att.url" v-else />
                                        </div>
                                        </template>

                                        <!-- video or photo for tweet -->
                                        <div class="text-center bg-light px-4"
                                             :class="{'mt-4': textContent.length}" v-if="feedPost.type === 'tweet' && feedPost.data.extended_entities && feedPost.data.extended_entities.media && feedPost.data.extended_entities.media.length">
                                            <template v-for="(media,i) in feedPost.data.extended_entities.media" >
                                                <img alt="Media"
                                                        :src="media.media_url_https" v-if="media.type === 'photo'" :key="i"/>
                                                <video class="w-100" controls preload="none"
                                                       v-else-if="media.video_info && (media.type === 'video' || media.type === 'animated_gif')" :key="'vid' + i">
                                                    <source
                                                            :src="media.video_info.variants[0].url" v-if="media.type === 'animated_gif'"/>
                                                    <source
                                                            :type="variant.content_type" :src="variant.url" v-for="(variant,i) in media.video_info.variants" :key="'src' + i"/>
                                                </video>
                                                <img alt="Media" :src="media.media_url_https" v-tooltip="'Media not available'" :key="'img' + i" v-else/> <!-- this tag is for when video cannot be embed outside of twitter official clients -->
                                            </template>
                                        </div>

                                        <!-- facebook post: video -->
                                        <div class="text-center bg-light px-4"
                                             :class="{'mt-4': textContent.length}" v-if="feedPost.type === 'post' && account.type === 'facebook.page' && feedPost.data.video_id">
                                            <div class="fb-video" data-show-text="false"
                                                 :data-href="'https://www.facebook.com/' + account.account_id + '/videos/' + feedPost.data.video_id">
                                                <blockquote :cite="'https://www.facebook.com/' + account.account_id + '/videos/' + feedPost.data.video_id" class="fb-xfbml-parse-ignore">
                                                    <a :href="'https://www.facebook.com/' + account.account_id + '/videos/' + feedPost.data.video_id"></a>
                                                    <p class="text-muted">(Video)</p>
                                                </blockquote>
                                            </div>
                                        </div>

                                        <!-- instagram media -->
                                        <div class="px-4 text-center"
                                             v-if="feedPost.type === 'post' && ['instagram.direct', 'instagram.api'].includes(account.type)">
                                            <div class="card border d-inline-block text-dark">
                                                <div class="card-header text-left p-2 d-flex justify-content-between">
                                                    <div>
                                                        @{{ feedPost.data.from.username }}
                                                        <span class="small-3"
                                                                v-if="feedPost.data.location">
                                                            at {{ feedPost.data.location.name }}
                                                        </span>
                                                    </div>
                                                    <div>
                                                        <a target="_blank"
                                                           :href="feedPost.data.permalink ? feedPost.data.permalink : '//instagram.com/p/' + (feedPost.data.code || feedPost.data.shortcode)" v-tooltip="'View on Instagram'">
                                                            <i class="social-icon active fa fa-instagram instagram"></i>
                                                        </a>
                                                    </div>
                                                </div>
                                                <div class="card-body p-0">
                                                    <div>
                                                        <template v-if="feedPost.data.media_type === 8 || feedPost.data.media_type === 'CAROUSEL_ALBUM'">
                                                            <!-- carousal -->
                                                            <vue-carousel :data="feedPost.data.carousel_media.map(m=> (createElement, content, context) => {
                                                                    if(m.video_versions){
                                                                        return createElement('video', {
                                                                            attrs: {
                                                                                controls: true,
                                                                                preload: 'none'
                                                                            },
                                                                            class: 'w-100'
                                                                        }, [
                                                                            createElement('source', {
                                                                                attrs: {
                                                                                    src: '/app/external/image/p?hash=' + encodeURIComponent(m.video_versions[0].url)
                                                                                }
                                                                            }, [])
                                                                        ]);
                                                                    } else {
                                                                        return createElement('img', {
                                                                            attrs: {
                                                                                src: '/app/external/image/p?hash=' + encodeURIComponent(m.image_versions2.candidates[0].url),
                                                                                alt: 'Media'
                                                                            }
                                                                        }, []);
                                                                    }
                                                                })" :controls="true" :autoplay="false" v-if="feedPost.data.carousel_media" />
                                                            <!-- image -->
                                                            <img alt="Media" :src="'/app/external/image/p?hash=' + encodeURIComponent(feedPost.data.media_url)" v-else />
                                                        </template>
                                                        <template v-else-if="feedPost.data.media_type === 2 || feedPost.data.media_type === 'VIDEO'">
                                                            <!-- video -->
                                                            <video class="w-100" controls preload="none">
                                                                <source
                                                                        :src="'/app/external/image/p?hash=' + encodeURIComponent(feedPost.data.media_url || feedPost.data.video_versions[0].url)" />
                                                            </video>
                                                        </template>
                                                        <template v-else-if="feedPost.data.media_type === 1 || feedPost.data.media_type === 'IMAGE'">
                                                            <!-- image -->
                                                            <img alt="Media" :src="'/app/external/image/p?hash=' + encodeURIComponent(feedPost.data.media_url || feedPost.data.image_versions2.candidates[0].url)" />
                                                        </template>
                                                    </div>
                                                    <div v-if="(feedPost.data.caption ? feedPost.data.caption.text : feedPost.data.text)" class="text-left p-2 ig-caption">
                                                        <!-- <strong class="font-weight-bold">{{ feedPost.data.user.username }}</strong> -->
                                                        <span v-html="autoLink(feedPost.data.caption ? feedPost.data.caption.text : feedPost.data.text)"></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- video or photo for instagram msgs -->
                                        <div class="text-center px-4"
                                             :class="{'mt-4': textContent.length, 'bg-light': feedPost.data.item_type && !['media_share', 'voice_media', 'like', 'raven_media'].includes(feedPost.data.item_type) }"
                                             v-if="feedPost.type === 'message' && (feedPost.data.item_type && feedPost.data.item_type !== 'text')">
                                            <template v-if="feedPost.data.item_type === 'media'">
                                                <img alt="Media" :src="'/app/external/image/p?hash=' + encodeURIComponent(feedPost.data.media.image_versions2.candidates[0].url)" v-if="feedPost.data.media.media_type === 1" />
                                                <video class="w-100" controls preload="none" v-else-if="feedPost.data.media.media_type === 2">
                                                    <source
                                                            :src="'/app/external/image/p?hash=' + encodeURIComponent(feedPost.data.media.video_versions[0].url)" />
                                                </video>
                                            </template>
                                            <template v-else-if="feedPost.data.item_type === 'like'">
                                                <div style="font-size: 48px;text-align: center;">{{ feedPost.data.like }}</div>
                                            </template>
                                            <template v-else-if="feedPost.data.item_type === 'link'">
                                                <div>{{ feedPost.data.link ? feedPost.data.link.text : '** link unavailable **' }}</div>
                                            </template>
                                            <template v-else-if="feedPost.data.item_type === 'reel_share'">
                                                <div v-if="feedPost.data.reel_share">
                                                    <span v-if="feedPost.data.reel_share.type === 'mention'">
                                                        Mentioned you in a story.
                                                    </span>
                                                    <span v-else-if="feedPost.data.reel_share.type === 'reply'">
                                                        Replied to a story.
                                                    </span>
                                                    <span v-else>
                                                        {{ feedPost.data.reel_share.type }}
                                                    </span>
                                                    <div v-if="feedPost.data.reel_share.media">
                                                        <template v-if="feedPost.data.reel_share.media.expiring_at && Number(feedPost.data.reel_share.media.expiring_at) < $momentUTC().format('X')">
                                                            (story expired)
                                                        </template>
                                                        <template v-else>
                                                            <img alt="Media" :src="'/app/external/image/p?hash=' + encodeURIComponent(feedPost.data.reel_share.media.image_versions2.candidates[0].url)" v-if="feedPost.data.reel_share.media.media_type === 1" />
                                                            <video class="w-100" controls preload="none" v-else-if="feedPost.data.reel_share.media.media_type === 2">
                                                                <source
                                                                        :src="'/app/external/image/p?hash=' + encodeURIComponent(feedPost.data.reel_share.media.video_versions[0].url)" />
                                                            </video>
                                                        </template>
                                                    </div>

                                                    <div class="text-left" v-if="feedPost.data.reel_share.text">
                                                        <p>
                                                            {{ feedPost.data.reel_share.text }}
                                                        </p>
                                                    </div>

                                                </div>
                                                <div v-else>
                                                    ** this message cannot be showed here **
                                                </div>
                                            </template>
                                            <template v-else-if="feedPost.data.item_type === 'story_share'">
                                                <div v-if="feedPost.data.story_share">
                                                    <span>
                                                        {{ feedPost.data.story_share.type ? feedPost.data.story_share.type : '' }}
                                                    </span>
                                                    <div v-if="feedPost.data.story_share.media">
                                                        <template v-if="feedPost.data.story_share.media.expiring_at && Number(feedPost.data.story_share.media.expiring_at) < $momentUTC().format('X')">
                                                            (expired)
                                                        </template>
                                                        <template v-else>
                                                            <img alt="Media" :src="'/app/external/image/p?hash=' + encodeURIComponent(feedPost.data.story_share.media.image_versions2.candidates[0].url)" v-if="feedPost.data.story_share.media.media_type === 1" />
                                                            <video class="w-100" controls preload="none" v-else-if="feedPost.data.story_share.media.media_type === 2">
                                                                <source
                                                                        :src="'/app/external/image/p?hash=' + encodeURIComponent(feedPost.data.story_share.media.video_versions[0].url)" />
                                                            </video>
                                                        </template>
                                                    </div>
                                                </div>
                                                <div v-else>
                                                    ** this message cannot be showed here **
                                                </div>
                                            </template>
                                            <template v-else-if="feedPost.data.item_type === 'placeholder'">
                                                <div>{{ feedPost.data.placeholder && feedPost.data.placeholder.title ? feedPost.data.placeholder.title : '** unavailable **' }}</div>
                                            </template>
                                            <template v-else-if="feedPost.data.item_type === 'raven_media'">
                                                <div v-if="feedPost.data.visual_media && feedPost.data.visual_media.media">
                                                    <img alt="Media" :src="'/app/external/image/p?hash=' + encodeURIComponent(feedPost.data.visual_media.media.image_versions2.candidates[0].url)" v-if="feedPost.data.visual_media.media.media_type === 1" />
                                                    <video class="w-100" controls preload="none" v-else-if="feedPost.data.visual_media.media.media_type === 2">
                                                        <source
                                                                :src="'/app/external/image/p?hash=' + encodeURIComponent(feedPost.data.visual_media.media.video_versions[0].url)" />
                                                    </video>
                                                </div>
                                                <div v-else>
                                                    ** this message cannot be displayed **
                                                </div>
                                            </template>
                                            <template v-else-if="feedPost.data.item_type === 'animated_media'">
                                                <img alt="Media" :src="'/app/external/image/p?hash=' + encodeURIComponent(feedPost.data.animated_media.images.fixed_height.url)" />
                                            </template>
                                            <template v-else-if="feedPost.data.item_type === 'voice_media'">
                                                <audio controls preload="none">
                                                    <source :src="'/app/external/image/p?hash=' + encodeURIComponent(feedPost.data.voice_media.media.audio.audio_src)" />
                                                </audio>
                                            </template>
                                            <template v-else-if="feedPost.data.item_type === 'media_share'">
                                                <div class="card border">
                                                    <div class="card-header text-left p-2 d-flex justify-content-between">
                                                        <div>
                                                            @{{ feedPost.data.media_share.user.username }}
                                                            <span v-if="feedPost.data.media_share.location">
                                                             at {{ feedPost.data.media_share.location.name }}
                                                            </span>
                                                        </div>
                                                        <div>
                                                            <a target="_blank"
                                                               :href="'//instagram.com/p/' + feedPost.data.media_share.code" v-tooltip="'View on Instagram'">
                                                                <i class="social-icon active fa fa-instagram instagram"></i>
                                                            </a>
                                                        </div>
                                                    </div>
                                                    <div class="card-body p-0">
                                                        <div>
                                                            <template v-if="feedPost.data.media_share.media_type === 8">
                                                                <!-- carousal -->
                                                                <vue-carousel :data="feedPost.data.media_share.carousel_media.map(m=> (createElement, content, context) => {
                                                                    if(m.video_versions){
                                                                        return createElement('video', {
                                                                            attrs: {
                                                                                controls: true,
                                                                                preload: 'none'
                                                                            },
                                                                            class: 'w-100'
                                                                        }, [
                                                                            createElement('source', {
                                                                                attrs: {
                                                                                    src: '/app/external/image/p?hash=' + encodeURIComponent(m.video_versions[0].url)
                                                                                }
                                                                            }, [])
                                                                        ]);
                                                                    } else {
                                                                        return createElement('img', {attrs: {src: '/app/external/image/p?hash=' + encodeURIComponent(m.image_versions2.candidates[0].url), alt: 'Media'}}, []);
                                                                    }
                                                                })" :controls="true" :autoplay="false" />
                                                            </template>
                                                            <template v-else-if="feedPost.data.media_share.media_type === 2">
                                                                <!-- video -->
                                                                <video class="w-100" controls preload="none">
                                                                    <source
                                                                            :src="'/app/external/image/p?hash=' + encodeURIComponent(feedPost.data.media_share.video_versions[0].url)" />
                                                                </video>
                                                            </template>
                                                            <template v-else-if="feedPost.data.media_share.media_type === 1">
                                                                <!-- image -->
                                                                <img alt="Media" :src="'/app/external/image/p?hash=' + encodeURIComponent(feedPost.data.media_share.image_versions2.candidates[0].url)" />
                                                            </template>
                                                        </div>
                                                        <div v-if="feedPost.data.media_share.caption" class="text-left p-2 ig-caption">
                                                            <strong class="font-weight-bold">{{ feedPost.data.media_share.user.username }}</strong> <span v-html="autoLink(feedPost.data.media_share.caption.text)"></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </template>
                                        </div>

                                    </div>
                                    <div class="post-footer d-flex justify-content-end" v-if="feedPost.type !== 'message'">
                                        <!-- reply, fav or RT button for tweets -->
                                        <div class="btn-group options"
                                             v-if="account.type === 'twitter.profile' && feedPost.type === 'tweet'">
                                            <!-- we do not show these action buttons for tweets matching a hashtag/search keyword to prevent spam -->
                                            <template v-if="!feedPost.data.from_search">
                                                <button type="button" class="btn btn-sm"
                                                        :class="{'btn-outline-secondary': !isSent, 'btn-light': isSent}"
                                                        @click="like" :title="feedPost.options && feedPost.options.liked ? 'Unlike' : 'Like'">
                                                    <i class="ph ph-fill ph-heart social-icon twitter active" v-if="feedPost.options && feedPost.options.liked"></i>
                                                    <i class="ph ph-fill ph-heart" v-else></i>
                                                </button>
                                                <button type="button" class="btn btn-sm"
                                                        :class="{'btn-outline-secondary': !isSent, 'btn-light': isSent}"
                                                        @click="retweet" :title="feedPost.options && feedPost.options.retweeted ? 'Undo Retweet' : 'Retweet'">
                                                    <i class="ph ph-repeat social-icon twitter active" v-if="feedPost.options && feedPost.options.retweeted"></i>
                                                    <i class="ph ph-repeat" v-else></i>
                                                </button>
                                                <button type="button" class="btn btn-sm"
                                                        :class="{'btn-outline-secondary': !isSent, 'btn-light': isSent}"
                                                        @click="replyToPost" :title="'Reply'">
                                                    <i class="ph ph-arrow-bend-up-left ph-md"></i>
                                                </button>
                                            </template>
                                        </div>
                                        <!-- reply or like for comment or post -->
                                        <div class="btn-group btn-group-sm options"
                                             v-else-if="account.type === 'facebook.page' && feedPost.type !== 'message'">
                                            <template v-if="feedPost.type === 'comment'">
                                                <button type="button" class="btn btn-sm"
                                                        :class="{'btn-outline-secondary': !isSent, 'btn-light': isSent}"
                                                        @click="like" :title="feedPost.options && feedPost.options.reaction ? 'Unlike' : 'Like'"
                                                        v-if="!isRating">
                                                    <i class="ph ph-fill ph-heart social-icon facebook active" v-if="feedPost.options && feedPost.options.reaction"></i>
                                                    <i class="ph ph-fill ph-heart" v-else></i>
                                                </button>
                                                <!-- make sure, we pass parent comment feedPost for child comment -->
                                                <button type="button" class="btn btn-sm"
                                                        :class="{'btn-outline-secondary': !isSent, 'btn-light': isSent}"
                                                        @click="replyToPost" :title="'Reply'">
                                                    <i class="ph ph-arrow-bend-up-left ph-md"></i>
                                                </button>
                                            </template>
                                            <template v-else-if="feedPost.type === 'post'">
                                                <button type="button" class="btn btn-sm"
                                                        :class="{'btn-outline-secondary': !isSent, 'btn-light': isSent}"
                                                        @click="like" :title="feedPost.options && feedPost.options.reaction ? 'Unlike' : 'Like'"
                                                        v-if="!isRating">
                                                    <i class="ph ph-fill ph-heart social-icon facebook active" v-if="feedPost.options && feedPost.options.reaction"></i>
                                                    <i class="ph ph-fill ph-heart" v-else></i>
                                                </button>
                                                <button type="button" class="btn btn-sm"
                                                        :class="{'btn-outline-secondary': !isSent, 'btn-light': isSent}"
                                                        @click="replyToPost" :title="'Reply'">
                                                    <i class="ph ph-arrow-bend-up-left ph-md"></i>
                                                </button>
                                            </template>
                                        </div>

                                        <!-- reply or like for instagram post or comment -->
                                        <div class="btn-group btn-group-sm options"
                                             v-else-if="['instagram.direct', 'instagram.api'].includes(account.type) && feedPost.type !== 'message'">
                                            <template v-if="feedPost.type === 'comment'">
                                                <button type="button" class="btn btn-sm"
                                                        :class="{'btn-outline-secondary': !isSent, 'btn-light': isSent}"
                                                        @click="like" :title="feedPost.options && feedPost.options.liked ? 'Unlike' : 'Like'" v-if="account.type === 'instagram.direct'">
                                                    <i class="ph ph-fill ph-heart social-icon instagram active" v-if="feedPost.options && feedPost.options.liked"></i>
                                                    <i class="ph ph-fill ph-heart" v-else></i>
                                                </button>
                                                <!-- make sure, we pass parent comment feedPost for child comment -->
                                                <button type="button" class="btn btn-sm"
                                                        :class="{'btn-outline-secondary': !isSent, 'btn-light': isSent}"
                                                        @click="replyToPost" :title="'Reply'">
                                                    <i class="ph ph-arrow-bend-up-left ph-md"></i>
                                                </button>
                                            </template>
                                            <template v-else-if="feedPost.type === 'post'">
                                                <button type="button" class="btn btn-sm"
                                                        :class="{'btn-outline-secondary': !isSent, 'btn-light': isSent}"
                                                        @click="like" :title="feedPost.options && feedPost.options.liked ? 'Unlike' : 'Like'"
                                                        v-if="!isRating && account.type === 'instagram.direct'">
                                                    <i class="ph ph-fill ph-heart social-icon instagram active" v-if="feedPost.options && feedPost.options.liked"></i>
                                                    <i class="ph ph-fill ph-heart" v-else></i>
                                                </button>
                                                <button type="button" class="btn btn-sm"
                                                        :class="{'btn-outline-secondary': !isSent, 'btn-light': isSent}"
                                                        @click="replyToPost" :title="'Reply'">
                                                    <i class="ph ph-arrow-bend-up-left ph-md"></i>
                                                </button>
                                            </template>
                                        </div>

                                        <!-- delete option -->
                                        <button type="button" class="btn btn-sm options ml-3"
                                                :class="{'btn-outline-secondary': !isSent, 'btn-light': isSent}"
                                                :disabled="deleting" @click="deletePost" :title="'Delete'" v-if="canDelete">
                                            <i class="ph ph-trash"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="d-flex align-items-start ml-4 timestamp" v-if="feedPost.type === 'message' && !isSent">
                                    <div>
                                        <small class="text-muted"
                                               :title="timestamp.format('dddd, MMMM Do YYYY, h:mm a')">
                                            <template v-if="timestamp.format('DMY') === $momentUTC().format('DMY')">
                                                {{ timestamp.format('h:mm a') }}
                                            </template>
                                            <template v-else>
                                                {{ timestamp.fromNow() }}
                                            </template>
                                        </small>
                                        <span class="unread-badge badge badge-danger mt-4" v-if="!isSent && !feedPost.read && !isRoot">NEW</span>
                                    </div>
                                </div>
                            </div>
                            <div class="small-4 text-right" v-if="feedPost.user_id && feedPost.type === 'message' && feedHasTeam && isSent && getUser(feedPost.user_id)">Sent by {{ getUser(feedPost.user_id).name }}</div>
                        </div>
                    </template>
                </div>

                <feed-post :index="i" :feed-has-team="feedHasTeam" :get-user="getUser" :indent="feedPost.type !== 'message' && (child.type === 'comment' || child.type === 'tweet' || child.type === 'private_note') && !isRoot" :show-image="shouldShowImage(i, child)" :account="account" :is-root="false" :post="child" v-for="(child, i) in feedPost.children" :key="child.id" />

            </div>
            <template v-if="isRoot">
                <div class="composer">
                    <div class="typing-indicator text-muted small-2 mb-2 font-weight-400 px-4" v-if="typingIndicator">
                        <i>{{ typingIndicator }}</i>
                    </div>
                    <div class="replying-to border-top bg-white font-weight-400" v-if="replyingToPost">
                        <div class="head badge badge-light border">
                            Replying to:
                            <span v-if="replyingToPost.type === 'private_note'">
                                {{ getUser(replyingToPost.user_id).name }}
                            </span>
                            <span v-else-if="account.type === 'twitter.profile'">
                                {{ replyingToPost.data.user.name }} <small>@{{ replyingToPost.data.user.screen_name }}</small>
                            </span>
                            <span v-else>
                                {{ replyingToPost.data.from.name }}
                            </span>
                        </div>
                        <div class="msg-quote pl-3 pr-3 pb-3">
                            <button type="button" class="close" @click="replyingToPost = null">&times;</button>
                            <div class="content">
                                {{ truncate(replyingToPost.data.message || replyingToPost.data.full_text || replyingToPost.data.content || replyingToPost.data.text || '...', {length: 140})}}
                            </div>
                        </div>
                    </div>

                    <ul class="nav nav-tabs-minimal bg-white border-top">
                        <li class="nav-item">
                            <a class="nav-link" href="#" :class="{'active': composerMode === 'reply'}" @click.prevent="composerMode = 'reply'">Reply</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" :class="{'active': composerMode === 'note'}" @click.prevent="composerMode = 'note'">Private Note</a>
                        </li>
                    </ul>

                    <div class="bg-white d-flex justify-content-around pt-3 px-2 pb-3">
                        <div class="position-relative"
                             :class="{'d-none': composerMode !== 'reply'}">
                            <img class="avatar"
                                 :src="account.image" :title="account.name" v-tooltip />

                            <i class="network-type fa social-icon active"
                               :title="account._type" v-tooltip
                               :class="{'instagram fa-instagram': ['instagram.direct', 'instagram.api'].includes(account.type), 'twitter fa-twitter-square': account.type === 'twitter.profile', 'facebook fa-facebook-square': account.type.indexOf('facebook') > -1, 'linkedin fa-linkedin-square': account.type.indexOf('linkedin') > -1}"></i>
                        </div>
                        <template v-if="false && composerMode==='reply' && account.type === 'twitter.profile' && feedPost.type === 'tweet' && feedPost.data.from_search">
                            <div class="w-80">
                                <textarea class="form-control border"
                                          title="Cannot reply to this conversation" placeholder="Cannot reply to this conversation" disabled
                                          v-tooltip></textarea>
                            </div>
                            <div class="d-flex align-items-baseline">
                                <button type="button" class="btn btn-outline-light text-dark border-secondary send-btn btn-sm disabled" disabled
                                        title="Cannot reply to this conversation" v-tooltip>
                                    <i class="ph ph-paper-plane-right ph-md"></i>
                                </button>
                            </div>
                        </template>
                        <template v-else>
                            <div class="w-80">
                                <textarea ref="composerTextarea" class="form-control  border" :title="'Type your ' + composerMode + '...'" :placeholder="'Type your ' + composerMode + '...'"
                                          @keypress.enter="send" v-model="reply" :disabled="replying" :class="{'bg-transparent': replying}" @input="eventHandler('typing', {userId: userId, postId: feedPost.id})"></textarea>
                            </div>
                            <div class="d-flex align-items-baseline">
                                <button type="button" class="btn btn-outline-light text-dark border-secondary send-btn btn-sm"
                                        :disabled="replying" @click="send">
                                    <i class="ph ph-circle-notch ph-spin" v-if="replying"></i>
                                    <i class="ph ph-paper-plane-right ph-md" v-else-if="composerMode==='reply'"></i>
                                    <i class="ph ph-lock ph-md" v-else-if="composerMode==='note'"></i>
                                </button>
                            </div>
                        </template>
                    </div>
                </div>
            </template>
        </template>
    </div>
</template>

<script>
import _ from "lodash";
import $ from "jquery";
import VuePerfectScrollbar from "vue-perfect-scrollbar";
import VueCarousel from "@chenfengyuan/vue-carousel";
const Autolinker = require("autolinker");
import { axios, alertify, appConfig } from "../../../components";

let replyingToPostVue = null;

export default {
    components: {
        VuePerfectScrollbar,
        VueCarousel
    },
    name: "FeedPost",
    props: {
        post: Object,
        account: Object,
        isRoot: Boolean,
        showImage: Boolean,
        indent: Boolean,
        index: Number,
        eventHandler: Function,
        getUser: Function,
        feedHasTeam: Boolean
    },
    data() {
        return {
            feedPost: {},
            loading: true,
            deleting: false,
            error: null,
            hasUnreadItems: false,
            reply: "",
            replyingToPost: null,
            typingIndicator: null,
            composerMode: "reply",
            replying: false,
            isRating: false // is true if current post or comment is part of a rating post
        };
    },
    computed: {
        userId() {
            return appConfig.userId;
        },
        scrollbarComponent() {
            if (this.isRoot) {
                return "VuePerfectScrollbar";
            } else {
                return "div";
            }
        },
        isSent() {
            if (this.feedPost.type === "private_note") return true;
            if (this.account.type === "twitter.profile") {
                if (this.feedPost.type === "message") {
                    return (
                        this.feedPost.data.message_create.sender_id ===
                        this.account.account_id
                    );
                } else {
                    return (
                        this.feedPost.data.user.id_str ===
                        this.account.account_id
                    );
                }
            } else if (this.account.type === "facebook.page") {
                return this.feedPost.data.from.id === this.account.account_id;
            } else if (this.account.type === "instagram.direct") {
                return (
                    String(this.feedPost.data.user.id) ===
                    this.account.account_id
                );
            } else if (this.account.type === "instagram.api") {
                return (
                    String(this.feedPost.data.from.id) ===
                    this.account.account_id
                );
            }
            return true;
        },
        timestamp() {
            if (this.feedPost.type === "private_note") {
                if (typeof this.feedPost.created_at === "string")
                    return this.$momentUTC(this.feedPost.created_at);
                else return this.$momentUTC(this.feedPost.created_at, "X");
            }
            if (this.account.type === "twitter.profile") {
                if (this.feedPost.type === "message") {
                    return this.$momentUTC(
                        Number(this.feedPost.data.created_timestamp),
                        "x"
                    );
                } else {
                    return this.$momentUTC(
                        this.feedPost.data.created_at,
                        "ddd MMM DD HH:mm:ss ZZ Y"
                    );
                }
            } else if (this.account.type === "facebook.page") {
                if (
                    this.feedPost.data.created_time &&
                    !this.feedPost.data.created_time.date
                ) {
                    if (isNaN(Number(this.feedPost.data.created_time))) {
                        return this.$momentUTC(this.feedPost.data.created_time);
                    } else {
                        return this.$momentUTC(
                            Number(this.feedPost.data.created_time),
                            "X"
                        );
                    }
                } else {
                    return this.$momentUTC(
                        this.feedPost.data.created_time.date
                    );
                }
            } else if (this.account.type === "instagram.direct") {
                if(this.post.data.timestamp){
                    if (typeof this.post.data.timestamp === "string"){
                        return this.$momentUTC(this.post.data.timestamp);
                    } else return this.$momentUTC(this.post.data.timestamp, "X");
                }
                if (typeof this.post.created_at === "string")
                    return this.$momentUTC(this.post.created_at);
                else return this.$momentUTC(this.post.created_at, "X");
            }else if (this.account.type === "instagram.api") {
                let timestamp = this.post.data.timestamp ? this.post.data.timestamp : this.post.data.timestamp;
                if (timestamp) {
                    if (isNaN(Number(timestamp))) {
                        return this.$momentUTC(timestamp);
                    } else {
                        if( String(timestamp).length > 10){ //it is in milliseconds, convert to seconds
                            return this.$momentUTC(Number(timestamp / 1000), "X");
                        } else {
                            return this.$momentUTC(Number(timestamp ), "X");
                        }
                    }
                } else return this.$momentUTC(timestamp.date);
            }
        },
        permalink() {
            if (
                this.account.type === "twitter.profile" &&
                this.feedPost.type === "tweet"
            ) {
                return (
                    "https://twitter.com/" +
                    this.feedPost.data.user.id_str +
                    "/status/" +
                    this.feedPost.data.id_str
                );
            } else if (
                this.account.type === "facebook.page" &&
                (this.feedPost.type === "post" ||
                    this.feedPost.type === "comment")
            ) {
                return "https://www.facebook.com/" + this.feedPost.data.post_id;
            } else if (
                this.account.type === "instagram.direct" &&
                this.feedPost.type === "post"
            ) {
                return "https://instagram.com/p/" + this.feedPost.data.code;
            } else if(this.feedPost.type === "post" && this.feedPost.permalink){
                return this.feedPost.permalink;
            }
            return null;
        },
        canDelete() {
            if (this.account.type === "facebook.page") {
                return true;
            } else if (this.account.type === "instagram.direct") {
                if(this.feedPost.type === 'post' && this.feedPost.data.user.id !== this.account.account_id){
                    return false; // cannot delete someone's media
                } else if(this.feedPost.type === 'comment' && this.feedPost.data.user.id !== this.account.account_id){
                    return this.callRoot("can_delete"); // returns true if root media can be deleted
                }
                return true;
            } else if (this.account.type === "instagram.api") {
                if(this.feedPost.type === 'comment'){
                    const _u = this.callRoot("get_user");
                    if(_u.id === this.account.account_id){
                        return true;
                    }
                }
                return false;
            } else {
                let otherId;
                if (this.feedPost.data.user) {
                    if (
                        this.feedPost.data.user.id_str &&
                        this.feedPost.data.user.id_str ===
                            this.account.account_id
                    ) {
                        return true;
                    } else if (
                        this.feedPost.data.user.id &&
                        this.feedPost.data.user.id === this.account.account_id
                    ) {
                        return true;
                    }
                } else if (this.feedPost.data.from) {
                    return (
                        this.account.account_id === this.feedPost.data.from.id
                    );
                }
            }
            return false;
        },
        textContent() {
            let content = "";
            const d = this.feedPost.data;
            if (this.account.type === "twitter.profile") {
                if (this.feedPost.type === "message") {
                    content = d.attachments.length
                        ? d.message_create.message_data.text.replace(
                              d.message_create.message_data.attachment.media
                                  .url,
                              ""
                          )
                        : d.message_create.message_data.text;
                    content = Autolinker.link(content, {
                        className: "external-link"
                    });
                } else {
                    content = d.full_text;
                    // replace t.co urls where needed
                    const entities = d.entities,
                        extended_entities = d.extended_entities;
                    entities &&
                        entities.urls &&
                        entities.urls.forEach(uInfo => {
                            //content = content.substr(0, uInfo.indices[0]) + `<a class="external-link" href="${uInfo.expanded_url}" target="_blank">${uInfo.display_url}</a>` + content.substr(uInfo.indices[1]);
                            content = content.replace(
                                uInfo.url,
                                `<a class="external-link" href="${
                                    uInfo.expanded_url
                                }" target="_blank">${uInfo.display_url}</a>`
                            );
                        });
                    extended_entities &&
                        extended_entities.media &&
                        extended_entities.media.forEach(uInfo => {
                            // strip the media url part because we include image/attachment
                            //content = content.substr(0, uInfo.indices[0]) + content.substr(uInfo.indices[1]);
                            content = content.replace(uInfo.url, "");
                        });

                    content = Autolinker.link(content, {
                        className: "external-link",
                        hashtag: "twitter",
                        mention: "twitter"
                    });
                }
            } else if (this.account.type === "instagram.direct") {
                if(this.feedPost.type === 'post') return "";
                content = Autolinker.link(d.text, {
                    className: "external-link"
                });
            } else if (this.account.type === "instagram.api") {
                if(this.feedPost.type === 'post') return "";
                if(this.feedPost.type === 'message'){
                    content = Autolinker.link(d.message, {
                        className: "external-link"
                    });
                }else {
                    content = Autolinker.link(d.text, {
                        className: "external-link"
                    });
                }
            } else {
                content = Autolinker.link(d.message, {
                    className: "external-link"
                });
            }
            return content;
        }
    },
    methods: {
        truncate: _.truncate,
        setAltImage(event){
            event.target.src = '/images/no-image.png'
        },
        autoLink(content) {
            return Autolinker.link(content, {
                className: "external-link"
            });
        },
        async initialize() {
            this.loading = true;
            try {
                const { data } = await axios.get(
                    "/app/respond/feeds/" + this.$root.id + "/posts/" + this.post.id
                );
                _.extend(this.feedPost, data);
            } catch (e) {
                this.error = e.message || "Unable to load data";
            }
            this.loading = false;
            this.$nextTick(() => {
                $(this.$refs.composerTextarea).autosize();
            });
        },
        async markRead() {
            try {
                const { data } = await axios.patch(
                    "/app/respond/feeds/" +
                        this.$root.id +
                        "/posts/" +
                        this.post.id +
                        "/mark_read"
                );
                this.$root.markRead(this.post.id);
            } catch (e) {
                console.error(e);
            }
        },
        updateTypingIndicator(t) {
            this.typingIndicator = t;
        },
        removeNewIndicator() {
            if (!this.feedPost.read) {
                this.feedPost.read = true;
                this.$forceUpdate();
                this.$nextTick(() => {
                    this.callRoot("refresh_unread");
                });
            }
        },
        shouldShowImage(i, child) {
            if (this.feedPost.type !== "message") return true; // only show for messages

            if (child.type === "private_note") return false;

            if (this.account.type === "twitter.profile") {
                if (
                    child.data.message_create.sender_id ===
                    this.account.account_id
                )
                    return false; // no img for sent msgs
                if (i === 0)
                    return (
                        this.feedPost.data.message_create.sender_id !==
                        child.data.message_create.sender_id
                    );
                else
                    return (
                        this.feedPost.children[i - 1].data.message_create
                            .sender_id !== child.data.message_create.sender_id
                    );
            } else if (this.account.type === "facebook.page") {
                if (child.data.from.id === this.account.account_id)
                    return false; // no img for sent msgs
                if (i === 0)
                    return this.feedPost.data.from.id !== child.data.from.id;
                else
                    return (
                        this.feedPost.children[i - 1].data.from.id !==
                        child.data.from.id
                    );
            } else if (this.account.type === "instagram.direct") {
                if (child.data.user.id === this.account.account_id)
                    return false; // no img for sent msgs
                if (i === 0)
                    return this.feedPost.data.user.id !== child.data.user.id;
                else
                    return this.feedPost.children[i - 1].data &&
                        this.feedPost.children[i - 1].data.user
                        ? this.feedPost.children[i - 1].data.user.id !==
                              child.data.user.id
                        : true;
            }
            return true;
        },
        replyToPost() {
            this.callRoot("reply", {
                feedPost: this.feedPost.data.parent_comment_id
                    ? this.$parent.feedPost
                    : this.feedPost,
                vue: this
            });
        },
        async send(e) {
            if (e.shiftKey) {
                return;
            }
            e.preventDefault();
            if (!this.reply.trim().length) return;
            this.replying = true;
            try {
                const { data } = await axios.post(
                    "/app/respond/feeds/" +
                        this.$root.id +
                        "/posts/" +
                        (this.replyingToPost
                            ? this.replyingToPost.id
                            : this.feedPost.id) +
                        "/" +
                        this.composerMode,
                    {
                        reply: this.reply
                    }
                );
                // `data` should be a feedPost object ready to be inserted into UI
                if (data.id) {
                    (this.replyingToPost
                        ? this.replyingToPost
                        : this.feedPost
                    ).children.push(data);
                    (this.replyingToPost
                        ? replyingToPostVue
                        : this
                    ).$forceUpdate(); // workaround as Ui is not updated sometimes
                }
                if (this.replyingToPost) {
                    this.replyingToPost = null;
                    replyingToPostVue = null;
                }
                this.replying = false;
                this.reply = "";
            } catch (e) {
                this.replying = false;
                this.$nextTick(() => {
                    $(this.$refs.composerTextarea).focus();
                });
                (() => {
                    if (e.response && e.response.data) {
                        const errors = e.response.data.errors;
                        if (errors) {
                            Object.keys(errors).forEach(k => {
                                alertify.error(errors[k].join(" \n"));
                            });
                            return;
                        }
                        if (e.response.data && e.response.data.message) {
                            return alertify.error(e.response.data.message);
                        }
                    }
                    alertify.error(e.message || "An error occurred.");
                })();
            }
        },
        async like() {
            try {
                const { data } = await axios.post(
                    "/app/respond/feeds/" +
                        this.$root.id +
                        "/posts/" +
                        this.feedPost.id +
                        "/like"
                );
                this.feedPost.options = data || null;
                this.$forceUpdate(); // workaround
            } catch (e) {
                alertify.error(e.message || "Unknown error");
            }
        },
        async retweet() {
            try {
                const { data } = await axios.post(
                    "/app/respond/feeds/" +
                        this.$root.id +
                        "/posts/" +
                        this.feedPost.id +
                        "/retweet"
                );
                this.feedPost.options = data || null;
                this.$forceUpdate(); // workaround
            } catch (e) {
                alertify.error(e.message || "Unknown error");
            }
        },
        deletePost() {
            if (this.deleting) return;
            alertify.delete("Are you sure you want to delete this post?", async () => {
                this.deleting = true;
                try {
                    const { data } = await axios.delete(
                        "/app/respond/feeds/" +
                            this.$root.id +
                            "/posts/" +
                            this.feedPost.id +
                            "?external_delete=true"
                    );
                    if (this.isRoot) {
                        document.location.reload();
                        return;
                    }
                    // remove this post
                    this.getParent(parent => {
                        parent.feedPost.children.some((feedPost, index) => {
                            if (feedPost.id === this.feedPost.id) {
                                parent.feedPost.children.splice(index, 1);
                                return true;
                            }
                        });
                        parent.$forceUpdate(); // workaround for the reply on first level
                    });
                } catch (e) {
                    alertify.error(e.message || "Unknown error occurred");
                }
                this.deleting = false;
            });
        },
        getParent(callback) {
            let desiredComp = this.$parent;
            while (!desiredComp.feedPost) {
                desiredComp = desiredComp.$parent;
            }
            callback(desiredComp);
        },
        callRoot(action, payload = {}) {
            if (!this.isRoot) {
                let desiredComp = this.$parent;
                while (!desiredComp.feedPost) {
                    desiredComp = desiredComp.$parent;
                }
                return desiredComp.callRoot(action, payload);
            }
            if (action === "refresh_unread" && this.$refs.msgsArea) {
                const $msgsArea = $(this.$refs.msgsArea.$el);
                this.hasUnreadItems =
                    $msgsArea.find(".unread-badge").length > 0;
            } else if (action === "jump_to_unread_item" && this.$refs.msgsArea) {
                const $msgsArea = $(this.$refs.msgsArea.$el);
                if ($msgsArea.find(".unread-badge").length) {
                    $msgsArea.scrollTop(
                        $msgsArea.scrollTop() +
                            $msgsArea
                                .find(".unread-badge")
                                .eq(0)
                                .position().top
                    );
                    $msgsArea
                        .find(".unread-badge")
                        .eq(0)
                        .click();
                } else $msgsArea[0].scrollTop = $msgsArea[0].scrollHeight;
            } else if (action === "reply") {
                // if the post is root post, no need to set replying to
                this.replyingToPost =
                    payload.feedPost.id !== this.feedPost.id
                        ? payload.feedPost
                        : null;
                replyingToPostVue =
                    payload.feedPost.id !== this.feedPost.id
                        ? payload.vue
                        : null;
                if (
                    this.replyingToPost &&
                    this.replyingToPost.type === "private_note"
                ) {
                    this.composerMode = "note";
                } else {
                    this.composerMode = "reply";
                }
                const $txtArea = $(this.$refs.composerTextarea);
                $("html, body").animate(
                    {
                        scrollTop: $txtArea.offset().top
                    },
                    10
                );
                $txtArea.focus();
            } else if (action === "can_delete"){
                return this.canDelete;
            } else if (action === "get_user"){
                return this.feedPost.data.user;
            } else {
                if(this.$refs.msgsArea)
                    throw new Error("Unknown action");
            }
        }
    },
    async mounted() {
        if (this.isRoot) {
            await this.initialize();
            setTimeout(() => {
                this.callRoot("refresh_unread");
                this.$nextTick(() => {
                    // scroll to first unread msg/item
                    this.callRoot("jump_to_unread_item");
                });
                try {
                    FB.XFBML.parse(); // re parse DOM for any FB-post rendering (specifically for VIDEO post)
                } catch (e) {
                    console.error(e);
                }
            }, 500);
            if (this.account.type === "facebook.page") {
                this.isRating = this.feedPost.data && this.feedPost.data.item === "rating";
            }
            this.markRead();
        } else {
            this.feedPost = this.post;
            if (this.account.type === "facebook.page") {
                this.getParent(
                    p => (this.isRating = p.feedPost.data.item === "rating")
                );
            }
            this.$nextTick(() => {
                this.loading = false;
            });
        }
        // make sure `children` is always set
        if (!this.feedPost.children) this.feedPost.children = [];
    }
};
</script>

<style lang="scss" scoped>
.msg-container {
    &.is-root {
        position: relative;
        min-height: 400px;
        max-height: calc(100vh - 270px);
        overflow-y: auto;
    }
}
.deleting {
    opacity: 0.5;
}
.unread-indicator {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    position: absolute;
    border-bottom-right-radius: 0;
    top: -1px;
    right: -1px;
    z-index: 1;
}
.msg {
    font-size: 0.85rem;
    line-height: 1.5;
    .sender-img {
        min-width: 48px;
        img {
            border-radius: 50%;
            width: 48px;
            height: 48px;
        }
    }
    .ig-caption {
        white-space: pre-line;
        a {
            color: inherit !important;
        }
    }
    .content-container {
        .content-wrapper {
            border-radius: 0.4em;
            position: relative;
            min-width: 130px;
            &:before {
                content: "";
                position: absolute;
                top: 0;
                width: 0;
                height: 0;
                border: 18px solid transparent;
                border-top: 0;
            }
            .content {
                img {
                    max-width: 100%;
                    width: auto;
                    max-height: 400px;
                    height: auto;
                }
                .content-text {
                    white-space: pre-line;
                    font-size: 1rem;
                }
            }
        }
    }
    &.chat {
        max-width: 75%;
    }
    &.received.chat {
        .timestamp {
            small {
                position: absolute;
            }
        }
        .content-container {
            .content-wrapper.has-user-image {
                border-radius: 0.4em;
                border-top-left-radius: 0;
                &:before {
                    left: 0;
                    border-right-color: #fff;
                    border-left: 0;
                    margin-left: -18px;
                }
            }
        }
    }
    .post-footer {
        //background: rgba(255, 255, 255, 0.3);
        padding: 0.4rem;
        .options {
            opacity: 0;
            transition: all 0.1s linear;
        }
    }
    &:hover,
    &.received {
        .post-footer {
            .options {
                opacity: 1;
            }
        }
    }
    &.received {
        .post-footer {
            //background: rgba(0, 0, 0, 0.05);
        }
    }
}
.composer {
    .replying-to {
        border-top-right-radius: 15px;
        border-top-left-radius: 15px;
        .head {
            position: relative;
            left: 15px;
            top: -10px;
        }
        button.close {
            margin-top: -15px;
        }
    }
    .network-type {
        position: absolute;
        top: 40px;
        left: 40px;
    }
    .send-btn {
        border-radius: 50%;
        line-height: 26px;
    }
}
</style>
