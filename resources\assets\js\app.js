// our core bootstrapping code
import "./bootstrap";

// import our vuejs code
import "./vue";
import { axios } from "./components";
import Cookies from "js-cookie";
import lozad from "lozad";

// import tour js
import tour from "./tour";

import SlideoutModal from "./slideout_modal";

// jQuery document.ready dependent code
let $sidebar;
jQuery(function($) {
    // initialize tooltip
    $('[data-toggle="tooltip"]').each((i, el) => {
        $(el)
            .tooltip()
            .on("click.tooltip_autohide", () => {
                setTimeout(() => {
                    try {
                        $(el).tooltip("hide");
                    } catch (e) {}
                }, 200);
            });
    });
     // lazy load images
     const observer = lozad(); // lazy loads elements with default selector as '.lozad'
     observer.observe();

    // remove onboarding checklist
    $('#removeChecklist').on('click',async function() {
        try {
            const res =  await axios.post("/app/onboarding/remove_checklist")
            if(res.status === 200){
                $('#checklistSection').remove();
            }
        } catch (error) {
            alert(error.message);
        }
    });

    // sidebar menu
    (async () => {
        $sidebar = $("#sidebar_menu_wrapper");

        const setupSubmenuCaret = $el => {
            $el.find(".nav-link").each(function() {
                const $navLink = $(this);
                const $icon = $navLink.find('.submenu-caret i');
                const $submenu = $($navLink.find('.submenu-caret').data('target'));
    
                // Check if the nav-link is active on page load
                if ($navLink.hasClass('active')) {
                    $icon.removeClass('ph-caret-down text-secondary').addClass('ph-caret-up text-primary');
                    $submenu.collapse('show'); // Ensure submenu is shown if nav-link is active
                }
    
                // Handle click events to toggle the submenu and icon
                $navLink.find(".submenu-caret").on("click", function(e) {
                    // Toggle submenu
                    $submenu.collapse('toggle');
    
                    // Toggle the icon and color
                    $icon.toggleClass('ph-caret-down ph-caret-up text-secondary text-primary');
    
                    e.stopPropagation();
                    return false;
                });
            });
            $el.find(".submenu").collapse({
                toggle: false
            });
        };

        // sidebar submenu
        setupSubmenuCaret($sidebar);

        // sidebar onclick toggle for mobile
        const $sidebarToggle = $("#sidebar_toggle"),
            $main = $("main.main");
        if ($sidebarToggle.is(":visible")) {
            // is visible with class: d-md-none
            // hide the sidebar
            $sidebar.addClass("d-none");
            $main.addClass("has-sidebar-toggle");
        }
        const menuModal = await SlideoutModal.create("SocialBu", {
            type: "left",
            class: "modal-sm",
            fade: false,
            onCreated(el) {
                const $menuContents = $("#sidebar-menu").html();
                const $menu = $("<div id='sidebar-menu-mobile' />").append($menuContents);
                $(el)
                    .find(".modal-body")
                    .addClass("bg-light p-0")
                    .empty()
                    .append($menu);
                setupSubmenuCaret($menu);
            },
            onOpen(){
                $sidebarToggle.attr("data-state", 'open');
            },
            onClose(){
                $sidebarToggle.attr("data-state", 'close');
            }

        });
        $sidebarToggle.on("click", e => {
            if( menuModal.isOpen() ){
                window.__recordUsageEvent("menu_mobile_close");
            } else {
                window.__recordUsageEvent("menu_mobile_open");
            }
            menuModal.toggle();
            e.stopPropagation();
            return false;
        });
    })();

    // tour
    $(window).ready(() => {
        // welcome
        tour.addStep(
            "welcome-1",
            `We have been thrilled to have you in our Bu Family. <br/><br/>Let's learn a few things quickly.<br/><br/>Remember, we are always here to <a href="/help" target="_blank" class="text-b">help</a> via <abbr title="Look out for the chat icon on bottom right">chat</abbr>, <a href="mailto:<EMAIL>">email</a>, or even a <abbr title="Send us a message and we can schedule the call">call</abbr>`,
            null,
            {
                title: "Welcome!"
            }
        );
        // tour for on-page elems
        const setupTour = () => {
            $("[data-tour]:visible").each((i, elem) => {
                const opts = String($(elem).attr("data-tour") || "")
                    .split(/; */)
                    .reduce((obj, str) => {
                        if (str === "") return obj;
                        let eq = str.indexOf("=");
                        if (eq === -1) eq = str.indexOf(":");
                        const key = eq > 0 ? str.slice(0, eq) : str;
                        let val = eq > 0 ? str.slice(eq + 1) : null;
                        if (val != null)
                            try {
                                val = decodeURIComponent(val);
                            } catch (ex) {
                                /* pass */
                            }
                        obj[key] = val ? val : true;
                        return obj;
                    }, {});

                tour.addStep(opts.name || opts.id, opts.text, elem, {
                    position: opts.position,
                    title: opts.title,
                    noArrow: opts.noarrow
                });

                $(elem).removeAttr("data-tour");
            });
        };
        $(document).on("shown.bs.modal", setupTour); // for modals
        $(document).on("setup_tour", setupTour); // for custom triggering
        setupTour();
    });
});

// to load script via ajax
window.__loadScript = (name, cb = m => {}) => {
    if (name === "accounts") {
        require(["./scripts/accounts"], cb);
    } else if (name === "teams") {
        require(["./scripts/teams"], cb);
    } else if (name === "settings") {
        require(["./scripts/settings"], cb);
    } else if (name === "analyze_general") {
        require(["./scripts/analyze_general"], cb);
    } else if (name === "create_content") {
        require(["./scripts/create_content"], cb);
    } else if (name === "posts") {
        require(["./scripts/posts"], cb);
    } else if (name === "link_shorteners") {
        require(["./scripts/link_shorteners"], cb);
    } else if (name === "chat_bu") {
        require(["./scripts/chat_bu"], cb);
    }
};
const isMobile = () => {
    const userAgent = navigator.userAgent.toLowerCase();
    return (/android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i
            .test(userAgent));
};

$(window).ready(async () => {
    //if mobile or extension install or cookies has the flag, no need to show the extension modal
    if(isMobile() || window.__SocialBu_webextension || Cookies.get('extension_modal_shown')) { 
        return;
    }
    try {
        const response = await axios.get('/app/extension-modal-status');
        const data = response.data;
        const showExtensionModal = data.show_extension_modal;

        if (showExtensionModal) {

            const modalElement = document.getElementById('install-extension-modal');
            if (modalElement) {
                $(modalElement).modal('show');

                //set in cookies that modal is shown
                Cookies.set('extension_modal_shown', 'true');

                // modal shown once, so update status
                try {
                    const response = await axios.patch( '/app/extension-modal-status',{ 
                        extension_modal_shown: true 
                    });
                } catch (error) {
                    console.error('Error setting flag to true:', error);
                }
            }
        }
    } catch (error) {
        console.error('Error fetching chrome extension flag:', error);
    }
});


window.__recordCrmEvent = async (event, data = "") => {
    try {
        await axios.post("/app/_record_event", {
            event,
            data
        });
    } catch (e){ }
};

// refresh sidebar vars
let __infoRefreshTimer = null;
window.__refreshInfo = async () => {
    if (__infoRefreshTimer) {
        clearTimeout(__infoRefreshTimer);
        __infoRefreshTimer = null;
    }
    if (!$sidebar) return (__infoRefreshTimer = setTimeout(window.__refreshInfo, 500));

    try {
        const { data } = await axios.get("/app/info");
        $sidebar.find("[data-var]").each(function(i, elem) {
            const $elem = $(elem);
            const dataVar = $elem.attr("data-var");
            const prepend = $elem.attr("data-prepend");
            const append = $elem.attr("data-append");
            if (data[dataVar]) {
                $elem.empty();
                if (prepend) $elem.append(prepend);
                $elem.append(data[dataVar]);
                if (append) $elem.append(append);
                $elem.show();
            } else {
                $elem.hide();
            }
        });
    } catch (e) {
        console.error(e);
    }

    __infoRefreshTimer = setTimeout(window.__refreshInfo, 60000);
};
$(window.__refreshInfo);
