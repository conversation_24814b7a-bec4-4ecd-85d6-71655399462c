@extends('layout.full_width')
@section('title', count($list) . ' Best Social Media Scheduling Tools For ' . ucwords($industry) . ' | ' . config('app.name'))
@push('head_html')

    <meta name="description" content="A quick overview of {{ count($list) }} social media scheduling tools which are known to work good for {{ ucwords($industry) }}. Find and compare multiple social media scheduling tools to find out the best one."/>
    <meta property="og:locale" content="en_US" />
    <meta property="og:type" content="website" />
    <meta property="og:title" content="Social Media Scheduling Tools For {{ ucwords($industry) }}" />
    <meta property="og:description" content="A quick overview of {{ count($list) }} social media scheduling tools which are known to work good for {{ ucwords($industry) }}. Find and compare multiple social media scheduling tools to find out the best one." />
    <meta property="og:url" content="https://socialbu.com/social-media-scheduling-tools/for-{{ $industry }}" />
    <meta property="og:site_name" content="SocialBu" />
    <meta property="og:image" content="https://socialbu.com/images/site/link-preview.jpg" />
    <meta property="fb:app_id" content="{{ config('services.facebook.client_id') }}" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content="https://socialbu.com/images/site/link-preview.jpg" />
    <meta name="twitter:title" content="Social Media Scheduling Tools For {{ ucwords($industry) }}" />
    <meta name="twitter:description" content="A quick overview of {{ count($list) }} social media scheduling tools which are known to work good for {{ ucwords($industry) }}. Find and compare multiple social media scheduling tools to find out the best one." />
    <meta name="twitter:site" content="@socialbuapp" />


    <link rel="canonical" href="https://socialbu.com/social-media-scheduling-tools/for-{{ $industry }}" />
    <style>
        .stickytop{
            position: sticky;
            top: 45px;
            z-index: 10;
        }
    </style>
@endpush
@section('content')
    <header class="header text-center pb-0">
        <div class="container">
            <h1 class="display-2">Social Media Scheduling Tools For {{ ucwords($industry) }}</h1>
            <p class="lead-2 mt-6">A quick overview of {{ count($list) }} social media scheduling tools which are known to work good for {{ ucwords($industry) }}.</p>
        </div>
    </header>
    <main class="main-content">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/">Home</a></li>
                    <li class="breadcrumb-item"><a href="/social-media-scheduling-tools/">Social Media Scheduling Tools</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Social Media Scheduling Tools For {{ ucwords($industry) }}</li>
                </ol>
            </nav>
        </div>
        <section class="section bg-light">
            <div class="container">

                <div class="row gap-y">
                    <div class="d-none d-md-block col-md-4 mx-auto">
                        <h5 class="stickytop mb-5">Related pages</h5>
                        @include('common.social_media_tools.sidebar')
                    </div>
                    <div class="col-md-8 col-md-offset-1">

                        @foreach($list as $i => $row)
                            <div class="card hover-shadow-7 p-40 border bg-white {{ $row['name'] === 'SocialBu' ? 'border-primary': 'border-secondary' }}{{ $i === 0 ? ' mt-0 mb-4': ' my-4'}}">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="pl-4">
                                            <h4 class="d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <span class="mr-2">{{ $loop->index + 1}}.</span>
                                                    <a href="{{ $row['name'] === 'SocialBu' ? '/auth/register': '/compare/' . strtolower($row['slug']) . '-alternative' }}">
                                                        {{ $row['name'] }}
                                                    </a>
                                                    @if($row['name'] === 'SocialBu')
                                                        <span class="badge badge-success rounded ml-2">
                                                            Best
                                                        </span>
                                                    @endif
                                                </div>
                                                <strong class="bold d-none d-sm-inline" style="font-size: 18px;">{{ $row['starting_price'] ?  $row['starting_price'] : 'N/A' }}</strong>
                                                <strong class="bold d-inline d-sm-none" style="font-size: 16px;">
                                                    {{ $row['starting_price'] ? $row['starting_price'] : 'N/A' }}
                                                </strong>
                                            </h4>
                                            
                                            <div class="d-flex justify-content-center my-4">
                                                @if($row['name'] === 'SocialBu')
                                                    <a href="/auth/register" class="h-100 d-flex align-items-start">
                                                        @if(!empty($row['logo']))
                                                            <img class="d-none d-md-block lozad {{ $row['logo_is_white'] ? 'bg-dark':'' }}" style="width:{{ strtolower($row['logo_category']) === 'square' ? '100px' : '160px' }};" src="/images/1x1.gif" data-src="{{ $row['logo'] }}" alt="{{ $row['name'] }} logo">
                                                        @else
                                                            <img class="d-none d-md-block {{ $row['logo_is_white'] ? 'bg-dark':'' }}" style="width:100px" src="/images/email/spacer.gif" alt="{{ $row['name'] }}">
                                                        @endif
                                                    </a>
                                                @else
                                                    <a href="/social-media-scheduling-tools/socialbu-vs-{{ $row['slug'] }}" class="h-100 d-flex align-items-start">
                                                        @if(!empty($row['logo']))
                                                            <img class="d-none d-md-block   lozad {{ $row['logo_is_white'] ? 'bg-dark':'' }}" style="width:{{ strtolower($row['logo_category']) === 'square' ? '100px' : '160px' }};" src="/images/1x1.gif" data-src="{{ $row['logo'] }}" alt="{{ $row['name'] }} logo">
                                                        @else
                                                            <img class="d-none d-md-block   {{ $row['logo_is_white'] ? 'bg-dark':'' }}" style="width:100px" src="/images/email/spacer.gif" alt="{{ $row['name'] }}">
                                                        @endif
                                                    </a>
                                                @endif
                                            </div>
                                            <p class="{{ $row['name'] === 'SocialBu' ? 'lead-2': 'lead-2' }}">

                                                <strong class="strong">Supported Networks: </strong> <br/> {{ !empty($row['networks']) ? implode(', ', $row['networks']) : 'N/A' }}<br/>
                                                <br/>
                                                <strong class="strong">Customer support: </strong> <br/> {{ $row['live_chat'] ? 'Live Chat' : 'N/A'}} <br/><br/>
                                                
                                                @if(!empty($row['starting_price']))
                                                    <strong class="strong">Starting Price</strong> <br/>  {{ $row['starting_price'] ? 'Paid Plan starts at ' . $row['starting_price'] : 'N/A'  }}
                                                    @if(!empty($row['starting_price_notes']))
                                                        <i class="ph ph-info ph-md pl-1" title="{{ implode('. ', $row['starting_price_notes']) }}" data-toggle="tooltip"></i>
                                                    @endif
                                                @else
                                                    Pricing not available
                                                @endif
                                            </p>
                                            <div class="pt-2">
                                                @if($row['name'] !== 'SocialBu')
                                                    
                                                        <a href="/compare/{{ $row['slug'] }}" class="btn  btn-outline-secondary mr-2 mt-md-0 mt-4">
                                                            Compare with Others
                                                        </a>
                                                        <a href="/compare/{{ $row['slug'] . '-alternative' }}" class="btn  btn-primary mt-md-0 mt-4">
                                                            Compare with SocialBu
                                                        </a>
                                                @else
                                                    <a href="/auth/register" class="btn  btn-primary">
                                                        Start for Free
                                                    </a>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        @endforeach
                    </div>
                </div>
                <div class="d-flex justify-content-between flex-column flex-md-row py-7 py-md-8">
                    <div class="card bg-white col-12 col-md-6 mr-md-2 mb-4 mb-md-0 p-6">
                        <div class="card-body p-6">
                            <h4 class="mb-3 display-4 font-weight-800 ml-5 ml-md-0">Get 7 days for free</h4>
                            <p class="lead">
                                SocialBu gives you 7 days free trial. You can cancel anytime or downgrade to the free plan.
                            </p>
                            <div class="text-center text-md-left mt-4 w-100">
                                <a href="/auth/register" class="btn btn-primary w-100 w-md-auto" aria-label="Try for Free">
                                    Try for Free <i class="ph ph-arrow-right ph-md" aria-hidden="true"></i>
                                </a>
                            </div> 
                        </div>
                    </div>
                    <div class="card bg-white col-12 col-md-6 p-6">
                        <div class="card-body p-6">
                            <h4 class="mb-3 display-4 d-none d-md-block mb-6">Have questions? Let's talk.</h4>
                            <h4 class="display-4 d-block d-md-none mb-5 text-center">Have questions? <br>Let's talk.</h4>
                            <div class="d-flex flex-column align-items-center align-items-md-start w-100 mt-4">
                                <a class="btn btn-outline-primary w-100 w-md-auto mb-3" href="#" onclick="typeof Beacon !== 'undefined' && Beacon('open');return false;">Live Chat</a>
                                <a class="btn btn-outline-primary w-100 w-md-auto" href="mailto:<EMAIL>">Email</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        
    </main>
@endsection
