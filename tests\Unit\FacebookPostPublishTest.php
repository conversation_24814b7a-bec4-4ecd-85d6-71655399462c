<?php

namespace Tests\Unit;

use App\Account;
use App\User;
use Facebook\Exceptions\FacebookSDKException;
use Illuminate\Support\Str;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Validation\ValidationException;
use LinkedIn\Exception;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Tests\TestCase;

class FacebookPostPublishTest extends TestCase
{
    use RefreshDatabase;
    private $account = null;
    private $publishedPosts = [];

    private $tempFiles = [];

    /**
     * Authenticates a user for testing.
     *
     * @return User The authenticated user instance.
     */
    protected function authenticate(): User
    {
        if (\Auth::check()) {
            return \Auth::user();
        }

        $user = factory(User::class)->create();
        $this->actingAs($user);

        return $user;
    }

    /**
     * @throws Exception
     * @throws FacebookSDKException
     * @throws \Exception
     */
    public function testStandardPostWithText()
    {

        $account = $this->getAccount();
    
        if (!$account) {
            $this->markTestSkipped('No active Facebook account found for testing. To add a test account, please create a Facebook account with the name "FacebookTestAccount" and set it as active.');
        }

        $post = $this->publishPost('Testing Facebook Post');

        $this->assertNotNull($post->published_at, 'Post was not published successfully.');

        // Check if the text was published successfully
        $response = $account->getApi()->get('/' . $post->external_id);
        $data = $response->getDecodedBody();

        $this->assertNotEmpty($data, 'Facebook API did not return any post data.');
        $this->assertEquals('Testing Facebook Post', $data['message'] ?? '', 'Post content does not match expected message.');
        
    }

    /**
     * @throws FacebookSDKException
     * @throws Exception
     * @throws \Exception
     */
    public function testStandardPostWithFirstComment()
    {
        $account = $this->getAccount();

        if (!$account) {
            $this->markTestSkipped('No active Facebook account found for testing. To add a test account, please create a Facebook account with the name "FacebookTestAccount" and set it as active.');
        }

        $post = $this->publishPost('Testing post',[],[
            'comment' => 'first comment on fb post',
        ]);

        $this->assertNotNull($post->published_at, 'Post was not published successfully.');

        // Check if the text was published successfully
        $response = $account->getApi()->get('/' . $post->external_id . '?fields=message');
        $data = $response->getDecodedBody();
        $this->assertArrayHasKey('message', $data, 'Message field not found in Facebook response.');
        $this->assertEquals('Testing post', $data['message'], 'Facebook post message does not match expected value.');

        // Check if the comment was published successfully
        $commentsResponse = $account->getApi()->get('/' . $post->external_id . '/comments?fields=message');
        $commentsData = $commentsResponse->getDecodedBody();
        $this->assertNotEmpty($commentsData['data'] ?? [], 'No comments found on the post.');
        $firstComment = $commentsData['data'][0]['message'] ?? null;
        $this->assertEquals('first comment on fb post', $firstComment, 'First comment does not match.');

  
    }

    /**
     * @throws FacebookSDKException
     * @throws Exception
     * @throws \Exception
     */
    public function testStandardPostWithPhotos()
    {
        $account = $this->getAccount();

        if (!$account) {
            $this->markTestSkipped('No active Facebook account found for testing. To add a test account, please create a Facebook account with the name "FacebookTestAccount" and set it as active.');
        }

        $post = $this->publishPost('Testing: post with photos', $this->getAttachmentsArray([
            resource_path('assets/test_data/test_photo1.png'),
        ]));

        $this->assertNotNull($post->published_at, 'Post was not published successfully.');

        // Check if the text was published successfully
        $response = $account->getApi()->get('/' . $post->external_id . '?fields=name');
        $data = $response->getDecodedBody();
        $this->assertArrayHasKey('name', $data, 'Message field not found in Facebook response.');
        $this->assertEquals('Testing: post with photos', $data['name'] ?? '', 'Post content does not match expected value.');

        // Check if the attachment was published successfully
        $response = $account->getApi()->get('/' . $post->external_id . '?fields=source,name,created_time');
        $data = $response->getDecodedBody();
        $this->assertArrayHasKey('source', $data, 'No attachments field in post data.');
        $this->assertNotEmpty($data['source'], 'Photo source URL is empty.');


    }

    /**
     * @throws FacebookSDKException
     * @throws Exception
     * @throws \Exception
     */
    public function testStandardPostWithPhotosAndAltText()
    {

        $account = $this->getAccount();

        if (!$account) {
            $this->markTestSkipped('No active Facebook account found for testing. To add a test account, please create a Facebook account with the name "FacebookTestAccount" and set it as active.');
        }

        // posting an image post with a text on facebook with all options
        $post = $this->publishPost('testing post', $this->getAttachmentsArray([
            resource_path('assets/test_data/test_photo1.png'),
        ]),[
            'media_alt_text' => ['Test photo:: colors'],    
        ]);

        $this->assertNotNull($post->published_at, 'Post was not published successfully.');

        //now check if the post has text
        $response = $account->getApi()->get('/' . $post->external_id . '?fields=name');
        $data = $response->getDecodedBody();
        $this->assertArrayHasKey('name', $data, 'Message field not found in Facebook response.');
        $this->assertEquals('testing post', $data['name'] ?? '', 'Post content does not match expected value.');

        //now check if the post has attachments
        $response = $account->getApi()->get('/' . $post->external_id . '?fields=source,name,created_time,alt_text');
        $data = $response->getDecodedBody();
        $this->assertArrayHasKey('source', $data, 'No attachments field in post data.');
        $this->assertNotEmpty($data['source'], 'Photo source URL is empty.');

        // Check if the alt text is set
        $this->assertArrayHasKey('alt_text', $data, 'No Alt text field in post data.');
        $this->assertEquals('Test photo:: colors',$data['alt_text'], 'Photo Alt text does not match expected value.');
  
    }

    /**
     * @throws Exception
     * @throws FacebookSDKException
     * @throws \Exception
     */
    public function testStandardPostWithVideo()
    {
        
        $account = $this->getAccount();    

        if (!$account) {
            $this->markTestSkipped('No active Facebook account found for testing. To add a test account, please create a Facebook account with the name "FacebookTestAccount" and set it as active.');
        }

        // posting a video post with a text on facebook
        $post = $this->publishPost('Testing:: video post', $this->getAttachmentsArray([
            resource_path('assets/test_data/test_video.mp4'),
        ]),[
            'video_title' => 'Test: our video title',
            'comment' => 'test: first comment on fb post',
        ]);
    
        //Check video source URL
        $this->assertNotNull($post->published_at, 'Post was not published successfully.');
        $response = $account->getApi()->get('/' . $post->external_id . '?fields=source,title,description,created_time');
        $data = $response->getDecodedBody();
        $this->assertArrayHasKey('source', $data, 'Video source (URL) not found.');
        $this->assertNotEmpty($data['source'], 'Video source URL is empty.');

    }

    /**
     * @throws Exception
     * @throws FacebookSDKException
     * @throws ValidationException
     * @throws \Exception
     */
    public function testStandardPostAsStory()
    {
      
        $account = $this->getAccount();
      
        if (!$account) {
            $this->markTestSkipped('No active Facebook account found for testing. To add a test account, please create a Facebook account with the name "FacebookTestAccount" and set it as active.');
        }

        // posting a story post with a text on facebook
        $post = $this->publishPost('testing story post', $this->getAttachmentsArray([
            resource_path('assets/test_data/test_video.mp4'),
        ]),[
            'post_as_story' => true,
        ]);
    
        $this->assertNotNull($post->published_at, 'Post was not published successfully.');

        //Check video source URL
        $response = $account->getApi()->get('/' . $post->external_id . '?fields=source,title,description,created_time');
        $data = $response->getDecodedBody();
        $this->assertArrayHasKey('source', $data, 'Video source (URL) not found.');
        $this->assertNotEmpty($data['source'], 'Video source URL is empty.');

        $pageId = $account->account_id;
        $storyResponse = $account->getApi()->get("/{$pageId}/stories");
        $storyData = $storyResponse->getDecodedBody();
        $storyUrl = null;

        foreach ($storyData['data'] as $story) {
            if ($story['post_id'] === $post->external_id) {
                $storyUrl = $story['url'];
                break;
            }
        }

        $this->assertNotEmpty($storyUrl, 'Story URL not found for the published post.');
    }

    // private helping methods below

    private function getAccount(){
        $user = $this->authenticate();
        if(!$this->account){
            //get the test facebook account
            $account = \DB::connection('mysql')->table('accounts')->where('name', 'FacebookTestAccount')->whereActive(true)->whereType('facebook.page')->first();
            if ($account) {
                $accountArr = (array) $account;
                $accountArr['user_id'] = $user->id;
                
                // SQLite will auto-increment
                unset($accountArr['id']);

                // Insert into sqlite_testing
                \DB::connection('sqlite_testing')->table('accounts')->insert($accountArr);
            }
        }

        // Now fetch the account from sqlite_testing
        $this->account = Account::where('name', 'FacebookTestAccount')->where('user_id', $user->id)->first();

        return $this->account;
    }

    private function getAttachmentsArray($paths): array
    {
        $attachments = [];
        foreach ($paths as $filePath) {
            $fName = Str::afterLast($filePath, '/');
            $attPath = 'attachments/' . time() . '_' . $fName;
            $newPath = \Storage::disk('local')->path($attPath);

            // copy $filePath to $newPath
            if (!is_dir(dirname($newPath))) {
                mkdir(dirname($newPath), 0755, true);
            }

            if (!copy($filePath, $newPath)) {
                throw new \RuntimeException('Failed to copy file to new path');
            }

            $this->tempFiles[] = $newPath;
            $file = new UploadedFile($newPath, $fName);
            $attType = strtolower($file->getClientOriginalExtension());
            $attachments[] = [
                'path' => $attPath,
                'type' => $attType,
                'name' => $file->getClientOriginalName(),
                'size' => $file->getSize(),
            ];
        }

        return $attachments;
    }

    /**
     * @throws ValidationException
     * @throws \Exception
     */
    private function publishPost($text, $attachments = [], $options = []){

        $account = $this->getAccount();

        $post = $account->publishPost([
            'content' => $text,
            'is_draft' => false,
            'options' => $options,
        ], now(), false, null, $attachments);

        $post->prepareVideo(true);

        $post->publish(true);
        
        $post->refresh();
        $this->publishedPosts[] = $post;

        return $post;

    }

    /**
     * @throws \Throwable
     */
    protected function tearDown(): void
    {
        // Delete all published posts
        foreach ($this->publishedPosts as $post) {
            try {
                $this->getAccount()->getApi()->delete('/' . $post->external_id);
            } catch (\Exception $e) {
                echo 'Failed to delete test post: ' . $e->getMessage() . PHP_EOL;
            }
        }
        // Clean up temporary files
        foreach ($this->tempFiles as $filePath) {
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }

        parent::tearDown();
    }

}
