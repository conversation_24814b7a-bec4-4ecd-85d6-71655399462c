<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateCurationFeedTopicPivotTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('curation_feed_topic', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigInteger('feed_id')->unsigned()->index();
            $table->foreign('feed_id')->references('id')->on('curation_feeds')->onDelete('cascade');

            $table->bigInteger('topic_id')->unsigned()->index();
            $table->foreign('topic_id')->references('id')->on('curation_topics')->onDelete('cascade');

            $table->primary(['feed_id', 'topic_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('curation_feed_topic');
    }
}
