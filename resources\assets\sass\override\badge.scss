.badge {
    padding: 6px 8px;
    &:empty {
      display: inline-block;
    }
  }
    
  .badge-primary{
    color: #0B46C2;
    background-color: map-get($pale-colors,'primary');
    
  }
  .badge-secondary{
    color: #204892;
    background-color:  map-get($pale-colors,'secondary');
  }
  .badge-success{
    color: #147830;
    background-color:  map-get($pale-colors,'success');
  }
  .badge-danger{
    color: #B51632;
    background-color:  map-get($pale-colors,'danger');
  }
  .badge-warning{
    color: #924901;
    background-color:  map-get($pale-colors,'warning');
  }
  .badge-info{
    color: #0167D6;
    background-color:  map-get($pale-colors,'info');
  }
  .badge-light{
    color: #4A5465;
    background: rgba(16, 63, 152, 0.06);
  }
  .badge-dark{
    color: #F1F3F5;
    background-color:  map-get($pale-colors,'dark');
  }

.badge-dot{
  padding: 0;
  
  &.badge-primary{
    background-color: #0557F0;
  }
  &.badge-secondary{
    background-color: #93B0E7;
  }
  &.badge-success{
    background-color: #28A835;
  }
  &.badge-danger{
    background-color: #FC2D2D;
  }
  &.badge-warning{
    background-color: #FC9D05;
  }
  &.badge-info{
    background-color: #0B46C2;
  }
  &.badge-light{
    background-color: #0286F9;
  }
  &.badge-dark{
    background-color: #252A32;
  }
}