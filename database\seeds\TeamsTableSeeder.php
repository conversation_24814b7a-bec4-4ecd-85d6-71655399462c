<?php

use App\Account;
use App\Team;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TeamsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $team = new Team([
            'name' => 'Some Other Team',
            'user_id' => 2,
        ]);
        $team->save();
        if(Account::find(5))
            $team->accounts()->attach(5);
        $team->updateMembers([1]);

    }
}
