<?php

namespace App\Http\Controllers\Api;

use App\Account;
use App\Helpers\TwitterOauthCustom;
use App\Http\Controllers\Json\JsonCollectionController;
use App\Post;
use App\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Filesystem\FileExistsException;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use FFMpeg\FFProbe;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Mimey\MimeTypes;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Twitter\Text\Parser as TweetParser;

class PostsController extends Controller
{
    /**
     * @return Builder|\Illuminate\Database\Query\Builder
     */
    static public function query()
    {
        // get posts
        /** @var Builder $builder */
        return Post::whereIn('account_id', user()->getAvailableAccounts(["id"], true)->map(function ($a) {
            return $a->id;
        }));
    }

    /**
     * @param Post $post
     * @return bool
     */
    static public function canEdit(Post $post)
    {
        return $post->canBeEditedBy(user());
    }

    /**
     * @param Request $request
     * @return Collection
     * @throws ValidationException
     */
    public function get(Request $request)
    {

        $postType = $request->input('type', 'scheduled_or_awaiting_approval');
        if (!in_array($postType, ['scheduled', 'awaiting_approval', 'scheduled_or_awaiting_approval', 'draft', 'published'])) {
            abort(400, 'Invalid post type');

        }

        // fetch posts
        return (new JsonCollectionController())->posts($request, false, $postType, null, 20);
    }

    /**
     * Serve the request
     * @param Request $request
     * @return Collection
     * @throws Exception
     */
    public function post(Request $request)
    {
        try {
            return $this->_post($request);
        } catch (\Exception $exception) {
            if (!$request->input('suppress_http_error')) {
                throw $exception;
            }
            if ($exception instanceof ValidationException) {
                return collect([
                    'success' => false,
                    'error' => array_first( array_flatten($exception->errors()) ),
                ]);
            }
            return collect([
                'success' => false,
                'error' => $exception->getMessage(),
            ]);
        }
    }

    /**
     * @param Request $request
     * @return Collection
     * @throws HttpException
     * @throws NotFoundHttpException
     * @throws ValidationException
     * @throws Exception
     */
    private function _post(Request $request)
    {

        $attachments = self::getAttachmentsFromRequest($request);
        
        //convert webp attachments to jpg
        $attachments = $this->convertWebpToJpg($attachments);
        

        $this->validate($request, [
            'accounts' => 'required|array',
            'accounts.*' => 'integer|exists:accounts,id',
            'team_id' => 'integer',
            'publish_at' => 'required',
            'options' => 'array',
            'postback_url' => 'url|nullable', // for api postback
        ]);

        if(count($request->input('accounts', [])) == 0) // frontend doesn't let user post if no accounts selected
            abort(400, 'No account selected');

        /** @var User $user */
        $user = $request->user();

        // fetch accounts
        $availableAccounts = $user->getAvailableAccounts();

        // validate accounts
        /** @var Collection|Account[] $accounts */
        $accounts = $availableAccounts->whereIn('id', $request->input('accounts'));

        if ($accounts->count() < count($request->input('accounts'))) {
            abort(400, 'Some accounts you selected are not accessible from your account.');
        }

        $attachment_types_found = [];
        $requiresMedia = $requiresText = false;
        foreach ($accounts as $account) {
            
            if (!Str::contains($account->type, 'reddit.profile')){
                \Validator::make(['attachments' => $attachments, 'content' => $request->input('content')], [
                    'content' => 'required_without:attachments|nullable',
                ])->validate();

                if ($account->requiresMedia()){
                    $requiresMedia = true;
                }   
                if ($account->requiresText()){
                    $requiresText = true;
                }
            }
            foreach ($account->getAttachmentTypes() as $ext) {
                $attachment_types_found[$ext] = (isset($attachment_types_found[$ext]) ? $attachment_types_found[$ext] : 0);
                ++$attachment_types_found[$ext];
            }
        }

        $accepted_attachment_extensions = [];
        foreach ($attachment_types_found as $ext => $count) {
            if ($count == count($request->input('accounts'))) {
                $accepted_attachment_extensions[] = $ext;
            }
        }

        if ($requiresMedia) {
            // give error if image not found, image required
            \Validator::make(['attachments' => $attachments], [
                'attachments.*' => 'required|file',
            ])->validate();
        }

        if ($requiresText) {
            $this->validate($request, [
                'content' =>  'required',
            ]);
        }

        // Handle multiple publish dates
        $publish_dates = $request->input('publish_at');
        if (!is_array($publish_dates)) {
            $publish_dates = [$publish_dates];
        }

        $now = Carbon::now('UTC');
        $valid_publish_dates = [];
        foreach ($publish_dates as $date) {
            $publish_at = Carbon::createFromFormat('Y-m-d H:i:s', $date, 'UTC');
            if ($publish_at < $now) {
                $publish_at = $now;
            }
            $valid_publish_dates[] = $publish_at;
        }

        // attachments validation
        \Validator::make(['content' => $request->input('content'), 'attachments' => $attachments], [
            'attachments.*' => 'required_without:content|file|mimes:' . implode(',', $accepted_attachment_extensions) . '|max:' . config('app.max_file_size'),
        ])->validate();

        // check limits
        foreach ($accounts as $account) {
            if (getUsage('monthly_posts', $account) + 1 > getPlanUsage('monthly_posts', $account)) {
                $_user = getResourceOwner($account);
                $options = isset($all_options[$account->id]) ? $all_options[$account->id] : [];
                if(isset($options['post_as_reel']) && $options['post_as_reel']){
                    if( !planHasFeature('reels_publishing', $_user) ){
                        abort(403, 'Reels publishing is not available for this account. Please upgrade your plan.');
                    }
                }
                // show error
                if ($_user->id !== $user->id) {
                    // is team account
                    abort(400, 'Please upgrade the subscription (of the team owner\'s account) for more posts on: ' . $account->name . '.');
                } else {
                    abort(400, 'Please upgrade your subscription for more posts on: ' . $account->name . '.');
                }
            }
        }

        $all_options = $request->input('options', []);

        $useSameOptionsForAll = false;
        if(count($all_options) > 0 ){
            $allOptionKeys = array_keys($all_options);
            // if all keys in $all_options not integers, then it's a single options for all accounts
            $useSameOptionsForAll = count(array_filter($allOptionKeys, function ($k) {$int = (int) $k;return is_int($k) && $int > 0;})) === 0;
            unset($allOptionKeys);
        }
        // validate each account post
        // will auto throw exception on validation fail
        foreach ($accounts as $account) {
            $options = isset($all_options[$account->id]) ? $all_options[$account->id] : [];

            // options are not set, our api allows sending options without account id,
            // so we need to set it here
            if(empty($options) && $useSameOptionsForAll){
                $options = $all_options;
            }

            if($request->has('team_id')){
                $options['team_id'] = $request->input('team_id');
            }
            try {
                // get content
                if(is_array($request->input('content'))){
                    // customized content for each account
                    if(!isset($request->input('content')[$account->id])){
                        throw new \Exception('Invalid request: content not found for account: ' . $account->name);
                    }
                    $content = $request->input('content')[$account->id];
                } else {
                    $content = $request->input('content');
                }
                // now proceed, validation should also be done in the same function publishPost()
                // Validate with the first publish date (validation is same for all dates)
                $account->publishPost([
                    'content' => $content,
                    'options' => $options,
                    'is_draft' => (boolean) $request->input('draft', false),
                    'attachments' => (array) $attachments,
                ], $valid_publish_dates[0], true);
            } catch (\Illuminate\Validation\ValidationException $e) {
                throw $e;
            } catch (\Exception $e) {
                // show error
                abort(400, $e->getMessage());
            }
        }

        // if we are here, validation is done
        // save posts
        $posts = [];
        foreach ($accounts as $account) {
        foreach ($valid_publish_dates as $publish_at) {
            $options = isset($all_options[$account->id]) ? $all_options[$account->id] : [];

            // options are not set, our api allows sending options without account id,
            // so we need to set it here
            if(empty($options) && $useSameOptionsForAll){
                $options = $all_options;
            }

            if($request->has('team_id')){
                $options['team_id'] = $request->input('team_id');
            }

            // get content
            if(is_array($request->input('content'))){
                // customized content for each account
                if(!isset($request->input('content')[$account->id])){
                    throw new \Exception('Invalid request: content not found for account: ' . $account->name);
                }
                $content = $request->input('content')[$account->id];
            } else {
                $content = $request->input('content');
            }

            if ($request->input('postback_url')){
                $options['postback_url'] = $request->input('postback_url');
            }

            // now proceed
            $post = $account->publishPost([
                'content' => $content,
                'options' => $options,
                'is_draft' => (boolean) $request->input('draft', false),
                'attachments' => (array) $attachments,
            ], $publish_at, false);

            if(!$post->draft){
                // save metric
                try {
                    get_insights_db()->table('misc_metrics')->insert([
                        'user_id' => $user->id,
                        'account_id' => $account->id,
                        'object' => 'post:' . $post->id,
                        'action' => 'post_scheduled',
                    ]);
                } catch (\Exception $e) { }
            }

            // return post object
            $posts[] = Post::transform($post);
        }
    }

        if(!$user->getOption('post_created', false)){
            $user->setOption('post_created', true);
        } 

        return collect([
            'success' => true,
            'posts' => $posts,
        ]);
    }

    /**
     * @param Request $request
     * @param $id
     * @return Collection
     * @throws Exception
     */
    public function patch(Request $request, $id)
    {
        /** @var Post $post */
        $post = self::query()->findOrFail($id);

        $accounts = user()->getAvailableAccounts();
        if (!isset($accounts[$post->account_id])) {
            abort(400, 'Social media account not accessible.');
        }

        /** @var Account $account */
        $account = $accounts[$post->account_id];


        if ($post->published_at) {
            abort(400, 'Cannot edit a published post.');
        }
        if (!self::canEdit($post)) {
            abort(400, 'You cannot edit this post');
        }

        // if user just wants to publish it now (no other thing is changed)
        if ($request->has('publish_now')) {
            return $this->publish($request, $id);
        }

        // convert string 'true' and 'false' to native boolean
        // only needed for boolean data fields
        // just to be sure
        foreach (['draft', 'approved'] as $key){
            if($request->has($key)) {
                $request->merge([
                    $key => filter_var($request->input($key), FILTER_VALIDATE_BOOLEAN)
                ]);
            }
        }
        // if request is from post composer, behave like a PUT endpoint
        if($request->input('from') === 'composer') {
            // now, options and existing attachments if not set, will be assumed empty, so we will remove from post
            $request->merge([
                'options' => $request->input('options', []), // options are not sent if all are removed
                'existing_attachments' => $request->input('existing_attachments', []), // composer will not send existing attachments only if all of them are removed
                'draft' => $request->input('draft', false), // composer sends draft = 'true' when drafting
            ]);
        }

        $attachments = [];
        $allCurrentAttachments = [];
        $existingCurrentAttachments = [];
        $attachmentPathsBeforeRequest = [];
        // process attachments before saving
        if($request->has('existing_attachments')) {
            // get files that are newly uploaded
            $attachmentPathsBeforeRequest = collect($post->getAttachments())->map(function ($f) {
                return $f['path'];
            })->toArray();
            $attachments = self::getAttachmentsFromRequest($request, $attachmentPathsBeforeRequest);

            // Convert WebP attachments to JPG
            $attachments = $this->convertWebpToJpg($attachments);

            // now add existing attachments that are missing from $attachments (because those are not needed to be uploaded file objects)
            $existingCurrentAttachments = collect($request->input('existing_attachments', []))->map(function ($json) {

                if(is_string($json)){
                    $json = json_decode($json, true);
                }

                return $json;
            })->filter(function ($att) use ($attachmentPathsBeforeRequest) {
                return isset($att['path']) && in_array($att['path'], $attachmentPathsBeforeRequest);
            })->toArray();

            $allCurrentAttachments = $attachments;
            foreach ($existingCurrentAttachments as $index => $existingCurrentAttachment) {
                // add existing attachment preserving the order
                $allCurrentAttachments[$index] = $existingCurrentAttachment;
            }
            // now $allCurrentAttachments is a mix of UploadedFile objects for new attachments and arrays(name, type, path,...) for existing attachments
        }

        // set post type
        $post_type = $post->type;
        if($request->has('existing_attachments')){
            $post_type = 'text';
            // check if it will be a video post
            $hasVideo = collect($allCurrentAttachments)->filter(function ($f) {
                    $mimes = new MimeTypes;
                    if($f instanceof UploadedFile) {
                        $attMime = $f->getMimeType();
                        $attType = strtolower($mimes->getExtension($attMime));
                    } else {
                        $attType = $f['type']; // already existing current attachment
                    }

                    // bug: when `type` is actually mime instead of extension
                    if(Str::contains($attType, '/')){
                        $attType = explode('/', $attType)[1];
                    }

                    return in_array($attType, ['mp4', 'm4v', 'mov', 'qt', 'avi',]);
                })->count() > 0;

            if($hasVideo){
                $post_type = 'video';
            } else if(count($allCurrentAttachments) > 0){
                $post_type = 'image';
            }
        }
        // validate rest of the data
        $this->validate($request, [
            'publish_at' => 'date_format:Y-m-d H:i:s',
            'draft' => 'boolean',
            'approved' => 'boolean',
            'options' => 'array',
            'content' => 'string|nullable',
        ]);

        // validate content length
        if ($request->has('content')) {

            // normalize newlines to \n
            $request->merge([
                'content' => preg_replace('/\r\n|\r|\n/', "\n", $request->input('content', ''))
            ]);

            $postLength = mb_strlen($request->input('content'));

            if ($account->type === 'twitter.profile') {
                $parser = new TweetParser();
                $result = $parser->parseTweet($request->input('content'));
                $postLength = $result->weightedLength;
            }

            if ($postLength > $account->getPostLength()) {
                // error
                abort(400, 'Cannot be more than ' . $account->getPostLength() . ' characters. (' . $postLength . ')');
            }
        }
        if($request->has('existing_attachments')) {
            // attachments validation
            \Validator::make(['content' => $request->input('content'), 'attachments' => $attachments], [
                'attachments' => 'max:' . $account->getMaxAttachments($post_type),
                'attachments.*' => 'file|mimes:' . implode(',', $account->getAttachmentTypes()) . '|max:' . config('app.max_file_size'),
            ])->validate();
        }


        $updates = [];

        if($post_type !== $post->type){
            $updates['type'] = $post_type;
        }

        if ($request->has('reset_result') && $request->input('reset_result')) {
            $updates['result'] = null;
        }

        if ($request->has('publish_at')) {
            $now = Carbon::now('UTC');
            $publish_at = Carbon::createFromFormat('Y-m-d H:i:s', $request->input('publish_at'), 'UTC');
            if ($publish_at < $now)
                $publish_at = $now;
            $updates['publish_at'] = $publish_at;
        }


        if ($request->has('content'))
            $updates['content'] = (string) $request->input('content');

        $isDraft = !!$post->draft;

        if($request->has('draft')) {
            $updates['draft'] = (boolean)$request->input('draft', false);
            if ($updates['draft']) {
                $isDraft = true;
                $updates['approved'] = true; // draft is always approved
            } else {
                $isDraft = false; // important: so that we set unapproved if needed
            }
        }

        $teamId = $post->getOption('team_id');
        $team = null;

        if($teamId !== null) {
            $team = $post->getTeam();
        } else {
            $team = $account->getTeam();
        }

        if(!$isDraft && $team && $team->requiresContentApproval()){
            if($team->hasPermission(user(), 'approvals.approve')){
                // i am an approver
                // so if this is my post
                // it should not require approval
                if($post->user_id === user()->id){
                    $updates['approved'] = true;
                }
            } else {
                // i am not an approver
                // so my changes will make the post unapproved
                $updates['approved'] = false;
            }
        }

        // do we need to update options
        if ($request->has('options')) {
            $options = $post->options;
            $postOptionsOld = Post::processPostOptions($post->account, $post->options, true);
            $postOptionsNew = Post::processPostOptions($account, $request->input('options'));

            // update options without replacing the internal options that we use e.g. attachments are stored in `options`
            foreach ($postOptionsOld as $key => $val) {
                unset($options[$key]); // unset old option
            }
            foreach ($postOptionsNew as $key => $val) {
                $options[$key] = $val; // set new opt
            }
            if($account->type === 'twitter.profile'){
                try {
                    $threadedReplies = TwitterOauthCustom::processThreadedReplies($account, $options['threaded_replies'] ?? [], $postOptionsOld['threaded_replies'] ?? []);

                    $options['threaded_replies'] = $threadedReplies;
                    if(empty($options['threaded_replies'])){
                        unset($options['threaded_replies']);
                    }
                } catch (\Exception $e) {
                    abort(400, $e->getMessage());
                }
            }
            if(isset($options['post_as_reel']) && $options['post_as_reel']){
                if( !planHasFeature('reels_publishing', $post->account) ){
                    abort(403, 'Reels publishing is not available for this account. Please upgrade your plan.');
                }
            }
            $post->options = $options;
            $post->save();
        }

        if (!empty($updates))
            $post->update($updates); // perform the update

        if($request->has('existing_attachments')) {
            // delete old attachments
            $existingAttachmentPaths = collect($existingCurrentAttachments)->map(function ($att) {
                return $att['path'];
            })->toArray();
            $pathsToDelete = collect($attachmentPathsBeforeRequest)->filter(function ($path) use ($existingAttachmentPaths) {
                return !in_array($path, $existingAttachmentPaths);
            })->toArray();
            \Storage::delete($pathsToDelete);

            // now we need to save the uploaded files in $attachments and save the attachments in post options
            $attachmentsData = collect($allCurrentAttachments)->map(function ($f) {
                if ($f instanceof UploadedFile) {
                    // save file
                    $att = self::saveUploadedFile($f);
                } else {
                    $att = $f; // existing file; already an array
                }
                return $att;
            })->toArray();
            $post->setOption('attachments', $attachmentsData);
        }

        $post->shortenLinksIfNeeded();

        $post->refresh();

        if($request->has('approved')){
            // post was approved or rejected
            try {
                $post->approvePost((bool) $request->input('approved'), (string) $request->input('reject_reason', ""));
            } catch (\Exception $exception){
                abort(400, $exception->getMessage());
            }
        }

        $postObj = Post::transform($post);

        if ($post->publish_at <= now() && !$post->draft && $post->approved)
            $postObj->put('_published', true);

        if ($request->has('approved'))
            $postObj->put('_reviewed', true);

        if(isset($updates['content']) && !$postObj['content']) {
            $postObj->put('content', $updates['content']);
        }
            
        return collect([
            'success' => true,
            'post' => $postObj,
        ]);

    }

    /**
     * @param Request $request
     * @param $id
     * @return Collection
     */
    public function getPost(Request $request, $id)
    {
        /** @var Post $post */
        $post = self::query()->find($id);
        if(!$post){
            return response()->json([
                'success' => false,
                'message' => 'Resource not found.'
            ], 404);
        }
        return collect(Post::transform($post));
    }

    public function publish(Request $request, $id)
    {

        /** @var Post $post */
        $post = self::query()->findOrFail($id);

        if (!self::canEdit($post)) {
            abort(400, 'You cannot edit this post.');
        }

        $post->draft = false;
        $post->publish_at = Carbon::now();
        $post->result = null;

        $options = $post->options;
        if (isset($options['in_queue']))
            unset($options['in_queue']);
        $post->options = $options;

        $accounts = user()->getAvailableAccounts();
        if (!isset($accounts[$post->account_id])) {
            abort(400);
        }

        /** @var Account $account */
        $account = $accounts[$post->account_id];

        $team = $account->getTeam();
        if($team && $team->requiresContentApproval()){
            if($team->hasPermission(user(), 'approvals.approve')){
                // i am an approver
                // so if this is my post
                // it should not require approval
                if($post->user_id === user()->id){
                    $post->approved = true;
                }
            } else {
                // i am not an approver
                // so my changes will make the post unapproved
                $post->approved = false;
            }
        }

        $post->save();

        try {
            $post->publish();
        } catch (\Exception $exception){
            // nothing to do, the post probably got queued to be published
        }

        return collect([
            'success' => true,
            'post' => [
                'draft' => $post->draft,
                'approved' => $post->approved,
                'publish_at' => $post->publish_at->toDateTimeString(),
                '_published' => !!$post->approved,
            ]
        ]);
    }

    /** Delete the post if it exists
     * @param Request $request
     * @param $id int ID of the post
     * @return void empty response
     */
    public function destroy(Request $request, $id)
    {
        /** @var Post $post */
        $post = self::query()->find($id);

        if (!$post) {
            abort(404, "The post doesn't exist or is already deleted.");
        }

        $deleted = false;
        try {
            $deleted = $post->delete();  // delete by deleting model, so our listener can catch the event
        } catch (Exception $e) {
            abort(500, $e->getMessage());
        }

        if(!$deleted){
            abort(400, 'You cannot delete this post');
        }
    }

    /** Edit the posts in bulk
     * @param Request $request
     * @return void empty response
     */
    public function bulkEdit(Request $request)
    {
        $query = self::query()->whereIn('id', $request->input('ids', []));

        /** @var Post[] $posts */
        $posts = $query->get();

        foreach ($posts as $post){
            try {
                if(!$post->canBeEditedBy(user())){
                    abort(400, 'You cannot edit this post');
                }
            } catch (Exception $e) {
                abort(500, $e->getMessage());
            }
        }

        $updates = [];

        if($request->input('draft') !== null){
            $updates['draft'] = (bool) $request->input('draft');
        }

        if(!empty($updates)){
            $query->update($updates);
        }

    }

    /** Delete the posts in bulk
     * @param Request $request
     * @return void empty response
     */
    public function bulkDelete(Request $request)
    {
        /** @var Post[] $posts */
        $posts = self::query()->whereIn('id', $request->input('ids', []))->get();

        foreach ($posts as $post){
            $deleted = false;
            try {
                $deleted = $post->delete();  // delete by deleting model, so our listener can catch the event
            } catch (Exception $e) {
                abort(500, $e->getMessage());
            }

            if(!$deleted){
                abort(400, 'You cannot delete this post');
            }
        }

    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ValidationException
     */
    public function createTemporaryFile(Request $request){
        $this->validate($request, [
            'name' => 'required|string|max:255',
            'mime_type' => 'required|string|max:255',
        ]);
        $mimes = new MimeTypes;
        $user = user();
        $fileName = $request->input('name');
        $fileMimeType = $request->input('mime_type');
        // get extension from mime
        $mediaType = $mimes->getExtension($request->input('mime_type'));

        $uniqueKey = 'temporary/'.$user->id . '_' . time() . '_' . str_random(40) . sha1(time() . $fileName . $fileMimeType . $user->id) . '.' . $mediaType;

        $commandData = [
            'Key' => $uniqueKey,
            'ACL' => 'private',
            'ContentType' => $fileMimeType
        ];
        $bucket = config('filesystems.disks.spaces.bucket'); // Get the current bucket

        $preSignedUrl = getSignedUrl('PutObject', $bucket, $commandData);
        $url = \Storage::cloud()->temporaryUrl($uniqueKey, now()->addDays(1));

        // make sure $fileName is maximum 100 characters and if needed, truncate from the left
        if(strlen($fileName) > 100){
            $fileName = substr($fileName, -100);
        }

        // need to add db row
        \DB::table('temporary_files')->insert([
            'user_id' => $user->id,
            'path' => $uniqueKey,

            'name' => $fileName,
            'mime' => $fileMimeType,

            'created_at' => now(),
            'updated_at' => now(), // we will use this to later cleanup
        ]);

        return response()->json([
            'name' => $fileName,
            'mime_type' => $fileMimeType,
            'signed_url' => $preSignedUrl,
            'key' => $uniqueKey,
            'secure_key' => encrypt($uniqueKey),
            'url' => $url
        ]);
    }

    public function checkFileStatus(Request $request){
        $this->validate($request, [
            'key' => 'required|string|max:1000',
        ]);

        if(!\Storage::cloud()->exists($request->input('key'))){
            return response()->json([
                'success' => false,
                'message' => 'File does not exist yet',
                'upload_token' => null
            ], 200);
        }

        return response()->json([
            'success' => true,
            'message' => 'File uploaded.',
            'upload_token' => encrypt($request->input('key'))
        ], 200);
    }

    /**
     * @param Request $request
     * @throws ValidationException
     */
    public function destroyTemporaryFile(Request $request){
        $this->validate($request, [
            'keys' => 'required|array',
        ]);
        $keys = $request->input('keys', []);
        foreach($keys as $key){
            $key = decrypt($key);
            if(!$key) abort(400);
            \Storage::cloud()->delete($key);
        }
    }

    /**
     * @param Request $request
     * @param array $excludeExistingAttachments paths to exclude
     * @return UploadedFile[]
     * @throws FileNotFoundException|\Exception
     */
    public static function getAttachmentsFromRequest(Request $request, array $excludeExistingAttachments = [])
    {
        $attachments = []; // attachment files
        $mimes = new MimeTypes;

        // validate if existing_attachments are set, they should be an array
        $request->validate([
            'existing_attachments' => 'nullable|array',
        ]);

        // check if attachments are existing, if yes, convert to files
        foreach ($request->input('existing_attachments', []) as $index => $existingAttachmentJson){

            if(is_string($existingAttachmentJson)){
                $existingAttachmentJson = json_decode($existingAttachmentJson);
            } else {
                // make sure it's an object
                $existingAttachmentJson = (object) $existingAttachmentJson;
            }

            $att = $existingAttachmentJson;
            if($att && isset($att->upload_token)){
                $key = null;
                try {
                    $key = decrypt($att->upload_token);
                } catch (\Exception $exception){
                    abort(400, 'Invalid upload token');
                }
                
                if(!\Storage::cloud()->exists($key)){
                    abort(400, 'File not found');
                }

                $fStream = \Storage::cloud()->readStream($key);
                
                $name = basename($key);
                
                $size = \Storage::cloud()->size($key);
                $mimeType  = \Storage::cloud()->mimeType($key);

                $height = $width = null;
                if(Str::contains($mimeType, 'video')){
                    try {
                        $localPath = 'temp/' . str_random() . '_' . $name;
                        // save the media locally
                        \Storage::disk('local')->writeStream($localPath, $fStream);

                        $ffprobe = FFProbe::create([
                            'ffprobe.binaries' => '/usr/local/bin/ffprobe', // for some reason, its not finding ffprobe here
                        ]);
                        
                        $width = (int) $ffprobe->streams(storage_path('app/' . $localPath))->videos()->first()->get('width');
                        $height = (int) $ffprobe->streams(storage_path('app/' . $localPath))->videos()->first()->get('height');

                        //cleanup
                        \Storage::disk('local')->delete($localPath);
                    } catch (\Exception $e) {
                        report($e);
                        abort(500, 'Unable to get video dimensions: ' . $e->getMessage());
                    }
                }else {
                    $img = \Intervention\Image\Facades\Image::make($fStream);
                    // Get dimensions
                    $width = $img->width();
                    $height = $img->height();
                }

                $att = (object) [
                    'uploadedMedia' => true,
                    'size' => $size,
                    'mimeType' => $mimeType,
                    'key' => $key,
                    'name' => $name,
                    'extension' => explode('.', $name)[1],
                    'temporary' => true,
                    'metaData' => [
                        'height' => $height,
                        'width' => $width
                    ],
                ];
            }
            // if it's an existing post attachment, type will be set as extension
            // if it's coming from our file uploader, it will have extension instead of type, mimeType instead of mime, and key instead of path
            if(isset($att->extension)){
                $att->type = $att->extension;
                unset($att->extension);
            }

            if(isset($att->mimeType)){
                $att->mime = $att->mimeType;
                unset($att->mimeType);
            }

            if(isset($att->key)){
                $att->path = $att->key;
                unset($att->key);
            }


            if(!$att || !isset($att->path, $att->name, $att->type)) {
                \Log::info('Invalid attachment: ' . json_encode($existingAttachmentJson));
                continue;
            }

            $isUploadedMedia = isset($att->uploadedMedia) && $att->uploadedMedia;

            $path = $att->path;

            if(in_array($path, $excludeExistingAttachments)) continue;

            $nameParts = explode('.', $att->name);
            array_pop($nameParts);
            $nameWithoutExt = implode('.', $nameParts);

            $name = str_slug($nameWithoutExt, '_') . '.' . $att->type;

            $isDeleted = isset($att->deleted) && $att->deleted;

            $exists = $isUploadedMedia ? \Storage::cloud()->exists($path) : \Storage::exists($path);

            if($isDeleted || !$exists){
                abort(400, 'The attachment ' . $name . ' is unavailable so this action cannot be completed.');
            }

            $fStream = $isUploadedMedia ? \Storage::cloud()->readStream($path) : \Storage::readStream($path);

            $localPath = 'temp/' . str_random() . '_' . basename($path);

            // save the file locally
            try {
                \Storage::disk('local')->writeStream($localPath, $fStream);
            } catch (FileExistsException $e) {
                abort(500, 'Unable to save file'); // should never happen
            }

            // now covert to HTTP File object; we pass true for `test` parameter because else the validation fails because this is not a pure user uploaded file
            $attachments[$index] = new UploadedFile(storage_path('app/' . $localPath), $name, $mimes->getMimeType($att->type), null, true);
        }

        // add normal attachments to our array
        foreach ($request->file('attachments', []) as $index => $uploadedFile){
            $attachments[$index] = $uploadedFile;
        }
        // all new attachments are ready with their order reserved
        return $attachments;
    }

    private function convertWebpToJpg($attachments){
        foreach ($attachments as &$attachment) {
            if ($attachment->getClientOriginalExtension() === 'webp') {
                $tempPath = tempnam(sys_get_temp_dir(), 'jpg');
                $image = \Intervention\Image\Facades\Image::make($attachment->getPathname());
                $image->save($tempPath, 90, 'jpg');
                $newAttachment = new \Illuminate\Http\UploadedFile($tempPath, pathinfo($attachment->getClientOriginalName(), PATHINFO_FILENAME) . '.jpg', 'image/jpeg', null, true);
                $attachment = $newAttachment;
            }
        }
        return $attachments;
    }

    /**
     * @param UploadedFile $file
     * @param bool $really
     * @return array
     * @throws Exception
     */
    public static function saveUploadedFile(UploadedFile $file, $really = true){

        $mimes = new MimeTypes;

        $attMime = $file->getMimeType();
        $attType =  strtolower($mimes->getExtension($attMime));

        $user = user();

        $path = null;
        if($really){
            $path = \Storage::putFileAs('attachments', $file,
                $user->id . '_' . time() . '_' . str_random(40) . sha1(time() . $file->getFilename() . $file->getMimeType() . $user->id) . '.' .$attType
            );
            if (!$path) {
                throw new \Exception('Unable to store attachment. Please try again.');
            }
        }

        return [
            'path' => $path,
            'type' => $attType,
            'ext' => $attType,
            'size' => $file->getSize(),
            'mime' => $file->getMimeType(),
            'name' => str_replace('.tmp', '.' . $attType, $file->getClientOriginalName()),
        ];

    }

}
