<?php

namespace App\Http\Controllers\Webhook;

use App\Jobs\ProcessWebhookFacebook;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Account;
class FacebookController extends Controller
{
    public function verify(Request $request){
        if ($request->get('hub_verify_token') == config('services.facebook.verification_token')) {
            return $request->get('hub_challenge');
        }
        return abort(403, 'Invalid token');
    }
    public function handle(Request $request){
        // Validate the integrity and payload and it's origin
        $payload = $request->getContent();
        $hub_sign = $request->header('X-Hub-Signature', 'sha1=null');
        if (hash_equals(explode('=', $hub_sign)[1], hash_hmac('sha1', $payload, config('services.facebook.client_secret')))) {
            dispatch((new ProcessWebhookFacebook($request->input()))->onQueue('fb_webhook')); // will be processed by dedicated queue
            return 'ok';
        } else {
            \Log::warning('webhook request couldnt be validated: ' . json_encode($request->input()) . ':' . json_encode($request->headers));
        }
        return abort(400);
    }

    public function delete(Request $request)
    {
        // Facebook sends deletion requests as a POST request with the following parameters
        // signed_request
        $signedRequest = $request->input('signed_request');

        // Decode the signed request
        $data = $this->parseSignedRequest($signedRequest);

        $userId = $data['user_id'];

        //need to disable users' all facebook and instagram accounts
        $accountIds = Account::where('token->user', $userId)->where(function($query){
                    $query->where('type', 'facebook.page')->orWhere('type', 'instagram.api');
                })->get()->map(function($item){
                    return $item->id;
                })->toArray();

        foreach($accountIds as $id){
            $acc = Account::find($id);
            try {
                $acc->setActive(false);
            } catch (\Exception $e) {
                \Log::error($e->getMessage());
                report($e);
            }
        };

        //in the confirmation_code, sending id for the first account only 
        $acc = count($accountIds) > 0 ? $accountIds[0] : '0';
        
        $data = [
            'url' => url('/app/accounts'),
            'confirmation_code' => 'D' . $acc ,
        ];

        return response()->json($data);
    }

    public function uninstalled(Request $request)
    {
        return $this->delete($request); // for now
    }

    private function parseSignedRequest($signedRequest)
    {
        list($encoded_sig, $payload) = explode('.', $signedRequest, 2);

        $secret = config('services.facebook.client_secret'); // Use your app secret here

        // decode the data
        $sig = base64_url_decode($encoded_sig);
        $data = json_decode(base64_url_decode($payload), true);

        // confirm the signature
        $expected_sig = hash_hmac('sha256', $payload, $secret, $raw = true);
        if ($sig !== $expected_sig) {
            \Log::info('Bad Signed JSON signature!');
            return null;
        }

        return $data;
    }
}
