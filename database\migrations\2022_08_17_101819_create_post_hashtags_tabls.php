<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreatePostHashtagsTabls extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.insights'))->create('post_hashtags', function (Blueprint $table) {
            $table->bigIncrements('id');

            $table->integer('post_id')->unsigned()->index();
            $table->integer('account_id')->unsigned()->index();

            $table->string('hashtag')->index();

            $table->timestamp('timestamp')->useCurrent()->index();;
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(config('database.insights'))->dropIfExists('post_hashtags');
    }
}
