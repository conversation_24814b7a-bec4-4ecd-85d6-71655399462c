<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RemoveRootIdColumnFromInboxConversationItems extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        $isSqlite = DB::connection()->getDriverName() === 'sqlite';
        
        if (!$isSqlite) {
            Schema::table('inbox_conversation_items', function (Blueprint $table) {
                $table->dropForeign(['root_id']);
                $table->dropIndex(['root_id']);
                $table->dropColumn('root_id');
            });
        } else {
            Schema::table('inbox_conversation_items', function (Blueprint $table) {
                $table->dropColumn('root_id');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('inbox_conversation_items', function (Blueprint $table) {
            $table->bigInteger('root_id')->unsigned()->nullable()->index();
            $table->foreign('root_id')->references('id')->on('inbox_conversation_items')->onDelete('cascade')->onUpdate('cascade');
        });
    }
}
