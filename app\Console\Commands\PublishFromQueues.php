<?php

namespace App\Console\Commands;

use App\PublishQueue;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class PublishFromQueues extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'publish_queues:publish';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Publish from queues';

    private $maxQueues = 10000;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $count = 0;
        PublishQueue::where('active', true)
            ->where(function($query){
                /** @var Builder $query */
                return $query->where('started_at', '<', now())->orWhere('started_at', null);
            })
            ->where(function($query){
                /** @var Builder $query */
                return $query->where('ended_at', '>', now())->orWhere('ended_at', null);
            })
            ->where('next_publish_at', '<=', now())
            ->whereHas('items') // only queues with items
            ->chunk(200, function(/** @var PublishQueue[]|Collection $queues */ $queues) use(&$count){

                \Log::info('PublishFromQueues: found a batch of ' . $queues->count() . ' queues to publish');

                $queues->each(function($queue){
                    /** @var PublishQueue $queue */

                    if(!$queue->user && $queue->active){
                        // user probably deleted, so deactivate
                        $queue->toggleStatus();
                    } else {

                        if($queue->getOption('is_publishing')){
                            $ts_old = $queue->getOption('is_publishing_since');

                            $tsC = \Carbon\Carbon::createFromTimestamp($ts_old);
                            $now = now();

                            if($tsC->diffInSeconds($now) > 60 * 5){
                                // if it has been more than 5 minutes, reset the flag; it will be processed next run
                                $queue->setOption('is_publishing', false);
                            }

                            \Log::info('Queue is already publishing, skipping', [
                                'queue_id' => $queue->id,
                                'queue_name' => $queue->name,
                            ]);

                            return;
                        }

                        // publish the post
                        $queue->publish();
                    }

                });

                $count += $queues->count();

                if($count >= $this->maxQueues){
//                    return false; // enough for this run
                }
            });
    }

}
