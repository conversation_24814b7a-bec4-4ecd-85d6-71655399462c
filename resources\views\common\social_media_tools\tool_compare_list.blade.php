@extends('layout.full_width')
@section('title', $tool['name'] . ' Comparison: Compare ' . $tool['name'] . ' with ' . count($list) . ' Tools | ' . config('app.name'))
@section('description', 'On this page, you can compare ' . $tool['name'] . ' with any of the ' . count($list) . ' tools. An overview and detailed comparison of ' . $tool['name'] . ' with any of the social media tool available in one click.')
@section('url', 'https://socialbu.com/compare/' . $tool['slug'])
@push('head_html')

    <meta name="description" content="@yield('description')"/>
    <meta property="og:locale" content="en_US" />
    <meta property="og:type" content="website" />
    <meta property="og:title" content="Compare {{ $tool['name'] }} with {{ count($list) }} Tools" />
    <meta property="og:description" content="@yield('description')" />
    <meta property="og:url" content="@yield('url')" />
    <meta property="og:site_name" content="SocialBu" />
    <meta property="fb:app_id" content="{{ config('services.facebook.client_id') }}" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Compare {{ $tool['name'] }} with {{ count($list) }} Tools" />
    <meta name="twitter:description" content="@yield('description')" />
    <meta name="twitter:site" content="@socialbuapp" />


    <link rel="canonical" href="@yield('url')" />

@endpush
@section('content')
    <header class="header text-center pb-0">
        <div class="container">
            <h1 class="display-1">
                {{ $tool['name'] }} <br/> Comparison
            </h1>
            <p class="lead-2 mt-6">
                Compare {{ $tool['name'] }} with any of the {{ count($list) }} social media tools on this page.
            </p>
        </div>
    </header>
    <main class="main-content">
        <section class="section bg-light">
            <div class="container">
                <div class="row gap-y">
                    <div class="d-none d-md-block col-md-4 mx-auto">
                        @include('common.social_media_tools.sidebar')
                    </div>
                    <div class="col-md-8 col-md-offset-1">
                        <div class="list-group">
                            @foreach($list as $competitor)
                                @if($competitor['name'] !== $tool['name'])
                                    @php($slugs = [$tool['slug'], $competitor['slug']])
                                    @php(sort($slugs))
                                    <div class="list-group-item mb-5 " style="border: none !important;">
                                        <div class="container">
                                            <div class="row d-flex align-items-center justify-content-between">
                                                <div class="">
                                                    @if(!empty($tool['logo']))
                                                        
                                                        <img data-toggle="tooltip" class="img-responsive  company-logo avatar-lg lozad d-none d-md-inline-block" style="width: {{ $tool['logo_category'] === 'Square' ? '50px' : '120px' }}; height: auto; max-width: none;" alt="{{ $tool['name'] }}" title="{{ $tool['name'] }}" src="/images/1x1.gif" data-src="{{ $tool['logo'] }}" />
                                                        <img data-toggle="tooltip" class="img-responsive  company-logo avatar-lg lozad d-inline-block d-md-none" style="width: {{ $tool['logo_category'] === 'Square' ? '50px' : '50px' }}; height: auto; max-width: none;" alt="{{ $tool['name'] }}" title="{{ $tool['name'] }}" src="/images/1x1.gif" data-src="{{ $tool['logo'] }}" />
                                                       
                                                    @else
                                                       <span class="lead-2 pl-2">{{ $tool['name'] }}</span> 
                                                    @endif
                                                </div>
                                                <div class="text-center">
                                                    <p class="lead-2 text-muted mb-0">vs.</p>
                                                </div>
                                                <div class="">
                                                    @if(!empty($competitor['logo']))
                                                        
                                                    <img data-toggle="tooltip" class="img-responsive company-logo avatar-lg lozad d-none d-md-inline-block" style="width: {{ $competitor['logo_category'] === 'Square' ? '50px' : '120px' }}; height: auto; max-width: none;" alt="{{ $competitor['name'] }}" title="{{ $competitor['name'] }}" src="/images/1x1.gif" data-src="{{ $competitor['logo'] }}" />
                                                    <img data-toggle="tooltip" class="img-responsive  company-logo avatar-lg lozad d-inline-block d-md-none" style="width: {{ $competitor['logo_category'] === 'Square' ? '50px' : '50px' }}; height: auto; max-width: none;" alt="{{ $competitor['name'] }}" title="{{ $competitor['name'] }}" src="/images/1x1.gif" data-src="{{ $competitor['logo'] }}" />    
                                                        
                                                    @else
                                                       <span class="lead-2 pl-2"> {{ $competitor['name'] }}</span>
                                                    @endif
                                                </div>
                                                <div class="">
                                                    <div class="">
                                                        @if($competitor['slug'] === 'socialbu')
                                                            <a href="/compare/{{ $tool['slug'] }}-alternative" class="btn btn-outline-primary btn-sm">See comparison</a>
                                                        @else
                                                            <a href="/compare/{{ $slugs[0] }}-vs-{{ $slugs[1] }}" class="btn btn-outline-primary btn-sm">See comparison</a>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                            
                                        </div>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
@endsection
