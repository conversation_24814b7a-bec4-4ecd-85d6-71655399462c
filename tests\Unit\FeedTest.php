<?php
/**
 * Created by PhpStorm.
 * User: dell
 * Date: 8/12/2018
 * Time: 3:48 PM
 */

namespace Tests\Unit;

use App\Team;
use App\User;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Feed;

class FeedTest extends TestCase
{
    use RefreshDatabase;
    public function testBasicTest()
    {
        $feed = new Feed(['name' => 'test feed', 'user_id' => 2,]);
        $this->assertEquals('test feed', $feed->getName());

        $team = factory(Team::class)->create();

        $feed = new Feed(['name' => 'test feed', 'user_id' => 2, 'team_id' => $team->id]);
        $feed->save();
    }
}
