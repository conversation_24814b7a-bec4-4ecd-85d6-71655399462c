<?php

namespace App\Jobs;

use App\Http\Controllers\User\InboxController;
use App\InboxConversation;
use App\InboxConversationItem;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class ProcessRespondReply implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $conversation;
    protected $data;
    protected $itemId;
    protected $type;
    
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(InboxConversation $conversation, $data, $itemId, $type)
    {
        $this->conversation = $conversation;
        $this->data = $data;
        $this->itemId = $itemId;
        $this->type = $type;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $this->inbox->reply($this->data, $this->itemId, $this->type, true);
        } catch (\Exception $exception){
            throw $exception;
        }
        
    }
}
