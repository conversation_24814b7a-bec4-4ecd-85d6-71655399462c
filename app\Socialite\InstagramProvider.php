<?php

namespace App\Socialite;

use <PERSON><PERSON>\Socialite\Two\AbstractProvider;
use <PERSON><PERSON>\Socialite\Two\ProviderInterface;
use <PERSON><PERSON>\Socialite\Two\User;

class InstagramProvider extends AbstractProvider implements ProviderInterface
{
    public static function getBaseUrl($url = null, $subdomain = 'www', $domain = 'instagram.com')
    {
        $baseUrl = 'https://' . $subdomain . '.' . $domain .'/';
        return $baseUrl . ( $url ? $url : '' );
    }
    /**
     * {@inheritdoc}
     */
    protected function getAuthUrl($state)
    {
        return $this->buildAuthUrlFromBase($this::getbaseUrl('oauth/authorize'), $state);
    }

    /**
     * {@inheritdoc}
     */
    protected function getTokenUrl()
    {
        return $this::getbaseUrl('oauth/access_token', 'api');
    }

    /**
     * {@inheritdoc}
     */
    public function getAccessTokenResponse($code)
    {
        $response = guzzle_client()->post($this->getTokenUrl(), [
            'form_params' => [
                'client_id' => config('services.instagram.client_id'),
                'client_secret' => config('services.instagram.client_secret'),
                'grant_type' => 'authorization_code',
                'redirect_uri' => route('accounts.auth.callback', ['provider' => 'instagram']),
                'code' => $code,
            ]
        ]);
        return json_decode($response->getBody(), true);
    }


    /**
     * {@inheritdoc}
     */
    protected function getTokenFields($code)
    {
        return [
            'code' => $code,
            'redirect_uri' => route('accounts.auth.callback', ['provider' => 'instagram']),
            'grant_type' => 'authorization_code',
        ];
    }
    

    /**
     * {@inheritdoc}
     */
    protected function getUserByToken($token)
    {
        $response = $this->getHttpClient()->get(
            $this::getBaseUrl(config('services.instagram.default_graph_version', 'v21.0') . '/me', 'graph'),
            [
                'query' => [
                    'fields' => 'user_id,username,name,profile_picture_url', 
                    'access_token' => $token,
                ]
            ]
        );

        return json_decode((string) $response->getBody(), true);
    }
    

    /**
     * {@inheritdoc}
     */
    protected function mapUserToObject(array $user)
    {
        return (new User())->setRaw($user)->map(
            [
                'id'       => $user['user_id'],
                'nickname' => isset($user['name']) ? $user['name'] : null,
                'name'     => isset($user['username']) ? $user['username'] : null,
                'avatar'   => isset($user['profile_picture_url']) ? $user['profile_picture_url'] : null,
            ]
        );
    }
}
