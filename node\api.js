const PORT = 3602;

const express = require("express");
const bodyParser = require("body-parser");
const log = require("./logger")("igpapi server");

// ig lib is bypassed from the key check but the key should be a valid one (expired will work)
const IgHelper = require("./ig");
const {
    IgLoginRequiredError,
    IgInactiveUserError,
    IgLoginBadPasswordError,
    IgLoginInvalidUserError,
    IgLoginTwoFactorRequiredError,
    IgCheckpointError,
    IgUserHasLoggedOutError
} = require("@igpapi/android");

const app = express();

// parse application/x-www-form-urlencoded
app.use(bodyParser.urlencoded({ extended: false }));

// parse application/json
app.use(bodyParser.json());

// log time
app.use((req, res, next) => {
    log(`${req.method} ${req.originalUrl} [STARTED]`);
    const start = process.hrtime();
    const getDurationInMilliseconds = start => {
        const NS_PER_SEC = 1e9;
        const NS_TO_MS = 1e6;
        const diff = process.hrtime(start);

        return (diff[0] * NS_PER_SEC + diff[1]) / NS_TO_MS;
    };

    res.on("finish", () => {
        const durationInMilliseconds = getDurationInMilliseconds(start);
        log(`${req.method} ${req.originalUrl} [FINISHED] ${durationInMilliseconds.toLocaleString()} ms`);
    });

    next();
});

// helper for errors
const errorHandler = (err, noLoginRetry = false) => {
    const response = {
        error: err.message
    };
    if (
        err instanceof IgLoginRequiredError ||
        err instanceof IgInactiveUserError ||
        err instanceof IgLoginTwoFactorRequiredError ||
        err instanceof IgLoginBadPasswordError ||
        err instanceof IgLoginInvalidUserError ||
        err instanceof IgUserHasLoggedOutError ||
        err instanceof IgCheckpointError
    ) {
        response.login_required = true;
        if(err instanceof IgLoginRequiredError && !noLoginRetry){
            response.auto_login = true;
        }
    }
    log(err);
    return response;
};

const refreshLogin = async (ig, data) => {
    log(`refreshLogin called: ${data.username}`);
    const loggedInResponse = await IgHelper.refreshLogin(ig, data.username, data.password);
    log(`Received login response after refresh: ${JSON.stringify(loggedInResponse)}`);
};

app.get("/ping", (req, res) => {
    res.send("pong");
});

app.post("/publish/post", async (req, res) => {
    const { state, data } = req.body;

    if (!state || !data) {
        return res.json(errorHandler(new Error("Missing state or data")));
    }

    log(`Publishing post: ${JSON.stringify(data)}`);

    let tries = 0;
    while (true) {
        if (tries >= 5) {
            return res.json(errorHandler(new Error("Publishing failed even after too many tries")));
        }
        tries++;

        try {
            const api = await IgHelper.getApifromState(state);
            const result = await IgHelper.publishPost(api, data);

            log(`Post published: ${JSON.stringify(result)}`);

            res.json({
                state: api.state,
                data: result
            });
        } catch (err) {
            if (IgHelper.shouldRetry(err)) {
                continue;
            }
            res.json(errorHandler(err));
        }

        break;
    }
});

app.post("/publish/story", async (req, res) => {
    const { state, data } = req.body;

    if (!state || !data) {
        return res.json(errorHandler(new Error("Missing state or data")));
    }

    log(`Publishing story: ${JSON.stringify(data)}`);

    let tries = 0;
    while (true) {
        if (tries >= 5) {
            return res.json(errorHandler(new Error("Publishing failed even after too many tries")));
        }
        tries++;

        try {
            const api = await IgHelper.getApifromState(state);
            const result = await IgHelper.publishStory(api, data);

            log(`Story published: ${JSON.stringify(result)}`);

            res.json({
                state: api.state,
                data: result
            });
        } catch (err) {
            if (IgHelper.shouldRetry(err)) {
                continue;
            }
            res.json(errorHandler(err));
        }

        break;
    }
});

app.post("/user", async (req, res) => {
    const { state, data } = req.body;

    if (!state) {
        return res.json(errorHandler(new Error("Missing state")));
    }

    try {
        const ig = await IgHelper.getApifromState(state);
        const user = await IgHelper.getUser(ig, data ? data.userId : null);

        res.json({
            state: ig.state,
            data: user
        });
    } catch (err) {
        res.json(errorHandler(err));
    }
});

app.post("/current_user", async (req, res) => {
    const { state, data } = req.body;

    if (!state) {
        return res.json(errorHandler(new Error("Missing state")));
    }

    try {
        const ig = await IgHelper.getApifromState(state);

        let user = await IgHelper.getCurrentUser(ig);

        res.json({
            state: ig.state,
            data: user
        });
    } catch (err) {
        res.json(errorHandler(err));
    }
});

app.post("/refresh_login", async (req, res) => {
    const { state, data } = req.body;

    if (!state) {
        return res.json(errorHandler(new Error("Missing state")));
    }
    if (!data) {
        return res.json(errorHandler(new Error("Missing data")));
    }

    try {
        log(`Refreshing login for: ${data.username}`);
        const ig = await IgHelper.getApifromState(state);
        await refreshLogin(ig, data);
        res.json({
            state: ig.state
        });
    } catch (err) {
        log(`Error while refreshing login: ${err.message}`);
        res.json(errorHandler(err, true));
    }
});

app.post("/publish/comment", async (req, res) => {
    const { state, data } = req.body;

    if (!state || !data) {
        return res.json(errorHandler(new Error("Missing state or data")));
    }

    try {
        const ig = await IgHelper.getApifromState(state);
        const commentRes = await IgHelper.addComment(ig, data);

        res.json({
            state: ig.state,
            data: commentRes
        });
    } catch (err) {
        res.json(errorHandler(err));
    }
});

app.post("/search/locations", async (req, res) => {
    const { state, data } = req.body;

    if (!state || !data) {
        return res.json(errorHandler(new Error("Missing state or data")));
    }

    try {
        const ig = await IgHelper.getApifromState(state);
        const response = await IgHelper.getLocations(ig, data);

        res.json({
            state: ig.state,
            data: response
        });
    } catch (err) {
        res.json(errorHandler(err));
    }
});

app.post("/search/users", async (req, res) => {
    const { state, data } = req.body;

    if (!state || !data) {
        return res.json(errorHandler(new Error("Missing state or data")));
    }

    try {
        const ig = await IgHelper.getApifromState(state);
        const response = await IgHelper.getUsers(ig, data);

        res.json({
            state: ig.state,
            data: response
        });
    } catch (err) {
        res.json(errorHandler(err));
    }
});

let server;
module.exports = {
    start() {
        if (server) return;
        server = app
            .listen(PORT, () => {
                log(`listening on port ${PORT}`);
            })
            .setTimeout(5 * 60 * 1000);
    },
    stop(cb) {
        if (!server) return;
        server.close();
        cb && cb();
    }
};
