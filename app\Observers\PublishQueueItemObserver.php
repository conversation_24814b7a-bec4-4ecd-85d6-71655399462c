<?php

namespace App\Observers;

use App\PublishQueueItem;

class PublishQueueItemObserver
{
    /**
     * Listen to the deleting event.
     *
     * @param  PublishQueueItem  $item
     * @return void
     */
    public function deleting(PublishQueueItem $item)
    {
        // delete attachment
        $options = $item->options;
        if(!empty($options['attachments'])){
            // no pending posts with same attachments found; delete the attachments
            foreach($options['attachments'] as $attachment){
                if(\Storage::exists($attachment['path']))
                    \Storage::delete($attachment['path']);
            }
            unset($options['attachments']);
        }
    }
}
