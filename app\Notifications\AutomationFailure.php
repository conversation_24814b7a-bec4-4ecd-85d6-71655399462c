<?php

namespace App\Notifications;

use App\Automation;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class AutomationFailure extends Notification implements ShouldQueue
{
    use Queueable;

    protected $automation;
    protected $reason;

    /**
     * Create a new notification instance.
     *
     * @param Automation $automation
     * @param string $reason
     */
    public function __construct(Automation $automation, $reason)
    {
        $this->automation = $automation;
        $this->reason = $reason;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Automation Failure: ' . e(str_limit($this->automation->description, 100)))
            ->error()
            ->line('An error occurred while executing an automation')
            ->line('Automation: ' . e(str_limit($this->automation->description, 255)))
            ->line('Triggered by: ' . studly_case(str_replace('.', '_', $this->automation->event)))
            ->line('Reason: ' . $this->reason)
            ->action('See more', url('app/automations/' . $this->automation->id))
            ->line('Please do not hesitate to contact us if you need any assistance or have any questions.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'automation_id' => $this->automation->id,
            'automation_description' => str_limit($this->automation->description, 255),
            'reason' => $this->reason,
            '_level' => 'danger',
        ];
    }
}
