<?php

namespace App\Console\Commands;

use App\User;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Str;

class UserEmailVerifyReminder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:email_verify_reminder';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remind user to verify their email';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        User::where('verified', false)->chunk(200, function ($users){
            /** @var User[]|Collection $users */
            foreach ($users as $user){
                // when was the last reminder sent?
                $reminderInfo = $user->getOption('email_verify_reminder');
                if(!$reminderInfo) $reminderInfo = [
                    'last_sent' => null,
                    'sent_count' => 0,
                ];
                $lastSent = null;
                if($reminderInfo['last_sent'])
                    $lastSent = Carbon::createFromTimestamp($reminderInfo['last_sent']);
                if($reminderInfo['sent_count'] < 3){

                    // last sent should be 2 days older
                    if(!$lastSent || $lastSent->diffInDays(Carbon::now()) > 4){
                        // send the reminder while we can
                        try {
                            $user->sendEmailVerification(false, true);
                        } catch(\Exception $exception){
                            if(!Str::contains($exception->getMessage(), 'not a valid address')){
                                report($exception);
                            }
                        }
                        // update the reminder info
                        ++$reminderInfo['sent_count'];
                        $reminderInfo['last_sent'] = time();
                        $user->setOption('email_verify_reminder', $reminderInfo);
                    }

                } else {
                    if($lastSent->diffInDays(Carbon::now()) > 15) {
                        // delete the user :(
                        if($user->getPlan(true) === 'free'){
                            $user->delete();
                        }
                    }
                }
            }
        });
    }
}
