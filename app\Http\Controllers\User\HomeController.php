<?php

namespace App\Http\Controllers\User;

use App\Account;
use App\User;
use Firebase\JWT\JWT;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class HomeController extends Controller
{
    public function index() {
        return view('user.index', [
            'unreadFeeds' => \App\Feed::userFeeds()->whereHas('posts', function($query){
                return $query->where('feed_posts.read', false);
            })->count(),
            'automations' => \App\Automation::userAutomations()->count(),
            'pendingPosts' => \App\Post::pending()->ofUser()->count(),
            'failedPosts' => \App\Post::ofUser()->where('published_at', null)->where('result', '<>', null)->count(),
            'inactiveAccounts' => \App\Account::ofUser()->where('active', false)->count(),
        ]);
    }

    public function dashboard(){
        return view('user.dashboard');
    }

    public function info(Request $request){

        // re-flash the session messages
        $request->session()->reflash();

        if(!$request->expectsJson()) abort(404);

        /** @var \App\User $user */
        $user = \Auth::user();

        if($request->input('check')){
            return [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'verified' => !!$user->verified,
            ];
        }

        $allAccountIds = collect($user->accounts()->where('active', true)->get(['id']))->merge($user->accountsFromTeams(true))->map(function($a){
            return $a->id;
        })->toArray();

        // cache data for 1 minute
        return \Cache::remember('user_post_stats_' . $user->id, 60, function() use ($allAccountIds){
            return array_filter([ // filter 0 values
                'posts.draft' => \App\Post::ofUser()->draft()->whereIn('account_id', $allAccountIds)->count(),
                'posts.scheduled' => \App\Post::pending()->whereIn('account_id', $allAccountIds)->count(),
                'posts.awaiting_approval' => \App\Post::awaitingApproval()->whereIn('account_id', $allAccountIds)->count(),
            ]);
        });
    }

    public function review(){
        return view('user.give_review');
    }

    public function roadmap(Request $request){

        $privateKey = config('app.upvoty_private_key'); //This token is the private key under the `Single Sign-On` section

        $user = user();
        $userData = [
            'id' => $user->id, // Required
            'name' => $user->name,  // Required
            'email' => $user->email,  // Optional but preferred
            'avatar' => $user->getPic(), // Optional but preferred
        ];
        $token = JWT::encode($userData, $privateKey, 'HS256');


        if($request->input('sso_login')){
            // redirect to upvoty sso login url
            $redirectUrl = $request->input('redirectUrl');
            return redirect($redirectUrl . '/?custom_sso_token=' . urlencode($token));
        }

        return view('user.roadmap', [
            'upvoty_token' => $token,
        ]);
    }
}
