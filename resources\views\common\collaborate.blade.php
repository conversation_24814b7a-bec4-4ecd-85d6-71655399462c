@extends('layout.full_width')
@php($title = 'Team Collaboration - Manage Your Social Media as a Team')
@php($description = 'Collaborate and work as a team on your social media. Share access to your social media accounts with your team members without sharing your passwords.')
@php($image = 'https://socialbu.com/images/site/link-preview.jpg')
@php($url = 'https://socialbu.com/collaborate')
@section('title', $title . ' | ' . config('app.name'))
@push('head_html')
    <meta name="description" content="{{ $description }}"/>
    <link rel="canonical" href="{{ $url }}" />

    <meta property="og:locale" content="en_US" />
    <!--
    <meta property="og:type" content="website" />
    -->
    <meta property="og:title" content="{{ $title }}" />
    <meta property="og:description" content="{{ $description }}" />
    <meta property="og:url" content="{{ $url }}" />
    <meta property="og:site_name" content="SocialBu" />
    <meta property="og:image" content="{{ $image }}" />
    <meta property="fb:app_id" content="{{ config('services.facebook.client_id') }}" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content="{{ $image }}" />
    <meta name="twitter:title" content="{{ $title }}" />
    <meta name="twitter:description" content="{{ $description }}" />
    <meta name="twitter:site" content="@socialbuapp" />
@endpush
@section('content')

    <header class="header">
        <div class="container">
            <div class="row">
                <div class="col-md-6 pl-md-0 pr-md-0">
                    <h1 class="display-1 text-center text-md-left mb-4">Collaborate & manage teams</h1>
                    
                    <p class="lead-2 text-center text-md-left pt-1 mb-0">
                        SocialBu makes it easy to collaborate and work as a team on your social media.
                    </p>
                    @include('common.internal.start-free-block')
                </div>
                <div class="d-none d-md-block col-12 col-md-6">
                    <img src="/images/1x1.gif" data-src="/images/redesign/collaboration/manage-teams.webp" alt="team collaboration for social media" class="img-responsive hover-move-up lozad position-relative" />
                </div>
            </div>
        </div>
    </header>

    <main class="main-content" id="main">
        <section class="section">
            <div class="container">
                <div class="row gap-y align-items-center">
                    <div class="col-md-6 text-center text-md-left offset-md-1 ml-0">
                        <h2 class="display-2 mb-4">
                            Collaboration made easy
                        </h2>
                        <p class="lead-2 pt-1">
                            Collaborate with your team members to publish content efficiently. You don't need to share passwords with your team members anymore.
                        </p>
                    </div>
                    <div class="col-md-6 order-md-first">
                        <img src="/images/1x1.gif" data-src="/images/redesign/collaboration/collaboration.webp" alt="team on socialbu" data-aos="fade-left" class="img-responsive rounded aos-init aos-animate lozad" />
                    </div>
                </div>
            </div>
        </section>

        <section class="section" id="multiple">
            <div class="container">
                <div class="row gap-y align-items-center">
                    <div class="col-md-6 text-center text-md-left">
                        <h2 class="display-2 mb-4">
                            Manage multiple brands
                        </h2>
                        <p class="lead-2 pt-1">
                            Manage as many accounts and as many brands as you want from one place.
                        </p>
                    </div>
                    <div class="col-md-6 offset-md-1 ml-0 d-flex justify-content-end">
                        <img src="/images/1x1.gif" data-src="/images/redesign/collaboration/manage-brands.webp" alt="draft posts in socialbu" data-aos="fade-left" class="img-responsive rounded aos-init aos-animate lozad" />
                    </div>
                </div>
            </div>
        </section>

        <section class="section" id="approvals">
            <div class="container">
                <div class="row gap-y align-items-center">
                    <div class="col-md-6 text-center text-md-left offset-md-1 ml-0">
                        <h2 class="display-2 mb-4">
                            Review before they get published
                        </h2>
                        <p class="lead-2 pt-1">
                            Post approvals make it easy to review content before it hits the feed.
                        </p>
                    </div>
    
                    <div class="col-md-6 order-md-first">
                        <img src="/images/1x1.gif" data-src="/images/redesign/collaboration/post-review.webp" alt="Post approvals" data-aos="fade-left" class="img-responsive w-500 rounded aos-init aos-animate lozad">
                    </div>
                </div>
            </div>
        </section>

        <section class="section" id="conversations">
            <div class="container">
                <div class="row gap-y align-items-center">
                    <div class="col-md-6 text-center text-md-left">
                        <h2 class="display-2 mb-4">
                            Private notes for your team
                        </h2>
                        <p class="lead-2 pt-1">
                            When you want to leave notes for your team members or discuss something, just add a private note.
                        </p>
                    </div>
                    <div class="col-md-6 offset-md-1 ml-0">
                        <img src="/images/1x1.gif" data-src="/images/redesign/collaboration/private-team.webp" alt="team collison detection" data-aos="fade-left" class="img-responsive rounded aos-init aos-animate lozad" />
                    </div>
                </div>
            </div>
        </section>
        @include('common.internal.join-us-block')

    </main>

@endsection
