<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddStatusColumnToConversationItems extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('inbox_conversation_items', function (Blueprint $table) {
            // we need status for sending status; like "sent" or "failed" maybe
            $table->string('status')->nullable()->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('inbox_conversation_items', function (Blueprint $table) {
            $table->dropColumn('status');
        });
    }
}
