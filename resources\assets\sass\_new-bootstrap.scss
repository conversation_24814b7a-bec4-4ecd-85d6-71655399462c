
// Fonts (important: load all in one request)
@import url(https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap);
// Variables
@import "variables";

// Bootstrap
//@import '~bootstrap/scss/bootstrap';
// thesaas also has bootstrap
@import "../thesaas/thesaas";

// not used; we load fonts from google; todo: delete
// @import "new-fonts";

@import "override/shadows";
@import "override/buttons";
@import "override/border";
@import "override/badge";
@import "override/alert";
@import "override/forms";
@import "override/typography";
@import "override/inputs";
@import "override/images";
@import "override/pagination";
@import "override/spinners";
@import "override/modal";
@import "override/dropdown";
@import "override/toast";
@import "override/card";
@import "override/list-group";
@import "override/navbar";
@import "override/nav";
@import "override/sidebar";
@import "override/table";

.navbar-brand img, .logo {
  max-width: 160px; // logo width
}

.cursor-default {
  cursor: default;
}

.bg-dark {
    background-color: $color-bg-dark !important;
}
.bg-darker {
    background-color: $color-bg-darker !important;
}
.bg-lightest{
    background-color: $color-bg-lightest;
}
.bg-light-blue {
    background: #dfebf7 !important;
}
.bg-pink {
    background-color: #f7e5df !important;
}
.bg-light-pink {
    background-color: #efdff7 !important;
}
.bg-light-yellow {
    background-color: #fef3cc !important;
}
.border-none{
    border: none !important;
}
.border-light {
    border-color: #F1F3F5 !important;
}
.border-lightest {
    border-color: #fafafa !important;
}
.border-dark-1{
    border: 1px solid #353F54 !important;
}

//support for border responsiveness (not available in bootstrap out of the box)
@each $breakpoint in map-keys($grid-breakpoints) {
    @include media-breakpoint-up($breakpoint) {
        $infix: breakpoint-infix($breakpoint, $grid-breakpoints);
        // support of .border-sm(md, lg, xl)-top, -right, -bottom, -left
        .border#{$infix}-top {      border-top: $border-width solid $border-color; }
        .border#{$infix}-right {    border-right: $border-width solid $border-color; }
        .border#{$infix}-bottom {   border-bottom: $border-width solid $border-color; }
        .border#{$infix}-left {     border-left: $border-width solid $border-color; }
        .border#{$infix}-none {     border: 0 !important; }
        // support of .border-sm-top-0, -right-0, -bottom-0, -left-0
        .border#{$infix}-top-0 {    border-top: 0; }
        .border#{$infix}-right-0 {  border-right: 0; }
        .border#{$infix}-bottom-0 { border-bottom: 0; }
        .border#{$infix}-left-0 {   border-left: 0; }
        // support of .border-sm (-md, -lg, -xl)
        .border#{$infix} {
            border-top: $border-width solid $border-color;
            border-bottom: $border-width solid $border-color;
            border-left: $border-width solid $border-color;
            border-right: $border-width solid $border-color;
        }
        // support of .border-sm-1 (-md, -lg, -xl)
        .border#{$infix}-1 {
            border-top: $border-width solid $border-color;
            border-bottom: $border-width solid $border-color;
            border-left: $border-width solid $border-color;
            border-right: $border-width solid $border-color;
        }
        // support of .border-sm-0 (-md, -lg, -xl)
        .border#{$infix}-0 {
            border-top: 0;
            border-bottom: 0;
            border-left: 0;
            border-right: 0;
        }
    }
}

.p-40{
    padding: 40px !important;
}
.p-20{
    padding: 20px !important;
}
.px-20{
    padding-left: 20px;
    padding-right: 20px;
}
.py-20{
    padding-top: 20px;
    padding-bottom: 20px;
}
.pt-20{
    padding-top: 20px !important;
}
.mx-20{
    margin-left: 20px;
    margin-right: 20px;
}
.pb-20{
    padding-bottom: 20px;
}
.header {
    padding-bottom: 80px;
    padding-top: 80px;
}
.section {
    padding-top: 5rem;
    padding-bottom: 5rem;
}
/* social icons */
.social-icon {
    color: #808080;
    font-size: 20px !important;
    &.facebook {
        color: #1877F2;
    }
    &.linkedin {
        color: #2867B2;
    }
    &.twitter {
        color: #000000;
    }
    &.google {
        color: #d62d20;
    }
    &.google-business-profile {
        color: #4285f4;
    }
    &.instagram {
        color: #D536E0;
    }
    &.mastodon {
        color: #563ACC;
    }
    &.tiktok {
        color: #000;
    }
    &.youtube {
        color: #ff3000;
    }
    &.reddit {
        color: #ff4500;
    }
    &.pinterest {
        color: #F0002A;
    }
    &.threads {
        color: #000;
    }
    &.bluesky {
        color: #0085ff;
    }
}
.position-sticky-top-fix {
    top: 55px;
    z-index: 10;
}
.close{
    opacity: 1 !important;
}
.close-button{
    border: none;
    background: none;
    padding-right: 0px !important;
    padding-top: 4px !important;

}

.ph{
    vertical-align: middle;
    font-size: 16px;
}
.ph-xs{
    font-size: 8px;
}
.ph-sm{
    font-size: 15px;
}
.ph-md{
    font-size: 20px;
}
.ph-lg{
    font-size: 24px;
}
.ph-xl{
    font-size: 40px;
}
.ph-2xl{
    font-size: 72px;
}

@-webkit-keyframes spin{
    0% {
        -webkit-transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
    }
}
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.ph-spin {
    -webkit-animation: spin 1s linear infinite;
    display: inline-block;
    animation: spin 1s infinite linear;
}

//for cookie pop up
#cc-main {
    color-scheme: light;

    --cc-bg: #f9faff;
    --cc-primary-color: #112954;
    --cc-secondary-color: #112954;

    --cc-btn-primary-bg: #0557f0;
    --cc-btn-primary-color: var(--cc-bg);
    --cc-btn-primary-hover-bg: #213657;
    --cc-btn-primary-hover-color: #fff;

    --cc-btn-secondary-bg: #dfe7f9;
    --cc-btn-secondary-color: var(--cc-secondary-color);
    --cc-btn-secondary-hover-bg: #c6d1ea;
    --cc-btn-secondary-hover-color: #000;

    --cc-cookie-category-block-bg: #ebeff9;
    --cc-cookie-category-block-border: #ebeff9;
    --cc-cookie-category-block-hover-bg: #dbe5f9;
    --cc-cookie-category-block-hover-border: #dbe5f9;
    --cc-cookie-category-expanded-block-hover-bg: #ebeff9;
    --cc-cookie-category-expanded-block-bg: #ebeff9;

    --cc-overlay-bg: rgba(219, 232, 255, 0.85)!important;

    --cc-toggle-readonly-bg: #cbd8f1;
    --cc-toggle-on-knob-bg: var(--cc-bg);
    --cc-toggle-off-bg: #8fa8d6;
    --cc-toggle-readonly-knob-bg: var(--cc-bg);

    --cc-separator-border-color: #f1f3f5;

    --cc-footer-border-color: #f1f3f5;
    --cc-footer-bg: var(--cc-bg);

    /** Make the buttons a bit rounder **/
    --cc-btn-border-radius: 8px;

    --cc-modal-border-radius: var(--cc-btn-border-radius);
    --cc-pm-toggle-border-radius: var(--cc-btn-border-radius);
}

.cc--light-funky #cc-main .toggle__icon:after {
    border-radius: var(--cc-btn-border-radius);
}

.cc--light-funky #cc-main .cm__btn--close {
    border-radius: var(--cc-btn-border-radius);
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

// Change default behavior of scrollbar
::-webkit-scrollbar {
    width: 6px;
}
/* Track */
::-webkit-scrollbar-track {
    background: #f1f1f1; 
}
/* Handle */
::-webkit-scrollbar-thumb {
    border-radius: 20px;
    background: #D6DAE0; 
}

.section-header {
    max-width: 80%;
}

.tools-page {
    table, .table {
        width: 100%;
        border: 1px solid $color-divider-light;
        th,
        td {
            width: 50%;
            font-size: 16px;
            font-weight: 400;
            border: $table-border-width solid $table-border-color;
            vertical-align: middle;
            padding: 10px 15px;
            text-align: center;
        }
        tr:nth-child(odd) td {
            background-color: #fafafa;
        }
        thead th {
            font-size: 1rem;
            font-weight: 500;
            border-bottom: none;
            border-top: $table-border-width solid $table-border-color;
            span{
                font-size: 1.25rem;
                font-weight: 500;
                
            }
        }
        td.section-main{
            font-weight: 700;
        }
        tbody + tbody {
            border-top: (4 * $table-border-width) solid $table-border-color;
        }
    }
}
.pricing-page{
    .table, table{
        th{
            font-size: 20px;
            font-weight: 600;
            color: #1E283B;
            line-height: 24px;
            font-family: "Plus Jakarta Sans";
            .price{
                span{
                    font-size: 16px;
                    font-weight: 400;
                    color: #465166;
                    font-family: "DM Sans";
                }
            }
        }
        td{
            font-size: 20px;
        }
    }
}
.bootstrap-datetimepicker-widget{
    table{
        border-radius: 12px !important;
        border-collapse: inherit !important;
    }
    table td {
        border-top: none !important;
        font-size: 14px;
        span{
            color: #465166;
        }
    }
    table td.disabled{
        opacity: 0.6;
    }
    table td.day{
        line-height: 21px !important;
        font-weight: 500;
    }
    .timepicker{
        padding-right: 0px !important;
        .timepicker-picker{
            margin-top: 3rem;
            margin-left: 24px;
        }
    }
}
.border-top-dashed{
    border-top: 1px dashed #E4E7ED;
}
.border-left-dashed{
    border-left: 1px dashed #E4E7ED;
}
.border-right-dashed{
    border-right: 1px dashed #E4E7ED;
}
.border-bottom-dashed{
    border-bottom: 1px dashed #E4E7ED;
}
.h-265{
    height: 265px;
}
.h-241{
    height: 241px;
}