<?php

namespace InstagramAPI;

class Constants
{
    // Core API Constants.
    const API_URLS = [
        1 => 'https://i.instagram.com/api/v1/',
        2 => 'https://i.instagram.com/api/v2/',
    ];
    const IG_VERSION = '107.0.0.27.121';
    const VERSION_CODE = '168361634';
    const IG_SIG_KEY = 'c36436a942ea1dbb40d7f2d7d45280a620d991ce8c62fb4ce600f0a048c32c11';
    const EXPERIMENTS = 'ig_android_push_notifications_settings_redesign_universe,ig_hashtag_display_universe,ig_android_video_ssim_fix_pts_universe,coupon_price_test_ad4ad_instagram_resurrection_universe,ig_android_live_rendering_looper_universe,ig_shopping_checkout_improvements_universe,ig_android_mqtt_cookie_auth_memcache_universe,ig_android_optional_contact_and_preset_universe,ig_android_video_player_memory_leaks,ig_android_stories_seen_state_serialization,ig_stories_photo_time_duration_universe,ig_android_bitmap_cache_executor_size,ig_android_stories_music_search_typeahead,android_ard_ig_use_brotli_effect_universe,ig_android_remove_fb_nux_universe,ig_android_delayed_comments,ig_android_direct_mutation_manager_media_3,ig_smb_ads_holdout_2019_h1_universe,ig_fb_graph_differentiation,ig_android_stories_share_extension_video_segmentation,ig_android_igtv_crop_top,ig_android_stories_create_flow_favorites_tooltip,ig_android_direct_reshare_chaining,ig_android_stories_no_inflation_on_app_start,ig_android_stories_viewer_viewpoint_universe,ig_android_separate_empty_feed_su_universe,ig_android_zero_rating_carrier_signal,ig_direct_holdout_h1_2019,ig_explore_2019_h1_destination_cover,ig_android_direct_stories_in_direct_inbox,ig_android_explore_recyclerview_universe,ig_android_show_muted_accounts_page,ig_android_vc_service_crash_fix_universe,ig_camera_android_subtle_filter_universe,ig_android_lazy_init_live_composer_controller,ig_fb_graph_differentiation_no_fb_data,ig_android_viewpoint_stories_public_testing,ig_camera_android_api_rewrite_universe,ig_android_growth_fci_team_holdout_universe,android_camera_core_cpu_frames_sync,ig_android_video_source_sponsor_fix,ig_android_save_all,ig_android_ttcp_improvements,ig_android_camera_ar_platform_profile_universe,ig_android_separate_sms_n_email_invites_setting_universe,ig_shopping_bag_universe,ig_ar_shopping_camera_android_universe,ig_android_recyclerview_binder_group_enabled_universe,ig_android_stories_viewer_tall_android_cap_media_universe,ig_android_video_exoplayer_2,native_contact_invites_universe,ig_android_stories_seen_state_processing_universe,ig_android_dash_script,ig_android_insights_media_hashtag_insight_universe,ig_android_search_qpl_switch,ig_camera_fast_tti_universe,ig_android_igtv_improved_search,ig_android_stories_music_filters,ig_android_render_thread_memory_leak_holdout,ig_android_automated_logging,ig_android_viewmaster_post_capture_universe,ig_android_2018_h1_hashtag_report_universe,ig_android_camera_effect_gallery_prefetch,ig_share_to_story_toggle_include_shopping_product,ig_android_interactions_verified_badge_on_comment_details,ig_android_fix_ppr_thumbnail_url,ig_android_camera_reduce_file_exif_reads,ig_interactions_project_daisy_creators_universe,ig_payments_billing_address,ig_android_fs_new_gallery_hashtag_prompts,ig_camera_android_gyro_senser_sampling_period_universe,ig_android_xposting_feed_to_stories_reshares_universe,ig_android_combined_consumption,ig_camera_remove_display_rotation_cb_universe,ig_android_interactions_migrate_inline_composer_to_viewpoint_universe,ig_android_ufiv3_holdout,ig_android_neue_igtv_profile_tab_rollout,ig_android_enable_zero_rating,ig_android_story_ads_carousel_performance_universe_1,ig_android_direct_leave_from_group_message_requests,ig_android_import_page_post_after_biz_conversion,ig_camera_ar_effect_attribution_position,ig_promote_add_payment_navigation_universe,ig_android_story_ads_carousel_performance_universe_2,ig_android_main_feed_refresh_style_universe,ig_stories_engagement_holdout_2019_h1_universe,ig_android_story_ads_performance_universe_1,ig_android_stories_viewer_modal_activity,ig_android_story_ads_performance_universe_2,ig_android_publisher_stories_migration,ig_android_story_ads_performance_universe_3,ig_android_quick_conversion_universe,ig_android_story_import_intent,ig_android_story_ads_performance_universe_4,instagram_android_profile_follow_cta_context_feed,ig_biz_graph_connection_universe,ig_android_stories_boomerang_v2_universe,ig_android_ads_profile_cta_feed_universe,ig_android_vc_cowatch_universe,ig_android_nametag,ig_hashtag_creation_universe,ig_android_igtv_chaining,ig_android_live_qa_viewer_v1_universe,ig_shopping_insights_wc_copy_update_android,ig_android_stories_music_lyrics_pre_capture,android_cameracore_fbaudio_integration_ig_universe,ig_android_camera_stopmotion,ig_android_igtv_reshare,ig_android_wellbeing_timeinapp_v1_universe,ig_android_profile_cta_v3,ig_end_of_feed_universe,ig_android_mainfeed_generate_prefetch_background,ig_android_vc_shareable_moments_universe,ig_camera_text_overlay_controller_opt_universe,ig_android_shopping_product_metadata_on_product_tiles_universe,ig_android_video_qp_logger_universe,ig_android_shopping_pdp_cache,ig_android_follow_request_button_improvements_universe,ig_android_vc_start_from_direct_inbox_universe,ig_android_separate_network_executor,ig_perf_android_holdout,ig_fb_graph_differentiation_only_fb_candidates,ig_android_media_streaming_sdk_universe,ig_android_direct_reshares_from_thread,ig_android_stories_video_prefetch_kb,ig_android_wellbeing_timeinapp_v1_migration,ig_android_camera_post_smile_face_first_universe,ig_android_maintabfragment,ig_android_cookie_injection_retry_universe,ig_inventory_connections,ig_stories_injection_tool_enabled_universe,ig_android_canvas_cookie_universe,ig_android_stories_disable_highlights_media_preloading,ig_android_effect_gallery_post_capture_universe,ig_android_shopping_variant_selector_redesign,ig_android_branded_content_ads_universe,ig_promote_lotus_universe,ig_android_video_streaming_upload_universe,ig_camera_android_attribution_bottomsheet_universe,ig_android_product_tag_hint_dots,ig_interactions_h1_2019_team_holdout_universe,ig_camera_android_release_drawing_view_universe,ig_android_music_story_fb_crosspost_universe,ig_android_disable_scroll_listeners,ig_carousel_bumped_organic_impression_client_universe,ig_android_ad_async_ads_universe,ig_biz_post_approval_nux_universe,ig_android_vc_participants_grid_refactor_universe,android_ard_ig_modelmanager_cache_hit,ig_android_persistent_nux,ig_android_crash_fix_detach_from_gl_context,ig_android_branded_content_upsell_keywords_extension,ig_android_vc_ringscreen_timeout_universe,ig_android_edit_location_page_info,ig_android_stories_project_eclipse,ig_camera_android_segmentation_v106_igdjango_universe,ig_android_camera_recents_gallery_modal,ig_promote_are_you_sure_universe,ig_android_li_session_chaining,ig_android_camera_platform_effect_share_universe,ig_android_rate_limit_mediafeedviewablehelper,ig_android_search_empty_state,ig_android_camera_ar_platform_logging,ig_stories_engagement_holdout_2019_h2_universe,ig_android_search_remove_null_state_sections,ig_direct_android_mentions_receiver,ig_camera_android_device_capabilities_experiment,ig_android_stories_viewer_drawable_cache_universe,ig_camera_android_qcc_constructor_opt_universe,ig_android_stories_alignment_guides_universe,ig_android_rn_ads_manager_universe,ig_android_video_visual_quality_score_based_abr,ig_explore_2018_post_chaining_account_recs_dedupe_universe,ig_android_stories_video_seeking_audio_bug_fix,ig_android_insights_holdout,ig_android_do_not_show_social_context_for_likes_page_universe,ig_android_context_feed_recycler_view,ig_fb_notification_universe,ig_android_report_website_universe,ig_android_feed_post_sticker,ig_android_inline_editing_local_prefill,ig_android_commerce_platform_bloks_universe,ig_android_stories_unregister_decor_listener_universe,ig_android_search_condensed_search_icons,ig_android_video_abr_universe,ig_android_blended_inbox_split_button_v2_universe,ig_android_nelson_v0_universe,ig_android_scroll_audio_priority,ig_android_own_profile_sharing_universe,ig_android_vc_cowatch_media_share_universe,ig_biz_graph_unify_assoc_universe,ig_challenge_general_v2,ig_android_place_signature_universe,ig_android_direct_inbox_cache_universe,ig_android_ig_branding_in_fb_universe,ig_android_business_promote_tooltip,ig_android_tap_to_capture_universe,ig_android_follow_requests_ui_improvements,ig_android_video_ssim_fix_compare_frame_index,ig_android_direct_aggregated_media_and_reshares,ig_android_story_camera_share_to_feed_universe,ig_android_fb_follow_server_linkage_universe,ig_android_stories_viewer_reply_box_placeholder_copy,ig_android_biz_reorder_value_props,ig_android_direct_view_more_qe,ig_android_churned_find_friends_redirect_to_discover_people,ig_android_main_feed_new_posts_indicator_universe,ig_vp9_hd_blacklist,ig_camera_android_ar_effect_stories_deeplink,ig_android_client_side_delivery_universe,ig_ios_queue_time_qpl_universe,ig_android_fix_direct_badge_count_universe,ig_android_insights_audience_tab_native_universe,ig_android_stories_send_client_reels_on_tray_fetch_universe,ig_android_felix_prefetch_thumbnail_sprite_sheet,ig_android_live_use_rtc_upload_universe,ig_android_multi_dex_class_loader_v2,ig_android_live_ama_viewer_universe,ig_smb_ads_holdout_2018_h2_universe,ig_android_camera_post_smile_low_end_universe,ig_android_profile_follow_tab_hashtag_row_universe,ig_android_watch_and_more_redesign,igtv_feed_previews,ig_android_live_realtime_comments_universe,ig_android_story_insights_native_universe,ig_smb_ads_holdout_2019_h2_universe,ig_android_purx_native_checkout_universe,ig_camera_android_filter_optmizations,ig_android_integrity_sprint_universe,ig_android_apr_lazy_build_request_infra,ig_android_igds_edit_profile_fields,ig_android_business_transaction_in_stories_creator,ig_android_rounded_corner_framelayout_perf_fix,ig_android_branded_content_appeal_states,android_cameracore_ard_ig_integration,ig_video_experimental_encoding_consumption_universe,ig_android_iab_autofill,ig_android_creator_quick_reply_universe,ig_android_location_page_intent_survey,ig_camera_android_segmentation_async_universe,ig_android_biz_story_to_fb_page_improvement,ig_android_direct_thread_target_queue_universe,ig_android_branded_content_insights_disclosure,ig_camera_android_target_recognition_universe,ig_camera_android_skip_camera_initialization_open_to_post_capture,ig_android_combined_tagging_videos,ig_android_stories_samsung_sharing_integration,ig_android_create_page_on_top_universe,ig_iab_use_default_intent_loading,ig_android_camera_focus_v2,ig_android_biz_conversion_pull_suggest_biz_data,ig_discovery_holdout_2019_h1_universe,ig_android_wellbeing_support_frx_comment_reporting,ig_android_insights_post_dismiss_button,ig_android_user_url_deeplink_fbpage_endpoint,ig_android_ad_holdout_watchandmore_universe,ig_android_follow_request_button_new_ui,ig_iab_dns_prefetch_universe,ig_android_explore_use_shopping_endpoint,ig_android_image_upload_skip_queue_only_on_wifi,ig_android_igtv_pip,ig_android_ad_watchbrowse_carousel_universe,ig_android_camera_new_post_smile_universe,ig_android_shopping_signup_redesign_universe,ig_android_direct_hide_inbox_header,ig_shopping_pdp_more_related_product_section,ig_android_experimental_onetap_dialogs_universe,ig_android_fix_main_feed_su_cards_size_universe,ig_android_direct_multi_upload_universe,ig_camera_text_mode_composer_controller_opt_universe,ig_explore_2019_h1_video_autoplay_resume,ig_android_multi_capture_camera,ig_android_video_upload_quality_qe1,ig_android_follow_requests_copy_improvements,ig_android_save_collaborative_collections,coupon_price_test_boost_instagram_media_acquisition_universe,ig_android_video_outputsurface_handlerthread_universe,ig_android_country_code_fix_universe,ig_perf_android_holdout_2018_h1,ig_android_stories_music_overlay,ig_android_enable_lean_crash_reporting_universe,ig_android_resumable_downloads_logging_universe,ig_android_low_latency_consumption_universe,ig_android_render_output_surface_timeout_universe,ig_android_big_foot_foregroud_reporting,ig_android_unified_iab_logging_universe,ig_threads_app_close_friends_integration,ig_aggregated_quick_reactions,ig_android_shopping_pdp_post_purchase_sharing,ig_android_aggressive_cookie,ig_android_offline_mode_holdout,ig_android_realtime_mqtt_logging,ig_android_rainbow_hashtags,ig_android_no_bg_effect_tray_live_universe,ig_android_direct_block_from_group_message_requests,ig_android_react_native_universe_kill_switch,ig_android_viewpoint_occlusion,ig_android_logged_in_delta_migration,ig_android_push_reliability_universe,ig_android_stories_gallery_video_segmentation,ig_android_direct_business_holdout,ig_android_vc_direct_inbox_ongoing_pill_universe,ig_android_xposting_upsell_directly_after_sharing_to_story,ig_android_direct_sticker_search_upgrade_universe,ig_android_insights_native_post_universe,ig_android_dual_destination_quality_improvement,ig_android_camera_focus_low_end_universe,ig_android_camera_hair_segmentation_universe,ig_android_direct_combine_action_logs,ig_android_leak_detector_upload_universe,ig_android_ads_data_preferences_universe,ig_android_branded_content_access_upsell,ig_android_follow_button_in_story_viewers_list,ig_android_vc_background_call_toast_universe,ig_hashtag_following_holdout_universe,ig_promote_default_destination_universe,ig_android_delay_segmentation_low_end_universe,ig_android_direct_media_latency_optimizations,mi_viewpoint_viewability_universe,android_ard_ig_download_manager_v2,ig_direct_reshare_sharesheet_ranking,ig_music_dash,ig_android_fb_url_universe,ig_android_le_videoautoplay_disabled,ig_android_reel_raven_video_segmented_upload_universe,ig_android_promote_native_migration_universe,invite_friends_by_messenger_in_setting_universe,ig_android_fb_sync_options_universe,ig_android_thread_gesture_refactor,ig_android_stories_skip_seen_state_update_for_direct_stories,ig_android_recommend_accounts_destination_routing_fix,ig_android_fix_prepare_direct_push,ig_android_enable_automated_instruction_text_ar,ig_android_multi_author_story_reshare_universe,ig_android_building_aymf_universe,ig_android_internal_sticker_universe,ig_traffic_routing_universe,ig_android_payments_growth_business_payments_within_payments_universe,ig_camera_async_gallerycontroller_universe,ig_android_direct_state_observer,ig_android_page_claim_deeplink_qe,ig_android_camera_effects_order_universe,ig_android_video_controls_universe,ig_android_video_local_proxy_video_metadata,ig_android_logging_metric_universe_v2,ig_android_network_onbehavior_change_fix,ig_android_xposting_newly_fbc_people,ig_android_visualcomposer_inapp_notification_universe,ig_android_do_not_show_social_context_on_follow_list_universe,ig_android_contact_point_upload_rate_limit_killswitch,ig_android_webrtc_encoder_factory_universe,ig_android_qpl_class_marker,ig_android_fix_profile_pic_from_fb_universe,ig_android_sso_kototoro_app_universe,ig_android_camera_3p_in_post,ig_android_ar_effect_sticker_consumption_universe,ig_android_direct_unread_count_badge,ig_android_profile_thumbnail_impression,ig_android_igtv_autoplay_on_prepare,ig_android_list_adapter_prefetch_infra,ig_file_based_session_handler_2_universe,ig_branded_content_tagging_upsell,ig_android_clear_inflight_image_request,ig_android_main_feed_video_countdown_timer,ig_android_live_ama_universe,ig_android_external_gallery_import_affordance,ig_search_hashtag_content_advisory_remove_snooze,ig_payment_checkout_info,ig_android_optic_new_zoom_controller,ig_android_photos_qpl,ig_stories_ads_delivery_rules,ig_android_downloadable_spark_spot,ig_android_video_upload_iframe_interval,ig_business_new_value_prop_universe,ig_android_power_metrics,ig_android_vio_pipeline_universe,ig_android_show_profile_picture_upsell_in_reel_universe,ig_discovery_holdout_universe,ig_android_direct_import_google_photos2,ig_direct_feed_media_sticker_universe,ig_android_igtv_upload_error_messages,ig_android_stories_collapse_seen_segments,ig_android_self_profile_suggest_business_main,ig_android_suggested_users_background,ig_android_fetch_xpost_setting_in_camera_fully_open,ig_android_hashtag_discover_tab,ig_android_stories_separate_overlay_creation,ig_android_ads_bottom_sheet_report_flow,ig_android_login_onetap_upsell_universe,ig_android_iris_improvements,enable_creator_account_conversion_v0_universe,ig_android_test_not_signing_address_book_unlink_endpoint,ig_android_low_disk_recovery_universe,ig_ei_option_setting_universe,ig_android_account_insights_native_universe,ig_camera_android_ar_platform_universe,ig_android_browser_ads_page_content_width_universe,ig_android_stories_viewer_prefetch_improvements,ig_android_livewith_liveswap_optimization_universe,ig_android_camera_leak,ig_android_feed_core_unified_tags_universe,ig_android_jit,ig_android_optic_camera_warmup,ig_stories_rainbow_ring,ig_android_place_search_profile_image,ig_android_vp8_audio_encoder,android_cameracore_safe_makecurrent_ig,ig_android_analytics_diagnostics_universe,ig_android_ar_effect_sticker_universe,ig_direct_android_mentions_sender,ig_android_whats_app_contact_invite_universe,ig_android_stories_reaction_setup_delay_universe,ig_shopping_visual_product_sticker,ig_android_profile_unified_follow_view,ig_android_video_upload_hevc_encoding_universe,ig_android_mentions_suggestions,ig_android_vc_face_effects_universe,ig_android_fbpage_on_profile_side_tray,ig_android_direct_empty_state,ig_android_shimmering_loading_state,ig_android_igtv_refresh_tv_guide_interval,ig_android_gallery_minimum_video_length,ig_android_notif_improvement_universe,ig_android_hashtag_remove_share_hashtag,ig_android_fb_profile_integration_fbnc_universe,ig_shopping_checkout_2x2_platformization_universe,ig_android_direct_bump_active_threads,ig_fb_graph_differentiation_control,ig_android_show_create_content_pages_universe,ig_android_igsystrace_universe,ig_android_search_register_recent_store,ig_feed_content_universe,ig_android_disk_usage_logging_universe,ig_android_search_without_typed_hashtag_autocomplete,ig_android_video_product_specific_abr,ig_android_vc_interop_latency,ig_android_stories_layout_universe,ig_android_dont_animate_shutter_button_on_open,ig_android_vc_cpu_overuse_universe,ig_android_invite_list_button_redesign_universe,ig_android_react_native_email_sms_settings_universe,ig_hero_player,ag_family_bridges_2018_h2_holdout,ig_promote_net_promoter_score_universe,ig_android_save_auto_sharing_to_fb_option_on_server,aymt_instagram_promote_flow_abandonment_ig_universe,ig_android_whitehat_options_universe,ig_android_keyword_media_serp_page,ig_android_delete_ssim_compare_img_soon,ig_android_felix_video_upload_length,android_cameracore_preview_frame_listener2_ig_universe,ig_android_direct_message_follow_button,ig_android_biz_conversion_suggest_biz_nux,ig_stories_ads_media_based_insertion,ig_android_analytics_background_uploader_schedule,ig_camera_android_boomerang_attribution_universe,ig_android_igtv_browse_long_press,ig_android_profile_neue_infra_rollout_universe,ig_android_profile_ppr_fixes,ig_discovery_2019_h2_holdout_universe,ig_android_stories_weblink_creation,ig_android_blur_image_renderer,ig_profile_company_holdout_h2_2018,ig_android_ads_manager_pause_resume_ads_universe,ig_android_vc_capture_universe,ig_nametag_local_ocr_universe,ig_android_stories_media_seen_batching_universe,ig_android_interactions_nav_to_permalink_followup_universe,ig_camera_discovery_surface_universe,ig_android_save_to_collections_flow,ig_android_direct_segmented_video,instagram_stories_time_fixes,ig_android_le_cold_start_improvements,ig_android_direct_mark_as_read_notif_action,ig_android_stories_async_view_inflation_universe,ig_android_stories_recently_captured_universe,ig_android_direct_inbox_presence_refactor_universe,ig_business_integrity_ipc_universe,ig_android_direct_selfie_stickers,ig_android_vc_missed_call_call_back_action_universe,ig_cameracore_android_new_optic_camera2,ig_fb_graph_differentiation_top_k_fb_coefficients,ig_android_fbc_upsell_on_dp_first_load,ig_android_rename_share_option_in_dialog_menu_universe,ig_android_direct_refactor_inbox_observable_universe,ig_android_business_attribute_sync,ig_camera_android_bg_processor,ig_android_view_and_likes_cta_universe,ig_android_optic_new_focus_controller,ig_android_dropframe_manager,ig_android_direct_default_group_name,ig_android_optic_new_features_implementation,ig_android_search_hashtag_badges,ig_android_stories_reel_interactive_tap_target_size,ig_android_video_live_trace_universe,ig_android_tango_cpu_overuse_universe,ig_android_igtv_browse_with_pip_v2,ig_android_direct_fix_realtime_status,ig_android_unfollow_from_main_feed_v2,ig_android_self_story_setting_option_in_menu,ig_android_story_ads_tap_and_hold_fixes,ig_android_camera_ar_platform_details_view_universe,android_ard_ig_cache_size,ig_android_story_real_time_ad,ig_android_hybrid_bitmap_v4,ig_android_iab_downloadable_strings_universe,ig_android_branded_content_ads_enable_partner_boost,ufi_share,ig_android_direct_remix_visual_messages,ig_quick_story_placement_validation_universe,ig_android_custom_story_import_intent,ig_android_live_qa_broadcaster_v1_universe,ig_android_search_impression_logging_viewpoint,ig_android_downloadable_fonts_universe,ig_android_view_info_universe,ig_android_camera_upsell_dialog,ig_android_business_transaction_in_stories_consumer,ig_android_dead_code_detection,ig_android_promotion_insights_bloks,ig_android_direct_autoplay_videos_automatically,ig_android_ad_watchbrowse_universe,ig_android_pbia_proxy_profile_universe,ig_android_qp_kill_switch,ig_android_new_follower_removal_universe,instagram_android_stories_sticker_tray_redesign,ig_android_branded_content_access_tag,ig_android_gap_rule_enforcer_universe,ig_android_business_cross_post_with_biz_id_infra,ig_android_direct_delete_or_block_from_message_requests,ig_android_photo_invites,ig_interactions_h2_2019_team_holdout_universe,ig_android_reel_tray_item_impression_logging_viewpoint,ig_account_identity_2018_h2_lockdown_phone_global_holdout,ig_android_direct_left_aligned_navigation_bar,ig_android_high_res_gif_stickers,ig_android_feed_load_more_viewpoint_universe,ig_android_stories_reshare_reply_msg,ig_close_friends_v4,ig_android_ads_history_universe,ig_android_pigeon_sampling_runnable_check,ig_promote_media_picker_universe,ig_direct_holdout_h2_2018,ig_android_sidecar_report_ssim,ig_android_pending_media_file_registry,ig_android_wab_adjust_resize_universe,ig_camera_android_facetracker_v12_universe,ig_android_camera_ar_effects_low_storage_universe,ig_android_profile_add_profile_pic_universe,ig_android_ig_to_fb_sync_universe,ig_android_ar_background_effect_universe,ig_android_audience_control,ig_android_fix_recommended_user_impression,ig_android_stories_cross_sharing_to_fb_holdout_universe,shop_home_hscroll_see_all_button_universe,ig_android_refresh_empty_feed_su_universe,ig_android_shopping_parallel_pdp_fetch,ig_android_enable_main_feed_reel_tray_preloading,ig_android_ad_view_ads_native_universe,ig_android_branded_content_tag_redesign_organic,ig_android_profile_neue_universe,ig_android_igtv_whitelisted_for_web,ig_android_viewmaster_dial_ordering_universe,ig_company_profile_holdout,ig_rti_inapp_notifications_universe,ig_android_vc_join_timeout_universe,ig_shop_directory_entrypoint,ig_android_direct_rx_thread_update,ig_android_add_ci_upsell_in_normal_account_chaining_universe,ig_android_feed_core_ads_2019_h1_holdout_universe,ig_close_friends_v4_global,ig_android_share_publish_page_universe,ig_android_new_camera_design_universe,ig_direct_max_participants,ig_promote_hide_local_awareness_universe,ar_engine_audio_service_fba_decoder_ig,ar_engine_audio_fba_integration_instagram,ig_android_igtv_save,ig_android_explore_lru_cache,ig_android_graphql_survey_new_proxy_universe,ig_android_music_browser_redesign,ig_camera_android_try_on_camera_universe,ig_android_follower_following_whatsapp_invite_universe,ig_android_fs_creation_flow_tweaks,ig_direct_blocking_redesign_universe,ig_android_viewmaster_ar_memory_improvements,ig_android_downloadable_vp8_module,ig_android_claim_location_page,ig_android_direct_inbox_recently_active_presence_dot_universe,ig_android_stories_gutter_width_universe,ig_android_story_ads_2019_h1_holdout_universe,ig_android_3pspp,ig_android_cache_timespan_objects,ig_timestamp_public_test,ig_android_fb_profile_integration_universe,ig_android_feed_auto_share_to_facebook_dialog,ig_android_skip_button_content_on_connect_fb_universe,ig_android_network_perf_qpl_ppr,ig_android_post_live,ig_camera_android_focus_attribution_universe,ig_camera_async_space_validation_for_ar,ig_android_core_search_2019_h2,ig_android_prefetch_notification_data,ig_android_stories_music_line_by_line_cube_reveal_lyrics_sticker,ig_android_iab_clickid_universe,ig_android_interactions_hide_keyboard_onscroll,ig_early_friending_holdout_universe,ig_story_camera_reverse_video_experiment,ig_android_profile_lazy_load_carousel_media,ig_android_stories_question_sticker_music_format,ig_android_vpvd_impressions_universe,ig_android_payload_based_scheduling,ig_pacing_overriding_universe,ig_android_ard_ptl_universe,ig_android_q3lc_transparency_control_settings,ig_stories_selfie_sticker,ig_android_sso_use_trustedapp_universe,ig_android_stories_music_lyrics,ig_android_spark_studio_promo,ig_android_stories_music_awareness_universe,ard_ig_broti_effect,ig_android_camera_class_preloading,ig_android_new_fb_page_selection,ig_video_holdout_h2_2017,ig_background_prefetch,ig_camera_android_focus_in_post_universe,ig_android_time_spent_dashboard,ig_android_story_sharing_universe,ig_promote_political_ads_universe,ig_android_camera_effects_initialization_universe,ig_promote_post_insights_entry_universe,ig_android_ad_iab_qpl_kill_switch_universe,ig_android_live_subscribe_user_level_universe,ig_android_igtv_creation_flow,ig_android_vc_sounds_universe,ig_android_video_call_finish_universe,ig_camera_android_cache_format_picker_children,direct_unread_reminder_qe,ig_android_direct_mqtt_send,ig_android_self_story_button_non_fbc_accounts,ig_android_self_profile_suggest_business_gating,ig_feed_video_autoplay_stop_threshold,ig_android_explore_discover_people_entry_point_universe,ig_android_live_webrtc_livewith_params,ig_feed_experience,ig_android_direct_activator_cards,ig_android_vc_codec_settings,ig_promote_prefill_destination_universe,ig_android_appstate_logger,ig_android_profile_leaks_holdouts,ig_android_video_cached_bandwidth_estimate,ig_promote_insights_video_views_universe,ig_android_global_scheduler_offscreen_prefetch,ig_android_discover_interests_universe,ig_android_camera_gallery_upload_we_universe,ig_android_business_category_sticky_header_qe,ig_android_dismiss_recent_searches,ig_android_feed_camera_size_setter,ig_payment_checkout_cvv,ig_android_fb_link_ui_polish_universe,ig_android_tags_unification_universe,ig_android_shopping_lightbox,ig_android_bandwidth_timed_estimator,ig_android_stories_mixed_attribution_universe,ig_iab_tti_holdout_universe,ig_android_ar_button_visibility,ig_android_igtv_crop_top_consumption,ig_android_camera_gyro_universe,ig_android_nametag_effect_deeplink_universe,ig_android_blurred_product_image_previews,ig_android_igtv_ssim_report,ig_android_optic_surface_texture_cleanup,ig_android_business_remove_unowned_fb_pages,ig_android_stories_combined_asset_search,ig_promote_enter_error_screens_universe,ig_stories_allow_camera_actions_while_recording,ig_android_analytics_mark_events_as_offscreen,ig_shopping_checkout_mvp_experiment,ig_android_video_fit_scale_type_igtv,ig_android_direct_pending_media,ig_android_scroll_main_feed,instagram_pcp_activity_feed_following_tab_universe,ig_android_optic_feature_testing,ig_android_igtv_player_follow_button,ig_android_intialization_chunk_410,ig_android_vc_start_call_minimized_universe,ig_android_recognition_tracking_thread_prority_universe,ig_android_stories_music_sticker_position,ig_android_optic_photo_cropping_fixes,ig_camera_regiontracking_use_similarity_tracker_for_scaling,ig_android_interactions_media_breadcrumb,ig_android_vc_cowatch_config_universe,ig_android_nametag_save_experiment_universe,ig_android_refreshable_list_view_check_spring,ig_android_biz_endpoint_switch,ig_android_direct_continuous_capture,ig_android_comments_direct_reply_to_author,ig_android_profile_visits_in_bio,ig_android_fs_new_gallery,ig_android_remove_follow_all_fb_list,ig_android_vc_webrtc_params,ig_android_specific_story_sharing,ig_android_claim_or_connect_page_on_xpost,ig_android_anr,ig_android_story_viewpoint_impression_event_universe,ig_android_image_exif_metadata_ar_effect_id_universe,ig_android_optic_new_architecture,ig_android_stories_viewer_as_modal_high_end_launch,ig_android_local_info_page,ig_new_eof_demarcator_universe';
    const LOGIN_EXPERIMENTS = 'ig_android_fci_onboarding_friend_search,ig_android_device_detection_info_upload,ig_android_account_linking_upsell_universe,ig_android_direct_main_tab_universe_v2,ig_android_sms_retriever_backtest_universe,ig_android_direct_add_direct_to_android_native_photo_share_sheet,ig_growth_android_profile_pic_prefill_with_fb_pic_2,ig_account_identity_logged_out_signals_global_holdout_universe,ig_android_login_identifier_fuzzy_match,ig_android_video_render_codec_low_memory_gc,ig_android_custom_transitions_universe,ig_android_push_fcm,ig_android_show_login_info_reminder_universe,ig_android_email_fuzzy_matching_universe,ig_android_one_tap_aymh_redesign_universe,ig_android_direct_send_like_from_notification,ig_android_suma_landing_page,ig_android_session_scoped_logger,ig_android_user_session_scoped_class_opt_universe,ig_android_accoun_switch_badge_fix_universe,ig_android_smartlock_hints_universe,ig_android_black_out,ig_activation_global_discretionary_sms_holdout,ig_android_account_switch_infra_universe,ig_android_video_ffmpegutil_pts_fix,ig_android_multi_tap_login_new,ig_android_caption_typeahead_fix_on_o_universe,ig_android_save_pwd_checkbox_reg_universe,ig_android_nux_add_email_device,ig_android_direct_remove_view_mode_stickiness_universe,ig_username_suggestions_on_username_taken,ig_android_ingestion_video_support_hevc_decoding,ig_android_secondary_account_creation_universe,ig_android_account_recovery_auto_login,ig_android_sim_info_upload,ig_android_mobile_http_flow_device_universe,ig_android_hide_fb_button_when_not_installed_universe,ig_android_targeted_one_tap_upsell_universe,ig_android_gmail_oauth_in_reg,ig_android_account_linking_flow_shorten_universe,ig_android_hide_typeahead_for_logged_users,ig_android_vc_interop_use_test_igid_universe,ig_android_log_suggested_users_cache_on_error,ig_android_reg_modularization_universe,ig_android_phone_edit_distance_universe,ig_android_device_verification_separate_endpoint,ig_android_universe_noticiation_channels,ig_smartlock_login,ig_android_igexecutor_sync_optimization_universe,ig_android_account_linking_skip_value_props_universe,ig_android_account_linking_universe,ig_android_hsite_prefill_new_carrier,ig_android_retry_create_account_universe,ig_android_family_apps_user_values_provider_universe,ig_android_reg_nux_headers_cleanup_universe,ig_android_device_info_foreground_reporting,ig_android_shortcuts_2019,ig_android_device_verification_fb_signup,ig_android_onetaplogin_optimization,ig_video_debug_overlay,ig_android_ask_for_permissions_on_reg,ig_assisted_login_universe,ig_android_display_full_country_name_in_reg_universe,ig_android_security_intent_switchoff,ig_android_device_info_job_based_reporting,ig_android_passwordless_auth,ig_android_direct_main_tab_account_switch,ig_android_modularized_dynamic_nux_universe,ig_android_fb_account_linking_sampling_freq_universe,ig_android_fix_sms_read_lollipop,ig_android_access_flow_prefill';
    const LAUNCHER_CONFIGS = 'ig_android_media_codec_info_collection,stories_gif_sticker,ig_android_felix_release_players,bloks_binding,ig_android_camera_network_activity_logger,ig_android_os_version_blocking_config,ig_android_carrier_signals_killswitch,live_special_codec_size_list,fbns,ig_android_aed,ig_client_config_server_side_retrieval,ig_android_bloks_perf_logging,ig_user_session_operation,ig_user_mismatch_soft_error,ig_android_prerelease_event_counter,fizz_ig_android,ig_android_vc_clear_task_flag_killswitch,ig_android_killswitch_perm_direct_ssim,ig_android_codec_high_profile,ig_android_smart_prefill_killswitch,sonar_prober,action_bar_layout_width,ig_auth_headers_device,always_use_server_recents';
    const LAUNCHER_LOGIN_CONFIGS = 'ig_camera_ard_use_ig_downloader,ig_android_dogfooding,ig_android_bloks_data_release,ig_donation_sticker_public_thanks,ig_business_profile_donate_cta_android,ig_launcher_ig_android_network_dispatcher_priority_decider_qe2,ig_multi_decode_config,ig_android_improve_segmentation_hint,ig_android_memory_manager_holdout,ig_android_interactions_direct_sharing_comment_launcher,ig_launcher_ig_android_analytics_request_cap_qe,ig_direct_e2e_send_waterfall_sample_rate_config,ig_android_cdn_image_sizes_config,ig_android_critical_path_manager,ig_android_mobileboost_camera,ig_android_pdp_default_sections,ig_android_video_playback,ig_launcher_explore_sfplt_secondary_response_android,ig_android_upload_heap_on_oom,ig_synchronous_account_switch,ig_android_direct_presence_digest_improvements,ig_android_request_compression_launcher,ig_android_feed_attach_report_logs,ig_android_insights_welcome_dialog_tooltip,ig_android_qp_surveys_v1,ig_direct_requests_approval_config,ig_android_react_native_ota_kill_switch,ig_android_video_profiler_loom_traces,video_call_gk,ig_launcher_ig_android_network_stack_cap_video_request_qe,ig_shopping_android_business_new_tagging_flow,ig_android_igtv_bitrate,ig_android_geo_gating,ig_android_explore_startup_prefetch,ig_android_camera_asset_blocker_config,post_user_cache_user_based,ig_android_branded_content_story_partner_promote_rollout,ig_android_quic,ig_android_videolite_uploader,ig_direct_message_type_reporting_config,ig_camera_android_whitelist_all_effects_in_pre,ig_android_shopping_influencer_creator_nux,ig_android_mobileboost_blacklist,ig_android_direct_gifs_killswitch,ig_android_global_scheduler_direct,ig_android_image_display_logging,ig_android_global_scheduler_infra,ig_igtv_branded_content_killswitch,ig_cg_donor_duplicate_sticker,ig_launcher_explore_verified_badge_on_ads,ig_android_cold_start_class_preloading,ig_camera_android_attributed_effects_endpoint_api_query_config,ig_android_highlighted_products_business_option,ig_direct_join_chat_sticker,ig_android_direct_admin_tools_requests,ig_android_rage_shake_whitelist,ig_android_shopping_ads_cta_rollout,ig_android_igtv_segmentation,ig_launcher_force_switch_on_dialog,ig_android_iab_fullscreen_experience_config,ig_android_instacrash,ig_android_specific_story_url_handling_killswitch,ig_mobile_consent_settings_killswitch,ig_android_influencer_monetization_hub_launcher,ig_and roid_scroll_perf_mobile_boost_launcher,ig_android_cx_stories_about_you,ig_android_replay_safe,ig_android_stories_scroll_perf_misc_fixes_h2_2019,ig_android_shopping_django_product_search,ig_direct_giphy_gifs_rating,ig_android_ppr_url_logging_config,ig_canvas_ad_pixel,ig_strongly_referenced_mediacache,ig_android_direct_show_threads_status_in_direct,ig_camera_ard_brotli_model_compression,ig_image_pipeline_skip_disk_config,ig_android_explore_grid_viewpoint,ig_android_iab_persistent_process,ig_android_in_process_iab,ig_android_launcher_value_consistency_checker,ig_launcher_ig_explore_peek_and_sfplt_android,ig_android_skip_photo_finish,ig_biz_android_use_professional_account_term,ig_android_settings_search,ig_android_direct_presence_media_viewer,ig_launcher_explore_navigation_redesign_android,ig_launcher_ig_android_network_stack_cap_api_request_qe,ig_qe_value_consistency_checker,ig_stories_fundraiser_view_payment_address,ig_business_create_donation_android,ig_android_qp_waterfall_logging,ig_android_bloks_demos,ig_redex_dynamic_analysis,ig_android_bug_report_screen_record,ig_shopping_android_carousel_product_ids_fix_killswitch,ig_shopping_android_creators_new_tagging_flow,ig_android_direct_threads_app_dogfooding_flags,ig_shopping_camera_android,ig_android_qp_keep_promotion_during_cooldown,ig_android_qp_slot_cooldown_enabled_universe,ig_android_request_cap_tuning_with_bandwidth,ig_android_client_config_realtime_subscription,ig_launcher_ig_android_network_request_cap_tuning_qe,ig_android_concurrent_coldstart,ig_android_gps_improvements_launcher,ig_android_notification_setting_sync,ig_android_stories_canvas_mode_colour_wheel,ig_android_iab_session_logging_config,ig_android_network_trace_migration,ig_android_extra_native_debugging_info,ig_android_insights_top_account_dialog_tooltip,ig_launcher_ig_android_dispatcher_viewpoint_onscreen_updater_qe,ig_android_disable_browser_multiple_windows,ig_contact_invites_netego_killswitch,ig_android_update_items_header_height_launcher,ig_android_bulk_tag_untag_killswitch,ig_android_employee_options,ig_launcher_ig_android_video_pending_request_store_qe,ig_story_insights_entry,ig_android_creator_multi_select,ig_android_direct_new_media_viewer,ig_android_gps_profile_launcher,ig_android_direct_real_names_launcher,ig_fev_info_launcher,ig_android_remove_request_params_in_network_trace,ig_android_rageshake_redesign,ig_launcher_ig_android_network_stack_queue_undefined_request_qe,ig_cx_promotion_tooltip,ig_text_response_bottom_sheet,ig_android_carrier_signal_timestamp_max_age,ig_android_qp_xshare_to_fb,ig_android_rollout_gating_payment_settings,ig_android_mobile_boost_kill_switch,ig_android_betamap_cold_start,ig_android_media_store,ig_android_async_view_model_launcher,ig_android_newsfeed_recyclerview,ig_android_feed_optimistic_upload,ig_android_fix_render_backtrack_reporting,ig_delink_lasso_accounts,ig_android_feed_report_ranking_issue,ig_android_shopping_insights_events_validator,ig_biz_android_new_logging_architecture,ig_launcher_ig_android_reactnative_realtime_ota,ig_android_boomerang_crash_android_go,ig_android_shopping_influencer_product_sticker_editing,ig_camera_android_max_vertex_texture_launcher,bloks_suggested_hashtag';
    const SIG_KEY_VERSION = '4';

    // Endpoint Constants.
    const BLOCK_VERSIONING_ID = 'a4b4b8345a67599efe117ad96b8a9cb357bb51ac3ee00c3a48be37ce10f2bb4c'; // getTimelineFeed()
    const BATCH_SURFACES = [
        ['4715', 'instagram_feed_header'],
        ['5734', 'instagram_feed_prompt'],
        ['5858', 'instagram_feed_tool_tip'],
    ];
    const BATCH_QUERY = 'viewer() {eligible_promotions.trigger_context_v2(<trigger_context_v2>).ig_parameters(<ig_parameters>).trigger_name(<trigger_name>).surface_nux_id(<surface>).external_gating_permitted_qps(<external_gating_permitted_qps>).supports_client_filters(true).include_holdouts(true) {edges {client_ttl_seconds,log_eligibility_waterfall,is_holdout,priority,time_range {start,end},node {id,promotion_id,logging_data,max_impressions,triggers,contextual_filters {clause_type,filters {filter_type,unknown_action,value {name,required,bool_value,int_value,string_value},extra_datas {name,required,bool_value,int_value,string_value}},clauses {clause_type,filters {filter_type,unknown_action,value {name,required,bool_value,int_value,string_value},extra_datas {name,required,bool_value,int_value,string_value}},clauses {clause_type,filters {filter_type,unknown_action,value {name,required,bool_value,int_value,string_value},extra_datas {name,required,bool_value,int_value,string_value}},clauses {clause_type,filters {filter_type,unknown_action,value {name,required,bool_value,int_value,string_value},extra_datas {name,required,bool_value,int_value,string_value}}}}}},is_uncancelable,template {name,parameters {name,required,bool_value,string_value,color_value,}},creatives {title {text},content {text},footer {text},social_context {text},social_context_images,primary_action{title {text},url,limit,dismiss_promotion},secondary_action{title {text},url,limit,dismiss_promotion},dismiss_action{title {text},url,limit,dismiss_promotion},image.scale(<scale>) {uri,width,height}}}}}}';
    const BATCH_SCALE = 3;
    const BATCH_VERSION = 1;

    // User-Agent Constants.
    const USER_AGENT_LOCALE = 'en_US'; // "language_COUNTRY".

    // HTTP Protocol Constants.
    const ACCEPT_LANGUAGE = 'en-US'; // "language-COUNTRY".
    const ACCEPT_ENCODING = 'gzip,deflate';
    const CONTENT_TYPE = 'application/x-www-form-urlencoded; charset=UTF-8';
    const X_FB_HTTP_Engine = 'Liger';
    const X_IG_Connection_Type = 'WIFI';
    const X_IG_Capabilities = '3brTvw==';

    // Supported Capabilities
    const SUPPORTED_CAPABILITIES = [
        [
            'name'    => 'SUPPORTED_SDK_VERSIONS',
            'value'   => '13.0,14.0,15.0,16.0,17.0,18.0,19.0,20.0,21.0,22.0,23.0,24.0,25.0,26.0,27.0,28.0,29.0,30.0,31.0,32.0,33.0,34.0,35.0,36.0,37.0,38.0,39.0,40.0,41.0,42.0,43.0,44.0,45.0,46.0,47.0,48.0,49.0,50.0,51.0,52.0,53.0,54.0,55.0,56.0,57.0,58.0,59.0,60.0,61.0,62.0,63.0,64.0,65.0,66.0,67.0,68.0,69.0',
        ],
        [
            'name'  => 'FACE_TRACKER_VERSION',
            'value' => '12',
        ],
        [
            'name'  => 'segmentation',
            'value' => 'segmentation_enabled',
        ],
        [
            'name'  => 'COMPRESSION',
            'value' => 'ETC2_COMPRESSION',
        ],
        [
            'name'  => 'world_tracker',
            'value' => 'world_tracker_enabled',
        ],
        [
            'name'  => 'gyroscope',
            'value' => 'gyroscope_enabled',
        ],
    ];

    // Supported Capabilities
    const SUPPORTED_CAPABILITIES_NEW = [
        [
            'name'    => 'SUPPORTED_SDK_VERSIONS',
            'value'   => '105.0,106.0,107.0,108.0,109.0,110.0,111.0,112.0,113.0,114.0,115.0,116.0,117.0,118.0,119.0,120.0,121.0,122.0,123.0,124.0',
        ],
        [
            'name'  => 'FACE_TRACKER_VERSION',
            'value' => '14',
        ],
        [
            'name'  => 'segmentation',
            'value' => 'segmentation_enabled',
        ],
        [
            'name'  => 'COMPRESSION',
            'value' => 'ETC2_COMPRESSION',
        ],
        [
            'name'  => 'world_tracker',
            'value' => 'world_tracker_enabled',
        ],
        [
            'name'  => 'gyroscope',
            'value' => 'gyroscope_enabled',
        ],
    ];

    // Facebook Constants.
    const FACEBOOK_OTA_FIELDS = 'update%7Bdownload_uri%2Cdownload_uri_delta_base%2Cversion_code_delta_base%2Cdownload_uri_delta%2Cfallback_to_full_update%2Cfile_size_delta%2Cversion_code%2Cpublished_date%2Cfile_size%2Cota_bundle_type%2Cresources_checksum%2Callowed_networks%2Crelease_id%7D';
    const FACEBOOK_ORCA_PROTOCOL_VERSION = 20150314;
    const FACEBOOK_ORCA_APPLICATION_ID = '124024574287414';
    const FACEBOOK_ANALYTICS_APPLICATION_ID = '567067343352427';

    // MQTT Constants.
    const PLATFORM = 'android';
    const FBNS_APPLICATION_NAME = 'MQTT';
    const INSTAGRAM_APPLICATION_NAME = 'Instagram';
    const PACKAGE_NAME = 'com.instagram.android';

    // Instagram Quick Promotions.
    const SURFACE_PARAM = [
        4715,
        5734,
    ];

    // Internal Feedtype Constants. CRITICAL: EVERY value here MUST be unique!
    const FEED_TIMELINE = 1;
    const FEED_TIMELINE_ALBUM = 2;
    const FEED_STORY = 3;
    const FEED_DIRECT = 4;
    const FEED_DIRECT_STORY = 5;
    const FEED_TV = 6;
    const FEED_REEL = 991;

    // General Constants.
    const SRC_DIR = __DIR__; // Absolute path to the "src" folder.

    // Story view modes.
    const STORY_VIEW_MODE_ONCE = 'once';
    const STORY_VIEW_MODE_REPLAYABLE = 'replayable';
}
