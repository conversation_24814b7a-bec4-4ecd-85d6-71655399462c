<?php

namespace App\Notifications;

use App\Team;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class TeamInvite extends Notification implements ShouldQueue
{
    use Queueable;

    /** @var Team $team */
    private $team;
    /** @var User $teamAdmin */
    private $teamAdmin;

    /**
     * Create a new notification instance.
     *
     * @param Team $team
     * @param null $teamAdmin
     */
    public function __construct($team, $teamAdmin = null)
    {
        $this->team = $team;

        if(!$teamAdmin)
            $teamAdmin = $team->user;

        $this->teamAdmin = $teamAdmin;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->greeting('Hey ' . explode(' ', ($notifiable->name . ''))[0])
            ->subject('Invitation to join: ' . e($this->team->name))
            ->line(e($this->teamAdmin->name) . ' invited you to the team: ' . e($this->team->name))
            ->action('Respond', url("app/teams"))
            ->line('After accepting the invite, you can have access to the team resources that are shared with you.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'team_id' => $this->team->id,
            'team_name' => $this->team->name,
            'invited_by_id' => $this->teamAdmin->id,
            'invited_by_name' => $this->teamAdmin->name,
        ];
    }
}
