<?php

namespace App\Respond;

use App\Account;
use App\InboxConversation;
use App\InboxConversationItem;
use Illuminate\Support\Str;

class InboxManager
{
    protected $account = null;

    public function __construct(Account $account)
    {
        $this->account = $account;
    }

    /**
     * @throws \Exception
     */
    public function execute($webhookData = null){

        if(!$this->account->getOption('inbox_config.active')){
            // inbox is not active for this account

            // update inbox_fetched_at
            $this->account->update([
                'inbox_fetched_at' => now()->addMinutes(60 * 24 * 365),
            ]);

            return;
        }

        // if we don't have any data in our db, then we should backfill
        $shouldBackFill = !$this->account->getOption('inbox.backfilled') && !InboxConversation::whereAccountId($this->account->id)->exists();

        // if we have webhook data, then we need to process it
        // call dynamic class based on account->type
        $class = 'App\Respond\Handlers\\' . Str::studly(str_replace('.', '_', $this->account->type));

        if(!class_exists($class)){
            // if we don't have a handler for this account type, then we should not try to poll again
            $this->account->update([
                'inbox_fetched_at' => now()->addMinutes(60 * 24 * 2), // 2 days at-least
            ]);

            return;
        }

        /** @var BaseInboxHandler $handler */
        $handler = new $class($this->account);

        // check if the class exists
        if ($webhookData) {
            $handler->handle($webhookData);
        }


        if($shouldBackFill){
            try {
                // back-fill if needed
                $handler->backFill();
                // update inbox.backfilled to true
                $this->account->setOption('inbox.backfilled', true);
            } catch (\Exception $e) {
                \Log::error('Error while backfilling inbox for account ' . $this->account->id . ': ' . $e->getMessage());
            }
        }

        $gotDataFromPolling = false;

        $pollAnswer = $handler->shouldPoll();

        $shouldPoll = false;

        if(is_string($pollAnswer)) {
            if ($pollAnswer === 'yes') {
                $shouldPoll = true;
            } else if($pollAnswer === 'never'){
                $this->account->update([
                    'inbox_fetched_at' => now()->addMinutes(60 * 24 * 7), // 1 week at-least
                ]);

                // no need for polling
                return;
            }
        } else {
            $shouldPoll = !!$pollAnswer;
        }

        if($shouldPoll){
            $gotDataFromPolling = $handler->poll();
        }


        if($gotDataFromPolling){

            if($this->account->getOption('inbox_polling_attempts')){
                // reset
                $this->account->removeOption('inbox_polling_attempts');
            }

            // update inbox_fetched_at to now
            $this->account->update([
                'inbox_fetched_at' => now()
            ]);
        } else {
            $attempts = $this->account->getOption('inbox_polling_attempts', 1);

            // increment attempts
            $this->account->setOption('inbox_polling_attempts', $attempts + 1);

            $delayMinutes = $attempts * 5;

            if(!$shouldBackFill && !$shouldPoll){
                // if we are not backfilling, and we are not polling, then we should not try again anytime sooner
                $delayMinutes = $delayMinutes * 10;
            }

            if ($delayMinutes > 60 * 6) {
                // max delay so that we don't forget to poll at all for accounts that are not getting much data
                $delayMinutes = 120;
            }

            // we didn't get any data from polling; next time, we should poll after attempts * 5 minutes
            $this->account->update([
                'inbox_fetched_at' => now()->addMinutes($delayMinutes),
            ]);
        }

        // if we are here, it means that we don't have a polling class for this account type,
        // so we should not try to poll again
        $this->account->update([
            'inbox_fetched_at' => now()->addMinutes(60 * 24 * 2), // 2 days at-least
        ]);
    }

    /**
     * @param $conversationOrConversationItem InboxConversation|InboxConversationItem
     * @return BaseObject
     * @throws \Exception
     */
    public function getApiObject($conversationOrConversationItem): BaseObject
    {
        if (!$conversationOrConversationItem) {
            throw new \InvalidArgumentException('Argument cannot be null');
        }

        $account = $this->account;

        if($conversationOrConversationItem instanceof \App\InboxConversation) {

            $conversation = $conversationOrConversationItem;

            // make class name
            // example: App\Objects\FacebookPageChat
            $class = 'App\Objects\\' . Str::studly(str_replace('.', '_', $account->type . '.' . $conversation->type));

            if (!class_exists($class)) {
                throw new \Exception('Class does not exist: ' . $class);
            }

            return new $class($conversation, $account);


        } elseif ($conversationOrConversationItem instanceof \App\InboxConversationItem) {

            $conversationItem = $conversationOrConversationItem;

            $conversation = $conversationItem->conversation;

            // make class name
            // example: App\Objects\FacebookPageChat\Message
            $class = 'App\Objects\\' . Str::studly(str_replace('.', '_', $account->type . '.' . $conversation->type)) . '\\' . Str::studly(str_replace('.', '_', $conversationItem->type));

            if (!class_exists($class)) {
                throw new \Exception('Class does not exist: ' . $class);
            }

            return new $class($conversationItem, $account);
        }

        throw new \Exception('Invalid argument type: ' . get_class($conversationOrConversationItem));
    }
}
