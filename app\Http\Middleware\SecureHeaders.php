<?php

namespace App\Http\Middleware;

use Closure;

class SecureHeaders
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        /** @var \Symfony\Component\HttpFoundation\Response $response */
        $response = $next($request);
        $request->setTrustedHosts([
            '^(.+\.)?socialbu\.com$',
            'glxsftsocial.serveo.net',
            '^(.+\.)?socialbuapp\.com$',
        ]);
        $response->headers->add([
            'x-content-type-options' => 'nosniff',
            'x-frame-options' => 'sameorigin',
            'x-xss-protection' => '1; mode=block',
            'Strict-Transport-Security' => 'max-age=31536000; includeSubdomains',
            'referrer-policy' => 'strict-origin-when-cross-origin',
        ]);
        return $response;
    }
}
