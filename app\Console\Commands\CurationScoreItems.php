<?php

namespace App\Console\Commands;

use App\CurationItem;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class CurationScoreItems extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'curation:score_items';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update score of curation items';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $scoredItems = 0;

        $startTime = now();

        // we only score items that have scored_at set to null or are older than x days from last scoring, and
        // we only score items that are newer than 30 days
        CurationItem::where('published_at', '>', now()->subDays(30))

            ->where(function (/** @var Builder $query */ $query) {
                $query->whereNull('scored_at')
                    ->orWhere('scored_at', '<', now()->subDays(7));
            })

            ->orderBy('scored_at', 'asc')
            ->chunk(100, function ($items) use(&$scoredItems, &$startTime) {

                /** @var CurationItem[]|Collection $items */
                foreach ($items as $item) {
                    $this->info('Scoring item: ' . $item->link);
                    try {
                        $item->updateScore();
                        $scoredItems++;
                    } catch (\Exception $e) {
                        $this->error('Error scoring item: ' . $item->link);
                        $this->error($e->getMessage());
                    }
                }

                // stop if 100 items are done or if the time limit (5 minutes) is reached
                if ($scoredItems >= 100 || now()->diffInSeconds($startTime) > 300) {
                    return false;
                }

                return true;
        });
    }
}
