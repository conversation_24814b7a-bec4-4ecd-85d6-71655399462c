@warn "This file is not used anymore.";

$app-sidebar-width: 200px;

$app-footer-height: 100px;

html {
    position: relative;
    min-height: 100%;
}

/* vuejs */
[v-cloak] {
    display: none;
}

/*
 * Navigation
 */
#navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    .navbar-brand {
        padding-top: 0.75rem;
        padding-bottom: 0.75rem;
        font-size: 1rem;
        //background-color: rgba(0, 0, 0, .25);
        //box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25);
        #sidebar_toggle {
            font-size: 22px;
            border-radius: 6px;
        }
    }

    .form-control {
        padding: 0.75rem 1rem;
        border-width: 0;
        border-radius: 0;
    }
    .hover-nav-menu {
        &:hover, &:active, &:focus {
            background-color: $color-secondary !important;
            //color: $socialbu-color !important;
        }
    }
}

.hover-bg {
    &:hover, &:active, &:focus {
        background-color: $gray-100 !important;
        //color: $socialbu-color !important;
    }
}
.hover-show {
    .show-on-hover {
        display: none;
    }
    &:hover, &:active, &:focus {
        .show-on-hover {
            display: block;
        }
    }
}

.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100; /* Behind the navbar */
    padding: 48px 0 0; /* Height of navbar */

    min-width: $app-sidebar-width;
    overflow-x: hidden;
    overflow-y: auto; /* Scrollable contents if viewport is shorter than content. */

    .sidebar-sticky {
        position: relative;
        top: 0;
        height: calc(100vh - 107px);
        padding-top: 1.5rem;
        overflow-x: hidden;
        overflow-y: auto; /* Scrollable contents if viewport is shorter than content. */
    }

    @supports ((position: -webkit-sticky) or (position: sticky)) {
        .sidebar-sticky {
            position: -webkit-sticky;
            position: sticky;
        }
    }
}
#sidebar-menu,
#sidebar-menu-mobile {
    .nav-link {
        padding: 0.5rem 1rem;
        font-weight: 500;
        color: #333;
        &:hover,
        &.active {
            color: $color-primary;
            .fa, .mdi {
                color: inherit;
            }
        }
        .fa, .mdi {
            margin-right: 4px;
            color: #999;
        }
    }
}

/* sticky footer */
.footer {
    position: absolute;
    bottom: 0;
    z-index: 102;
    width: 100%;
    // height: $app-footer-width; /* Set the fixed height of the footer here */
}

/*
 * Content
 */

[role="main"] {
    padding-top: 74px; /* Space for fixed navbar */
    margin-left: $app-sidebar-width;
    margin-bottom: $app-footer-height + 40px; /* Margin bottom by footer height + margin-bottom normal */
    // margin-bottom: 40px; /* Margin bottom by footer height + margin-bottom normal */
    //min-height: 1024px; // so it looks good with footer
    &.has-sidebar-toggle {
        margin-left: 0;
    }
}

/* social icons */
.social-icon {
    color: #808080;
    &.facebook {
        &:hover,
        &:focus,
        &.active {
            color: #4267b2;
        }
    }
    &.linkedin {
        &:hover,
        &:focus,
        &.active {
            color: #4875b4;
        }
    }
    &.twitter {
        &:hover,
        &:focus,
        &.active {
            color: #3097d1;
        }
    }
    &.google {
        &:hover,
        &:focus,
        &.active {
            color: #d62d20;
        }
    }
    &.google-business-profile {
        &:hover,
        &:focus,
        &.active {
            color: #4285f4;
        }
    }
    &.instagram {
        &:hover,
        &:focus,
        &.active {
            color: #517fa4;
        }
    }
    &.mastodon {
        &:hover,
        &:focus,
        &.active {
            color: #6364ff;
        }
    }
    &.tiktok {
        &:hover,
        &:focus,
        &.active {
            color: #000;
        }
    }
    &.pinterest {
        &:hover,
        &:focus,
        &.active {
            color: #F0002A;
        }
    }
    &.youtube {
        &:hover,
        &:focus,
        &.active {
            color: #ff0000;
        }
    }
    &.reddit {
        &:hover,
        &:focus,
        &.active {
            color: #ff4500;
        }
    }
}

.social-bg {
    &.facebook {
        background-color: #4267b2;
    }
    &.linkedin {
        background-color: #4875b4;
    }
    &.twitter {
        background-color: #3097d1;
    }
    &.google {
        background-color: #d62d20;
    }
    &.instagram {
        background-color: #517fa4;
    }
    &.google-business-profile {
        background-color: #4285f4;
    }
    &.mastodon {
        background-color: #6364ff;
    }
    &.tiktok {
        background-color: #FE2C55;
    }
    &.pinterest {
        background-color: #FE2C55;
    }
    &.youtube {
        background-color: #ff0000;
    }
    &.reddit {
        background-color: #ff4500;
    }
}

/* loader (spinner animation) - not used anywhere - use font awesome spinner instead */
.loader {
    border: 16px solid #f3f3f3; /* Light grey */
    border-top: 16px solid #6699fd; /* SB color */
    border-radius: 50%;
    width: 120px;
    height: 120px;
    animation: spin 2s linear infinite;

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }
        100% {
            transform: rotate(360deg);
        }
    }
}

/* some general classes */
.overflow-hidden {
    overflow: hidden;
}

.no-margin {
    margin: 0 !important;
}

.no-padding {
    padding: 0 !important;
}

.padding-5 {
    padding: 5px;
}

.padding-15 {
    padding: 5px;
}

// select2 custom
.select2-container--bootstrap .select2-selection {
    border-color: #eee;
}

.select2-container--bootstrap .select2-selection {
    box-shadow: none !important;
}

.input-seamless {
    border: none !important;
    background-color: transparent !important;
    box-shadow: none !important;
}

.input-seamless.h3 {
    font-size: 24px;
}

// btn-group with btn-block children
.btn-group-justified {
    display: flex;
    .btn {
        flex-grow: 1;
    }
}

// popover increase width
.popover {
    max-width: 600px;
}

// cursor pointer
.cursor-pointer {
    cursor: pointer;
}

.bg-secondary-original {
    background-color: rgb(108, 117, 125);
}

.ajs-right {
    right: 70px !important; // alertify fix because we also have chat widget on bottom right
}

.tooltip {
    &.bs-tooltip-top {
        .arrow::before {
            border-top-color: rgba(0, 0, 0, 0.8) !important;
        }
    }
    &.bs-tooltip-bottom {
        .arrow::before {
            border-bottom-color: rgba(0, 0, 0, 0.8) !important;
        }
    }
    &.bs-tooltip-left {
        .arrow::before {
            border-left-color: rgba(0, 0, 0, 0.8) !important;
        }
    }
    &.bs-tooltip-right {
        .arrow::before {
            border-right-color: rgba(0, 0, 0, 0.8) !important;
        }
    }
    .tooltip-inner {
        padding: 0.2rem 0.4rem;
        background: rgba(0, 0, 0, 0.8);
        border-radius: 0;
    }
    font-size: 85% !important;
}

.account {
    position: relative;
    i.network-type {
        position: absolute;
        top: 30px;
        left: 30px;
        &.mdi {
            top: 24px;
        }
    }
}

// accounts selector: editor and story publisher, and somewhere else (accounts-container)
.accounts-selector, .accounts-container {
    min-height: 66px;
    &.invisible {
        visibility: hidden;
    }

    .add-account {
        color: #eeeeee;
    }

    .add-account:hover {
        color: #636b6f;
    }

    .account-pic {
        min-height: 48px;
        min-width: 48px;
    }
    .account-pic img {
        border-radius: 50%;
        height: 36px;
        width: 36px;
        cursor: pointer;
    }
    .account-pic i.network-type {
        position: absolute;
        top: 30px;
        left: 30px;
        &.mdi {
            top: 24px;
        }
    }

    .account-pic input[type="checkbox"] {
        display: none;
    }

    .account-pic label {
        display: block;
        position: relative;
        cursor: pointer;
    }

    .account-pic label:before {
        display: block;
        position: absolute;
        top: 25px;
        left: 30px;
        font-size: 8px;
        transition-duration: 0.4s;
        transform: scale(0);
    }

    .account-pic label img {
        transition-duration: 0.2s;
        transform-origin: 50% 50%;
    }

    .account-pic label {
        filter: grayscale(100%) opacity(40%);
    }
    .account-pic label:hover {
        filter: grayscale(0%) opacity(70%);
    }
    .account-pic :checked + label {
        filter: grayscale(0%) opacity(100%);
    }
}

.upload-wrapper {
    border-style: dashed !important;
    border-width: 3px !important;
    i.fa {
        opacity: 0.6;
    }
    &:hover {
        border-color: $color-secondary !important;
        i.fa {
            opacity: 1;
        }
    }
}

.custom-control-input:checked ~ .custom-control-label::before {
    border-color: $color-secondary;
}

.bootstrap-datetimepicker-widget.dropdown-menu {
    width: auto !important; // fix width, dont know why it needs fixing
}

// modal slide out from right or left
.modal {
    // bootstrap adds unexpected padding some times, on right
    padding-left: 0 !important;
    padding-right: 0 !important;
    .modal-dialog.modal-dialog-slideout {
        min-height: 100%;
        margin: 0 0 0 auto;
        transform: translate(100%, 0) !important; // slide out from right
        &.left {
            margin: 0 auto 0 0;
            transform: translate(-100%, 0) !important; // slides out from left
        }
        &.bottom {
            margin: 0 0 auto 0;
            transform: translate(0, 100%) !important; // slides out from bottom
            width: 100%;
            max-width: 100%;
        }
        &.full {
            max-width: 100%;
            width: 100%;
        }
        .modal-content {
            border: 0;
            border-radius: 0;
            height: 100%;
        }
    }

    &.show {
        .modal-dialog.modal-dialog-slideout {
            transform: translate(0, 0) !important;
            display: flex;
            align-items: stretch;
            height: 100%;
            .modal-body {
                overflow-y: auto;
                overflow-x: hidden;
            }
        }
    }
}
.dashboard-card {
    box-shadow: none;
}
.dashboard-card:hover {
    box-shadow: 0 0 25px rgba(0, 0, 0, 0.07);
}

.position-sticky-top-fix {
    top: 55px; // header height
}

// prosemirror editor
@import "~prosemirror-view/style/prosemirror.css";
.ProseMirror .placeholder {
    color: #aaa;
    pointer-events: none;
    height: 0;
}
.ProseMirror p{
    margin-bottom: 0px !important;
}

.ProseMirror:focus .placeholder {
    display: none;
}

.ProseMirror[contenteditable] {
    outline: 0 solid transparent;
    height: 100%;
}


.hover-zoom {
    transition: transform 0.2s; /* Animation */
    &:hover {
        transform: scale(1.2); /* (150% zoom - Note: if the zoom is too large, it will go outside of the viewport) */
    }
}

/* simple icons middle position fix */
.si {
    vertical-align: middle;
}
.referral-content-box{
    width: 46px; 
    height:46px;
    background:#323D47;
    border-radius: 100%;
    border: 1px solid #fff;
    border-width: 0;
    .referral-icon{
        top: 8px;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 20px;
        line-height: 1.35;
        color: #fff;
    }
}
.referral_card_bg{
    background: #FAFBFF;
}
