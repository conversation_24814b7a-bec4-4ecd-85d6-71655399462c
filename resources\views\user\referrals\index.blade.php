@extends('layout.user')
@section('title', ($user->getOption('is_affiliate') ? 'Affiliates' : 'Referrals') . ' | ' . config('app.name'))
@section('content')
    
    <div class="row">
        <div class="col-12">
            <h3>Get More, Together</h3>
            <p>Share with your friends, family members, or co-workers and get even more out of SocialBu.</p>
        </div>
        <div class="col-12">
            <h5>Referrals Growth</h5>
            <div class="row">
                <div class="col pr-2">
                    <div class="card border">
                        <div class="card-body p-20">
                            <h4 class="mb-1">
                                {{ $customersCount }}
                            </h4>
                            <p class="mb-0" title="Users who started trial or paid subscription.">Customers</p>
                        </div>
                    </div>
                </div>
                <div class="col px-2">
                    <div class="card border">
                        <div class="card-body p-20">
                            <h4 class="mb-1">
                                {{ $referralsCount }}
                            </h4>
                            <p class="mb-0">Sign Ups</p>
                        </div>
                    </div>
                </div>
                <div class="col px-2">
                    <div class="card border">
                        <div class="card-body p-20">
                            <h4 class="mb-1">
                                {{ $hits }}
                            </h4>
                            <p class="mb-0" title="Unique visitors">Visits</p>
                        </div>
                    </div>
                </div>
                <div class="col pl-2">
                    <div class="card border">
                        <div class="card-body p-20">
                            @if ($user->getOption('is_affiliate'))
                            <h4 class="mb-1">
                                ${{ $earnings }}
                            </h4>
                            <p class="mb-0" title="Total available earning. This is subject to change and only represents approximate value.">Earnings</p>
                            @else
                            <h4 class="mb-1">
                                {{ round($earnings) }}
                            </h4>
                            <p class="mb-0" title="Total available credits. This is subject to change and only represents approximate value.">Credits</p>
                            @endif
                        </div>
                    </div>
                </div>
                        
            </div>
            <div class="row pt-4">
                <div class="col-12">
                    {!! $chart->container() !!}
                </div>
            </div>
            @if($referralsCount > 0)
            <div class="row pt-4">
                <div class="col-12">
                    <h3>Last 10 Sign Ups</h3>
                    <div class="table-responsive table-border rounded-md">
                        <table class="table table-hover mb-0">
                            <thead class="card-header">
                                <tr>
                                    <th class="pl-4 py-2" scope="col">
                                        Date
                                    </th>
                                    <th class="pl-4 py-2" scope="col">
                                        User
                                    </th>
                                    <th class="pl-4 py-2" scope="col">
                                        Active Subscription (paid/trial)
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($user->referrals()->orderBy('id', 'DESC')->take(10)->get() as $user)
                                <tr>
                                    <td>
                                        {{ $user->created_at->format('d M, Y') }}
                                    </td>
                                    <td>
                                        {{ $email_hide($user->email) }}
                                    </td>
                                    <td>
                                        {{ $user->getPlan(true) !== 'free' ? 'Yes' : 'No' }}
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @endif
            <div class="row py-8">
                
                <div class="col-md-6">
                    <h6 class="font-weight-500 mb-1">Your Link</h6>
                    <input value="{{ $user->getReferralLink() }}" class="form-control w-100 text-dark readonly" readonly title="Click to select" onclick="this.select()"  data-toggle="tooltip" />
                </div>
                <div class="col-md-6">
                    <h6 class="font-weight-500 pb-4 pt-1">Share the link on your socials</h6>
                    <div class="mt-1">

                        <a target="_blank" class="text-secondary mr-5" href="http://www.facebook.com/sharer.php?u={{ urlencode($user->getReferralLink()) }}">
                            <img class="lozad" src="/images/redesign/networks/facebook-original.svg" > Facebook
                        </a>
                        <a target="_blank" class="text-secondary mr-5" href="http://twitter.com/share?text={{ urlencode('Try SocialBu, a social media management and automation platform.') }}&url={{ urlencode($user->getReferralLink()) }}&via=socialbuapp">
                            <img class="lozad" src="/images/redesign/networks/twitter-circular.svg" > Twitter
                        </a>
                        <a target="_blank" class="text-secondary mr-5" href="http://www.linkedin.com/shareArticle?mini=true&url={{ urlencode($user->getReferralLink()) }}&title={{ urlencode('SocialBu - Social Media Management and Automation') }}&summary={{ urlencode('Try SocialBu, a social media management and automation platform.') }}&source=socialbu.com">
                            <img class="lozad" src="/images/redesign/networks/linkedin-circular.svg" > LinkedIn
                        </a>
                        <a target="_blank" class="text-secondary" href="mailto:?subject={{ urlencode('Try SocialBu, a social media management and automation platform.') }}&body={{ urlencode($user->getReferralLink()) }}">
                            <i class="ph ph-envelope-simple"></i> Email
                        </a>
                    </div>
                </div>

            </div>
            <div class="row">
                <div class="col-md-12">
                    <h3>
                        How to refer friends to SocialBu and earn credits
                    </h3>
                    @if ($user->getOption('is_affiliate'))
                        <p>
                            Earn when a referred user signs up: 20% on every payment
                        </p>
                        <p>
                            To Redeem your earnings, please <a href="mailto:<EMAIL>" target="_blank">contact support</a>. 
                        </p>
                    @else
                        <p class="mb-4">
                            Earn credits when a referred user signs up: 50% of the first payment, 10% of the recurring payments
                        </p>
                    @endif
                </div>
            </div>
            @if (!$user->getOption('is_affiliate'))

            <div class="row">

                <div class="col-md-4 col-12">
                    <div class="card border">
                        <div class="card-body p-20">
                            <div class="position-relative referral-content-box referral-box mb-4">
                                <i class="ph ph-trophy ph-lg position-absolute"></i>
                            </div>
                            <h6 class="font-weight-500 mb-1">Earn 950 Credits</h6>
                            <p class="small-2 font-weight-400 mb-0">If your friend picks SocialBu Standard Plan, you get 950 credits for the first payment and 190 for every recurring payment.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-12">
                    <div class="card border">
                        <div class="card-body p-20">

                            <div class="position-relative referral-content-box referral-box mb-4">
                                <i class="ph ph-trophy ph-lg position-absolute"></i>
                            </div>
                            <h6 class="font-weight-500 mb-1">Earn 2950 Credits</h6>
                            <p class="small-2 font-weight-400 mb-0">If your friend picks SocialBu Super Plan, you get 2950 credits for the first payment and 295 for every recurring payment.</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-12">
                    <div class="card border">
                        <div class="card-body p-20">

                            <div class="position-relative referral-content-box referral-box mb-4">
                                <i class="ph ph-trophy ph-lg position-absolute"></i>
                            </div>
                            <h6 class="font-weight-500 mb-1">Earns 9950 Credits</h6>
                            <p class="small-2 font-weight-400 mb-0">If your friend picks SocialBu Supreme Plan, you get 9950 credits for the first payment and 995 for every recurring payment.</p>
                        </div>
                    </div>
                </div>
            </div>
            @endif
            @if (!$user->getOption('is_affiliate'))

            <div class="row pt-8">

                <div class="col-md-12">
                    <h3>
                        Win Rewards
                    </h3>
                    <p class="mb-4">
                        To Redeem your points, please <a href="mailto:<EMAIL>" target="_blank">contact support</a>. 
                    </p>
                    
                </div>
                <div class="col-md-4 col-12 pr-2">
                    <div class="card border win-card">
                        <div class="card-body p-20">

                                <div class="position-relative referral-content-box win-box mb-4">
                                    <i class="ph ph-trophy ph-lg position-absolute"></i>
                                </div>
                                <div>
                                    <h6 class="font-weight-500 mb-1">$30 Amazon Gift Card</h6>
                                    <p class="small-2 font-weight-400 mb-0">Spend 3000 credits and get a $30 amazon gift card.</p>
                                </div>
                        </div>
                    </div>
                    
                </div>
                <div class="col-md-4 col-12 px-2">
                    <div class="card border win-card">
                        <div class="card-body p-20">

                                <div class="position-relative referral-content-box win-box mb-4">
                                    <i class="ph ph-trophy ph-lg position-absolute"></i>
                                </div>
                                <div>
                                    <h6 class="font-weight-500 mb-1">Donate to WWF</h6>
                                    <p class="small-2 font-weight-400 mb-0">Donate your 1000 credits to World Wildlife Foundation</p>
                                </div>
                        </div>
                    </div>
                    
                </div>
                <div class="col-md-4 col-12 pl-2">
                    <div class="card border win-card">
                        <div class="card-body p-20">

                                <div class="position-relative referral-content-box win-box mb-4">
                                    <i class="ph ph-trophy ph-lg position-absolute"></i>
                                </div>
                                <div>
                                    <h6 class="font-weight-500 mb-1">Get $100 cashback</h6>
                                    <p class="small-2 font-weight-400 mb-0">Earn 10000 credits and get $100</p>
                                </div>
                        </div>
                    </div>
                    
                </div>
                <!-- <div class="col-md-3 col-12">
                    <div>
                        <h4 class="font-weight-500 mb-1">Google Ad Credit</h4>
                        <p class="small-2 font-weight-400 mb-0">Get $50 ad credit for google<br> by using 5000 credits</p>
                    </div>
                    
                </div> -->
                <div class="col-md-4 col-12 pr-2">
                    <div class="card border win-card  mt-4">
                        <div class="card-body p-20">

                                <div class="position-relative referral-content-box win-box  mb-4">
                                    <i class="ph ph-trophy ph-lg position-absolute"></i>
                                </div>
                                <div>
                                    <h6 class="font-weight-500 mb-1">Socialbu Supreme</h6>
                                    <p class="small-2 font-weight-400 mb-0">Earn 20000 credits and get Socialbu supreme subscription for a month</p>
                                </div>
                        </div>
                    </div>
                </div>
            </div>
            @endif
        </div>    
    </div>       

@endsection
@push('footer_html')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/2.7.1/Chart.min.js" charset="utf-8"></script>
    {!! $chart->script() !!}
@endpush
