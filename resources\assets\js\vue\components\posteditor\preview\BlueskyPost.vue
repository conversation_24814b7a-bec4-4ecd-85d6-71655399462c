<template>
    <div class="post border rounded p-4">
        <div class="d-flex align-items-start px-2"
             v-for="(post, i) in posts" :key="i + '-' + post.media.length + 'bs_preview'">
            <img :src="account.image" class="avatar rounded-circle" alt="avatar" />
            <div class="ml-2" style="min-width: 80%;">
                <div class="d-flex mb-2">
                    <a class="fullname" href="#" @click.prevent>{{ account.name }}</a>
                    <span class="username ml-1">@username</span>
                    <span class="ml-1 timestamp">· now</span>
                </div>

                <RichText :value="post.content" :readonly="true" class="mb-2"/>
                
                <div class="attachment-container  overflow-hidden" v-if="post.media && post.media.length">
                    <div class="gallery gallery-4-type-2" v-if="post.media.length > 1">
                        <div v-for="(attachment, index) in post.media" class="gallery-item" :key="index + attachment.url">
                            <img :src="attachment.url" :alt="attachment.name" :title="attachment.name" style="max-height: 500px;" />
                        </div>
                    </div>
                    <div class="w-100 text-center" v-else-if="post.media.length === 1">
                        <img class="rounded" :src="post.media[0].url" :alt="post.media[0].name" :title="post.media[0].name" style="max-height: 500px;" />
                    </div>
                </div>

                <LinkPreview
                    v-if="link && attachments.length === 0"
                    :url="link"
                    type="bluesky"
                    style="border: 1px solid #ccd6dd;" />

                <div class="post-footer mt-4">
                    <div class="post-actions">
                        <div class="mt-2 d-flex justify-content-between">
                            <div class="post-footer-option mt-2 mb-2 w-100 d-flex justify-content-between">
                                <i class="ph ph-chat-circle ph-lg "></i>
                                <i class="ph ph-repeat ph-lg "></i>
                                <i class="ph ph-heart ph-lg"></i>
                                <i class="ph ph-dots-three ph-lg"></i>      
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>


<script>
import LinkPreview from "../LinkPreview.vue";
import RichText from "../../common/RichText.vue";

export default {
    name: "BlueskyPost",
    props: ["account", "text", "attachments", "link", "options"],
    components: {
        LinkPreview,
        RichText
    },
    computed: {
        content() {
            // normalize linebreaks
            return (this.text || "").replace(/(\r\n|\r|\n){2,}/g, "$1\n");
        },
        posts() {
            const posts = [];
            posts.push({ content: this.content, media: this.attachments });
          
            return posts;
        }
    }
};
</script>

<style lang="scss" scoped>
.fullname,
.username,
.timestamp,
.post {
    font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Ubuntu, "Helvetica Neue", sans-serif;
}
.text {
    overflow-wrap: break-word;
    word-break: break-word;
    white-space: pre-wrap;
    font-weight: 400;
    a {
        color: rgb(27, 149, 224) !important;
        &:hover {
            text-decoration: underline !important;
        }
    }
}
.fullname {
    font-weight: bold;
    color: #000;
    line-height: 1.5;
    &:hover {
        text-decoration: underline;
    }
}
.username,
.timestamp {
    color: rgb(101, 119, 134);
}
.attachment-container {
   
    border-radius: 4px;
}
.gallery-item img{
    border-radius: 4px;
}
.post-actions svg {
    width: 1.25em;
    height: 1.25em;
    color: #657786;
    fill: currentColor;
}
</style>
