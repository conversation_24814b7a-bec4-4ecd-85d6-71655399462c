## Current proxies:
- proxy-1: **************:3128
- proxy-3: *************:3128
- proxy-5: *************:3128

# Adding new proxy

## Install Squid
```shell script
apt-get update
apt-get install squid -y
```

## Configuration
Open the file `/etc/squid/squid.conf`

### Set port if needed (default 3128)
```
http_port 3128
```

### Allow connection from all
Find the `http_access deny all` and replace with the following
```
http_access allow all
# http_access deny all
```

### Make it undetectable
Add the following at the end of the file
```shell script
# custom rules
via off
forwarded_for off
request_header_access Via deny all
request_header_access X-Forwarded-For deny all
```

### Disable caching
Add the following at the end of the file
```shell script
cache deny all
```

## Restart Squid
```shell script
sudo service squid restart
```
## Create swapfile (low memory will kill squid automatically)
```shell script
sudo fallocate -l 2G /swapfile # size depends on your actual ram
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

Verify swapfile is available
```shell script
sudo swapon --show
free -h
```

Our recent changes have enabled the swap file for the current session. However, if we reboot, the server will not retain the swap settings automatically. We can change this by adding the swap file to our `/etc/fstab` file.

Back up the `/etc/fstab` file in case anything goes wrong:
```shell script
sudo cp /etc/fstab /etc/fstab.bak
```

Add the swap file information to the end of your `/etc/fstab` file by typing:
```shell script
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
```

Change swapiness to 10:
```shell script
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
```


## Increase open file limit 
```shell script
echo 'fs.file-max = 500000' | sudo tee -a /etc/sysctl.conf
```

## Reboot after making the above changes
```shell script
reboot
```

## Make sure to keep the server protected by a firewall
Firewall used for this in DO: proxy
