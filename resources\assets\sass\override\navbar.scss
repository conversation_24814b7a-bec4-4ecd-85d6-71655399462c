.navbar{
    top: 20px;
    position: relative;
    .container-fluid{
        padding-left: 2rem;
        padding-right: 2rem;
    }
}
.nav-navbar {
    > .nav-item > .nav-link,
    > .nav-link{
        line-height: 1.5em;
    }
    .nav-link {
        font-size: 1em;
        font-weight: 500;
        text-transform: capitalize;
    }
    .dropdown-item {
        padding: 12px 28px 12px 20px;
    }
    .networks-nav{
        .dropdown-item{
            padding: 12px 20px !important;
        }
    }
}


@media (min-width: 770px){
    .dropdown-menu {
        display: none;
    }
    .nav-item.dropdown:hover .dropdown-menu {
        display: block;
    }
}

#app{
    & .navbar {
        position: fixed
    }
    .nav-navbar {
        .dropdown-item {
            padding: 8px 16px;
        }
    }
}

/*
 * Navigation
 */
 #navbar {
    top: 0;
    // z-index: 1000;
    .navbar-brand {
        padding-top: 0.75rem;
        padding-bottom: 0.75rem;
        font-size: 1rem;
        //background-color: rgba(0, 0, 0, .25);
        //box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.25);
        #sidebar_toggle {
            font-size: 22px;
            border-radius: 6px;
        }
    }
    .form-control {
        padding: 0.75rem 1rem;
        border-width: 0;
        border-radius: 0;
    }
    .hover-nav-menu {
        &:hover, &:active, &:focus {
            background-color: $color-secondary !important;
            //color: $socialbu-color !important;
        }
    }
}
.navbar-reports{
    padding: 0px !important;
    white-space: nowrap;
    .nav-link{
        font-size: 1rem !important;
        font-weight: 500 !important;
        color: #353F54 !important;
        background-color: white !important;
        border: none !important;
        line-height: 22.4px !important;
        font-family: 'Plus Jakarta Sans';
    }
    .nav-link.active{
        border-bottom: 2px solid $color-primary !important;
        color: $color-primary !important;
    }
    
    
}
