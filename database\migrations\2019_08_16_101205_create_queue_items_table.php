<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateQueueItemsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('publish_queue_items', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->increments('id');

            $table->integer('user_id')->unsigned()->index(); // user who added this
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade')->onUpdate('cascade');

            $table->text('content')->nullable(); // actual post content

            $table->json('options')->nullable(); // a general json column for storing any data if needed

            $table->integer('publish_queue_id')->unsigned()->index(); // queue this item belongs to
            $table->foreign('publish_queue_id')->references('id')->on('publish_queues')->onDelete('cascade')->onUpdate('cascade');

            $table->unsignedInteger('times_published')->default(0); // number of times it is published

            $table->timestamp('last_published_at')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('publish_queue_items');
    }
}
