<template>
    <div class="mt-4">
        <div class="attachment-container position-relative d-flex align-items-center justify-content-center w-100  overflow-hidden" v-if="attachment">
            <template v-if="attachments.length > 1">
                <ol class="carousel-indicators">
                    <li v-for="(attachment, index) in attachments" 
                        :key="index" 
                        @click="currentSlide = index"
                        :data-target="'#imagesCarousel'" 
                        :data-slide-to="index" 
                        :class="{ active: index === currentSlide }"></li>
                </ol>
                <div id="imagesCarousel" class="carousel slide w-100 position-absolute" data-ride="carousel">
                    <div class="carousel-inner">
                        <div v-for="(attachment, index) in attachments" 
                            :key="index" 
                            class="carousel-item" 
                            :class="{ active: index === currentSlide}">
                            <img 
                                :src="attachment.url" 
                                alt="Slide" 
                                class="d-block w-100 h-auto"
                            />
                            <div class="attachments-counter text-white position-absolute font-weight-500">{{ index + 1 }} / {{ attachments.length }}</div>
                        </div>
                    </div>
                </div>
            </template>

            <template v-else>
                <video class="video w-100" ref="video" @click="playOrPause" :title="attachment.name" style="object-fit: cover" v-if="attachment.mime.includes('video')">
                    <source :src="attachment.url" />
                </video>
                <img class="w-100" :src="attachment.url" alt="tiktok image" v-else>
            </template>
            <div class="mr-2 position-absolute" style="right: 0px;bottom: 98px;z-index: 1;">
                <div class="post-actions">
                    <div class="text-white text-center">
                        <div class="mb-6">
                            <img :src="account.image" class="tiktok-avatar rounded-circle" alt="avatar" />
                        </div>
                        <div class="pb-4">
                            <i class="ph ph-heart ph-fill ph-xl text-white"></i>
                            <div class="shadow text-white">
                                102K
                            </div>
                        </div>
                        <div class="pb-4">
                            <i class="ph ph-chat-circle ph-fill ph-xl text-white"></i>
                            <div class="shadow text-white">
                                879
                            </div>
                        </div>
                        <div>
                            <i class="ph ph-share-fat ph-fill ph-xl text-white"></i>
                            <div class="shadow text-white">
                                7879
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mb-2 position-absolute text-white account-info w-100">
                <div class="d-flex align-items-center mb-2" v-if="attachments[0].mime.includes('image')">
                    <span class="font-weight-600 lead-2 account-name">{{ account.name }}</span>
                    <div class="font-weight-600 photo-card ml-1 d-flex">
                        <i class="ph ph-md ph-image ph-fill"></i>
                        Photo
                    </div>
                    <span class="font-weight-500 date text-muted">· 08-08</span>
                </div>
                <div v-else>
                    <span class="font-weight-600 lead-2 account-name">{{ account.name }}</span>
                </div>
                <div class="font-weight-600 title mb-1" v-if="options.title">
                    {{ options.title }}
                </div>

                <div class="d-flex align-items-start justify-content-between">
                    <RichText :value="content" :readonly="true" class="description mb-0" :marks="richTextMarksSchema" />

                    <img :src="account.image" class="avatar rounded-circle" alt="avatar" />
                </div>
            </div>
        </div>
        <div class="text-center text-white attachment-container position-relative d-flex align-items-center justify-content-center w-100  overflow-hidden" v-else>
            <p class="mb-0">Please attach media to preview</p>
        </div>
    </div>
</template>

<script>
import { truncate } from "lodash";
import { sanitizeHtml } from "../../../../components";
import RichText from "../../common/RichText.vue";

export default {
    name: "TikTokPost",
    props: ["account", "text", "attachments", "link", "options"],
    components: {
        RichText
    },
    data(){
        return {
            currentSlide: 0,
            showFull: false
        }
    },
    computed: {
        content() {
             // normalize linebreaks
            let text = (this.text || "").replace(/(\r\n|\r|\n){2,}/g, "$1\n");
            return sanitizeHtml(this.showFull ? text : text.length >= 80 ? (`${text.substring(0, 80)}<span class="_see_all cursor-pointer"> more</span>`) : text);

        },
        richTextMarksSchema() {
            return {
                span: {
                    attrs: {
                        className: {}, // Define class attribute for the span
                        styles:{default:null}
                    },
                    inclusive: false,
                    parseDOM: [
                        {
                            tag: "span[class]", // Match span elements with a class attribute
                            getAttrs: dom => {
                                const className = dom.getAttribute('class');
                                const styles = dom.getAttribute('style');
                                return { className, styles };
                            }
                        }
                    ],
                    toDOM(node) {
                        const { className, styles } = node.attrs;
                        return ["span", { class: className, style: styles }, 0];
                    }
                }
            }
        },
        attachment(){
            return this.attachments && this.attachments.length && this.attachments[0];
        },
    },
    methods: {
        truncate,
        playOrPause(){
            if(this.$refs.video.paused){
                this.$refs.video.play();
            } else {
                this.$refs.video.pause();
            }
        },
        showMore() {
            this.showFull = true;
            return false;
        }
    },
    mounted() {
        $(document).on("click.see_all", "._see_all", this.showMore);
    },
    beforeDestroy() {
        $(document).off("click.see_all", "._see_all", this.showMore);
    }
};
</script>
<style lang="scss" scoped>
.tiktok-avatar{
    width: 52px;
    height: 52px;
}
.attachment-container{
    height: 754px;
    background: #000000;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}
.attachments-counter{
    padding: 4px 10px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 100px;
    line-height: 18.75px;
    font-size: 15px;
    top: 0px; 
    right:14px;
}

.account-info{
    padding: 8px 10px 8px 20px;
    bottom:8px; 
    left: 0px; 
    z-index: 1;
}
.post-actions{
    i{
        filter: drop-shadow(0px 1px 4px rgba(0, 0, 0, 0.20));
        opacity: 0.9;
    }
}
.shadow{
    filter: drop-shadow(0px 1px 4px rgba(0, 0, 0, 0.20));
}
.carousel-indicators{
    bottom: 120px !important;
}
.carousel-indicators li{
    width: 8px !important;
    height: 8px !important;
    border-radius: 50%;
}
.date{
    font-size: 18px;
    line-height: 22.5px;
}
.photo-card{
    padding: 4px 5px;
    line-height: 20px;
    background: rgba(255, 255, 255, 0.06);
    i{
        margin-right: 2px;
    }
}
.account-name{
    line-height: 25px;
}
.title{
    line-height: 22.5px;
    font-size:18px
}
.description{
    font-size: 18px;
    line-height: 22.5px !important;
}

</style>
