@extends('layout.user')
@section('title', str_limit($feed->getName()) . ' | ' . config('app.name'))
@section('content')

    @push('footer_html')
        <script>
            __loadComponent("feed", "#feed", function (feed) {
                feed.initialize({{ $feed->id }});
            });
        </script>
    @endpush

    <div class="row">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <div>
                <h1>
                    {{ $feed->getName() }}
                </h1>
            </div>
            <div class="d-flex">
                <div id="onlineUsers" class="mr-4 d-none d-sm-block"></div>
                <a class="btn btn-outline-secondary btn-sm border mr-2" role="button" href="{{ route('feeds.index') }}"><i class="ph ph-arrow-left" title="@lang('generic.back')"></i> @lang('generic.back')</a>
                @if($feed->user_id === \Auth::id() || ($feed->team && $feed->team->hasPermission(user(), 'feeds.edit')) )
                    <a id="feed_configure_btn" class="btn btn-outline-secondary btn-sm border" role="button" href="#" title="Configure" data-toggle="tooltip"><i class="ph ph-gear-six ph-md"></i></a>
                @endif
            </div>
        </div>
    </div>

    <div class="row">
        <div  class="col-12">
            <div id="feed"></div>
        </div>
    </div>

@endsection
