<?php

namespace App\Http\Middleware;

use App\Http\Controllers\User\AccountsController;
use Closure;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Http\Request;

class RequireAuthForAccountConnect extends Middleware
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param \Closure $next
     * @param mixed ...$guards
     * @return mixed
     * @throws AuthenticationException
     */
    public function handle($request, Closure $next, ...$guards)
    {   
        if(!AccountsController::getConnectRequestData()){
            $this->authenticate($request, $guards);
        }

        return $next($request);
    }
}
