<?php

namespace App\Generate\DynamicForm\Forms;

use App\Generate\DynamicForm\FormInterface;

class Test implements FormInterface
{
    public function fields(): array
    {
        return [
            [
                'id' => 'name',
                'label' => 'Name',
                'type' => 'text',
                'rules' => 'required',
                'class' => 'col-md-4',
            ],
            [
                'id' => 'email',
                'label' => 'Email',
                'type' => 'text',
                'rules' => 'required|email',
                'class' => 'col-md-4',
            ],
            [
                'type' => 'separator',
                'label' => 'Contact Information',
            ],
        ];
    }

    public function steps(): array
    {
        return [
            [
                'step' => 'log',
                'input' => [
                    'message' => 'Test form submitted',
                    'type' => 'info',
                ],
            ],
            [
                'step' => 'log',
                'input' => [
                    'message' => 'Hello {{form.first_name}} {{form.last_name}}',
                    'type' => 'info',
                ],
            ],
        ];
    }

    public function outputData(): array
    {
        return [
            'message' => 'Form submitted by {{form.first_name}} {{form.last_name}}',
        ];
    }

    public function outputComponents(): array
    {
        return [
            [
                'component' => 'input', // component is the type of element
                'props' => [ // props are the attributes of the component/element
                    'type' => 'text',
                    'value' => '{{form.first_name}} {{form.last_name}}',
                    'readonly' => 'readonly',
                ],
            ]
        ];
    }
}
