# NGINX Config
At `/etc/nginx/sites-available/socialbu.com`:
```
server {
    server_name www.socialbu.com;
    return 301 $scheme://socialbu.com$request_uri;
}
server {

    client_max_body_size 100M;
    proxy_read_timeout 300s;

    listen 80;
    server_name socialbu.com;
    root /socialbu/current/public;

    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options "nosniff";

    index index.html index.htm index.php;

    charset utf-8;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location /socket.io {
        proxy_pass http://127.0.0.1:6001; #could be localhost if <PERSON> and NginX are on the same box
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "Upgrade";
    }

    # blog wordpress; make sure ip is correct
    location ^~ /blog/ {
        proxy_pass                         http://**************;
        proxy_http_version                 1.1;
        proxy_cache_bypass                 $http_upgrade;

        # Proxy headers
        proxy_set_header Upgrade           $http_upgrade;
        proxy_set_header Connection        "upgrade";
        proxy_set_header Host              "socialbu.com";
        proxy_set_header X-Real-IP         $remote_addr;
        proxy_set_header X-Forwarded-For   $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto "https";
        proxy_set_header X-Forwarded-Host  $host;
        proxy_set_header X-Forwarded-Port  $server_port;

        # Proxy timeouts
        proxy_connect_timeout              60s;
        proxy_send_timeout                 60s;
        proxy_read_timeout                 60s;
    }


    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }

    error_page 404 /index.php;

    location ~ \.php$ {
        include snippets/fastcgi-php.conf;
        fastcgi_read_timeout 300s;
        fastcgi_pass unix:/var/run/php/php7.3-fpm.sock;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }
}

server {
    listen 80;
    server_name mp.socialbu.com;

    location /lib.min.js {
        proxy_set_header X-Real-IP $http_x_forwarded_for;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_pass https://cdn.mxpnl.com/libs/mixpanel-2-latest.min.js;
    }

    location /lib.js {
        proxy_set_header X-Real-IP $http_x_forwarded_for;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_pass https://cdn.mxpnl.com/libs/mixpanel-2-latest.js;
    }

    location /decide {
        proxy_set_header Host decide.mixpanel.com;
        proxy_set_header X-Real-IP $http_x_forwarded_for;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_pass https://decide.mixpanel.com/decide;
    }

    location / {
        proxy_set_header Host api.mixpanel.com;
        proxy_set_header X-Real-IP $http_x_forwarded_for;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Host $server_name;
        proxy_pass https://api.mixpanel.com/;
    }
}
```

# Bash
```
#!/bin/bash
sudo apt-get -y update

sudo apt-get -y install nginx-extras

curl -sL https://deb.nodesource.com/setup_12.x | sudo -E bash -
sudo apt-get install -y nodejs

sudo apt-get install apt-transport-https lsb-release ca-certificates
sudo wget -O /etc/apt/trusted.gpg.d/php.gpg https://packages.sury.org/php/apt.gpg
sudo echo "deb https://packages.sury.org/php/ $(lsb_release -sc) main" | sudo tee /etc/apt/sources.list.d/php.list
sudo apt-get update
# not sure about 7.2, install 7.3
# sudo apt-get install php7.2-cli php7.2-fpm php7.2-curl php7.2-gd php7.2-mysqlnd php7.2-mbstring php7.2-exif php7.2-xml  php7.2-zip php7.2-bcmath php7.2-intl -y
sudo apt-get install php7.3-cli php7.3-fpm php7.3-curl php7.3-gd php7.3-mysqlnd php7.3-mbstring php7.3-exif php7.3-xml  php7.3-zip php7.3-bcmath php7.3-intl -y

sudo wget https://dev.mysql.com/get/mysql-apt-config_0.8.10-1_all.deb
sudo apt install gdebi-core -y
sudo gdebi mysql-apt-config_0.8.10-1_all.deb
sudo apt-get -y update

mkdir /socialbu

apt-get install git redis-server -y

php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"
php -r "if (hash_file('SHA384', 'composer-setup.php') === '544e09ee996cdf60ece3804abc52599c22b1f40f4323403c44d44fdfdd586475ca9813a858088ffbc1f233e9b180f061') { echo 'Installer verified'; } else { echo 'Installer corrupt'; unlink('composer-setup.php'); } echo PHP_EOL;"
php composer-setup.php
php -r "unlink('composer-setup.php');"
mv composer.phar /usr/local/bin/composer

apt-get install --no-install-recommends gcc make libpng-dev libc-dev bash acl zip unzip -y


# enable swapfile
sudo fallocate -l 4G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
sudo cp /etc/fstab /etc/fstab.bak
echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
sysctl -p

# increase open file limit 
echo 'fs.file-max = 500000' | sudo tee -a /etc/sysctl.conf
sysctl -p

sudo apt-get install supervisor -y

# no root password and mysql8 (all default options)
sudo apt-get install mysql-server -y

# create random password
PASSWDDB="zH3pvFMItpyo8g"

# replace "-" with "_" for database username
MAINDB="socialbu"

# If /root/.my.cnf exists then it won't ask for root password
if [ -f /root/.my.cnf ]; then

    mysql -e "CREATE DATABASE ${MAINDB} /*\!40100 DEFAULT CHARACTER SET utf8 */;"
    mysql -e "CREATE USER ${MAINDB}@localhost IDENTIFIED WITH mysql_native_password BY '${PASSWDDB}';"
    mysql -e "GRANT ALL PRIVILEGES ON ${MAINDB}.* TO '${MAINDB}'@'localhost';"
    mysql -e "FLUSH PRIVILEGES;"

# If /root/.my.cnf doesn't exist then it'll ask for root password   
else
    echo "Please enter root user MySQL password!"
    read rootpasswd
    mysql -uroot -p${rootpasswd} -e "CREATE DATABASE ${MAINDB} /*\!40100 DEFAULT CHARACTER SET utf8 */;"
    mysql -uroot -p${rootpasswd} -e "CREATE USER ${MAINDB}@localhost IDENTIFIED WITH mysql_native_password BY '${PASSWDDB}';"
    mysql -uroot -p${rootpasswd} -e "GRANT ALL PRIVILEGES ON ${MAINDB}.* TO '${MAINDB}'@'localhost';"
    mysql -uroot -p${rootpasswd} -e "FLUSH PRIVILEGES;"
fi

# also, in mysql.cnf, change auth method so that php can support it as php doesnt support caching_sha2_password
# [mysqld]
# default_authentication_plugin=mysql_native_password 

# also,set mysql datadir if needed
# sudo nano /etc/my.cnf and add the following content assuming /mnt/volume_01 is the path to the volume
# datadir=/mnt/volume_01/mysql
# socket=/mnt/volume_01/mysql/mysql.sock
# [client]
# port=3306
# socket=/mnt/volume_01/mysql/mysql.sock


cd /etc/nginx/sites-available/
sudo nano socialbu.com # and put nginx config
sudo ln -s /etc/nginx/sites-available/socialbu.com /etc/nginx/sites-enabled/

service nginx reload

# for running puppeteer
apt-get install gconf-service libasound2 libatk1.0-0 libc6 libcairo2 libcups2 libdbus-1-3 libexpat1 libfontconfig1 libgcc1 libgconf-2-4 libgdk-pixbuf2.0-0 libglib2.0-0 libgtk-3-0 libnspr4 libpango-1.0-0 libpangocairo-1.0-0 libstdc++6 libx11-6 libx11-xcb1 libxcb1 libxcomposite1 libxcursor1 libxdamage1 libxext6 libxfixes3 libxi6 libxrandr2 libxrender1 libxss1 libxtst6 ca-certificates fonts-liberation libappindicator1 libnss3 lsb-release xdg-utils wget -y

# check if feature supported
cat /boot/config-$(uname -r) | grep CONFIG_USER_NS

# enable kernel feature for puppeteer
echo 'kernel.unprivileged_userns_clone=1' > /etc/sysctl.d/00-local-userns.conf; 
service procps restart

apt-get install lsof -y

# install ffmpeg: installs old version, min required 6.x
apt-get install ffmpeg -y

# install ffmpeg 4.3.1 (https://www.johnvansickle.com/ffmpeg/faq/)
wget https://johnvansickle.com/ffmpeg/releases/ffmpeg-release-amd64-static.tar.xz
tar xvf ffmpeg-release-amd64-static.tar.xz

# move ffmpeg and ffprobe bin to local path
sudo mv ffmpeg-4.3.1-amd64-static/ffmpeg ffmpeg-4.3.1-amd64-static/ffprobe /usr/local/bin/

# install build tools
apt-get install build-essential -y

```


```
    # copy .env file to /socialbu/shared/.env
    
    # generate key
    cd /socialbu/current && php artisan key:generate --force
    
    # setup cronjob 
    crontab -l | { cat; echo "* * * * * cd /socialbu/current && php artisan schedule:run >> /dev/null 2>&1"; } | crontab -
    
    # set max upload size (and post max size) in php.ini too (100M)
    # set max execution time to 120s in php.ini
    # set memory_limit to 512M in php.ini
    # set max_file_uploads = 100 in php.ini
    # set open files limit for www-data in /etc/security/limits.conf (10240)
```

# Install python >= 3.7 for node-gyp 

# add the following to crontab too
```
0 1 * * * find  /socialbu/shared/storage/app/converting_videos -type f -atime +5 -delete
0 1 * * * find  /tmp -type f -atime +5 -delete
0 1 * * * find  /socialbu/shared/storage/app/twitter_uploading -type f -atime +5 -delete
0 1 * * * find  /socialbu/shared/storage/app/linkedin_uploading -type f -atime +5 -delete
0 1 * * * find  /socialbu/shared/storage/app/instagram_uploading -type f -atime +5 -delete
0 1 * * * find  /socialbu/shared/storage/app/facebook_uploading -type f -atime +5 -delete
0 1 * * * find  /socialbu/shared/storage/app/tmp -type f -atime +5 -delete
0 1 * * * find  /socialbu/shared/storage/app/temp -type f -atime +5 -delete
0 */2 * * * find /socialbu/shared/storage/app/instagram/php-api -user root -type d -exec chown -R www-data:www-data {} \;
*/5 * * * * find /socialbu/shared/storage/app/instagram/php-api -user root -type f -exec chown -R www-data:www-data {} \;
```

# After server setup
```
cat >> /etc/supervisor/conf.d/laravel-worker.conf << EOF
[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /socialbu/current/artisan queue:work redis --queue=default --sleep=3 --tries=3 --timeout=1800
autostart=true
autorestart=true
user=www-data
numprocs=8
redirect_stderr=true
stdout_logfile=/socialbu/worker.log
EOF

cat >> /etc/supervisor/conf.d/laravel-automations-worker.conf << EOF
[program:laravel-automations-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /socialbu/current/artisan queue:work redis --queue=automations --sleep=3 --tries=3 --timeout=1800
autostart=true
autorestart=true
user=www-data
numprocs=8
redirect_stderr=true
stdout_logfile=/socialbu/automations-worker.log
EOF

cat >> /etc/supervisor/conf.d/laravel-posts-worker.conf << EOF
[program:laravel-posts-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /socialbu/current/artisan queue:work redis --queue=posts --sleep=3 --tries=3 --timeout=1800
autostart=true
autorestart=true
user=www-data
numprocs=48
redirect_stderr=true
stdout_logfile=/socialbu/posts-worker.log
EOF

# warning: fb webhook worker should have only 1 process, else leads to race conditions
cat >> /etc/supervisor/conf.d/laravel-fb-webhook-worker.conf << EOF
[program:laravel-fb-webhook-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /socialbu/current/artisan queue:work redis --queue=fb_webhook --sleep=3 --tries=3 --timeout=180
autostart=true
autorestart=true
user=www-data
numprocs=8
redirect_stderr=true
stdout_logfile=/socialbu/fb-webhook-worker.log
EOF

cat >> /etc/supervisor/conf.d/laravel-long-worker.conf << EOF
[program:laravel-long-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /socialbu/current/artisan queue:work redis_long --queue=default_long --sleep=3 --tries=1 --timeout=1300
autostart=true
autorestart=true
user=www-data
numprocs=10
redirect_stderr=true
stdout_logfile=/socialbu/long-worker.log
EOF

cat >> /etc/supervisor/conf.d/text2img-server.conf << EOF
[program:text2img-server]
process_name=%(program_name)s_%(process_num)02d
directory=/socialbu/current
environment=DEBUG="text2img*"
command=npm start text2img
autostart=true
autorestart=true
user=www-data
numprocs=1
redirect_stderr=true
stdout_logfile=/socialbu/text2img-server.log
EOF

sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start laravel-worker:*
sudo supervisorctl start laravel-fb-webhook-worker:*
sudo supervisorctl start laravel-long-worker:*
sudo supervisorctl start laravel-posts-worker:*
sudo supervisorctl start text2img-server:*
```


# create personal passport client (only once)
# php artisan passport:client --personal

# configure openssl
Add following at the end of /etc/ssl/openssl.cnf
```
[system_default_sect]
MinProtocol = TLSv1.0
CipherString = DEFAULT@SECLEVEL=1
```

# set sysctl.conf
```
echo "net.core.somaxconn=65536" >> /etc/sysctl.conf
sysctl -p
service php7.3-fpm restart
```
