<!DOCTYPE html>
<html>
<head>

    <meta charset="utf-8" />
    <meta name="csrf-token" content="{{ csrf_token() }}" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <!-- auth pages dont need to be indexed -->
    <meta name="robots" content="noindex, nofollow" />

    <!--
    <meta name="description" content="" />
    -->


    <link rel="icon" href="/favicon.ico" />


    <title>@yield('title')</title>

    <link href="{{ mix('/css/new-main.css') }}" rel="stylesheet" type="text/css" />

    <link href="{{ mix('/css/auth.css') }}" rel="stylesheet" type="text/css" />

    <style type="text/css">
        .ft a, .ft {
            color: grey;
        }
    </style>

    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->

    @include('layout.includes.common_head')

</head>

<body>
@include('layout.includes.common_body')

<div class="wrapper my-md-8 my-0">
    @yield('content')
</div>

<script type="text/javascript">
    /* load additional css font */
    (function(){
        var loadCss = function(cssId, href){
            if (!document.getElementById(cssId))
            {
                var head  = document.getElementsByTagName('head')[0];
                var link  = document.createElement('link');
                link.id   = cssId;
                link.rel  = 'stylesheet';
                link.type = 'text/css';
                link.href = href;
                link.media = 'all';
                head.appendChild(link);
            }
        };
        loadCss('faCSS', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css');
        loadCss('mdiCSS', 'https://cdnjs.cloudflare.com/ajax/libs/MaterialDesign-Webfont/6.5.95/css/materialdesignicons.min.css');

        // phosphor icons
        loadCss('ph-regular', 'https://cdn.jsdelivr.net/npm/@phosphor-icons/web@2.1.1/src/regular/style.min.css');
        loadCss('ph-bold', 'https://cdn.jsdelivr.net/npm/@phosphor-icons/web@2.1.1/src/bold/style.min.css');
        loadCss('ph-fill', 'https://cdn.jsdelivr.net/npm/@phosphor-icons/web@2.1.1/src/fill/style.min.css');
    })();
</script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>

<script>
    $(function(){
        const passwordInput = $('#password');
        const toggleIcon = $('#togglePassword');
        toggleIcon.on('click', function(){
            passwordInput.attr('type', passwordInput.attr('type') === 'password' ? 'text' : 'password');
            toggleIcon.toggleClass('ph-eye ph-eye-slash');
        })

        const passwordConfirmation = $('#passwordConfirmation');
        const showPasswordBtnForConfirmation = $('#togglePasswordConfirmation');
        showPasswordBtnForConfirmation.on('click', function(){
            passwordConfirmation.attr('type', passwordConfirmation.attr('type') === 'password' ? 'text' : 'password');
            showPasswordBtnForConfirmation.toggleClass('ph-eye ph-eye-slash');
        })
    });

</script>

<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js" integrity="sha384-Tc5IQib027qvyjSMfHjOMaLkfuWVxZxUPnCJA7l2mCWNIpG9mGCD8wGNIcPD7Txa" crossorigin="anonymous"></script>

@include('layout.includes.default.footer_html')
@stack('footer_html')
</body>
</html>
