import Shepherd from "shepherd.js";
import Cookies from "js-cookie";
import { debounce, uniq } from "lodash";
import $ from "jquery";
import { appConfig, axios } from "./components";

/*
URLs to exclude
 */
const URL_EXCLUSIONS = [
    "publish/share", // share page
    "publish/extension_editor", // extension editor
    "accounts/connect" // connect account
];

const tour = new Shepherd.Tour({
    tourName: "default",
    defaultStepOptions: {
        // classes: 'example-step-extra-class',
        buttons: [], // is set later
        cancelIcon: {
            enabled: true
        },
        popperOptions: {
            modifiers: [
                {
                    name: "offset",
                    options: {
                        offset: [0, 24]
                    }
                },
                {
                    name: "preventOverflow",
                    options: {
                        enabled: true,
                        boundariesElement: "viewport"
                    }
                }
            ]
        },
        canClickTarget: false,
        scrollTo: true,
        modalOverlayOpeningPadding: "10"
    },
    useModalOverlay: true
});

let bodyPosition = "",
    $body = $("body"),
    fixPositionActive = false,
    fixPosition = open => {
        // in bootstrap modal, tour position is not good, possible fix is adding relative position to body (container of popup)
        if (open) {
            if (!fixPositionActive) {
                bodyPosition = $body.css("position");
                $body.css("position", "relative");
                fixPositionActive = true;
            }
        } else {
            if (fixPositionActive) {
                $("body").css("position", bodyPosition);
                bodyPosition = "";
                fixPositionActive = false;
            }
        }
    };

if (process.env.MIX_APP_ENV !== "production") {
    window.removeTourCookie = () => Cookies.remove("completed_tour_steps");
}

const startTour = debounce(() => {
    fixPosition(true);
    setTimeout(tour.start, 500);
}, 400);

// fix position ev
["hide", "cancel", "complete"].forEach(event => tour.on(event, () => fixPosition(false)));

// remove all tour steps on completion/cancellation
const removeAllSteps = () => {
    if (!tour.steps.length) return;
    const ids = tour.steps.map(s => s.id);
    ids.forEach(id => {
        tour.removeStep(id);
    });
};
tour.on("cancel", removeAllSteps);
tour.on("complete", removeAllSteps);

// record event when cancelling/completing tour
tour.on("cancel", ()=>{
    __recordUsageEvent("tour_cancelled");
});
tour.on("complete", ()=>{
    __recordUsageEvent("tour_completed");
});

let completedTourSteps = [...(Cookies.get("completed_tour_steps") || "").split(","), ...(appConfig.completedTourSteps || "").split(",")];
const stepShown = async name => {

    // get completed steps from cookie + user data
    completedTourSteps = uniq(completedTourSteps).filter(l => l);

    if (!completedTourSteps.includes(name)) {
        completedTourSteps.push(name);

        const strToSave = completedTourSteps.join(",");

        // cookies set
        Cookies.set("completed_tour_steps", strToSave, {
            expires: 365,
            sameSite: "None",
            secure: true
        });

        // now save to user data too
        try {
            await axios.post("/app/onboarding/tour_steps", {
                completed: strToSave
            });
        } catch (e) {
            console.error(e);
        }

        __recordUsageEvent("tour_step_shown", {
            name
        });

    }

};

export default {
    removeStep: tour.removeStep,
    addStep(name, textOrHtml, elemSelector, config = {}) {
        const currPageUrl = document.location.href;
        if (URL_EXCLUSIONS.find(str => currPageUrl.includes(str))) {
            // no need to add step on this page; do nothing
            return;
        }

        if (completedTourSteps.includes(name)) {
            // no need to show this step because the step was showed to user already
            return;
        }

        if (tour.steps.map(s => s.id).includes(name)) {
            if (process.env.MIX_APP_ENV !== "production") {
                console.log("Skipping the call: a step with this name (" + name + ") is already added");
            }
            return;
        }

        config.position = typeof config.position === "string" ? config.position : "auto";

        if (config.position === "") config.position = "auto";

        /*
        if(elemSelector && typeof elemSelector !== "string"){
            // convert to string id
            $(elemSelector).addClass("tour__step__" + name);
            elemSelector = ".tour__step__" + name;
        }
        */

        const newStep = tour.addStep({
            id: name,
            text: textOrHtml,
            title: config.title,
            arrow: !config.noArrow,
            ...(elemSelector
                ? {
                      attachTo: {
                          element: elemSelector,
                          on: config.position
                      }
                  }
                : {}),
            when: {
                show() {
                    stepShown(name).then(r => {});

                    if (tour.steps.length < 2) return; // only add if greater than 1

                    const currentStepElement = tour.getCurrentStep().el;
                    const footer = currentStepElement.querySelector(".shepherd-footer");
                    if (!footer) {
                        console.warn("No footer to add tour progress");
                        return;
                    }

                    const progress = document.createElement("span");
                    progress.className = "text-muted small";
                    progress.style["margin-right"] = "auto";
                    progress.innerText = `${tour.steps.indexOf(tour.getCurrentStep()) + 1} / ${tour.steps.length}`;
                    footer.prepend(progress);
                }
            },
            scrollToHandler(elem) {
                if (!elem) return;
                const getScrollParent = function(element, includeHidden) {
                    let style = getComputedStyle(element);
                    const excludeStaticParent = style.position === "absolute";
                    const overflowRegex = includeHidden ? /(auto|scroll|hidden)/ : /(auto|scroll)/;

                    if (style.position === "fixed") return document.body;
                    for (let parent = element; (parent = parent.parentElement); ) {
                        style = getComputedStyle(parent);
                        if (excludeStaticParent && style.position === "static") {
                            continue;
                        }
                        if (overflowRegex.test(style.overflow + style.overflowY + style.overflowX)) return parent;
                    }

                    return document.body;
                };
                const elemToScroll = getScrollParent(elem);
                if (elemToScroll === document.body) {
                    $([document.documentElement, document.body]).animate(
                        {
                            scrollTop: $(elem).offset().top - 80
                        },
                        200
                    );
                } else {
                    $(elemToScroll).animate(
                        {
                            scrollTop: $(elem).offset().top - 80
                        },
                        200
                    );
                }
            }
        });

        // change Next to Done on the last step
        for (let i = 0; i < tour.steps.length; ++i) {
            const step = tour.steps[i];
            const isLast = i === tour.steps.length - 1;
            const buttons = [];
            if (i > 0) {
                buttons.push({
                    text: `<i class="ph ph-arrow-left"></i> Back`,
                    secondary: true,
                    action() {
                        // make sure to remove the id from cookie so it shows
                        const index = tour.steps.indexOf(tour.getCurrentStep());
                        tour.back();
                    },
                    classes:
                        "btn btn-light btn-sm mr-2" /*,
                disabled() {
                    const index = tour.steps.indexOf(tour.getCurrentStep());
                    return index < 1;
                }*/
                });
            }
            buttons.push({
                text: isLast ? `Done <i class="ph ph-check"></i>` : `Next <i class="ph ph-arrow-right"></i>`,
                classes: "btn btn-primary btn-sm",
                action: tour.next
            });
            step.updateStepOptions({
                buttons
            });
        }

        if (!tour.isActive() && tour.steps.length) {
            startTour();
        }
    }
};
