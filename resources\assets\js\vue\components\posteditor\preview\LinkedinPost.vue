<template>
    <div class="post">
        <div class="d-flex justify-content-between user-profile mt-4 mb-3">
            <div class="d-flex align-items-start">
                <img class="linkedin-avatar" alt="avatar" :src="account.image" :class="{'rounded-circle': account.type ==='linkedin.profile', 'rounded': account.type !== 'linkedin.profile'}"/>
                <div class="info">
                    <div>
                        <div class="fullname font-weight-600">{{ account.name.length > 22 ? account.name.substring(0,22).concat("...") : account.name }}</div>
                    </div>
                    <div class="headline small-2" v-if="account.type === 'linkedin.profile'">
                        Your headline will show here
                    </div>
                    <div class="followers small-2" v-if="account.type === 'linkedin.org'">
                        378,840 followers
                    </div>
                    <div class="timestamp small-2">
                        1m
                        <span>
                            • 
                            <i class="ph ph-globe-hemisphere-west ph-fill ph-small"></i>
                        </span>
                    </div>
                </div>
            </div>
            <i class="ph ph-dots-three-vertical ph-bold ph-lg"></i>
        </div>

        <RichText class="text" style="min-height: 8px;" placeholder="Write something..."
                :value="content"
                :readonly="true" :marks="richTextMarksSchema"/>

        <div v-if="attachments.length">
            <div class="gallery gallery-4-type-2 mt-3" v-if="attachments.length > 1">
                <div v-for="(attachment, index) in attachments" class="gallery-item" :key="'att' + index">
                    <video class="rounded" controls
                           :title="attachment.name" v-if="attachment.type.includes('video')" style="max-height: 500px;">
                        <source :src="attachment.url" />
                    </video>
                    <img :src="attachment.url" :alt="attachment.name" :title="attachment.name" style="max-height: 500px;"
                         class="rounded"
                         v-if="attachment.mime.includes('image')"/>
                    <embed v-if="attachment.type.includes('pdf')" :src="attachment.url" type="application/pdf">
                </div>
            </div>
            <div class="w-100 text-center mt-3" v-else-if="attachments.length === 1">
                <video class="w-100 object-fit-cover" controls :title="attachments[0].name" v-if="attachments[0].type.includes('video')" style="max-height: 500px;">
                    <source :src="attachments[0].url" />
                </video>
                <img class="w-100 object-fit-cover" :src="attachments[0].url" :alt="attachments[0].name" :title="attachments[0].name" style="max-height: 500px;"
                     v-else-if="attachments[0].mime.includes('image')"/>

                <iframe v-else-if="attachments[0].mime.includes('pdf')" class="pdf" 
                    :src="attachments[0].url"
                    width="100%" height="300">
                </iframe>

                <template v-else>
                    <a class="d-flex align-items-center justify-content-center flex-column border rounded p-2" style="height: 140px;" :href="attachments[0].url" target="_blank">  <i class="ph ph-lg ph-download-simple"></i> {{ attachments[0].name }}</a>
                </template>
            </div>
        </div>

        <LinkPreview
            v-if="link && attachments.length === 0"
            :url="options.link || link"
            :linkTitle="options.link_title"
            :linkDescription="options.link_description"
            :linkImage="options.link_thumbnail && options.link_thumbnail.url ? options.link_thumbnail.url : ''"
            type="linkedin" />
        <div class="social-details d-flex justify-content-between align-items-center border-bottom">
            <div class="d-flex align-items-center icons">
                <img src="/images/redesign/networks/linkedin/post-reactions.webp" alt="post reactions">
                <span class="small-2">
                    540
                </span>
            </div>
            <div>
                120 comments • 62 reposts
            </div>
        </div>
        <div class="d-flex flex-column">
            <div class="post-footer py-4">
                <div class="post-footer-option">

                    <div class="post-actions d-flex justify-content-between">
                        <div class="d-flex align-items-center flex-column">
                            <i class="ph ph-thumbs-up ph-bold ph-md"></i>
                            <div class="ml-1">Like</div>
                        </div>
                        <div class="d-flex align-items-center flex-column">
                            <i class="ph ph-chat-teardrop-text ph-bold ph-md"></i>
                            <div class="ml-1">Comment</div>
                        </div>
                        <div class="d-flex align-items-center flex-column">
                            <i class="ph ph-repeat ph-md ph-bold"></i>
                            <div class="">Repost</div>
                        </div>
                        <div class="d-flex align-items-center flex-column">
                            <i class="ph ph-paper-plane-tilt ph-bold ph-fill ph-md"></i>
                            <div class="ml-1">Share</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import _ from "lodash";
import LinkPreview from "../LinkPreview.vue";
import RichText from "../../common/RichText.vue";
export default {
    name: "LinkedinPost",
    props: ["account", "text", "attachments", "link", "options"],
    components: {
        LinkPreview,
        RichText
    },
    data() {
        return {
            showFull: false
        };
    },
    computed: {
        content() {
            let text = this.text.replace(/(\r\n|\r|\n){2,}/g, "$1\n"); // normalize linebreaks
            if(this.options['trim_link_from_content']){                     
                if(this.options['link']){           
                    text = this.text.replace(this.options['link'], '');
                } else {
                    text = this.text.replace(this.link, '');           
                }     
            }

            return this.showFull ? text : text.length >= 110 ? (`${text.substring(0, 110)}<span class="_see_all cursor-pointer" style="color:#666666;">...see more</span>`) : text;
        },
        richTextMarksSchema() {
            return {
                span: {
                    attrs: {
                        className: {}, // Define class attribute for the span
                        styles:{default:null}
                    },
                    inclusive: false,
                    parseDOM: [
                        {
                            tag: "span[class]", // Match span elements with a class attribute
                            getAttrs: dom => {
                                const className = dom.getAttribute('class');
                                const styles = dom.getAttribute('style');
                                return { className, styles };
                            }
                        }
                    ],
                    toDOM(node) {
                        const { className, styles } = node.attrs;
                        return ["span", { class: className, style: styles }, 0];
                    }
                }
            }
        }
    },
    methods: {
        showMore() {
            // isn't implemented yet
            this.showFull = true;
        }
    },
    mounted() {
        $(document).on("click.see_all", "._see_all", this.showMore);
    },
    beforeDestroy() {
        $(document).off("click.see_all", "._see_all", this.showMore);
    }
};
</script>

<style lang="scss" scoped>
.post {
    font-family: "Roboto", serif, -apple-system, system-ui, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Fira Sans, Ubuntu,
        Oxygen, Oxygen Sans, Cantarell, Droid Sans, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol, Lucida Grande,
        Helvetica, Arial, sans-serif;

}
.user-profile{
    padding-left: 14px;
    padding-right: 12px;
    .info{
        margin-left: 9px;
    }
}
.linkedin-avatar{
    width: 57px;
    height: 57px;
}
.fullname {
    font-size: 18px;
    line-height: 24.3px;
    color: #404040;
    
}
.ph-small{
    font-size: 14px;
}
.text {
    overflow-wrap: break-word;
    word-break: break-word;
    line-height: 21.6px !important;
    padding: 0px 24px 0px 14px;
    color: #1A1A1A;
    a {
        color: #0A66C2 !important;
        font-weight: 600 !important;

    }
}
.headline,
.timestamp,.followers {
    line-height: 18.9px !important;
    color: #666;
}
.social-details{
    margin: 16px 14px 0px 14px;
    padding-bottom: 14px;
    .icons{
        img{
            margin-right: 6px;
            width: 46px;
            height: 18px;
        }
    }
}
.post-footer {
    padding-left: 40px;
    padding-right: 40px;
    color: #404040;
    font-weight: 500;
    .post-actions {
        
        @media (max-width: 480px) {
            > div {
                margin-right: 10px;
            }
        }
    }
}
</style>
