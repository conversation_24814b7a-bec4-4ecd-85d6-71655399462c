import {merge} from "lodash";

let api = null;

/**
 *
 * @return {Promise<Object>} Content Drips API instance
 */
export function getApi() {
    return new Promise((resolve, reject) => {
        if (api) {
            resolve(api);
        } else {
            ((document, url) => {
                const script = document.createElement("script");
                script.src = url;
                script.onload = async () => {
                    contentdrips.init({
                        client_id: "socialbu4j13ofrK", 
                    });
                    api = contentdrips;
                    resolve(api);
                };
                script.onerror = (error) => {
                    reject(error);
                }
                document.body.appendChild(script);
            })(document, "https://assets.contentdrips.com/embed.js?v=1.2");
        }
    });
}

/**
 *
 * @param opts
 * @return {Promise<string>}
 */
export function createDesign(opts) {
    const defaultOpts = {
        canvas_width: 1080, // for 1:1 aspect ratio
        canvas_height: 1080, // for 1:1 aspect ratio
        is_scratch: false // this hides templates option
    };
    opts = merge(defaultOpts, opts);

    return new Promise((resolve, reject) => {
        getApi().then(api => {
            //current, content drips don't send any event on cancellation of design creation,
            // so need to handle it ourselves
            const intervalID = setInterval(() => {
                let cdripsHost = document.getElementById('cdrips-host');
                const shadowRoot = cdripsHost.shadowRoot;

                if(shadowRoot){
                    const cdrips_modal = shadowRoot.querySelector('.cdrips_modal')
                    
                    if(cdrips_modal && cdrips_modal.style.display === 'none'){
                        clearInterval(intervalID);
                        reject('User cancelled the design creation');
                    }
                }
            }, 1000);

            api.open(opts);

            api.on("image_export", function (e) {
                const newImageUrl = e.data.image_url;
                clearInterval(intervalID);
                resolve([newImageUrl]);
            });

            api.on('carousel_export', function(e){
                const images = e.data.images;
                clearInterval(intervalID);
                resolve(images);
            })


        })
    });
}

/**
 * @param {Array<String>} base64Files - base64
 * @return {Promise<File>} - files array
*/
export function getFileFromBase64(base64Files){
    return new Promise((resolve, reject) => {
        try {
            const files = base64Files.map(base64 => {
                const parts = base64.split(',');
                const mimeType = parts[0].split(':')[1].split(';')[0];
                const byteString = atob(base64.split(',')[1]);
                const buffer = new ArrayBuffer(byteString.length);
                const unit8array = new Uint8Array(buffer);

                for (let i = 0; i < byteString.length; i++) {
                    unit8array[i] = byteString.charCodeAt(i);
                }

                const ext = mimeType.split('/')[1];
                const blob = new Blob([buffer], { type: mimeType });
                return new File([blob], ext, {lastModified: new Date().getTime(), type: mimeType});
            })
            resolve(files);

        } catch (error) {
            reject(error);
        }

    })
}

export default {
    getApi,
    createDesign,
    getFileFromBase64
};
