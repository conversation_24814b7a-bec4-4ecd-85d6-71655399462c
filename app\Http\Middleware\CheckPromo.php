<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CheckPromo
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if( $request->query('promo') && strlen($request->query('promo')) > 0 ) {
            // queue cookie for 90 days
            \Cookie::queue('add_promo', $request->query('promo'), 129600);
        }
        return $next($request);
    }
}
