<?php


namespace App\Helpers;


use App\Account;
use App\LinkShortener;
use Illuminate\Database\Eloquent\Builder;
use PHPLicengine\Service\Bitlink;

class LinkShortenerHelper
{
    public static function getLinkShortenersForAccount(Account $account){
        return LinkShortener::whereHas('accounts',  function ($query /** @var Builder $query */) use($account) {
            $query->where('accounts.id', $account->id)->where('accounts.active', true);
        })->where('active', true)->get();
    }

    /**
     * @param Account $account
     * @param string $link
     * @param array $placeholder
     * @return string|null
     */
    public static function shortenLinkForAccount(Account $account, string $link, $placeholder = []){
        // get link shortener for this account
        /** @var LinkShortener $shortener */
        $shortener = self::getLinkShortenersForAccount($account)->first();

        if(!$shortener){
            // no user defined shortener
            return null;
        }

        try {
            return $shortener->shortenLink($link, $placeholder);
        } catch (\Exception $e) {
            $shortener->testConnection(true);
        }

        return null;
    }

}
