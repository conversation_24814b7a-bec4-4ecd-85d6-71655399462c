@extends('layout.default')

@php 
    $title = 'Free YouTube Username Generator for Channel Names';
    $description = 'Discover creative channel names with our free YouTube username generator. Start your YouTube journey with the perfect name.';
    $image = 'https://socialbu.com/images/site/robot_working_on_paper.png';
    $url = 'https://socialbu.com/tools/generate-username/youtube';

    $form = new \App\Generate\DynamicForm\Form('username_generator');
    $fields = $form->getFields();

    $fields = array_filter($fields, function ($field) {
        return $field['id'] !== 'network';
    });
@endphp

@php($title = 'Free YouTube Username Generator for Channel Names')
@php($description = 'Discover creative channel names with our free YouTube username generator. Start your YouTube journey with the perfect name.')
@php($image = 'https://socialbu.com/images/site/robot_working_on_paper.png')
@php($url = 'https://socialbu.com/tools/generate-username/youtube')
@section('title', $title . ' | ' . config('app.name'))
@push('head_html')
    <meta name="description" content="{{ $description }}"/>
    <link rel="canonical" href="{{ $url }}" />

    <meta property="og:locale" content="en_US" />
    <!--
    <meta property="og:type" content="website" />
    -->
    <meta property="og:title" content="{{ $title }}" />
    <meta property="og:description" content="{{ $description }}" />
    <meta property="og:url" content="{{ $url }}" />
    <meta property="og:site_name" content="SocialBu" />
    <meta property="og:image" content="{{ $image }}" />
    <meta property="fb:app_id" content="{{ config('services.facebook.client_id') }}" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content="{{ $image }}" />
    <meta name="twitter:title" content="{{ $title }}" />
    <meta name="twitter:description" content="{{ $description }}" />
    <meta name="twitter:site" content="@socialbuapp" />
@endpush
@push('footer_html')
    <script>
        window.execOnLoad(function () {
            __loadComponent("generate-tool-output", "#generate-tool-output", function(c){
                c.initialize({
                    form: {!! json_encode([
                        'fields' => $fields
                    ]) !!},
                    title:'Generated Usernames',
                    type: 'username_generator',
                    splitDataBy:',',
                    inputData:{
                        network: 'youtube'
                    },
                });
            });
        });
    </script>
@endpush
@section('content')
    <main class="main-content">
        <section class="section">
            <div class="container">
                <div class="row">
                    <div class="col-md-12 text-center mb-5">
                        <h2 class="d-md-block d-none display-4 mb-3 pb-0">
                            Free YouTube Username Ideas Generator
                        </h2>
                        <h4 class="d-md-none display-4 mb-3 pb-0">
                            Free YouTube Username Ideas Generator
                        </h4>
                        <p class="mb-6">
                            Discover YouTube name ideas tailored to your vibe in seconds
                        </p>
                    </div>
                    <div class="col-md-8 mx-auto w-100 mh-350">
                        <div id="generate-tool-output">
                            <div class="text-center">
                                <i class="ph ph-circle-notch ph-spin ph-lg text-muted"></i><span class="sr-only">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section>    
            <div class="container-fluid d-md-block d-none p-0 pt-7">
                <img class="lozad supported-platform img-fluid w-100 h-100" src="/images/1x1.gif" data-src="/images/redesign/generate-ai/generate-username.webp" alt="Generate username">
            </div>
            <div class="container-fluid d-md-none d-block p-0 pt-6">
                <img class="lozad supported-platform img-fluid w-100 h-100" src="/images/1x1.gif" data-src="/images/redesign/generate-ai/generate-username-mobile.webp" alt="Generate username">
            </div>
        </section>
         
        <section class="section rounded-xl p-xxl-5 px-20">
            <div class="container d-flex flex-column align-items-center text-center bg-primary text-white rounded-xl rounded-4 p-6 p-md-5 p-lg-8">
                <h2 class="display-2 fw-bold mb-6">Got your perfect YouTube Username?</h2>
                <p class="lead-2 mb-6">Schedule your YouTube Shorts in Bulk.</p>
                <a href="#" class="lead-2 btn bg-white btn-light text-primary d-flex align-items-center gap-2 px-4 py-2 ">
                    Automate your YouTube →
                </a>
            </div>
        </section>

        <section class="section">
            <div class="container px-20">
                <div class="row align-items-stretch">
                    <div class="col-12 px-20">
                        <h4 class="d-md-block d-none display-3 mb-2" >Create Username with Free YouTube Name <br>Generator AI</h4>
                        <h2 class="d-md-none display-6 mb-4 pb-1">Create Your Perfect TikTok Username in Seconds</h2>
                        <p class="pt-1 mb-6">
                            No more brainstorming burnout. Our free YouTube name generator gets you personalized handles really fast. Here’s how it works:
                        </p>
                    </div>
                    <div class="col-md-4 col-12 text-md-left py-0 pb-md-0 pb-2 mb-md-0 mb-4 px-md-3">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">1. Drop your Keywords</h6>
                                <p class="mb-5">Tell us your niche—gaming, beauty, tech? Add words like "vibe, pro, glow" for custom flair.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">2. Add a Short Description</h6>
                                <p class="mb-0">Share your vision. Do you want a quirky, cute, or professional tone? It will tailor it accordingly.<p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">3. Set the Tone & Category</h6>
                                <p class="mb-0">Choose fun, professional, or bold, then choose gaming, lifestyle, or more for rare, niche-specific results.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4 mt-4">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">4. Customize Your Username</h6>
                                <p class="mb-0">Decide how long you want it (8 characters, 10, or more), and whether you want numbers or special characters to make your YouTube username for girls pop.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4 mt-4">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">5. Customize It</h6>
                                <p class="mb-0">Select length (8, 12, or more characters) and add symbols for a unique twist.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4 mt-4">
                        <div class="card bg-light rounded-xl h-100">
                            <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                <h6 class="display-5 mb-3">6. Generate & Tweak</h6>
                                <p class="mb-0">In seconds, get 12 YouTube name ideas. Love one? Copy it. Need more? Hit generate again!</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-12 mb-md-0 mb-4 mt-5">
                        <div class="card bg-light rounded-xl h-100 d-flex flex-column">
                            <div class="pt-md-6 pb-md-6 pt-5 pb-6 px-5 d-flex flex-column align-items-center justify-content-between">
                                <h6 class="display-5">Why don't you try for yourself?</h6>
                                <a href="#top" class="btn btn-primary mt-6">Generated Usernames</a>
                            </div>
                        </div>
                    </div>                                           
                </div>
            </div>
        </section>

       <section class="section">
            <div class="container">
                <div class="text-left">
                    <h4 class="display-4 text-left mb-4">
                        What's the Best YouTube Username Ideas Generator?
                    </h4>
                    <p class="pt-1 mb-0">
                        SocialBu's Youtube Username ideas Generator creates fresh, memorable usernames fast for
                        influencers, gamers, and businesses.
                    </p>
                </div>
                <div class="mt-md-8 mt-4 pt-md-0 pt-1">
                    <div class="row">
                        <div class="col-md-3 col-12 pb-3">
                            <div class="card bg-primary-light rounded-xl h-100">
                                <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                    <h6 class="display-5 mb-3">Enter Keywords</h6>
                                    <p class="mb-0">Add words, describe your vibe.</p>
                                </div>
                            </div>
                        </div>
        
                        <div class="col-md-3 col-12 pb-3">
                            <div class="card bg-success-light rounded-xl h-100">
                                <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                    <h6 class="display-5 mb-3">Choose Style</h6>
                                    <p class="mb-0">Select tone and category easily.</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3 col-12 pb-3">
                            <div class="card bg-danger-light rounded-xl h-100">
                                <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                    <h6 class="display-5 mb-3">Customize Options</h6>
                                    <p class="mb-0">Set length and characters quickly.</p>
                                </div>
                            </div>
                        </div>
        
                        <div class="col-md-3 col-12 pb-3">
                            <div class="card bg-warning-light rounded-xl h-100">
                                <div class="card-body pt-md-6 pb-md-6 pl-md-5 pr-md-5 mt-md-2 mb-md-2 mx-md-0 pt-5 py-5 px-4 mx-1 my-1">
                                    <h6 class="display-5 mb-3">Pick Favorite</h6>
                                    <p class="mb-0">Choose and use your handle.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section class="section">
            <div class="container">
                <div class="row cta-image-container">
                    <img class="img-responsive cta-image lozad rounded-2xl" src="/images/redesign/generate-ai/cta-image.webp" alt="Run Your Social Media on Autopilot">
                    <div class="cta-image-text">
                        <h2 class="display-2 d-md-block d-none mb-6">Run Your Social Media on Autopilot</h2>
                        <h3 class="display-3 d-md-none mb-1">Run Your Social Media on Autopilot</h3>
                        <button class="btn btn-xl btn-primary d-md-block d-none rounded-md">Get Started <i class="ph ph-arrow-right"></i></button>
                        <button class="btn btn-primary d-md-none rounded-md mt-4">Get Started<i class="ph ph-arrow-right"></i></button>
                        <p class="pt-3 d-md-block d-none" style="color: #657085;">Cancel anytime. Free for 7 days.</p>
                    </div>
                </div>
            </div>
        </section>
        
        <section class="section">
            <div class="container">
                <div class="row gap-y justify-content-center">
                    <div class="col-md-9">
                        <h6 class="d-md-block d-none display-4 mb-3 mx-3">FAQs</h6>
                        <h2 class="d-md-none display-6 mb-4">FAQs</h2>
                        <div class="accordion accordion-arrow-right border border-light rounded-2xl px-5 py-2" id="frequent-questions">
                            @foreach([
                            ['id' => '1', 'question' => 'What’s a free YouTube username generator?', 'answer' => 'It’s a tool like ours that creates unique, free YouTube usernames based on your style or niche. It is perfect for kickstarting your channel.'],
                            ['id' => '2', 'question' => 'How do I pick the best YouTube username?', 'answer' => 'Try our free YouTube name generator AI: input keywords, set a tone, and customize to match your channel’s soul.'],
                            ['id' => '3', 'question' => 'Can I verify if a username is taken?', 'answer' => 'Our free YouTube name checker doesn’t confirm availability directly, but it gives you fresh YouTube name ideas to test on YouTube.'],
                            ['id' => '4', 'question' => 'How do I get a professional YouTube handle?', 'answer' => 'Choose “Business” or “Education” categories and a sleek tone in our free YouTube username generator for polished results.'],
                            ['id' => '5', 'question' => 'What if my ideal username is taken?', 'answer' => 'Add symbols or tweak it with our free YouTube name generator AI for rare, standout alternatives.'],
                            ['id' => '6', 'question' => 'Can creators use this tool?', 'answer' => 'Yes! Our tool delivers free YouTube premium username ideas for every niche from gamers to educators.'],
                            ['id' => '7', 'question' => 'How do I make my username brandable?', 'answer' => 'Use niche keywords and a bold tone in our free YouTube username generator for memorable, brand-friendly handles.'],
                            ['id' => '8', 'question' => 'Can I change my YouTube username later?', 'answer' => 'YouTube allows handle switches, so play around with our free YouTube name ideas worry-free.'],
                            ['id' => '9', 'question' => 'Are there username rules?', 'answer' => 'Handles must be 3-30 characters, no spaces—our tool keeps your free YouTube username within the limits.'],
                            ['id' => '10', 'question' => 'Does a good username boost discoverability?', 'answer' => 'A clear, niche-relevant handle from our free YouTube name checker vibe can help fans find you faster.'],
                            ['id' => '11', 'question' => 'How does this tool work?', 'answer' => 'Our free YouTube name generator AI blends your keywords, tone, and preferences into custom handles instantly.'],
                            ['id' => '12', 'question' => 'Can I target my niche?', 'answer' => 'Yes! Add keywords like "fitness, diy, music" for tailored free YouTube premium username ideas.'],
                            ['id' => '13', 'question' => 'Is this really free?', 'answer' => 'Totally! Our free YouTube username generator offers unlimited YouTube name ideas at zero cost.'],
                            ['id' => '14', 'question' => 'How do I make an aesthetic username?', 'answer' => 'Turn on symbols and pick a quirky tone for artsy, eye-catching free YouTube name ideas.'],
                            ['id' => '15', 'question' => 'What’s trending for YouTube usernames?', 'answer' => 'Try keywords like "chill, epic, neon" for fresh, trendy free YouTube premium username options.'],
                            ['id' => '16', 'question' => 'How do I stand out?', 'answer' => 'Mix unique keywords, symbols, and a fun tone for a free YouTube username that grabs attention.'],
                            ] as $index => $faq)
                            <div class="card shadow-none mb-2">
                                <div class="card-header px-0 pt-5 mb-1 {{ !$loop->first && !$loop->last ? 'border-bottom pb-5' : 'pb-2' }}" data-toggle="collapse" href="#collapse-{{ $faq['id'] }}" aria-expanded="{{ $index === 0 ? 'true' : 'false' }}">
                                    <div class="card-title d-flex align-items-center justify-content-between">
                                        <h6 class="mb-0 display-5 font-weight-600">{{ $faq['question'] }}</h6>
                                        <i class="ph {{ $index === 0 ? 'ph-caret-up' : 'ph-caret-down' }} ph-md"></i>
                                    </div>
                                </div>
                                <div id="collapse-{{ $faq['id'] }}" class="collapse {{ $index === 0 ? 'show' : '' }}" data-parent="#frequent-questions">
                                    <div class="card-body pb-3 px-0 pt-0 {{  !$loop->last ? 'border-bottom' : '' }}">
                                        <p class="mb-3">{!! $faq['answer'] !!}</p>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>

                    </div>
                </div>
            </div>
        </section>


    </main>

@endsection
