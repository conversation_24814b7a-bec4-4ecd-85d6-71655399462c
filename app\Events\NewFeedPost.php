<?php

namespace App\Events;

use App\Feed;
use App\FeedPost;
use App\User;
use Illuminate\Broadcasting\Channel;
use Illuminate\Queue\SerializesModels;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

class NewFeedPost implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /** @var Feed */
    private $feed;

    /** @var FeedPost */
    private $feedPost;

    /** @var string $socketId */
    private $socketId;

    /**
     * Create a new event instance.
     *
     * @param Feed $feed
     * @param FeedPost $feedPost
     * @param string $socketId
     */
    public function __construct(Feed $feed, FeedPost $feedPost, string $socketId = null)
    {
        $this->feed = $feed;
        $this->feedPost = $feedPost;
        $this->socketId = $socketId;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PresenceChannel('feed.' . $this->feed->id);
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        return [
        ];
    }
}
