<?php

namespace App\Respond;

use App\Account;

abstract class BaseInboxHandler
{
    /** @var Account $account */
    private $account;

    public function __construct(Account $account){
        $this->account = $account;
    }

    /**
     * @return mixed Boolean or 'yes' or 'never'
     */
    abstract public function shouldPoll();

    /**
     * @return bool True if we got new data, false otherwise
     */
    abstract public function poll(): bool;
    abstract public function backFill(): void;

    /**
     * @return bool True if we got new data, false otherwise
     */
    abstract public function handle($webhookData): bool;

    public function getAccount(): Account
    {
        return $this->account;
    }
}
