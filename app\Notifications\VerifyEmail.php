<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class VerifyEmail extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->greeting('Hey ' . explode(' ', ($notifiable->name . ''))[0])
            ->subject(explode(' ', ($notifiable->name . ''))[0] . ', complete your SocialBu account registration')
            ->line('You signed up for SocialBu using the email address: ' . $notifiable->email)
            ->line('Please complete email verification to start using SocialBu.'
                . ($notifiable->password === 'no_password' ? ' You may also need to set your account password.' : '')
            )
            ->action('Verify Now', url("auth/confirm/{$notifiable->token}"))
            ->line('Please do not hesitate to contact us if you need any assistance or have any questions.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}
