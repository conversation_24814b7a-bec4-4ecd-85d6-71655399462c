<?php

namespace App\Http\Controllers\User;

use App\Account;
use App\Helpers\EmailListHelper;
use App\LinkShortener;
use App\Team;
use Composer\Package\Link;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Laravel\Socialite\Two\InvalidStateException;
use Laravel\Socialite\Two\User;

class LinkShortenersController extends Controller
{
    public function redirectToProvider($provider){
        if(!config('services.' . $provider))
            abort(404);

        if(!in_array($provider, ['bitly', 'linkmngr'])){
            abort(404);
        }

        return \Socialite::driver($provider)->redirect();
    }

    /**
     * @param Request $request
     * @param $provider
     * @return \Illuminate\Http\RedirectResponse
     */
    public function handleProviderCallback(Request $request, $provider)
    {
        if (!config('services.' . $provider))
            abort(404);

        if ($request->input('error_code', false)) {
            flash('Error ' . $request->input('error_code') . ': ' . $request->input('error_message', 'Authentication error'), 'error');
            return redirect()->route('link_shorteners.index');
        } else if($request->has('error') && $request->has('error_description')){
            flash('Error ' . $request->input('error') . ': ' . $request->input('error_description', 'Authentication error'), 'error');
            return redirect()->route('link_shorteners.index');
        }

        try {
            /** @var User $external_user */
            $external_user = \Socialite::driver($provider)->user(); // name, email
        } catch (InvalidStateException $exception) {
            flash('Session state expired or invalid. Please try again.', 'error');
            return redirect()->route('link_shorteners.index');
        } catch (\InvalidArgumentException $exception) {
            flash('Session state expired or invalid. Please try again.', 'error');
            return redirect()->route('link_shorteners.index');
        } catch (\Exception $e) {
            report($e);
            flash($e->getMessage(), 'error');
            return redirect()->route('link_shorteners.index');
        }

        // $external_user->token
        // add to db
        if($provider === 'bitly'){
            // $external_user->nickname is the unique external id
            $shortener = self::query()->where('type', $provider)->where('external_id', $external_user->nickname)->first();
            if(!$shortener) {
                $shortener = new LinkShortener([
                    'user_id' => user()->id,
                    'type' => $provider,
                    'active' => true,
                    'external_id' => $external_user->nickname,
                    'name' => $external_user->name,
                ]);
                $shortener->save();
            }
            $shortener->setToken($external_user->token);
        } else if($provider === 'linkmngr'){

            // always add new shortener so user can customize each one however needed
            $shortener = new LinkShortener([
                'user_id' => user()->id,
                'type' => $provider,
                'active' => true,
                'external_id' => $external_user->id,
                'name' => $external_user->name,
            ]);
            $shortener->save();

            $shortener->setToken([
                'access_token' => $external_user->token,
                'refresh_token' => $external_user->refreshToken,
                'expires_in' => $external_user->expiresIn,
                'timestamp' => time(),
            ]);
        }

        if(!isset($shortener)){
            flash('Invalid link shortener type', 'error');
            return redirect()->route('link_shorteners.index');
        }

        flash( $shortener->getType() . ' link shortener is connected.', 'success');

        // send event
        try {
            EmailListHelper::getInstance()->sendEvent($shortener->user, 'link_shortener_connected', $shortener->getName());
        } catch (\Exception $exception){
            report($exception);
        }
        record_usage_event('link_shortener_connected', $shortener->getName());

        return redirect()->route('link_shorteners.index');
    }

    public function index(){
        $shorteners = self::query()->paginate();
        return view('user.link_shorteners.index', [
            'link_shorteners' => $shorteners,
        ]);
    }

    /**
     * @param Request $request
     * @param $id
     * @return \Illuminate\Support\Collection|\Tightenco\Collect\Support\Collection
     * @throws \Illuminate\Validation\ValidationException
     */
    public function update(Request $request, $id){
        $this->validate($request, [
            'accounts' => 'array',
            'accounts.*' => 'integer|exists:accounts,id',
            'query_parameters' => 'array',
            'query_parameters.*.key' => 'required|string|min:1|max:255',
            'query_parameters.*.value' => 'string|max:255',
        ]);

        /** @var LinkShortener $shortener */
        $shortener = self::query()->findOrFail($id);

        $accounts = user()->accounts;

        $userAccountIds = $accounts->map(function($a){return $a->id;})->toArray();

        $accountIdsToAttach = collect($request->input('accounts', []))->filter(function($id) use($userAccountIds){
            return in_array($id, $userAccountIds);
        })->toArray();

        $accountsToAttach = $accounts->filter(function (Account $account) use($accountIdsToAttach){
            return in_array($account->id, $accountIdsToAttach);
        });

        // make sure these accounts are not attached to any link shortener
        foreach ($accountsToAttach as $account){
            $account->linkShorteners->each(function (LinkShortener $linkShortener) use($account){
                $linkShortener->accounts()->detach($account->id);
            });
        }

        // now attach new accounts
        $shortener->accounts()->sync($accountIdsToAttach);

        $shortener->setQueryParameters($request->input('query_parameters', []));
        $shortener->setSettings($request->input('settings', [])); // if any

        // send event
        try {
            EmailListHelper::getInstance()->sendEvent($shortener->user, 'link_shortener_updated', $shortener->getName());
        } catch (\Exception $exception){
            report($exception);
        }
        record_usage_event('link_shortener_updated', $shortener->getName());

        return LinkShortener::transform($shortener);
    }

    /**
     * @param Request $request
     * @param $id
     * @throws \Exception
     */
    public function destroy(Request $request, $id){
        /** @var LinkShortener $shortener */
        $shortener = self::query()->findOrFail($id);
        $shortener->delete();

        // send event
        try {
            EmailListHelper::getInstance()->sendEvent($shortener->user, 'link_shortener_disconnected', $shortener->getName());
        } catch (\Exception $exception){
            report($exception);
        }
        record_usage_event('link_shortener_disconnected', $shortener->getName());
    }

    /**
     * @param Request $request
     * @param $id
     * @return \Illuminate\Support\Collection
     * @throws \Exception
     */
    public function testConnection(Request $request, $id){
        /** @var LinkShortener $shortener */
        $shortener = self::query()->findOrFail($id);
        $shortener->testConnection();
        return LinkShortener::transform($shortener);
    }

    /**
     * @return Builder
     */
    public static function query(){
        $user = user();
        return LinkShortener::where('user_id', $user->id);
    }
}
