<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateFeedPostsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('feed_posts', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->increments('id');

            // feed this post belongs to
            $table->integer('feed_id')->unsigned()->index();
            $table->foreign('feed_id')->references('id')->on('feeds')->onDelete('cascade')->onUpdate('cascade');

            // external id of the post/object
            $table->string('external_id')->index();

            // for storing keywords to be searched as json is not easily searchable
            $table->string('keywords')->nullabe()->index();

            // for storing type e.g. message or tweet
            $table->string('type');

            // parent id of the FeedPost this post belongs to e.g. for comments on a post (parent)
            $table->integer('parent_id')->unsigned()->nullable()->index();
            $table->foreign('parent_id')->references('id')->on('feed_posts')->onDelete('cascade')->onUpdate('cascade');

            // id of the FeedPost this post will come under (root)
            $table->integer('root_id')->unsigned()->nullable()->index();
            $table->foreign('root_id')->references('id')->on('feed_posts')->onDelete('cascade')->onUpdate('cascade');

            $table->boolean('read')->default(false);

            // json representation of post/object
            $table->json('data');

            $table->integer('account_id')->unsigned()->index(); // account from which this post was retrieved
            $table->foreign('account_id')->references('id')->on('accounts')->onDelete('cascade')->onUpdate('cascade');

            // user who added the post (if sent to outside from inside) - this will be set if the feedpost was created as a result of a user reply
            $table->integer('user_id')->unsigned()->nullable();
            $table->foreign('user_id')->references('id')->on('users');

            // to store misc. data for the feed (if needed)
            $table->json('options')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('feed_posts');
    }
}
