<?php

namespace App\Generate\DynamicForm;

use App\Generate\DynamicForm\Forms\FormFromData;
use Illuminate\Support\Str;

class Form
{
    /** @var FormInterface $form */
    protected $form;

    private $output = [];

    /**
     * @param $idOrArray mixed
     * @throws \Exception
     */
    public function __construct($idOrArray)
    {
        // check if it's an array
        if (is_array($idOrArray)) {
            $this->form = new FormFromData($idOrArray);
        } else {
            $id = $idOrArray;

            // id will be like 'test' or linkedin_bio or twitter_tweet
            // we need to convert it to Test or LinkedinBio or TwitterTweet
            $className = Str::studly($id);

            // classname cannot be FormFromData
            if($className === 'FormFromData'){
                throw new \Exception('Form not found');
            }

            // we need to get the full class name
            $class = 'App\Generate\DynamicForm\Forms\\' . $className;

            // check if the class exists
            if (!class_exists($class)) {
                throw new \Exception('Form not found');
            }

            // we need to instantiate the class
            $this->form = new $class();
        }
    }

    public function getFields(): array
    {
        return $this->form->fields();
    }

    private function getSteps(): array
    {
        return $this->form->steps();
    }

    /**
     * @throws \Exception
     */
    public function validate(array $data = [])
    {
        // ensure 'required' fields are found in data
        foreach ($this->getFields() as $field){
            if(isset($field['rules'])){
                try {
                    \Validator::validate($data, [
                        $field['id'] => $field['rules'],
                    ]);
                } catch (\Illuminate\Validation\ValidationException $validationException){
                    throw new \InvalidArgumentException($field['id'] . ': ' . $validationException->validator->errors()->first($field['id']));
                }
            }
        }
    }

    /**
     * @param array $formData
     * @return array
     * @throws \Exception
     */
    public function process(array $formData = []): array
    {
        // first, we validate
        $this->validate($formData);

        $data = [
            'form' => $formData,
        ];
        // execute form steps
        foreach ($this->getSteps() as $index => $stepData) {

            // find step class by name
            $class = 'App\Generate\DynamicForm\Steps\\' . Str::studly($stepData['step']);

            // check if the class exists
            if (!class_exists($class)) {
                throw new \Exception('Step not found');
            }

            // return the class
            /** @var Step $step */
            $step = new $class('step' . ($index + 1), $stepData, $data);

            $output = $step->process();

            // $output will be merged with $data
            $data = array_merge($data, $output);
        }

        // finally, we return the output
        $outputData = $this->form->outputData();

        if(empty($outputData)){
            $this->output = $data;
        } else {
            $this->output = transform_data_placeholders($outputData, $data);
        }

        return $this->output;
    }

    public function getOutputComponents(): array {
        return transform_data_placeholders($this->form->outputComponents(), $this->output);
    }
}
