<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class InsightsChangeValueCollumn extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.insights'))->table('post_engagements', function (Blueprint $table) {
            $table->unsignedBigInteger('value')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(config('database.insights'))->table('post_engagements', function (Blueprint $table) {
            $table->unsignedDecimal('value')->change();
        });
    }
}
