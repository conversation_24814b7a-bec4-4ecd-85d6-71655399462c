const dotenv = require("dotenv");
const fs = require("fs-extra");
const path = require("path");
const envFile = dotenv.parse(fs.readFileSync(path.resolve(__dirname, "./.env")));
process.env.IGPAPI_LICENCE_KEY = envFile.IGPAPI_LICENCE_KEY;

const children = [];
const commands = {
    tunnel: function(subdomain, port = 8000) {
        if (!subdomain) return console.log("subdomain not passed");
        commands.localtunnel(subdomain, port);
        // and create local proxy to speed up site on local machine; requires you to add entry in os hosts file for the dns host;
        commands.localproxy(subdomain + ".tunnel.socialbuapp.com", port);
    },
    serveo: function(subdomain, port = 8000) {
        if (!subdomain) return console.log("subdomain not passed");
        const spawn = require("child_process").spawn;
        const child = spawn(
            "ssh",
            ["-oStrictHostKeyChecking=no", ...`-R ${subdomain}.serveo.net:80:localhost:${port} serveo.net`.split(" ")],
            {
                shell: true
            }
        );
        child.stdout.on("data", d => console.log(d + ""));
        child.stderr.on("data", d => console.log(d + ""));
        children.push(child);
    },
    localtunnel: function(subdomain, port = 8000) {
        port = port || 8000;
        let localtunnel = require("localtunnel");
        let spawn = require("child_process").spawn;
        if (!subdomain) {
            console.log("Using random subdomain.");
        }

        let tunnel, setup, st;
        (setup = function() {
            tunnel = localtunnel(
                port,
                {
                    subdomain: subdomain,
                    host: "http://tunnel.socialbuapp.com"
                },
                function(err, tunnel) {
                    if (err) {
                        console.log("Tunnel: error (" + err.message + "). reconnecting in 5s. ");
                        if (st) {
                            clearTimeout(st);
                            st = false;
                        }
                        st = setTimeout(setup, 5000);
                        return;
                    }

                    console.log("Tunnel: started " + tunnel.url);

                    tunnel.on("close", function() {
                        // tunnels are closed
                        console.log("Tunnel: closed. reconnecting in 5s");
                        if (st) {
                            clearTimeout(st);
                            st = false;
                        }
                        st = setTimeout(setup, 5000);
                    });

                    tunnel.on("error", function(err) {
                        // tunnels are closed
                        console.log("Tunnel: error (" + err.message + "). reconnecting in 5s. ");
                        if (st) {
                            clearTimeout(st);
                            st = false;
                        }
                        st = setTimeout(setup, 5000);
                    });
                }
            );
        })();
    },
    localproxy: function(host, port = 8000) {
        if (!host) return console.log("host not passed");
        console.log("starting localProxy for host " + host);
        const httpProxy = require("http-proxy"),
            fs = require("fs");

        const proxy = httpProxy.createProxyServer({ target: `http://127.0.0.1:${port}` }); // See (†)
        proxy.listen(80);
        proxy.on("error", function(err, req, res) {
            res.writeHead(500, {
                "Content-Type": "text/plain"
            });
            console.log(err);
            res.end(err.message);
        });

        const proxySSL = httpProxy.createProxyServer({
            target: `http://127.0.0.1:${port}`,
            ssl: {
                key: fs.readFileSync("local/key.pem", "utf8"),
                cert: fs.readFileSync("local/cert.pem", "utf8")
            },
            headers: {
                "x-forwarded-proto": "https"
            }
        }); // See (†)
        proxySSL.listen(443);
        proxySSL.on("error", function(err, req, res) {
            res.writeHead(500, {
                "Content-Type": "text/plain"
            });
            console.log(err);
            res.end(err.message);
        });
    },
    instagram() {
        try {
            const server = require("./node/instagram_worker");
            server.start();
            process.on("SIGTERM", function() {
                server.stop(function() {
                    process.exit(0);
                });
            });
            process.on("SIGINT", function() {
                server.stop(function() {
                    process.exit(0);
                });
            });
        } catch (e) {
            console.error(e.message);
        }

        // also start igpapi because we now use that too for instagram
        commands.igpapi();
    },
    igpapi() {
        try {
            const server = require("./node/api");
            server.start();
            process.on("SIGTERM", function() {
                server.stop(function() {
                    process.exit(0);
                });
            });
            process.on("SIGINT", function() {
                server.stop(function() {
                    process.exit(0);
                });
            });
        } catch (e) {
            console.error(e.message);
        }
    },
    text2img(){
        process.env.DREAMSTUDIO_API_KEY = envFile.DREAMSTUDIO_API_KEY;
        process.env.REPLICATE_API_KEY = envFile.REPLICATE_API_KEY;
        try {
            const server = require("./node/text2img");
            server.start();
            process.on("SIGTERM", function() {
                server.stop(function() {
                    process.exit(0);
                });
            });
            process.on("SIGINT", function() {
                server.stop(function() {
                    process.exit(0);
                });
            });
        } catch (e) {
            console.error(e.message);
        }
    }
};

(function() {
    /*
    if (process.platform === "win32") {
        const rl = require("readline").createInterface({
            input: process.stdin,
            output: process.stdout
        });

        rl.on("SIGINT", function () {
            process.emit("SIGINT");
        });
    }
     */
    const cleanUp = function() {
        children.forEach(function(child) {
            child.kill();
        });
    };
    let cleanExitCalled = false;
    const cleanExit = function(e) {
        if (cleanExitCalled) return;
        console.log("exiting");
        cleanUp();
        cleanExitCalled = true;
        process.exit();
    };
    for (const ev of ["exit", "SIGINT", "SIGUSR1", "SIGUSR2", "SIGTERM"]) {
        process.on(ev, cleanExit);
    }

    process.on("uncaughtException", err => {
        console.log(`Uncaught Exception: ${err.message}`);
    });

    // process.on('SIGINT', cleanExit); // catch ctrl-c
    // process.on('SIGTERM', cleanExit); // catch kill
    if (process.argv.length > 2) {
        if (commands[process.argv[2]]) {
            let args = [].slice.call(process.argv).splice(3);
            commands[process.argv[2]].apply(this, args);
        } else {
            console.log("Unknown function ", process.argv[2]);
        }
    } else {
        console.log("Nothing to do.");
    }
})();
