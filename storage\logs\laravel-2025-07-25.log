[2025-07-25 05:58:56] local.ERROR: Maximum execution time of 60 seconds exceeded {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Maximum execution time of 60 seconds exceeded at C:\\xampp\\htdocs\\socialtool\\vendor\\socialiteproviders\\manager\\src\\ServiceProvider.php:9)
[stacktrace]
#0 {main}
"} 
[2025-07-25 06:37:10] local.ERROR: Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    "error": {
        "message": "You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 {"userId":1,"exception":"[object] (GuzzleHttp\\Exception\\ClientException(code: 401): Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    \"error\": {
        \"message\": \"You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 at C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\RequestException.php:113)
[stacktrace]
#0 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(65): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response))
#1 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(204): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(153): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(48): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(248): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(224): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(269): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(226): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(62): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(182): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(95): GuzzleHttp\\Client->request('post', 'https://api.ope...', Array)
#11 C:\\xampp\\htdocs\\socialtool\\app\\Helpers\\Hashtags.php(49): GuzzleHttp\\Client->__call('post', Array)
#12 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(281): App\\Helpers\\Hashtags::getSuggested(Object(Illuminate\\Database\\Eloquent\\Collection), 'yt5yewe')
#13 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(42): App\\Http\\Controllers\\Json\\JsonSearchController->suggested_hashtags(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Json\\JsonSearchController->serve(Object(Illuminate\\Http\\Request), 'suggested_hasht...')
#15 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('serve', Array)
#16 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(219): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Json\\JsonSearchController), 'serve')
#17 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(176): Illuminate\\Routing\\Route->runController()
#18 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(681): Illuminate\\Routing\\Route->run()
#19 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\RequireOnboardingCompleted.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\RequireOnboardingCompleted->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Laravel\\Passport\\Http\\Middleware\\CreateFreshApiToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\UpdateUserData.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\UpdateUserData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckPromo.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckPromo->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckReferral.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckReferral->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(77): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(56): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(683): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(658): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(624): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(613): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\SecureHeaders.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\SecureHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\socialtool\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\socialtool\\public\\index.php(53): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\socialtool\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#70 {main}
"} 
[2025-07-25 06:37:28] local.ERROR: Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    "error": {
        "message": "You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 {"userId":1,"exception":"[object] (GuzzleHttp\\Exception\\ClientException(code: 401): Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    \"error\": {
        \"message\": \"You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 at C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\RequestException.php:113)
[stacktrace]
#0 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(65): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response))
#1 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(204): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(153): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(48): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(248): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(224): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(269): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(226): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(62): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(182): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(95): GuzzleHttp\\Client->request('post', 'https://api.ope...', Array)
#11 C:\\xampp\\htdocs\\socialtool\\app\\Helpers\\Hashtags.php(49): GuzzleHttp\\Client->__call('post', Array)
#12 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(281): App\\Helpers\\Hashtags::getSuggested(Object(Illuminate\\Database\\Eloquent\\Collection), 'yt5yewe')
#13 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(42): App\\Http\\Controllers\\Json\\JsonSearchController->suggested_hashtags(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Json\\JsonSearchController->serve(Object(Illuminate\\Http\\Request), 'suggested_hasht...')
#15 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('serve', Array)
#16 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(219): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Json\\JsonSearchController), 'serve')
#17 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(176): Illuminate\\Routing\\Route->runController()
#18 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(681): Illuminate\\Routing\\Route->run()
#19 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\RequireOnboardingCompleted.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\RequireOnboardingCompleted->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Laravel\\Passport\\Http\\Middleware\\CreateFreshApiToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\UpdateUserData.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\UpdateUserData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckPromo.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckPromo->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckReferral.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckReferral->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(77): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(56): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(683): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(658): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(624): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(613): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\SecureHeaders.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\SecureHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\socialtool\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\socialtool\\public\\index.php(53): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\socialtool\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#70 {main}
"} 
[2025-07-25 06:43:29] local.ERROR: Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    "error": {
        "message": "You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 {"userId":1,"exception":"[object] (GuzzleHttp\\Exception\\ClientException(code: 401): Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    \"error\": {
        \"message\": \"You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 at C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\RequestException.php:113)
[stacktrace]
#0 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(65): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response))
#1 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(204): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(153): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(48): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(248): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(224): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(269): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(226): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(62): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(182): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(95): GuzzleHttp\\Client->request('post', 'https://api.ope...', Array)
#11 C:\\xampp\\htdocs\\socialtool\\app\\Helpers\\Hashtags.php(49): GuzzleHttp\\Client->__call('post', Array)
#12 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(281): App\\Helpers\\Hashtags::getSuggested(Object(Illuminate\\Database\\Eloquent\\Collection), 'wgyery')
#13 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(42): App\\Http\\Controllers\\Json\\JsonSearchController->suggested_hashtags(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Json\\JsonSearchController->serve(Object(Illuminate\\Http\\Request), 'suggested_hasht...')
#15 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('serve', Array)
#16 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(219): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Json\\JsonSearchController), 'serve')
#17 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(176): Illuminate\\Routing\\Route->runController()
#18 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(681): Illuminate\\Routing\\Route->run()
#19 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\RequireOnboardingCompleted.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\RequireOnboardingCompleted->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Laravel\\Passport\\Http\\Middleware\\CreateFreshApiToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\UpdateUserData.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\UpdateUserData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckPromo.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckPromo->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckReferral.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckReferral->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(77): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(56): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(683): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(658): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(624): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(613): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\SecureHeaders.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\SecureHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\socialtool\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\socialtool\\public\\index.php(53): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\socialtool\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#70 {main}
"} 
[2025-07-25 06:44:44] local.ERROR: Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    "error": {
        "message": "You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 {"userId":1,"exception":"[object] (GuzzleHttp\\Exception\\ClientException(code: 401): Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    \"error\": {
        \"message\": \"You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 at C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\RequestException.php:113)
[stacktrace]
#0 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(65): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response))
#1 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(204): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(153): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(48): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(248): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(224): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(269): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(226): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(62): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(182): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(95): GuzzleHttp\\Client->request('post', 'https://api.ope...', Array)
#11 C:\\xampp\\htdocs\\socialtool\\app\\Helpers\\Hashtags.php(49): GuzzleHttp\\Client->__call('post', Array)
#12 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(281): App\\Helpers\\Hashtags::getSuggested(Object(Illuminate\\Database\\Eloquent\\Collection), 'iwueriluyrui')
#13 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(42): App\\Http\\Controllers\\Json\\JsonSearchController->suggested_hashtags(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Json\\JsonSearchController->serve(Object(Illuminate\\Http\\Request), 'suggested_hasht...')
#15 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('serve', Array)
#16 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(219): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Json\\JsonSearchController), 'serve')
#17 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(176): Illuminate\\Routing\\Route->runController()
#18 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(681): Illuminate\\Routing\\Route->run()
#19 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\RequireOnboardingCompleted.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\RequireOnboardingCompleted->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Laravel\\Passport\\Http\\Middleware\\CreateFreshApiToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\UpdateUserData.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\UpdateUserData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckPromo.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckPromo->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckReferral.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckReferral->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(77): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(56): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(683): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(658): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(624): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(613): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\SecureHeaders.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\SecureHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\socialtool\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\socialtool\\public\\index.php(53): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\socialtool\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#70 {main}
"} 
[2025-07-25 06:51:20] local.ERROR: Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    "error": {
        "message": "You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 {"userId":1,"exception":"[object] (GuzzleHttp\\Exception\\ClientException(code: 401): Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    \"error\": {
        \"message\": \"You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 at C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\RequestException.php:113)
[stacktrace]
#0 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(65): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response))
#1 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(204): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(153): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(48): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(248): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(224): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(269): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(226): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(62): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(182): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(95): GuzzleHttp\\Client->request('post', 'https://api.ope...', Array)
#11 C:\\xampp\\htdocs\\socialtool\\app\\Helpers\\Hashtags.php(49): GuzzleHttp\\Client->__call('post', Array)
#12 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(281): App\\Helpers\\Hashtags::getSuggested(Object(Illuminate\\Database\\Eloquent\\Collection), 'jghhfty')
#13 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(42): App\\Http\\Controllers\\Json\\JsonSearchController->suggested_hashtags(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Json\\JsonSearchController->serve(Object(Illuminate\\Http\\Request), 'suggested_hasht...')
#15 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('serve', Array)
#16 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(219): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Json\\JsonSearchController), 'serve')
#17 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(176): Illuminate\\Routing\\Route->runController()
#18 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(681): Illuminate\\Routing\\Route->run()
#19 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\RequireOnboardingCompleted.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\RequireOnboardingCompleted->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Laravel\\Passport\\Http\\Middleware\\CreateFreshApiToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\UpdateUserData.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\UpdateUserData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckPromo.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckPromo->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckReferral.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckReferral->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(77): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(56): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(683): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(658): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(624): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(613): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\SecureHeaders.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\SecureHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\socialtool\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\socialtool\\public\\index.php(53): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\socialtool\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#70 {main}
"} 
[2025-07-25 06:55:20] local.ERROR: Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    "error": {
        "message": "You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 {"userId":1,"exception":"[object] (GuzzleHttp\\Exception\\ClientException(code: 401): Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    \"error\": {
        \"message\": \"You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 at C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\RequestException.php:113)
[stacktrace]
#0 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(65): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response))
#1 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(204): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(153): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(48): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(248): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(224): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(269): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(226): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(62): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(182): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(95): GuzzleHttp\\Client->request('post', 'https://api.ope...', Array)
#11 C:\\xampp\\htdocs\\socialtool\\app\\Helpers\\Hashtags.php(49): GuzzleHttp\\Client->__call('post', Array)
#12 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(281): App\\Helpers\\Hashtags::getSuggested(Object(Illuminate\\Database\\Eloquent\\Collection), 'yuuryt')
#13 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(42): App\\Http\\Controllers\\Json\\JsonSearchController->suggested_hashtags(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Json\\JsonSearchController->serve(Object(Illuminate\\Http\\Request), 'suggested_hasht...')
#15 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('serve', Array)
#16 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(219): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Json\\JsonSearchController), 'serve')
#17 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(176): Illuminate\\Routing\\Route->runController()
#18 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(681): Illuminate\\Routing\\Route->run()
#19 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\RequireOnboardingCompleted.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\RequireOnboardingCompleted->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Laravel\\Passport\\Http\\Middleware\\CreateFreshApiToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\UpdateUserData.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\UpdateUserData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckPromo.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckPromo->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckReferral.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckReferral->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(77): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(56): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(683): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(658): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(624): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(613): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\SecureHeaders.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\SecureHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\socialtool\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\socialtool\\public\\index.php(53): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\socialtool\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#70 {main}
"} 
[2025-07-25 06:55:44] local.ERROR: Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    "error": {
        "message": "You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 {"userId":1,"exception":"[object] (GuzzleHttp\\Exception\\ClientException(code: 401): Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    \"error\": {
        \"message\": \"You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 at C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\RequestException.php:113)
[stacktrace]
#0 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(65): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response))
#1 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(204): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(153): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(48): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(248): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(224): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(269): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(226): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(62): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(182): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(95): GuzzleHttp\\Client->request('post', 'https://api.ope...', Array)
#11 C:\\xampp\\htdocs\\socialtool\\app\\Helpers\\Hashtags.php(49): GuzzleHttp\\Client->__call('post', Array)
#12 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(281): App\\Helpers\\Hashtags::getSuggested(Object(Illuminate\\Database\\Eloquent\\Collection), 'gurtyer')
#13 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(42): App\\Http\\Controllers\\Json\\JsonSearchController->suggested_hashtags(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Json\\JsonSearchController->serve(Object(Illuminate\\Http\\Request), 'suggested_hasht...')
#15 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('serve', Array)
#16 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(219): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Json\\JsonSearchController), 'serve')
#17 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(176): Illuminate\\Routing\\Route->runController()
#18 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(681): Illuminate\\Routing\\Route->run()
#19 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\RequireOnboardingCompleted.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\RequireOnboardingCompleted->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Laravel\\Passport\\Http\\Middleware\\CreateFreshApiToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\UpdateUserData.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\UpdateUserData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckPromo.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckPromo->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckReferral.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckReferral->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(77): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(56): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(683): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(658): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(624): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(613): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\SecureHeaders.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\SecureHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\socialtool\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\socialtool\\public\\index.php(53): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\socialtool\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#70 {main}
"} 
[2025-07-25 06:57:33] local.ERROR: Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    "error": {
        "message": "You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 {"userId":1,"exception":"[object] (GuzzleHttp\\Exception\\ClientException(code: 401): Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    \"error\": {
        \"message\": \"You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 at C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\RequestException.php:113)
[stacktrace]
#0 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(65): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response))
#1 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(204): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(153): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(48): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(248): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(224): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(269): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(226): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(62): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(182): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(95): GuzzleHttp\\Client->request('post', 'https://api.ope...', Array)
#11 C:\\xampp\\htdocs\\socialtool\\app\\Helpers\\Hashtags.php(49): GuzzleHttp\\Client->__call('post', Array)
#12 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(281): App\\Helpers\\Hashtags::getSuggested(Object(Illuminate\\Database\\Eloquent\\Collection), 'ehwiety')
#13 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(42): App\\Http\\Controllers\\Json\\JsonSearchController->suggested_hashtags(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Json\\JsonSearchController->serve(Object(Illuminate\\Http\\Request), 'suggested_hasht...')
#15 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('serve', Array)
#16 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(219): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Json\\JsonSearchController), 'serve')
#17 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(176): Illuminate\\Routing\\Route->runController()
#18 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(681): Illuminate\\Routing\\Route->run()
#19 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\RequireOnboardingCompleted.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\RequireOnboardingCompleted->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Laravel\\Passport\\Http\\Middleware\\CreateFreshApiToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\UpdateUserData.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\UpdateUserData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckPromo.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckPromo->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckReferral.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckReferral->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(77): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(56): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(683): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(658): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(624): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(613): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\SecureHeaders.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\SecureHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\socialtool\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\socialtool\\public\\index.php(53): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\socialtool\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#70 {main}
"} 
[2025-07-25 06:59:41] local.ERROR: Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    "error": {
        "message": "You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 {"userId":1,"exception":"[object] (GuzzleHttp\\Exception\\ClientException(code: 401): Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    \"error\": {
        \"message\": \"You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 at C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\RequestException.php:113)
[stacktrace]
#0 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(65): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response))
#1 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(204): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(153): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(48): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(248): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(224): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(269): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(226): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(62): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(182): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(95): GuzzleHttp\\Client->request('post', 'https://api.ope...', Array)
#11 C:\\xampp\\htdocs\\socialtool\\app\\Helpers\\Hashtags.php(49): GuzzleHttp\\Client->__call('post', Array)
#12 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(281): App\\Helpers\\Hashtags::getSuggested(Object(Illuminate\\Database\\Eloquent\\Collection), 'jgyjhttyd')
#13 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(42): App\\Http\\Controllers\\Json\\JsonSearchController->suggested_hashtags(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Json\\JsonSearchController->serve(Object(Illuminate\\Http\\Request), 'suggested_hasht...')
#15 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('serve', Array)
#16 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(219): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Json\\JsonSearchController), 'serve')
#17 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(176): Illuminate\\Routing\\Route->runController()
#18 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(681): Illuminate\\Routing\\Route->run()
#19 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\RequireOnboardingCompleted.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\RequireOnboardingCompleted->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Laravel\\Passport\\Http\\Middleware\\CreateFreshApiToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\UpdateUserData.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\UpdateUserData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckPromo.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckPromo->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckReferral.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckReferral->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(77): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(56): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(683): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(658): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(624): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(613): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\SecureHeaders.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\SecureHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\socialtool\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\socialtool\\public\\index.php(53): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\socialtool\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#70 {main}
"} 
[2025-07-25 07:00:26] local.ERROR: Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    "error": {
        "message": "You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 {"userId":1,"exception":"[object] (GuzzleHttp\\Exception\\ClientException(code: 401): Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    \"error\": {
        \"message\": \"You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 at C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\RequestException.php:113)
[stacktrace]
#0 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(65): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response))
#1 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(204): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(153): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(48): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(248): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(224): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(269): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(226): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(62): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(182): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(95): GuzzleHttp\\Client->request('post', 'https://api.ope...', Array)
#11 C:\\xampp\\htdocs\\socialtool\\app\\Helpers\\Hashtags.php(49): GuzzleHttp\\Client->__call('post', Array)
#12 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(281): App\\Helpers\\Hashtags::getSuggested(Object(Illuminate\\Database\\Eloquent\\Collection), 'jgyjhttyd')
#13 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(42): App\\Http\\Controllers\\Json\\JsonSearchController->suggested_hashtags(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Json\\JsonSearchController->serve(Object(Illuminate\\Http\\Request), 'suggested_hasht...')
#15 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('serve', Array)
#16 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(219): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Json\\JsonSearchController), 'serve')
#17 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(176): Illuminate\\Routing\\Route->runController()
#18 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(681): Illuminate\\Routing\\Route->run()
#19 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\RequireOnboardingCompleted.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\RequireOnboardingCompleted->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Laravel\\Passport\\Http\\Middleware\\CreateFreshApiToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\UpdateUserData.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\UpdateUserData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckPromo.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckPromo->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckReferral.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckReferral->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(77): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(56): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(683): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(658): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(624): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(613): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\SecureHeaders.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\SecureHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\socialtool\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\socialtool\\public\\index.php(53): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\socialtool\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#70 {main}
"} 
[2025-07-25 07:02:04] local.ERROR: Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    "error": {
        "message": "You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 {"userId":1,"exception":"[object] (GuzzleHttp\\Exception\\ClientException(code: 401): Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    \"error\": {
        \"message\": \"You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 at C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\RequestException.php:113)
[stacktrace]
#0 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(65): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response))
#1 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(204): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(153): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(48): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(248): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(224): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(269): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(226): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(62): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(182): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(95): GuzzleHttp\\Client->request('post', 'https://api.ope...', Array)
#11 C:\\xampp\\htdocs\\socialtool\\app\\Helpers\\Hashtags.php(49): GuzzleHttp\\Client->__call('post', Array)
#12 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(281): App\\Helpers\\Hashtags::getSuggested(Object(Illuminate\\Database\\Eloquent\\Collection), 'ytyertret')
#13 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(42): App\\Http\\Controllers\\Json\\JsonSearchController->suggested_hashtags(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Json\\JsonSearchController->serve(Object(Illuminate\\Http\\Request), 'suggested_hasht...')
#15 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('serve', Array)
#16 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(219): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Json\\JsonSearchController), 'serve')
#17 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(176): Illuminate\\Routing\\Route->runController()
#18 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(681): Illuminate\\Routing\\Route->run()
#19 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\RequireOnboardingCompleted.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\RequireOnboardingCompleted->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Laravel\\Passport\\Http\\Middleware\\CreateFreshApiToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\UpdateUserData.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\UpdateUserData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckPromo.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckPromo->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckReferral.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckReferral->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(77): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(56): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(683): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(658): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(624): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(613): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\SecureHeaders.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\SecureHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\socialtool\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\socialtool\\public\\index.php(53): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\socialtool\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#70 {main}
"} 
[2025-07-25 07:05:58] local.ERROR: Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    "error": {
        "message": "You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 {"userId":1,"exception":"[object] (GuzzleHttp\\Exception\\ClientException(code: 401): Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    \"error\": {
        \"message\": \"You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 at C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\RequestException.php:113)
[stacktrace]
#0 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(65): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response))
#1 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(204): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(153): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(48): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(248): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(224): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(269): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(226): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(62): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(182): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(95): GuzzleHttp\\Client->request('post', 'https://api.ope...', Array)
#11 C:\\xampp\\htdocs\\socialtool\\app\\Helpers\\Hashtags.php(49): GuzzleHttp\\Client->__call('post', Array)
#12 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(281): App\\Helpers\\Hashtags::getSuggested(Object(Illuminate\\Database\\Eloquent\\Collection), '456')
#13 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(42): App\\Http\\Controllers\\Json\\JsonSearchController->suggested_hashtags(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Json\\JsonSearchController->serve(Object(Illuminate\\Http\\Request), 'suggested_hasht...')
#15 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('serve', Array)
#16 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(219): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Json\\JsonSearchController), 'serve')
#17 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(176): Illuminate\\Routing\\Route->runController()
#18 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(681): Illuminate\\Routing\\Route->run()
#19 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\RequireOnboardingCompleted.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\RequireOnboardingCompleted->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Laravel\\Passport\\Http\\Middleware\\CreateFreshApiToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\UpdateUserData.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\UpdateUserData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckPromo.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckPromo->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckReferral.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckReferral->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(77): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(56): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(683): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(658): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(624): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(613): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\SecureHeaders.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\SecureHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\socialtool\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\socialtool\\public\\index.php(53): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\socialtool\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#70 {main}
"} 
[2025-07-25 08:13:14] local.ERROR: Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    "error": {
        "message": "You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 {"userId":1,"exception":"[object] (GuzzleHttp\\Exception\\ClientException(code: 401): Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    \"error\": {
        \"message\": \"You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 at C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\RequestException.php:113)
[stacktrace]
#0 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(65): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response))
#1 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(204): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(153): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(48): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(248): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(224): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(269): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(226): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(62): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(182): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(95): GuzzleHttp\\Client->request('post', 'https://api.ope...', Array)
#11 C:\\xampp\\htdocs\\socialtool\\app\\Helpers\\Hashtags.php(49): GuzzleHttp\\Client->__call('post', Array)
#12 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(281): App\\Helpers\\Hashtags::getSuggested(Object(Illuminate\\Database\\Eloquent\\Collection), 'wwr')
#13 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(42): App\\Http\\Controllers\\Json\\JsonSearchController->suggested_hashtags(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Json\\JsonSearchController->serve(Object(Illuminate\\Http\\Request), 'suggested_hasht...')
#15 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('serve', Array)
#16 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(219): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Json\\JsonSearchController), 'serve')
#17 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(176): Illuminate\\Routing\\Route->runController()
#18 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(681): Illuminate\\Routing\\Route->run()
#19 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\RequireOnboardingCompleted.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\RequireOnboardingCompleted->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Laravel\\Passport\\Http\\Middleware\\CreateFreshApiToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\UpdateUserData.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\UpdateUserData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckPromo.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckPromo->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckReferral.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckReferral->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(77): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(56): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(683): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(658): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(624): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(613): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\SecureHeaders.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\SecureHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\socialtool\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\socialtool\\public\\index.php(53): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\socialtool\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#70 {main}
"} 
[2025-07-25 08:16:35] local.ERROR: Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    "error": {
        "message": "You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 {"userId":1,"exception":"[object] (GuzzleHttp\\Exception\\ClientException(code: 401): Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    \"error\": {
        \"message\": \"You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 at C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\RequestException.php:113)
[stacktrace]
#0 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(65): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response))
#1 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(204): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(153): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(48): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(248): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(224): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(269): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(226): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(62): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(182): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(95): GuzzleHttp\\Client->request('post', 'https://api.ope...', Array)
#11 C:\\xampp\\htdocs\\socialtool\\app\\Helpers\\Hashtags.php(49): GuzzleHttp\\Client->__call('post', Array)
#12 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(281): App\\Helpers\\Hashtags::getSuggested(Object(Illuminate\\Database\\Eloquent\\Collection), 'errtrt')
#13 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(42): App\\Http\\Controllers\\Json\\JsonSearchController->suggested_hashtags(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Json\\JsonSearchController->serve(Object(Illuminate\\Http\\Request), 'suggested_hasht...')
#15 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('serve', Array)
#16 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(219): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Json\\JsonSearchController), 'serve')
#17 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(176): Illuminate\\Routing\\Route->runController()
#18 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(681): Illuminate\\Routing\\Route->run()
#19 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\RequireOnboardingCompleted.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\RequireOnboardingCompleted->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Laravel\\Passport\\Http\\Middleware\\CreateFreshApiToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\UpdateUserData.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\UpdateUserData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckPromo.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckPromo->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckReferral.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckReferral->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(77): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(56): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(683): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(658): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(624): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(613): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\SecureHeaders.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\SecureHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\socialtool\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\socialtool\\public\\index.php(53): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\socialtool\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#70 {main}
"} 
[2025-07-25 08:21:57] local.ERROR: Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    "error": {
        "message": "You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 {"userId":1,"exception":"[object] (GuzzleHttp\\Exception\\ClientException(code: 401): Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    \"error\": {
        \"message\": \"You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 at C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\RequestException.php:113)
[stacktrace]
#0 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(65): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response))
#1 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(204): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(153): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(48): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(248): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(224): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(269): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(226): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(62): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(182): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(95): GuzzleHttp\\Client->request('post', 'https://api.ope...', Array)
#11 C:\\xampp\\htdocs\\socialtool\\app\\Helpers\\Hashtags.php(49): GuzzleHttp\\Client->__call('post', Array)
#12 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(281): App\\Helpers\\Hashtags::getSuggested(Object(Illuminate\\Database\\Eloquent\\Collection), 'hkjh')
#13 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(42): App\\Http\\Controllers\\Json\\JsonSearchController->suggested_hashtags(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Json\\JsonSearchController->serve(Object(Illuminate\\Http\\Request), 'suggested_hasht...')
#15 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('serve', Array)
#16 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(219): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Json\\JsonSearchController), 'serve')
#17 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(176): Illuminate\\Routing\\Route->runController()
#18 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(681): Illuminate\\Routing\\Route->run()
#19 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\RequireOnboardingCompleted.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\RequireOnboardingCompleted->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Laravel\\Passport\\Http\\Middleware\\CreateFreshApiToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\UpdateUserData.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\UpdateUserData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckPromo.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckPromo->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckReferral.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckReferral->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(77): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(56): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(683): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(658): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(624): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(613): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\SecureHeaders.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\SecureHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\socialtool\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\socialtool\\public\\index.php(53): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\socialtool\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#70 {main}
"} 
[2025-07-25 08:22:25] local.ERROR: Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    "error": {
        "message": "You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 {"userId":1,"exception":"[object] (GuzzleHttp\\Exception\\ClientException(code: 401): Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    \"error\": {
        \"message\": \"You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 at C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\RequestException.php:113)
[stacktrace]
#0 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(65): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response))
#1 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(204): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(153): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(48): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(248): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(224): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(269): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(226): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(62): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(182): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(95): GuzzleHttp\\Client->request('post', 'https://api.ope...', Array)
#11 C:\\xampp\\htdocs\\socialtool\\app\\Helpers\\Hashtags.php(49): GuzzleHttp\\Client->__call('post', Array)
#12 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(281): App\\Helpers\\Hashtags::getSuggested(Object(Illuminate\\Database\\Eloquent\\Collection), 'rtww')
#13 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(42): App\\Http\\Controllers\\Json\\JsonSearchController->suggested_hashtags(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Json\\JsonSearchController->serve(Object(Illuminate\\Http\\Request), 'suggested_hasht...')
#15 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('serve', Array)
#16 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(219): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Json\\JsonSearchController), 'serve')
#17 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(176): Illuminate\\Routing\\Route->runController()
#18 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(681): Illuminate\\Routing\\Route->run()
#19 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\RequireOnboardingCompleted.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\RequireOnboardingCompleted->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Laravel\\Passport\\Http\\Middleware\\CreateFreshApiToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\UpdateUserData.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\UpdateUserData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckPromo.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckPromo->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckReferral.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckReferral->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(77): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(56): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(683): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(658): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(624): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(613): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\SecureHeaders.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\SecureHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\socialtool\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\socialtool\\public\\index.php(53): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\socialtool\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#70 {main}
"} 
[2025-07-25 08:25:55] local.ERROR: Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    "error": {
        "message": "You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 {"userId":1,"exception":"[object] (GuzzleHttp\\Exception\\ClientException(code: 401): Client error: `POST https://api.openai.com/v1/chat/completions` resulted in a `401 Unauthorized` response:
{
    \"error\": {
        \"message\": \"You didn't provide an API key. You need to provide your API key in an Authorization (truncated...)
 at C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Exception\\RequestException.php:113)
[stacktrace]
#0 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(65): GuzzleHttp\\Exception\\RequestException::create(Object(GuzzleHttp\\Psr7\\Request), Object(GuzzleHttp\\Psr7\\Response))
#1 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(204): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Response))
#2 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(153): GuzzleHttp\\Promise\\Promise::callHandler(1, Object(GuzzleHttp\\Psr7\\Response), NULL)
#3 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\TaskQueue.php(48): GuzzleHttp\\Promise\\Promise::GuzzleHttp\\Promise\\{closure}()
#4 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(248): GuzzleHttp\\Promise\\TaskQueue->run(true)
#5 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(224): GuzzleHttp\\Promise\\Promise->invokeWaitFn()
#6 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(269): GuzzleHttp\\Promise\\Promise->waitIfPending()
#7 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(226): GuzzleHttp\\Promise\\Promise->invokeWaitList()
#8 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\promises\\src\\Promise.php(62): GuzzleHttp\\Promise\\Promise->waitIfPending()
#9 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(182): GuzzleHttp\\Promise\\Promise->wait()
#10 C:\\xampp\\htdocs\\socialtool\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(95): GuzzleHttp\\Client->request('post', 'https://api.ope...', Array)
#11 C:\\xampp\\htdocs\\socialtool\\app\\Helpers\\Hashtags.php(49): GuzzleHttp\\Client->__call('post', Array)
#12 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(281): App\\Helpers\\Hashtags::getSuggested(Object(Illuminate\\Database\\Eloquent\\Collection), 'kjdhilurf')
#13 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Controllers\\Json\\JsonSearchController.php(42): App\\Http\\Controllers\\Json\\JsonSearchController->suggested_hashtags(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Json\\JsonSearchController->serve(Object(Illuminate\\Http\\Request), 'suggested_hasht...')
#15 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('serve', Array)
#16 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(219): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Json\\JsonSearchController), 'serve')
#17 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(176): Illuminate\\Routing\\Route->runController()
#18 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(681): Illuminate\\Routing\\Route->run()
#19 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\RequireOnboardingCompleted.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\RequireOnboardingCompleted->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\passport\\src\\Http\\Middleware\\CreateFreshApiToken.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Laravel\\Passport\\Http\\Middleware\\CreateFreshApiToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\UpdateUserData.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\UpdateUserData->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckPromo.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckPromo->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\CheckReferral.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\CheckReferral->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(77): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\AuthenticateSession.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\AuthenticateSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(56): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(683): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(658): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(624): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(613): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(170): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(130): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\socialtool\\app\\Http\\Middleware\\SecureHeaders.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): App\\Http\\Middleware\\SecureHeaders->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\socialtool\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(171): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(105): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 C:\\xampp\\htdocs\\socialtool\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(110): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\socialtool\\public\\index.php(53): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 C:\\xampp\\htdocs\\socialtool\\server.php(21): require_once('C:\\\\xampp\\\\htdocs...')
#70 {main}
"} 
