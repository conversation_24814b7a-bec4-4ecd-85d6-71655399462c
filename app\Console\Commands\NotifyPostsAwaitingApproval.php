<?php

namespace App\Console\Commands;

use App\Notifications\PostsAwaitingApproval;
use App\Post;
use App\Team;
use App\User;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class NotifyPostsAwaitingApproval extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'notify:posts_awaiting_approval';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Awaiting approval notification for posts';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        // fetch teams who have posts awaiting approval
        Team::with(['accounts:id', 'members',])->whereHas('accounts.posts', function ($query) {
            /** @var Builder $query */
            $query->where('posts.approved', false);
        })->chunk(200, function($teams){
            /** @var Team[]|Collection $teams */

            $teams->each(function(/** @var Team $team */ $team){

                $accountIds = $team->accounts->map(function($a){return $a->id;})->toArray();

                $lastMaxId = $team->getOption('approval.last_max_post_id');

                /** @var Builder $query */
                $query = Post::awaitingApproval()
                    ->whereIn('account_id', $accountIds)
                    ->when($lastMaxId, function($query, $v){
                        /** @var Builder $query */
                        return $query->where('id', '>', $v);
                    });

                $maxId = $query->max('id');

                if(!$maxId){
                    $this->line('no new post to send notification for team: ' . $team->name);
                    return;
                }

                $awaitingApprovalCount = $query->count();

                /** @var User[] $users */
                $users = $team->getApprovers();

                foreach ($users as $user){
                    $user->notify(new PostsAwaitingApproval($team, $awaitingApprovalCount));
                }

                // set the max id so we only check for posts after this post to send the notification (to prevent duplicate notifs)
                $team->setOption('approval.last_max_post_id', $maxId);

                $this->line('notified ' . count($users) . ' users for team: ' . $team->name);
            });

        });

    }
}
