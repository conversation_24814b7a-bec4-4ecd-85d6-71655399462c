<template>
    <div class="post rounded mt-4">
        <div class="d-flex flex-column">
            <div class="d-flex justify-content-between align-items-start">
                <div class="d-flex account-info">
                    <div class="position-relative mr-3">
                        <img class="avatar-sm rounded-circle" :src="account.image" />
                    </div>
                    <div class="d-flex flex-column">
                        <div class="account-name font-weight-500">{{ account.name.length > 22 ? account.name.substring(0,22).concat("...") : account.name }}</div>
                        <div class="post-date">Just now</div>
                    </div>
                </div>
                <div class="d-flex align-items-center mr-3">
                    <div class="mr-4">
                        <i class="ph ph-share-network ph-fill ph-md icon" aria-hidden="true"></i>
                    </div>
                    <div>
                        <i class="ph ph-dots-three-outline-vertical ph-fill ph-md icon" aria-hidden="true"></i>
                    </div>                
                </div>
            </div>
            <div v-if="attachments.length">
                <div class="w-100">
                    <video controls :title="attachments[0].name" v-if="attachments[0].type.includes('video')">
                        <source :src="attachments[0].url" />
                    </video>
                    <img :src="attachments[0].url" :alt="attachments[0].name" :title="attachments[0].name" class="w-100"
                        v-else/>
                </div>
            </div>
            <div v-if="options.topic_type === 'OFFER'" class="offer mt-4 mr-4 ml-4 mb-4">
                <div class="offer-title d-flex" v-if="options.event_title">
                    <div>
                        {{options.event_title}}
                    </div>
                    <span>
                        <svg class="offer-badge" focusable="false" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M21.41 11.58l-9-9C12.05 2.22 11.55 2 11 2H4c-1.1 0-2 .9-2 2v7c0 .55.22 1.05.59 1.42l9 9c.36.36.86.58 1.41.58.55 0 1.05-.22 1.41-.59l7-7c.37-.36.59-.86.59-1.41 0-.55-.23-1.06-.59-1.42zM5.5 7C4.67 7 4 6.33 4 5.5S4.67 4 5.5 4 7 4.67 7 5.5 6.33 7 5.5 7z"></path></svg>
                    </span>
                </div>
                <div v-if="options.event_start"> Valid {{eventStartDate}} - {{eventEndDate}}</div> 
                <p class="details-text mt-4" v-html="content"></p>
                <div class='details-card text-center' v-if="options.offer_coupon">
                    <div class="offer-msg">Show this code at the store</div>
                    <div class="offer-coupon">{{options.offer_coupon}}</div>
                    <div class="offer-date">Valid {{options.event_start}} - {{options.event_end}}</div>
                </div>
                <div class="offer-redeem mt-2 mb-2" v-if="options.offer_link">
                    <a href="#" @click.prevent>REDEEM ONLINE</a>
                </div>
                <div class="offer-terms bt-1" v-if="options.offer_terms">
                    {{options.offer_terms}}
                </div>
            </div>
            <div v-else-if="options.topic_type === 'EVENT'" class="event mt-4 mr-4 ml-4">
                <div class="event-title">{{options.event_title}}</div>
                <div v-if="options.event_start"> {{eventStartDate}} - {{eventEndDate}}</div> 
                <p class="event-details mt-4" v-html="content"></p>
            </div>
            <div v-else>
                <RichText class="content mb-0" style="min-height: 8px;" placeholder="Write something..."
                :value="content"
                :readonly="true" :marks="richTextMarksSchema"/>
        </div>
            <div v-if="options.call_to_action" class="d-flex justify-content-start">
                <button class="btn btn-primary btn-sm action-button" >{{options.call_to_action.replace('_', ' ')}}</button>
            </div>
        </div>
    </div>
</template>
<script>
import RichText from "../../common/RichText.vue";
import { sanitizeHtml} from "../../../../components";

export default {
    name: 'GBPPost',
    props: ['account', 'text', 'attachments', 'link', 'options'],
    components:{RichText},
    data(){
        return{
            showTerms: false,
            showFull:false,

        }
    },
    computed:{
        content(){
            let text = this.text.replace(/(\r\n|\r|\n){2,}/g, "$1\n"); // normalize linebreaks
            return sanitizeHtml (this.showFull ? text : text.length >= 110 ? (`${text.substring(0, 110)}<span class="_see_all cursor-pointer" style="color:#666666;">...see more</span>`) : text);
        },
        richTextMarksSchema() {
            return {
                span: {
                    attrs: {
                        className: {}, // Define class attribute for the span
                        styles:{default:null}
                    },
                    inclusive: false,
                    parseDOM: [
                        {
                            tag: "span[class]", // Match span elements with a class attribute
                            getAttrs: dom => {
                                const className = dom.getAttribute('class');
                                const styles = dom.getAttribute('style');
                                return { className, styles };
                            }
                        }
                    ],
                    toDOM(node) {
                        const { className, styles } = node.attrs;
                        return ["span", { class: className, style: styles }, 0];
                    }
                }
            }
        },
        eventStartDate(){
            return this.$momentUTC(this.options.event_start, 'DD/MM/YYYY HH:mm a').local().format('MMM D')
        },
        eventEndDate(){
            return this.$momentUTC(this.options.event_end, 'DD/MM/YYYY HH:mm a').local().format('MMM D')
        }
    },
    methods:{
        showMore() {
            this.showFull = true;
            return false;
        },
    },
    mounted() {
        $(document).on("click.see_all", "._see_all", this.showMore);
    },
    beforeDestroy() {
        $(document).off("click.see_all", "._see_all", this.showMore);
    }
}
</script>
<style lang="scss" scoped>
.post{
    font-family: Roboto,RobotoDraft,Helvetica,Arial,sans-serif;
    color: #000000;
    position:relative;
    .account-info{
        padding: 0px 18px 8px 18px;
    }
    .account-name{
        line-height: 24px;
    }
    .icon{
        color: #666666;
    }
    .post-date{
        color: #666666 !important;
        font-size: 14px;
        line-height: 21px;
    }
    .date{
        font-size: 12px;
        line-height: 1.34;
    }
    .content{
        overflow-wrap: break-word;
        word-break: break-word;
        line-height: 24px;
        padding: 20px 20px 20px 18px;
        a {
            color: #1967d2;
            &:hover{
                text-decoration: underline !important;
            }
        }
    }
    .action-button{
        margin: 18px 0 24px 16px;
    }
}
.offer{
    .details-card{
        background-color: #f8f9fa;
        border: 2px dashed #dadce0;
        border-radius: 6px;
        margin-top: 12px;
        padding-bottom: 15px;
        padding-left: 5px;
        padding-right: 5px;
        padding-top: 15px;
    }
    .offer-badge{
        fill:#f4b400;
        width: 24px;
        height: 24px;
        position: absolute;
        bottom: 3px;
    }
    .offer-terms{
        color: #bdbdbd;
        font-size: 11px;
        line-height: 14px;
        overflow: hidden;
        overflow-wrap: break-word;
        word-wrap: break-word;
    }
    .offer-title{
        font-size: 18px;
        line-height: 30px;
        color: #202124;
        position: relative;
    }
    .details-text{
        font-size: 14px;
        line-height: 24px;
        margin-top: 0px;
        white-space: pre-wrap;
    }
    .code-msg{
        color: #8c8e94;
    }
    .offer-coupon{
        color: #202124;
        font-family: arial,sans-serif-medium,sans-serif;
        font-size: 24px;
        letter-spacing: 0.12px;
    }
    .offer-date{
        color: #202124;
        font-size: 12px;
    }
    .offer-redeem{
        a {
            color: #1967d2;
            &:hover{
                text-decoration: underline !important;
            }
        }
    }
}
.event{
    .event-title{
        font-size: 18px;
        line-height: 30px;
        color: #202124;
    }
    .event-details{
        color:#8c8e94;
    }
}

</style>
