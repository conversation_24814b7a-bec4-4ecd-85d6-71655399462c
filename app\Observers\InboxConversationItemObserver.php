<?php

namespace App\Observers;

use App\InboxConversationItem;

class InboxConversationItemObserver
{
    public function deleting(InboxConversationItem $item){
        // delete attachments if any
        $attachments = $item->attachments;

        if(is_array($attachments) && count($attachments) > 0){
            foreach ($attachments as $attachment) {
                if(\Storage::exists($attachment['path']))
                    \Storage::delete($attachment['path']);
            }
        }

        return true;
    }
}
