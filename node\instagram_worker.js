const Koa = require("koa");
const bodyParser = require("koa-bodyparser");
const {
    AndroidIgpa<PERSON>,
    Account<PERSON>oginCommand,
    IgLoginTwoFactorRequiredError,
    TwoFactorLoginCommand,
    SendTwoFactorLoginSmsCommand,
    IgCheckpointError,
    DeltaAcknowledgeApprovedChallengeScreen,
    SelectContactPointChallengeScreen,
    VerifyCodeChallengeScreen,
    VoidChallengeScreen,
    UserInfoCommand,
    TwoFactorCheckTrustedNotificationStatusCommand,
    IgLoginBadPasswordError,
    IgLoginInvalidUserError,
} = require("@igpapi/android");

const log = require("./logger")("instagram");

const shouldRetry = require("./ig").shouldRetry;

const PORT = 3601;
let _server = null;

const sessions = {};

function cleanupSession(username, kill = false) {
    // if there is no session, dont do anything
    if (!sessions[username]) return;

    // clear the old timer
    clearTimeout(sessions[username].timer);

    const killSession = () => {
        delete sessions[username];
        log(`[${username}] session killed`);
    };

    if (kill) {
        killSession();
    } else {
        // set to auto kill after 30 mins - 30 mins because sometimes user may need to get the code from a third party
        return (sessions[username].timer = setTimeout(() => {
            // auto kill
            cleanupSession(username, true);
        }, 30 * 60 * 1000));
    }
}

const wait = ms =>
    new Promise((res, rej) => {
        setTimeout(res, ms);
    });

module.exports = {
    async httpLogin({ user_id, username, password, account_id = null, verify_code = null, proxy = null }) {

        if(verify_code === 0){
            verify_code = "0";
        } else if(verify_code) {
            // make sure verify code is string
            verify_code = verify_code + "";
        }

        const getSession = () => sessions[username];
        const setSession = obj => {
            sessions[username] = { ...sessions[username], ...obj };
            cleanupSession(username);
        };

        if (process.env.TEST || username === "code_error") {
            // for testing the flow
            user_id = "TEST";
            log("testing mode");
        }

        if (!sessions[username]) {
            // create session if not exists
            log(`[${username}] creating session`);
            sessions[username] = {
                ig: null,
                exception: null,
                challenge: null,
                timer: cleanupSession(username),
                stage: "init",
            };
        }

        const responses = {
            code_required(){
                return {
                    success: false,
                    requiresCode: true,
                    message: "A code is already sent to you. Please enter the code",
                };
            },
            ["2fa_pending_trusted_notification"](){
                return {
                    success: false,
                    requiresCode: true,
                    message: "Please approve our login request and enter 1 as the code. You may see the request on the mobile app or website where you are already logged-in to Instagram",
                };
            },
            ["2fa_sms_code"]() {
                return {
                    success: false,
                    requiresCode: true,
                    message: "Enter the 2-factor authentication code sent to the phone number associated with the Instagram account"
                };
            },
            ["2fa_app_code"]() {
                return {
                    success: false,
                    requiresCode: true,
                    message: "Enter the 2-factor authentication code from the authenticator app"
                };
            },
            ["2fa_whatsapp_code"]() {
                return {
                    success: false,
                    requiresCode: true,
                    message: "Enter the 2-factor authentication code sent to the WhatsApp number associated with the Instagram account"
                };
            },
            sms_code() {
                return {
                    success: false,
                    requiresCode: true,
                    message: "Enter the code sent to the phone number associated with the Instagram account"
                };
            },
            email_code() {
                return {
                    success: false,
                    requiresCode: true,
                    message: "Enter the code sent to the email address associated with the Instagram account"
                };
            },
            error(line) {
                return {
                    success: false,
                    error: line
                };
            },
            ok(obj = {}) {
                // called on successful login with data to return
                // also kill session
                log(`[${username}] login ok`);
                cleanupSession(username, true);
                return {
                    success: true,
                    ... obj
                };
            }
        };

        const handleChallenge = async ig => {
            let loops = 1;
            while(true){

                if(loops >= 20){
                    return responses.error("Something went wrong. Error #289");
                } else {
                    ++loops;
                }

                // resolve all challenges
                const challenge = await ig.challenge.router.resolve();

                log("got challenge", challenge.constructor.name);
                log("challenge state", challenge.state);

                if(challenge.state.user_id){
                    // save in session because we are unable to extract it in case of challenge
                    setSession({
                        challenge_user_id: challenge.state.user_id
                    });
                }

                if(challenge instanceof VoidChallengeScreen){
                    // all challenges are resolved
                    break;
                }

                if(challenge instanceof DeltaAcknowledgeApprovedChallengeScreen){
                    await challenge.submit();
                    continue;
                }

                if (challenge instanceof SelectContactPointChallengeScreen) {
                    const contactPoint = challenge.state.step_data.choice;
                    await challenge.submit(contactPoint);
                    continue;
                }

                if (challenge instanceof VerifyCodeChallengeScreen) {
                    setSession({
                        stage: "code",
                        ig,
                        exception: null,
                        challenge,
                    })
                    if(challenge.state.step_name === "verify_code" || challenge.state.step_name === "verify_sms_code"){
                        return responses.sms_code();
                    } else if(challenge.state.step_name === "verify_email" || challenge.state.step_name === "verify_email_code"){
                        return responses.email_code();
                    } else {
                        return responses.code_required();
                    }
                }

                // if we are here, we are getting probably a challenge not handled above
                cleanupSession(username, true);
                return responses.error("Something went wrong. Error #189: " + challenge.constructor.name + ". Most likely, you have a checkpoint to clear on Instagram. Please open Instagram on your mobile and clear any checkpoint if needed. If the problem persists, contact us.");

            }
        };
        const handle2FA = async (ig, twoFactorInfo) => {

            log(`[${username}] 2FA required`, twoFactorInfo);

            setSession({
                two_factor_info: twoFactorInfo,
            });

            if(twoFactorInfo.totp_two_factor_on){
                log(`[${username}] 2FA totp`);
                setSession({
                    stage: "code",
                    ig: ig,
                    challenge: null,
                    verificationMethod: "0"
                });
                return responses["2fa_app_code"]();
            } else if(twoFactorInfo.whatsapp_two_factor_on || twoFactorInfo.sms_two_factor_on){
                if(twoFactorInfo.pending_trusted_notification){
                    // no code was sent, so we force sending a code
                    return sendTwoFA(ig);
                } else {
                    log(`[${username}] 2FA sms code is sent by IG`);
                    const verificationMethod = twoFactorInfo.whatsapp_two_factor_on ? "6" : "1";
                    setSession({
                        stage: "code",
                        ig: ig,
                        challenge: null,
                        verificationMethod
                    });
                    return verificationMethod === "6" ? responses["2fa_whatsapp_code"]() : responses["2fa_sms_code"]();
                }
            } else {
                log(`[${username}] 2FA unknown`);
                return responses.error(e.message);
            }

        };
        const handleException = async (ig, e) => {
            if (e instanceof IgLoginTwoFactorRequiredError) {
                return await handle2FA(ig, e.response.body.two_factor_info);
            } else if(e instanceof IgCheckpointError) {
                const ret = await handleChallenge(ig);
                if(ret){
                    return ret;
                }
            } else {
                return responses.error(e.message);
            }
        };
        const sendTwoFA = async ig => {
            log(`[${username}] sending 2fa code via sms`);
            // resend sms code
            const response = await ig.execute(SendTwoFactorLoginSmsCommand, {
                twoFactorIdentifier: getSession().two_factor_info.two_factor_identifier,
                username: getSession().two_factor_info.username,
            });
            if(response.two_factor_info){
                log(`[${username}] 2FA code sent to phone`, response.two_factor_info);
                // it's always sent to sms, so we always should use sms verification method
                const verificationMethod = "1";
                setSession({
                    two_factor_info: response.two_factor_info,
                    stage: "code",
                    ig: ig,
                    challenge: null,
                    verificationMethod
                });
                return verificationMethod === "6" ? responses["2fa_whatsapp_code"]() : responses["2fa_sms_code"]();
            } else {
                cleanupSession(username, true);
                return responses.error("Unexpected response received when requesting 2FA sms code. Make sure you have 2FA via SMS enabled and try again after a few minutes.");
            }
        };
        const getUserData = async (ig, userId, loggedInRes = null) => {
            if(!loggedInRes) {
                const { user } = await ig.execute(UserInfoCommand, {userId});
                loggedInRes = user;
            }

            log(`[${username}] user info success`);

            // login done
            return {
                state: ig.state,
                proxy: proxy,
                uid: loggedInRes.pk,
                image: loggedInRes.profile_pic_url,
            };
        };

        if (!verify_code) {
            // check if we expect a code
            if (getSession().stage === "code") {
                log(`[${username}] verify code required but not provided`);
                return responses.code_required();
            }

            const ig = new AndroidIgpapi();
            ig.http.setTimeout(60000 * 5);

            // set proxy if needed
            if (proxy){
                ig.state.proxyUrl = "http://" + proxy;
                log(`[${username}] proxy = ${ig.state.proxyUrl}`);
            }

            try {
                log(`[${username}] login start`);
                const loggedInRes = await ig.execute(AccountLoginCommand, { username, password });
                try {
                    // login done
                    return responses.ok(await getUserData(ig, loggedInRes.pk, loggedInRes));
                } catch (e) {
                    log(e);
                    return responses.error(e.message);
                }
            } catch (e) {

                if(e instanceof IgLoginBadPasswordError){
                    log(`[${username}] bad password`);
                    return responses.error("User error: The password you entered is incorrect. Please try again.");
                } else if(e instanceof IgLoginInvalidUserError){
                    log(`[${username}] invalid user`);
                    return responses.error("User error: The username you entered is incorrect. Please try again.");
                }

                const ret = await handleException(ig, e);
                if(ret){
                    return ret;
                }
            }

            log(`[${username}] login success`);

            try {
                // login done
                return responses.ok(await getUserData(ig, ig.state.extractUserId()));
            } catch (e) {
                log(e);
                return responses.error(e.message);
            }

        } else {
            log(`[${username}] verify code is ${verify_code}`);

            if (getSession().stage !== "code") {
                log(`[${username}] verify code not expected`);
                // end the session too
                cleanupSession(username, true);
                return {
                    success: false,
                    error: "Session expired (#141). Please try again"
                };
            }

            cleanupSession(username);

            try {
                log(`[${username}] verifying code`);

                const ig = getSession().ig;

                if(getSession().challenge){

                    if(verify_code === "resend"){
                        return responses.error("This can only be used for 2FA codes. This is normal login verification and not 2FA.");
                    }

                    log(`[${username}] submitting challenge code`);

                    // verify code for a challenge checkpoint
                    await getSession().challenge.submit(verify_code);

                    const ret = await handleChallenge(ig);
                    if(ret){
                        return ret;
                    }

                    log(`[${username}] challenge success`);
                    // if we are here, we are good

                    // fetch user details
                    try {
                        // login done
                        return responses.ok(await getUserData(ig, getSession().challenge_user_id ? getSession().challenge_user_id : ig.state.extractUserId()));
                    } catch (e) {
                        log(e);
                        return responses.error(e.message);
                    }

                } else {

                    log(`[${username}] need to pass 2fa`);

                    let loginResponse;
                    const two_factor_info = getSession().two_factor_info;
                    if(two_factor_info.pending_trusted_notification){
                        log(`[${username}] checking 2fa approval request first`);
                        let response;
                        try {
                            response = await ig.execute(TwoFactorCheckTrustedNotificationStatusCommand, {
                                twoFactorIdentifier: getSession().two_factor_info.two_factor_identifier,
                                username: getSession().two_factor_info.username,
                            });
                            log(`[${username}] 2fa check trusted response`, response);
                        } catch (e){
                            log(e);
                            response = {
                                review_status: 2
                            };
                        }
                        if(response.review_status === 1){
                            // was approved, now we need to send empty verification code
                            loginResponse = await ig.execute(TwoFactorLoginCommand, {
                                verificationCode: "",
                                verificationMethod: "4",
                                twoFactorIdentifier: getSession().two_factor_info.two_factor_identifier,
                                username: getSession().two_factor_info.username,
                            });
                        }
                    }

                    if(!loginResponse) {
                        if(verify_code.toLowerCase().trim() === "resend" ){
                            // for resending sms 2fa code
                            return sendTwoFA(ig);
                        }

                        log(`[${username}] submitting 2fa code`);
                        loginResponse = await ig.execute(TwoFactorLoginCommand, {
                            verificationCode: verify_code,
                            twoFactorIdentifier: getSession().two_factor_info.two_factor_identifier,
                            username: getSession().two_factor_info.username,
                            verificationMethod: getSession().verificationMethod,
                        });
                    }

                    if(loginResponse && loginResponse.logged_in_user){
                        log(`[${username}] logged in with 2fa successfully`);
                        const user = loginResponse.logged_in_user;
                        // successful 2fa
                        return responses.ok(await getUserData(ig, user.pk, user));
                    } else {
                        // failed 2fa
                        log(`[${username}] failed 2fa`, loginResponse);
                        cleanupSession(username, true);
                        return responses.error("2FA failed");
                    }
                }

            } catch (e) {

                if(e.message.includes("check the security code")){
                    return {
                        ... responses["code_required"](),
                        message: "Invalid security code entered. Please try again. If you want to resend the code, enter 'resend' as code."
                    };
                }

                log(e);

                const ret = await handleException(getSession().ig, e);
                if(ret){
                    return ret;
                }

                cleanupSession(username, true);

                return responses.error(e.message);
            }
        }
    },
    async startServer() {
        const app = new Koa();
        app.use(bodyParser());

        app.use(async (ctx, next) => {
            await next();
            const rt = ctx.response.get("X-Response-Time");
            log(`${ctx.method} ${ctx.url} - ${rt}`);
        });

        app.use(async (ctx, next) => {
            log(`${ctx.method} ${ctx.url} - start`);
            const start = Date.now();
            await next();
            const ms = Date.now() - start;
            ctx.set("X-Response-Time", `${ms}ms`);
        });

        app.on("error", (err, ctx) => {
            log("server error");
            log(err);
        });

        app.use(async ctx => {
            if (ctx.method === "POST" && ctx.path === "/login") {
                log("http request received");
                const REQUIRED = ["user_id", "username", "password"];
                if (
                    !REQUIRED.every(k => {
                        return ctx.request.body[k] && ctx.request.body[k].length;
                    })
                ) {
                    log("missing paramters");
                    ctx.throw(400, "Invalid request. Missing parameters.");
                    return;
                }

                const bodyForLog = {
                    ...ctx.request.body,
                    password: "******"
                };
                log(bodyForLog);

                ctx.status = 200;

                let tries = 0;
                let ret;
                while(true){
                    tries++;

                    ret = await this.httpLogin(
                        ctx.request.body
                    );

                    if(!ret.success && ret.message && tries < 5 && shouldRetry(new Error(ret.message))){
                        log("retrying request that failed because: " + ret.message);
                        continue;
                    }

                    break;
                }

                ctx.body = ret;

                log("Response sent", ret);

                return;
            }

            ctx.throw(400, ctx.method + " " + ctx.path + ": Invalid request");
            log("invalid request");
        });

        _server = app.listen(PORT, () => {
            log("server started");
        });

        // set timeout
        _server.setTimeout(5 * 60 * 1000); // default is 2 mins, we set 5mins
    },
    async start() {
        await this.startServer();
    },
    async stop(cb) {
        _server.close();
        cb && cb();
    }
};
