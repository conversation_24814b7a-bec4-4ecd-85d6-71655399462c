<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateInboxConversationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('inbox_conversations', function (Blueprint $table) {
            $table->bigIncrements('id');

            $table->integer('account_id')->unsigned()->index();
            $table->foreign('account_id')->references('id')->on('accounts')->onDelete('cascade')->onUpdate('cascade');

            $table->integer('assigned_to')->nullable()->unsigned()->index();
            $table->foreign('assigned_to')->references('id')->on('users');

            $table->bigInteger('social_contact_id')->nullable()->unsigned()->index();
            $table->foreign('social_contact_id')->references('id')->on('social_contacts')->onDelete('cascade')->onUpdate('cascade');

            $table->timestamp('closed_at')->nullable()->index();
            
            $table->string('external_id')->nullable()->index();
            
            $table->string('status')->default('open');

            $table->json('data')->nullable();
            $table->json('options')->nullable();

            $table->timestamps();
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('inbox_conversations');
    }
}
