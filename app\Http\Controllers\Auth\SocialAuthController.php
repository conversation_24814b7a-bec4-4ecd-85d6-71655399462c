<?php

namespace App\Http\Controllers\Auth;

use App\Account;
use App\Http\Controllers\Controller;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Laravel\Socialite\Facades\Socialite;
use Lara<PERSON>\Socialite\Two\InvalidStateException;

class SocialAuthController extends Controller
{
    /**
     * Redirect the user to the authentication page.
     *
     * @param string $provider
     * @param int $attempt
     * @return Response
     * @throws \Exception
     */
    public function redirectToProvider($provider = null, $attempt = 1)
    {
        if(!config('services.' . $provider))
            abort(404);

        if (!in_array($provider, ['facebook', 'twitter', 'google', ]))
            abort(404);

        try {
            $social_driver =  Socialite::driver($provider);

            if($provider === 'twitter'){
                $social_driver->scopes(config('services.twitter.required_scopes'));
            }

            return $social_driver->redirect();
        } catch (\Exception $exception){
            if($attempt > 5) {
                throw $exception;
            } else {
                // retry (workaround for random curl error 35: unknown ssl protocol)
                return $this->redirectToProvider($provider, $attempt + 1);
            }
        }
    }

    /**
     * Obtain the user information from GitHub.
     *
     * @param Request $request
     * @param null $provider
     * @return \Illuminate\Http\RedirectResponse|Response
     */
    public function handleProviderCallback(Request $request, $provider = null)
    {
        if(!config('services.' . $provider))
            abort(404);

        if($request->input('error_code', false)){
            flash('Error ' . $request->input('error_code') . ': ' . $request->input('error_message', 'Authentication error'), 'error');
            return redirect()->route('login');
        }

        try {
            $external_user = Socialite::driver($provider)->user();
        } catch (InvalidStateException $exception) {
            flash('Session state expired or invalid. Please try again.', 'error');
            return redirect()->route('login');
        } catch (\InvalidArgumentException $exception) {
            flash('Session state expired or invalid. Please try again.', 'error');
            return redirect()->route('login');
        } catch(\GuzzleHttp\Exception\ClientException $exception){
            $response = $exception->getResponse();
            $responseBodyAsString = $response ? $response->getBody()->getContents() : '';

            $json = $responseBodyAsString ? @json_decode($responseBodyAsString, true) : null;

            if($json){
                if (isset($json['error']) && is_string($json['error'])) {
                    flash('Error: ' .  $json['error'], 'error');
                } else {
                    flash('Authentication failed: ' . $responseBodyAsString, 'error');
                }
            } else {
                flash('Authentication failed: ' . $responseBodyAsString, 'error');
            }

            return redirect()->route('login');
        } catch (\Exception $e) {
            report($e);
            return errorPage([
                'title' => 'Authentication failed',
                'message' => $e
            ]);
        }

        /*
        // OAuth Two Providers
        $token = $external_user->token;
        $refreshToken = $external_user->refreshToken; // not always provided
        $expiresIn = $external_user->expiresIn;

        // OAuth One Providers
        $token = $external_user->token;
        $tokenSecret = $external_user->tokenSecret;
        */

        $user_token_obj = [
            'token' => $external_user->token
        ];

        if(isset($external_user->tokenSecret)) {
            $user_token_obj['secret'] = $external_user->tokenSecret;
        }
        if(isset($external_user->refreshToken)) {
            $user_token_obj['refresh_token'] = $external_user->refreshToken;
        }
        if(isset($external_user->expiresIn)) {
            $user_token_obj['expires_in'] = $external_user->expiresIn;
        }

        $user_id = $external_user->getId();
        $user_nick = $external_user->getNickname();
        $user_name = $external_user->getName();
        $user_email = $external_user->getEmail();

        if(empty($user_email)){
            // we don't have user email
            return redirect()->route('login')->withErrors('email', trans('auth.no_email'));
        }

        // verify email
        if(!RegisterController::verifyEmailIsGood($user_email)){
            return redirect()->route('login')->withErrors(
                'email',
                'Invalid email'
            );
        }

        /** @var User $user */
        $user = null;

        // match account with user
        $acc = Account::private()->where('account_id', $user_id)->where('type', $provider . '.profile')->first();

        if($acc !== null){
            $user = $acc->user;
        }

        if($user === null){
            // user not found by matching account, check by email
            $user = User::withTrashed()->where('email', $user_email)->first();

            // account didn't exist, assign this account to $user
            if($acc === null && $user !== null){
                $acc = new Account([
                    'account_id' => $user_id,
                    'type' => $provider . '.profile',
                    'user_id' => $user->id,
                    'name' => $user->name,
                    'private' => true
                ]);
                $acc->save();
            }
        }

        if ($user && $user->trashed()) {
            $user->restore();
        }

        $newUserCreated = false;
        if($user === null) {
            $newUserCreated = true;
            $user = new User([
                'name' => $user_name,
                'email' => $user_email,
                'password' => 'no_password',
                'verified' => true
            ]);
            $user->save();
            $acc = new Account([
                'account_id' => $user_id,
                'type' => $provider . '.profile',
                'user_id' => $user->id,
                'name' => $user->name,
                'private' => true
            ]);
            $acc->save();
        }

        $acc->setToken($user_token_obj);

        if(!$acc->active) // if account not active, mark as active
            $acc->setActive(true);

        \Auth::login($user);

        if($newUserCreated)
            flash(trans('auth.registered_verification'), 'success');

        return redirect()->route('home');

    }
}
