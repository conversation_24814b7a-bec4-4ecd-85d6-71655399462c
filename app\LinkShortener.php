<?php

namespace App;

use App\Helpers\ApiHelper;
use App\Notifications\LinkShortenerInactive;
use App\Traits\HasOptions;
use GuzzleHttp\RequestOptions;
use Illuminate\Database\Eloquent\Model;
use PHPLicengine\Api\Result;
use PHPLicengine\Api\Result as BitlyResult;

/**
 * App\LinkShortener
 *
 * @property int $id
 * @property string $name
 * @property string $type
 * @property string $external_id
 * @property int $user_id
 * @property array|null $options
 * @property bool $active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Account[] $accounts
 * @property-read int|null $accounts_count
 * @method static \Illuminate\Database\Eloquent\Builder|\App\LinkShortener newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\LinkShortener newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\LinkShortener query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\LinkShortener whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\LinkShortener whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\LinkShortener whereExternalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\LinkShortener whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\LinkShortener whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\LinkShortener whereOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\LinkShortener whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\LinkShortener whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\LinkShortener whereUserId($value)
 * @mixin \Eloquent
 * @property-read \App\User $user
 */
class LinkShortener extends Model
{
    use HasOptions;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id', 'name', 'type', 'active', 'external_id',
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'options' => 'array',
        'active' => 'boolean',
    ];

    public static function transform(self $linkShortener, $withAvailableSettings = false)
    {
        $data = collect([
            'id' => $linkShortener->id,
            'name' => $linkShortener->getName(),
            'type' => $linkShortener->type,
            '_type' => $linkShortener->getType(),
            'reconnect_url' => $linkShortener->getReconnectUrl(),
            'active' => $linkShortener->active,
            // we do array_values because for some reason, sometimes this is not an array but is an object
            'accounts' => array_values($linkShortener->accounts->map(function($account){
                return Account::transform($account);
            })->toArray()),
            'query_parameters' => $linkShortener->getQueryParameters(),
            'settings' => $linkShortener->getSettings(),
        ]);
        if($withAvailableSettings){
            $data->put('available_settings', $linkShortener->getAvailableSettings());
        }
        return $data;
    }

    /**
     * Get the user
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the associated accounts.
     */
    public function accounts()
    {
        return $this->belongsToMany(Account::class);
    }

    public function getName(){
        $domain = $this->getSetting('domain');
        $explodedDomain = explode('_', $domain);
        if($domain){
            return $this->name . ' / ' . ( count($explodedDomain) > 1 ? $explodedDomain[1] : $explodedDomain[0] );
        } else {
            return $this->name;
        }
    }

    public function setToken($token){
        $this->setOption('token', $token);
    }

    public function getToken(){
        return $this->getOption('token');
    }

    public function setQueryParameters(array $data){
        $data = array_slice($data, 0, 10); // 10 max
        $this->setOption('query_parameters', $data);
    }

    public function getQueryParameters(){
        return (array) $this->getOption('query_parameters');
    }

    public function getReconnectUrl(){
        $t = $this->type;
        $MAP = [
            'bitly' => url('/app/link_shorteners/bitly/connect'),
        ];
        if(isset($MAP[$t])) return $MAP[$t];
        return null;
    }

    public function getType(){
        $t = $this->type;
        $MAP = [
            'bitly' => 'Bitly',
            'linkmngr' => 'LinkMngr',
        ];
        if(isset($MAP[$t])) return $MAP[$t];
        return $t;
    }

    public function buildLink($link, $placeholders = []){

        $addParams = collect($this->getQueryParameters())->mapWithKeys(function($o) use($placeholders){
            $o['value'] = placeholders_replace($o['value'], $placeholders); // placeholders
            return [
                $o['key'] => $o['value']
            ];
        })->toArray();

        if (!is_array($addParams)) {
            return $link;
        }

        $info = parse_url($link);

        $query = array();

        if (isset($info['query'])) {
            parse_str($info['query'], $query);
        }

        if (!is_array($query)) {
            $query = array();
        }

        $params = array_merge($addParams, $query);

        $result = '';

        if (isset($info['scheme'])){
            $result .= $info['scheme'] . ':';
        }

        if (isset($info['host'])) {
            $result .= '//' . $info['host'];
        }

        if (isset($info['path'])) {
            $result .= $info['path'];
        }

        if ($params) {
            $result .= '?' . http_build_query($params);
        }

        return $result;
    }

    /**
     * @param $targetLink
     * @param array $placeholders
     * @return string Shortened link
     * @throws \Exception
     */
    public function shortenLink($targetLink, $placeholders = []){

        if($this->type === 'bitly'){
            $helper = ApiHelper::getBitly($this->getToken());
            /** @var Result $res */
            $res = $helper->bitlink->createBitlink(array_filter([
                'long_url' => $this->buildLink($targetLink, $placeholders),
                'domain' => $this->getSetting('domain'),
            ]));

            // if cURL error occurs.
            if ($helper->api->isCurlError()) {
                throw new \Exception($helper->api->getCurlErrno().': '.$helper->api->getCurlError());
            } else {
                // if Bitly response contains error message.
                if ($res->isError()) {
                    throw new \Exception('Error ' . $res->getDescription() . ': ' . $res->getResponse());
                } else {
                    // if Bitly response is 200 or 201
                    if ($res->isSuccess()) {
                        return $res->getResponseArray()['link'];
                    } else {
                        throw new \Exception('Unknown response: ' . $res->getResponse());
                    }
                }
            }
        } else if($this->type === 'linkmngr'){

            $brand_id = null;
            $domain = $this->getSetting('domain'); // is either domain or brandId_domain
            $exploded = explode('_', $domain);
            if(count($exploded) > 1){
                $brand_id = $exploded[0];
                $domain = $exploded[1];
            }

            $api = ApiHelper::getLinkMngr($this);
            $res = $api->post('v1/links', [
                RequestOptions::JSON => array_filter([
                    'destination' => $this->buildLink($targetLink, $placeholders),
                    'domain' => $domain,
                    'brand_id' => $brand_id,
                ]),
            ]);
            return @json_decode($res->getBody()->getContents())->link;
        }

        // should never reach here
        throw new \Exception('Link not shortened');

    }

    public function getSettings(){
        return (object) $this->getOption('settings', []); // key - value
    }

    public function getSetting($key, $default = null){
        return $this->getOption('settings.' . $key, $default);
    }

    /**
     * @return array
     */
    public function getAvailableSettings(){
        $allSettings = [];

        if(!$this->active) return $allSettings;
        if($this->type === 'bitly'){
            try {
                $api = ApiHelper::getBitly($this->getToken());

                /** @var BitlyResult $user */
                $user = $api->user->getUser();

                $group_guid = $user->getResponseArray()['default_group_guid'];

                /** @var BitlyResult $group */
                $group = $api->group->getGroup($group_guid);

                $domains = $group->getResponseArray()['bsds'];

                if (count($domains)) {
                    $allSettings[] = [
                        'name' => 'domain',
                        'title' => 'Branded Short Domain',
                        'type' => 'dropdown',
                        'options' => collect($domains)->map(function ($domain) {
                            return [
                                'title' => $domain,
                                'value' => $domain
                            ];
                        })->toArray(),
                    ];
                }
            } catch (\Exception $exception){
                report($exception);
            }
        } else if($this->type === 'linkmngr'){
            try {
                $api = ApiHelper::getLinkMngr($this);

                $allBrands = [];
                $page = 1;
                do {
                    $brandsRes = @json_decode($api->get('v1/brands?page=' . $page)->getBody()->getContents(), true); // is an array
                    $allBrands = array_merge($allBrands, $brandsRes['items']);
                    $page++;
                } while($brandsRes['nextPage']);

                if (count($allBrands)) {
                    $options = [];
                    foreach($allBrands as $brand){
                        foreach($brand['domains'] as $domain){
                            if($domain['active']){
                                $options[] = [
                                    'title' => $brand['name'] . ' / ' . $domain['domain'],
                                    'value' => $brand['id'] . '_' . $domain['domain'],
                                ];
                            }
                        }
                    }
                    $allSettings[] = [
                        'name' => 'domain',
                        'title' => 'Domain',
                        'type' => 'dropdown',
                        'options' => $options,
                    ];
                }
            } catch (\Exception $exception){
                report($exception);
            }
        }
        return $allSettings;
    }

    /**
     * @param array $data
     * @return void
     */
    public function setSettings(array $data){
        if(count($data) === 0) return;
        $availableSettings = $this->getAvailableSettings();
        foreach ($availableSettings as $availableSetting){
            $key = $availableSetting['name'];
            if(!isset($data[$key])){
                // remove option
                $this->removeOption('settings.' . $key);
                continue;
            }
            if($availableSetting['type'] === 'dropdown'){
                if (in_array($data[$key], collect($availableSetting['options'])->map(function ($opt){ return $opt['value']; })->toArray())){
                    $this->setOption('settings.' . $key, $data[$key]); // set value
                }
            }
        }
    }

    public function testConnection($notifyOnFailure = true){

        $success = true;
        if($this->type === 'bitly') {
            try {
                $api = ApiHelper::getBitly($this->getToken());

                $api->user->getUser();

            } catch (\Exception $exception) {
                $success = false;
            }
        } else if($this->type === 'linkmngr') {
            try {
                $api = ApiHelper::getLinkMngr($this);

                /** @var BitlyResult $user */
                $api->get('v1/auth/user');

            } catch (\Exception $exception) {
                $success = false;
            }
        }


        if(!$success && $this->user){
            $this->user->notify(new LinkShortenerInactive($this));
        }

        $this->active = $success;
        $this->save();

        return $success;
    }


    private function getOptions()
    {
        return (array)$this->options;
    }

    private function setOptions($options)
    {
        $this->options = $options;
        $this->save();
        return $this;
    }
}
