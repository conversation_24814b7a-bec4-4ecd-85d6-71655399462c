<?php

namespace App\Console\Commands;

use App\Http\Controllers\SocialMediaTools;
use Illuminate\Console\Command;
use Illuminate\Support\Str;
use Psr\Http\Message\UriInterface;
use Spatie\Sitemap\Sitemap;
use Spa<PERSON>\Sitemap\SitemapGenerator;
use <PERSON><PERSON>\Sitemap\SitemapIndex;
use <PERSON><PERSON>\Sitemap\Tags\Url;

class BuildSitemap extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sitemap:generate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate sitemap';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        SitemapGenerator::create('https://socialbu.com')
            ->shouldCrawl(function (UriInterface $url) {
                // dont add tools links as they are added separately
                return !Str::contains($url->getPath(), [
                    '/blog/',
                    '/help/',
                    '/social-media-scheduling-tools/',
                    '/compare/',
                    '/social-media-glossary/',
                ]);
            })
            ->hasCrawled(
                function (Url $url) {

                    if(in_array($url->path(), [
                        //'/blog/',
                        //'/help/',
                        '',
                        '/help/general',
                        '/help/respond',
                        '/help/publish',
                        '/help/automate',
                    ])) return null;

                    $disallowed = [
                        'auth/',
                        'cdn-cgi',
                        'blog/tag/',
                        'app/',
                    ];
                    foreach ($disallowed as $uri){
                        if(strpos($url->path(), $uri) !== false){
                            return null;
                        }
                    }


                    $url->lastModificationDate = null;

                    if (strpos($url->path(), '/blog/') !== false) {
                        $url->setChangeFrequency('weekly');
                    }

                    if (strpos($url->path(), '/about/') !== false) {
                        $url->setChangeFrequency('monthly');
                    }


                    $url->priority = null; // no need to have priority
                    $url->changeFrequency = null;

                return $url;
            })
            ->writeToFile(public_path('pages_sitemap.xml'));


        // get social media tools links
        $data = (new SocialMediaTools())->loadAllTools();
        $sm = Sitemap::create();
        $sm
            ->add(Url::create('/social-media-scheduling-tools/'))
            ->add(Url::create('/social-media-scheduling-tools/cheapest'))
            ->add(Url::create('/social-media-scheduling-tools/free'))
            ->add(Url::create('/social-media-scheduling-tools/offering-live-support'))
            ->add(Url::create('/social-media-scheduling-tools/facebook'))
            ->add(Url::create('/social-media-scheduling-tools/twitter'))
            ->add(Url::create('/social-media-scheduling-tools/instagram'))
            ->add(Url::create('/social-media-scheduling-tools/linkedin'))
            ->add(Url::create('/social-media-scheduling-tools/for-agencies'))
            ->add(Url::create('/social-media-scheduling-tools/for-real-estate'));

        $doneSlugs = [];
        foreach($data as $tool1){

            // alternative page
            if($tool1['slug'] === 'socialbu'){
                // skip socialbu
                continue;
            }

            $sm->add(Url::create('/compare/' . $tool1['slug'] . '-alternative')); // comparison with socialbu
            $sm->add(Url::create('/compare/' . $tool1['slug'])); // list of all comparisons


            foreach ($data as $tool2){

                if($tool2['slug'] === 'socialbu'){
                    // skip socialbu
                    continue;
                }

                if($tool1['slug'] === $tool2['slug']){
                    // skip self
                    continue;
                }

                $slugs = [$tool1['slug'], $tool2['slug']];
                sort($slugs);

                // skip if already done
                if(in_array($slugs[0] . '-vs-' . $slugs[1], $doneSlugs)) {
                    continue;
                }

                // generate vs. link
                $sm->add(Url::create('/compare/' . $slugs[0] . '-vs-' . $slugs[1]));
                $doneSlugs[] = $slugs[0] . '-vs-' . $slugs[1];

            }
        }
        $sm
            ->writeToFile(public_path('scheduling_tools_sitemap.xml'));

        // todo: also create sitemap for social-media-glossary and add to sitemap index

        SitemapIndex::create()
            ->add('/pages_sitemap.xml')
            ->add('/scheduling_tools_sitemap.xml')
            ->writeToFile(public_path('sitemap.xml'));

        $this->info('Sitemap generated.');
        return true;
    }
}
