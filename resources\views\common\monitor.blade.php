@extends('layout.full_width')
@php($title = 'Social Media Monitoring - Track and Monitor Conversations On Social Media')
@php($description = 'Track and monitor conversations about you and your brand on multiple social media networks.')
@php($image = 'https://socialbu.com/images/site/11_2020/social-listening.png')
@php($url = 'https://socialbu.com/monitor')
@section('title', $title . ' | ' . config('app.name'))
@push('head_html')
    <meta name="description" content="{{ $description }}"/>
    <link rel="canonical" href="{{ $url }}" />

    <meta property="og:locale" content="en_US" />
    <!--
    <meta property="og:type" content="website" />
    -->
    <meta property="og:title" content="{{ $title }}" />
    <meta property="og:description" content="{{ $description }}" />
    <meta property="og:url" content="{{ $url }}" />
    <meta property="og:site_name" content="SocialBu" />
    <meta property="og:image" content="{{ $image }}" />
    <meta property="fb:app_id" content="{{ config('services.facebook.client_id') }}" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content="{{ $image }}" />
    <meta name="twitter:title" content="{{ $title }}" />
    <meta name="twitter:description" content="{{ $description }}" />
    <meta name="twitter:site" content="@socialbuapp" />
@endpush
@section('content')

    <header class="header">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h1 class="display-1 text-center text-md-left">Monitor Social Media with Ease</h1>
                    
                    <p class="lead-2 text-center text-md-left">
                        Track mentions, and monitor keywords and hashtags related to you or your brand.
                    </p>
                    @include('common.internal.start-free-block')
                </div>
                <div class="d-none d-md-block col-12 col-md-6">
                    <img src="/images/1x1.gif" data-src="/images/redesign/monitor/monitor-keywords.webp" alt="social media monitoring and listening" class="img-responsive hover-move-up lozad position-relative" />
                </div>
            </div>
        </div>
    </header>

    <main class="main-content" id="main">

        <section class="section" id="keywords">
            <div class="container">
                <div class="row gap-y align-items-center">
                    <div class="col-md-6 text-center text-md-left offset-md-1 ml-0">
                        <h2 class="display-2">Monitor keywords and hashtags</h2>
                        <p class="lead-2">
                            Track and monitor keywords and hashtags from multiple social media networks.
                        </p>
                    </div>
                    <div class="col-md-6 order-md-first">
                        <img src="/images/1x1.gif" data-src="/images/redesign/monitor/monitor-hashtags.webp" alt="Monitor conversations" data-aos="fade-left" class="img-responsive rounded aos-init aos-animate lozad">
                    </div>
                </div>
            </div>
        </section>

        <section class="section" id="hashtags">
            <div class="container">
                <div class="row gap-y align-items-center social social-brand">
                    <div class="col-md-6 text-center text-md-left offset-md-1 ml-0">
    
                        <h2 class="display-2">Interact and engage with your audience</h2>
                        <p class="lead-2">
                            It is quick and easy to jump into the conversations which interest you.
                        </p>
                    </div>
                    <div class="col-md-6 offset-md-1 ml-0">
                        <img src="/images/1x1.gif" data-src="/images/redesign/monitor/engagement.webp" alt="Monitor conversations" data-aos="fade-left" class="img-responsive rounded aos-init aos-animate lozad">
                    </div>
    
                </div>
            </div>
        </section>

        @include('common.internal.join-us-block')

    </main>

@endsection
