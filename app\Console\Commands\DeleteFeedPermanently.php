<?php

namespace App\Console\Commands;

use App\Feed;
use App\FeedPost;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;

class DeleteFeedPermanently extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'feeds:delete';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete soft deleted feeds';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     * @throws \Exception
     */
    public function handle()
    {
        /** @var Feed[]|Collection $feeds */
        $feeds = Feed::onlyTrashed()->limit(100);

        $feeds->each(function($feed){
            /** @var Feed $feed */
            // delete 200 posts
            $feedPosts = $feed->posts()->where('root_id', null)->limit(1000)->get();

            if($feedPosts->count() === 0){
                // it's now safe to permanently delete the feed
                $feed->forceDelete();
                return;
            }

            foreach ($feedPosts as $feedPost){
                $feed->deletePost($feedPost);
            }

        });
    }
}
