<?php

namespace App\Http\Controllers\Api;

use App\Notifications\AccountInactive;
use App\Notifications\AutomationFailure;
use App\Notifications\ConversationAssigned;
use App\Notifications\LinkShortenerInactive;
use App\Notifications\NewFeedItem;
use App\Notifications\PostApprovalRejected;
use App\Notifications\PostFailure;
use App\Notifications\PostsAwaitingApproval;
use App\Notifications\PublishQueueFailure;
use App\Notifications\QueueDisableOnFailure;
use App\Notifications\TeamInvite;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use App\Http\Controllers\Controller;
use Illuminate\Notifications\DatabaseNotification;

class NotificationsController extends Controller
{

    function __construct()
    {
        // re-flash the session messages
        session()->reflash();
    }

    public function index(){
        return self::transformNotifications(user()->notifications()->paginate());
    }

    public function readIndex(){
        return self::transformNotifications(user()->readNotifications()->paginate());
    }

    public function unreadIndex(){
        return self::transformNotifications(user()->unreadNotifications()->paginate());
    }

    public function get($id){
        /** @var DatabaseNotification $notification */
        $notification = user()->notifications()->findOrFail($id);
        return self::transformNotification($notification);
    }

    public function markRead($id){
        /** @var DatabaseNotification $notification */
        $notification = user()->notifications()->findOrFail($id);
        $notification->markAsRead();

        return self::transformNotification($notification);
    }

    public function markUnread($id){
        /** @var DatabaseNotification $notification */
        $notification = user()->notifications()->findOrFail($id);
        $notification->markAsUnread();

        return self::transformNotification($notification);
    }

    /**
     * @return void
     */
    public function markAllRead(){
        user()->unreadNotifications()->update(['read_at' => now()]);
    }

    /**
     * Message strings for the notification messages
     * @return array
     */
    static public function getMessages()
    {
        return [
            AccountInactive::class => [
                'title' => 'Account disconnected',
                'body' => '{{ account_name }} ({{ account_type }}) has been disconnected. Please re-connect this account.',
                'url' => url('/app/accounts'),
            ],
            AutomationFailure::class => [
                'title' => 'Automation failed',
                'body' => 'Unable to execute: {{ automation_description }}. {{ reason }}',
                'url' => url('/app/automations') . '/{{ automation_id }}',
            ],
            LinkShortenerInactive::class => [
                'title' => 'Link Shortener disconnected',
                'body' => '{{ link_shortener_name }} has been disconnected. Please re-connect.',
                'url' => url('/app/link_shorteners'),
            ],
            NewFeedItem::class => [
                'title' => 'New items in Feed',
                'body' => '{{ feed_name }} has {{ new_updates }} new items.',
                'url' => url('/app/respond/feeds') . '/{{ feed_id }}',
            ],
            PostApprovalRejected::class => [
                'title' => 'Post not approved',
                'body' => '{{ approver_name }} has rejected your post: {{ reason }}',
                'url' => url('/app/publish/drafts'),
            ],
            PostFailure::class => [
                'title' => 'Post not published',
                'body' => 'One of your posts could not be published to {{ account_name }} ({{ account_type }}). {{ reason }}',
                'url' => url('/app/publish/posts'),
            ],
            PostsAwaitingApproval::class => [
                'title' => 'Content needs approval',
                'body' => 'You have {{ count }} post(s) awaiting your approval.',
                'url' => url('/app/publish/awaiting_approval'),
            ],
            PublishQueueFailure::class => [
                'title' => 'Queue: post not published',
                'body' => 'One of your posts could not be published to {{ account_info }} from your queue: {{ queue_name }}. {{ reason }}',
                'url' => url('/app/publish/posts'),
            ],
            QueueDisableOnFailure::class => [
                'title' => 'Queue disabled: post not published',
                'body' => 'One of your posts could not be published to {{ account_info }} from your queue: {{ queue_name }}. {{ reason }}',
                'url' => url('/app/publish/queues'),
            ],
            TeamInvite::class => [
                'title' => 'Invited to the team: {{ team_name }}',
                'body' => '{{ invited_by_name }} has invited you to the team: {{ team_name }}. After accepting the invite, you can have access to the team resources that are shared with you.',
                'url' => url('/app/teams'),
            ],
            ConversationAssigned::class => [
                'title' => 'Conversation assigned to you',
                'body' => '{{ conversation_title }}',
                'url' => url('/app/inbox/') . '{{ conversation_id }}',
            ],
        ];
    }

    static private function transformNotifications(LengthAwarePaginator $data){
        return collect([
            'items' => collect($data->items())->map(function (DatabaseNotification $notification){
                return self::transformNotification($notification);
            }),
            'currentPage' => $data->currentPage(),
            'lastPage' => $data->lastPage(),
            'nextPage' => $data->hasMorePages() ? $data->currentPage() + 1 : null,
            'total' => $data->total(),
        ]);
    }

    static private function transformNotification(DatabaseNotification $notification){
        return array_merge([
            'created_at' => $notification->created_at->toDateTimeString(),
            'id' => $notification->id,
            'read_at' => $notification->read_at ? $notification->read_at->toDateTimeString() : null,
            'is_unread' => $notification->unread(),
        ], self::getData($notification->type, $notification->data));
    }

    static private function getData($type, array $data){
        // map of notification classes to message strings
        $MESSAGES = self::getMessages();

        return isset($MESSAGES[$type]) ? [
            'title' => placeholders_replace($MESSAGES[$type]['title'], $data),
            'body' => placeholders_replace($MESSAGES[$type]['body'], $data),
            'url' => $MESSAGES[$type]['url'] ? placeholders_replace($MESSAGES[$type]['url'], $data) : null,
            'level' => isset($data['_level']) ? $data['_level'] : 'info',
        ] : [
            'title' => $type, // should never happen
            'body' => 'No details available',
            'url' => null,
        ];
    }
}
