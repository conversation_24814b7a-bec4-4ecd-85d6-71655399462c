.sidebar {
    padding: 0px;
    padding-top: 3.7rem;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100; /* Behind the navbar */
    min-width: $app-sidebar-width;
    overflow-x: hidden;
    overflow-y: auto;
    .nav-link {
        font-weight: 500;
        font-family: 'Plus Jakarta Sans';
    }
    .sidebar-sticky {
        height: calc(100vh - 100px);
        overflow-x: hidden;
        overflow-y: auto; /* Scrollable contents if viewport is shorter than content. */
    }
}

#sidebar-menu{
    padding-top: .5rem;
    .nav-link{
        &:hover,
        &.active{
            & svg {
                path {
                    fill: $color-primary;
                }
            }
        }
    }
    .submenu {
        list-style: none;
        padding: 8px;
        .nav-item::before{
            content:'';
            width: 20px;
            height: 20px;
            border: 1px solid #D5E1F6;
            border-top: 0px;
            border-right: 0px;
            border-radius: 0% 50% 0%;
            position: absolute;
            left: -1px;
            bottom: 20px;
        }
    }
}
#sidebar-menu-mobile {
    .nav-link {
        padding: 0.5rem 1rem;
        font-weight: 500;
        color: #333;
        &:hover,
        &.active {
            color: $color-primary;
            
            .fa, .mdi {
                color: inherit;
            }
        }
        .fa, .mdi {
            margin-right: 4px;
            color: #999;
        }
    }
}
