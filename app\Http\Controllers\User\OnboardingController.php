<?php

namespace App\Http\Controllers\User;

use App\Account;
use App\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class OnboardingController extends Controller
{
    public function showPage(){
        return view('user.onboarding');
    }

    public function isOnboardingCompleted(){
        $state = $this->getState();
        $allDone = true;
        $requiredSteps = [
            'is_verified',
            'has_password',
            'has_persona',
            'has_acquisition_channel',
            'has_timezone',
            'selected_plan',
        ];
        foreach($requiredSteps as $step){
            if(isset($state[$step]) && !$state[$step]){
                $allDone = false;
                break;
            }
        }
        return $allDone;
    }
    
    public function removeChecklist( Request $request){
        $user = user();
        $user->setOption('onboarding.hide',true);
    }

    public function getState(){
        $user = user();
        return [
            'is_verified' => !!$user->verified,
            'has_company' => !!$user->company,
            'has_password' => $user->password !== 'no_password',
            'has_phone' => !!$user->getPhone(),
            'has_persona' => !!$user->getOption('persona'),
            'has_acquisition_channel' => !!$user->getOption('acquisition_channel'),
            'has_timezone' => !!$user->getTimezone(null),
            'selected_plan' => with($user, function(/** @var User $user */ $user){

                if($user->getOption('onboarding.plan')){
                    // has selected a plan in the onboarding process
                    return true;
                }
                if($user->getPlan(true) !== 'free'){
                    // if on a plan, that means no need to ask them to start trial
                    return true;
                }
                if($user->accountsFromTeams(true)->count() > 0){
                    // is part of a team, so no need
                        return true;
                }
                return !!$user->subscription();
            }),
            'added_accounts' => with($user, function(/** @var User $user */ $user){
                $pendingTeamInvites = $user->pendingTeams()->count(); // if user is invited to a team, that means its fine
                $count = $user->accountsFromTeams(true)->count() + Account::ofUser($user->id)->count(); // accounts user has access to
                return $count > 0 || $pendingTeamInvites > 0;
            }),
        ];
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function getData(){
        $user = user();
        return response()->json([
            'data' => array_merge(User::transform(user())->toArray(), [
                'company' => user()->company,
                'phone' => user()->getPhone(),
                'persona' => user()->getOption('persona', ''),
                'acquisition_channel' => user()->getOption('acquisition_channel', ''),

                'timezone' => user()->getTimezone(null),

                // important: we don't send plan so user can always choose even when on free plan
                'plan' => user()->getPlan(true) !== 'free' ? user()->getPlan(true) : null,
                'plan_duration' => user()->planIsYearly() ? 'yearly' : 'monthly',
                'can_get_trial' => !!$user->subscription('default'),

                'coupon_name' => with($user->getOption('stripe_coupon'), function($code){
                    if(!$code){
                        return null;
                    }
                    $coupon = user()->getCoupon();
                    if(!$coupon){
                        return null;
                    }
                    return $coupon->name;
                }),
            ]),
            'state' => $this->getState(),
            'timezones' => timezone_identifiers_list(\DateTimeZone::ALL_WITH_BC),
            'plans' => collect(config('plans'))->map(function($p, $k){
                return array_merge($p, [
                    'id' => $k,
                ]);
            })->filter(function($p){
                return in_array($p['id'], ['free', 'standard2', 'super', 'supreme']);
            })->values()->toArray(),
            'countries' => collect(getCountries())->map(function($name, $code){
                return [
                    'name' => $name,
                    'code' => $code,
                ];
            })->values()->toArray(),
        ]);
    }

    public function verifyEmail(Request $request){
        $this->validate($request, [
            'email' => 'required|max:191|email|unique:users,email,' . \Auth::id(),
        ]);
        if(user()->verified){ // already verified
            return response()->json("already_verified");
        }
        try {
            if(user()->email !== $request->input('email')){
                user()->setEmail($request->input('email'));
            } else {
                // old email was sent, so simply resend the email
                user()->sendEmailVerification(true);
            }
        } catch (\Exception $e) {
            abort(400, $e->getMessage());
        }
        return response()->json("sent");
    }

    public function setPassword(Request $request){
        $this->validate($request, [
            'password' => 'bail|required|min:8|confirmed',
        ]);

        try {
            user()->setPassword($request->input('password'));

            // update pass hash in session
            $request->session()->put([
                'password_hash' => $request->user()->getAuthPassword(),
            ]);

        } catch (\Exception $e) {
            abort(400, $e->getMessage());
        }
    }

    public function userDetails(Request $request){
        $this->validate($request, [
            'persona' => 'required|string|max:191',
            'company' => 'nullable|string|max:191',
            'phone' => 'nullable|string|max:191',
            'acquisition_channel' => 'required|string|max:191',
        ]);

        try {
            $user = user();

            $user->setOption('persona', $request->input('persona'));
            $user->setOption('acquisition_channel', $request->input('acquisition_channel'));

            if($request->input('phone')) {
                $user->setPhone($request->input('phone'));
            }

            if($request->input('company')) {
                $user->company = $request->input('company');
                $user->save();
            }

        } catch (\Exception $e) {
            abort(400, $e->getMessage());
        }
    }

    public function setTimezone(Request $request){
        $this->validate($request, [
            'timezone' => 'bail|required',
        ]);

        try {
            if(is_valid_timezone($request->input('timezone'))) {
                user()->setTimezone($request->input('timezone'));
            } else {
                throw new \Exception('This timezone is not supported.');
            }
        } catch (\Exception $e) {
            abort(400, $e->getMessage());
        }
    }

    public function selectPlan(Request $request){
        $this->validate($request, [
            'plan' => 'bail|string|required|in:' . implode(',', array_keys(config('plans'))), // required
            'payment_method' => 'bail|string|nullable',
            'card_name' => 'bail|string|nullable|max:191',
            'country' => 'bail|string|nullable|max:191',
            'plan_duration' => 'bail|string|nullable|in:monthly,yearly',
        ]);

        if(!in_array($request->input('plan_duration'), ['monthly', 'yearly'])){
            $request->merge([
                'plan_duration' => 'monthly',
            ]);
        }

        // settings controller expects type and action
        $request->merge([
            'type' => $request->input('plan_duration', 'monthly'),
            'action' => 'start', // start is for new subscription
        ]);

        try {

            if($request->input('plan', 'free') !== 'free'){
                // start subscription
                // we just pass the request to SettingsController which already has the code for starting the subscription
                (new SettingsController())->updateSubscription($request);
            }

            // set plan so user doesn't have to complete this step again
            user()->setOption('onboarding.plan', $request->input('plan'));

        } catch (\Exception $e) {
            abort(400, $e->getMessage());
        }
    }

}
