<template>
    <div>
        <div ref="modal" class="modal" id="create_post_modal" tabindex="-1" role="dialog"
             aria-labelledby="create_post_modal_label" aria-hidden="true" :class="{'editor-rendered-in-popup': renderedInPopup}">
            <div class="modal-dialog modal-xl" role="document"
                 :class="{'modal-dialog-slideout bottom': show.fullScreen}">
                <div class="modal-content">
                    <div v-if="show.loader" v-html="overlayLoaderHtml"></div>
                    <div class="modal-body" :class="{'mt-5': renderedInPopup}">

                        <div :class="{'ml-4-n': !renderedInPopup}">
                            <div class="row pt-4">
                                <div
                                    :class="{ 'col-12': renderedInPopup || (!renderedInPopup && !preview.show), 'col-12 col-md-7': preview.show && !renderedInPopup, 'd-none': preview.show && renderedInPopup}">
                                    <div class="new_post_wrapper"
                                         @dragover.prevent.stop="onDrag('start', $event)"
                                         @dragenter.prevent.stop="onDrag('start', $event)"
                                         @dragleave.prevent.stop="onDrag('end', $event)"
                                         @dragend.prevent.stop="onDrag('end', $event)"
                                         @drop.prevent.stop="onDrag('drop', $event)"
                                    >
                                    <div class="d-flex align-items-center justify-content-between mb-3">
                                        <div class="d-flex align-items-center" v-if="mode !== 'edit' && mode !== 'queue_edit' && mode !== 'queue_add'">
                                            <div class="pr-3 border-right">
                                                <div class="form-check">
                                                    <input type="checkbox" class="form-check-input" :checked="selectedAccounts.length === availableAccounts.length" @change="onSelectAll" id="select-all-account">
                                                    <label class="form-check-label small-2" for="select-all-account">Select All</label>
                                                </div>
                                            </div>
                                            <div class="d-flex align-items-center small-2 ml-3" v-tour:posteditor_team2="'You can switch between your teams here. Selecting a team will make sure you only see accounts related to that team.'">
                                                <div class="dropdown">
                                                    <button class="btn border-none p-0 btn-xs font-weight-400" 
                                                        type="button" 
                                                        id="dropdownMenuButton"
                                                        data-toggle="dropdown" 
                                                        aria-haspopup="true" 
                                                        aria-expanded="false">
                                                        {{ currentTeam ? currentTeam.name : 'My Accounts' }}
                                                        <i class="ph ph-caret-down"></i>
                                                    </button>
                    
                                                    <div class="dropdown-menu">
                                                        <a class="dropdown-item" href="#" @click.prevent="selectedTeam = null && teams.length">
                                                            My Accounts
                                                        </a>
                                                        <a class="dropdown-item disabled" href="#" v-if="!teams.length">
                                                            You currently don't have a team
                                                        </a>
                    
                                                        <a class="dropdown-item" 
                                                            v-for="(team, i) in teams" 
                                                            :key="'team_opt_' + i" 
                                                            href="#" 
                                                            @click.prevent="showAccountsForTeam(team.id)">
                                                            {{ team.name }}
                                                        </a>
                                                        <div class="dropdown-divider"></div>
                                                        <div class="text-center">
                                                            <a href="/app/teams" class="btn btn-primary w-100" target="_blank">
                                                                <i class="ph ph-plus"></i>
                                                                Add a team
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="ml-auto" v-if="!renderedInPopup">
                                            <button class="btn btn-sm text-muted p-0" type="button" data-dismiss="modal" aria-label="Close">
                                                <i class="ph ph-x ph-md"></i>
                                            </button>
                                        </div>
                                    </div>
                                        <form class="editor" method="POST" enctype="multipart/form-data">
                                            <input type="hidden" name="team_id"
                                                   :value="selectedTeam" v-if="selectedTeam"/>
                                            <div class="row"
                                                 v-if="show.accounts">
                                                <div
                                                    class="col-md-12 accounts-selector d-flex flex-wrap align-items-center pb-4"
                                                    :class="{invisible: !initialized}"
                                                    v-tour:posteditor_accounts="'Select the accounts where you want your post to be published'">
                                                    <div class="my-1 mr-2 account-pic"
                                                         v-for="(account, i) in availableAccounts"
                                                         :key="uuid + '_' + account.id + '_' + i">
                                                        <input :title="account.name" class="selected_accounts"
                                                               name="accounts[]" :value="account.id" type="checkbox"
                                                               :id="'acc' + account.id + '_' + uuid"
                                                               :data-type="account.type"
                                                               :data-id="account.id"
                                                               @change="toggleAccountSelection(account.id, account.type)"
                                                               :checked="selectedAccountsIds.includes(account.id)"/>
                                                        <label :for="'acc' + account.id + '_' + uuid">
                                                            <img :src="account.image"
                                                                class="profile-pic"
                                                                 alt="account image"
                                                                :title="account.name" v-tooltip/>
                                                               <img class="rounded-circle border border-white network-icon"
                                                                alt="account type"
                                                                :src="getIconForAccount(account.type, 'circular')"
                                                                :title="account._type" v-tooltip>
                                                        </label>
                                                    </div>
                                                    <div class="add-account my-1 mr-2" v-tooltip:right="$lang.get('accounts.add')">
                                                        <a href="/app/accounts/add" target="_blank"><i
                                                            class="ph ph-plus ph-lg text-primary"
                                                            aria-hidden="true"></i>
                                                        </a>
                                                    </div>
                                                    
                                                </div>
                                            </div>
                                            <div class="row" v-else-if="mode === 'edit'">
                                                <div class="col accounts-selector">
                                                    <div class=""
                                                         v-for="account in availableAccounts"
                                                         :key="uuid + 'a' + account.id"
                                                         v-if="selectedAccountsIds.includes(account.id)">
                                                        <div class="d-flex align-items-center">
                                                            <div class="line-height-1 account-pic position-relative">
                                                                <img class="profile-pic1 border border-secondary"
                                                                    :src="account.image"
                                                                    :title="account.name"
                                                                    :class="{'border border-success': selectedAccountsIds.includes(account.id)}" v-tooltip>
                                                                <img class="border rounded-circle border-white network-icon"
                                                                    :src="getIconForAccount(account.type, 'circular')"
                                                                    :title="account._type" v-tooltip>
                                                            </div>
                                                            <h6 class="mb-0 ml-2">{{ account.name }}</h6>

                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-12">
                                                    <div class="form-group mb-3">
                                                        <div class="container-fluid border rounded border-secondary" @paste="onPaste($event)">
                                                            <!-- post box container -->
                                                            <div class="row">
                                                                <div
                                                                    class="col-12 px-0 border-bottom d-flex justify-content-between">
                                                                    <!-- toolbar for textarea -->
                                                                    <div class="d-flex">
                                                                        <button
                                                                            class="btn btn-sm btn-link text-decoration-none text-body pl-3 py-2"
                                                                            type="button"
                                                                            title="Separate post content for each account"
                                                                            v-tooltip
                                                                            v-tour:posteditor_customize_content="'Customize post content for each account. This is primarily useful if you want to set different content for each of the selected accounts.'"
                                                                            @click="customizeContent = !customizeContent"
                                                                            v-if="selectedAccounts.length > 1 && mode !=='queue_add' && mode !== 'queue_edit'">
                                                                            <i class="ph ph-toggle-right ph-fill ph-lg text-success"
                                                                               v-if="customizeContent"></i>
                                                                            <i class="ph ph-toggle-left ph-fill ph-lg text-light" v-else></i>
                                                                            Customize for each account
                                                                        </button>
                                                                        <!--
                                                                        <button class="btn btn-sm btn-link text-muted text-decoration-none" type="button"
                                                                                :class="{'border-left': customizeContent}"
                                                                                @click="triggerCommands"
                                                                                v-tooltip="'Show currently available commands'">
                                                                            <i class="mdi mdi-slash-forward"></i>
                                                                        </button>
                                                                        -->
                                                                    </div>
                                                                </div>
                                                                <div :class="`px-0 bg-light`" v-if="shouldCustomizeContent">
                                                                    <div
                                                                        class="list-group list-group-flush accounts-container">
                                                                        <button type="button"
                                                                                class="list-group-item list-group-item-action p-1 border-right cursor-pointer d-flex align-items-center justify-content-center"
                                                                                :class="{'bg-light selected': customizeContentFor === account.id, 'opacity-70 border-right': customizeContentFor !== account.id}"
                                                                                @click.prevent="customizeContentFor = account.id"
                                                                                v-for="account in selectedAccounts"
                                                                                :key="uuid + account.id + 'cca'">
                                                                            <span
                                                                                class="d-block account-pic position-relative">
                                                                                <img alt="avatar"
                                                                                     class="profile-pic avatar-xs rounded-circle"
                                                                                     :class="{'opacity-70': customizeContentFor !== account.id}"
                                                                                     :title="account.name"
                                                                                     :src="account.image"/>
                                                                                <img class="border rounded-circle border-white network-icon"
                                                                                     alt="account type"
                                                                                    :src="getIconForAccount(account.type, 'circular')"
                                                                                    :title="account._type" v-tooltip>
                                                                            </span>
                                                                        </button>
                                                                    </div>
                                                                </div>
                                                                <div class="pb-2 pt-4"
                                                                     :class="{'col-12': !shouldCustomizeContent, [`col-10 ${preview.show ? 'col-md-10' : 'col-md-11'}`]: shouldCustomizeContent}">
                                                                    <RichText style="min-height: 60px;"
                                                                              placeholder="Write something..."
                                                                              ref="text_input"
                                                                              :highlight="highlightConfig"
                                                                              :autocomplete="autocompleteConfig"
                                                                              v-model="content"
                                                                    />
                                                                </div>
                                                                <div class="col-12 mb-4">

                                                                    <button-uploaded-media
                                                                        ref="mediaSelectBtn"
                                                                        :multiple="true"
                                                                        :max="maximumAttachments"
                                                                        :validator="validateAttachment"
                                                                        :filesToRender="attachments"
                                                                        :compressImage="selectedAccounts.filter(a => a.type.includes('twitter'))"
                                                                        @resetMediaOptions="resetMediaOptions"
                                                                        @change="files => {
                                                                                attachments = files;
                                                                            }"
                                                                    />
<!--                                                                    <div class="border-top mt-2 p-1"-->
<!--                                                                         v-if="attachments.length">-->
<!--                                                                        <span v-for="(attachment, i) in attachments"-->
<!--                                                                              :key="i + 'att'" :title="attachment.name"-->
<!--                                                                              class="badge badge-light border m-1">-->
<!--                                                                            <span>{{-->
<!--                                                                                    attachment.name.length > 30 ? truncate(attachment.name, {length: 30}) : attachment.name-->
<!--                                                                                }}</span>-->
<!--                                                                            <span class="cursor-pointer ml-1"-->
<!--                                                                                  title="Remove" v-tooltip-->
<!--                                                                                  @click="removeAttachment(attachment, i)">-->
<!--                                                                                <i class="ph ph-x ph-md"></i>-->
<!--                                                                            </span>-->
<!--                                                                        </span>-->
<!--                                                                    </div>-->
                                                                </div>
                                                            </div>
                                                            <div class="row bg-light rounded-md mx-0 my-2">
                                                                <div class="col-8 px-0">
                                                                    <div class="d-flex align-items-center">
                                                                        <div class="dropdown picker-dropdown dropup">
                                                                            <button type="button"
                                                                                class="btn btn-xs btn-link text-muted text-decoration-none cursor-pointer p-2 mr-2"
                                                                                :class="{'add-border' : show.emojiPicker}"
                                                                                title="Insert an emoji"
                                                                                v-tooltip
                                                                                @click="show.emojiPicker = !show.emojiPicker"
                                                                                v-click-outside="()=>{ if(show.emojiPicker) show.emojiPicker = false; }">
                                                                                <i class="ph ph-smiley ph-md"></i>
                                                                            </button>
                                                                            <div class="dropdown-menu dropdown-menu-left p-0"
                                                                                :class="{'show': show.emojiPicker}"
                                                                                @click.stop="()=>{}">
                                                                                <emoji-picker :on-select="emoji => insertEmoji(emoji)"/>
                                                                            </div>
                                                                        </div>
                                                                        <div class="dropdown suggested-dropdown dropup">
                                                                            <button type="button"
                                                                                class="btn btn-xs btn-link text-muted text-decoration-none cursor-pointer p-2 mr-2"
                                                                                :class="{'add-border' :show.suggestedHashtags}"
                                                                                title="Suggested hashtags"
                                                                                v-tooltip
                                                                                @click="show.suggestedHashtags = !show.suggestedHashtags"
                                                                                :disabled="!suggestedHashtags.length"
                                                                                v-click-outside="()=>{ if(show.suggestedHashtags) show.suggestedHashtags = false; }">
                                                                                <i class="ph ph-hash ph-md"></i>
                                                                            </button>
                                                                            <div 
                                                                                class="dropdown-menu overflow-auto hashtag-menu" 
                                                                                :class="{'show': show.suggestedHashtags}"
                                                                                @click.stop="()=>{}">
                                                                                <button class="dropdown-item" type="button" v-for="hashtag in suggestedHashtags"
                                                                                    @click="$event=>{ content += ' #' + hashtag; show.suggestedHashtags = false; }">
                                                                                    {{ hashtag }}
                                                                                </button>
                                                                            </div>
                                                                        </div>
                                                                        <button
                                                                            class="btn btn-xs btn-link text-muted text-decoration-none border-none rounded-0 p-2 mr-2"
                                                                            type="button"
                                                                            @click="autocompleteByAI"
                                                                            v-tour:post_ai_autocomplete="'Our AI assistant can help you autocomplete your post.'"
                                                                            v-tooltip="'Autocomplete using AI'"
                                                                            :disabled="!focusedAccount">
                                                                            <i class="ph ph-magic-wand ph-md"></i>
                                                                        </button>
                                                                        <div class="dropdown attach-media-dropdown">
                                                                            <button type="button"
                                                                                class="btn text-muted btn-link text-dark text-decoration-none d-flex align-items-center p-2"
                                                                                data-toggle="dropdown"
                                                                                aria-haspopup="true"
                                                                                aria-expanded="false"
                                                                                title="Attach Media"
                                                                                v-tooltip
                                                                                :disabled="attachments.length >= maximumAttachments"
                                                                                data-tour="id=posteditor_media_attach;text=Depending on the social media accounts you have selected, you may also add media attachments (photos or video). <br/><br/>The recommended file types, limits, and dimensions depends on the account(s) you have selected.<br/><br/> We will tell you if you select an invalid file.">
                                                                                <i class="ph ph-image ph-md mr-1"></i>
                                                                                <i class="ph ph-caret-down ph-sm"></i>
                                                                                <i class="ph ph-caret-up ph-sm d-none"></i>
                                                                            </button>
                                                                            <div class="dropdown-menu dropdown-menu-right">
                                                                                <a class="dropdown-item" href="#"
                                                                                    @click.prevent="$refs.mediaSelectBtn.selectFile">
                                                                                    <i class="ph ph-upload-simple ph-md mr-1"></i>
                                                                                    Upload from Device
                                                                                </a>
                                                                                <a class="dropdown-item" href="#"
                                                                                    @click.prevent="openCanva(file => $refs.mediaSelectBtn.addFiles([file]))">
                                                                                    <i class="ph ph-palette ph-md mr-1"></i>
                                                                                    Design with Canva
                                                                                </a>
                                                                                <a class="dropdown-item"
                                                                                    href="#"
                                                                                    @click.prevent="openContentDrips(files => $refs.mediaSelectBtn.addFiles(files))">
                                                                                    <i class="ph ph-palette ph-md mr-1"></i>
                                                                                    Design with Contentdrips
                                                                                </a>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                                <div class="col-4 px-0">
                                                                    <div class="d-flex justify-content-end">
                                                                        <button type="button"
                                                                            class="btn btn-xs btn-link show_post_options border-none text-muted rounded-0 px-2 py-md-1 py-2 mr-1"
                                                                            @click.prevent="toggleOptions"
                                                                            v-tooltip="'Post options'"
                                                                            v-tour:posteditor_more_opts.left="'Depending on your post, there may be advanced options available to you. Clicking this button will reveal those options.'"
                                                                            :disabled="!Object.keys(postOptions).length">
                                                                            <span class="d-flex align-items-center" v-if="!show.postOptions">
                                                                                <i class="ph ph-plus-circle ph-md"></i>
                                                                                <span class="d-md-block d-none ml-1">Options </span>   
                                                                            </span>
                                                                            <span class="d-flex align-items-center" v-else>
                                                                                <i class="ph ph-minus-circle ph-md"></i>
                                                                                <span class="d-md-block d-none ml-1">Options</span>  
                                                                            </span>
                                                                        </button>
                                                                        <button
                                                                            class="btn btn-xs btn-link text-decoration-none border-none text-muted px-2 py-md-1 py-2"
                                                                            type="button"
                                                                            @click="preview.show = !preview.show;"
                                                                            v-tour:post_preview="'See how your post will look once it is published'"
                                                                            v-tooltip
                                                                            title="Show preview">
                                                                            <span class="d-flex align-items-center" v-if="!preview.show"><i class="ph ph-eye ph-md mr-1"></i> <span class="d-md-block d-none">Preview</span> </span>
                                                                            <span class="d-flex align-items-center" v-else><i class="ph ph-eye-slash ph-md mr-1"></i><span class="d-md-block d-none">Preview</span></span>
                    
                                                                        </button>
                                                                        <div class="content_counter_wrapper small-2 text-center d-md-block d-none my-2 ml-1">
                                                                            <div v-tooltip="'Post length of your account'" class="d-flex justify-content-center border-left" :style="maxPostLength === null  ? 'color: #98A1B2' : ''"
                                                                                :class="{ 'text-danger': maxPostLength - postLength < 0 }">
                                                                                <template v-if="!maxPostLength">
                                                                                    0
                                                                                </template>
                                                                                <template v-else>
                                                                                    {{ maxPostLength - postLength }}
                                                                                </template>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="form-group mb-0">
                                                        <div class="small-2"
                                                             :title="'Try to maintain one of the recommended aspect ratios to prevent unexpected cropping that may occur in some cases.'"
                                                             v-if="showRecommendedAspectRatios">
                                                            Recommended aspect ratios:
                                                            {{ recommendedAspectRatios.join(", ") }}
                                                        </div>
                                                    </div>
                                                    <div class="pb-4 post-options mt-6"
                                                         :class="{'px-1': selectedAccounts.length > 1}"
                                                         v-show="Object.keys(postOptions).length && show.postOptions"
                                                         v-tour:posteditor_opts_container="'These extra options depend on the post and the social media accounts that you have selected. <br/><br/>For example, you can add replies to your tweet (threaded replies) if you are posting to Twitter.'"
                                                    >
                                                        <div class="d-flex align-items-center flex-wrap pt-6 pb-5">
                                                            <div v-for="(type,i) in postOptionAccountTypes">
                                                                <div 
                                                                    class="post-option-account p-2 cursor-pointer mr-2"
                                                                    :class="{'post-option-account-selected rounded-md' : type === selectedPostOptionAccountType}"
                                                                    @click.prevent="selectedPostOptionAccountType = type"
                                                                >
                                                                    <img
                                                                        alt="account type"
                                                                        :src="getIconForAccount(type, 'original')"
                                                                    >
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="row">
                                                            <div class=""
                                                                 :class="{'col-12 col-md-6': !preview.show && selectedAccounts.length > 1, 'col-12': preview.show || selectedAccounts.length <= 1}"
                                                                 v-for="(opts, id, i) in postOptions"
                                                                 :key="uuid + 'p-options-i' + i + id">
                                                                <div class="form-row mb-6">
                                                                    <h5 class="mb-3 col-12 font-weight-500 d-flex pl-0" v-if="postOptionAccounts[selectedPostOptionAccountType] && postOptionAccounts[selectedPostOptionAccountType].length > 1">
                                                                        <img alt="account type" class='rounded-circle network-icon mr-2' :src="getIconForAccount(opts.account.type, 'circular')">
                                                                        {{ opts.account.name.length > 22 ? opts.account.name.substring(0,22).concat("..."): opts.account.name }} <span v-if=" opts.account.extra_data.creator_info && opts.account.extra_data.creator_info.creator_nickname"> ({{opts.account.extra_data.creator_info.creator_nickname}})</span>
                                                                    </h5>

                                                                    <template
                                                                        v-for="(opt, j) in opts.options">

                                                                        <!-- init value -->
                                                                        {{ (options[id] = options[id] || {}) && null }}

                                                                        <div :class="'form-group col-' + (inp.colWidth) + ''"
                                                                            v-for="(inp,i) in opt.inputs"
                                                                            :key="uuid + 'uuid-input' + i + j + id">

                                                                            <!-- init value -->
                                                                            {{
                                                                                (options[id][inp.props.name] = options[id][inp.props.name] || inp.props.checked  ||  "") && null
                                                                            }}

                                                                            <template
                                                                                v-if="inp.elem === 'input' && inp.props.type === 'checkbox'">
                                                                                <div class="form-check">
                                                                                    <input class="form-check-input"
                                                                                            type="checkbox"
                                                                                            value="true"
                                                                                            data-value-type="boolean"
                                                                                            :id="'options[' + id + '][' + inp.props.name + ']'"
                                                                                            :checked="options[id][inp.props.name]"
                                                                                            :disabled="inp.props.disabled"
                                                                                            :name="'options[' + id + '][' + inp.props.name + ']'"
                                                                                            @change="updateOptions(id, inp.props.name, $event.target.checked)"/>
                                                                                    <label class="form-check-label cursor-pointer" :for="'options[' + id + '][' + inp.props.name + ']'">
                                                                                        {{  opt.title }}
                                                                                    </label>
                                                                                    <span class="text-danger" v-if="inp.props.required" title="Required" v-tooltip>*</span>
                                                                                </div>
                                                                                <span
                                                                                    class="form-text small-2"
                                                                                    v-if="opt.description && opt.description.length">
                                                                                    {{ opt.description }}
                                                                                </span>
                                                                            </template>
                                                                            <template
                                                                                v-else-if="inp.elem === 'input' && inp.props.type === 'text'">
                                                                                <label class="text-secondary font-weight-400" :title="opt.title">{{
                                                                                        truncate(opt.title, {length: 50})
                                                                                    }} <span class="text-danger" v-if="inp.props.required" title="Required" v-tooltip>*</span></label>
                                                                                <select class="form-control"
                                                                                        :name="'options[' + id + '][' + inp.props.name + ']'"
                                                                                        :title="inp.props.title"
                                                                                        :placeholder="inp.props.placeholder"
                                                                                        @change="updateOptions(id, inp.props.name, $event.target.value)"
                                                                                        v-select2="{ config: opt.select2, onChange: v => updateOptions(id, inp.props.name, v) }"
                                                                                        v-if="opt.type === 'ig_location'">
                                                                                    <option selected
                                                                                            :value="JSON.stringify(options[id][inp.props.name])"
                                                                                            v-if="options[id][inp.props.name] && options[id][inp.props.name].title">
                                                                                        {{
                                                                                            options[id][inp.props.name].title
                                                                                        }}
                                                                                    </option>
                                                                                </select>
                                                                                <!--                                                                                <input class="form-control"             -->
                                                                                <!--                                                                                       :value="options[id][inp.props.name]['index_' + inp.props.index]"                                                                             -->
                                                                                <!--                                                                                       :name="'options[' + id + '][' + inp.props.name + '][' + inp.props.name +']'" :title="inp.props.title" type="text" :placeholder="inp.props.placeholder" @change="updateOptions(id, inp.props.name + `[${inp.props.index}]`, $event.target.value,  inp.props.index)"-->
                                                                                <!--                                                                                       v-else-if="inp.props.name === 'image_alt_text'" />-->

                                                                                <input class="form-control"
                                                                                       :value="getOptionValue(options[id], inp.props.name)"
                                                                                       :name="getOptionName(inp.props.name, id)"
                                                                                       :title="inp.props.title"
                                                                                       type="text"
                                                                                       :placeholder="inp.props.placeholder"
                                                                                       @change="updateOptions(id, inp.props.name, $event.target.value)"
                                                                                       v-else/>

                                                                                <span
                                                                                    class="form-text small-2"
                                                                                    v-if="opt.description && opt.description.length">
                                                                                    {{ opt.description }}
                                                                                </span>
                                                                            </template>
                                                                            <template
                                                                                v-else-if="inp.elem === 'input' && inp.props.type === 'url'">
                                                                                <label class="text-secondary font-weight-400">{{ opt.title }} <span class="text-danger" v-if="inp.props.required" title="Required" v-tooltip>*</span></label>
                                                                                <input class="form-control"
                                                                                       :value="options[id][inp.props.name]"
                                                                                       :name="'options[' + id + '][' + inp.props.name + ']'"
                                                                                       :title="inp.props.title"
                                                                                       type="url"
                                                                                       :placeholder="inp.props.placeholder"
                                                                                       @change="updateOptions(id, inp.props.name, $event.target.value)"
                                                                                />

                                                                                <span
                                                                                    class="form-text small-2"
                                                                                    v-if="opt.description && opt.description.length">
                                                                                    {{ opt.description }}
                                                                                </span>
                                                                            </template>
                                                                            <template
                                                                                v-else-if="inp.elem === 'input' && inp.props.type === 'timestamp'">
                                                                                <label class="text-secondary font-weight-400">{{ opt.title }} <span class="text-danger" v-if="inp.props.required" title="Required" v-tooltip>*</span></label>
                                                                                <input class="form-control"
                                                                                       :value="options[id][inp.props.name]"
                                                                                       :name="'options[' + id + '][' + inp.props.name + ']'"
                                                                                       :title="inp.props.title"
                                                                                       type="text"
                                                                                       :placeholder="inp.props.placeholder"
                                                                                       v-datetimepicker="{format: opt.format, onChange: (date)=>updateOptions(id, inp.props.name, date) }"
                                                                                />

                                                                                <span
                                                                                    class="form-text small-2"
                                                                                    v-if="opt.description && opt.description.length">
                                                                                    {{ opt.description }}
                                                                                </span>
                                                                            </template>
                                                                            <template v-else-if="inp.elem === 'select'">
                                                                                {{ 
                                                                                    (() => {
                                                                                        if (!options[id][inp.props.name]) {
                                                                                            const selectedOpt = inp.options.find(opt => opt.selected);
                                                                                            options[id][inp.props.name] = selectedOpt ? selectedOpt.val : '';
                                                                                        }
                                                                                    })() 
                                                                                }}
                                                                                <label class="text-secondary font-weight-400"> {{ opt.title }} <span class="text-danger" v-if="inp.props.required" title="Required" v-tooltip>*</span></label>
                                                                                <select class="form-control small"
                                                                                    :name="'options[' + id + '][' + inp.props.name + ']'"
                                                                                    :title="inp.props.title || opt.description || opt.title"
                                                                                    @change="updateOptions(id, inp.props.name, $event.target.value)"
                                                                                    v-model="options[id][inp.props.name]"
                                                                                    :key="'options[' + id + '][' + inp.props.name + ']'">
                                                                                    <option
                                                                                        v-for="(option,index) in inp.options"
                                                                                        :key="'option['+index+'][' + id + '][' + inp.props.name + ']'"
                                                                                        :value="typeof option.val === 'string' ? option.val : option.text">
                                                                                        {{ option.text }}
                                                                                    </option>
                                                                                </select>

                                                                                <span
                                                                                    class="form-text small-2"
                                                                                    v-if="opt.description && opt.description.length">
                                                                                    {{ opt.description }}
                                                                                </span>
                                                                            </template>
                                                                            <template
                                                                                v-else-if="inp.elem === 'textarea'">
                                                                                <label class="text-secondary font-weight-400">{{ opt.title }} <span class="text-danger" v-if="inp.props.required" title="Required" v-tooltip>*</span></label>
                                                                                <textarea class="form-control"
                                                                                          :value="options[id][inp.props.name]"
                                                                                          :name="'options[' + id + '][' + inp.props.name + ']'"
                                                                                          :title="inp.props.title"
                                                                                          :placeholder="inp.props.placeholder"
                                                                                          @input="updateOptions(id, inp.props.name, $event.target.value)"></textarea>
                                                                                <span
                                                                                    class="form-text small-2"
                                                                                    v-if="opt.description && opt.description.length">
                                                                                    {{ opt.description }}
                                                                                </span>
                                                                            </template>
                                                                            <template v-if="inp.elem === 'input' && inp.props.type === 'file'">
                                                                                <div
                                                                                    class="form-group col-12 p-0">
                                                                                    <label class="text-secondary font-weight-400 mb-0">{{ opt.title }} <span class="text-danger" v-if="inp.props.required" title="Required" v-tooltip>*</span></label>
                                                                                    <span
                                                                                        class="form-text small-2 mt-0 mb-1 pb-2"
                                                                                        v-if="opt.description && opt.description.length">
                                                                                        {{ opt.description }}
                                                                                    </span>
                                                                                    <div class="container-fluid">
                                                                                        <div class="row border rounded">
                                                                                            <div class="col-12 px-0">
                                                                                                <button-uploaded-media
                                                                                                    :class="{'border-top': !!options[id][inp.props.name]}"
                                                                                                    :ref="'file_uploader_'+ id + '_'+ inp.props.name"
                                                                                                    :multiple="false"
                                                                                                    :validator="(type, showError, extraData) => validateAttachment(type, showError, extraData, inp.allowedAttachments, 'file_uploader_'+ id + '_'+ inp.props.name)"
                                                                                                    :compressImage="selectedAccounts.filter(a => a.type.includes('twitter'))"
                                                                                                    :filesToRender="options[id][inp.props.name] ? [options[id][inp.props.name]] : []"
                                                                                                    @change="files => {
                                                                                                        options[id][inp.props.name] = files.length ? files[0] : null 
                                                                                                    }"
                                                                                                />
                                                                                                <div
                                                                                                    class="border-top d-flex justify-content-between">
                                                                                                    <div class="dropdown">
                                                                                                        <button
                                                                                                            class="btn btn-sm btn-link text-dark text-secondary text-decoration-none"
                                                                                                            data-toggle="dropdown"
                                                                                                            aria-haspopup="true"
                                                                                                            aria-expanded="false"
                                                                                                            title="Attach Media"
                                                                                                            v-tooltip
                                                                                                            :class="{'border-right mr-1': attachments.length > 0}">
                                                                                                            <i class="ph ph-link"></i>
                                                                                                            <span
                                                                                                                class="d-none d-md-inline">Attach File</span>
                                                                                                            <i class="ph ph-caret-down"></i>
                                                                                                        </button>
                                                                                                        <div
                                                                                                            class="dropdown-menu dropdown-menu-right">
                                                                                                            <a class="dropdown-item"
                                                                                                            href="#"
                                                                                                            @click.prevent="$refs['file_uploader_' + id + '_'+ inp.props.name][0].selectFile()">
                                                                                                                <i class="ph ph-upload-simple ph-md mr-1"></i>
                                                                                                                Upload from
                                                                                                                Device
                                                                                                            </a>
                                                                                                            <a class="dropdown-item"
                                                                                                            href="#"
                                                                                                            @click.prevent="openCanva(file => $refs['file_uploader_' + id + '_'+ inp.props.name][0].onFilesSelected([file]))">
                                                                                                                <i class="ph ph-palette ph-md mr-1"></i>
                                                                                                                Design with
                                                                                                                Canva
                                                                                                            </a>
                                                                                                             <a class="dropdown-item"
                                                                                                                href="#"
                                                                                                                @click.prevent="openContentDrips(files => $refs['file_uploader_' + id + '_'+ inp.props.name][0].onFilesSelected(files))">
                                                                                                                    <i class="ph ph-palette ph-md mr-1"></i>
                                                                                                                    Design
                                                                                                                    with
                                                                                                                    Contentdrips
                                                                                                            </a>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </template>
                                                                        </div>

                                                                        <!-- custom inputs -->
                                                                        <div class="form-group col-12 mb-6"
                                                                             v-if="opt.type === 'threaded_replies'">
                                                                            <label class="mb-0 text-secondary font-weight-400 mb-3">{{ opt.title }}</label>
                                                                            <span
                                                                                class="form-text small-2 mt-0 mb-1 pb-2"
                                                                                v-if="opt.description && opt.description.length">
                                                                                {{ opt.description }}
                                                                            </span>

                                                                            <!-- important: the following lines initialize the required properties, this is a hack -->
                                                                            {{
                                                                                (!Array.isArray(options[id].threaded_replies) ? (options[id].threaded_replies = []) : null) && null
                                                                            }}

                                                                            <!-- render -->
                                                                            <div
                                                                                class="ml-4 pl-4 border-left mb-2 border-secondary"
                                                                                v-for="(tweet, ti) in options[id].threaded_replies"
                                                                                :key="uuid + '_' + id + '_' + ti+'_threaded_replies'"
                                                                                @paste="onThreadPaste($event,id,ti)"
                                                                                >
                                                                                    <div class="container-fluid">
                                                                                        <div class="row border rounded">

                                                                                            <div class="d-flex justify-content-end w-100 pt-2 pr-2 position-relative">
                                                                                                <i class="ph ph-x ph-md text-muted cursor-pointer position-absolute" style="right: 4px; top: 4px; z-index: 1000"
                                                                                                title="Remove"
                                                                                                v-tooltip
                                                                                                @click="()=> {
                                                                                                const newArr = [...options[id].threaded_replies];
                                                                                                newArr.splice(ti, 1);
                                                                                                updateOptions(id, 'threaded_replies', newArr);
                                                                                                    }"></i>
                                                                                            </div>

                                                                                            <div class="col-12 px-0">
                                                                                                <!-- important: rich-text should always have a col container -->
                                                                                                <RichText
                                                                                                    style="min-height: 60px;"
                                                                                                    placeholder="Write something..."
                                                                                                    :ref="'threadInput'+ id"
                                                                                                    class="pl-2 pr-2"
                                                                                                    :highlight="highlightConfig"
                                                                                                    :id="'options_' + id + '_threaded_replies_' + ti"
                                                                                                    v-model="options[id].threaded_replies[ti].text"
                                                                                                    @input="(content) =>{
                                                                                                        const newArr = [...options[id].threaded_replies];
                                                                                                        newArr[ti].text = content; 
                                                                                                        updateOptions(id, 'threaded_replies', newArr)
                                                                                                    }"
                                                                                                />
                                                                                            </div>
                                                                                            <div class="col-12 px-0">
                                                                                                <button-uploaded-media
                                                                                                    :ref="'threadUploadMedia'+ id"
                                                                                                    :multiple="true"
                                                                                                    :max="maximumAttachments"
                                                                                                    :validator="validateAttachment"
                                                                                                    :filesToRender="options[id].threaded_replies[ti].media"
                                                                                                    :compressImage="selectedAccounts.filter(a => a.type.includes('twitter'))"
                                                                                                    @change="files => {
                                                                                                        const newArr = [...options[id].threaded_replies];
                                                                                                        newArr[ti].media = files;
                                                                                                        updateOptions(id, 'threaded_replies', newArr);
                                                                                                    }"
                                                                                                />
                                                                                                    
                                                                                                <div
                                                                                                    class="d-flex justify-content-between align-items-center">
                                                                                                    <div class="d-flex">
                                                                                                        <div class="dropdown">
                                                                                                            <button type="button"
                                                                                                                class="btn btn-sm btn-link text-decoration-none cursor-pointer text-dark px-2"
                                                                                                                title="Insert an emoji"
                                                                                                                :class="{'bg-secondary text-dark shadow': show.threadEmojiPicker === ti}"
                                                                                                                v-tooltip
                                                                                                                @click="show.threadEmojiPicker = ti"
                                                                                                                v-click-outside="()=>{ show.threadEmojiPicker = null; }">
                                                                                                                <i class="ph ph-smiley"></i>
                                                                                                            </button>
                                                                                                                
                                                                                                            <div
                                                                                                                class="dropdown-menu dropdown-menu-left show p-0"
                                                                                                                v-if="show.threadEmojiPicker === ti"
                                                                                                                @click.stop="()=>{}">
                                                                                                                <emoji-picker
                                                                                                                    :on-select="emoji => insertThreadEmoji(emoji,id,ti)"/>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                        <div
                                                                                                            class="dropdown">
                                                                                                            <button
                                                                                                                class="btn btn-sm btn-link text-dark text-decoration-none px-2"
                                                                                                                data-toggle="dropdown"
                                                                                                                aria-haspopup="true"
                                                                                                                aria-expanded="false"
                                                                                                                title="Attach Media"
                                                                                                                v-tooltip
                                                                                                                :class="{'mr-1': attachments.length > 0}">
                                                                                                                <i class="ph ph-image"></i>
                                                                                                                    <span
                                                                                                                        class="d-none d-md-inline">Attach Media</span>
                                                                                                                    <i class="ph ph-caret-down"></i>
                                                                                                            </button>
                                                                                                            <div
                                                                                                                class="dropdown-menu dropdown-menu-right">
                                                                                                                <a class="dropdown-item"
                                                                                                                    href="#"
                                                                                                                    @click.prevent="$refs['threadUploadMedia' + id][ti].selectFile()">
                                                                                                                        <i class="ph ph-upload-simple ph-md mr-1"></i>
                                                                                                                        Upload
                                                                                                                        from
                                                                                                                        Device
                                                                                                                </a>
                                                                                                                <a class="dropdown-item"
                                                                                                                    href="#"
                                                                                                                    @click.prevent="openCanva(file => $refs['threadUploadMedia' + id][ti].onFilesSelected([file]))">
                                                                                                                        <i class="ph ph-palette ph-md mr-1"></i>
                                                                                                                        Design
                                                                                                                        with
                                                                                                                        Canva
                                                                                                                </a>
                                                                                                                <a class="dropdown-item"
                                                                                                                    href="#"
                                                                                                                    @click.prevent="openContentDrips(files => $refs['threadUploadMedia' + id][ti].onFilesSelected(files))">
                                                                                                                        <i class="ph ph-palette ph-md mr-1"></i>
                                                                                                                        Design
                                                                                                                        with
                                                                                                                        Contentdrips
                                                                                                                </a>
                                                                                                            </div>
                                                                                                        </div>
                                                                                                    </div>
                                                                                                    <div>
                                                                                                        <span
                                                                                                            class="small mr-2" :class="{ 'text-danger' : opts.account.post_maxlength - getTweetLength(options[id].threaded_replies[ti].text || '') < 0 }">
                                                                                                            {{
                                                                                                                opts.account.post_maxlength - getTweetLength(options[id].threaded_replies[ti].text || "")
                                                                                                            }}
                                                                                                        </span>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>

                                                                            <div
                                                                                class="">
                                                                                <button class="btn btn-sm btn-light"
                                                                                        @click.prevent="options[id].threaded_replies.push({text: '', media: []}) && $nextTick(()=>$forceUpdate())">
                                                                                    Add Another Post
                                                                                </button>
                                                                            </div>
                                                                        </div>
                                                                    </template>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                                <!-- preview -->
                                <div class="preview"
                                    :class="{'col-12': preview.show && renderedInPopup, ' col-12 col-md-5': preview.show && !renderedInPopup  }"
                                    v-if="preview.show && selectedAccounts.length">
                                    <div class="card border border-secondary">
                                        <div class="d-flex flex-column">
                                            <div class="mt-1 mb-0">

                                                <div class="d-flex justify-content-between align-items-center mx-3">
                                                    <p class="small-2 font-weight-500 text-dark mb-0">Preview</p>
                                                    <div class="d-flex align-items-center">
                                                        <div class="mr-1" style="z-index:1;">
                                                            <i class="ph ph-info info-icon-color"
                                                                title="This preview might not be always accurate"
                                                                v-tooltip></i>
                                                        </div>
                                                        <button type="button" class="btn btn-light  btn-sm"
                                                                @click="preview.show = false" v-if="renderedInPopup">
                                                            <i class="ph ph-arrow-left"></i> Back
                                                        </button>
                                                        <button type="button" class="close-button" aria-label="Close"
                                                                title="Close preview"
                                                                @click="preview.show = false"
                                                                v-tooltip
                                                                v-else>
                                                            <i class="ph ph-x"></i>
                                                        </button>
                                                    </div>
                                                </div>
    
                                                <div class="px-2">
                                                    <ul class="nav nav-tabs-minimal border-none p-0">
                                                        <li class="nav-item mb-4" v-for="(network, i) in previewAccountTypes"
                                                            :key="uuid + '__' + network + 'prev'">
                                                            <a class="nav-link border-none p-2 preview-network rounded-md" href="#"
                                                                @click.prevent="customizeContent ? ( preview.network = network && (customizeContentFor = previewAccountIdsByNetwork[network][0])) : preview.network = network"
                                                                :class="{'preview-network-selected': preview.network === network || (i===0 && (!preview.network || !previewAccountTypes.includes(preview.network)))} ">
                                                                
                                                                <img
                                                                    alt="account type"
                                                                    class=""
                                                                    :src="getIconForAccount(network, 'original')"
                                                                >
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
    
                                            </div>
                                        </div>

                                        <div class="tab-pane fade show">
                                            <div class="btn-group preview-button-group bg-white rounded-md"
                                                style="position: absolute;right: 12px;z-index: 1; margin-top: -24px;"
                                                v-if="Object.keys(previewAccountsByNetwork[preview.network]).length > 1">
                                                <button
                                                    type="button"
                                                    class="btn btn-xs border-none p-2"
                                                    @click="previewPostForPreviousAccount"
                                                    :disabled="preview.account[preview.network].index === 0"
                                                >
                                                    <i class="ph ph-caret-left"></i>
                                                </button>
                                                <button
                                                    class="btn btn-xs border-none p-2"
                                                    type="button"
                                                    @click="previewPostForNextAccount"
                                                    :disabled="preview.account[preview.network].index >= Object.keys(previewAccountsByNetwork[preview.network]).length - 1"
                                                >
                                                    <i class="ph ph-caret-right"></i>
                                                </button>
                                            </div>
                                            <Preview class=""
                                                :account="previewAccountsByNetwork[preview.network][preview.account[preview.network].id]"
                                                :text="content"
                                                :attachments="attachments"
                                                :options="options[previewAccountIdsByNetwork[preview.network][preview.account[preview.network].index]] || {}"
                                                :link="getLink()"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-footer p-5">
                        <div class="form-group d-md-flex justify-content-end align-items-start">
                            <div class="text-warning mr-2 mt-2"
                                 v-if="!canPost">
                                <i class="ph ph-warning ph-lg cursor-pointer"
                                   @click="alert(cannotPostReason)"
                                   v-tooltip="cannotPostReason"
                                ></i>
                            </div>
                            <div :title="cannotPostReason"
                                 v-tour:posteditor_postbtns="'After composing your post, you can publish it immediately, schedule it, or save it as draft and more'">
                                <template v-if="mode === 'queue_add' || mode === 'queue_edit'">
                                    <!-- have callback, so just call that on save -->
                                    <button class="btn btn-primary btn-sm " type="button"
                                            :disabled="!canPost"
                                            @click="submitForm">
                                        Save
                                    </button>
                                </template>
                                <template v-else>
                                    <button class="btn btn-light btn-sm" type="button"
                                            :disabled="!canPost"
                                            @click="postNow">
                                        {{ $lang.get('publish.post_now') }}
                                    </button>
                                    <div class="btn-group btn-group-xs rounded">
                                        <button class="btn btn-primary btn-sm pr-1" type="button"
                                                :disabled="!canPost"
                                                @click="openScheduleModal">
                                            {{ $lang.get('publish.schedule') }}
                                        </button>
                                        <button :disabled="!canPost" type="button"
                                                class="btn btn-primary btn-sm border-radius-right px-1"
                                                :id="'publishMenu' + uuid" data-toggle="dropdown" aria-haspopup="true"
                                                aria-expanded="false">
                                            <i class="ph ph-caret-down"></i>
                                        </button>
                                        <div class="dropdown-menu dropdown-menu-right"
                                             :aria-labelledby="'publishMenu' + uuid">
                                            <a href="#" class="dropdown-item"
                                               :class="{'disabled': !canPost}"
                                               @click.prevent="submitForm({isDraft: true})">
                                                Save as Draft
                                            </a>
                                            <a href="#" class="dropdown-item"
                                               :class="{'disabled': !canPost}" @click.prevent="openQueuesModal"
                                               v-if="mode !== 'edit'">
                                                Save to Queue
                                            </a>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- modal for schedule popup -->
        <div ref="schedule_modal" class="modal" role="dialog" tabindex="-1" aria-hidden="true">

            <div class="modal-dialog">
                <!-- Modal content-->
                <div class="modal-content border border-secondary shadow">
                    <div class="modal-header px-5 pt-4 mt-1 pb-0">
                    </div>
                    <!-- Modal Body -->
                    <div class="modal-body schedule_at_modal px-5 pt-3 pb-0">
                        <!-- Toggle Tabs -->
                        <div class="d-flex bg-light p-1 mb-4 rounded" style="gap: 2px; border-radius: 10px;">
                            <button
                                type="button"
                                class="btn py-2 px-3 border-0 text-center flex-fill rounded"
                                :class="[
                                    scheduleMode === 'single' ? 'btn-white text-dark fw-medium border' : 'text-muted'
                                ]"
                                @click="scheduleMode = 'single'">
                                Single Date
                            </button>
                            <button
                                type="button"
                                class="btn py-2 px-3 border-0 text-center flex-fill rounded"
                                :class="[
                                    scheduleMode === 'multiple' ? 'btn-white text-dark fw-medium border' : 'text-muted'
                                ]"
                                @click="scheduleMode = 'multiple'">
                                Multiple Dates
                            </button>
                        </div>
                        <!-- Content Area -->
                        <div>
                            <!-- Single Date Content -->
                            <div v-if="scheduleMode === 'single'" class="d-flex justify-content-center pr-2">
                                <div ref="publish_at_datetime"></div>
                            </div>

                            <!-- Multiple Dates Content -->
                            <div v-if="scheduleMode === 'multiple'">
                                <div class="mb-3">
                                    <div v-for="(dateTime, index) in publishAtArray" :key="index"
                                        @click="openDatePicker(index)" class="d-flex align-items-center justify-content-between bg-white m-2 cursor-pointer">
                                        <div class="d-flex py-2 px-3 border bg-white align-items-center rounded" style="width: 280px;">
                                            <i class="ph ph-calendar-blank mr-3 text-muted"></i>
                                            <span>{{ formatDateDisplay(dateTime.split(' ')[0]) }}</span>
                                        </div>
                                        <div class="d-flex py-2 px-3 border ml-3 bg-white align-items-center rounded" style="width: 166px; height: 44px;">
                                            <i class="ph ph-clock mr-2 text-muted"></i>
                                            <span>{{ formatTimeDisplay(dateTime.split(' ')[1]) }}</span>
                                        </div>
                                        <div>
                                            <button type="button" class="d-flex align-items-center justify-content-center p-1 btn border ml-3" @click.stop="removeDate(index)" style="width: 20px; height: 20px;">
                                                <i class="ph ph-x"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" class="btn border-0" @click="openDatePicker(-1)" style="color: #007bff;">
                                    <i class="ph ph-plus mr-2"></i>
                                    Add date
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Modal Footer -->
                    <div class="pt-0 mt-5 px-5 pb-5">
                        <div class="d-flex justify-content-between w-100">
                            <button type="button" class="btn btn-light border-0" @click="closeScheduleModal">
                                Cancel
                            </button>
                            <button type="button" class="btn btn-primary" @click="submitScheduledPost">
                                Schedule
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div ref="queue_modal" class="modal" role="dialog" tabindex="-1" aria-hidden="true">

            <div class="modal-dialog">
                <!-- Modal content-->
                <div class="modal-content border border-secondary shadow rounded-xl">
                    <div class="modal-header p-6">
                        <h5 class="modal-title">
                            Add to Queue
                        </h5>
                        <button type="button" class="close-button" @click="closeQueuesModal"><i class="ph ph-x"></i></button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group mb-2">
                            <div class="alert alert-warning"
                                 v-if="queues.length === 0">
                                There are no queues available. Please <a href="/app/publish/queues">create a queue
                                first</a>.
                            </div>
                            <select class="form-control" title="Select queue" v-selectpicker multiple
                                    ref="inputQueueSelect" v-else>
                                <option
                                    v-for="(queue,i) in queues" :key="uuid + 'q' + i" :value="queue.id">
                                    {{ queue.name }}
                                </option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer pt-6">
                        <button type="button"
                                class="btn btn-primary btn-md"
                                @click="submitQueuedPost">
                            Save
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Date Picker Modal for Multiple Dates -->
        <div v-if="showDatePickerModal" class="position-fixed d-flex align-items-center justify-content-center" style="top: 0; left: 0; right: 0; bottom: 0; background: rgba(0, 0, 0, 0.5); z-index: 9999;">
            <div class="bg-white rounded p-4 w-90" style="max-width: 500px;">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0">{{ editingDateIndex === -1 ? 'Add New Date' : 'Edit Date' }}</h6>
                    <button type="button" class="btn btn-sm btn-light border-0" @click="closeDatePicker">
                        <i class="ph ph-x"></i>
                    </button>
                </div>
                <div ref="multiple_date_picker"></div>
                <div class="mt-3 d-flex justify-content-end">
                    <button type="button" class="btn btn-light mr-2" @click="closeDatePicker">Cancel</button>
                    <button type="button" class="btn btn-primary" @click="saveDateSelection">{{ editingDateIndex === -1 ? 'Add' : 'Update' }}</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Cookies from "js-cookie";
import $ from "jquery";
import {capitalize, cloneDeep, debounce, isEqual, truncate, uniqBy, get, set} from "lodash";
import twitter from "twitter-text";
import {
    alertify,
    appConfig,
    axios,
    axiosErrorHandler,
    overlayLoaderHtml,
    getIconForAccount
} from "../../components";

import ButtonMediaSelect from "./common/ButtonMediaSelect.vue";
import EmojiPicker from "./common/EmojiPicker.vue";
import ButtonUploadedMedia from "./common/ButtonUploadedMedia.vue";
import LinkPreview from "./posteditor/LinkPreview.vue";
import Preview from "./posteditor/Preview";
import canva from "../../canva";
import contentDrips from '../../contentDrips';
import canvaHelper from "../../canva";
import {getAccounts} from "../../data";
import RichText from "./common/RichText";
import {store as emojiStore} from "emoji-mart-vue";
import {emojiIndex} from "emoji-mart";

let scheduledPosts = null;
let mediaModal = null;
export default {
    name: "post-editor",
    props: {
        renderedInPopup: Boolean,
        renderedInExtension: Boolean
    },
    components: {Preview, RichText, LinkPreview, ButtonMediaSelect, EmojiPicker, ButtonUploadedMedia},
    data() {
        return {
            scheduleMode: 'single',
            showDatePickerModal: false,
            editingDateIndex: -1,
            selectedDateTime: null,
            mode: "add", // default mode. is 'edit' when editing an existing post
            post: null, // in case of editing a post

            accounts: [],
            selectedAccountsIds: [],

            queues: [], // queues

            selectedTeam: null, // id

            selectedPostOptionAccountType:null,

            content: "",
            customizeContent: false, // customized post content for each account; false if off, or account id if on
            customizeContentFor: null, // account id
            customizedContent: {}, // customized content for each account

            allSuggestedHashtags: [],

            // toggles
            show: {
                suggestedHashtags: false,
                emojiPicker: false,
                threadEmojiPicker:null,
                postOptions: false,
                accounts: true,
                fullScreen: false, // uses full screen modal
                loader: false // busy indicator
            },

            attachments: [],

            preview: {
                network: null,
                show: false,
                account: {
                    facebook: {
                        id:null,
                        index:0
                    },
                    twitter: {
                        id:null,
                        index:0
                    },
                    instagram: {
                        id:null,
                        index:0
                    },
                    linkedin: {
                        id:null,
                        index:0
                    },
                    "google.location": {
                        id:null,
                        index:0
                    },
                    "google.youtube": {
                        id:null,
                        index:0
                    },
                    mastodon: {
                        id:null,
                        index:0
                    },
                    tiktok: {
                        id:null,
                        index:0
                    },
                    pinterest: {
                        id:null,
                        index:0
                    },
                    reddit: {
                        id:null,
                        index:0
                    },
                    threads: {
                        id:null,
                        index:0
                    },
                    bluesky: {
                        id:null,
                        index:0
                    },
                }
            },

            options: {}, // object to hold post option values (user inputs)

            // to display client-side recommendations/notice if user uploads a media with non-standard aspect ratio
            attachmentRecommendedAspectRatios: {
                // TODO: update ratios for linkedin
                image: {
                    "instagram.direct": ["1:1", "1.91:1", "4:5", "16:9"],
                    "instagram.api": ["1:1", "1.91:1", "4:5", "16:9"],
                    "tiktok.profile": ["1:1", "1.91:1", "4:5", "16:9"],
                    "facebook.page": ["1:1", "1.91:1", "4:5", "2:3", "16:9", "9:16"],
                    "facebook.group": ["1:1", "1.91:1", "4:5", "2:3", "16:9", "9:16"],
                    "twitter.profile": ["1:1", "1.91:1", "16:9"],
                    "mastodon.profile": ["1:1", "1.91:1", "16:9"],
                    "linkedin.profile": ["1:1"],
                    "linkedin.org": ["1:1"],
                    "linkedin.brand": ["1:1"],
                    "google.location": ["4:3"],
                    "pinterest.profile": ["2:3"],
                    "reddit.profile": ["1:1", "1.91:1", "4:5", "2:3", "16:9", "9:16"],
                    "reddit.subreddit": ["1:1", "1.91:1", "4:5", "2:3", "16:9", "9:16"],
                    "threads.profile": ["1:1", "1.91:1", "16:9"],
                    "bluesky.profile": ["1:1", "1.91:1", "16:9"],
                },
                video: {
                    "instagram.direct": ["1:1", "1.91:1", "4:5", "16:9"],
                    "instagram.api": ["1:1", "1.91:1", "4:5", "16:9"],
                    "facebook.page": ["16:9", "4:5", "1:1", "2:3", "9:16"],
                    "facebook.group": ["16:9", "4:5", "1:1", "2:3", "9:16"],
                    "twitter.profile": ["16:9", "4:5", "1:1", "2:3", "9:16"],
                    "mastodon.profile": ["16:9", "4:5", "1:1", "2:3", "9:16"],
                    "linkedin.profile": ["1:1"],
                    "linkedin.org": ["1:1"],
                    "linkedin.brand": ["1:1"],
                    "google.location": ["1:1"],
                    "tiktok.profile": ["16:9", "9:16"],
                    "pinterest.profile": ["2:3"],
                    "google.youtube": ["16:9", "9:16"],
                    "reddit.profile": ["1:1", "1.91:1", "4:5", "2:3", "16:9", "9:16"],
                    "reddit.subreddit": ["1:1", "1.91:1", "4:5", "2:3", "16:9", "9:16"],
                    "threads.profile": ["16:9", "4:5", "1:1", "2:3", "9:16"],
                }
            },
            publishAt: "",
            initialized: false
        };
    },
    computed: {
        overlayLoaderHtml: () => overlayLoaderHtml,
        publishAtArray: {
            get() {
                // Always return an array - if publishAt is a single date, wrap it in array
                if (Array.isArray(this.publishAt)) {
                    return this.publishAt;
                } else if (this.publishAt) {
                    return [this.publishAt];
                } else {
                    return [];
                }
            },
            set(value) {
                this.publishAt = value;
            }
        },
        myCompany() {
            return appConfig.userCompany;
        },
        teams() {
            const teamsById = {};
            this.accounts.filter(a => a.team).forEach(a => {
                teamsById[a.team.id] = a.team;
            });
            return Object.values(teamsById);
        },
        currentTeam() {
            return this.teams.find(t => t.id === this.selectedTeam);
        },
        canPost() {
            return !this.cannotPostReason;
        },
        availableAccounts() {
            if (this.selectAccounts) {
                return this.accounts.filter(a => this.selectAccounts.includes(a.id));
            } else if (this.post) {
                // editing a post
                let _this = this;
                return [this.accounts.filter(a => _this.post.account_id === a.id).pop()];
            }
            return this.selectedTeam > 0
                ? this.accounts.filter(a => this.selectedTeam === a.team_id)
                : this.accounts.filter(a => !a.team_id);
        },
        selectedAccounts() {
            return this.availableAccounts.filter(a => this.selectedAccountsIds.includes(a.id));
        },
        shouldCustomizeContent() {
            return this.customizeContent && this.selectedAccounts.length > 1;
        },
        customizeContentForAccount() {
            return this.availableAccounts.find(a => a.id === this.customizeContentFor);
        },
        postLength() {
            if (this.selectedAccounts.find(a => ["twitter.profile", "mastodon.profile", "bluesky.profile"].includes(a.type))) {
                return this.getTweetLength(this.content);
            }
            return this.content.length;
        },
        hashtags() {
            return twitter.extractHashtags(this.content);
        },
        invalidOptionReason() {
            let instaAccounts = this.selectedAccounts.filter(a => a.type.includes('instagram'));
            let fbAccounts = this.selectedAccounts.filter(a => a.type.includes('facebook'));
            let twitterAccounts = this.selectedAccounts.filter(a => a.type.includes('twitter'));
            let linkedinAccounts = this.selectedAccounts.filter(a => a.type.includes('linkedin'));
            let pinterestAccounts = this.selectedAccounts.filter(a => a.type.includes('pinterest'));
            let tiktokAccounts = this.selectedAccounts.filter(a => a.type.includes('tiktok'));
            let redditAccounts = this.selectedAccounts.filter(a => a.type.includes('reddit'));
            let blueskyAccounts = this.selectedAccounts.filter(a => a.type.includes('bluesky'));

            const tweetExceedingLimit = (id) => {
                const maxLimit = twitterAccounts.find(a => a.id === id)?.post_maxlength || 280;
                return this.options[id] && this.options[id].threaded_replies?.some(reply => {
                    return this.getTweetLength(reply.text) > maxLimit
                });
            }

            if (instaAccounts.some(account => twitter.extractMentions((this.options[account.id] && this.options[account.id].comment) || '').length > 10)) {
                return "Only 10 mentions are allowed in Instagram first comment";
            } else if (instaAccounts.some(account => this.options[account.id] && this.options[account.id].post_as_story && (this.attachments.length > 1)) || fbAccounts.some(account => this.options[account.id] && this.options[account.id].post_as_story && (this.attachments.length > 1))) {
                return "Only 1 media attachment is allowed for a story.";
            } else if (twitterAccounts.some(a => tweetExceedingLimit(a.id))) {
                return 'Twitter reply exceeds the character limit.';
            }

            if(pinterestAccounts.some(account => !this.options[account.id] || !this.options[account.id].board_name)){
                return "Select Board in the options for Pinterest.";
            }

            if(pinterestAccounts.some(account => this.attachment && this.attachment.type.includes("video") && this.options[account.id] && !this.options[account.id].link_thumbnail)){
                return "Attach video thumbnail for Pinterest.";
            }
            if (tiktokAccounts.some(account => this.options[account.id] && !this.options[account.id].privacy_status)) {
                return "Please select privacy status for TikTok.";
            }
            
            if (tiktokAccounts.some(account => this.options[account.id] && this.options[account.id].title && this.options[account.id].title.length > 90)) {
                 return "Title for TikTok should be less than 90 characters.";
            }


            if(this.attachment && (["pdf", "doc", "docx", "ppt", "pptx"].indexOf(this.attachment.type) > -1) && linkedinAccounts.some(account => this.options[account.id] && !this.options[account.id].document_title )){
                return "Document title is required.";
            }

            if (linkedinAccounts.some(account => this.options[account.id] && this.options[account.id].customize_link && !this.options[account.id].link_title)) {
                return "Link title is required.";
            }

            if (blueskyAccounts.length && this.attachment){
                for (const blueskyAccount of blueskyAccounts) {
                    const altTexts = this.options[blueskyAccount.id]?.media_alt_text || [];
                    for (let i = 0; i < altTexts.length; i++) {
                        const altText = altTexts[i];
                        if (altText.length > 2000) {
                            return "Bluesky alt text cannot exceed 2000 characters.";
                        }
                    }
                }
            }
            
            if (redditAccounts.length) {	
                for (const redditAccount of redditAccounts) {
                    const redditOptions = this.options[redditAccount.id];
                    if (this.attachment && !this.postLength && (!redditOptions || !redditOptions.title)) {
                        return "Please add content or title for media.";
                    }
                }
            } else {
                return null;
            }
        },
        previewAccountTypes() {
            const allTypes = [...new Set(this.selectedAccounts.map(a => a.type))].map(t => {
                if (t.includes("google.")) {
                    return t; // return google.youtube, google.location, etc
                } else {
                    return t.split(".")[0]; // return facebook for facebook.page, facebook.group, etc
                }
            });
            return [...new Set(allTypes)];
        },
        previewAccountsByNetwork() {
            const data = {};
            this.previewAccountTypes.forEach(t => {
                const accountsForType = t.includes(".")
                    ? this.selectedAccounts.filter(a => a.type === t)  // Full type match
                    : this.selectedAccounts.filter(a => a.type.split(".")[0] === t);  // Short type match

                data[t] = {};
                accountsForType.forEach(account => {
                    data[t][account.id] = account; 
                });
            });

            return data;
        },
        previewAccountIdsByNetwork() {
            let accountId = {};

            Object.keys(this.previewAccountsByNetwork).forEach(platform => {
                accountId[platform] = Object.keys(this.previewAccountsByNetwork[platform]).map(id => parseInt(id));
            });

            return accountId;
        },
        postOptionAccountTypes() {
            const allTypes = [...new Set(this.selectedAccounts.map(a => a.type))].map(t => {
                if (t.includes("google.")) {
                    return t; // return google.youtube, google.location, etc
                } else {
                    return t.split(".")[0]; // return facebook for facebook.page, facebook.group, etc
                }
            });
            return [...new Set(allTypes)];
        },
        postOptionAccounts() {
            const data = {};
            this.previewAccountTypes.forEach(t => {
                if (t.includes(".")) {
                    // full type
                    data[t] = this.selectedAccounts.filter(a => a.type === t);
                } else {
                    // short type, e.g. facebook, twitter, etc
                    data[t] = this.selectedAccounts.filter(a => a.type.split(".")[0] === t);
                }
            });
            return data;
        },
        focusedAccount() {
            let account = null;
            if (this.shouldCustomizeContent && this.customizeContentForAccount) {
                // if we are customizing content for a specific account, we need to highlight the content for that account
                account = this.customizeContentForAccount;
            } else if (this.selectedAccounts.length === 1) {
                // there is only one account selected, so we can highlight the content for that account
                account = this.selectedAccounts[0];
            }
            return account;
        },
        highlightConfig() {
            let account = this.focusedAccount;

            if (account) {
                if (account.type === "twitter.profile") {
                    return [
                        "hashtag",
                        "mention",
                        "link",
                    ];
                } else if (account.type === "mastodon.profile") {
                    return [
                        "hashtag",
                        "mention",
                        "mastodon_mention",
                        "link",
                    ];
                } else if (account.type.includes("linkedin")) {
                    return [
                        "hashtag",
                        "link",
                    ];
                } else if (account.type.includes("facebook")) {
                    return [
                        "hashtag",
                        "link",
                    ];
                } else if (account.type === "google.location") {
                    return []; // nothing to highlight
                } else if (account.type.includes("instagram")) {
                    return [
                        "hashtag",
                        "mention",
                    ];
                } else if (account.type === "threads.profile") {
                    return [
                        "hashtag",
                        "mention",
                        "link",
                    ];
                } else if (account.type === "bluesky.profile") {
                    return [
                        "hashtag",
                        "mention",
                        "link",
                    ];
                }
            }

            // default to highlighting all
            return [
                "hashtag",
                "mention",
                "link",
            ];
        },
        autocompleteConfig() {
            const config = {
                possibleTriggers: ["@", ":", "/", "#", "/account", "/team"],
                handlers: [
                    {
                        trigger: "@",
                        liveSearch: true,
                        allowSpaceInQuery: true,
                        loader: true, // support loading spinner
                        getData: async (term) => {
                            let data = [];
                            if (term) {
                                if (this.focusedAccount && this.focusedAccount.type.includes('twitter')) {
                                    try {
                                        const res = await axios.get("/api/v1/search/twitter_users", {
                                            params: {
                                                q: term,
                                                account_id: this.focusedAccount.id
                                            }
                                        });
                                        if (res.data.items) {
                                            data = res.data.items.map(element => {
                                                return {
                                                    title: element.title,
                                                    image: element.image,
                                                    description: '@' + element.screen_name,
                                                    verified: element.verified,
                                                    html: `<div class="d-flex align-items-center">
                                                                <h6 class="mb-0">${element.title}</h6>
                                                                ${element.verified ? `<span><i class="ph ph-check-decagram verified_icon"></i></span>` : ''}
                                                            </div>`,
                                                    onSelect(api) {
                                                        api.addMention({
                                                            id: element.id_str,
                                                            text: element.screen_name,
                                                        });
                                                        return false;
                                                    }
                                                };
                                            });
                                        }
                                    } catch (e) {
                                        console.error(e);
                                    }
                                } else if(this.focusedAccount && this.focusedAccount.type.includes('linkedin')){
                                    try {
                                        const res = await axios.get("/api/v1/search/linkedin_companies", {
                                            params: {
                                                q: term,
                                                account_id: this.focusedAccount.id
                                            }
                                        });
                                        if(res.data.items){
                                            data = res.data.items.map(element => {
                                                return {
                                                    title: element.title,
                                                    image: element.image,
                                                    onSelect(api) {
                                                        let entityParts = element.entity.split(':'); //entity: urn:li:organization:2414183 or urn:li:person:2414183
                                                        api.addMention({
                                                            id: entityParts[2] + '-' + entityParts[3], //id: organzition-********* or person-*********
                                                            text: element.title,
                                                        });
                                                        return  false;
                                                    }
                                                };
                                            });
                                        }
                                    } catch (e) {
                                        console.error(e);
                                    }
                                }
                            }
                            return data;
                        }
                    },
                    {
                        trigger: ":",
                        liveSearch: true, // no live search
                        async getData(term) {
                            if (!term) {
                                let frequentData = emojiStore.get("frequently");
                                if (!frequentData) {
                                    frequentData = {};
                                }
                                // return frequent emojis
                                return Object.keys(frequentData)
                                    .map(name => {
                                        if (!name) return false;
                                        const emojiSearch = emojiIndex.search(name);
                                        if (!emojiSearch || !emojiSearch.length) {
                                            return false;
                                        }
                                        const emoji = emojiSearch[0];
                                        return {
                                            score: frequentData[name],
                                            title:
                                                emoji.native +
                                                " " +
                                                truncate(emoji.short_names.length ? emoji.short_names[0] : emoji.id, {
                                                    length: 15
                                                }),
                                            onSelect() {
                                                if (!emoji.id) {
                                                    return false;
                                                }
                                                const frequentData = emojiStore.get("frequently");
                                                frequentData[emoji.id] = frequentData[emoji.id] + 1 || 1;
                                                emojiStore.set("frequently", frequentData);
                                                return emoji.native + " ";
                                            }
                                        };
                                    })
                                    .filter(e => e)
                                    .sort((a, b) => b.score - a.score)
                                    .map(o => {
                                        delete o.score;
                                        return o;
                                    });
                            } else {
                                return emojiIndex.search(term).map(emoji => {
                                    return {
                                        title:
                                            emoji.native +
                                            " " +
                                            truncate(emoji.short_names.length ? emoji.short_names[0] : emoji.id, {
                                                limit: 20
                                            }),
                                        onSelect() {
                                            if (!emoji.id) {
                                                return false;
                                            }
                                            const frequentData = emojiStore.get("frequently");
                                            frequentData[emoji.id] = frequentData[emoji.id] + 1 || 1;
                                            emojiStore.set("frequently", frequentData);
                                            return emoji.native + " ";
                                        }
                                    };
                                });
                            }
                        }
                    },
                    {
                        trigger: "#",
                        liveSearch: false,
                        getData: async () => {
                            return this.suggestedHashtags.map(hashtag => {
                                return {
                                    title: "#" + hashtag,
                                    onSelect() {
                                        return "#" + hashtag + " ";
                                    }
                                };
                            });
                        }
                    },
                    {
                        trigger: "/",
                        liveSearch: false, // no live search
                        getData: async () => {
                            // commands for editor
                            const items = [
                                {
                                    title: "Attach media",
                                    icon: "ph ph-image ph-lg",
                                    description: "",
                                    onSelect: () => {
                                        this.$refs.mediaSelectBtn.selectFile();
                                        return false;
                                    }
                                },
                                ...this.selectedAccounts.length > 1 ? [
                                    {
                                        title: "Customize content",
                                        description: "",
                                        icon: "ph ph-note-pencil ph-lg",
                                        onSelect: () => {
                                            this.customizeContent = !this.customizeContent;
                                            return false;
                                        }
                                    }
                                ] : [],
                                ...this.mode === 'add' ? [
                                    {
                                        title: "Select accounts",
                                        description: "",
                                        icon: "ph ph-user ph-lg",
                                        onSelect: api => {
                                            api.openAutocomplete("/account");
                                            return false;
                                        }
                                    }
                                ] : [],
                            ];

                            // autocomplete by AI for focusedAccount
                            if (this.focusedAccount) {
                                items.unshift({
                                    title: "Autocomplete",
                                    description: "",
                                    icon:'ph ph-magic-wand ph-lg',
                                    onSelect: () => {
                                        this.$nextTick(this.autocompleteByAI);
                                        return false;
                                    }
                                });
                            }

                            // if there are teams, add team entry too
                            if (this.teams.length && this.mode === 'add') {
                                items.push({
                                    title: "Switch team",
                                    description: "",
                                    icon: "ph ph-users ph-lg",
                                    onSelect: api => {
                                        api.openAutocomplete("/team");
                                        return false;
                                    }
                                });
                            }

                            if (this.canPost) {
                                items.push(
                                    {
                                        title: "Preview",
                                        description: "",
                                        icon: "ph ph-eye ph-lg",
                                        onSelect: () => {
                                            this.preview.show = !this.preview.show;
                                            return false;
                                        }
                                    },
                                    {
                                        title: "Save as draft",
                                        description: "",
                                        icon: "ph ph-file-dashed ph-lg",
                                        onSelect: () => {
                                            this.submitForm({isDraft: true});
                                            return false;
                                        }
                                    },
                                    {
                                        title: "Post now",
                                        description: "",
                                        icon: "ph ph-arrow-up-right ph-lg",
                                        onSelect: () => {
                                            this.postNow();
                                            return false;
                                        }
                                    }
                                );
                            }

                            // close the post composer
                            items.push({
                                title: "Close",
                                icon: "ph ph-x ph-lg",
                                description: "",
                                onSelect: () => {
                                    $(this.$refs.modal).modal("hide");
                                    return false;
                                }
                            });

                            return items;
                        }
                    },
                    {
                        trigger: "/account",
                        liveSearch: false,
                        allowSpaceInQuery: true,
                        getData: async () => {
                            return [
                                {
                                    title: "All accounts",
                                    icon: "ph ph-check-circle-outline ph-lg",
                                    description: "Toggle all accounts for this post",
                                    onSelect: () => {
                                        this.onSelectAll();
                                        return false;
                                    }
                                },
                                ...this.availableAccounts.map(account => {
                                    return {
                                        title: account.name,
                                        description: account._type,
                                        image: account.image,
                                        onSelect: () => {
                                            this.toggleAccountSelection(account.id, account.type);
                                            return false;
                                        }
                                    };
                                })
                            ];
                        }
                    },
                    {
                        trigger: "/team",
                        liveSearch: false,
                        allowSpaceInQuery: true,
                        getData: async () => {
                            return [
                                ...this.teams.filter(t => t.id !== this.selectedTeam).map(team => {
                                    return {
                                        title: team.name,
                                        onSelect: () => {
                                            this.showAccountsForTeam(team.id);
                                            return false;
                                        }
                                    };
                                }),
                                ...(this.selectedTeam > 0
                                    ? [
                                        {
                                            title: "My accounts",
                                            onSelect: () => {
                                                this.showAccountsForTeam(null);
                                                return false;
                                            }
                                        }
                                    ]
                                    : [])
                            ];
                        }
                    }
                ]
            };

            // account-specific auto-completions (e.g. for @mentions)
            let account = this.focusedAccount;
            if (account) {
                // todo: add autocomplete handler for @ for current account
            }

            return config;
        },
        postOptions() {
            const allOptions = {};
            if (this.noOptions) return allOptions;
                if(this.selectedPostOptionAccountType && this.postOptionAccounts && this.postOptionAccounts[this.selectedPostOptionAccountType]){
                    this.postOptionAccounts[this.selectedPostOptionAccountType].forEach(acc => {
                        const options = [];
                        if (acc.type.includes("instagram")) {
                            const _this = this;
                            options.push({
                                    title: "Post as Reel",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 6,
                                            props: {
                                                name: "post_as_reel",
                                                type: "checkbox",
                                                disabled: !this.attachment || !(this.attachment.mime?.includes("video") || this.attachment.type?.includes("video")),
                                            },
                                            onUpdate(val, allOptions) {
                                                if (allOptions.post_as_story) {
                                                    _this.options[acc.id].post_as_story = false;
                                                }
                                            }
                                        }
                                    ],
                                },
                                {
                                    title: "Post as Story",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 6,
                                            props: {
                                                name: "post_as_story",
                                                type: "checkbox",
                                                disabled: !this.attachment,
                                            },
                                            onUpdate(val, allOptions) {
                                                if (allOptions.post_as_reel) {
                                                    _this.options[acc.id].post_as_reel = false;
                                                }
                                            }
                                        }
                                    ]
                                }
                            );
                            const postAsReel = this.options[acc.id] && this.options[acc.id].post_as_reel;
        
                            if (postAsReel) {
                                options.push({
                                    title: "Share reel to feed",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 6,
                                            props: {
                                                name: "share_reel_to_feed",
                                                type: "checkbox"
                                            }
                                        }
                                    ]
                                });
                            }
        
                            const postAsStory = this.options[acc.id] && this.options[acc.id].post_as_story;
        
                            if(!postAsStory){
                                options.push({
                                    title: "Add collaborators",
                                    description: "Add up to 3 public accounts as collaborators, separated by commas.",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 12,
                                            props: {
                                                name: "collaborators",
                                                type: "text",
                                                placeholder: "socialbu,instagram,meta"
                                            }
                                        }
                                    ]
                                },
                                {
                                    title: "Comment",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "textarea",
                                            colWidth: 12,
                                            props: {
                                                name: "comment",
                                                placeholder: "Add first comment to your post "
                                            }
                                        }
                                    ]
                                });
                                if(this.attachment && (this.attachment.mime?.includes("video") || this.attachment.type?.includes("video"))){
                                    options.push(
                                        {
                                            title: "Attach Thumbnail",
                                            description: "",
                                            inputs: [
                                                {
                                                    elem: "input",
                                                    colWidth: this.selectedAccounts.length > 1 ? 12 : 6,
                                                    props: {
                                                        name: "thumbnail",
                                                        type: "file",
                                                    },
                                                    allowedAttachments: ['jpg', 'png', 'jpeg']
                                                }
                                            ]
                                        }
                                    )
                                }
                            }
        
                        } else if (acc.type.includes("facebook")) {
                            if (this.getLinks().length > 1 && !this.attachment) {
                                options.push({
                                    title: "Attach link",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 12,
                                            props: {
                                                name: "link",
                                                type: "text",
                                                placeholder: "Leave empty for the first link"
                                            }
                                        }
                                    ]
                                });
                            }
                            if (this.getLink() && !this.attachment) {
                                options.push({
                                    title: "Trim link from post text",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 12,
                                            props: {
                                                name: "trim_link_from_content",
                                                type: "checkbox"
                                            }
                                        }
                                    ]
                                });
                            }
                            const postAsStory = this.options[acc.id] && this.options[acc.id].post_as_story;
                            const postAsReel = this.options[acc.id] && this.options[acc.id].post_as_reel;

                            if (!this.getLink() && this.attachment && !postAsReel) {
                                options.push({
                                    title: "Post as story",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 12,
                                            props: {
                                                name: "post_as_story",
                                                type: "checkbox"
                                            }
                                        }
                                    ]
                                });
                            }
                            if(this.attachment && (this.attachment.mime?.includes("video") || this.attachment.type?.includes("video")) && !postAsStory){
                                options.push({
                                    title: "Post as Reel",
                                    description: "Reel should be maximum 90 seconds",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 12,
                                            props: {
                                                name: "post_as_reel",
                                                type: "checkbox"
                                            }
                                        }
                                    ]
                                });

                                options.push(
                                    {
                                        title: "Attach Thumbnail",
                                        description: "",
                                        inputs: [
                                            {
                                                elem: "input",
                                                colWidth: this.selectedAccounts.length > 1 ? 12 : 6,
                                                props: {
                                                    name: "thumbnail",
                                                    type: "file",
                                                },
                                                allowedAttachments: ['jpg', 'png', 'jpeg']
                                            }
                                        ]
                                    }
                                )
                            }
                            if (this.attachment && (this.attachment.mime?.includes("video") || this.attachment.type?.includes("video")) && !postAsStory && !postAsReel) {
                                options.push({
                                    title: "Title",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 12,
                                            props: {
                                                name: "video_title",
                                                placeholder: "Add title for your video",
                                                type: "text"
                                            }
                                        }
                                    ]
                                });
                            }
                            if(!postAsStory && !postAsReel){
                                options.push(
                                    { 
                                        title: "Comment",
                                        description: "",
                                        inputs: [
                                            {
                                                elem: "textarea",
                                                colWidth: 12,
                                                props: {
                                                    name: "comment",
                                                    placeholder: "Add first comment to your post "
                                                }
                                            }
                                        ]
                                    },
                                );
                            }
                        } else if (acc.type.includes("linkedin")) {
                            if (this.getLinks().length > 1 && !this.attachment) {
                                options.push({
                                    title: "Attach link",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 12,
                                            props: {
                                                name: "link",
                                                type: "text",
                                                placeholder: "Leave empty for the first link"
                                            }
                                        }
                                    ]
                                });
                            }
        
                            if (this.getLink() && !this.attachment) {
                                options.push({
                                        title: "Trim link from post text",
                                        description: "",
                                        inputs: [
                                            {
                                                elem: "input",
                                                colWidth: 12,
                                                props: {
                                                    name: "trim_link_from_content",
                                                    type: "checkbox"
                                                }
                                            }
                                        ]
                                    },
                                    {
                                        title: "Customize Link",
                                        description: "",
                                        inputs: [
                                            {
                                                elem: "input",
                                                colWidth: 12,
                                                props: {
                                                    name: "customize_link",
                                                    type: "checkbox"
                                                }
                                            }
                                        ]
                                    }
                                );
                            }
                            let customizeLink = this.options[acc.id] && this.options[acc.id].customize_link;
                            if (customizeLink) {
                                options.push({
                                    title: "Link Title",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 12,
                                            props: {
                                                name: "link_title",
                                                type: "text",
                                                required: true
                                            }
                                        }
                                    ]
                                });
                                options.push({
                                    title: "Link Description",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 12,
                                            props: {
                                                name: "link_description",
                                                type: "text",
                                            }
                                        }
                                    ]
                                });
                                options.push(
                                    {
                                        title: "Attach Link Thumbnail",
                                        description: "",
                                        inputs: [
                                            {
                                                elem: "input",
                                                colWidth: 6,
                                                props: {
                                                    name: "thumbnail",
                                                    type: "file",
                                                },
                                            }
                                        ]
                                    }
                                )
                            }
                            options.push(
                                {
                                    title: "Comment",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "textarea",
                                            colWidth: 12,
                                            props: {
                                                name: "comment",
                                                placeholder: "Add first comment to your post "
                                            }
                                        }
                                    ]
                                },
                            );
                            if (this.attachment && (["pdf", "doc", "docx", "ppt", "pptx"].indexOf(this.attachment.type) > -1)) {
                                options.push({
                                    title: "Document Title",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 12,
                                            props: {
                                                name: "document_title",
                                                placeholder: "Add a descriptive title to your document",
                                                required: true,
                                                type: "text"
                                            }
                                        }
                                    ]
                                });
                            }
                        } else if (acc.type.includes("twitter")) {
                            options.push({
                                title: "Threaded Replies",
                                description: "",
                                type: "threaded_replies"
                            });
                        } else if (acc.type.includes("mastodon")) {
                            if (this.attachment) {
                                options.push({
                                    title: "Mark as sensitive",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 12,
                                            props: {
                                                name: "mark_sensitive",
                                                type: "checkbox"
                                            }
                                        }
                                    ]
                                });
                            }
                            options.push({
                                title: "Threaded Replies",
                                description: "",
                                type: "threaded_replies"
                            });
                            options.push({
                                title: "Spoiler Text",
                                description: "",
                                inputs: [
                                    {
                                        elem: "input",
                                        colWidth: 12,
                                        props: {
                                            name: "spoiler",
                                            placeholder: "Add a spoiler text to your post",
                                            type: "text"
                                        }
                                    }
                                ]
                            });
                        } else if (acc.type === "google.location") {
        
                            options.push({
                                title: "Post Type",
                                description: "",
                                inputs: [
                                    {
                                        elem: "select",
                                        colWidth: 12,
                                        props: {
                                            name: "topic_type",
                                            title: "Standard"
                                        },
                                        options: [
                                            {
                                                text: "Standard",
                                                val: "",
                                                selected: true
                                            },
                                            {
                                                text: "Event",
                                                val: "EVENT"
                                            },
                                            {
                                                text: "Offer",
                                                val: "OFFER"
                                            }
                                        ]
                                    }
                                ]
                            });
        
                            const topicType = this.options[acc.id] && this.options[acc.id].topic_type;
        
                            if (this.attachments.length > 0) {
                                options.push({
                                    title: "Save post media to gallery",
                                    description: "Save post media to your public gallery",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 12,
                                            props: {
                                                name: "save_media_to_gallery",
                                                type: "checkbox"
                                            }
                                        }
                                    ]
                                });
                            }
        
                            if (topicType === "EVENT" || topicType === "OFFER") {
                                // schedule details
                                options.push(
                                    {
                                        title: "Title",
                                        description: "",
                                        inputs: [
                                            {
                                                elem: "input",
                                                colWidth: 12,
                                                props: {
                                                    name: "event_title",
                                                    placeholder: topicType === "EVENT" ? "Title of the event": "Title of the offer",
                                                    type: "text",
                                                    required: true
                                                }
                                            }
                                        ]
                                    },
                                    {
                                        title: "Start At",
                                        description: "",
                                        format: "DD/MM/YYYY hh:mm a",
                                        inputs: [
                                            {
                                                elem: "input",
                                                colWidth: 12,
                                                props: {
                                                    name: "event_start",
                                                    placeholder: "Click to select",
                                                    type: "timestamp",
                                                    required: true
                                                }
                                            }
                                        ]
                                    },
                                    {
                                        title: "End At",
                                        description: "",
                                        format: "DD/MM/YYYY hh:mm a",
                                        inputs: [
                                            {
                                                elem: "input",
                                                colWidth: 12,
                                                props: {
                                                    name: "event_end",
                                                    placeholder: "Click to select",
                                                    type: "timestamp",
                                                    required: true
                                                }
                                            }
                                        ]
                                    }
                                );
                            }
                            if (topicType === "OFFER") {
                                // offer specific inputs
                                options.push(
                                    {
                                        title: "Coupon",
                                        description: "",
                                        inputs: [
                                            {
                                                elem: "input",
                                                colWidth: 12,
                                                props: {
                                                    name: "offer_coupon",
                                                    placeholder: "Coupon code for the offer",
                                                    type: "text"
                                                }
                                            }
                                        ]
                                    },
                                    {
                                        title: "URL",
                                        description: "",
                                        inputs: [
                                            {
                                                elem: "input",
                                                colWidth: 12,
                                                props: {
                                                    name: "offer_link",
                                                    placeholder: "URL where the offer can be redeemed",
                                                    type: "url"
                                                }
                                            }
                                        ]
                                    },
                                    {
                                        title: "Terms and Conditions",
                                        description: "",
                                        inputs: [
                                            {
                                                elem: "input",
                                                colWidth: 12,
                                                props: {
                                                    name: "offer_terms",
                                                    placeholder: "Any terms and conditions for the offer",
                                                    type: "text"
                                                }
                                            }
                                        ]
                                    }
                                );
                            }
        
                            if (topicType !== "OFFER") {
                                options.push({
                                    title: "Call To Action",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "select",
                                            colWidth: 12,
                                            props: {
                                                name: "call_to_action"
                                            },
                                            options: [
                                                {
                                                    text: "No button",
                                                    val: "",
                                                    selected: true
                                                },
                                                {
                                                    text: "BOOK"
                                                },
                                                {
                                                    text: "ORDER"
                                                },
                                                {
                                                    text: "SHOP"
                                                },
                                                {
                                                    text: "LEARN MORE",
                                                    val: "LEARN_MORE"
                                                },
                                                {
                                                    text: "SIGN UP",
                                                    val: "SIGN_UP"
                                                },
                                                {
                                                    text: "CALL"
                                                }
                                            ]
                                        }
                                    ]
                                });
                                const noLinkNeeded =
                                    !this.options[acc.id] ||
                                    (!this.options[acc.id].call_to_action || this.options[acc.id].call_to_action === "CALL");
                                !noLinkNeeded &&
                                options.push({
                                    title: "Call To Action URL",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 12,
                                            props: {
                                                name: "call_to_action_url",
                                                placeholder: "https://yourwebsite.com/page",
                                                type: "url"
                                            }
                                        }
                                    ]
                                });
                            } else {
                                this.$nextTick(() => {
                                    if (this.options[acc.id].call_to_action) {
                                        this.options[acc.id].call_to_action = ""; // reset cta
                                    }
                                });
                            }
                        }else if (acc.type === 'pinterest.profile'){
                            const boards = [];
                            if(acc.extra_data && acc.extra_data.boards && acc.extra_data.boards.length){
                                boards.push(...acc.extra_data.boards);
                            }
                            options.push(
                                {
                                    title: "Title",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 12,
                                            props: {
                                                name: "pin_title",
                                                placeholder: "Add title for your pin",
                                                type: "text"
                                            }
                                        }
                                    ]
                                },
                                {
                                    title: "Board",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "select",
                                            colWidth: 12,
                                            props: {
                                                name: "board_name",
                                                title: "board",
                                                required: true
                                            },
                                            options: boards.map(b => {
                                                return {
                                                    text: b.name,
                                                    val: b.name,
                                                    selected: this.options[acc.id] && this.options[acc.id]['board_name'] && b.name === this.options[acc.id]['board_name']
                                                };
                                            })
                                        }
                                    ]
                                },
                                {
                                    title: "Link",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 12,
                                            props: {
                                                name: "pin_link",
                                                placeholder: "Add a link",
                                                type: "text"
                                            }
                                        }
                                    ]
                                },
                                {
                                    title: "Note to self",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 12,
                                            props: {
                                                name: "pin_note",
                                                placeholder: "Add a private note",
                                                type: "text"
                                            }
                                        }
                                    ]
                                },
        
                            )
                            if(this.attachment && (this.attachment.mime?.includes("video") || this.attachment.type?.includes("video"))){
                                options.push(
                                    {
                                        title: "Attach Thumbnail",
                                        description: "",
                                        inputs: [
                                            {
                                                elem: "input",
                                                colWidth: 6,
                                                props: {
                                                    name: "thumbnail",
                                                    type: "file",
                                                    required: acc.type === 'pinterest.profile'
                                                },
                                            }
                                        ]
                                    }
                                )
                            }
                        } else if (acc.type === "google.youtube") {
                            options.push({
                                title: "Post as Short",
                                description: "",
                                inputs: [
                                    {
                                        elem: "input",
                                        colWidth: 6,
                                        props: {
                                            name: "post_as_short",
                                            type: "checkbox"
                                        }
                                    }
                                ]
                            });
                            options.push({
                                title: "Made for kids",
                                description: "",
                                inputs: [
                                    {
                                        elem: "input",
                                        colWidth: 6,
                                        props: {
                                            name: "made_for_kids",
                                            type: "checkbox"
                                        }
                                    }
                                ]
                            });
                            options.push({
                                title: "Title",
                                description: "",
                                inputs: [
                                    {
                                        elem: "input",
                                        colWidth: 12,
                                        props: {
                                            name: "video_title",
                                            placeholder: "Add title for your video",
                                            type: "text"
                                        }
                                    }
                                ]
                            });
                            options.push({
                                title: "Visibility",
                                description: "",
                                inputs: [
                                    {
                                        elem: "select",
                                        colWidth: 12,
                                        props: {
                                            name: "privacy_status",
                                            title: "Privacy Status"
                                        },
                                        options: [
                                            {
                                                text: "Public",
                                                val: "public",
                                                selected: true
                                            },
                                            {
                                                text: "Private",
                                                val: "private"
                                            },
                                            {
                                                text: "Unlisted",
                                                val: "unlisted"
                                            }
                                        ]
                                    }
                                ]
                            });
                            options.push({
                                title: "Tags",
                                description: "",
                                inputs: [
                                    {
                                        elem: "input",
                                        colWidth: 12,
                                        props: {
                                            name: "video_tags",
                                            placeholder: "Add tags,separated by comma",
                                            type: "text"
                                        }
                                    }
                                ]
                            });
                            const categories = [];
                            if (acc.extra_data && acc.extra_data.categories && acc.extra_data.categories.length) {
                                categories.push(...acc.extra_data.categories);
                            } else {
                                categories.push(...[
                                    {"id": "2", "name": "Autos & Vehicles"},
                                    {"id": "1", "name": " Film & Animation"},
                                    {"id": "10", "name": "Music"},
                                    {"id": "15", "name": "Pets & Animals"},
                                    {"id": "17", "name": "Sports"},
                                    {"id": "18", "name": "Short Movies"},
                                    {"id": "19", "name": "Travel & Events"},
                                    {"id": "20", "name": "Gaming"},
                                    {"id": "21", "name": "Videoblogging"},
                                    {"id": "22", "name": "People & Blogs"},
                                    {"id": "23", "name": "Comedy"},
                                    {"id": "24", "name": "Entertainment"},
                                    {"id": "25", "name": "News & Politics"},
                                    {"id": "26", "name": "Howto & Style"},
                                    {"id": "27", "name": "Education"},
                                    {"id": "28", "name": "Science & Technology"},
                                ]);
                            }
                            options.push({
                                title: "Category",
                                description: "",
                                inputs: [
                                    {
                                        elem: "select",
                                        colWidth: 12,
                                        props: {
                                            name: "category_id",
                                            title: "Video category"
                                        },
                                        options: categories.map(c => {
                                            c.id = String(c.id);
                                            return {
                                                text: c.name,
                                                val: c.id,
                                                selected: c.id === "22"
                                            };
                                        })
                                    }
                                ]
                            });
        
                            const post_as_short = this.options[acc.id] && this.options[acc.id].post_as_short;
                             if (!post_as_short && this.attachment && (this.attachment.mime?.includes("video") || this.attachment.type?.includes("video"))) {
                                options.push(
                                    {
                                        title: "Attach Thumbnail",
                                        description: "Attach custom thumbnail to the video",
                                        inputs: [
                                            {
                                                elem: "input",
                                                colWidth: 6,
                                                props: {
                                                    name: "thumbnail",
                                                    type: "file",
                                                },
                                                allowedAttachments: ['jpg', 'png', 'jpeg']
                                            }
                                        ]
                                    }
                                );
                            }
                            
                        } else if (["reddit.profile", "reddit.subreddit"].includes(acc.type)) {
                            options.push({
                                title: "Title",
                                description: "",
                                inputs: [
                                    {
                                        elem: "input",
                                        colWidth: 12,
                                        props: {
                                            name: "title",
                                            placeholder: "My awesome post",
                                            type: "text",
                                        }
                                    }
                                ]
                            });
                             options.push(
                                {
                                    title: "Comment",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "textarea",
                                            colWidth: 12,
                                            props: {
                                                name: "comment",
                                                placeholder: "Add first comment to your post "
                                            }
                                        }
                                    ]
                                },
                            );
                            options.push({
                                title: "Mark as Spoiler",
                                description: "",
                                inputs: [
                                    {
                                        elem: "input",
                                        colWidth: 12,
                                        props: {
                                            name: "is_spoiler",
                                            type: "checkbox"
                                        }
                                    }
                                ]
                            });
                            options.push({
                                title: "Mark as NSFW",
                                description: "",
                                inputs: [
                                    {
                                        elem: "input",
                                        colWidth: 12,
                                        props: {
                                            name: "is_nsfw",
                                            type: "checkbox"
                                        }
                                    }
                                ]
                            });
                            if(acc.extra_data && acc.extra_data.flairs && acc.extra_data.flairs.length){
                                options.push({
                                    title: "Flair",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "select",
                                            colWidth: 12,
                                            props: {
                                                name: "flair_id",
                                                title: "Flair"
                                            },
                                            options: acc.extra_data.flairs.map(f => {
                                                return {
                                                    text: f.text,
                                                    val: f.id,
                                                };
                                            })
                                        }
                                    ]
                                });
                            }
                        }else if(acc.type.includes('tiktok')){
                            if(acc.extra_data && acc.extra_data.creator_info && acc.extra_data.creator_info.privacy_level_options){
                                let privacyOptions = acc.extra_data.creator_info.privacy_level_options.map(option =>{
                                    return {
                                        text: option.split('_').join(' '),
                                        val: option
                                    }
                                })
        
                                options.push({
                                    title: "Privacy",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "select",
                                            colWidth: 12,
                                            props: {
                                                name: "privacy_status",
                                                title: "Privacy Status",
                                                required: true
                                            },
                                            options: privacyOptions
                                        }
                                    ]
                                },)
                            }
                            if (this.attachment && (this.attachment.mime?.includes("image") || this.attachment.type?.includes("image"))) {
                                options.push({
                                    title: "Title",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 12,
                                            props: {
                                                name: "title",
                                                placeholder: "Add title for your post",
                                                type: "text"
                                            }
                                        }
                                    ]
                                }, {
                                    title: "Auto add music",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 6,
                                            props: {
                                                name: "auto_add_music",
                                                type: "checkbox",
                                            }
                                        }
                                    ],
                                });
                            } else if(this.attachment && (this.attachment.mime?.includes("video") || this.attachment.type?.includes("video"))){
                                options.push(
                                    {
                                        title: "Attach Thumbnail",
                                        description: "",
                                        inputs: [
                                            {
                                                elem: "input",
                                                colWidth: this.selectedAccounts.length > 1 ? 12 : 6,
                                                props: {
                                                    name: "thumbnail",
                                                    type: "file",
                                                },
                                                allowedAttachments: ['jpg', 'png', 'jpeg']
                                            }
                                        ]
                                    }
                                );
                            }
                            options.push(
                                {
                                    title: "Allow Comment",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 6,
                                            props: {
                                                name: "allow_comment",
                                                type: "checkbox",
                                                disabled: acc.extra_data.creator_info && acc.extra_data.creator_info.comment_disabled
                                            }
                                        }
                                    ],
                                },
                                {
                                    title: "Allow Duet",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 6,
                                            props: {
                                                name: "allow_duet",
                                                type: "checkbox",
                                                disabled: acc.extra_data.creator_info && acc.extra_data.creator_info.duet_disabled
                                            }
                                        }
                                    ],
                                },
                                {
                                    title: "Allow Stitch",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 6,
                                            props: {
                                                name: "allow_stitch",
                                                type: "checkbox",
                                                disabled: acc.extra_data.creator_info && acc.extra_data.creator_info.stitch_disabled
                                            },
                                        }
                                    ],
                                },
                                {
                                    title: "Disclose post content/ad",
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 6,
                                            props: {
                                                name: "disclose_content",
                                                type: "checkbox",
                                            }
                                        }
                                    ],
                                },
                            )
        
                            const discloseContent = this.options[acc.id] && this.options[acc.id].disclose_content;
                            if(discloseContent){
                                options.push(
                                    {
                                        title: "Your brand",
                                        description: "You are promoting yourself or your own business.",
                                        inputs: [
                                            {
                                                elem: "input",
                                                colWidth: 6,
                                                props: {
                                                    name: "own_brand",
                                                    type: "checkbox",
                                                }
                                            }
                                        ],
                                    },
                                    {
                                        title: "Branded content",
                                        description: "You are in a paid partnership with a brand.",
                                        inputs: [
                                            {
                                                elem: "input",
                                                colWidth: 6,
                                                props: {
                                                    name: "branded_content",
                                                    type: "checkbox",
                                                }
                                            }
                                        ],
                                    },
                                )
                            }
                        } else if(acc.type === "threads.profile"){
        
                            options.push({
                                title: "Who can reply",
                                description: "",
                                inputs: [
                                    {
                                        elem: "select",
                                        colWidth: 12,
                                        props: {
                                            name: "reply_control",
                                        },
                                        options: [
                                            {
                                                text: "Everyone",
                                                val: "everyone",
                                                selected: true
                                            },
                                            {
                                                text: "Accounts you follow",
                                                val: "accounts_you_follow"
                                            },
                                            {
                                                text: "Mentioned accounts",
                                                val: "mentioned_only"
                                            }
                                        ]
                                    }
                                ]
                            });
                            options.push({
                                title: "Threaded Replies",
                                description: "",
                                type: "threaded_replies"
                            });
        
        
                        } else if (acc.type === 'bluesky.profile'){
                            if(this.attachment && (this.attachment.mime?.includes("video") || this.attachment.type?.includes("video"))){
                                options.push({
                                    title: `Alt text for media: ${this.attachment.name}`,
                                    description: "",
                                    inputs: [
                                        {
                                            elem: "input",
                                            colWidth: 12,
                                            props: {
                                                name: 'media_alt_text[0]',
                                                placeholder: "Alternative text for the media",
                                                type: "text",
                                            }
                                        }
                                    ]
                                });
                            }
                            options.push({
                                title: "Threaded Replies",
                                description: "",
                                type: "threaded_replies"
                            });
                        }
        
                        if (this.attachments.length && this.options[acc.id] && !this.options[acc.id].post_as_story && ["facebook.page", "linkedin.", "twitter.profile", "mastodon.profile", "pinterest.profile", "bluesky.profile","instagram.api"].find(_t => acc.type.includes(_t))) {
                            // add attachments with their indexes preserved
                            const imageAttachments = this.attachments.filter(attachment => {
                                return attachment.type && attachment.type.includes("image") || attachment.mime && attachment.mime.includes("image");
                            });
                            if (imageAttachments.length) {
                                imageAttachments.forEach((attachment, index) => {
                                    options.push({
                                        title: `Alt text for media: ${attachment.name}`,
                                        description: "",
                                        inputs: [
                                            {
                                                elem: "input",
                                                colWidth: 12,
                                                props: {
                                                    name: 'media_alt_text[' + index + ']',
                                                    placeholder: "Alternative text for the image",
                                                    type: "text",
                                                }
                                            }
                                        ]
                                    });
                                })
        
                            }
                        }
        
                        if (options.length)
                            allOptions[acc.id] = {
                                account: acc,
                                options
                            };
                    });
                }

            return allOptions;
        },
        maximumAttachments() {
            let max = 81;
            this.selectedAccounts.forEach(a => {
                if (a.max_attachments < max) {
                    max = a.max_attachments;
                }
            });
            return max;
        },
        recommendedAspectRatios() {
            let ratios = [];
            let allRatios = null;
            if (!this.attachment) return ratios;

            if (this.attachment.mime?.includes("video") || this.attachment.type?.includes("video")) {
                allRatios = this.attachmentRecommendedAspectRatios.video;
            } else {
                allRatios = this.attachmentRecommendedAspectRatios.image;
            }
            this.accounts.forEach(account => {
                if (this.selectedAccountsIds.indexOf(account.id) > -1) {
                    const accRatios = allRatios[account.type] ? allRatios[account.type] : [];
                    if (ratios.length === 0) {
                        ratios.push(...accRatios);
                    } else {
                        ratios.forEach((r, i) => {
                            if (!accRatios.includes(r)) {
                                ratios.splice(i, 1);
                            }
                        });
                    }
                    //story aspect ratio
                    if ((account.type === 'instagram.api' || account.type === 'facebook.page') && this.options[account.id] && this.options[account.id].post_as_story) {
                        ratios = ['9:16']
                    }
                }
            });
            return ratios;
        },
        attachment() {
            return this.attachments && this.attachments[0];
        },
        showRecommendedAspectRatios() {
            if (this.selectedAccounts.length === 0) return false;
            if (!this.attachments || !this.attachments.length) return false;
            let ok = true;
            this.attachments.forEach(attachment => {
                const metaData = attachment._metaData;

                if (!metaData || !Object.keys(metaData).length) {
                    return;
                }

                function gcd(a, b) {
                    return b === 0 ? a : gcd(b, a % b);
                }

                // get gcd
                const r = gcd(metaData.width, metaData.height);

                // calculate aspect ratio
                const ratio = metaData.width / r + ":" + metaData.height / r;
                if (!this.recommendedAspectRatios.includes(ratio)) {
                    ok = false;
                }
            });
            return !ok;
        },
        suggestedHashtags() {
            return this.allSuggestedHashtags.filter(s => {
                try {
                    if (s) {
                        // make sure it is not in content already
                        return !this.content
                            .toLowerCase()
                            .trim()
                            .includes("#" + s.toLowerCase().trim());
                    }
                } catch (e) {
                }
                return false;
            });
        },
        requiresMedia() {
            let selected = [];
            this.accounts.forEach(account => {
                if (this.selectedAccountsIds.indexOf(account.id) > -1) {
                    selected.push(account);
                }
            });
            return selected.some(a => a.post_media_required);
        },
        videoLimit() {
            let dimensions = {
                min: [null, null],
                max: [null, null]
            };
            let duration = {
                min: null,
                max: null
            };
            let dimensionsAccount = null;
            this.accounts.forEach(account => {
                if (this.selectedAccountsIds.indexOf(account.id) > -1) {
                    const min = account.video_dimensions.min,
                        max = account.video_dimensions.max;

                    if (!dimensions.min[0] || (min[0] && min[0] > dimensions.min[0])) {
                        dimensions.min[0] = min[0];
                        dimensionsAccount = account;
                    }
                    if (!dimensions.min[1] || (min[1] && min[1] > dimensions.min[1])) {
                        dimensions.min[1] = min[1];
                        dimensionsAccount = account;
                    }

                    if (!dimensions.max[0] || (max[0] && max[0] < dimensions.max[0])) {
                        dimensions.max[0] = max[0];
                         dimensionsAccount = account;
                    }
                    if (!dimensions.max[1] || (max[1] && max[1] < dimensions.max[1])) {
                        dimensions.max[1] = max[1];
                         dimensionsAccount = account;
                    }

                    if (!duration.min || account.video_duration.min > duration.min) {
                        duration.min = account.video_duration.min;
                         dimensionsAccount = account;
                    }

                    let accountDuration = account.video_duration.max;
                    // if the post for this account is reel, then the max duration is 15 min
                    if (account.type === "instagram.api") {
                        accountDuration = 15 * 60; // for reels
                    }
                    if (!duration.max || accountDuration < duration.max) {
                        duration.max = accountDuration;
                         dimensionsAccount = account;
                    }
                }
            });
            return {
                dimensions,
                duration,
                account: dimensionsAccount
            };
        },
        allowedAttachmentTypes() {
            const allowedAttachmentTypes = {};
            const selectedAccounts = this.selectedAccounts;
            selectedAccounts.forEach(acc => {
                const allowedExts = acc.attachment_types;
                allowedExts.forEach(ext => {
                    allowedAttachmentTypes[ext] = allowedAttachmentTypes[ext] || 0;
                    ++allowedAttachmentTypes[ext];
                });
            });

            let commonTypes = [];
            for (let ext in allowedAttachmentTypes) {
                if (allowedAttachmentTypes.hasOwnProperty(ext)) {
                    if (allowedAttachmentTypes[ext] === selectedAccounts.length)
                        // present in all accounts
                        commonTypes.push(ext);
                }
            }

            return commonTypes;
        },
        maxPostLength() {
            let maxlength = null;
            this.selectedAccounts.forEach(acc => {
                if (this.focusedAccount && this.focusedAccount.id !== acc.id) {
                    return;
                }
                let acc_maxlength = Number(acc.post_maxlength);
                if (maxlength === null || maxlength > acc_maxlength) maxlength = acc_maxlength;
            });

            return maxlength;
        },
        cannotPostReason() {
            if(this.mode === 'queue_edit') {
                return null;
            }
            if (!this.selectedAccounts.length) {
                // ask to add account
                return "Select at least one account to publish to.";
            } else if (this.selectedAccounts.some(account => account.type === 'instagram.direct')) {
                return 'Post scheduling/publishing not allowed for Instagram direct accounts.'
            } else if (this.requiresMedia && !this.attachment) {
                // ask to add content or attachment for posting on instagram
                return "Please attach media.";
            } else if (!this.postLength && !this.attachment  && !this.selectedAccounts.some(account => account.type === 'reddit.profile') ) {
                return "Please add content or media.";
            } else if (this.postLength > this.maxPostLength) {
                return "The content length cannot be greater than " + this.maxPostLength + " characters.";
            } else if (this.hashtags.length > 30) {
                return "Maximum 30 hashtags are allowed.";
            } else if (this.attachments.length > this.maximumAttachments) {
                return (
                    "Maximum " +
                    this.maximumAttachments +
                    " attachment(s) are allowed for the account(s) that you have selected."
                );
            } else if(this.$refs.mediaSelectBtn.uploading){
                return 'file upload is in process';
            } else if (this.requiresMedia && this.allowedAttachmentTypes.length && this.attachments.some(attachment => this.allowedAttachmentTypes.indexOf(attachment.type.toLowerCase()) === -1)){
                const currentSelected = [... new Set(this.attachments.map(a=>a.type)) ].map(ext => {
                    // ensure lowercase
                    return ext.toLowerCase();
                });
                if(this.selectedAccounts.length === 1){
                    return 'Invalid attachment(s) for ' + this.selectedAccounts[0]._type + '. Allowed types are: ' + this.allowedAttachmentTypes.join(', ') + '. You selected: ' + currentSelected.join(', ');
                } else {
                    return 'Invalid attachment(s) for selected accounts. Allowed types are: ' + this.allowedAttachmentTypes.join(', ') + '. You selected: ' + currentSelected.join(', ');
                }
            } else if (this.invalidOptionReason) {
                return this.invalidOptionReason;
            }

            return null;
        },
        noOptions() {
            return false;
        },
        noCookies() {
            return this.mode === "queue_add" || this.mode === "queue_edit" || this.mode === "edit";
        }
    },
    watch: {
        content(v) {
            this.loadSuggestedHashtags();

            // store text in case user mistakenly closes page
            if (v) {
                try {
                    localStorage.setItem("PostEditor__content", v);
                } catch (e) {
                }
            } else {
                try {
                    localStorage.removeItem("PostEditor__content");
                } catch (e) {
                }
            }

            // custom content update if needed
            if (this.shouldCustomizeContent) {
                this.customizedContent[this.customizeContentFor] = v;
            }
        },
        customizeContent(v) {
            if (!v) {
                this.customizeContentFor = null;
                // restore content if any
                if (this.customizedContent.default) {
                    this.content = this.customizedContent.default;
                }
                if (this.customizeContentForAccount) {
                    this.previewAccountTypes.forEach((type) => {
                        if (this.customizeContentForAccount.type.startsWith(type) && this.preview) {
                            this.preview.network = type;  // Set the matching type as the network
                            this.preview.account[type].id = v;
                        }
                    });
                }
                // reset all custom content
                this.customizedContent = {};
            } else {
                // select first account
                this.customizeContentFor = this.selectedAccounts[0].id;

                // copy content to all accounts
                this.customizedContent = {
                    default: this.content
                };
                this.selectedAccounts.forEach(acc => {
                    if (!this.customizedContent[acc.id]) {
                        this.customizedContent[acc.id] = "" + this.content;
                    }
                });
            }
        },
        customizeContentFor(v) {
            if (v) {
                if (!this.customizedContent[v]) {
                    this.customizedContent[v] = this.customizedContent.default;
                }
                if (this.customizeContentForAccount) {

                    this.previewAccountTypes.forEach((type) => {

                        if (this.customizeContentForAccount.type.startsWith(type) && this.preview) {
                            this.preview.network = type;  // Set the matching type as the network
                            this.preview.account[type].id = v;
                            this.selectedPostOptionAccountType = type; //set the network for account options
                        }
                    });
                }
                this.content = this.customizedContent[v];
            }
        },
        selectedAccountsIds() {
            if (!this.noCookies)
                Cookies.set("editor_selected_accounts", this.selectedAccountsIds.join(","), {
                    sameSite: "None",
                    secure: true
                });

            if (this.attachments.length && !this.renderedInExtension && this.mode === "add") {
                this.attachments.forEach(a => {
                    if (a.url) {
                        // skip already uploaded files
                        return;
                    }
                    this.validateAttachment(a.type, false);
                });
            }

            // set preview network if not set correct already
            if (this.previewAccountTypes.length) {
                if (!this.preview.network || !this.previewAccountTypes.includes(this.preview.network)) {
                    this.preview.network = this.previewAccountTypes[0];
                }
                this.preview.account[this.preview.network].id = this.selectedAccountsIds[0];
                
                
            } else {
                // make sure preview is disabled
                if (this.preview.show) this.preview.show = false;
                this.preview.network = null; //reset network
                this.selectedPostOptionAccountType = null; // reset account
            }

            if(this.postOptionAccountTypes.length){
                if(this.focusedAccount){
                    this.selectedPostOptionAccountType = this.focusedAccount.type.includes("google.") ? this.focusedAccount.type : this.focusedAccount.type.split(".")[0];
                }else {
                    this.selectedPostOptionAccountType = this.postOptionAccountTypes[0];    
                }
            }else {
                this.selectedPostOptionAccountType = null; // reset account
            }

            if (this.selectedAccountsIds.length <= 1) {
                this.customizeContent = false;
            }
        },
        'preview.network': function(newNetwork){
            if(newNetwork && this.previewAccountIdsByNetwork[newNetwork].length){
                this.preview.account[this.preview.network].id = this.previewAccountIdsByNetwork[newNetwork][0]
            }
        },
        selectedTeam(id) {
            if (!this.noCookies) {
                if (!id || id === "all") Cookies.remove("editor_selected_team");
                else Cookies.set("editor_selected_team", id, {sameSite: "None", secure: true});
            }
            if (!this.initialized) return;
            this.$nextTick(() => {
                this.unselectAllAccounts();
                this.selectDefaultAccount();
            });
        },
        // Watch for schedule mode changes to reinitialize calendar
        scheduleMode(newMode) {
            if (newMode === 'single') {
                this.$nextTick(() => {
                    this.initDateTimePicker();
                });
            }
        }
    },
    methods: {
        // Initialize or reinitialize the datetime picker
        initDateTimePicker() {
            if (this.$refs.publish_at_datetime) {
                // Destroy existing datetimepicker if it exists
                try {
                    $(this.$refs.publish_at_datetime).datetimepicker('destroy');
                } catch (e) {
                    // Ignore errors if datetimepicker doesn't exist
                }

                // Initialize the datetimepicker
                $(this.$refs.publish_at_datetime)
                    .on("change.datetimepicker", e => {
                        this.publishAt = e.date.format("YYYY-MM-DD HH:mm:00");
                    })
                    .datetimepicker({
                        inline: true,
                        sideBySide: true,
                        minDate: new Date(),
                        timeZone: appConfig.timezone,
                        icons: {
                            previous: 'ph ph-caret-left ph-md',
                            next: 'ph ph-caret-right ph-md',
                            up: 'ph ph-caret-up ph-bold',
                            down: 'ph ph-caret-down ph-bold'
                        }
                    });

                $(this.$refs.publish_at_datetime).find('.datepicker-days')
                    .off('click', '.day.disabled') // Remove any previously bound event
                    .on('click', '.day.disabled', function() {
                        alertify.error('Sorry, you cannot select date in the past!');
                    });
            }
        },
        // Open date picker modal for multiple dates
        openDatePicker(index) {
            this.editingDateIndex = index;
            this.showDatePickerModal = true;

            // Set initial date if editing existing entry
            if (index >= 0 && this.publishAtArray[index]) {
                this.selectedDateTime = this.publishAtArray[index];
            } else {
                this.selectedDateTime = null;
            }

            // Initialize the date picker in the modal after DOM update
            this.$nextTick(() => {
                // Add a small delay to ensure modal is fully rendered
                setTimeout(() => {
                    this.initMultipleDatePicker();
                }, 100);
            });
        },
        // Close date picker modal
        closeDatePicker() {
            this.showDatePickerModal = false;
            this.editingDateIndex = -1;
            this.selectedDateTime = null;

            // Destroy the date picker
            if (this.$refs.multiple_date_picker) {
                try {
                    $(this.$refs.multiple_date_picker).datetimepicker('destroy');
                } catch (e) {
                    // Ignore errors
                }
            }
        },
        // Initialize date picker for multiple dates modal
        initMultipleDatePicker() {
            if (this.$refs.multiple_date_picker) {
                // Destroy existing datetimepicker if it exists
                try {
                    $(this.$refs.multiple_date_picker).datetimepicker('destroy');
                } catch (e) {
                    // Ignore errors if datetimepicker doesn't exist
                }

                // Set initial date if editing existing entry
                let initialDate = null;
                if (this.selectedDateTime) {
                    initialDate = this.$momentUTC(this.selectedDateTime, "YYYY-MM-DD HH:mm:00");
                } else {
                    initialDate = this.$momentUTC().add(1, 'hour'); // Default to 1 hour from now
                }

                // Initialize the datetimepicker
                $(this.$refs.multiple_date_picker)
                    .on("change.datetimepicker", e => {
                        this.selectedDateTime = e.date.format("YYYY-MM-DD HH:mm:00");
                    })
                     .datetimepicker({
                        inline: true,
                        sideBySide: true,
                        minDate: new Date(),
                        timeZone: appConfig.timezone,
                        icons: {
                            previous: 'ph ph-caret-left ph-md',
                            next: 'ph ph-caret-right ph-md',
                            up: 'ph ph-caret-up ph-bold',
                            down: 'ph ph-caret-down ph-bold'
                        }
                    });

                // Set the selectedDateTime to the initial date
                this.selectedDateTime = initialDate.format("YYYY-MM-DD HH:mm:00");

                $(this.$refs.multiple_date_picker).find('.datepicker-days')
                    .off('click', '.day.disabled')
                    .on('click', '.day.disabled', function() {
                        alertify.error('Sorry, you cannot select date in the past!');
                    });
            }
        },
        // Save the selected date/time
        saveDateSelection() {
            if (!this.selectedDateTime) {
                alertify.error('Please select a date and time');
                return;
            }

            // Ensure publishAt is an array
            if (!Array.isArray(this.publishAt)) {
                this.publishAt = this.publishAt ? [this.publishAt] : [];
            }

            if (this.editingDateIndex === -1) {
                // Adding new date
                this.publishAt.push(this.selectedDateTime);
            } else {
                // Editing existing date
                this.publishAt[this.editingDateIndex] = this.selectedDateTime;
            }

            this.closeDatePicker();
        },
        toggleOptions() {
            this.show.postOptions = !this.show.postOptions;
        },
        insertEmoji(emoji) {
            this.$refs.text_input.addText(emoji.native);
            this.show.emojiPicker = false;
        },
        insertThreadEmoji(emoji,id,index){
            this.$refs['threadInput'+id][index].addText(emoji.native);
            this.show.threadEmojiPicker = null;
        },
        truncate: truncate,
        getIconForAccount,
        alert(msg) {
            window.alert(msg);
        },
        setMode(mode) {
            this.mode = mode;
        },
        showAccountsForTeam(teamId) {
            if (teamId && teamId !== "all") this.selectedTeam = Number(teamId);
            else this.selectedTeam = null;
        },
        onSelectAll() {
            if (this.availableAccounts.length === this.selectedAccountsIds.length) {
                this.selectedAccountsIds = [];
                this.options = {};
            } else {
                this.selectedAccountsIds = this.availableAccounts.map(a => a.id);
            }
        },
        initialize(uploadFile = false) {
            if (this.initialized) return;
            if (!this.noCookies) {
                let checkedAccounts = (Cookies.get("editor_selected_accounts") || "").split(",");
                checkedAccounts.forEach(id => {
                    const accId = Number(id);
                    if (this.availableAccounts.map(a => a.id).includes(accId)) {
                        this.selectedAccountsIds.push(accId);
                        this.options[accId] = {} //init options
                    }
                });
            }

            this.$nextTick(() => {

                // set content if stored in localstorage
                try {
                    const storedContent = localStorage.getItem("PostEditor__content");
                    if (storedContent) {
                        this.content = storedContent;
                    }
                } catch (e) {
                }

                // Initialize the datetime picker
                this.initDateTimePicker();
                this.selectDefaultAccount();

                // show modal if in popup (e.g. share popup)
                if (this.renderedInPopup) {
                    this.show.fullScreen = true;
                    $("#create_post_modal").modal({
                        backdrop: "static",
                        keyboard: false,
                        show: true
                    });
                }

                this.initialized = true;
                this.$nextTick(() => {
                    this.focusTextArea();
                    if (uploadFile) {
                        setTimeout(() => {
                            this.$refs.mediaSelectBtn.selectFile();
                        }, 200);
                    }
                });
            });
        },
        focusTextArea() {
            this.$refs.text_input.focus();
        },
        setScheduledPostsInstance(c) {
            scheduledPosts = c;
        },
        validateAttachment(type, showError = true, extraData = {}, allowedAttachments=[], elementRef) {
            if (!this.selectedAccountsIds.length) {
                if (this.mode === "queue_add" || this.mode == 'queue_edit') {
                    return true;
                }
                const msg = "Please select an account first";
                if (showError) alertify.error(msg);
                else alertify.warning(msg);
                this.$refs.mediaSelectBtn.removeAll(); // remove media if any
                return false;
            }
            let MIME_MAP = {
                "image/jpeg": "jpg",
                "image/jpg": "jpg",
                "image/png": "png",
                "image/x-png": "png",
                "image/gif": "gif",
                "image/bmp": "bmp",
                "image/x-bmp": "bmp",
                "image/x-bitmap": "bmp",
                "image/tiff": "tiff",
                "image/webp": "webp",
                "video/mp4": "mp4",
                "video/quicktime": "mov",
                "video/x-msvideo": "avi",
                "application/pdf": "pdf",
                "application/msword": "doc",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "docx",
                "application/vnd.ms-powerpoint": "ppt",
                "application/vnd.openxmlformats-officedocument.presentationml.presentation": "pptx",
            };
            
            let MIME_MAP_REVERSED = {};
            Object.keys(MIME_MAP).forEach(k => {
                const v = MIME_MAP[k];
                MIME_MAP_REVERSED[v] = k;
            });

            if(allowedAttachments.length){
                if(allowedAttachments.indexOf(MIME_MAP[type]) === -1){
                    if (showError){
                        alertify.error(
                            this.$lang.get("publish.error_unsupported_image_type", {
                                types: allowedAttachments.join(", ")
                            })
                        );

                    } else {
                        alertify.warning("Please re-select attachment(s)");
                    }
                    if(this.$refs[elementRef]){
                        let comp = this.$refs[elementRef];
                        if(Array.isArray(comp)) { // ref in loops are always arrays
                            comp.forEach(c => c.removeAll());
                        } else {
                            comp.removeAll();
                        }
                    }
                    return false;
                }else {
                    return true
                }
            }
            // convert extension to mime if needed
            if (MIME_MAP_REVERSED[type]) {
                type = MIME_MAP_REVERSED[type];
            }
            if (!MIME_MAP[type] || this.allowedAttachmentTypes.indexOf(MIME_MAP[type]) === -1) {
                if (showError)
                    alertify.error(
                        this.$lang.get("publish.error_unsupported_image_type", {
                            types: this.allowedAttachmentTypes.join(", ")
                        })
                    );
                else alertify.warning("Please re-select attachment(s)");
                this.$refs.mediaSelectBtn.removeAll(); // remove media if any
                return false;
            }

            // check dimensions for video
            if (type.includes("video")) {
                const showMsg = showError ? alertify.error : alertify.warning;

                const limit = this.videoLimit;

                // extraData has video data {width, height, duration}
                if (limit.dimensions.min[0] && extraData.width && limit.dimensions.min[0] > extraData.width) {
                    // width too less
                    showMsg("Video width should not be less than " + limit.dimensions.min[0] + "px for account " + limit.account.name + " (" + limit.account._type + ")");
                    return false;
                } else if (limit.dimensions.min[1] && extraData.height && limit.dimensions.min[1] > extraData.height) {
                    // height too less
                    showMsg("Video height should not be less than " + limit.dimensions.min[1] + "px for account " + limit.account.name + " (" + limit.account._type + ")");
                    return false;
                } else if (limit.dimensions.max[0] && limit.dimensions.max[0] < extraData.width) {
                    // width too large
                    showMsg("Video width should not be greater than " + limit.dimensions.max[0] + "px for account " + limit.account.name + " (" + limit.account._type + ")");
                    return false;
                } else if (limit.dimensions.max[1] && limit.dimensions.max[1] < extraData.height) {
                    // height too large
                    showMsg("Video height should not be greater than " + limit.dimensions.max[1] + "px for account " + limit.account.name + " (" + limit.account._type + ")");
                    return false;
                }

                // check duration
                if (extraData.duration > limit.duration.max + 1 || extraData.duration < limit.duration.min) {
                    showMsg(
                        "Video duration must be between " +
                        limit.duration.min +
                        " and " +
                        limit.duration.max +
                        " seconds for account " + limit.account.name + " (" + limit.account._type + ")"
                    );
                    return false;
                }
            }
            return true;
        },
        toggleAccountSelection(accId, type) {
            if (!this.initialized) return;

            if (!this.selectedAccountsIds.includes(accId)) {
                // check for twitter
                if (type === "twitter.profile" && this.selectedAccounts.some(acc => acc.type === "twitter.profile")) {
                    // a twitter account is already selected and this is also a twitter account
                    alertify.warning(this.$lang.get("publish.one_twitter_at_a_time"));
                    return false;
                }

                // add account
                this.selectedAccountsIds.push(accId);
                this.options[accId] = {} //init options
            } else if (this.selectedAccountsIds.includes(accId)) {
                // remove account
                this.selectedAccountsIds.splice(this.selectedAccountsIds.indexOf(accId), 1);

                // remove options too
                const opts = {...this.options};
                if (opts[accId]) {
                    delete opts[accId];
                }
                this.options = opts;
            }
        },
        triggerCommands() {
            this.$refs.text_input.focus();
            this.$nextTick(() => {
                this.$refs.text_input.openAutocomplete("/");
            })
        },
        onDrag(type, e) {
            if (type === "start") {
                $(".new_post_wrapper").css("opacity", 0.6);
            } else if (type === "end" || type === "drop") {
                $(".new_post_wrapper").css("opacity", 1);
            }
            if (type === "drop") {
                const newFiles = Array.from(e.dataTransfer.files);
                this.$refs.mediaSelectBtn.addFiles(newFiles);
            }
        },
        onPaste(e) {
            const clipboardItems = e.clipboardData.items;
            for (let i = 0; i < clipboardItems.length; i++) {
                const item = clipboardItems[i];
                if (item.type.indexOf("image") !== -1 || item.type.indexOf("video") !== -1) {
                    const file = item.getAsFile();
                    if(file){
                        this.$refs.mediaSelectBtn.addFiles([file]);
                    }
                }
            }
        },
        onThreadPaste(e,id,ti) {
            const clipboardItems = e.clipboardData.items;
            for (let i = 0; i < clipboardItems.length; i++) {
                const item = clipboardItems[i];
                if (item.type.indexOf("image") !== -1 || item.type.indexOf("video") !== -1) {
                    const file = item.getAsFile();
                    if (file) {
                        this.$refs['threadUploadMedia' + id][ti].onFilesSelected([file]);
                    }
                }
            }
        },
        getOptionValue(obj, name) {
            // if name is in dot notation, then get the value
            if (name.includes("[")) {
                name = name.replace(/\[(\w+)\]/g, ".$1");
            }
            return get(obj, name);
        },
        getOptionName(name, accountId) {
            // name can be like name[id] or just name
            if (name.includes("[")) {
                name = name.replace(/\[(\w+)\]/g, ".$1");
            }
            const arr = name.split('.');

            arr.unshift('options', accountId);

            return arr.reduce((a, s, i) => i === 0 ? s : a + `[${s}]`);
        },
        updateOptions(accountId, name, value) {
            // Check if the name is in array syntax e.g. img_alt_text[0]
            if (name.includes('[') && name.includes(']')) {
                // Extract the name before the array brackets
                const optionName = name.substring(0, name.indexOf('['));

                const indexAsString = name.match(/\[(.*?)\]/)[1];

                // Extract the index from the array syntax
                const index = parseInt(indexAsString);

                if (!this.options[accountId][optionName]) {
                    if (isNaN(index)) {
                        // If the index is not a number, then it is a string, so we need to initialize it as an object
                        this.options[accountId][optionName] = {};
                    } else {
                        // If the index is a number, then it is an array, so we need to initialize it as an array
                        this.options[accountId][optionName] = [];
                    }
                }

                this.options[accountId][optionName][indexAsString] = value;
            } else {
                // if we need to update with dot notation
                if (name.includes(".")) {
                    set(this.options[accountId], name, value);
                } else {
                    this.options[accountId][name] = value;
                }
            }

            // execute onUpdate handler if needed
            // doesn't work with dot notation in name
            let inputObj = null;
            this.postOptions[accountId].options.some(opt => {
                return opt.inputs?.some(inp => {
                    if (inp.props?.name === name) {
                        inputObj = inp;
                        return true;
                    }
                });
            });

            if (inputObj && inputObj.onUpdate) {
                // call the update handler; this update handler can also modify this.options[id]
                inputObj.onUpdate(value, this.options[accountId]);
            }

            this.options = cloneDeep(this.options); // trigger reactivity
        },
        async submitForm(data) {
            if (this.show.loader) return;

            const publishingNow = this.publishAt === this.$momentUTC().format("YYYY-MM-DD HH:mm:00");

            function appendFormData(formData, data, name) {
                name = name || "";
                if (typeof data === "object") {
                    $.each(data, function (index, value) {
                        if (name === "") {
                            appendFormData(formData, value, index);
                        } else {
                            appendFormData(formData, value, name + "[" + index + "]");
                        }
                    });
                } else {
                    if (data) {
                        formData.append(name, data)
                    }
                }
            }

            const formData = new FormData();
            if (this.attachments.length) {
                // add attachments with their indexes preserved
                this.attachments.forEach((attachment, index) => {
                    if (attachment instanceof File) {
                        // is file
                        formData.append("attachments[" + index + "]", attachment);
                    } else {
                        // is path to our existing file
                        attachment["path"] = attachment.key ? attachment.key : attachment["path"];
                        formData.append("existing_attachments[" + index + "]", JSON.stringify(attachment));
                    }
                });
            }

            if (this.mode !== "edit") {
                this.selectedAccountsIds.forEach(id => {
                    formData.append("accounts[]", id);
                });
            }

            if (this.mode !== "edit" && this.shouldCustomizeContent) {
                // content should be an object having content for all selected accounts
                const allContent = {
                    ...this.customizedContent
                };
                // exclude default
                delete allContent.default;
                // add to form data
                appendFormData(formData, allContent, "content");
            } else {
                formData.append("content", this.content);
            }

            // Always send publish_at as array - single date becomes array with 1 item
            const publishDates = this.scheduleMode === 'multiple' ? this.publishAtArray : [this.publishAt];

            // Send each date as array element (Laravel will automatically create array)
            publishDates.filter(date => date).forEach((date, index) => {
                formData.append(`publish_at[${index}]`, date);
            });
            if (data && data.isDraft) formData.append("draft", "true");
            if(data && data.queueIds && data.queueIds.length){
                data.queueIds.forEach(queueId => {
                    formData.append("queue_ids[]", queueId);
                });
            }

            // bind post to team or no team (whatever the case is)
            // for no team, we send 0 because if we don't send 0, it auto assigns a team
            formData.append("team_id", this.selectedTeam ? this.selectedTeam : "0");

            if (this.mode === "edit") {
                if (this.options[this.post.account_id]) {
                    // in case of editing, options should be a flat object
                    appendFormData(formData, this.options[this.post.account_id], "options");
                }

                // reset result column
                formData.append("reset_result", "true");

                // this is required so the endpoint acts as a PUT method instead of PATCH
                // that means, the values that we don't send are removed from the post
                // without this, only the values that we send are updated
                // with this, all values are updated and those which we don't send get removed
                formData.append("from", "composer");
            } else {
                appendFormData(formData, this.options, "options");
            }

            if (this.mode === "edit") {
                formData.append("_method", "PATCH");
            }

            if (this.mode === "queue_add" || this.mode === "queue_edit") {
                this.$emit("postAdded", formData);

                if(this.mode === "queue_add"){
                    window.__recordUsageEvent("queue_post_added");
                } else {
                    window.__recordUsageEvent("queue_post_updated");
                }

                return;
            }

            this.show.loader = true;
            try {

                const res = await (this.mode === "edit"
                    ? axios.post("/app/publish/posts/" + this.post.id, formData)
                    : axios.post("/app/publish/posts", formData));

                const {data} = res;

                if (this.mode === "edit") {
                    this.$emit("postUpdated", data.post);
                    window.__recordUsageEvent("post_updated");
                } else {
                    // emit event
                    this.$emit("postsAdded", data.posts);
                    window.__recordUsageEvent("post_added");

                    alertify.success("The post will be published at the scheduled time and may take a few minutes to appear on the account.");

                    if (!publishingNow && data.posts && data.posts.length) {
                        let activePostIds = [];
                        data.posts.forEach(post => {
                            const shouldAdd =
                                scheduledPosts &&
                                ((scheduledPosts.type === "scheduled" && !post.draft && post.approved) ||
                                    (scheduledPosts.type === "draft" && post.draft && post.approved) ||
                                    (scheduledPosts.type === "awaiting_approval" && !post.draft && !post.approved));
                            if (shouldAdd) {
                                scheduledPosts.posts.items.push(post);
                                activePostIds.push(post.id);
                            }
                        });
                        if (scheduledPosts && activePostIds.length) {
                            scheduledPosts.activePosts = activePostIds;
                        }
                    }
                }

                this.reset();
            } catch (e) {
                axiosErrorHandler(e);
            } finally {
                this.show.loader = false;
            }
        },
        postNow(e) {
            this.publishAt = this.$momentUTC().format("YYYY-MM-DD HH:mm:00");
            this.$nextTick(this.submitForm);
            e && e.preventDefault();
        },
        setPublishAt(date) {
            this.publishAt = this.$momentUTC(date).format("YYYY-MM-DD HH:mm:00");
            $(this.$refs.publish_at_datetime)
                .data("datetimepicker")
                .date(date);
        },

        openScheduleModal(e) {
            $(this.$refs.schedule_modal).modal("show");
            e && e.preventDefault();
        },
        closeScheduleModal(e) {
            $(this.$refs.schedule_modal).modal("hide");
        },
        submitScheduledPost(e) {
            // Always submit form the same way since we now always send arrays
            this.submitForm();
            $(this.$refs.schedule_modal).modal("hide");
            e && e.preventDefault();
        },

        addDate() {
            // Open date picker for adding new date
            this.openDatePicker(-1);
        },

        removeDate(index) {
            // Ensure publishAt is an array
            if (!Array.isArray(this.publishAt)) {
                this.publishAt = this.publishAt ? [this.publishAt] : [];
            }
            this.publishAt.splice(index, 1);
        },

        formatDateDisplay(dateStr) {
            if (!dateStr) return 'Select date';
            return this.$momentUTC(dateStr).format('ddd, MMM Do YYYY');
        },
        formatTimeDisplay(timeStr) {
            if (!timeStr) return 'Select time';
            // Create a moment object from the time string (assuming today's date)
            const timeWithDate = this.$momentUTC().format('YYYY-MM-DD') + ' ' + timeStr;
            return this.$momentUTC(timeWithDate).format('h:mm A');
        },
        openQueuesModal(e) {
            $(this.$refs.queue_modal).modal("show");
            e && e.preventDefault();
        },
        closeQueuesModal(e) {
            $(this.$refs.queue_modal).modal("hide");
        },
        submitQueuedPost(e) {
            this.submitForm({
                queueIds: $(this.$refs.inputQueueSelect).val()
            });
            $(this.$refs.queue_modal).modal("hide");
            e && e.preventDefault();
        },

        unselectAllAccounts() {
            this.selectedAccountsIds = [];
        },
        selectDefaultAccount() {
            if (
                this.availableAccounts.length === 1 &&
                !this.selectedAccountsIds.includes(this.availableAccounts[0].id)
            ) {
                this.selectedAccountsIds.push(this.availableAccounts[0].id);
            }
        },
        getLinkRegex() {
            return /\b((?:https?:\/\/|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}\/)(?:[^\s()<>]+|\(([^\s()<>]+|(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'\".,<>?«»“”‘’]))/gi;
        },
        getLinks() {
            // return links in content
            const regex = this.getLinkRegex();
            return (this.content || "").match(regex) || [];
        },
        getLink() {
            // always return the first link
            const matches = this.getLinks();
            if (matches.length) {
                if (!matches[0].startsWith("http")) return "http://" + matches[0];
                else return matches[0];
            }
            return null;
        },
        getTweetLength(text = "") {
            return twitter.parseTweet(text).weightedLength;
        },
        loadSuggestedHashtags: debounce(async function () {
            if (!this.content.trim().length) return;
            try {
                const {data} = await axios.get("/app/json/search/suggested_hashtags", {
                    params: {
                        q: this.content,
                        accounts: this.selectedAccountsIds
                    }
                });
                this.allSuggestedHashtags = data.items.filter(s => s); // array of strings
            } catch (e) {
                console.error(e);
            }
        }, 1000),
        async autocompleteByAI() {
            // autocomplete content by AI
            this.show.loader = true;
            try {
                const {data} = await axios.post("/api/v1/generated_content/autocomplete_post", {
                    content: this.content,
                    account_id: this.focusedAccount.id,
                    team_id: this.currentTeam ? this.currentTeam.id : null
                });
                this.$nextTick(() => {
                    this.content = this.content + data.content;
                });
            } catch (e) {
                axiosErrorHandler(e);
            }
            this.show.loader = false;
        },
        reset(hard = false) {
            return new Promise(res => {
                this.$refs.mediaSelectBtn.removeAll(); // remove media if any
                this.content = "";

                // also remove custom content data if set
                this.customizedContent = {};
                this.customizeContent = false;

                // reset post options
                this.options = {};

                this.post = null;

                this.mode = "add";

                const postOptionsState = this.show.postOptions === true;
                this.show.postOptions = false; // temporary hide, so it gets re-rendered next time it shows

                this.$nextTick(() => {
                    if (postOptionsState) {
                        // re show post options, this is because this is a way to force it to get re-rendered and clear old inputs
                        this.show.postOptions = true;
                    }

                    if (hard) {
                        Object.keys(this.show).forEach(k => {
                            this.show[k] = k === "accounts"; // show accounts only on hard reset
                        });
                        this.setPublishAt(new Date());
                    }

                    res();
                });
            });
        },
        previewPostForNextAccount() { 
            this.preview.account[this.preview.network].index++;
            const accountId = parseInt(this.previewAccountIdsByNetwork[this.preview.network][this.preview.account[this.preview.network].index]);
            this.preview.account[this.preview.network].id = accountId;
            if(this.customizeContent){
                this.customizeContentFor = accountId;
            }
        },
        previewPostForPreviousAccount() {
            if (this.preview.account[this.preview.network].index > 0) {
                this.preview.account[this.preview.network].index--;
                const accountId = parseInt(this.previewAccountIdsByNetwork[this.preview.network][this.preview.account[this.preview.network].index]);
                this.preview.account[this.preview.network].id = accountId;
                if(this.customizeContent){
                    this.customizeContentFor = accountId;
                }
            }
        },
        resetMediaOptions(index){
            this.selectedAccountsIds.forEach(acc =>{
                const accountOptions = this.options[acc];
                if (accountOptions?.media_alt_text?.length){
                    accountOptions.media_alt_text.splice(index, 1);
                }

                if (accountOptions?.thumbnail){
                    accountOptions.thumbnail = null;
                }
            });
        },
        async populateAttachments(attachments) {
            this.$refs.mediaSelectBtn.addFiles(attachments);
        },
        async populate(post) {
            // populate all content/fields from existing post
            // post content
            this.content = post.content ? post.content : '' ;

            if(this.mode === 'queue_add' || this.mode === "queue_edit"){
                this.options = post.options;
            }else {
                this.options = {
                    [post.account_id]: cloneDeep(post.post_options)
                };
            }

            if (this.mode === "add") {
                this.accounts.forEach(a => (this.options[a.id] = cloneDeep(post.post_options)));
            }

            this.setPublishAt(this.$momentUTC(post.publish_at, "YYYY-MM-DD HH:mm:00").toDate());

            this.show.postOptions = true;

            this.attachments = post.attachments

            if(this.attachments){
                this.$refs.mediaSelectBtn.setFiles(this.attachments);

            }
        },
        async openCanva(cb) {
            this.show.loader = true;
            let url = await canva.createDesign();
            if (url) {
                let file = await canvaHelper.getFileFromUrl(url);
                cb(file);
            }
            this.show.loader = false;
        },
        async openContentDrips(cb) {
            this.show.loader = true;
            try {
                let base64Files = await contentDrips.createDesign();
                if (base64Files) {
                    let files = await contentDrips.getFileFromBase64(base64Files);
                    cb(files);
                }
    
                this.show.loader = false;
            } catch (error) {
               this.show.loader = false; 
            }
        },
        debounced$forceUpdate: debounce(function () {
            this.$forceUpdate();
        }, 500),
        removeAttachment(file) {
            this.$refs.mediaSelectBtn.removeFile(file);
        },
    },
    async mounted() {
        // load data
        try {
            this.accounts = (await getAccounts(true)).filter(a => {
                if (a.team) {
                    // team account, so check permission
                    const me = a.team.members.find(m => m.id === appConfig.userId);
                    if (!me.permissions.includes("posts.create") && !me.permissions.includes("posts.edit")) {
                        // cannot create posts, so don't show this account in post composer
                        return false;
                    }
                }
                if (!a.active) {
                    return false;
                }
                return true;
            });

            if (this.selectAccounts) {
                this.selectedAccountsIds = [...this.selectAccounts];
            }
        } catch (e) {
            axiosErrorHandler(e);
            alert("Reloading page");
            return document.location.reload();
        }
        try {
            const teams = this.teams;
            let defTeamSet = false;
            if (this.selectTeam) {
                teams.forEach(team => {
                    if (team.id + "" === this.selectTeam + "") {
                        this.showAccountsForTeam(team.id);
                        defTeamSet = true;
                    }
                });
            } else if (!this.noCookies) {
                const teamFromCookie = Cookies.get("editor_selected_team");
                teams.forEach(team => {
                    if (team.id + "" === teamFromCookie) {
                        this.showAccountsForTeam(team.id);
                        defTeamSet = true;
                    }
                });
            }
            if (!defTeamSet && !this.accounts.filter(a => !a.team_id).length) {
                // no user-owned account, so set a team as active if possible
                if (teams.length) {
                    this.showAccountsForTeam(teams[0].id);
                    defTeamSet = true;
                }
            }
        } catch (e) {
            alert("Unable to load teams.");
        }
        try {
            const res = await axios.get("/app/json/collection/queues");
            this.queues = res.data;
        } catch (e) {
            alert("Unable to load queues.");
        }
        this.initialize();
    },
    updated() {
        if (this.selectAccounts && !isEqual(this.selectAccounts, this.selectedAccountsIds)) {
            this.selectedAccountsIds = [...this.selectAccounts];
        }
    },
    beforeDestroy() {

    }
};
</script>

<style lang="scss">
@import "../../../sass/variables";

.dummy-textarea {
    resize: none;
    overflow: hidden;
    overflow-wrap: break-word;
    height: 70.8px;
}

form.editor {
    clear: both;
    position: relative;
}

.new_post_wrapper {
    margin-bottom: -30px;
}
.content_counter_wrapper{
    width: 52px;
}
.teams-selector {
    max-width: 200px;
}

.character_count {
    position: relative;
    right: 10px;
    float: right;
    bottom: 30px;
    margin-bottom: -30px;
    font-size: 16px;
}
.show_post_options {
    text-decoration: none !important;
}

.accounts-container .list-group-item {
    &.selected {
        border-right: 2px solid $blue !important;
    }
}

.verified_icon {
    color: #1D9BF0;
}
.preview{
    .nav-item{
        font-size: 16px;
        margin-right: 1px;
        .nav-link {
            border-bottom: 1px solid transparent;
            &.active {
                border-bottom-color: #0557f0;
            }
        }
    }
}
@media (min-width: 576px){
    .editor-accounts-modal-content {
        .modal-body{
            height: 260px;
            overflow:auto
        }
    }
}
.preview-button-group{
    box-shadow: 0px 4px 8px -1px rgba(29, 33, 41, 0.04);
    border: 1px solid #F2F4F7;
}
.preview-network{
    width: 36px;
    height: 36px;
    line-height: 1 !important;
    img {
        width: 20px !important;
        height: 20px !important;
    }
}
.preview-network-selected{
    background: #F2F4F7;
}
.editor-rendered-in-popup{
    .modal-body{
        flex: 0 1 auto;
    }
}
.post-option-account{
    width: 36px;
    height: 36px;
    line-height: 1 !important;
    img {
        width: 20px !important;
        height: 20px !important;
    }
}
.post-option-account-selected{
    background: #F2F4F7;
}
.attach-media-dropdown,.thread-attach-media, .picker-dropdown, .suggested-dropdown{
    button{
        border: 1px solid transparent;
        &:hover{
            background: #F2F4F7 !important;
        }
    }
    .add-border{
        border: 1px solid #E4E7ED !important;
        background: #F2F4F7 !important;
        
    }
}
.attach-media-dropdown.show button{
    border: 1px solid #E4E7ED !important;
    background: #F2F4F7 !important;
    .ph-caret-up{
        display: block !important;
    }
    .ph-caret-down{
        display: none;
    }
}
.show_post_options {
    text-decoration: none !important;
}

.hashtag-menu{
    height: 200px;
}
.rounded {
    border-radius: 0.75rem !important;
}

</style>
