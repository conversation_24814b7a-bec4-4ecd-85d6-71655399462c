<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddTypeColumnToInboxConversations extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('inbox_conversations', function (Blueprint $table) {
            // we need type so we can save a conversation type like comment, ad_comment, etc.
            // this also allows us to set up dedicated cron jobs for each type if needed
            $table->string('type')->nullable()->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('inbox_conversations', function (Blueprint $table) {
            $table->dropColumn('type');
        });
    }
}
