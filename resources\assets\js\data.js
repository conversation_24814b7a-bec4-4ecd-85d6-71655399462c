import { axios } from "./components";
import NodeCache from "node-cache";
const myCache = new NodeCache({ stdTTL: 120, checkperiod: 120 });
const promisesByKey = {};
const remember = async (key, backend) => {
    let value = myCache.get(key);
    if (value === undefined) {
        // handle miss!
        if (!promisesByKey[key]) promisesByKey[key] = backend();
        value = await promisesByKey[key];
        myCache.set(key, value, 120);
    }
    return value;
};

export async function getAccounts(include_inactive = false) {
    return await remember("accounts", async () => {
        const { data } = await axios.get("/app/json/collection/accounts?type=all" + (include_inactive ? "&include_inactive=true" : ""));
        return data;
    });
}

export async function getUserTeams(type = "joined") {
    return await remember("teams", async () => {
        const { data } = await axios.get("/app/json/collection/teams?type=" + type);
        return data;
    });
}

export async function getNetworks(include_inactive = false){
    return await remember("networks", async () => {

        // get networks from accounts
        const accounts = await getAccounts(include_inactive);

        const networks = [];
        const addedTypes = [];

        accounts.forEach(account => {
            const type = account.type;
            const typeTitle = account._type;
            if (!addedTypes.includes(type)) {
                networks.push({ title: typeTitle, type });
                addedTypes.push(type);
            }
        });

        return networks;
    });
}
