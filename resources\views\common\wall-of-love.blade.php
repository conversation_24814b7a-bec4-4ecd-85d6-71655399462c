@extends('layout.default')
@php($title = 'Wall of Love')
@php($description = 'SocialBu is a social media management tool that is highly valued by social media managers, marketing agencies, and brands. See what our customers are saying about our platform.')
@php($image = 'https://socialbu.com/images/site/link-preview.jpg')
@php($url = 'https://socialbu.com/love')
@section('title', $title . ' | ' . config('app.name'))
@push('head_html')
<meta name="description" content="{{ $description }}" />
<link rel="canonical" href="{{ $url }}" />

<meta property="og:locale" content="en_US" />
<!--
    <meta property="og:type" content="website" />
    -->
<meta property="og:title" content="{{ $title }}" />
<meta property="og:description" content="{{ $description }}" />
<meta property="og:url" content="{{ $url }}" />
<meta property="og:site_name" content="SocialBu" />
<meta property="og:image" content="{{ $image }}" />
<meta property="fb:app_id" content="{{ config('services.facebook.client_id') }}" />

<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:image" content="{{ $image }}" />
<meta name="twitter:title" content="{{ $title }}" />
<meta name="twitter:description" content="{{ $description }}" />
<meta name="twitter:site" content="@socialbuapp" />
@endpush
@section('content')
<header class="header pt-8 pb-0">
            <div class="col-12 text-center p-0 px-md-10 px-5 py-7">
                <h1 class="display-1">Wall of Love</h1>
                <p class="lead-2">A space to celebrate the kind words, amazing stories, and<br class="d-none d-lg-block"> heartfelt support from our users, fans, and friends.</p>
            </div>
</header>
<main class="main-content">
    <div class="px-md-10 px-5">
        <script src="https://widget.senja.io/widget/02f76a54-efd6-4c85-98af-5a4291f98eaf/platform.js" type="text/javascript" async></script>
        <div class="senja-embed" data-id="02f76a54-efd6-4c85-98af-5a4291f98eaf" data-mode="shadow" data-lazyload="false" style="display: block;"></div>
    </div>
    @include('common.internal.join-us-block')
</main>
@endsection
