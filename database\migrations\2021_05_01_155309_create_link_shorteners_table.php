<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateLinkShortenersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('link_shorteners', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name', 191)->index();
            $table->string('type', 20)->index(); // type, e.g. bitly, bitly_custom, etc
            $table->string('external_id');

            $table->integer('user_id')->unsigned()->index(); // user who added this
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade')->onUpdate('cascade');

            $table->json('options')->nullable(); // will store misc data

            $table->boolean('active')->default(true); // if its active or not

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('link_shorteners');
    }
}
