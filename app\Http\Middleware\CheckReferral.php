<?php

namespace App\Http\Middleware;

use App\User;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CheckReferral
{

    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if( $request->query('ref') ) {

            if( !$request->hasCookie('referral') ){

                /** @var User $referredByUser */
                $referredByUser = User::findByPublicId($request->query('ref'));
                if(!$referredByUser) {
                    return $next($request);
                }

                try {
                    $insert = DB::connection('mysql_insights')->table('referral_hits')->insert([
                        'referrer_id' => $referredByUser->id,
                    ]);
                    if(!$insert){
                        report(new \Exception('Database insert failed for referral hit'));
                    }
                } catch (\Exception $exception){
                    report($exception);
                }

                // 90 days cookie
                return redirect($request->url())->withCookie(cookie()->make('referral', $request->query('ref'), 129600));

            }

        }

        return $next($request);
    }
}
