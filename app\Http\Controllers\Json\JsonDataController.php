<?php

namespace App\Http\Controllers\Json;

use App\Account;
use App\Automation;
use App\Feed;
use App\Http\Controllers\User\LinkShortenersController;
use App\LinkShortener;
use App\Post;
use App\Team;
use App\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Str;

class JsonDataController extends Controller
{

    /**
     * Serve the request
     * @param Request $request
     * @param string $type
     * @param int $id
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\Response
     */
    public function serve(Request $request, $type = null, $id = null)
    {

        if(!in_array($type, ['team', 'account', 'post', 'automation', 'feed', 'link_shortener']))
            abort(404);

        if(!$id)
            abort(400);

        $result = $this->{$type}($id, $request);

        return response()->json($result);
    }

    /**
     * Get team data
     * @param int $id
     * @return mixed
     */
    public function team($id)
    {
        /** @var User $user */
        $user = \Auth::user();

        $has_access = $user->joinedTeams()->where('id', $id)->count() + $user->teams()->where('id', $id)->count();

        if($has_access === 0) abort(404, 'Not found');

        $team = Team::findOrFail($id);

        return Team::transform($team);
    }

    /**
     * Get account data
     * @param int $id
     * @return mixed
     */
    public function account($id)
    {
        /** @var Account $acc */
        $acc = user()->getAvailableAccounts(['*'], true)->firstWhere('id', $id);

        if(!$acc) abort(404);

        $result = Account::transform($acc)->toArray();
        $result['_type'] = $acc->getType();
        $result['test_url'] = route('accounts.test_connection', ['id' => $acc->id]);
        $result['reconnect_url'] = route('accounts.auth', array_filter([
            'provider' => explode('.', $acc->type)[0],
            'service' => $acc->type === 'google.youtube' ? 'youtube' : null,
        ]));
        // append account_id (NEEDED FOR IG)
        $result['reconnect_url'] = Str::contains($result['reconnect_url'], '?') ? $result['reconnect_url'] . '&account_id=' . $id : $result['reconnect_url'] . '?account_id=' . $id;

        $result['shared_with_count'] = 0; // temporary, supposed to be the number of teams this account is shared with
        return $result;
    }

    /**
     * Get automation data
     * @param int $id
     * @return mixed
     */
    public function automation($id)
    {
        $ob = Automation::userAutomations()->findOrFail($id);
        return Automation::transform($ob)->toArray();
    }

    /**
     * Get feed data
     * @param int $id
     * @return mixed
     */
    public function feed($id)
    {
        /** @var Feed $ob */
        $ob = Feed::select(['id', 'name', 'user_id'])->ofUser()->findOrFail($id);
        $result = $ob->toArray();
        $result['accounts'] = $ob->accounts()->get(['id', 'name', 'type'])->toArray();
        return $result;
    }

    /**
     * Get post data
     * @param int $id
     * @param Request $request
     * @return mixed
     * @throws \Illuminate\Validation\ValidationException
     */
    public function post($id, $request)
    {
        /** @var Post $post */
        $post = Post::findOrFail($id); // find post by id

        // get all accounts current user has access to
        $accountIds = user()->getAvailableAccounts()->map(function($a){
            return $a->id;
        })->toArray();

        if(!in_array($post->account_id, $accountIds)){
            abort(404);
        }

        return Post::transform($post)->toArray();
    }

    /**
     * @param int $id
     * @param Request $request
     * @return mixed
     */
    public function link_shortener($id, Request $request)
    {
        /** @var LinkShortener $shortener */
        $shortener = LinkShortenersController::query()->findOrFail($id);
        return LinkShortener::transform($shortener, true);
    }
}
