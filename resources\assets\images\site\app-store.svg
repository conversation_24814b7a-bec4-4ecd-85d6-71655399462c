<svg xmlns="http://www.w3.org/2000/svg" width="120.505" height="39.419" viewBox="0 0 120.505 39.419"><defs><style>.a{fill:#fff;}</style></defs><rect width="120.505" height="39.419" rx="6"/><g transform="translate(8.083 6.128)"><path class="a" d="M712.32,360.75a19.526,19.526,0,0,1-1.764,3.294c-1.146,1.745-2.761,3.918-4.763,3.936-1.779.017-2.235-1.157-4.649-1.144s-2.918,1.165-4.7,1.149c-2-.018-3.532-1.98-4.678-3.725-3.2-4.879-3.539-10.6-1.563-13.648a7.092,7.092,0,0,1,5.7-3.429c2.121,0,3.455,1.163,5.209,1.163,1.7,0,2.738-1.165,5.191-1.165a6.923,6.923,0,0,1,5.218,2.755A5.971,5.971,0,0,0,712.32,360.75Z" transform="translate(-688.979 -340.822)"/><g transform="translate(11.219)"><path class="a" d="M741.8,324.062a5.954,5.954,0,0,0,1.322-4.41,6.432,6.432,0,0,0-4.153,2.234,5.526,5.526,0,0,0-1.357,4.3A5.429,5.429,0,0,0,741.8,324.062Z" transform="translate(-737.549 -319.652)"/></g></g><g transform="translate(36.597 6.186)"><g transform="translate(0 9.443)"><path class="a" d="M817.015,361.717l-4.591,13.513h2.1l1.37-4.149h4.445l1.409,4.149h2.16l-4.61-13.513Zm1.089,2.222c.155.6.322,1.23.537,1.906l1.237,3.583h-3.521l1.22-3.606C817.763,365.237,817.94,364.605,818.1,363.939Z" transform="translate(-812.424 -361.502)"/><path class="a" d="M870.558,376.883a3.826,3.826,0,0,0-3.25,1.559l-.081-1.344h-1.87l.011.172c.055.837.078,1.759.078,3.082v10.416h2.045v-4.861a3.59,3.59,0,0,0,2.795,1.2,4.215,4.215,0,0,0,3.1-1.317,5.586,5.586,0,0,0,1.368-3.932,5.439,5.439,0,0,0-1.188-3.591A3.89,3.89,0,0,0,870.558,376.883Zm-.509,1.693c1.823,0,2.638,1.688,2.638,3.361,0,2.158-1.041,3.5-2.716,3.5a2.479,2.479,0,0,1-2.409-1.9,2.608,2.608,0,0,1-.073-.662v-1.644a3.1,3.1,0,0,1,.065-.5c.01-.056.021-.112.029-.166A2.589,2.589,0,0,1,870.049,378.576Z" transform="translate(-853.13 -373.165)"/><path class="a" d="M916.139,376.883a3.827,3.827,0,0,0-3.251,1.559l-.081-1.344h-1.87l.01.172c.056.838.079,1.759.079,3.082v10.416h2.045v-4.861a3.588,3.588,0,0,0,2.794,1.2,4.22,4.22,0,0,0,3.1-1.317,5.594,5.594,0,0,0,1.367-3.932,5.439,5.439,0,0,0-1.189-3.591A3.888,3.888,0,0,0,916.139,376.883Zm-.509,1.693c1.822,0,2.638,1.688,2.638,3.361,0,2.158-1.042,3.5-2.716,3.5a2.478,2.478,0,0,1-2.408-1.9,2.6,2.6,0,0,1-.072-.662v-1.644a3.039,3.039,0,0,1,.065-.507c.01-.055.02-.11.03-.164A2.587,2.587,0,0,1,915.631,378.576Z" transform="translate(-888.183 -373.165)"/><path class="a" d="M974.355,366.726c-1.908-.739-2.519-1.32-2.519-2.394,0-.9.718-1.816,2.324-1.816a4.649,4.649,0,0,1,2.4.6l.173.107.577-1.706-.114-.067a5.923,5.923,0,0,0-2.977-.668c-2.607,0-4.428,1.539-4.428,3.742,0,1.777,1.14,3.019,3.589,3.909,1.735.649,2.381,1.313,2.381,2.452,0,1.289-1,2.089-2.6,2.089a5.6,5.6,0,0,1-2.947-.835l-.177-.114-.549,1.746.1.067a7,7,0,0,0,3.455.888c3.514,0,4.76-2.143,4.76-3.978C977.8,368.859,976.805,367.693,974.355,366.726Z" transform="translate(-933.209 -360.785)"/><path class="a" d="M1009.86,367.735l-2.005.63v1.7h-1.468V371.7h1.468V376.7a3.826,3.826,0,0,0,.708,2.594,2.621,2.621,0,0,0,1.978.777,4.817,4.817,0,0,0,1.58-.244l.115-.041-.1-1.637-.2.06a3.087,3.087,0,0,1-.951.11c-.792,0-1.131-.5-1.131-1.678V371.7h2.466v-1.634h-2.466Z" transform="translate(-961.585 -366.13)"/><path class="a" d="M1037.862,376.883c-2.86,0-4.858,2.135-4.858,5.191,0,2.964,1.933,5.034,4.7,5.034h.019a4.82,4.82,0,0,0,4.858-5.21,5.166,5.166,0,0,0-1.3-3.616A4.549,4.549,0,0,0,1037.862,376.883Zm-.059,1.614c1.839,0,2.676,1.793,2.676,3.46,0,2.049-1.143,3.537-2.715,3.537h-.02c-1.551,0-2.676-1.463-2.676-3.478C1035.069,380.265,1035.914,378.5,1037.8,378.5Z" transform="translate(-982.054 -373.165)"/><path class="a" d="M1084.642,376.978l-.123-.031a1.969,1.969,0,0,0-.508-.064,2.916,2.916,0,0,0-2.563,1.646l-.06-1.431h-1.835l.012.172c.055.809.077,1.718.077,2.944l-.019,6.679h2.045v-5.21a5.252,5.252,0,0,1,.056-.793,2.259,2.259,0,0,1,2.151-2.06,5.23,5.23,0,0,1,.549.034l.219.022Z" transform="translate(-1017.851 -373.165)"/><path class="a" d="M1106.1,376.8c-2.722,0-4.623,2.175-4.623,5.289a4.571,4.571,0,0,0,4.818,4.936,7.845,7.845,0,0,0,3.237-.621l.123-.056-.393-1.523-.171.069a6.334,6.334,0,0,1-2.562.458,2.842,2.842,0,0,1-3.062-2.964l6.6.02.027-.13a4.568,4.568,0,0,0,.061-.853C1110.151,379.29,1109.09,376.8,1106.1,376.8Zm-.136,1.555a2.018,2.018,0,0,1,1.5.572,2.911,2.911,0,0,1,.7,1.9H1103.5A2.61,2.61,0,0,1,1105.96,378.354Z" transform="translate(-1034.708 -373.1)"/></g><path class="a" d="M812.424,321.311a14.9,14.9,0,0,1,1.961-.133,4.376,4.376,0,0,1,2.879.788,2.8,2.8,0,0,1,1.012,2.306,3.238,3.238,0,0,1-1.023,2.486,4.745,4.745,0,0,1-3.153.92,14.228,14.228,0,0,1-1.677-.076Zm.928,5.665a6.625,6.625,0,0,0,.928.038c1.961.009,3.026-.987,3.026-2.714.011-1.528-.939-2.467-2.878-2.467a5.821,5.821,0,0,0-1.075.085Z" transform="translate(-812.424 -320.884)"/><path class="a" d="M843.853,333.537a2.3,2.3,0,0,1-2.446-2.362,2.369,2.369,0,0,1,2.53-2.439,2.287,2.287,0,0,1,2.456,2.353,2.377,2.377,0,0,1-2.53,2.448Zm.032-.626a1.634,1.634,0,0,0,1.55-1.793,1.56,1.56,0,1,0-1.561,1.793Z" transform="translate(-834.712 -326.696)"/><path class="a" d="M866.191,329.187l.685,2.325c.137.512.274,1,.369,1.471h.032c.116-.465.285-.959.453-1.461l.833-2.335h.78l.791,2.3c.189.541.337,1.034.453,1.5h.032a12.691,12.691,0,0,1,.39-1.49l.727-2.306h.917l-1.645,4.593h-.844l-.78-2.192a15.022,15.022,0,0,1-.454-1.518h-.021a12.64,12.64,0,0,1-.464,1.528l-.822,2.182h-.844l-1.54-4.593Z" transform="translate(-853.042 -327.043)"/><path class="a" d="M901.212,330.084c0-.484-.011-.863-.042-1.243h.823l.052.75h.021a1.933,1.933,0,0,1,1.687-.854,1.747,1.747,0,0,1,1.8,1.955v2.742h-.927v-2.647c0-.74-.306-1.366-1.181-1.366a1.324,1.324,0,0,0-1.244.854,1.128,1.128,0,0,0-.064.389v2.771h-.927Z" transform="translate(-880.671 -326.696)"/><path class="a" d="M926.688,319.9h.928v6.737h-.928Z" transform="translate(-900.295 -319.904)"/><path class="a" d="M938.22,333.537a2.305,2.305,0,0,1-2.446-2.362,2.369,2.369,0,0,1,2.53-2.439,2.287,2.287,0,0,1,2.456,2.353,2.378,2.378,0,0,1-2.531,2.448Zm.031-.626a1.634,1.634,0,0,0,1.55-1.793,1.56,1.56,0,1,0-1.561,1.793Z" transform="translate(-907.283 -326.696)"/><path class="a" d="M964.695,332.332a6.914,6.914,0,0,0,.074,1.1h-.833l-.084-.579h-.031a1.961,1.961,0,0,1-1.561.683,1.391,1.391,0,0,1-1.561-1.319c0-1.11,1.1-1.717,3.068-1.708v-.095c0-.379-.115-1.072-1.159-1.063a2.66,2.66,0,0,0-1.339.341l-.211-.56a3.591,3.591,0,0,1,1.687-.4,1.707,1.707,0,0,1,1.95,1.879Zm-.906-1.243c-1.012-.019-2.161.142-2.161,1.034a.776.776,0,0,0,.865.8,1.287,1.287,0,0,0,1.255-.769.764.764,0,0,0,.042-.266Z" transform="translate(-926.45 -326.696)"/><path class="a" d="M987.614,319.9v5.551c0,.408.011.873.042,1.186h-.833l-.042-.8h-.021a1.91,1.91,0,0,1-1.729.9,2.19,2.19,0,0,1-2.193-2.334,2.29,2.29,0,0,1,2.288-2.467,1.755,1.755,0,0,1,1.54.693h.021V319.9Zm-.928,4.014a1.394,1.394,0,0,0-.042-.361,1.341,1.341,0,0,0-1.339-.958,1.571,1.571,0,0,0-1.529,1.765,1.523,1.523,0,0,0,1.508,1.707,1.381,1.381,0,0,0,1.36-1,1.3,1.3,0,0,0,.042-.361Z" transform="translate(-943.475 -319.904)"/><path class="a" d="M1020.708,333.537a2.305,2.305,0,0,1-2.446-2.362,2.369,2.369,0,0,1,2.53-2.439,2.287,2.287,0,0,1,2.457,2.353,2.378,2.378,0,0,1-2.53,2.448Zm.032-.626a1.634,1.634,0,0,0,1.55-1.793,1.56,1.56,0,1,0-1.56,1.793Z" transform="translate(-970.717 -326.696)"/><path class="a" d="M1044.782,330.084c0-.484-.011-.863-.042-1.243h.823l.053.75h.021a1.933,1.933,0,0,1,1.687-.854,1.747,1.747,0,0,1,1.8,1.955v2.742h-.928v-2.647c0-.74-.305-1.366-1.181-1.366a1.326,1.326,0,0,0-1.244.854,1.139,1.139,0,0,0-.063.389v2.771h-.928Z" transform="translate(-991.079 -326.696)"/><path class="a" d="M1079.124,324.424v1.1h1.328v.636h-1.328v2.477c0,.569.179.892.7.892a1.92,1.92,0,0,0,.538-.057l.042.636a2.787,2.787,0,0,1-.822.114,1.405,1.405,0,0,1-1-.351,1.668,1.668,0,0,1-.358-1.206V326.16h-.791v-.636h.791v-.844Z" transform="translate(-1016.215 -323.38)"/><path class="a" d="M1095.047,319.9h.928v2.866H1096a1.576,1.576,0,0,1,.664-.588,1.96,1.96,0,0,1,.949-.237,1.746,1.746,0,0,1,1.782,1.964v2.733h-.928V324c0-.75-.306-1.366-1.181-1.366a1.334,1.334,0,0,0-1.244.835.921.921,0,0,0-.063.4v2.771h-.928Z" transform="translate(-1029.766 -319.904)"/><path class="a" d="M1119.681,331.248a1.555,1.555,0,0,0,1.74,1.594,3.866,3.866,0,0,0,1.413-.228l.169.588a4.564,4.564,0,0,1-1.708.294,2.263,2.263,0,0,1-2.51-2.315,2.338,2.338,0,0,1,2.4-2.486,2.01,2.01,0,0,1,2.1,2.164,1.931,1.931,0,0,1-.031.4Zm2.711-.6a1.232,1.232,0,0,0-1.276-1.357,1.445,1.445,0,0,0-1.423,1.357Z" transform="translate(-1048.021 -326.664)"/></g></svg>