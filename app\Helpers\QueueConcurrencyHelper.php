<?php


namespace App\Helpers;


use Carbon\Carbon;

class QueueConcurrencyHelper
{
    private $id = null;
    private $maxJobs = 1;
    private $maxLastAttemptDelay = 120; // seconds
    private $details = null;
    private $isStarted = false;

    function __construct($id, $maxJobs = 1, $maxLastAttemptDelay = 120)
    {
        $this->id = '__' . self::class . '_' . md5($id);
        $this->maxJobs = $maxJobs;
        $this->maxLastAttemptDelay = $maxLastAttemptDelay;
        $this->details = $this->getDetails();
    }

    public function shouldNotWait(){
        if($this->getDetails()['count'] < $this->maxJobs){
            return true;
        } else {
            // count >= maxJobs
            $cbTimestamp = Carbon::createFromTimestamp($this->getDetails()['timestamp']);
            if($cbTimestamp->diffInSeconds(now()) > $this->maxLastAttemptDelay){
                return true;
            }
        }
        return false;
    }

    public function shouldWait(){
        return !$this->shouldNotWait();
    }

    /**
     * @throws \Exception
     */
    public function start(){

        if($this->isStarted){
           return;
        }

        $this->isStarted = true;

        if($this->shouldWait()){
            // should not start, throw error
            throw new \Exception('Should wait a bit more');
        }
        if($this->details['count'] < $this->maxJobs) {
            ++$this->details['count']; // increment
        }
        $this->details['timestamp'] = time(); // update last time

        $this->saveDetails();

    }

    /**
     * @throws \Exception
     */
    public function stop(){

        if(!$this->isStarted){
            return;
        }

        $this->isStarted = false;

        if($this->details['count'] > 0){
            --$this->details['count']; // decrement
        } else {
            // should not start, throw error
            throw new \Exception('Nothing to stop; Current count: ' . $this->details['count']);
        }
        $this->details['timestamp'] = time(); // update last time

        $this->saveDetails();
    }



    private function getDetails($forceGet = false){
        if(!$forceGet && $this->details){
            return $this->details;
        }
        $details = \Cache::get($this->id);
        if(!$details){
            // first attempt
            $details = [
                'count' => 0,
                'timestamp' => time(),
            ];
        }
        return $details;
    }

    private function saveDetails(){
        // save
        \Cache::forever($this->id, $this->getDetails());
    }
}
