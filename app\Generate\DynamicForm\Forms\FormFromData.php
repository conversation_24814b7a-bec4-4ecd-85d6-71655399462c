<?php

namespace App\Generate\DynamicForm\Forms;

use App\Generate\DynamicForm\FormInterface;

class FormFromData implements FormInterface
{
    protected $fields = [];
    protected $steps = [];
    protected $output = [];
    protected $outputComponents = [];

    public function __construct(array $data)
    {
        $this->fields = $data['fields'] ?? [];
        $this->steps = $data['steps'];
        $this->output = $data['output'];
        $this->outputComponents = $data['outputComponents'] ?? [];
    }

    public function fields(): array
    {
        return $this->fields;
    }

    public function steps(): array
    {
        return $this->steps;
    }

    public function outputData(): array
    {
        return $this->output;
    }

    public function outputComponents(): array
    {
        return $this->outputComponents;
    }
}
