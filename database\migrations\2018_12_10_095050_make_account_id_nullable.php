<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class MakeAccountIdNullable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('feed_posts', function (Blueprint $table) {
            $table->dropForeign(['account_id']);
        });
        Schema::table('feed_posts', function (Blueprint $table) {
            $table->integer('account_id')->nullable()->unsigned()->change();
        });
        Schema::table('feed_posts', function (Blueprint $table) {
            $table->foreign('account_id')->references('id')->on('accounts')->onDelete('cascade')->onUpdate('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('feed_posts', function (Blueprint $table) {
            $table->dropForeign(['account_id']);
        });
        Schema::table('feed_posts', function (Blueprint $table) {
            $table->integer('account_id')->nullable(false)->unsigned()->change();
        });
        Schema::table('feed_posts', function (Blueprint $table) {
            $table->foreign('account_id')->references('id')->on('accounts')->onDelete('cascade')->onUpdate('cascade');
        });
    }
}
