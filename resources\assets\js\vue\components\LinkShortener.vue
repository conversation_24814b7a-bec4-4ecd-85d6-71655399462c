<template>
    <div class="mr-1">

        <div class="row">
            <div class="col-12 text-center" v-if="!initialized || busy" v-html="spinnerHtml"></div>
            <template v-else-if="data">

                <div class="col-12 d-flex justify-content-between py-3 mb-2">
                    <div class="d-flex align-items-center">
                        <template v-if="data.active">
                            <i class="ph ph-fill ph-circle ph-xs text-success mr-1"></i> Connected
                        </template>
                        <template v-else>
                            <i class="ph ph-fill ph-circle ph-xs text-danger mr-1"></i> Disconnected
                        </template>
                    </div>
                    <div>
                        <button role="button" class="btn btn-sm btn-light" type="button" @click="testConnection">
                            Test Connection
                        </button>
                        <a role="button" class="btn btn-sm btn-primary"
                           :href="data.reconnect_url" v-if="data.reconnect_url && !data.active">
                            Reconnect
                        </a>
                    </div>
                </div>

                <div class="col-12 mb-6" v-if="data.available_settings && data.available_settings.length">
                    <h4>
                        Settings
                    </h4>
                    <div class="card border border-secondary">
                        <div class="card-body">
                            <div class="list-group list-group-flush">
                                <div class="list-group-item border-secondary"
                                     v-for="(setting, index) in data.available_settings">
                                    <div class="form-group">
                                        <template v-if="setting.type === 'dropdown'">
                                            <label :for="setting.name + index + 'opt'">
                                                {{ setting.title || setting.name }}
                                            </label>
                                            <select class="form-control"
                                                    v-model="data.settings[setting.name]"
                                                    :id="setting.name + index + 'opt'" :title="setting.name" :multiple="setting.multiple">
                                                <option v-for="opt in setting.options" :value="opt.value">
                                                    {{ opt.title }}
                                                </option>
                                            </select>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-12 mb-6">
                    <h5 class="font-weight-500">
                        Social Accounts
                    </h5>
                    <div class="card">
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush"
                                 v-tour:link_shortener__accounts.bottom="'The links will be shortened for the accounts that you set here'">
                                <div class="list-group-item d-flex justify-content-between align-items-center border-secondary py-4 px-0"
                                     v-for="(account, index) in data.accounts">
                                    <div class="d-flex">
                                        <div class="account position-relative">
                                            <img class="avatar avatar-sm" alt="avatar" :src="account.image" />
                                            <img class="position-absolute rounded-circle border border-white network-icon"
                                                alt="account type"
                                                :src="getIconForAccount(account.type, 'circular')"
                                                :title="account._type" v-tooltip>

                                        </div>
                                        <div class="ml-3 line-height-1">
                                            <h6 class="font-weight-500 mb-0">{{ account.name }}</h6>
                                            <p class="small-2 mb-0">{{ account._type }}</p>
                                        </div>
                                    </div>
                                    <div>
                                        <button class="btn btn-sm btn-light border-secondary" title="Remove Account"
                                                v-tooltip @click="data.accounts.splice(index, 1)">
                                            Remove
                                        </button>
                                    </div>
                                </div>
                                <div class="list-group-item border-secondary p-0 pt-20">
                                    <select  class="form-control input_team_accounts" name="accounts[]" multiple="multiple" id="input_team_accounts" title="Accounts"
                                             v-select2="select2Configs.accounts"
                                    ></select>
                                    <small class="small-2 text-light">
                                        Social media accounts you want to use with this link shortener. An account can only be assigned to one link shortener at a time.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-12 mb-6">
                    <h5 class="font-weight-600 mb-3">
                        Query Parameters
                    </h5>
                    <div class="card">
                        <div class="card-body p-0">
                            <div v-tour:link_shortener__queryparams="'These query parameters can be added to your target links automatically if they are not already set. You can use this for setting UTM parameters.'">
                                <div v-for="(param, index) in data.query_parameters">
                                    <div class="d-flex align-items-center mb-2">

                                        <div class="mr-2">
                                            <input class="form-control" type="text" placeholder="Parameter name" title="Parameter name"
                                                    v-model="param.key" />
                                        </div>
                                        <div class="mr-3">
                                            <input class="form-control" type="text" placeholder="Parameter value" title="Parameter value" v-model="param.value" />
                                        </div>
                                        <button class="btn btn-outline-light p-2" title="Remove query parameter"
                                            v-tooltip @click="data.query_parameters.splice(index, 1)">
                                            <i class="ph ph-trash ph-md cursor-pointer"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="px-0">
                                    <button class="btn btn-sm btn-light" title="Add query parameter"
                                        v-tooltip @click="data.query_parameters.push({ key: 'key here', value: 'value here' })">
                                        <div class="d-flex align-items-center">
                                            <i class="ph-bold ph-plus cursor-pointer mr-2"></i>
                                            Add
                                        </div>
                                    </button>
                                    <button class="btn btn-sm btn-light" title="Add common UTM parameters if not already added"
                                        v-tooltip @click="addUtmParams">
                                        <div class="d-flex align-items-center">
                                            <i class="ph-bold ph-plus cursor-pointer mr-2"></i>
                                            Add UTM parameters
                                        </div>
                                    </button>
                                    <div class="small-2 text-light mt-2">
                                        Query or UTM parameters that you want to set for your target links.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-12 d-flex justify-content-between mb-4 pt-md-0 pt-2">
                    <div>
                        <button class="btn btn-sm btn-outline-danger" @click="deleteLinkShortener">
                            Delete
                        </button>
                    </div>
                    <div>
                        <button class="btn btn-sm btn-primary" @click="save" v-if="data.active">
                            Save
                        </button>
                    </div>
                </div>

            </template>
        </div>

    </div>
</template>

<script>
import { spinnerHtml, getIconForAccount, alertify, axios, axiosErrorHandler } from "./../../components";
import $ from "jquery";
export default {
    name: "LinkShortener",
    data() {
        return {
            data: null,
            initialized: false,
            busy: false
        };
    },
    computed: {
        spinnerHtml: () => spinnerHtml,
        getIconForAccount: () => getIconForAccount,
        select2Configs() {
            return {
                accounts: {
                    config: {
                        placeholder: this.$lang.get("generic.type_to_search"),
                        ajax: {
                            url: "/app/json/search/accounts",
                            dataType: "json",
                            delay: 250,
                            data: function(params) {
                                return {
                                    q: params.term // search term
                                };
                            },
                            processResults: (data, params) => {
                                params.page = params.page || 1;

                                const aa = [];
                                data.items.forEach((item, index) => {
                                    item.text = item.name;
                                    item.value = item.id;
                                    if (!this.data.accounts.find(a => a.id === item.id)) {
                                        aa.push(item);
                                    }
                                });

                                return {
                                    results: aa
                                };
                            },
                            cache: true
                        },
                        minimumInputLength: 1
                    },
                    onSelecting: (d, el) => {
                        const account = d.params.args.data;
                        this.data.accounts.push(account);
                        $(el)
                            .select2("close")
                            .val(null);
                        return false;
                    }
                }
            };
        }
    },
    methods: {
        async initialize(id) {
            window.__recordUsageEvent("link_shortener_open");
            try {
                const { data } = await axios.get("/app/json/data/link_shortener/" + id);
                this.data = { ...data };
                this.$nextTick(() => {
                    this.initialized = true;
                });
            } catch (e) {
                axiosErrorHandler(e);
            }
        },
        deleteLinkShortener() {
            if (this.busy) return;
            alertify.delete(
                "This will be removed and unlinked from any accounts that you have set for this link shortener.",
                async () => {
                    this.busy = true;
                    try {
                        await axios.delete("/app/link_shorteners/" + this.data.id);
                        alertify.warning("Link Shortener has been disconnected");
                        document.location.reload();
                    } catch (e) {
                        axiosErrorHandler(e);
                        this.busy = false;
                    }
                }
            );
        },
        addUtmParams() {
            if (!this.data.query_parameters.find(o => o.key === "utm_source")) {
                this.data.query_parameters.push({
                    key: "utm_source",
                    value: "{{ network }}"
                });
            }
            if (!this.data.query_parameters.find(o => o.key === "utm_medium")) {
                this.data.query_parameters.push({
                    key: "utm_medium",
                    value: "social"
                });
            }
            if (!this.data.query_parameters.find(o => o.key === "utm_campaign")) {
                this.data.query_parameters.push({
                    key: "utm_campaign",
                    value: "{{ socialbu_id }}"
                });
            }
            window.__recordUsageEvent("link_shortener_add_default_utm_params");
        },
        async testConnection() {

            window.__recordUsageEvent("link_shortener_test_connection");

            this.busy = true;
            try {
                const { data } = await axios.post("/app/link_shorteners/" + this.data.id + "/test");

                if(data.active) {
                    alertify.success("Connected");
                } else {
                    alertify.error("Disconnected");
                }
                this.data = data;

            } catch (e) {
                axiosErrorHandler(e);
            }
            this.busy = false;
        },
        async save() {
            this.busy = true;
            try {
                await axios.patch("/app/link_shorteners/" + this.data.id, {
                    accounts: this.data.accounts.map(a => a.id),
                    query_parameters: this.data.query_parameters,
                    settings: this.data.settings
                });
                alertify.success("Successfully saved");
                document.location.reload();
            } catch (e) {
                axiosErrorHandler(e);
                this.busy = false;
            }
        }
    }
};
</script>

<style scoped>
.network-icon{
    top: 20px;
    left: 20px;
}
.text-light{
    color: #657085 !important;
}
</style>
