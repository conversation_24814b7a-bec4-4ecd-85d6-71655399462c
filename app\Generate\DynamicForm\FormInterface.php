<?php

namespace App\Generate\DynamicForm;

interface FormInterface
{
    /**
     * @return array
     */
    public function fields(): array;

    /**
     * @return array
     */
    public function steps(): array;

    /**
     * The final output of the form
     * @return array
     */
    public function outputData(): array;

    /**
     * Returns the fields that will be rendered with outputData
     * These fields have to be implemented on the client side for this to work
     * Format: [{component, props}]
     * @return array
     */
    public function outputComponents(): array;
}
