<template>
    <div class="modal fade" id="chat_bu_modal" tabindex="-1" role="dialog" aria-labelledby="chat_bu_modal_label"
         aria-hidden="true">
        <div class="modal-dialog modal-md modal-dialog-slideout right" role="document">
            <div class="modal-content">
                <div class="modal-header px-20 pt-5 pb-2">
                    <h5 class="modal-title d-flex align-items-center" id="chat_bu_modal_label">
                        <span class="mr-2">
                            Bu AI
                        </span>
                        <div class="dropdown" v-if="messages.length">
                            <button class="btn border-none p-0 btn-sm pb-1" type="button" id="dropdownMenuButton"
                                    data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <i class="ph ph-caret-down ph-md"></i>
                            </button>
                            <div class="dropdown-menu">
                                <a
                                    class="dropdown-item"
                                    href="#"
                                    @click.prevent="copyConversation"
                                >
                                <i class="ph ph-copy ph-md mr-1"></i> Copy conversation
                                </a>
                                <div class="dropdown-divider"></div>
                                <a
                                    class="dropdown-item"
                                    href="#"
                                    @click.prevent="clearConversation"
                                >
                                    <i class="ph ph-x ph-md mr-2"></i> Clear messages
                                </a>
                            </div>
                        </div>
                    </h5>
                    <div>
                        <button class="close-button" type="button" data-dismiss="modal" aria-label="Close">
                            <i class="ph ph-x ph-md"></i>
                        </button>
                    </div>
                </div>
                <div id="chat_messages_wrapper" class="modal-body p-0">

                    <div v-if="loading" v-html="overlayLoaderHtml"></div>
                    <div
                        class="h-100"
                        id="chat_messages"
                        v-else
                    >
                        <div
                            class="container-fluid messages-container"
                            v-for="(message, index) in messages"
                            v-bind:key="index + 'msg'">
                            <div class="row">
                                <div class="col">
                                    <div class="d-flex align-items-start flex-md-row flex-column">
                                        <div class="d-flex">
                                            <div
                                                class="mr-2"
                                                style="min-width: 48px">
                                                <img
                                                    src="/images/redesign/logo-rectangular.png"
                                                    class="avatar-sm mr-2"
                                                    alt="Bu"
                                                    v-if="message.sender !== 'me'"
                                                />
                                                <img
                                                    class="avatar-sm rounded-circle mr-2"
                                                    :src="userAvatar"
                                                    alt="me"
                                                    v-else
                                                />
                                            </div>
                                            <div class="overflow-auto">
                                                <nl2br :text="message.content" tag="p" />
                                            </div>
                                        </div>
                                        <div class="ml-auto"
                                            v-if="message.sender === 'you'">
                                                <!-- copy btn -->
                                            <button
                                                class="btn btn-outline-light border-0 btn-sm text-secondary text-decoration-none p-1"
                                                type="button"
                                                title="Copy"
                                                v-tooltip
                                                @click.prevent="copyMessage(message.content,index)">
                                                <i class="ph ph-copy ph-md" :ref="`copy_icon_${index}`"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div
                            v-if="!messages.length"
                            class="h-100 d-flex align-items-center justify-content-center w-100 m-auto">
                            <div class=" text-center">
                                <div class="logo bg-white rounded-xl m-auto mb-2">

                                    <img
                                        src="/images/redesign/logo-icon.png"
                                        alt="Bu"
                                    />
                                </div>

                                <p class="my-6">
                                    Hi, I’m Bu AI!
                                    <br><br>
                                    I can help you generate ideas, <br> answer your questions, and more.

                                </p>
                                <div class="text-center">
                                    <button
                                        class="btn btn-primary btn-sm"
                                        type="button"
                                        @click.prevent="sayHi"
                                    > 👋 Say Hi!
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div
                            class="container-fluid pb-5"
                            v-if="sending">
                            <div class="d-flex">
                                <div class="mr-2">
                                    <img
                                        src="/images/redesign/logo-rectangular.png"
                                        class="avatar-sm mr-4"
                                        alt="Bu"
                                    />
                                </div>
                                <div class="text-dark">
                                    <p class="d-flex align-items-center pb-0 mb-0 h-100">
                                        <span class="dot-flashing"></span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <!-- Form -->
                <div class="modal-footer px-md-20 px-4 pt-2 pb-3">
                    <form class="w-100" action="#"
                          @submit.prevent="send">
                        <div class="form-row">
                            <div class="col">
                                <div class="position-relative rounded-md">
                                    <textarea
                                        rows="1"
                                        ref="chat_input"
                                        class="form-control rounded-md pl-4 pr-3 py-3 chat-input"
                                        v-model="message"
                                        type="text"
                                        placeholder="Type your message..."
                                        aria-label="Type your message..."
                                        style="resize: none"
                                        @input="setChatInputButtonHeight($event)"
                                        @keydown="onInput">
                                    </textarea>
                                    <button type="submit" :disabled="!message" class="btn btn-primary d-flex align-items-center position-absolute cursor-pointer chat_input_button text-white p-1">
                                        <i class="ph ph-arrow-up ph-bold ph-md"></i>
                                    </button>

                                </div>
                            </div>
                        </div>
                    </form>
                    <div class="w-100 d-flex justify-content-center mt-3">
                       <p class="pr-1 small-2 mb-0">For SocialBu account queries, please </p> <a href="mailto:<EMAIL>">contact support</a>.
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {
    alertify,
    appConfig,
    axios,
    axiosErrorHandler,
    overlayLoaderHtml
} from "./../../components";
import copy from "copy-to-clipboard";
import {debounce} from "lodash";

export default {
    name: "ChatBu",
    data() {
        return {
            loading: false,
            sending: false,
            message: "",
            messages: [],
        };
    },
    computed: {
        overlayLoaderHtml: () => overlayLoaderHtml,
        userAvatar(){
            return document.querySelector("#user_avatar").src;
        }
    },
    methods: {
        async load() {
            // load messages
            this.loading = true;
            try {
                const { data } = await axios.get("/api/v1/chat_bu/messages");
                this.messages = data;
            } catch (e) {
                axiosErrorHandler(e);
            }
            this.loading = false;
            this.$nextTick(() => {
                this.scrollMessagesToBottom();
            });
        },
        focusTextArea() {
            // focus input
            if(this.$refs.chat_input) {
                this.$refs.chat_input.focus();
            }
        },
        scrollMessagesToBottom: debounce(function () {
            const messages = document.getElementById("chat_messages_wrapper");
            if (!messages) return;
            $(messages).animate({scrollTop: messages.scrollHeight}, 250, () => {
            });
        }, 100),
        appendMessage(message, sender = "you") {
            return new Promise(async (res) => {
                this.messages.push({
                    content: message,
                    sender,
                });
                this.$nextTick(() => {
                    this.scrollMessagesToBottom();
                    res();
                });
            });
        },

        onInput(event) {
            // on enter key, send message
            if (!event.shiftKey && event.keyCode === 13) {
                this.send(event);
                event.stopPropagation();
                event.preventDefault();
                return false;
            }
        },
        copyMessage(text,index) {
            // copy text to clipboard using js
            copy(text);
            const element = this.$refs[`copy_icon_${index}`][0];
            setTimeout(() => {
                element.classList.remove('ph-check');
                element.classList.add('ph-copy');
            },200);
            element.classList.remove('ph-copy');
            element.classList.add('ph-check');
        },
        setChatInputButtonHeight(event){
            const { target } = event;
            // return css properties of target
            const style = getComputedStyle(target);
            const maxHeight = parseFloat(style.maxHeight) || 120;
            target.style.height = "auto";
            const newHeight = target.scrollHeight;
            // Show vertical scrollbar only if the content exceeds maxHeight
            target.style.height = `${Math.min(newHeight, maxHeight)}px`;
            target.style.overflowY = newHeight > maxHeight ? "auto" : "hidden";

        },
        copyConversation() {
            // copy conversation to clipboard using js
            let text = "";
            this.messages.forEach((message) => {
                text +=
                    (message.sender === "you" ? "Bu" : "Me") +
                    ": " +
                    message.content.trim() +
                    "\n";
            });

            // add notice to the end
            text += `\n\n---\nThis conversation was copied from SocialBu (socialbu.com)`;

            copy(text);
        },
        async clearConversation(){
            this.loading = true;
            try {
                await axios.delete("/api/v1/chat_bu/messages");
                this.messages = [];
                this.$nextTick(() => {
                    this.scrollMessagesToBottom();
                });
            } catch (e) {
                axiosErrorHandler(e);
            }
            this.loading = false;
        },
        async sayHi() {
            this.message = "Hi";

            this.focusTextArea();

            await this.send();
        },
        async send() {
            if (this.message.trim() === "" || this.sending || this.loading) {
                return;
            }

            const msg = this.message;
            this.message = "";

            await this.appendMessage(msg, "me");
            this.$nextTick(async () => {
                this.sending = true;

                this.$nextTick(async () => {
                    this.scrollMessagesToBottom();
                    try {
                        await new Promise((res) => setTimeout(res, 1000));

                        // send message to server
                        const {data} = await axios.post("/api/v1/chat_bu/messages", {
                            message: msg,
                        });

                        await this.appendMessage(data, "you");
                    } catch (e) {
                        axiosErrorHandler(e);
                        this.message = msg;
                        this.messages.pop();
                    }
                    this.sending = false;
                });
            });
        },
    },
    async mounted() {
        await this.load();
    },
}
</script>

<style lang="scss" scoped>

#chat_messages {
    min-height: 70px;
    p.message-text {
        white-space: pre-wrap;
    }
}
.messages-container{
    padding: 0px 20px 20px 20px;
}
@media (max-width: 767px){
    .messages-container{
        padding: 0px 16px 20px 16px !important;
    }
}
.dot-flashing {
    position: relative;
    width: 12px;
    height: 12px;
    border-radius: 8px;
    background-color: #0557F0;
    color: #0557F0;
    animation: dot-flashing 1s infinite linear alternate;
    animation-delay: 0.5s;
  }
  .dot-flashing::before, .dot-flashing::after {
    content: "";
    display: inline-block;
    position: absolute;
    top: 0;
  }
  .dot-flashing::before {
    left: -15px;
    width: 12px;
    height: 12px;
    border-radius: 8px;
    background-color: #0557F0;
    color: #0557F0;
    animation: dot-flashing 1s infinite alternate;
    animation-delay: 0s;
  }
  .dot-flashing::after {
    left: 15px;
    width: 12px;
    height: 12px;
    border-radius: 8px;
    background-color: #0557F0;
    color: #0557F0;
    animation: dot-flashing 1s infinite alternate;
    animation-delay: 1s;
  }
  
  @keyframes dot-flashing {
    0% {
      background-color: #E3E6EB;
    }
    50%, 100% {
      background-color: #0557F0;
    }
}

.logo{
    width: 56px;
    height: 56px;
}
.input-group-append{
    background: white;
    margin: 0px;
}
.chat-input{
    min-height: 52px;
    max-height: 120px;
}
.chat_input_button{
    height: 28px;
    width: 28px;
    border-radius: 6px;
    right: 12px;
    bottom: 12px;
}
</style>
