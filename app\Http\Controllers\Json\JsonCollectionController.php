<?php

namespace App\Http\Controllers\Json;

use App\Account;
use App\Http\Controllers\Api\PostsController;
use App\Post;
use App\PublishQueue;
use App\Team;
use App\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class JsonCollectionController extends Controller
{

    /**
     * Serve the request
     * @param string $type
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function serve(Request $request, $type = null)
    {

        if (!in_array($type, ['accounts', 'team_accounts', 'teams', 'posts', 'queues', ]))
            abort(404);

        $result = $this->{$type}($request);

        return response()->json($result);
    }

    /**
     * Get available queues
     * @param \Illuminate\Http\Request $request
     * @return Collection
     */
    public function queues(Request $request)
    {

        $data = [];

        /** @var PublishQueue[] $queues*/
        $queues = PublishQueue::available();
        foreach ($queues as $queue) {
            $data[] = PublishQueue::transform($queue);
        }

        return collect($data);
    }

    /**
     * Get user accounts
     * @param \Illuminate\Http\Request $request
     * @return Collection
     * @throws \Illuminate\Validation\ValidationException
     */
    public function accounts(Request $request)
    {
        $this->validate($request, [
            'type' => 'in:all,user,shared',
        ]);

        $type = $request->input('type', 'all');
        $include_inactive = !!$request->input('include_inactive');

        $data = [];

        if ($type == 'all' || $type == 'user') {
            // get all user accounts
            /** @var Account[] $accounts */
            if(!$include_inactive)
                $accounts = Account::ofUser()->where('active', true)->get();
            else
                $accounts = Account::ofUser()->get();
            foreach ($accounts as $acc) {
                $sTime = time();
                $data[] = Account::transform($acc);
                $eTime = time();
                $timeTaken = $eTime - $sTime;
                if($timeTaken > 1){
                    \Log::info('Transforming account ' . $acc->id . ' - ' . $acc->name . ' (' . $acc->type . ') took ' . $timeTaken . 's');
                }
            }
        }

        if ($type == 'all' || $type == 'shared') {
            // get all user accounts
            /** @var User $user */
            $user = \Auth::user();
            $teams = $user->accountsFromTeams(false, $include_inactive);

            foreach ($teams as $team_id => $accounts) {
                /** @var Account[] $accounts */
                foreach ($accounts as $acc){
                    /** @var Team $team */
                    $team = Team::find($team_id);
                    $sTime = time();
                    $data[] = Account::transform($acc, $team);
                    $eTime = time();
                    $timeTaken = $eTime - $sTime;
                    if($timeTaken > 1){
                        \Log::info('Transforming account ' . $acc->id . ' - ' . $acc->name . ' (' . $acc->type . ') took ' . $timeTaken . 's');
                    }
                }
            }
        }

        return collect($data);
    }

    /**
     * Get user teams
     * @param \Illuminate\Http\Request $request
     * @return Collection
     */
    public function teams(Request $request)
    {

        if ($request->has('type') && !in_array($request->type, ['all', 'joined', 'created']))
            abort(400);

        $data = [];

        $type = $request->input('type', 'joined');

        if ($type == 'joined' || $type == 'all') {
            // get all user accounts
            /** @var Team[] $items */
            $items = \Auth::user()->joinedTeams()->get();
            foreach ($items as $itm) {
                $data[] = Team::transform($itm);
            }
        } 

        if ($type == 'created' || $type == 'all') {
            // get all user accounts
            /** @var Team[] $items */
            $items = Team::ofUser()->get();
            foreach ($items as $itm) {
                $data[] = Team::transform($itm);
            }
        }

        // remove duplicates
        $teamIds = [];
        $data = array_filter($data, function($team) use (&$teamIds){
            if(in_array($team['id'], $teamIds)){
                return false;
            }
            $teamIds[] = $team['id'];
            return true;
        });

        return collect($data);
    }

    /**
     * Get accounts pending posts
     * @param \Illuminate\Http\Request $request
     * @param bool $returnPaginated
     * @param null $postType
     * @param bool $getAll
     * @param int $perPage
     * @return Post[]|LengthAwarePaginator|Collection
     * @throws \Illuminate\Validation\ValidationException
     */
    public function posts(Request $request, $returnPaginated = false, $postType = null, $getAll = false, $perPage = 20)
    {

        $request = clone $request; // important, so we dont modify original request

        if($postType){
            $request->merge([
                'post_type' => $postType,
                'type' => $postType,
            ]);
        }

        // create type from post_type if needed
        if(!$request->has('type') && $request->has('post_type')) {
            $request->merge([
                'type' => $request->input('post_type'),
            ]);
        }


        $this->validate($request, [
            'start' => 'date',
            'end' => 'date',
            'type' => 'in:scheduled,draft,awaiting_approval,published,scheduled_or_awaiting_approval',
            'team' => 'integer|exists:teams,id',
            'accounts' => 'array',
            'accounts.*' => 'integer|exists:accounts,id',
            'user' => 'integer|exists:users,id',
            'source' => 'string',
            'q' => 'string',
        ]);

        $start = $request->input('start') ? Carbon::parse($request->input('start'), timezone())->timezone('UTC') : null;
        $end = $request->input('end') ? Carbon::parse($request->input('end'), timezone())->timezone('UTC') : null;

        $postType = $request->input('type', 'scheduled');

        $pageNo = $request->get("page", 1);

        if($request->input('team')){
            /** @var Team $team */
            $team = user()->joinedTeams()->findOrFail($request->input('team'));
            $allAccounts = $team->accounts()->get();
        }else {
            $allAccounts = user()->getAvailableAccounts(['*'], true);
        }

        if($request->input('accounts', [])){
            $allAccounts = $allAccounts->whereIn('id', $request->input('accounts', []));
        }

        // at this point, $allAccounts have accounts that are relevant for the user
        // for draft, we have to show posts where I am the author and I don't have permission to see all drafts but I still need to see only drafts where I am the author
        $relevantAccountIds = $allAccounts->map(function(Account $a){
            return $a->id;
        })->toArray();

        $showAllPostsForAccounts = $allAccounts->filter(function(Account $a){
            $team = $a->getTeam();
            if($team && !$team->hasPermission(user(), 'posts.view')){
                return false;
            }
            return true;
        });

        $showAllDraftsForAccounts = $allAccounts->filter(function(Account $a){
            $team = $a->getTeam();
            if($team && !$team->hasPermission(user(), 'posts.view_drafts')){
                return false;
            }
            return true;
        });

        $showAllApprovalsForAccounts = $allAccounts->filter(function(Account $a){
            $team = $a->getTeam();
            if($team && !$team->hasPermission(user(), 'approvals.approve')){
                return false;
            }
            return true;
        });

        /** @var $query Builder */
        $query  = Post::query();

        // we will filter by account ids once in the query (for performance reasons: specifying account ids multiple times in the query is slow because index is not used correctly)
        // account ids where I can see posts
        $accountIdsToFilterBy = $showAllPostsForAccounts->map(function($a){
            return $a->id;
        })->toArray();

        if($postType === 'draft'){
            // for draft, user needs to be able to see posts if user is author else only drafts where user has permission to see drafts
            // here we only include drafts where user has permission to see drafts
            // later in code, we will also include drafts where user is author in the query
            $accountIdsToFilterBy = $showAllDraftsForAccounts->map(function($a){
                return $a->id;
            })->toArray();
        } else {
            // non-draft posts, so we also need to include posts requiring approval
            $accountIdsToFilterBy = array_merge($accountIdsToFilterBy, $showAllApprovalsForAccounts->map(function($a){
                return $a->id;
            })->toArray());
        }

        // remove duplicate account ids if any
        $accountIdsToFilterBy = array_unique($accountIdsToFilterBy);

        $query
            ->when($request->input('user'), function (/** @var $query Builder */ $query, $userId){
                return $query->where('user_id', $userId);
            })
            ->when($request->input('source'), function (/** @var $query Builder */ $query, $source){
                return $query->where('source', $source);
            })
            ->when($request->input('q'), function (/** @var $query Builder */ $query, $keyword){
                return $query->where('content', 'like', '%' . $keyword . '%');
            })
            ->when($start, function(/** @var $query Builder */ $query, $val){
                return $query->where('publish_at', '>=', $val);
            })
            ->when($end, function(/** @var $query Builder */ $query, $val){
                return $query->where('publish_at', '<=', $val);
            })
            ->when($postType === 'published', function(/** @var $query Builder */ $query){
                return $query->where('published_at', '<>', NULL)->orderByDesc('published_at');
            })
            ->when($postType !== 'published', function(/** @var $query Builder */ $query){ // only pending posts
                return $query->where('published_at', NULL)->orderBy('publish_at');
            })
            ->when($postType === 'awaiting_approval', function(/** @var $query Builder */ $query){ // only requiring approval
                return $query->where('approved', false);
            })
            ->when($postType === 'scheduled', function(/** @var $query Builder */ $query) {
                return $query
                    ->where('approved', true) // scheduled posts dont require approval
                    ->where(function(/** @var $query Builder */ $query){
                        return $query
                            ->where(function(/** @var $query Builder */ $query){
                                return $query
                                    ->where('has_result', true) // post has error
                                    ->where('publish_at', '>', now()->subMonth(6)); // in case of error, we will discard post after 6 months (also for performance reasons)
                            })
                            ->orWhere('publish_at', '>', Carbon::now()); // or is not published yet
                    });
            })
            ->when($postType === 'scheduled_or_awaiting_approval', function(/** @var $query Builder */ $query) use($postType, $showAllApprovalsForAccounts){ // default: all scheduled posts and posts with errors
                return $query
                    ->where(function(/** @var $query Builder */ $query) use($showAllApprovalsForAccounts){
                        return $query
                            ->where(function(/** @var $query Builder */ $query){
                                return $query
                                    ->where(function(/** @var $query Builder */ $query){
                                        return $query
                                            ->where('has_result', true) // post has error
                                            ->where('publish_at', '>', now()->subMonth(6)); // in case of error, we will discard post after 6 months (also for performance reasons)
                                    })
                                    ->orWhere('publish_at', '>', Carbon::now()); // or is not published yet
                            })
                            ->orWhere('approved', false);
                    });
            })
            ->when($postType === 'draft', function(/** @var $query Builder */ $query) use($accountIdsToFilterBy, $relevantAccountIds) {
                // only drafts
                return $query
                    ->where('draft', true)
                    ->where(function(/** @var $query Builder */ $query) use($accountIdsToFilterBy, $relevantAccountIds){
                        return $query
                            // where user has permission
                            ->whereIn('account_id', $accountIdsToFilterBy)
                            // or where user is the author AND account_id is in $relevantAccountIds
                            ->orWhere(function(/** @var $query Builder */ $query) use($relevantAccountIds){
                                return $query
                                    ->where('user_id', user()->id)
                                    ->whereIn('account_id', $relevantAccountIds);
                            });
                    });
            })->when($postType !== 'draft', function(/** @var $query Builder */ $query) use($accountIdsToFilterBy) {
                // non-draft posts including scheduled and awaiting approval
                return $query
                    ->where('draft', false)
                    ->whereIn('account_id', $accountIdsToFilterBy);
            })
            ->with('user', 'account');

        if($request->input('dump')) {
            $query->dd();
        }

        if($getAll){
            return $query->get();
        }

        /** @var Post[]|LengthAwarePaginator $items */
        $items = $query->paginate($perPage, ['*'], 'page', $pageNo);

        if($returnPaginated){
            return $items;
        }

        // get all post ids
        $postIds = $items->pluck('id')->toArray();

        // fetch metrics at once for all posts
        $metrics = null;
        try {
            $metrics = Post::getMetricsForPosts($postIds);
        } catch (\Exception $e){
            \Log::error('Error fetching metrics for posts: ' . $e->getMessage());
        }

        $data = [];
        foreach ($items as $itm) {
            $data[] = Post::transform($itm, $metrics);
        }

        return collect([
            'items' => $data,
            'currentPage' => $items->currentPage(),
            'lastPage' => $items->lastPage(),
            'nextPage' => $items->hasMorePages() ? $items->currentPage() + 1 : null,
            'total' => $items->total(),
        ]);
    }
}
