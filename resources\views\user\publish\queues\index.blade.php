@extends('layout.user')
@section('title', 'Queues | ' . config('app.name'))
@section('content')
    <div class="row">
        <div class="col-12 py-md-0 py-20">
            <h3 class="d-flex align-items-center justify-content-between mb-md-4 mb-0">
                Queues
                <button class="btn btn-light btn-sm float-right" type="button"
                    data-toggle="modal" data-target="#new_queue">
                    <div class="d-flex align-items-center">
                        <i class="ph-bold ph-plus mr-2" title="New Queue"></i> New Queue
                    </div>
                </button>
            </h3>
            <div id="new_queue" class="modal text-left" role="dialog">
                <div class="modal-dialog">
                    <div class="modal-content rounded-xl">
                        <div class="modal-header p-6">
                            <h5 class="modal-title">New Queue</h5>
                            <button type="button" class="close-button" data-dismiss="modal"><i class="ph ph-x ph-md"></i></button>
                        </div>
                        <div class="modal-body">
                            <form id="new_queue_form" action="" method="POST">
                                {{ csrf_field() }}
                                <div>
                                    <div class="form-group mb-5" data-tour="id=createqueue_name;text=Enter a name (something meaningful) for your new queue">
                                        <label>Queue Name</label>
                                        <input name="name" class="form-control input-lg" title="Name" placeholder="Enter Your Queue name" />
                                    </div>

                                    @if(count(user()->joinedTeams) > 0)
                                        <div class="form-group mb-6" data-tour="id=createqueue_team;text=You can also assign this queue to a team if you want. All team members will have access to the queue.">
                                            <label>
                                                Assign to a team (optional)
                                            </label>
                                            <select class="form-control" name="team_id" title="Choose a team if you want to assign this queue to a team">
                                                <option value="">---</option>
                                                @foreach(user()->joinedTeams as $team)
                                                    <option value="{{ $team->id }}">
                                                        {{ $team->name }}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                    @endif
                                </div>

                                <div class="form-group text-right overflow-hidden pt-2 mb-6">
                                    <button type="button" class="btn btn-light btn-lg float-left " data-dismiss="modal">Cancel</button>
                                    <button type="submit" class="btn btn-primary btn-lg float-right ">Next</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12">
            @if(empty($queues) || $queues->count() == 0)
                <div class="card pt-6">
                    <div class="card-body">
                        <div class="text-center">
                            <h4 class="font-weight-400">Looks like you have no queue setup yet.</h4>
                            <p class="mb-5 container w-60">
                                You can set up publishing queue to automate publishing and recycling posts according to your custom schedule. Try creating a new queue.
                            </p>
                            <button class="btn btn-primary btn-sm" type="button" data-toggle="modal" data-target="#new_queue">
                                <div class="d-flex align-items-center">
                                    <i class="ph-bold ph-plus mr-2"></i> Create A Queue
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            @else
                
                @foreach($queues as $queue)
                    <div class="card border mb-3">
                        <a href="{{ route('publish.queues.show', ['id' => $queue->id]) }}" class="card-body p-20">
                            <div class="d-flex w-100 justify-content-between align-items-center">
                                <h5 class="font-weight-500 mb-3">{{ $queue->name  }}</h5>
                                <small class="text-muted">
                                    @if($queue->team_id > 0 && $queue->team)
                                        <i class="ph ph-users" title="Created by {{ explode(' ', $queue->user->name)[0] }} for {{ $queue->team->name }}" data-toggle="tooltip"></i>
                                    @endif
                                    &nbsp;
                                    <i class="ph ph-fill ph-circle {{ $queue->active ? 'text-success' : 'text-secondary' }}"></i>
                                </small>
                            </div>

                                @php($accounts = $queue->accounts())
                                @php($count = $queue->items()->count())
                                <p class="mb-0 text-secondary">
                                    {{ $count }}
                                    @if($count > 1)
                                        posts
                                    @else
                                        post
                                    @endif
                                    |
                                    {{ $accounts->count() }}
                                    @if($accounts->count() > 1)
                                        accounts
                                    @else
                                        account
                                    @endif
                                    @if($queue->findNextPublishTime() && $queue->findNextPublishTime() > now() && $queue->findNextQueueItemId())
                                        |
                                        next post in
                                        {{ $queue->findNextPublishTime()->diffForHumans() }}
                                    @endif
                                </p>
                        </a>
                    </div>
                @endforeach
            @endif
        </div>
    </div>
@endsection
