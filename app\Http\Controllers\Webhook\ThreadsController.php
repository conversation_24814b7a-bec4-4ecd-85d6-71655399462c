<?php

namespace App\Http\Controllers\Webhook;

use App\Account;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ThreadsController extends Controller
{
    public function delete(Request $request)
    {
        // Facebook sends deletion requests as a POST request with the following parameters
        // signed_request
        $signedRequest = $request->input('signed_request');

        // Decode the signed request
        $data = $this->parseSignedRequest($signedRequest);

        $userId = $data['user_id'];

        // disable account with this account_id
        $acc = Account::where('account_id', $userId)->first();
        try {
            $acc->setActive(false);
        } catch (\Exception $e) {
            \Log::error($e->getMessage());
            report($e);
        }

        return response()->json([
            'url' => url('/app/accounts'),
            'confirmation_code' => 'D' . $acc ? $acc->id : '0',
        ]);
    }

    public function uninstalled(Request $request)
    {
        return $this->delete($request); // for now
    }

    private function parseSignedRequest($signedRequest)
    {
        list($encoded_sig, $payload) = explode('.', $signedRequest, 2);

        $secret = config('services.threads.client_secret'); // Use your app secret here

        // decode the data
        $sig = base64_url_decode($encoded_sig);
        $data = json_decode(base64_url_decode($payload), true);

        // confirm the signature
        $expected_sig = hash_hmac('sha256', $payload, $secret, $raw = true);
        if ($sig !== $expected_sig) {
            \Log::info('Bad Signed JSON signature!');
            return null;
        }

        return $data;
    }
}
