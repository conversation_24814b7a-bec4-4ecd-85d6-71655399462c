<template>
    <div :class="'link-container card cursor-pointer ' + linkClass" @click="openLink" v-if="url">
        <div class="bg-white card-img-top link-img border-bottom-1"
             :style="'background-image: url(\'' + image + '\');'"
             v-if="image"
        ></div>
        <img class="thread-image" :src="image" alt="image" v-if="image && type === 'thread'">
        <div class="card-body pt-1 pb-2 pl-2">
            <div class="d-flex align-items-center justify-content-between" :class="{'mastodon-link-card' : type === 'mastodon' && !image}">

                <img class="linkedin-image" :src="image" alt="Linkedin image" v-if="type === 'linkedin'">
                <div>
                    <p class="link-website website" v-if="['bluesky', 'facebook','thread'].includes(type)">
                        {{ truncate(website || "Loading...", { length: 50 }) }}
                    </p>
                    <h5 class="card-title mb-0 link-title">
                        {{ truncate(title || website || url, { length: type === 'facebook' ? 50 : 40 }) }}
                    </h5>
                    <p class="card-text link-description mb-0" v-if="description">
                        {{ truncate(description || "Description will show here...", { length: 255 }) }}
                    </p>
                    <p :class="{'d-none' : type === 'mastodon' && !image }" class="mb-0 mt-1 text-muted link-website" v-if="!['bluesky', 'facebook','thread'].includes(type)">
                        <span>
                            {{ truncate(website || "Loading...", { length: 50 }) }}
                        </span>
                    </p>
                </div>
                <div class="mastodon-link-document rounded" v-if="type === 'mastodon' && !image">
                    <i class="ph ph-article"></i>
                </div>
            </div>
            <p class="link-website mb-0" v-if="type === 'mastodon' && !image">
                <span>
                    {{ truncate(website || "Loading...", { length: 50 }) }}
                </span>
            </p>
        </div>
    </div>
</template>

<script>
import _ from "lodash";
import { axios } from "../../../components";
export default {
    name: "LinkPreview",
    props: ["type", "url", 'linkTitle', 'linkImage', 'linkDescription',],
    data() {
        return {
            loading: true,

            title: null,
            description: null, // optional
            website: null,
            image: null // optional
        };
    },
    computed: {
        linkClass() {
            return (this.type || "border rounded default") + "-link";
        }
    },
    methods: {
        truncate: _.truncate,
        openLink() {
            if (!this.url) return;
            window.open(this.url, "_blank");
        },
        loadDetails: _.debounce(
            async function() {
                if (!this.url) return;

                const a = document.createElement("a");
                a.href = this.url;
                this.website = a.hostname;

                // user's provided custom title and thumbnail (esp for LinkedIn)
                if(this.linkTitle) {
                    this.title = this.linkTitle
                }
                if(this.linkDescription){
                    this.description = this.linkDescription;
                }
                if(this.linkImage) {
                    this.image = this.linkImage;
                }

                console.log('need to render url', this.url);

                if(!this.title || !this.image || !this.description){
                    // get title, description, and image
                    try {
                        let details;
                        if (window.__SB_LINK_CACHE[this.url]) {
                            details = window.__SB_LINK_CACHE[this.url];
                        } else {
                            const { data } = await axios.get("/app/json/search/url_data", {
                                params: {
                                    q: this.url
                                }
                            });
                            details = data.items[0];
                            window.__SB_LINK_CACHE[this.url] = details;
                        }

                        const getSanitizedStr = text => {
                            const div = document.createElement("div");
                            div.innerHTML = text;
                            return div.innerText;
                        };

                        if(!this.title){
                            this.title = getSanitizedStr(details.title);
                        }
                        if(!this.description){
                            this.description = getSanitizedStr(details.description);
                        }
                        if(!this.image){
                            this.image = getSanitizedStr(details.image);
                        }
                    } catch (e) {
                        console.log("Couldn't load url data for rendering... " + e.message);
                    }
                }

                this.loading = false;
            },
            400,
            { leading: true }
        )
    },
    watch: {
        url() {
            this.loadDetails();
        }
    },
    mounted() {
        window.__SB_LINK_CACHE = window.__SB_LINK_CACHE || {};
        this.loadDetails();
    }
};
</script>

<style lang="scss" scoped>
.link-container {
    overflow: hidden;
    font-family: Roboto, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Ubuntu, "Helvetica Neue", sans-serif;

}
.link-img {
    width: 100%;
    height: 300px;
    background-repeat: no-repeat;
    background-position: 50% 50%;
    background-size: cover;
    border-radius: 0;
}
.default-link {
    background-color: #F1F3F5;
    .link-title {
        color: #252A32;
        font-size: 16px;
        font-weight: 400;
    }
}
.twitter-link {
    font-family: Roboto, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Ubuntu, "Helvetica Neue", sans-serif;
    border: 1px solid #D6D7DB;
    border-radius: 14px;
    line-height: 24.3px;
    .card-body{
        padding: 8px 12px 8px 10px !important;
    }
    .link-img {
        border-bottom: 0.5px solid #D6D7DB !important;
    }
    .link-title {
        color: #101318 !important;
        font-size: 20px !important;
        font-weight: 400 !important;
        line-height: 28px !important;
    }
    .link-description{
        color: #101318;
        line-height: 21px !important;
    }
    .link-website {
        color: #526470 !important;
    }
}
.linkedin-link {

    border-radius: 0 !important;
    background-color: #fff;
    border: 1px solid #E8E8E8;
    margin: 8px 24px 0px 14px !important;
    border-radius: 7px !important;
    .card-body{
        padding: 14px !important;
        .linkedin-image{
            width: 151px;
            height: 85px;
            margin-right: 14px;
            border-radius: 9px;
        }
    }

    .link-img {
        border-radius: 9px !important;
        display: none;
    }
    .link-title {
        color: #191919 !important;
        font-size: 16px !important;
        font-weight: 500 !important;
        line-height: 19.2px;
    }
    .link-description {
        display: none;
    }
    .link-website {
        color: #666666 !important;
        font-size: 14px;
        line-height: 16.8px;
    }
}
.facebook-link {
    font-family: Roboto, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Ubuntu, "Helvetica Neue", sans-serif;
    border-radius: 0 !important;
    border-bottom: 1px solid #F2F4F7;
    .card-img-top{
        border-bottom: none !important;
    }
    .link-title {
        color: #080808;
        font-size: 16px !important;
        font-weight: 600 !important;
    }
    .link-img{
        margin-top: 8px;
    }
    .card-body{
        padding: 8px 24px 9px 12px !important;
    }
    .link-description {
        display: none;
    }
    .link-website {
        color: #63686B !important;
        font-weight: 400 !important;
        font-size: 16px !important;
        margin-bottom: 1px;
    }
}
.thread-link{
    border-radius: 10px;
    border: 1px solid #CCC;
    margin-top: 6px;

    .timestamp{
        margin-left: 6px;
    }
    .link-img{
        display: none;
    }
    .thread-image{
        width: 100%;
        height: 178px;
        object-fit: contain;
    }
    .card-body{
        padding: 16px 20px !important;
        background: white;
        .link-title{
            color: #000000;
            line-height: 21.25px !important;
            font-size: 17px;
            font-weight: 400 !important;
        }
        .link-description{
            display: none;
        }
        .website{
            color: #999999 !important;
            line-height: 21px !important;
            font-size: 15px;
            margin-bottom: 0px;
        }
    }
}
.mastodon-link{
    margin: 8px 20px 0px 18px !important;
    border: 1px solid #D9D9E8;
    border-radius: 10px !important;
    .link-img{
        border-bottom: none;
    }
    .card-body{
        padding: 0px !important;
        .link-title{
            font-weight: 500;
            line-height: 21.6px !important;
            color:#000000 !important;
            font-size: 18px;
            padding: 20px 18px 0px 18px !important;
        }
        .link-description{
            margin-top: 4px;
            color: #000000;
            line-height: 19.2px;
            padding: 0px 18px 12px 18px !important;
        }
        .link-website{
            border-top: 1px solid #D9D9E8;            
            padding: 12px 18px !important;
            color: #000000 !important;
            line-height: 19.2px !important;
        }
        .mastodon-link-card{
            padding: 16px 16px 14px 18px !important;
            .link-title{
                padding: 0px !important;
                color: #000;
                font-size: 18px;
                line-height: 21.6px !important
            }
            .mastodon-link-document{
                padding: 28px;
                background: #C6BFD9;

                i{
                    color:#292938;
                    font-size:56px
                }
            }
            .link-website{
                padding: 14px 18px;
            }
        }
    }
}
</style>
