@extends('layout.full_width')
@section('title', $page['title']  . ' | ' . config('app.name'))
@push('head_html')

    <meta name="description" content="{{ $page['description'] }}"/>

    <meta property="og:locale" content="en_US" />
    <meta property="og:type" content="website" />
    <meta property="og:title" content="{{ $page['title'] }}" />
    <meta property="og:description" content="{{ $page['description'] }}" />
    <meta property="og:url" content="https://socialbu.com/compare/{{ $page['slug'] }}" />
    <meta property="og:site_name" content="SocialBu" />

    <meta property="og:image" content="{{ $page['image'] }}" />

    <meta property="fb:app_id" content="{{ config('services.facebook.client_id') }}" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content="{{ $page['image'] }}" />
    <meta name="twitter:title" content="{{ $page['title'] }}" />
    <meta name="twitter:description" content="{{ $page['description'] }}" />
    <meta name="twitter:site" content="@socialbuapp" />

    <link rel="canonical" href="https://socialbu.com/compare/{{ $page['slug'] }}" />
    <style>
        .sticky-top {
            position: sticky;
            top: 0;
            z-index: 1000;
        }
         /* we need to apply overflow:clip for only large devices */
        @media (min-width: 750px){
            .table-comparison{
                overflow: clip;
            }
        }
    </style>
@endpush
@section('content')
    <header class="header header-main">
        <div class="container">
            <div class="row">
                <h1 class="display-1 text-center pb-4 pb-md-8 mb-0">
                    {{ $page['title'] }}
                </h1>
                <div class="col-12 d-md-flex" >
                    <div class="col-12 col-md-6 position-ralative mt-6 mt-md-0" style="height: 300px">
                        <div class="bg-light text-primary position-absolute font-weight-bold p-4" style="top: 43%; right: 45%; border-radius: 50%">
                            VS
                        </div>
                        <div class="bg-primary h-50 d-flex justify-content-center align-items-center" style="border-top-left-radius: 24px !important; border-top-right-radius: 24px">
                            <img 
                                data-toggle="tooltip" 
                                class="img-responsive  avatar-lg lozad d-md-none first {{ strtolower($tool2['logo_category'])}}" 
                                style="width: {{ strtolower($tool2['logo_category']) == 'square' ? '90px' : '140px' }}; height: auto; max-width: none;" 
                                alt="{{ $tool2['name'] }}" 
                                title="{{ $tool2['name'] }}" 
                                src="/images/1x1.gif" 
                                data-src="{{ $tool2['sb_white_logo'] }}" 
                            />
                            
                            <img 
                                data-toggle="tooltip" 
                                class="img-responsive  avatar-lg lozad d-none d-md-block second" 
                                style="width: {{ strtolower($tool2['logo_category']) === 'square' ? '90px' : '160px' }}; height: auto; max-width: none;" 
                                alt="{{ $tool2['name'] }}" 
                                title="{{ $tool2['name'] }}" 
                                src="/images/1x1.gif" 
                                data-src="{{ $tool2['sb_white_logo'] }}" 
                            />
                        </div>
                        <div class="bg-white h-50 d-flex justify-content-center align-items-center" style="border-bottom-left-radius: 24px !important; border-bottom-right-radius: 24px">
                            <img 
                                data-toggle="tooltip" 
                                class="img-responsive  avatar-lg lozad d-md-none third" 
                                style="width: {{ strtolower($tool1['logo_category']) === 'square' ? '120px' : '140px' }}; height: auto; max-width: none;" 
                                alt="{{ $tool1['name'] }}" 
                                title="{{ $tool1['name'] }}" 
                                src="/images/1x1.gif" 
                                data-src="{{ $tool1['logo'] }}" 
                            />
                            
                            <img 
                                data-toggle="tooltip" 
                                class="img-responsive  avatar-lg lozad d-none d-md-block four" 
                                style="width: {{ strtolower($tool1['logo_category']) === 'square' ? '90px' : '160px' }}; height: auto; max-width: none;" 
                                alt="{{ $tool1['name'] }}" 
                                title="{{ $tool1['name'] }}" 
                                src="/images/1x1.gif" 
                                data-src="{{ $tool1['logo'] }}" 
                            />
                        </div>
                    </div>
                    <p class="lead mt-6 mt-md-0 lead-2 col-12 col-md-6 order-md-first">
                        {!! $page['detailed_description'] !!}
                    </p>
                    {{-- <img src="/images/1x1.gif" data-src="{{ $page['header_image'] }}" alt="{{ $page['title'] }}" class="img-responsive hover-move-up border rounded lozad" /> --}}
                </div>
            </div>
        </div>
    </header>
    <main class="main-content">
        <section class="section bg-light pt-4 pt-md-8 pb-0">
            <div class="container">
                <div class="row gap-y">
                    <div class="container pb-3 text-center">
                        <h2 class="display-3">Quick Comparison: Which is better fit?</h2>
                    </div>
                    <div class="col-12">
                        <div class="card bg-white">
                            <div class="card-body p-2">
                                <div class="table-responsive table-comparison">
                                    <table class="table table-hover">
                                        <thead class="thead-inverse sticky-top bg-white">
                                            <tr>
                                                <th class="w-50"></th>
                                                <th class="text-center">
                                                    <img 
                                                        data-toggle="tooltip" 
                                                        class="img-responsive  avatar-lg lozad d-md-none" 
                                                        style="width: {{ strtolower($tool1['logo_category']) === 'square' ? '40px' : '90px' }}; height: auto; max-width: none;" 
                                                        alt="{{ $tool1['name'] }}" 
                                                        title="{{ $tool1['name'] }}" 
                                                        src="/images/1x1.gif" 
                                                        data-src="{{ $tool1['logo'] }}" 
                                                    />
                                                    
                                                    <img 
                                                        data-toggle="tooltip" 
                                                        class="img-responsive  avatar-lg lozad d-none d-md-block" 
                                                        style="width: {{ strtolower($tool1['logo_category']) === 'square' ? '70px' : '160px' }}; height: auto; max-width: none;" 
                                                        alt="{{ $tool1['name'] }}" 
                                                        title="{{ $tool1['name'] }}" 
                                                        src="/images/1x1.gif" 
                                                        data-src="{{ $tool1['logo'] }}" 
                                                    />
                                                </th>
                                                <th class="text-center d-flex justify-content-center">
                                                    
                                                    <img 
                                                        data-toggle="tooltip" 
                                                        class="img-responsive  avatar-lg lozad d-md-none" 
                                                        style="width: {{ strtolower($tool2['logo_category']) === 'square' ? '40px' : '90px' }}; height: auto; max-width: none;" 
                                                        alt="{{ $tool2['name'] }}" 
                                                        title="{{ $tool2['name'] }}" 
                                                        src="/images/1x1.gif" 
                                                        data-src="{{ $tool2['logo'] }}" 
                                                    />
                                                    
                                                    <img 
                                                        data-toggle="tooltip" 
                                                        class="img-responsive  avatar-lg lozad d-none d-md-block" 
                                                        style="width: {{ strtolower($tool2['logo_category']) === 'square' ? '70px' : '160px' }}; height: auto; max-width: none;" 
                                                        alt="{{ $tool2['name'] }}" 
                                                        title="{{ $tool2['name'] }}" 
                                                        src="/images/1x1.gif" 
                                                        data-src="{{ $tool2['logo'] }}" 
                                                    />
                                                </th>
                                            </tr>
                                            
                                        </thead>
                                    <tbody>
                                        <tr class="sticky-top bg-light" style="top: 70px">
                                            <th class="w-25 strong border-none">
                                                <h5 class="m-0">Supported Social networks</h5>
                                            </th>
                                            <th class="border-none"> </th>
                                            <th class="border-none"></th>
                                        </tr>
                                        @foreach ($socialbu_networks as $network)
                                            <tr>
                                                <td class="w-25  strong"><b>{{ $network }} </b></td>
                                                <td class="text-center">
                                                    @if(in_array($network, explode(',', $tool1['networks'])))
                                                        <i class="ph-fill ph-lg ph-check-circle text-primary pr-2"></i> 
                                                    @else
                                                        <i class="ph-fill ph-lg ph-x-circle pr-2 text-muted "></i> 
                                                    @endif
                                                </td>
                                                <td class="text-center">
                                                    @if (in_array($network, explode(',', $tool2['networks'])))
                                                        <i class="ph-fill ph-lg ph-check-circle text-primary pr-2"></i> 
                                                    @else
                                                        <i class="ph-fill ph-lg ph-x-circle pr-2 text-muted "></i> 
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                            <tr class="sticky-top bg-light" style="top: 70px">
                                                <th class="w-25 strong text-bold border-none">
                                                    <h5 class="m-0">Other Features</h5>
                                                </th>
                                                <th class="border-none"> </th>
                                                <th class="border-none"></th>
                                            </tr>
                                            <tr>
                                                <td class="bold  strong"><b>Post Scheduling</b></td>
                                                <td class="text-center">
                                                    <i class="ph-fill ph-lg ph-check-circle text-primary pr-2"></i>
                                                </td>
                                                <td class="text-center">
                                                    <i class="ph-fill ph-lg ph-check-circle text-primary pr-2"></i> 
                                                </td>
                                            </tr>
                                            @foreach(['social_calendar', 'content_approvals', 'bulk_import', 'post_preview', 'ai_content_generator', 'post_recycling', 'twitter_thread_scheduling', 'instagram_story_scheduling', 'instagram_first_comment', 'auto_post_from_rss_feeds',  'social_inbox', 'top_performing_posts',] as $key)
                                            <tr>
                                                    <td class=" strong"><b>
                                                        {{ str_replace(['Ai'], ['AI'], ucwords(str_replace('_', ' ', $key))) }}
                                                    </b>
                                                    </td>
                                                    <td class="text-center strong">
                                                        @if(isset($tool1[$key]) && $tool1[$key])
                                                            <i class="ph-fill ph-lg ph-check-circle text-primary pr-2"></i> 
                                                        @else
                                                            <i class="ph-fill ph-lg ph-x-circle pr-2 text-muted "></i> 
                                                        @endif
                                                    </td>
                                                    <td class="text-center">
                                                        @if(isset($tool2[$key]) && $tool2[$key])
                                                            <i class="ph-fill ph-lg ph-check-circle text-primary pr-2"></i> 
                                                        @else
                                                            <i class="ph-fill ph-lg ph-x-circle pr-2 text-muted "></i> 
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                            <tr>
                                                <td class=" strong"><b>Live Support</b></td>
                                                <td class="text-center">
                                                    @if($tool1['live_chat'])
                                                        <i class="ph-fill ph-lg  ph-check-circle text-primary pr-2"></i> 
                                                    @else
                                                        <i class="ph-fill ph-lg ph-x-circle pr-2 text-muted "></i> 
                                                    @endif
                                                </td>
                                                <td class="text-center">

                                                    @if($tool2['live_chat'])
                                                        <i class="ph-fill ph-lg ph-check-circle text-primary pr-2"></i> 
                                                    @else
                                                        <i class="ph-fill ph-lg ph-x-circle pr-2 text-muted "></i> 
                                                    @endif
                                                </td>
                                            </tr>
                                            <tr>
                                                <td class="w-50  strong"><b>Pricing Plan</b></td>
                                                <td>
                                                    {{ $tool1['starting_price'] }}
                                                </td>
                                                <td>
                                                    {{ $tool2['starting_price'] }}
                                                </td>
                                            </tr>
                                    </tbody>
                                        
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 pt-8 pb-0">
                        <div class="card border p-40 shadow">
                            <div class="card-body p-0">
                                <div class="row">
                                    <div class="col-12 col-md-8 d-flex align-items-center">
                                        <div class="ml-4 text-center text-md-left">
                                            <h2 class="display-3" style="color: #1E283B;">
                                                Stop worrying!
                                            </h2>
                                            <p class="lead-2 w-100">
                                                Try SocialBu for free. You won't regret it.
                                            </p>
                                        </div>
                                    </div>
                                    <div class="col-12 col-md-4 d-flex flex-column align-items-center justify-content-end">
                                        <a class="btn btn-primary mb-4 w-100" href="/auth/register">
                                            Start for free <i class="ph ph-arrow-right ph-md ml-2" aria-hidden="true"></i>
                                        </a>    
                                        <a class="btn btn-light w-100" data-toggle="modal" data-target="#demo_modal">
                                            Book a Demo
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 pt-8 pb-0">
                        @if(!empty($tool2['socialbu_key_features']))
                            <div class="card">
                                <div class="card-body strong">
                                    {!! str_replace(['<strong>', '<h3>','<p>'], ['<strong class="text-color">', '<h3 class="display-4">','<p class="">'], Illuminate\Mail\Markdown::parse($tool2['socialbu_key_features']) ) !!}
                                </div>
                            </div>
                            <br/><br/>
                        @endif
                    </div>                    

                </div>
            </div>
        </section>

        <section class="section pt-3">
            <div class="container pb-3">
                <h2 class="display-3">Dive into the Details:</h2>
            </div>            
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        @if(count($dynamic_data) > 0)
                            <div class="card-columns">
                                @foreach($dynamic_data as $c)
                                    <div class="card shadow">
                                        <div class="card-body">
                                            <h3 class="strong card-title">
                                                {{ is_array($c['title']) ? \Illuminate\Support\Arr::random($c['title']) : $c['title'] }}
                                            </h3>
                                            <p class="card-text ">
                                                {{ is_array($c['content']) ? \Illuminate\Support\Arr::random($c['content']) : $c['content'] }}
                                            </p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                        @if(isset($page['heading1']))
                        <div class="card border">
                            <div class="card-body">
                                @foreach(range(1, 5) as $n)
                                    @if(isset($page['heading' . $n]))
                                        <section class="section py-7 overflow-hidden">
                                            <div class="container">
                                                <div class="row gap-y align-items-center">
                                                    @if($n % 2 == 0)
                                                        <div class="col-md-6 mx-auto">
                                                            <h2 class="h4 font-weight-700">
                                                                {{ $page['heading' . $n] }}
                                                            </h2>
                                                            <p class="lead">
                                                                {{ $page['para' . $n] }}
                                                            </p>
                                                        </div>
                                                        @if(isset($page['image' . $n]) && !empty($page['image' . $n]))
                                                            <div class="col-md-6 order-md-first">
                                                                <img src="/images/1x1.gif" data-src="{{ $page['image' . $n] }}" alt="{{ $page['heading' . $n] }}" data-aos="fade-left" class="aos-init aos-animate lozad" />
                                                            </div>
                                                        @endif
                                                        @else
                                                        <div class="col-md-6 mx-auto">
                                                            <h2 class="h4 font-weight-700">
                                                                {{ $page['heading' . $n] }}
                                                            </h2>
                                                            <p class="lead">
                                                                {{ $page['para' . $n] }}
                                                            </p>
                                                        </div>
                                                        @if(isset($page['image' . $n]) && !empty($page['image' . $n]))
                                                            <div class="col-md-6">
                                                                <img src="/images/1x1.gif" data-src="{{ $page['image' . $n] }}" alt="{{ $page['heading' . $n] }}" data-aos="fade-left" class="aos-init aos-animate lozad" />
                                                            </div>
                                                        @endif
                                                    @endif
                                                </div>
                                            </div>
                                        </section>
                                    @endif
                                @endforeach
                            </div>
                        </div>
                    @endif
                    </div>
                </div>
            </div>
        </section>
        
        <section class="section py-7 bg-white p-0 p-md-3">
            <div class="container">
                <div class="d-flex justify-content-between flex-column flex-md-row">
                    <div class="card bg-light col-12 col-md-6 mr-md-2 mb-4 mb-md-0">
                        <div class="card-body">
                            <h4 class="mb-3 display-4 font-weight-800 ml-5 ml-md-0">Get 7 days for free</h4>
                            <p class="lead">
                                SocialBu gives you 7 days free trial. You can cancel anytime or downgrade to the free plan.
                            </p>
                            <div class="text-center text-md-left mt-4 w-100">
                                <a href="/auth/register" class="btn btn-primary w-100 w-md-auto" aria-label="Try for Free">
                                    Try for Free <i class="ph ph-arrow-right ph-md" aria-hidden="true"></i>
                                </a>
                            </div> 
                        </div>
                    </div>
                    <div class="card bg-light col-12 col-md-6">
                        <div class="card-body">
                            <h4 class="mb-3 display-4 font-weight-800 d-none d-md-block">Have questions? Let's talk.</h4>
                            <h4 class="mb-3 display-4 font-weight-800 d-block d-md-none ml-7">Have questions? <br>Let's talk.</h4>
                            <div class="d-flex flex-column align-items-center align-items-md-start w-100 mt-4">
                                <a class="btn btn-outline-primary w-100 w-md-auto mb-3" href="#" onclick="typeof Beacon !== 'undefined' && Beacon('open');return false;">Live Chat</a>
                                <a class="btn btn-outline-primary w-100 w-md-auto" href="mailto:<EMAIL>">Email</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        
        <div class="container mt-5 pt-5">
            <h2 class="text-center display-4 display-md-3">Frequently Asked Questions</h2>
            <p class=" text-center lead text-md-body">
                Got a question? We've got answers. If you have some other questions, contact us.
            </p>
        </div> 

        <section class="mt-4">
            <div class="d-flex justify-content-center">
                <div class="col-md-8 mt-5 pb-10">
                    <div class="container px-0">
                        @include('common.internal.tools-faq')
                    </div>
                </div>
            </div>
        </section>
    </main>
    
@endsection
