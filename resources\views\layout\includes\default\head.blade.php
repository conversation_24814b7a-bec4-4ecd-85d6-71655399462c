
<meta charset="utf-8" />
<meta name="csrf-token" content="{{ csrf_token() }}" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta name="viewport" content="width=device-width, initial-scale=1" />


<link rel="icon" href="/favicon.ico" />


<title>
    @yield('title')
</title>

<!-- critical css -->
{{-- https://www.corewebvitals.io/tools/critical-css-generator --}}
<style id="critical_css">
    h1,h3,h4{margin-bottom:.5rem;margin-top:0}h1,h3,h4{font-family:DM Sans,sans-serif;font-weight:600;line-height:1.4;margin-bottom:.5rem}.text-dark{color:#131519!important}h1,h3,h4{color:#252a32;letter-spacing:.5px}h3,h4{font-weight:700}.text-dark{color:#252a32!important}.feature-card{height:230px;width:335px}h1,h2,h3,h4,h5{margin-bottom:.5rem;margin-top:0}h1,h2,h3,h4,h5{font-family:DM Sans,sans-serif;font-weight:600;line-height:1.4;margin-bottom:.5rem}h3{font-size:1.75rem}.col-12,.col-6,.col-md-12,.col-md-4,.col-md-6,.col-md-8{padding-left:15px;padding-right:15px;position:relative;width:100%}.btn-outline-light{background-color:transparent;background-image:none;border-color:#f9fafc;color:#f9fafc}.btn-outline-light:not([disabled]):not(.disabled).active{background-color:#f9fafc;border-color:#f9fafc;box-shadow:0 0 0 0 rgba(249,250,252,.5);color:#465166}@media (max-width:1200px){h3{font-size:calc(1.3rem + .6vw)}}.carousel{position:relative}.carousel-inner{overflow:hidden;position:relative;width:100%}.carousel-inner:after{clear:both;content:"";display:block}.carousel-item.active{display:block}.carousel-control-next,.carousel-control-prev{align-items:center;background:0 0;border:0;bottom:0;color:#fff;display:flex;justify-content:center;opacity:.5;padding:0;position:absolute;text-align:center;top:0;width:15%;z-index:1}.carousel-control-prev{left:0}.carousel-control-next{right:0}.border-secondary{border-color:#d5e1f6!important}.rounded-lg{border-radius:.5rem!important}.align-items-end{align-items:flex-end!important}.position-absolute{position:absolute!important}.h-100{height:100%!important}.mr-0{margin-right:0!important}.mb-1{margin-bottom:.25rem!important}.mr-6,.mx-6{margin-right:2rem!important}.mx-6{margin-left:2rem!important}.mb-7{margin-bottom:3rem!important}.ml-7{margin-left:3rem!important}.mt-8{margin-top:5rem!important}.pb-1{padding-bottom:.25rem!important}.p-2{padding:.5rem!important}.p-3{padding:.75rem!important}.p-4{padding:1rem!important}.pt-4,.py-4{padding-top:1rem!important}.pb-4,.py-4{padding-bottom:1rem!important}.pr-5{padding-right:1.5rem!important}.px-6{padding-right:2rem!important}.px-6{padding-left:2rem!important}.pt-7{padding-top:3rem!important}.pl-7{padding-left:3rem!important}.pb-8{padding-bottom:5rem!important}.m-auto{margin:auto!important}.text-dark{color:#101929!important}h1,h2,h3,h4,h5{color:#1e283b;letter-spacing:.5px}h2,h3,h4{font-weight:700}h5{font-weight:600}.h-30px{height:30px!important}.text-white a:not(.btn):not(.dropdown-item){color:hsla(0,0%,100%,.8)}.text-white p{color:hsla(0,0%,100%,.85)}.btn-outline-light{border:1px solid #e4e7ed;color:#353f54}.btn-outline-light:not([disabled]):not(.disabled).active{background-color:#f2f4f7;border-color:#e4e7ed;box-shadow:none;color:#353f54}.rounded-lg{border-radius:.75rem!important}.border-secondary{border-color:#e3e6eb!important}.text-primary{color:#0557f0!important}.text-dark{color:#1e283b!important}.display-1,.display-2,.display-5{font-family:Plus Jakarta Sans}.text-white a:not(.btn):not(.dropdown-item){color:#fff}.text-white p{color:#fff}.display-1,.display-2,.display-5{font-feature-settings:"liga";line-height:1.2!important}.border-none{border:none!important}.ph-xl{font-size:40px}.feature-card{height:176px;width:335px}.feature-card .icon-component{height:72px;width:72px}.bg-success-light{background-color:#defad4}.bg-warning-light{background-color:#fef3cc}@media (max-width:450px){.products-nav-dropdown{width:350px}.header{padding-bottom:3.75rem;padding-top:3.75rem}}.carousel-control-prev{left:-66px}.carousel-control-next,.carousel-control-prev{height:44px!important;opacity:1!important;top:48%;width:44px!important}.carousel-control-next{right:-66px}.carousel-btn{background:#fff!important;border-color:#f1f3f5!important}.everything-section-button.active{background:#f9fafc!important}h4{font-weight:600!important}header,nav{display:block}h1,h4{margin-bottom:.5rem;margin-top:0}h1,h4{font-family:DM Sans,sans-serif;font-weight:600;line-height:1.4;margin-bottom:.5rem}.bg-light{background-color:#fafafa!important}h1,h4{color:#252a32;letter-spacing:.5px}h4{font-weight:700}.display-1,.display-5{font-family:Plus Jakarta Sans}@media (max-width:450px){.platforms-nav-dropdown{width:350px}.products-nav-dropdown{width:350px}.header{padding-top:5rem}.header{padding-bottom:3rem}}.btn{background-color:transparent;border:.0625rem solid transparent;border-radius:.5rem;color:#4a5465;display:inline-block;font-family:DM Sans;font-size:1rem;font-weight:500;line-height:1.5;padding:.75rem 1.255rem;text-align:center;vertical-align:middle}.dropdown-menu{background-clip:padding-box;background-color:#fff;border:.0625rem solid #e3e6eb;border-radius:.375rem;color:#4a5465;display:none;float:left;font-size:1rem;left:0;list-style:none;margin:.125rem 0 0;min-width:15rem;padding:.5rem 0;position:absolute;text-align:left;top:100%;z-index:1000}.navbar{padding:.5rem 1rem;position:relative}.dropdown-menu{border:none;box-shadow:0 2px 25px rgba(0,0,0,.07);font-size:14px;margin-top:5px;padding:10px 16px}.nav-link{color:#4a5465;font-weight:400}#overlay{background-color:rgba(0,0,0,.5);bottom:0;display:none;height:100%;left:0;position:fixed;right:0;top:0;width:100%;z-index:1048}#navbar_main .nav-link{color:#131519!important}#navbar_main .nav-link.border-top-hover{border-top:2px solid transparent}#navbar_main{z-index:1049}#navbar_main .dropdown-item{opacity:1!important}#navbar_main .dropdown-fullwidth{position:static}#navbar_main .dropdown-fullwidth .dropdown-menu{border-radius:0;max-height:70vh;overflow-y:auto;width:100%}@media (max-width:991.98px){#navbar_main .dropdown-fullwidth .dropdown-menu{max-height:unset}#navbar_main{max-height:98vh;overflow-y:auto}#navbar_main .nav-menu-mob{border:1px solid #e3e6eb}.navbar-expand-lg>.container{padding-left:0;padding-right:0}}#navbar_main #menu_content>ul{position:static}h2,h4{font-weight:600!important}p{font-weight:400!important}.nav-menu-mob{background:#fff;padding:20px}.display-4{font-weight:400}:root{--blue:#0557f0;--indigo:#6610f2;--purple:#6f42c1;--pink:#e83e8c;--red:#d82030;--orange:#fd7e14;--yellow:#d87e03;--green:#1d9033;--teal:#20c997;--cyan:#0286f9;--white:#fff;--gray:#717f96;--gray-dark:#252a32;--primary:#0557f0;--secondary:#d5e1f6;--success:#1d9033;--info:#0286f9;--warning:#d87e03;--danger:#d82030;--light:#fafafa;--dark:#131519;--breakpoint-xs:0;--breakpoint-sm:576px;--breakpoint-md:768px;--breakpoint-lg:992px;--breakpoint-xl:1200px;--font-family-sans-serif:"DM Sans","Nunito","proxima nova","helvetica neue",helvetica,sans-serif,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans","Liberation Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";--font-family-monospace:SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace}body{background-color:#fafafa;color:#4a5465;font-family:DM Sans,Nunito,proxima nova,helvetica neue,helvetica,sans-serif,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,Liberation Sans,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;font-size:1rem;font-weight:400;line-height:1.5;margin:0;text-align:left}h1,h2,h4{margin-bottom:.5rem;margin-top:0}button{font-family:inherit;font-size:inherit;line-height:inherit;margin:0}button{overflow:visible}button{text-transform:none}[type=button],button{-webkit-appearance:button}[type=button]::-moz-focus-inner,button::-moz-focus-inner{border-style:none;padding:0}h1,h2,h4{font-family:DM Sans,sans-serif;font-weight:600;line-height:1.4;margin-bottom:.5rem}@media (max-width:1200px){h4{font-size:calc(1.275rem + .3vw)}.display-1{font-size:calc(1.525rem + 3.3vw)}}@media (min-width:992px){.nav-menu-mob{background:0 0;padding:0}}.col-12,.col-md-6{padding-left:15px;padding-right:15px;position:relative;width:100%}@media (min-width:768px){.col-md-6{flex:0 0 50%;max-width:50%}}.btn{background-color:transparent;border:.0625rem solid transparent;border-radius:.5rem;color:#4a5465;display:inline-block;font-family:DM Sans;font-weight:500;padding:.75rem 1.255rem;text-align:center;vertical-align:middle}.btn-secondary{background-color:#d5e1f6;border-color:#d5e1f6;color:#4a5465}.dropdown-menu{background-clip:padding-box;background-color:#fff;border:.0625rem solid #e3e6eb;border-radius:.375rem;color:#4a5465;display:none;float:left;font-size:1rem;left:0;list-style:none;margin:.125rem 0 0;padding:.5rem 0;position:absolute;text-align:left;top:100%;z-index:1000}.dropdown-item{border:0;clear:both;color:#131519;display:block;font-weight:400;padding:.75rem 1.25rem;text-align:inherit;white-space:nowrap;width:100%}.navbar-light .navbar-nav .nav-link{color:#131519}.card{word-wrap:break-word;background-clip:border-box;background-color:#fff;border:.0625rem solid #e3e6eb;border-radius:.5rem;display:flex;flex-direction:column;min-width:0;position:relative}.card-body{color:#4a5465;flex:1 1 auto;min-height:1px;padding:2rem}.border{border:.0625rem solid #e3e6eb!important}.mt-1{margin-top:.25rem!important}.px-0{padding-right:0!important}@media (min-width:768px){.d-md-none{display:none!important}.d-md-block{display:block!important}.d-md-flex{display:flex!important}.flex-md-row{flex-direction:row!important}.m-md-0{margin:0!important}.mt-md-0{margin-top:0!important}.mx-md-2{margin-right:.5rem!important}.mx-md-2{margin-left:.5rem!important}}h1,h2,h4{color:#252a32;letter-spacing:.5px}.dropdown-item{background-color:transparent;color:#e3e6eb;opacity:.8;padding-left:0;padding-right:0}.nav-link{color:#4a5465}.navbar{left:0;min-height:50px;padding-bottom:0;padding-top:0;position:absolute;right:0;z-index:1000}.navbar-light .nav-navbar>.nav-item>.nav-link{color:#131519}@media (max-width:991.98px){.navbar-expand-lg .nav-navbar:not(.nav-inline){flex-direction:column;flex-wrap:nowrap;width:100%}.navbar-expand-lg .nav-navbar:not(.nav-inline)>.nav-item>.nav-link{font-size:.95rem;line-height:inherit;min-height:inherit;padding-bottom:.75rem;padding-top:.75rem;text-transform:none}.section{padding-bottom:5rem;padding-top:5rem}}.btn{border-radius:8px;font-size:1rem;letter-spacing:1.4px;line-height:1.5;padding:10px 18px;text-transform:none}.btn-secondary{color:#252a32}::-webkit-file-upload-button{background:#f1f3f5;border:1px solid #4a5465;border-radius:6px;padding:8px 16px}.display-1{letter-spacing:-1.28px}.display-1,.display-4,.display-5{font-family:Plus Jakarta Sans}.dropdown-item{border:none;border-radius:8px;color:#252a32;opacity:1;padding:12px 20px}.navbar{position:relative;top:10px}.navbar .nav-link{color:#252a32;font-weight:500}.header{padding-bottom:80px;padding-top:120px}@media (max-width:450px){.platforms-nav-dropdown{width:350px}.products-nav-dropdown{width:350px}.header{padding-top:5rem}.header,.section{padding-bottom:3rem}.section{padding-top:3rem}}:root{--blue:#0557f0;--indigo:#6610f2;--purple:#6f42c1;--pink:#e83e8c;--red:#d82030;--orange:#fd7e14;--yellow:#d87e03;--green:#1d9033;--teal:#20c997;--cyan:#0286f9;--white:#fff;--gray:#465166;--gray-dark:#1e283b;--primary:#0557f0;--secondary:#d5e1f6;--success:#1d9033;--info:#0286f9;--warning:#d87e03;--danger:#d82030;--light:#f9fafc;--dark:#101929;--breakpoint-xs:0;--breakpoint-sm:576px;--breakpoint-md:768px;--breakpoint-lg:992px;--breakpoint-xl:1200px;--font-family-sans-serif:"DM Sans","Nunito","proxima nova","helvetica neue",helvetica,sans-serif,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans","Liberation Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";--font-family-monospace:SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace}*,:after,:before{box-sizing:border-box}html{-webkit-text-size-adjust:100%;font-family:sans-serif;line-height:1.15}header,nav,section{display:block}body{background-color:#fff;color:#465166;font-family:DM Sans,Nunito,proxima nova,helvetica neue,helvetica,sans-serif,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,Liberation Sans,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;font-size:1rem;font-weight:400;line-height:1.5;margin:0;text-align:left}h1,h2,h4,h5,h6{margin-bottom:.5rem;margin-top:0}p{margin-bottom:1rem;margin-top:0}ul{margin-bottom:1rem}ul{margin-top:0}a{background-color:transparent;text-decoration:none}img{border-style:none}img{vertical-align:middle}label{display:inline-block;margin-bottom:1rem}button{border-radius:0}button,input,select,textarea{font-family:inherit;font-size:inherit;line-height:inherit;margin:0}button,input{overflow:visible}button,select{text-transform:none}select{word-wrap:normal}[type=button],[type=submit],button{-webkit-appearance:button}[type=button]::-moz-focus-inner,[type=submit]::-moz-focus-inner,button::-moz-focus-inner{border-style:none;padding:0}textarea{overflow:auto;resize:vertical}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}h1,h2,h4,h5,h6{font-family:DM Sans,sans-serif;font-weight:600;line-height:1.4;margin-bottom:.5rem}h1{font-size:2.75rem}@media (max-width:1200px){h1{font-size:calc(1.4rem + 1.8vw)}}h2{font-size:2.25rem}@media (max-width:1200px){h2{font-size:calc(1.35rem + 1.2vw)}}h4{font-size:1.5rem}@media (max-width:1200px){h4{font-size:calc(1.275rem + .3vw)}}h5{font-size:1.25rem}h6{font-size:1rem}.display-1{font-size:4rem;font-weight:800;line-height:1.4}@media (max-width:1200px){.display-1{font-size:calc(1.525rem + 3.3vw)}}.display-2{font-size:3rem;font-weight:800;line-height:1.4}@media (max-width:1200px){.display-2{font-size:calc(1.425rem + 2.1vw)}}.display-4{font-size:1.5rem;font-weight:700;line-height:1.4}@media (max-width:1200px){.display-4{font-size:calc(1.275rem + .3vw)}}.container,.container-fluid{margin-left:auto;margin-right:auto;padding-left:15px;padding-right:15px;width:100%}@media (min-width:576px){.container{max-width:540px}}@media (min-width:768px){.container{max-width:720px}}@media (min-width:992px){.container{max-width:960px}}@media (min-width:1200px){.container{max-width:1140px}}.row{display:flex;flex-wrap:wrap;margin-left:-15px;margin-right:-15px}.col-12,.col-6,.col-md-12,.col-md-6{padding-left:15px;padding-right:15px;position:relative;width:100%}.col-6{flex:0 0 50%;max-width:50%}.col-12{flex:0 0 100%;max-width:100%}@media (min-width:768px){.col-md-6{flex:0 0 50%;max-width:50%}.col-md-12{flex:0 0 100%;max-width:100%}}.form-control{background-clip:padding-box;background-color:#fff;border:.0625rem solid #d1d7e3;border-radius:.5rem;color:#465166;display:block;font-family:DM Sans;font-size:1rem;height:calc(1.5em + 1.625rem);line-height:1.5;padding:.75rem 1.255rem;width:100%}.form-control::-ms-expand{background-color:transparent;border:0}.form-control::-moz-placeholder{color:#98a1b2;opacity:1}.form-control:-ms-input-placeholder{color:#98a1b2;opacity:1}select.form-control:-moz-focusring{color:transparent;text-shadow:0 0 0 #465166}textarea.form-control{height:auto}.form-group{margin-bottom:1rem}.form-row{display:flex;flex-wrap:wrap;margin-left:-5px;margin-right:-5px}.form-row>[class*=col-]{padding-left:5px;padding-right:5px}.btn{background-color:transparent;border:.0625rem solid transparent;border-radius:.5rem;color:#465166;display:inline-block;font-family:DM Sans;font-weight:500;padding:.75rem 1.255rem;text-align:center;vertical-align:middle}.btn-primary{background-color:#0557f0;border-color:#0557f0;color:#fff}.btn-secondary{border-color:#d5e1f6;color:#465166}.btn-light{background-color:#f9fafc;border-color:#f9fafc;color:#465166}.btn-outline-primary{background-color:transparent;background-image:none;border-color:#0557f0;color:#0557f0}.btn-lg{border-radius:.5rem;font-size:1.25rem;line-height:1.5;padding:1rem 1.5rem}.btn-sm{border-radius:.35rem;font-size:.875rem;line-height:1.5;padding:.75rem 1rem}.collapse:not(.show){display:none}.dropdown{position:relative}.dropdown-toggle{white-space:nowrap}.dropdown-toggle:after{border-bottom:0;border-left:.3em solid transparent;border-right:.3em solid transparent;border-top:.3em solid;content:"";display:inline-block;margin-left:.255em;vertical-align:.255em}.dropdown-menu{background-clip:padding-box;background-color:#fff;border:.0625rem solid #e4e7ed;border-radius:.375rem;color:#465166;display:none;float:left;font-size:1rem;left:0;list-style:none;margin:.125rem 0 0;padding:.5rem 0;position:absolute;text-align:left;top:100%;z-index:1000}.dropdown-item{border:0;clear:both;color:#101929;display:block;font-weight:400;padding:.75rem 1.25rem;text-align:inherit;white-space:nowrap;width:100%}.nav{display:flex;flex-wrap:wrap;list-style:none;margin-bottom:0;padding-left:0}.nav-link{display:block;padding:.5rem 1rem}.navbar{padding:.5rem 1rem}.navbar,.navbar .container,.navbar .container-fluid{align-items:center;display:flex;flex-wrap:wrap;justify-content:space-between}.navbar-brand{display:inline-block;font-size:1.25rem;line-height:inherit;margin-right:1rem;padding-bottom:0;padding-top:0;white-space:nowrap}.navbar-nav{display:flex;flex-direction:column;list-style:none;margin-bottom:0;padding-left:0}.navbar-nav .nav-link{padding-left:0;padding-right:0}.navbar-nav .dropdown-menu{float:none;position:static}.navbar-collapse{align-items:center;flex-basis:100%;flex-grow:1}@media (max-width:991.98px){.navbar-expand-lg>.container{padding-left:0;padding-right:0}}@media (min-width:992px){.navbar-expand-lg{flex-flow:row nowrap;justify-content:flex-start}.navbar-expand-lg .navbar-nav{flex-direction:row}.navbar-expand-lg .navbar-nav .dropdown-menu{position:absolute}.navbar-expand-lg .navbar-nav .nav-link{padding-left:.5rem;padding-right:.5rem}.navbar-expand-lg>.container{flex-wrap:nowrap}.navbar-expand-lg .navbar-collapse{display:flex!important;flex-basis:auto}}.navbar-light .navbar-brand{color:#0557f0}.navbar-light .navbar-nav .nav-link{color:#101929}.card{word-wrap:break-word;background-clip:border-box;background-color:#fff;border:.0625rem solid #e4e7ed;border-radius:.5rem;display:flex;flex-direction:column;min-width:0;position:relative}.card-body{color:#465166;flex:1 1 auto;min-height:1px;padding:2rem}.card-title{margin-bottom:2rem}.card-header{background-color:rgba(0,0,0,.03);border-bottom:.0625rem solid #e4e7ed;margin-bottom:0;padding:2rem}.card-header:first-child{border-radius:.4375rem .4375rem 0 0}.accordion>.card{overflow:hidden}.accordion>.card:not(:last-of-type){border-bottom:0;border-bottom-left-radius:0;border-bottom-right-radius:0}.accordion>.card:not(:first-of-type){border-top-left-radius:0;border-top-right-radius:0}.accordion>.card>.card-header{border-radius:0;margin-bottom:-.0625rem}.close{color:#000;float:right;font-size:1.5rem;font-weight:700;line-height:1;opacity:.5;text-shadow:none}@media (max-width:1200px){.close{font-size:calc(1.275rem + .3vw)}}button.close{background-color:transparent;border:0;padding:0}.modal{display:none;height:100%;left:0;outline:0;overflow:hidden;position:fixed;top:0;width:100%;z-index:1050}.modal-dialog{margin:.5rem;position:relative;width:auto}.modal-content{background-clip:padding-box;background-color:#fff;border:.0625rem;border-radius:.5rem;display:flex;flex-direction:column;outline:0;position:relative;width:100%}.modal-header{align-items:flex-start;border-bottom:.0625rem;border-top-left-radius:.4375rem;border-top-right-radius:.4375rem;display:flex;justify-content:space-between;padding:2rem 2rem 1rem}.modal-header .close{margin:-2rem -2rem -2rem auto;padding:2rem 2rem 1rem}.modal-body{flex:1 1 auto;padding:2rem;position:relative}@media (min-width:576px){.modal-dialog{margin:1.75rem auto;max-width:540px}}.carousel-item{-webkit-backface-visibility:hidden;backface-visibility:hidden;display:none;float:left;margin-right:-100%;position:relative;width:100%}.bg-primary{background-color:#0557f0!important}.border{border:.0625rem solid #e4e7ed!important}.border-bottom{border-bottom:.0625rem solid #e4e7ed!important}.border-0{border:0!important}.border-light{border-color:#f9fafc!important}.d-none{display:none!important}.d-inline-block{display:inline-block!important}.d-block{display:block!important}.d-flex{display:flex!important}@media (min-width:768px){.d-md-none{display:none!important}.d-md-inline-block{display:inline-block!important}.d-md-block{display:block!important}.d-md-flex{display:flex!important}}@media (min-width:992px){.modal-lg{max-width:800px}.d-lg-none{display:none!important}}.flex-column{flex-direction:column!important}.justify-content-end{justify-content:flex-end!important}.justify-content-center{justify-content:center!important}.justify-content-between{justify-content:space-between!important}.align-items-start{align-items:flex-start!important}.align-items-center{align-items:center!important}.position-relative{position:relative!important}.w-75{width:75%!important}.w-100{width:100%!important}.w-auto{width:auto!important}.h-auto{height:auto!important}.mt-0{margin-top:0!important}.mb-0{margin-bottom:0!important}.ml-0{margin-left:0!important}.mt-1,.my-1{margin-top:.25rem!important}.mr-1{margin-right:.25rem!important}.mb-1,.my-1{margin-bottom:.25rem!important}.ml-1{margin-left:.25rem!important}.mt-2{margin-top:.5rem!important}.mr-2{margin-right:.5rem!important}.mb-2{margin-bottom:.5rem!important}.ml-2{margin-left:.5rem!important}.mt-3{margin-top:.75rem!important}.mr-3{margin-right:.75rem!important}.mb-3{margin-bottom:.75rem!important}.ml-3{margin-left:.75rem!important}.m-4{margin:1rem!important}.mt-4{margin-top:1rem!important}.mr-4{margin-right:1rem!important}.mb-4{margin-bottom:1rem!important}.ml-4{margin-left:1rem!important}.mt-5{margin-top:1.5rem!important}.mb-5{margin-bottom:1.5rem!important}.ml-5{margin-left:1.5rem!important}.mt-6{margin-top:2rem!important}.mr-6{margin-right:2rem!important}.mb-6{margin-bottom:2rem!important}.mt-7{margin-top:3rem!important}.mr-7{margin-right:3rem!important}.p-0{padding:0!important}.pr-0,.px-0{padding-right:0!important}.pb-0{padding-bottom:0!important}.px-0{padding-left:0!important}.p-1{padding:.25rem!important}.pt-1{padding-top:.25rem!important}.pt-2{padding-top:.5rem!important}.px-2{padding-right:.5rem!important}.px-2{padding-left:.5rem!important}.py-4{padding-top:1rem!important}.pr-4{padding-right:1rem!important}.py-4{padding-bottom:1rem!important}.p-5{padding:1.5rem!important}.py-5{padding-top:1.5rem!important}.py-5{padding-bottom:1.5rem!important}.pt-6{padding-top:2rem!important}.pb-6{padding-bottom:2rem!important}.pr-7{padding-right:3rem!important}.ml-auto{margin-left:auto!important}@media (min-width:768px){.flex-md-row{flex-direction:row!important}.m-md-0{margin:0!important}.mt-md-0{margin-top:0!important}.mb-md-1{margin-bottom:.25rem!important}.mx-md-2{margin-right:.5rem!important}.mx-md-2{margin-left:.5rem!important}.py-md-0{padding-top:0!important}.py-md-0{padding-bottom:0!important}.pb-md-2{padding-bottom:.5rem!important}.pr-md-4{padding-right:1rem!important}.pb-md-4{padding-bottom:1rem!important}.pt-md-5{padding-top:1.5rem!important}}.text-left{text-align:left!important}.text-center{text-align:center!important}.text-body{color:#465166!important}a{color:#0557f0}h1,h2,h4,h5,h6{color:#1e283b;letter-spacing:.5px}h1{font-weight:800}h2,h4{font-weight:700}h5,h6{font-weight:600}h6{letter-spacing:.75px}@media (max-width:767.98px){html{font-size:.9375rem}}.card{border:0}.card-title{color:inherit}.accordion .card{border:1px solid #e4e7ed!important}.accordion .card-title{background-color:#e4e7ed;margin-bottom:0;padding-left:20px;padding-right:20px}.btn{border-radius:2px;font-size:11px;letter-spacing:1.7px;padding:8px 26px 6px;text-transform:uppercase}.btn{outline:0}.btn-xs{font-size:10px;padding:4px 12px 3px}.btn-sm{font-size:11px;padding:8px 20px 6px}.btn-lg{font-size:12px;padding:7px 32px 6px}.dropdown-toggle:after{border:1px solid;border-left:none;border-top:none;content:"";height:4px;margin-left:.5rem;vertical-align:initial;vertical-align:middle;width:4px}.dropdown-toggle:after{transform:rotate(45deg)}.dropdown-menu{box-shadow:0 2px 25px rgba(0,0,0,.07);font-size:14px;margin-top:5px;padding:10px 16px}.dropdown-item{background-color:transparent;color:#e4e7ed;opacity:.8;padding-left:0;padding-right:0}.modal .close{font-weight:100}.modal-dialog{display:flex;height:100%;margin-bottom:0;margin-top:0}.modal-content{border:none;margin-bottom:auto;margin-top:auto}.modal-header{padding-bottom:1rem;padding-top:1rem}.nav-link{color:#465166}.form-control,.form-control option{font-weight:300}label{font-size:13px;font-weight:400;letter-spacing:.5px}body{display:flex;flex-direction:column;min-height:100vh}.row.gap-y{margin-bottom:-15px;margin-top:-15px}.row.gap-y>[class*=col-]{padding-bottom:15px;padding-top:15px}.navbar{left:0;min-height:50px;padding-bottom:0;padding-top:0;position:absolute;right:0;top:10px;z-index:1000}.navbar-brand{align-items:center;display:inline-flex}.navbar-brand img{vertical-align:baseline}.nav-navbar,.nav-navbar .nav-item{position:relative}.nav-navbar>.nav-item>.nav-link{line-height:50px;padding-bottom:0;padding-top:0}.nav-navbar .nav-link{font-size:.87em;font-weight:600;letter-spacing:1px;padding-left:1.125rem;padding-right:1.125rem;text-transform:uppercase;word-spacing:2px}.navbar-light .nav-navbar>.nav-item>.nav-link{color:#101929}.header{background-position:50%;background-repeat:no-repeat;background-size:cover;padding-bottom:90px;padding-top:140px;position:relative}.header>.container{height:100%;position:relative}.section{background-position:50%;background-repeat:no-repeat;background-size:cover;border-bottom:none;padding-bottom:7rem;padding-top:7rem;position:relative}@media (max-width:991.98px){.navbar-expand-lg .nav-navbar:not(.nav-inline){flex-direction:column;flex-wrap:nowrap;width:100%}.navbar-expand-lg .nav-navbar:not(.nav-inline)>.nav-item>.nav-link{font-size:.95rem;line-height:inherit;min-height:inherit;padding-bottom:.75rem;padding-top:.75rem;text-transform:none}.section{padding-bottom:5rem;padding-top:5rem}}.h-auto{height:auto}.w-auto{width:auto}.lead-2{font-size:1.25rem!important}.small-2{font-size:.875rem!important}.font-weight-500{font-weight:500!important}.font-weight-600{font-weight:600!important}.text-white{color:hsla(0,0%,100%,.85)!important}.shadow-none{box-shadow:none!important}.btn{border-radius:8px;font-size:1rem;letter-spacing:1px;line-height:1.5;padding:10px 18px;text-transform:none}.btn-secondary{background-color:#d5e1f6;color:#1e283b}.btn-light{background:rgba(16,63,152,.06);color:#353f54}.btn-xs{font-size:14px;padding:8px 14px}.btn-sm{font-size:16px;padding:8px 16px}.btn-lg{font-size:16px;padding:12px 20px}.rounded-xl{border-radius:1rem!important}.rounded-2xl{border-radius:1.25rem!important}.rounded-circle{border-radius:50%!important}select::-webkit-scrollbar{background-color:#f2f4f7;width:16px}select::-webkit-scrollbar-thumb{background-color:#d1d7e3;border-radius:6px}select{scrollbar-color:#007bff #f5f5f5;scrollbar-width:thin}::-webkit-file-upload-button{background:#f2f4f7;border:1px solid #353f54;border-radius:6px;padding:8px 16px}.form-control{font-weight:400}.form-control option{font-weight:400;padding:8px 20px}input.form-control{padding:12px 20px}label{color:#1e283b;font-size:1rem;font-weight:500;letter-spacing:0;margin-bottom:4px}.text-danger{color:#d82030!important}.display-1,.display-2{letter-spacing:-.32px}.display-5{font-size:1.25rem;font-weight:700;line-height:1.4}.display-1,.display-2,.display-4,.display-5{font-family:Plus Jakarta Sans}@media (max-width:450px){.display-1,.display-2{font-size:40px!important;line-height:44px}}.text-white{color:#fff!important}.display-1,.display-2,.display-4,.display-5,.display-6{font-feature-settings:"liga";line-height:1.2!important}img{height:auto;max-width:100%}.avatar{border-radius:10rem;height:48px;width:48px}.modal-header .close{color:#1e283b;font-weight:700;padding:2rem 2rem 0}.modal-title{font-family:DM Sans,sans-serif;font-size:20px;font-weight:500;line-height:1.5;margin-bottom:0}.modal-body{font-size:16px;font-weight:400;padding:0 2rem}.modal-header{padding-bottom:2rem;padding-top:2rem}.modal{padding-left:0!important;padding-right:0!important}.dropdown-menu{border:none;font-size:16px;font-weight:500;min-width:15rem;padding:8px}.dropdown-menu :last-child{margin-bottom:0}.dropdown-item{border:none;border-radius:8px;color:#1e283b;opacity:1;padding:12px 20px}.dropdown-toggle{font-size:1rem;padding:12px 20px}.dropdown-toggle:after{height:7px;margin-bottom:.2rem;margin-left:.6rem;width:7px}.dropdown-toggle:after{border:none!important;content:"\f107"!important;font:16px/1 FontAwesome;margin-bottom:.5rem;margin-left:.2rem;margin-right:.2rem;transform:rotate(1deg)}.card-title{color:#1e283b;font-family:DM Sans,sans-serif;font-size:20px;font-weight:700;line-height:1.2;margin-bottom:1rem}.card-header{background-color:#fafafa;border-bottom:none}.accordion{overflow-anchor:none}.accordion .card{background-color:#fff;border:none!important;border-radius:8px;box-shadow:0 10px 40px -12px rgba(29,33,41,.12);margin-bottom:16px}.accordion .card:last-child{margin-bottom:0}.accordion .card:first-of-type,.accordion .card:not(:first-of-type){border-radius:8px}.accordion .card-header{background-color:#fff;border:none;color:#101929;font-family:DM Sans,sans-serif;font-weight:600;padding:1rem 1.25rem}.accordion .card-header .card-title{background-color:#fff;font-size:1rem;font-weight:600;margin-bottom:0;padding-left:0;padding-right:0}.accordion .card-body{padding:0 1.25rem 1.25rem 2.9rem}.navbar{position:relative;top:20px}.navbar .container-fluid{padding-left:2rem;padding-right:2rem}.nav-navbar>.nav-item>.nav-link{line-height:1.5em}.nav-navbar .nav-link{font-size:1em;font-weight:500;text-transform:capitalize}.nav-navbar .dropdown-item{padding:16px 24px}@media (min-width:770px){.dropdown-menu{display:none}}.nav{padding:8px}.navbar .nav-link{color:#1e283b;font-weight:500}.nav-link{font-weight:400}.navbar-brand img{max-width:160px}.border-light{border-color:#f1f3f5!important}.p-20{padding:20px!important}.px-20{padding-left:20px;padding-right:20px}.mx-20{margin-left:20px;margin-right:20px}.header{padding-bottom:80px;padding-top:80px}.section{padding-bottom:5rem;padding-top:5rem}.close{opacity:1!important}.ph{font-size:16px;vertical-align:middle}.ph-md{font-size:20px}.ph-lg{font-size:24px}::-webkit-scrollbar{width:6px}::-webkit-scrollbar-track{background:#f1f1f1}::-webkit-scrollbar-thumb{background:#d6dae0;border-radius:20px}.platform-menu .network-icon{height:22px;width:22px}.products-nav-dropdown{width:710px}.platforms-nav-dropdown{width:520px}@media (max-width:450px){.products-nav-dropdown{width:350px}.header,.section{padding-bottom:3.75rem;padding-top:3.75rem}.card-overflow{overflow:auto!important}}.card-overflow::-webkit-scrollbar{display:none}.card-overflow{-ms-overflow-style:none;scrollbar-width:none}.testimonial-mobile-block{white-space:nowrap}.testimonial-mobile-block .card-component{display:inline-block;height:224px}.bg-light{background-color:#f9fafc!important}.font-plus-jakarta{font-family: Plus Jakarta Sans, sans-serif}  .testimonial-img{border-radius:50%;height:60px;width:60px}
</style>
{{--<link id="critical_css" rel="stylesheet" href="/css/main_critical.min.css?id=v1" type="text/css" />--}}

<!-- our full main css -->
<noscript>
    <link href="{{ mix('/css/new-main.css') }}" rel="stylesheet" type="text/css" />
</noscript>
<link id="main_css" rel="preload" href="{{ mix('/css/new-main.css') }}" as="style" onload="this.onload=null;this.rel='stylesheet';" />
@push('footer_html')
    <script>
        // on doc load, remove critical css
        document.addEventListener('DOMContentLoaded', () => {
            var el = document.createElement('div');
            el.className = 'test-css-loaded';
            document.body.appendChild(el);
            // var mainCss = document.getElementById('main_css');
            var criticalCss = document.getElementById('critical_css');
            var fnCheckCssTimer = setInterval(function() {
                // check color property = red
                if(getComputedStyle(el).zIndex === '22'){
                    // loaded
                    clearInterval(fnCheckCssTimer);
                    criticalCss.remove();
                    el.remove();
                }

            }, 200);
        });
    </script>
@endpush
@stack('head_html')

<!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
<!--[if lt IE 9]>
<script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
<script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
<![endif]-->

@include('layout.includes.common_head')
