<?php

namespace App\Http\Controllers\Api;

use App\Account;
use App\Team;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\InboxConversation;
use App\InboxConversationItem;
use App\Notifications\ConversationAssigned;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class InboxController extends Controller
{
    /**
     * Get conversations.
     *
     * @param Request $request
     * @return \Illuminate\Http\Response|\Illuminate\Http\JsonResponse
     * @throws ValidationException
     */
    public function getConversations(Request $request){
        
        $this->validate($request, [
            'team' => 'integer|exists:teams,id',
            'accounts.*' => 'integer|exists:accounts,id',
            'assigned_to' => 'integer|nullable|exists:users,id',
            'contact' => 'integer|exists:social_contacts,id',
            'contact_status' => 'string|in:spam,blocked,muted',
            'tags' => 'array',
            'tags.*' => 'integer|exists:tags,id',
            'status' => 'string|in:active,pending,closed,spam',
            'start' => 'date|nullable',
            'end' => 'date|nullable',
            'page' => 'integer',
            'per_page' => 'integer',
            'q' => 'string',
        ]);

        $teamId = $request->input('team');

        $perPage = $request->input('per_page', 20);

        // perPage is max 100
        if($perPage > 100){
            $perPage = 100;
        }

        if($teamId){
            /** @var Team $team */
            $team = user()->joinedTeams()->findOrFail($teamId);
            $allAccounts = $team->accounts()->get();
        } else {
            $allAccounts = user()->getAvailableAccounts(['*'], true);
        }

        if(count($request->input('accounts', [])) > 0){ 
            $allAccounts = $allAccounts->whereIn('id', $request->input('accounts', []));
        }

        //check if the user has permission for all accounts 
        $notAllowedAccounts = $allAccounts->filter(function($acc){
            return !$this->userHasPermissionForAccount($acc, 'inbox.manage');
        });

        if($notAllowedAccounts->count() !== 0){
            abort(400, 'You do not have permission to perform this action');
        }

        $accountIds = $allAccounts->pluck('id')->toArray();

        $query = InboxConversation::whereIn('account_id', $accountIds)
            ->when($request->input('assigned_to'), function($query, $assigned_to){
                return $query->where('assigned_to', $assigned_to);
            })
            ->when($request->input('contact'), function($query, $social_contact){
                return $query->where('social_contact_id', $social_contact);
            })
            ->when($request->input('status'), function($query, $status){
                return $query->where('status', $status);
            })
            ->when($request->input('contact_status'), function($query, $status){
                return $query->whereHas('socialContact', function($q) use ($status){
                    $q->where('status', $status);
                });
            })
            ->when($request->input('tags'), function($query, $tags){
                return $query->whereHas('tags', function($q) use ($tags){
                    $q->whereIn('tags.id', $tags);
                });
            })
            ->when($request->input('start'), function($query, $start){ // for dates, we only filter by updated_at
                return $query->where('updated_at', '>=', $start);
            })
            ->when($request->input('end'), function($query, $end){
                return $query->where('updated_at', '<=', $end);
            })
            ->when($request->has('q'), function($query) use ($request){
                $searchString = $request->get('q');
                return $query
                    ->whereHas('socialContact', function($q) use ($searchString){
                        $q->where('name', 'like', '%' . $searchString . '%');
                    })
                    ->orWhereHas('items', function($q) use ($searchString){
                        // ->whereFullText('content', $searchString) //need to add fulltext index
                        $q->where('data', 'like', '%' . $searchString . '%'); //looking in content could slow the query?
                    });
            });

        \Log::info('Inbox query:' . $query->toSql());

        return response()->json(get_paginated_data($query, $request->input('page', 1), $perPage, function($itm){
            return InboxConversation::transform($itm);
        }));
    }

    /**
     * Get conversation items.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function getConversationItems(Request $request, int $id){
        
        /** @var InboxConversation $conversation */
        $conversation = InboxConversation::available()->findOrFail($id);

        $perPage = $request->input('per_page', 20);

        // perPage is max 100
        if($perPage > 100){
            $perPage = 100;
        }

        if(!$this->userHasPermissionForAccount($conversation->account, 'inbox.manage')){
            abort(404, 'You do not have permission to perform this action');
        }

        $query = $conversation->items()->where('parent_id', null);

        return response()->json(get_paginated_data($query, $request->input('page', 1), $perPage, function($itm){
            return InboxConversationItem::transform($itm);
        }));
    }

    /**
     * Get conversation item replies.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int $id
     * @return mixed
    */
    public function getConversationItemReplies(Request $request, $convoId, $itemId){

        $perPage = $request->input('per_page', 20);

        // perPage is max 100
        if($perPage > 100){
            $perPage = 100;
        }

        $conversation = InboxConversation::available()->findOrFail($convoId);

        if(!$this->userHasPermissionForAccount($conversation->account, 'inbox.manage')){
            abort(404, 'You do not have permission to perform this action');
        }

        /** @var InboxConversationItem $item */
        $item = $conversation->items()->where('id', $itemId)->firstOrFail();

        $query = $item->children()->where('parent_id', $itemId);

        return response()->json(get_paginated_data($query, $request->input('page', 1), $perPage, function($itm){
            return InboxConversationItem::transform($itm);
        }));
    }
    
    /**
     * Configure the account with the provided settings for inbox.
     *
     * @param \Illuminate\Http\Request $request.
     * @param int $accId Account ID.
     * @return void
     * @throws \Exception.
     */
    public function configureAccount(Request $request, int $accId){

        $acc = Account::findOrFail($accId); // we fetch account directly because we have validation in permission check function

        if(!$this->userHasPermissionForAccount($acc, 'inbox.config')){
            abort(404, 'You do not have permission to perform this action');
        }

        $this->validate($request, [
            'active' => 'required|boolean',
            'items' => 'sometimes|array',
            'items.*' => [
                'sometimes',
                'string',
                function($attribute, $value, $fail) use ($acc){
                    if(!in_array($value, $this->getItemTypes($acc))){
                        $fail('The item ' . $value . ' is not supported for ' . $acc->getType());
                    }
                }
            ],
        ]);

        $wasActive = $acc->getOption('inbox_config.active');

        $data = array_merge($acc->getOption('inbox_config', []), $request->only(['active', 'items',]));

        // if inbox was not active, and is now active, reset the fetched_at
        if(!$wasActive && $data['active']){
            $acc->inbox_fetched_at = null;

            $acc->save();
        }

        $acc->setOption('inbox_config', $data);

        $acc->save();
    }

    /**
     * Get the account configuration for inbox.
     *
     * @param Request $request
     * @param $accId
     * @return \Illuminate\Http\JsonResponse
     */
    public function getAccountConfiguration(Request $request, $accId){

        $acc = user()->getAvailableAccounts()->firstWhere('id', $accId);

        if(!$acc){
            abort(404, 'Account not found');
        }

        return response()->json($acc->getOption('inbox_config', []));
    }

    /**
     * Get the available items to catch for an account.
     *
     * @param Account|null $account
     * @return array
     */
    public function getItemTypes(Account $account = null)
    {
        $ITEMS = [
            'facebook.page' => [
                'message',
                'comment',
                'post',
                'rating',
            ],
            'instagram.api' => [
                'comment',
                'mention',
                'post'
            ],
        ];

        $data = [];

        if ($account) {
            if (isset($ITEMS[$account->type])) {
                $availableItems = $ITEMS[$account->type];
                foreach ($availableItems as $item) {
                    $data[$item] = $item;
                }
            } else {
                $data = $ITEMS;
            }
        } else {
            $data = $ITEMS;
        }

        return array_values($data);
    }

    /**
     * Assigns conversations in bulk to a user in a team.
     *
     * @param Request $request .
     * @return void
     * @throws ValidationException
     */
    public function assignConversation(Request $request)
    {
        $this->validate($request, [
            'conversation_ids' => 'required|array',
            'conversation_ids.*' => 'integer|exists:inbox_conversations,id',
            'team_id' => 'required|integer|exists:teams,id',
            'user_id' => 'required|integer|exists:users,id',
        ]);

        $conversationIds = $request->input('conversation_ids');
        $teamId = $request->input('team_id');
        $userId = $request->input('user_id');

        $queuedObjects = [];

        foreach ($conversationIds as $convoId) {
            /** @var InboxConversation $conversation */
            $conversation = InboxConversation::available()->findOrFail($convoId);

            /** @var Team $team */
            $team = user()->joinedTeams()->findOrFail($teamId);

            if (!$team->hasPermission(user(), 'inbox.manage')) {
                abort(404, 'You do not have permission to perform this action');
            }

            /** @var \App\User $user */
            $user = $team->members()->findOrFail($userId);

            // Check if the user has permission for the account
            if (!$this->userHasPermissionForAccount($conversation->account, 'inbox.manage')) {
                abort(404, 'The user you are assigning to does not have permission to manage this conversation');
            }

            $queuedObjects[] = [
                'conversation' => $conversation,
                'user' => $user,
                'team' => $team,
            ];
        }

        foreach($queuedObjects as $queuedObject){
            // we do this separately so that validation is done before any changes are made
            $queuedObject['conversation']->assignTo(
                $queuedObject['user'],
                $queuedObject['team']
            );
        }
    }

    /**
     * Mark a conversation as read
     *
     * @param Request $request
     * @throws ValidationException
     */
    public function updateConversationStatus(Request $request){

        $this->validate($request, [
            'conversation_ids' => 'required|array',
            'conversation_ids.*' => 'integer|exists:inbox_conversations,id',
            'status' => 'required|string|in:open,closed,pending',
        ]);

        $conversationIds = $request->input('conversation_ids');
        foreach ($conversationIds as $convoId) {
            $conversation = InboxConversation::available()->findOrFail($convoId);
            
            if(!$this->userHasPermissionForAccount($conversation->account, 'inbox.manage')){
                abort(404, 'You do not have permission to perform this action');
            }

            $conversation->setStatus($request->input('status'));
        }

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param Request $request
     * @param int $convoId
     * @throws \Exception
     */
    public function deleteConversation(Request $request, int $convoId){
        // maybe check if the user has permission to delete the conversation
        $conversation = InboxConversation::available()->findOrFail($convoId);
        
        if(!$this->userHasPermissionForAccount($conversation->account, 'inbox.manage')){
            abort(404, 'You do not have permission to perform this action');
        }

        $conversation->delete();
    }

    /**
     * Find and delete post
     *
     * @param Request $request
     * @param $convoId
     * @param $itemId
     * @throws \Exception
     */
    public function deleteConversationItem(Request $request, $convoId, $itemId){
        $conversation = InboxConversation::available()->findOrFail($convoId);

        if(!$this->userHasPermissionForAccount($conversation->account, 'inbox.manage')){
            abort(404, 'You do not have permission to perform this action');
        }

        $item = $conversation->items()->where('id', $itemId)->firstOrFail();

        $item->delete();
    }

    /**
     * User permission for the given account
     * @param Account $acc
     * @param string $perm
     * @return bool
    */
    private function userHasPermissionForAccount(Account $acc, string $perm): bool
    {
        // teams where user has this permission
        $teams = user()->joinedTeams->filter(function($team) use ($perm){
            return $team->hasPermission(user(), $perm);
        });

        // to-do: enforce team permission even if this is user's account
        $teamAccountIds = [];
        foreach($teams as $team){
            $teamAccountIds = array_merge($teamAccountIds, $team->accounts()->pluck('id')->toArray());
        }

        $hasPerm = in_array($acc->id, $teamAccountIds);

        if($hasPerm){
            return true;
        }

        // user's accounts
        $userAccountIds = user()->accounts()->pluck('id')->toArray();

        //check if the account belongs to the user
        if(in_array($acc->id, $userAccountIds)){
            return true;
        }

        return false;
    }

}
