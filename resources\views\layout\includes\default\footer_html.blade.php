<script defer type="text/javascript">
    var functionsToExecute = [];

    window.execOnLoad = (func) => {
        if (window.loaded && typeof(func) === "function") {
            func(); //exec immediately
        } else {
            functionsToExecute.push(func); //push to queue
        }
    }

    /* load additional css font */
    (function(){
        var loadCss = function(cssId, href){
            if (!document.getElementById(cssId))
            {
                var head  = document.getElementsByTagName('head')[0];
                var link  = document.createElement('link');
                link.id   = cssId;
                link.rel  = 'stylesheet';
                link.type = 'text/css';
                link.href = href;
                link.media = 'all';
                head.appendChild(link);
            }
        };
        var loadJS = function( src, defer, cb ){
            "use strict";
            var ref = document.getElementsByTagName( "script" )[ 0 ];
            var script = document.createElement( "script" );

            script.src = src;
            if(defer){
                script.defer = true;
            } else {
                script.async = true;
            }
            ref.parentNode.insertBefore( script, ref );

            if (cb && typeof(cb) === "function") {
                script.onload = cb;
            }
            return script;
        };
        window.addEventListener("load", function(){
            // phosphor icons
            loadCss('ph-regular', 'https://cdn.jsdelivr.net/npm/@phosphor-icons/web@2.1.1/src/regular/style.min.css');
            loadCss('ph-bold', 'https://cdn.jsdelivr.net/npm/@phosphor-icons/web@2.1.1/src/bold/style.min.css');
            loadCss('ph-fill', 'https://cdn.jsdelivr.net/npm/@phosphor-icons/web@2.1.1/src/fill/style.min.css');

            // needed for some places: like bootstrap default dropdown icon
            loadCss('faCSS', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css');

            loadJS("{{mix('/js/main.js')}}", true, function() {
                window.loaded = true;
                // execute all functions that were queued before main.js was loaded
                functionsToExecute.forEach(func => {
                    if (typeof func === "function") {
                        func();
                    }
                });
            });
            
        });
    })();

</script>
