<?php

namespace App\Traits;


use Illuminate\Support\Str;

trait HasOptions
{
    private static $retryExceptions = [
        'timeout exceeded',
    ];

    abstract protected function getOptions();
    abstract protected function setOptions($opts);

    /**
     * Get an option using dot syntax.
     *
     * @param string $key
     * @param null $default
     * @return mixed
     */
    public function getOption(string $key, $default = null)
    {
        if (trim($key) == '') return null;
        return array_get($this->getOptions(), $key, $default);
    }

    /**
     * Set an option using dot syntax.
     *
     * @param string $key
     * @param mixed $val
     * @return $this
     * @throws \Exception
     */
    public function setOption(string $key, $val)
    {
        return $this->setMultipleOptions([$key => $val]);
    }

    /**
     * Set multiple options using dot syntax.
     * @param array $opts
     * @return $this
     * @throws \Exception
     */
    public function setMultipleOptions(array $opts)
    {
        $origOpts = $this->getOptions();

        foreach ($opts as $key => $val) {
            array_set($origOpts, $key, $val);
        }

        try {
            $this->setOptions($origOpts);
        } catch (\Exception $e) {
            if (Str::contains(strtolower($e->getMessage()), self::$retryExceptions)) {
                // retry
                sleep(1);
                return $this->setMultipleOptions($opts);
            } else {
                throw $e;
            }
        }

        return $this;
    }

    /**
     * Remove an option using dot syntax.
     *
     * @param string $key
     * @return $this
     * @throws \Exception
     */
    public function removeOption(string $key)
    {
        return $this->removeMultipleOptions([$key]);
    }

    /**
     * Remove multiple options using dot syntax.
     *
     * @param array $keys
     * @return $this
     * @throws \Exception
     */
    public function removeMultipleOptions(array $keys)
    {
        $opts = $this->getOptions();
        foreach ($keys as $key) {
            array_forget($opts, $key);
        }

        try {
            $this->setOptions($opts);
        } catch (\Exception $e) {
            if (Str::contains(strtolower($e->getMessage()), self::$retryExceptions)) {
                // retry
                sleep(1);
                return $this->removeMultipleOptions($keys);
            } else {
                throw $e;
            }
        }
        return $this;
    }

}
