<template>
    <div class="post mt-4">
        <div class="d-flex">
            <img :src="account.image" class="threads-avatar rounded-circle" alt="avatar" />
            <div class="w-100 ml-3">
                <div class="d-flex align-item-center justify-content-between">
                    <div>
                        <a class="fullname font-weight-500" href="#" @click.prevent>{{ account.name }}</a>
                        <span class="timestamp font-weight-400"> Just now</span>
                    </div>
                    <i class="ph ph-dots-three ph-lg"></i> 
                </div>
                <div>
                    <RichText class="text" :value="content" :readonly="true" v-if="content" />
                </div>
            
                <div class="overflow-hidden" v-if="attachments.length">
                    <div class="gallery gallery-4-type-2" v-if="attachments.length > 1">
                        <div v-for="(attachment, index) in attachments" class="gallery-item" :key="index">
                            <video class="media video" controls :title="attachment.name"  v-if="attachment.type.includes('video')" style="max-height: 500px;">
                                <source :src="attachment.url" />
                            </video>
                            <img
                                class="media"
                                :src="attachment.url" :alt="attachment.name" :title="attachment.name" style="max-height: 500px;"
                                v-else
                            />
                        </div>
                    </div>
                    <div class="w-100 text-center" v-else-if="attachments.length === 1">
                        <video class="media video w-100" controls :title="attachments[0].name"  v-if="attachments[0].type.includes('video')" style="max-height: 500px;">
                            <source :src="attachments[0].url" />
                        </video>
                        <img class="media w-100" :src="attachments[0].url" :alt="attachments[0].name" :title="attachments[0].name" style="max-height: 500px;"
                            v-else/>
                    </div>
                </div>
                <LinkPreview
                        v-if="link && attachments.length === 0"
                        :url="link"
                        type="thread" 
                        style="background-color: #f0f2f5;"/>
    
                <div>
                    <div class="post-footer">
                        <div class="post-footer-option d-flex pt-4 pb-4 mb-1">
                            <div class="d-flex align-items-center mr-5">
                                <i class="ph ph-heart ph-lg mr-1"></i>
                                80
                            </div>    
                            <div class="d-flex align-items-center mr-5">
                                <i class="ph ph-chat-circle ph-lg mr-1"></i>
                                1K
                            </div>   
                            <div class="d-flex align-items-center mr-5">
                                <i class="ph ph-repeat ph-lg mr-1"></i>
                                40
                            </div>
                            <div class="d-flex align-items-center">
                                <i class="ph ph-paper-plane-tilt ph-lg mr-1"></i>
                                16
                            </div>    
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>

<script>
import _ from "lodash";
import AutoLinker from "autolinker";
import VueCarousel from "@chenfengyuan/vue-carousel";
import RichText from "../../common/RichText.vue";
import LinkPreview from "../LinkPreview.vue";
export default {
    name: "ThreadsPost",
    props: ["account", "text", "attachments", "link", "options"],
    data() {
        return {
            showFull: false
        };
    },
    components: {
        VueCarousel,
        LinkPreview,
        RichText
    },
    computed: {
        content() {
    return this.getHtmlText(this.text);
    },
        comment() {
            if (!this.options.comment) return null;
            return this.getHtmlText(
                this.showFull
                    ? this.options.comment
                    : _.truncate(this.options.comment, {
                          length: 120,
                          omission: "... [_SB_]more[_SB_]"
                      }).replace(
                          "... [_SB_]more[_SB_]",
                          `... <a href='#' class="_see_all" style="color: #8e8e8e;">more</a>`
                      )
            );
        },
    },
    methods: {
        truncate: _.truncate,
        getHtmlText(text) {
            text = text.replace(/(\r\n|\r|\n){2,}/g, "$1\n"); // normalize linebreaks
            return AutoLinker.link(text, {
                className: "external-link",
                urls: false,
                email: false,
                phone: false,
                replaceFn: match => {
                    const tag = match.buildTag();
                    tag.setAttr(this.$options._scopeId, ""); // so our scoped css works
                    return tag;
                }
            });
        },
        showMore() {
            this.showFull = true;
            return false;
        }
    },
        mounted() {
        $(document).on("click.see_all", "._see_all", this.showMore);
    },



    beforeDestroy() {
        $(document).off("click.see_all", "._see_all", this.showMore);
    }
};
</script>

<style lang="scss" scoped>
.post {
    font-family: Roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
    padding-left: 14px;
    padding-right: 20px;
}
.threads-avatar{
    width: 42px;
    height: 42px;
}
.fullname {
    font-size: 18px;
    color: #000000;
    line-height: 25.2px;
}

.timestamp {
    color: #999999;
    line-height: 22.4px;

}
.media{
    border-radius: 10px;
    border: 0.5px solid #CCC;
}
.video{
    object-fit: cover;
}
.gallery-item{
    width: 232px;
    height: 232px;
}
.text {
    overflow-wrap: break-word;
    word-break: break-word;
    line-height: 25.2px !important;
    font-size: 18px !important;
    margin-top: 2px;
    color: #000000 !important;
    a {
        color: rgb(0, 55, 107) !important;
    }
}
.media{
    margin-top: 6px;
    border: 0.5px solid #CCCCCC;
    border-radius: 10px;
}
.post-footer {
    color: #424242;
    line-height: 20.85px !important;
    font-size: 15px;
    i{
        line-height: 20.85px !important;
    }
}
</style>
