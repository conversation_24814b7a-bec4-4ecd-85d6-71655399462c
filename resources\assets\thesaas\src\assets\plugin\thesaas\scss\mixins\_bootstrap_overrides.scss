
//----------------------------------------------------/
// Buttons
//----------------------------------------------------/

// Normal
//
@mixin button-variant($background, $border, $hover-background: darken($background, 4%), $hover-border: darken($border, 4%), $active-background: darken($background, 8%), $active-border: darken($border, 8%)) {
  color: color-yiq($background);
  @include gradient-bg($background);
  border-color: $border;
  @include box-shadow($btn-box-shadow);

  @include hover {
    color: color-yiq($hover-background);
    @include gradient-bg($hover-background);
    border-color: $hover-border;
    box-shadow: 0 1px 10px rgba($background, .4);
  }

  &:focus,
  &.focus {
    color: color-yiq($background);
    // Avoid using mixin so we can pass custom focus shadow properly
    @if $enable-shadows {
      box-shadow: $btn-box-shadow, 0 0 0 $btn-focus-width rgba($border, .5);
    } @else {
      box-shadow: 0 0 0 $btn-focus-width rgba($border, .5);
    }
  }

  // Disabled comes first so active can properly restyle
  &.disabled,
  &:disabled {
    background-color: $background;
    border-color: $border;
  }

  &:not([disabled]):not(.disabled):active,
  &:not([disabled]):not(.disabled).active,
  .show > &.dropdown-toggle {
    color: color-yiq($active-background);
    background-color: $active-background;
    @if $enable-gradients {
      background-image: none; // Remove the gradient for the pressed/active state
    }
    border-color: $active-border;

    // Avoid using mixin so we can pass custom focus shadow properly
    @if $enable-shadows {
      box-shadow: $btn-active-box-shadow, 0 0 0 $btn-focus-width rgba($border, .5);
    } @else {
      box-shadow: 0 0 0 $btn-focus-width rgba($border, .5);
    }
  }
}

// Outline
//
@mixin button-outline-variant($color, $color-hover: #fff, $active-background: $color, $active-border: $color) {
  color: $color;
  background-color: transparent;
  background-image: none;
  border-color: $color;

  &:focus,
  &.focus {
    color: $color;
    box-shadow: 0 0 0 $btn-focus-width rgba($color, .5);
  }

  &:hover {
    color: color-yiq($color);
    background-color: $active-background;
    border-color: $active-border;
    box-shadow: 0 1px 10px rgba($color, .4);
  }

  &.disabled,
  &:disabled {
    color: $color;
    background-color: transparent;
  }

  &:not([disabled]):not(.disabled):active,
  &:not([disabled]):not(.disabled).active,
  .show > &.dropdown-toggle {
    color: color-yiq($color);
    background-color: $active-background;
    border-color: $active-border;
    // Avoid using mixin so we can pass custom focus shadow properly
    box-shadow: 0 0 0 $btn-focus-width rgba($color, .5);
  }
}
