import $ from "jquery";
import { axios, alertify, spinner } from "../components";

const axiosError = e => {
    if (e.response && e.response.data) {
        let handled = false;
        const errors = e.response.data.errors;
        if (errors) {
            Object.keys(errors).forEach(k => {
                alertify.error(errors[k].join(" \n"));
            });
            handled = true;
        } else if (e.response.data.message) {
            alertify.error(e.response.data.message);
            handled = true;
        }
        if(e.response.data.redirect){
            document.location.href = e.response.data.redirect;
            handled = true;
        }
        if (handled) return;
    }
    alertify.error(e.message || "Error");
};

// general
// email verification resend
$("#resend_verification_email").on("click", async function() {
    const $this = $(this);
    if ($this.attr("data-busy") === "true") return false;
    $this.attr("data-busy", "true");
    try {
        await axios.post("/app/settings/resend_verification_email");
        alertify.success("Email sent. Please complete email verification.");
    } catch (e) {
        axiosError(e);
    }
    $this.attr("data-busy", "false");
    return false;
});

(function(){
    const passwordInput = $('#password');
    const toggleIcon = $('#togglePassword');
    toggleIcon.on('click', function(){
        passwordInput.attr('type', passwordInput.attr('type') === 'password' ? 'text' : 'password');
        toggleIcon.toggleClass('ph-eye ph-eye-slash');
    })

    const passwordConfirmation = $('#passwordConfirmation');
    const showPasswordBtnForConfirmation = $('#togglePasswordConfirmation');
    showPasswordBtnForConfirmation.on('click', function(){
        passwordConfirmation.attr('type', passwordConfirmation.attr('type') === 'password' ? 'text' : 'password');
        showPasswordBtnForConfirmation.toggleClass('ph-eye ph-eye-slash');
    })
})();

// delete account
$("#delete_account").on("click", ()=>{
    alertify.delete(`
        <h2 class="text-danger">WARNING!!!</h2>
        <h4 class="text-danger">THIS ACTION IS NOT REVERSIBLE</h4>
        <p class="lead text-danger">
            Deleting your SocialBu account will remove all your data and your access to SocialBu. The features you are currently using will stop working for you.
            <br/><br/>
            Please review your current subscription and account status to make any changes to your SocialBu account that you want to make. You cannot access your account or login to SocialBu after you delete your SocialBu account.
            <br/><br/>
            You are going to delete your SocialBu account.
        </p>
        <hr/>
        <p class="text-muted small-2">
            <a href="https://socialbu.com/about/terms" target="_blank">These terms apply to your use of SocialBu</a>
        </p>
        `, ()=>{
        axios.post("/app/settings/delete_account").then(()=>{
            window.alert("It has been a good time together. We will miss you.");
            window.alert("You will be redirected to SocialBu homepage after your account is deleted. Note that your account will be permanently removed soon.");
            window.location.href = "https://socialbu.com";
        }).catch(e=>{
            axiosError(e);
        })
    })
});

$(".plan_type_input input")
    .on("change", () => {
        const type = $(".plan_type_input input:checked").val();
        if (type === "monthly") {
            $(".yearly").hide();
            $(".monthly").show();
        } else if (type === "yearly") {
            $(".monthly").hide();
            $(".yearly").show();
        }
    })
    .trigger("change");
$("select.country, select#input_user_timezone").selectpicker({
    liveSearch: true
});
try {
    (() => {
        $(".settings-tabs a").on("click", function() {
            document.location.hash = $(this).attr("href");
            setTimeout(()=>{
                // setup tour
                $(document).trigger("setup_tour");
            }, 1000);
        });
        let v;
        if ((v = document.location.hash)) {
            if(v.length) {
                $(".settings-tabs a[href='" + v + "']").tab("show");
            }
        }
    })();
} catch (e) {}
setTimeout(() => {
    $(".settings-container").removeClass("invisible");
    $(document).trigger("setup_tour");
}, 500);

// subscription / payment
let subBtnClicked;
$("[data-subscription]").on("click", async function() {
    const $this = $(this);

    const action = $this.attr("data-subscription");

    const cardOnFile = $("#card_details").length > 0;

    if ((!cardOnFile) && action !== "deactivate_deal") {
        $("#stripe_card_modal").modal("show");
        subBtnClicked = this;
        return false;
    }

    $this.attr("disabled", "disabled").prop("disabled", true);
    spinner.show();

    if (action === "resume") {
        try {
            await axios.patch("/app/settings/subscription", {
                action
            });

            __recordEvent("ResumePlan");

            window.__recordUsageEvent("subscription_resumed", "", false);

            alertify.success("Welcome back!");
            document.location.reload();
        } catch (e) {
            axiosError(e);
        }
    } else if (action === "cancel") {

        const $modal = $("#cancel_modal");

        if($this.attr("data-final")){

            // initialize modal
            const reason = $modal.find("input[name='reason']:checked").val();

            if(!reason){
                alertify.error("Please select a reason for the cancellation");
            } else {

                let reasonText = "";
                if(reason === "Other"){
                    // get the text
                    reasonText = $modal.find("input[name='other_reason']").val();
                } else {
                    reasonText = reason;
                }

                try {
                    await axios.patch("/app/settings/subscription", {
                        action,
                        reason: reasonText
                    });

                    __recordEvent("CancelPlan", {
                        reason: reasonText
                    });

                    window.__recordUsageEvent("subscription_cancelled", {
                        reason: reasonText
                    }, false);

                    document.location.reload();
                } catch (e) {
                    axiosError(e);
                }
            }


        } else {

            const processCancel = () => {
                // show popup
                $modal.modal("show");

                if(!$modal.attr("data-init")){
                    $modal.find("input[name='reason']").on("change", function(){
                        if($("input[name='reason'][value='Other']").is(":checked")){
                            $modal.find("#reason_input_container").show();
                        } else {
                            $modal.find("#reason_input_container").hide();
                        }
                    }).trigger("change");
                }
            };

            if(false && typeof window.profitwell === "function"){
                window.profitwell('init_cancellation_flow').then(result => {
                    // This means the customer either aborted the flow (i.e.
                    // they clicked on "never mind, I don't want to cancel"), or
                    // accepted a salvage attempt or salvage offer.
                    // Thus, do nothing since they won't cancel.
                    if (result.status === 'retained' || result.status === 'aborted') {
                        return;
                    }

                    // At this point, the customer ended deciding to cancel (i.e.
                    // they rejected the salvage attempts and the salvage offer).
                    // It could also be the case the widget couldn't be shown properly for
                    // some reason (for which case, `result.status` will be 'error'), but that
                    // shouldn't stop them from cancelling.
                    // The normal cancel flow goes here
                    processCancel();
                }).catch(error => {
                    console.log(error);
                    // This means the widget couldn't be loaded properly for some reason.
                    // It's not a big deal, so we can just proceed with the normal cancel flow.
                    processCancel();
                });
            } else {
                processCancel();
            }

        }

    } else if (action === "end") {
        window.confirm("Are you sure you want to end your subscription? This action is not reversible.") && await (async () => {
            try {
                const { data } = await axios.patch("/app/settings/subscription", {
                    action
                });

                if(data && data.redirect){
                    document.location.href = data.redirect;
                } else {
                    document.location.reload();
                }
            } catch (e) {
                axiosError(e);
            }
        })();
    }  else if (action === "start") {
        try {
            const { data } = await axios.patch("/app/settings/subscription", {
                action,
                plan: $this.attr("data-plan"),
                type: $(".plan_type_input input:checked").val()
            });

            __recordEvent("SelectPlan", {
                plan: $this.attr("data-plan"),
            });
            window.__recordUsageEvent("select_plan", {
                plan: $this.attr("data-plan")
            }, false);

            if(data && data.redirect){
                document.location.href = data.redirect;
            } else {
                document.location.reload();
            }
        } catch (e) {
            axiosError(e);
        }
    } else if (action === "select") {
        try {
            const { data } = await axios.patch("/app/settings/subscription", {
                action,
                plan: $this.attr("data-plan"),
                type: $(".plan_type_input input:checked").val()
            });

            __recordEvent("SelectPlan", {
                plan: $this.attr("data-plan"),
            });
            window.__recordUsageEvent("select_plan", {
                plan: $this.attr("data-plan")
            }, false);

            if(data && data.redirect){
                document.location.href = data.redirect;
            } else {
                document.location.reload();
            }
        } catch (e) {
            axiosError(e);
        }
    } else if (action === "free") {
        try {
            await axios.patch("/app/settings/subscription", {
                action,
                plan: $this.attr("data-plan")
            });

            __recordEvent("SelectPlan", {
                plan: "free",
            });
            window.__recordUsageEvent("select_plan", {
                plan: "free"
            }, false);

            document.location.reload();
        } catch (e) {
            axiosError(e);
        }
    } else if (action === "deactivate_deal") {
        try {
            await axios.patch("/app/settings/subscription", {
                action
            });

            document.location.reload();
        } catch (e) {
            axiosError(e);
        }
    }
    $this.removeAttr("disabled").prop("disabled", false);
    spinner.hide();
});
(async () => {

    // make sure stripe is loaded
    let checks = 0;
    while(!window.Stripe && checks < 30){
        await new Promise((res, rej) => setTimeout(res, 1000));
        ++checks;
    }
    if(!window.Stripe){
        window.alert("Some components failed to load. Reloading page...");
        document.location.reload();
        return;
    }

    // init stripe sdk
    const stripe = Stripe(process.env.MIX_STRIPE_KEY);
    const elements = stripe.elements();
    // Create an instance of the card Element.
    const card = elements.create("card", {
        style: {
            base: {
                fontSize: "16px",
                color: "#32325d"
            }
        }
    });
    const cardElement = $("#card-element")[0];
    const $cardModal = $("#stripe_card_modal");

    $cardModal
        .on("hidden.bs.modal", () => {
            card.unmount(cardElement);
            $cardModal.find(".card-submit").off("click");
        })
        .on("shown.bs.modal", async () => {
            setTimeout(() => {
                const $errors = $("card-errors");

                // Add an instance of the card Element into the `card-element` <div>.
                card.mount(cardElement);

                card.addEventListener("change", ({ error }) => {
                    if (error) {
                        $errors.text(error.message).show();
                    } else {
                        $errors.empty().hide();
                    }
                });

                let submitting = false;

                $cardModal.find(".card-submit").on("click", async () => {
                    if (submitting) return false;

                    submitting = true;

                    $cardModal.spin();

                    let clientSecret;

                    try {
                        // get intent secret
                        const { data } = await axios.post("/app/settings/create_payment_intent");
                        clientSecret = data.client_secret;
                    } catch (e) {
                        axiosError(e);
                        submitting = false;
                        $cardModal.spin(false);
                        return false;
                    }


                    const cardHolder = $(
                        "#stripe_card_modal input.card_name"
                    ).val();

                    const { setupIntent, error } = await stripe.confirmCardSetup(
                        clientSecret, {
                            payment_method: {
                                card: card,
                                billing_details: { name: cardHolder }
                            }
                        }
                    );

                    if (error) {
                        // Inform the customer that there was an error.
                        $errors.text(error.message);
                    } else {
                        // Send the token to your server.
                        try {
                            const reqData = {
                                payment_method: setupIntent.payment_method,
                                card_name: cardHolder,
                                country: $(
                                    "#stripe_card_modal select.country"
                                ).val()
                            };
                            if (subBtnClicked) {
                                reqData.plan = $(subBtnClicked).attr("data-plan");
                                reqData.type = $(
                                    ".plan_type_input input:checked"
                                ).val();
                                reqData.action = $(subBtnClicked).attr(
                                    "data-subscription"
                                );
                            }
                            const { data } = await axios.patch(
                                subBtnClicked
                                    ? "/app/settings/subscription"
                                    : "/app/settings/payment",
                                reqData
                            );
                            __recordEvent("AddPaymentInfo");
                            $cardModal.modal("hide");

                            if(data && data.redirect){
                                document.location.href = data.redirect;
                            } else {
                                document.location.reload();
                            }

                        } catch (e) {
                            axiosError(e);
                        }
                    }
                    submitting = false;

                    $cardModal.spin(false);

                    return false;
                });
            }, 500);
        });
})();
