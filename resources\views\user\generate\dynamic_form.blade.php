@extends('layout.user')
@section('title', 'Generate Test | ' . config('app.name'))
@push('footer_html')
    <script>
        (function () {
            __loadComponent("generate_tool", "#generated_content", function(c){
                c.initialize({
                    id: "{{ $id }}",
                    form: {!! json_encode($form) !!}
                });
            });
        })();
    </script>
@endpush
@section('content')
    <div class="row">
        <div class="col-12">
            <div id="generated_content">
                <div class="text-center">
                    <i class="ph ph-circle-notch ph-spin ph-lg text-muted"></i><span class="sr-only">Loading...</span>
                </div>
            </div>
        </div>
    </div>
@endsection
