<?php


use Tests\TestCase;

class DynamicFormTest extends TestCase
{
    public function testFormInterface()
    {
        $form = new \App\Generate\DynamicForm\Form('test');

        $this->assertGreaterThan(0, count($form->getFields()));

        $this->assertGreaterThan(0, count($form->getOutputComponents()));

        try {
            $form = new \App\Generate\DynamicForm\Form('not_found');
        } catch (\Exception $e) {
            $this->assertEquals('Form not found', $e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public function testFormSubmission()
    {

        $form = new \App\Generate\DynamicForm\Form('test');

        $output = $form->process([
            'first_name' => 'Usama',
            'last_name' => 'Dev',
        ]);

        // output should have message and type
        $this->assertArrayHasKey('message', $output);
    }

    /**
     * @throws Exception
     */
    public function testDynamicForm()
    {
        $form = new \App\Generate\DynamicForm\Form([
            'fields' => [
                [
                    'id' => 'name',
                    'label' => 'Name',
                    'type' => 'text',
                    'rules' => 'required',
                ],
            ],
            'steps' => [
                [
                    'step' => 'log',
                    'input' => [
                        'message' => 'My name is {{form.name}}',
                        'type' => 'info',
                    ],
                ],
            ],
            'output' => [
                'message' => 'Form submitted by {{form.name}}',
            ],
        ]);

        $output = $form->process([
            'name' => 'Sheela',
        ]);

        $this->assertArrayHasKey('message', $output);
        $this->assertEquals('Form submitted by Sheela', $output['message']);
    }
}
