<?php

namespace App\Http\Controllers\User;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class AnalyzeController extends Controller
{
    public function index(){
        return redirect()->route('analyze.content-performance');
        // todo: use proper index page
        return view('user.analyze.index');
    }

    /**
     * @deprecated Old report endpoint
     * @return \Illuminate\Http\RedirectResponse
     */
    public function followers(){
        return redirect()->route('analyze.index');
    }

    /**
     * @deprecated Old report endpoint
     * @return \Illuminate\Http\RedirectResponse
     */
    public function topPosts(){
        return redirect()->route('analyze.index');
    }

    public function content(){
        return view('user.analyze.content');
    }
    public function account(){
        return view('user.analyze.account');
    }
    public function network(){
        return view('user.analyze.network');
    }
    public function team(){
        return view('user.analyze.team');
    }
}
