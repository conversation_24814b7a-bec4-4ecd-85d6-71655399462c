@extends('layout.user')
@section('title', 'Settings | ' . config('app.name'))
@section('content')

    <style>
        .limit-card{
            box-shadow: none;
        }
        .limit-card:hover{
            box-shadow: 0 0 25px rgb(0 0 0 / 7%);
        }
        .toggle-password-btn{
            top:49px;
            right:20px;
            color:#999999;
            z-index:10
        }
        .feature-list-item{
            font-size: 1.25rem;
            font-weight: 400;
            margin-bottom: 12px;
        }
        .setting-progress-bar{
            height: 8px;
        }
        .settings-tabs{
            color: #1E283B;
            font-size: 18px !important;
            font-family: "Plus Jakarta Sans";
        }
        .current-plan{
            color: #657085 !important;  
        }
        .plan_type_input{
            border: 1px solid #0557f0 !important;
            border-radius: 12px;
            .active{
                color: #fff !important;
                background-color: #0557f0 !important;
                border-radius: 8px !important;
            }
            .btn{
                border-radius: 8px !important;
                border: none !important;
            }
            .off-text{
                color: #657085 !important;
            }
        }
        @media (max-width: 600px){
            .plan-info{
                width: 100% !important;
            }
            .settings-tabs{
                height: 50px;
                overflow-x: auto;
                overflow-y: hidden;
                scrollbar-width: none;
                white-space: nowrap;
            }
        }
    </style>

    <div class="settings-container invisible">
        <ul class="nav settings-tabs nav-tabs-minimal lead-2 p-0 mb-6 border-md-bottom-0 border-bottom-1 flex-nowrap" role="tablist">
            <li class="nav-item">
                <a class="nav-link active font-weight-600 pl-0 pr-2 py-md-2 py-3" data-toggle="tab" href="#profile">My Profile</a>
            </li>
            <li class="nav-item">
                <a class="nav-link font-weight-600 px-2 py-md-2 py-3" data-toggle="tab" href="#billing">Subscription &amp; Billing</a>
            </li>
            <li class="nav-item">
                <a class="nav-link font-weight-600 px-2 py-md-2 py-3" data-toggle="tab" href="#account">Account</a>
            </li>
            <li class="nav-item">
                <a class="nav-link font-weight-600 px-2 py-md-2 py-3" data-toggle="tab" href="#developers">
                    API
                </a>
            </li>
        </ul>

        <div class="tab-content pt-2">
            <div class="tab-pane fade show active" id="profile" role="tabpanel">
                <div class="col-12 col-md-6 px-0">
                    <form action="{{ route('settings.update', ['type' => 'account']) }}" method="POST">
                        {{ csrf_field() }}
                        {{ method_field('PUT') }}

                        <div class="form-group mb-5">
                            <label for="input_user_name">
                                @lang('settings.full_name')
                            </label>
                            <input id="input_user_name" class="form-control"
                                placeholder="@lang('settings.full_name')" name="name"
                                value="{{ $user->name }}"/>
                        </div>

                        <div class="form-group @if(!$user->verified) has-warning @endif mb-5">
                            <label for="input_user_email">
                                Email
                            </label>
                            <input id="input_user_email" class="form-control"
                                placeholder="Work Email" name="email"
                                value="{{ $user->email }}"/>

                            @if(!$user->verified)
                                <span class="form-text">
                                    @lang('settings.email_unverified_msg', ['resend_link' => '<a href="#" id="resend_verification_email">' . trans('settings.resend_verification_email') . '</a>'])
                                </span>
                            @endif
                        </div>

                        <div class="form-group mb-5">
                            <label for="input_user_phone">
                                Phone Number
                            </label>
                            <input type="tel" id="input_user_phone" class="form-control"
                                placeholder="Example: ******-567-8912" name="phone"
                                value="{{ $user->getPhone() }}"/>
                        </div>

                        <div class="form-group mb-5">
                            <label for="input_user_company">
                                @lang('generic.company')
                            </label>
                            <input id="input_user_company" class="form-control"
                                placeholder="Your Company Name" name="company"
                                value="{{ $user->company }}"/>
                        </div>
                        <div class="form-group mb-5">
                            <label for="input_user_timezone">@lang('generic.timezone')</label>
                            <select id="input_user_timezone" class="form-control" name="timezone">
                                @foreach($timezones as $timezone)
                                    <option value="{{ $timezone }}"
                                            @if($user->getTimezone() === $timezone)
                                                selected
                                        @endif> {{ $timezone }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary btn-sm">@lang('generic.save')</button>
                        </div>

                    </form>
                </div>
            </div>
            <div class="tab-pane fade" id="billing" role="tabpanel">
                <div class="col-12">

                    <!-- cancel popup -->
                    <div class="modal fade" id="cancel_modal" tabindex="-1" role="dialog" aria-labelledby="cancel_modal_label" aria-hidden="true">
                        <div class="modal-dialog" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="cancel_modal_label">Cancel your subscription</h5>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                <div class="modal-body lead">
                                    <div>
                                        <p>
                                            We are sad to see you cancel your subscription. Please share the reason behind your decision. This will help us improve SocialBu.
                                        </p>
                                    </div>
                                    <hr/>
                                    <div class="custom-controls-stacked">
                                        <!--
                                        <div class="custom-control custom-radio">
                                            <input id="reason_better" title="Found something better" value="Found something better" type="radio" class="custom-control-input" name="reason" />
                                            <label for="reason_better" class="custom-control-label">Found something better</label>
                                        </div>

                                        <div class="custom-control custom-radio">
                                            <input id="reason_slow" title="Slow and buggy" value="Slow and buggy" type="radio" class="custom-control-input" name="reason" />
                                            <label for="reason_slow" class="custom-control-label">Slow and buggy</label>
                                        </div>

                                        <div class="custom-control custom-radio">
                                            <input id="reason_expensive" title="Too expensive" value="Too expensive" type="radio" class="custom-control-input" name="reason" />
                                            <label for="reason_expensive" class="custom-control-label">Too expensive</label>
                                        </div>

                                        <div class="custom-control custom-radio">
                                            <input id="reason_bad_support" title="Bad or no customer support" value="Bad or no customer support" type="radio" class="custom-control-input" name="reason" />
                                            <label for="reason_bad_support" class="custom-control-label">Bad or no customer support</label>
                                        </div>
                                        -->
                                        <div class="custom-control custom-radio d-none">
                                            <input id="reason_other" title="Other" value="Other" type="radio" class="custom-control-input" name="reason" checked />
                                            <label class="custom-control-label" for="reason_other">
                                                Other
                                            </label>
                                        </div>
                                        <div class="form-group" id="reason_input_container">
                                            <input class="form-control" type="text" placeholder="Please specify the reason" name="other_reason" />
                                        </div>

                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-light" data-dismiss="modal">Do not cancel</button>
                                    <button type="button" class="btn btn-danger" data-subscription="cancel" data-final="true">Cancel Subscription</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card mb-2">
                            <div class="card-body p-0">
                                <div class="row mb-md-5 mb-4 pb-md-0 pb-1">
                                    <div class="col-12 px-0">
                                        <h3 class="mb-3">
                                            Current Plan: <span class="current-plan">{{ $user->getPlan()['name'] }} {{$user->planIsYearly() ? 'Yearly' : 'Monthly'}}</span> 
                                            @if($user->getActiveDeal())
                                                <span class="badge badge-success">
                                                    @if($user->isLifetimeDeal())
                                                        lifetime deal
                                                    @else
                                                        Deal
                                                    @endif
                                                </span>
                                            @elseif($user->subscribed('default') && $user->subscription('default')->active() && !$user->subscription('default')->onGracePeriod())
                                                <span class="badge badge-success lead-2">active</span>
                                            @endif
                                        </h3>
                                    </div>

                                    <div class="col-12 px-0">
                                        <p class="w-75 plan-info mb-0">
                                            @php($latestPayment = $user->hasIncompletePayment('default') ? $user->subscription('default')->latestPayment() : null)
                                            @if($user->getActiveDeal())
                                                You are using a promo deal. You cannot select another subscription plan without leaving the deal. You can <button class="btn btn-link p-0 btn-sm" type="button" data-subscription="deactivate_deal">Deactivate</button> your deal anytime.
                                            @elseif($user->hasIncompletePayment('default') && $latestPayment)
                                                Your subscription is not active yet.

                                                Please <a class="alert-link border-bottom border-info" href="{{ route('cashier.payment', [$latestPayment->id, 'redirect' => route('settings', ['#billing'])]) }}">complete the payment</a> to activate your subscription.

                                                You can also <button class="btn btn-link p-0 btn-sm" type="button" data-subscription="end">cancel</button> your subscription request.
                                            @elseif($user->subscribed('default') && $user->onTrial('default') && !$user->subscription('default')->cancelled())
                                                You are on trial. All plans come with 7 days free trial. Your trial will expire on {{ $user->subscription('default')->trial_ends_at->toFormattedDateString() }}. You can <button class="btn btn-outline-danger btn-sm" type="button" data-subscription="cancel">cancel</button> your subscription anytime.
                                            @elseif(!$user->subscribed('default') && !$user->subscription('default'))
                                                All plans come with 7 days free trial. Choose a plan and start your trial now. You can cancel anytime during the trial.
                                            @else
                                                @if($user->subscription('default')->cancelled())
                                                    @if($user->subscription('default')->onGracePeriod())
                                                        Your subscription will end on {{ $user->subscription('default')->ends_at->toFormattedDateString() }}. If you have changed your mind, you can <button class="btn btn-primary btn-sm" type="button" data-subscription="resume">resume</button> your subscription.
                                                    @else
                                                        Your current subscription has ended. Select a plan to start your subscription.
                                                    @endif
                                                @else
                                                    You are subscribed to <strong>{{ $user->getPlan()['name'] }}</strong> plan.
                                                @endif
                                            @endif

                                            @if( !$user->subscribed('default') && $user->getOption('stripe_coupon') )
                                                @php($stripeCoupon = $user->getCoupon())
                                                The discount {{ $stripeCoupon ? '(' . $stripeCoupon->name . ')' : '' }} will automatically be applied.
                                            @endif
                                        </p>
                                    </div>
                                </div>
                                <!-- account limits -->
                                <div class="row mt-1 mb-6">
                                    <div class="col-sm-4 pl-md-0 pr-md-2 px-0 mb-md-4 mb-3">
                                        <div class="card border">
                                            <div class="card-body p-md-4 p-20">
                                                <p class="d-md-none font-weight-500 mb-4 pb-1">Social Accounts</p>
                                                <div class="progress setting-progress-bar mb-md-0 mb-2">
                                                    <div class="progress-bar " role="progressbar" aria-valuenow="{{isset($user->getPlan()['limits']['accounts']) ? $user->getUsage()['accounts'] : 0}}"
                                                        aria-valuemin="0" aria-valuemax="{{isset($user->getPlan()['limits']['accounts']) ? $user->getPlan()['limits']['accounts'] : 0}}" style="width: {{isset($user->getPlan()['limits']['accounts']) && $user->getPlan()['limits']['accounts'] ?  $user->getUsage()['accounts']*100/$user->getPlan()['limits']['accounts']. '%' : 0}} ">

                                                    </div>
                                                </div>
                                                <p class="small-2 mb-md-2 mb-0">  {{isset($user->getPlan()['limits']['accounts']) ? $user->getUsage()['accounts'] : 0}} /  {{isset($user->getPlan()['limits']['accounts']) ? $user->getPlan()['limits']['accounts'] : 0}}</p>
                                                <p class="d-md-block d-none mb-0">Social Accounts</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-4 px-md-2 px-0 mb-md-4 mb-3">
                                        <div class="card border">
                                            <div class="card-body p-md-4 p-20">
                                                <p class="d-md-none font-weight-500 mb-4 pb-1">Monthly Posts</p>
                                                <div class="progress setting-progress-bar mb-md-0 mb-2">
                                                    <div class="progress-bar" role="progressbar" aria-valuenow="{{isset($user->getPlan()['limits']['monthly_posts']) ? $user->getUsage()['monthly_posts'] : 0}}"
                                                        aria-valuemin="0" aria-valuemax="{{isset($user->getPlan()['limits']['monthly_posts']) ? $user->getPlan()['limits']['monthly_posts'] : 0 }}" style="width: {{ isset($user->getPlan()['limits']['monthly_posts']) && $user->getPlan()['limits']['monthly_posts'] > 0 ? $user->getUsage()['monthly_posts']*100/$user->getPlan()['limits']['monthly_posts']. '%' : 0 }} ">

                                                    </div>
                                                </div>
                                                <p class="small-2 mb-md-2 mb-0">{{isset($user->getPlan()['limits']['monthly_posts']) ? $user->getUsage()['monthly_posts'] : 0}} / {{ isset($user->getPlan()['limits']['monthly_posts']) ? $user->getPlan()['limits']['monthly_posts'] : 0}}</p>
                                                <p class="d-md-block d-none mb-0">Monthly Posts</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-4 px-md-2 px-0 mb-md-4 mb-3">
                                        <div class="card border">
                                            <div class="card-body p-md-4 p-20">
                                                <p class="d-md-none font-weight-500 mb-4 pb-1">AI Posts</p>
                                                <div class="progress setting-progress-bar mb-md-0 mb-2">
                                                    <div class="progress-bar" role="progressbar" aria-valuenow="{{ isset($user->getPlan()['limits']['generated_content']) ? $user->getUsage()['generated_content'] : 0}}"
                                                        aria-valuemin="0" aria-valuemax="{{isset($user->getPlan()['limits']['generated_content']) ? $user->getPlan()['limits']['generated_content'] : 0}}" style="width: {{ isset($user->getPlan()['limits']['generated_content']) && $user->getPlan()['limits']['generated_content'] > 0 ? $user->getUsage()['generated_content']*100/$user->getPlan()['limits']['generated_content']. '%' : 0 }} ">

                                                    </div>
                                                </div>
                                                <p class="small-2 mb-md-2 mb-0"> {{ isset($user->getPlan()['limits']['generated_content']) ? $user->getUsage()['generated_content'] : 0}} / {{isset($user->getPlan()['limits']['generated_content']) ? $user->getPlan()['limits']['generated_content'] : 0 }}</p>
                                                <p class="d-md-block d-none mb-0">AI Posts</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-4 pl-md-0 pr-md-2 px-0 mb-md-0 mb-3">
                                        <div class="card border">
                                            <div class="card-body p-md-4 p-20">
                                                <p class="d-md-none font-weight-500 mb-4 pb-1">Feeds</p>
                                                <div class="progress setting-progress-bar mb-md-0 mb-2">
                                                    <div class="progress-bar" role="progressbar" aria-valuenow="{{ isset($user->getPlan()['limits']['feeds']) ? $user->getUsage()['feeds'] : 0}}"
                                                        aria-valuemin="0" aria-valuemax="{{isset($user->getPlan()['limits']['feeds']) ? $user->getPlan()['limits']['feeds'] : 0}}" style="width: {{isset($user->getPlan()['limits']['feeds']) && $user->getPlan()['limits']['feeds'] > 0 ? $user->getUsage()['feeds']*100/$user->getPlan()['limits']['feeds']. '%' : 0}} ">
                                                    </div>
                                                </div>
                                                <p class="small-2 mb-md-2 mb-0"> {{isset($user->getPlan()['limits']['feeds']) ? $user->getUsage()['feeds'] : 0}} / {{ isset($user->getPlan()['limits']['feeds']) ? $user->getPlan()['limits']['feeds'] : 0}}</p>
                                                <p class="d-md-block d-none mb-0">Feeds</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-4 px-md-2 px-0 mb-md-0 mb-3">
                                        <div class="card border">
                                            <div class="card-body p-md-4 p-20">
                                                <p class="d-md-none font-weight-500 mb-4 pb-1">Automations</p>
                                                <div class="progress setting-progress-bar mb-md-0 mb-2">
                                                    <div class="progress-bar" role="progressbar" aria-valuenow="{{isset($user->getPlan()['limits']['automations']) ? $user->getUsage()['automations'] : 0}}"
                                                        aria-valuemin="0" aria-valuemax="{{isset($user->getPlan()['limits']['automations']) ? $user->getPlan()['limits']['automations'] : 0}}" style="width: {{ isset($user->getPlan()['limits']['automations']) && $user->getPlan()['limits']['automations'] > 0 ? $user->getUsage()['automations']*100/$user->getPlan()['limits']['automations']. '%' : 0}} ">
                                                    </div>
                                                </div>
                                                <p class="small-2 mb-md-2 mb-0"> {{isset($user->getPlan()['limits']['automations']) ? $user->getUsage()['automations'] : 0}} / {{isset($user->getPlan()['limits']['automations']) ? $user->getPlan()['limits']['automations'] : 0}}</p>
                                                <p class="d-md-block d-none mb-0">Automations</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-4 px-md-2 px-0">
                                        <div class="card border">
                                            <div class="card-body p-md-4 p-20">
                                                <p class="d-md-none font-weight-500 mb-4 pb-1">Teams</p>
                                                <div class="progress setting-progress-bar mb-md-0 mb-2">
                                                    <div class="progress-bar" role="progressbar" aria-valuenow="{{isset($user->getPlan()['limits']['teams']) ? $user->getUsage()['teams'] : 0}}"
                                                        aria-valuemin="0" aria-valuemax="{{isset($user->getPlan()['limits']['teams']) ? $user->getPlan()['limits']['teams'] : 0}}" style="width: {{isset($user->getPlan()['limits']['teams']) && $user->getPlan()['limits']['teams'] > 0 ? $user->getUsage()['teams']*100/$user->getPlan()['limits']['teams']. '%' : 0}} ">

                                                    </div>
                                                </div>
                                                <p class="small-2 mb-md-2 mb-0"> {{isset($user->getPlan()['limits']['teams']) ? $user->getUsage()['teams'] : 0}} / {{isset($user->getPlan()['limits']['teams']) ? $user->getPlan()['limits']['teams'] : 0}}</p>
                                                <p class="d-md-block d-none mb-0">Teams</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- end account limits -->
                                @if(in_array($user->getPlan(true), ['basic', 'standard', 'plus', 'plus-ultra', 'starter', ]))
                                    <div class="row mt-2">
                                        @foreach(config('plans') as $plan => $planDetails)
                                            @if($plan === $user->getPlan(true))
                                                <div class="col-12 col-md-4 mx-auto">
                                                    <div class="card mb-4">
                                                        <div class="card-header">
                                                            <h4 class="my-0 font-weight-normal text-left">
                                                                {{ $planDetails['name'] }}
                                                                @if($user->getOption('subscription_quantity.' . $plan))
                                                                    ({{ $user->getOption('subscription_quantity.' . $plan) }}x)
                                                                @endif
                                                            </h4>
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <h2 class="monthly card-title ">${{ $planDetails['price'] }} <span class="h4">/mo</span></h2>
                                                            <h2 class="yearly card-title  mb-0" style="display: none;">~${{ round($planDetails['price_yearly'] / 12) }} <span class="h4">/mo</span></h2>
                                                            <p class="yearly text-muted mb-0" style="display: none;">
                                                                ${{ $planDetails['price_yearly'] }} billed annually
                                                            </p>
                                                            <p class="lead alert alert-warning">
                                                                This plan is not offered anymore but will continue to work for you unless you choose another plan.
                                                            </p>
                                                            <ul class="list-unstyled mt-3 mb-4 text-left">
                                                                <li class="feature-list-item"><i class="ph ph-check ph-lg"></i> {{ $planDetails['limits']['accounts'] }} accounts</li>
                                                                @if($planDetails['limits']['teams'] === 0)
                                                                    <li class="feature-list-item"><i class="ph ph-x ph-lg"></i> No teams</li>
                                                                @else
                                                                    <li class="feature-list-item"><i class="ph ph-check ph-lg"></i> {{ $planDetails['limits']['teams'] }} teams </li>
                                                                @endif
                                                                <li class="feature-list-item">
                                                                    @if(in_array($plan, ['super', 'supreme']))
                                                                        <i class="ph ph-check ph-lg"></i>
                                                                    @else
                                                                        <i class="ph ph-x ph-lg"></i> No
                                                                    @endif
                                                                    Phone Support
                                                                </li>
                                                            </ul>
                                                            @if(!$user->hasPaymentMethod())
                                                                <button type="button" class="btn btn-lg btn-block btn-outline-primary" data-subscription="start" data-plan="{{ $plan }}">
                                                                    Get Started
                                                                </button>
                                                            @elseif($user->getPlan(true) === $plan)
                                                                @if($user->subscribedToPlan($plan, 'default'))
                                                                    <button type="button" class="monthly btn btn-sm btn-block btn-outline-danger" data-subscription="cancel" @if($user->subscription('default')->cancelled()) disabled @endif>
                                                                        Cancel
                                                                    </button>
                                                                    <button type="button" class="yearly btn btn-sm btn-block btn-outline-primary" data-subscription="select" data-plan="{{ $plan }}">
                                                                        Select Plan (yearly)
                                                                    </button>
                                                                @else
                                                                    <button type="button" class="yearly btn btn-sm btn-block btn-outline-danger" data-subscription="cancel" @if($user->subscription('default')->cancelled()) disabled @endif>
                                                                        Cancel
                                                                    </button>
                                                                    <button type="button" class="monthly btn btn-sm btn-block btn-outline-primary" data-subscription="select" data-plan="{{ $plan }}">
                                                                        Select Plan (monthly)
                                                                    </button>
                                                                @endif
                                                            @else
                                                                <button type="button" class="btn btn-sm btn-block btn-outline-primary" data-subscription="select" data-plan="{{ $plan }}">
                                                                    Select Plan
                                                                </button>
                                                            @endif
                                                            @if(!$user->subscription('default'))
                                                                <div class="badge text-dark">7 days free trial</div>
                                                            @endif
                                                            <div class="mt-2 text-center">
                                                                <a class="btn btn-link" href="/pricing" target="_blank">
                                                                    Details <i class="ph ph-arrow-up-right"></i>
                                                                </a>
                                                            </div>
                                                        </div>
                                                        
                                                    </div>
                                                </div>
                                            @endif
                                        @endforeach
                                    </div>
                                    <hr/>
                                @endif
                            </div>
                        </div>    

                    <div class="card">
                        <div class="card">
                            <div class="card-body p-0">
                                <div class="row">
                                    <div class="col-md-12 text-center pb-4">
                                        <div class="plan_type_input btn-group btn-group-xs btn-group-toggle p-1" data-toggle="buttons">
                                            <label class="btn btn-outline-light btn-xs @if(!$user->planIsYearly()) active @endif">
                                                <input type="radio" name="plan_type" value="monthly" autocomplete="off" @if(!$user->planIsYearly()) checked @endif /> Pay monthly
                                            </label>
                                            <label class="btn btn-outline-light btn-xs @if($user->planIsYearly()) active @endif" data-tour="id=save_on_yearly_plan;title=Save money on yearly plans;text=If you choose yearly plans, you get 2 months for free">
                                                <input type="radio" name="plan_type" value="yearly" autocomplete="off" @if($user->planIsYearly()) checked @endif />Pay yearly
                                            </label>
                                        </div>
                                        <p class="off-text small-2 mt-3 pb-1">2 months off on yearly plans</p>
                                    </div>
                                </div>
                                <div class="row mb-6">
                                    <div class="col-md-12 pl-4 pr-md-0 pr-2">
                                        <div class="row">

                                            @foreach(config('plans') as $plan => $planDetails)
                                                @if(in_array($plan, ['standard2', 'super', 'supreme',]))
                                                    <div class="col-12 col-md-6 pl-0  mb-md-4 mb-3">
                                                        <div class="card border @if($plan === 'super') popular @endif rounded-xl">
                                                            <div class="card-body">
                                                                <h6>
                                                                    {{ $planDetails['name'] }}
                                                                    @if($user->getOption('subscription_quantity.' . $plan))
                                                                        ({{ $user->getOption('subscription_quantity.' . $plan) }}x)
                                                                    @endif
                                                                    @if($plan === 'super')
                                                                        <span class="badge badge-success float-right font-size-16">MOST POPULAR</span>
                                                                    @endif
                                                                </h6>
                                                                <h3 class="monthly">${{ $planDetails['price'] }}<span class="h4">/mo</span></h3>
                                                                <h3 class="yearly" style="display: none;">~${{ round($planDetails['price_yearly'] / 12) }}/mo</h3>
                                                                <p class="yearly text-muted mb-0" style="display: none;">
                                                                    ${{ $planDetails['price_yearly'] }} billed annually
                                                                </p>
                                                                <div class="py-20">
                                                                    <hr class="m-0">
                                                                </div>
                                                                <ul class="list-unstyled mb-6 text-left">
                                                                    <li class="d-flex align-items-center feature-list-item"><i class="ph ph-check-circle ph-fill ph-md text-primary mr-3"></i> {{ $planDetails['limits']['accounts'] }} accounts</li>
                                                                    @if($planDetails['limits']['teams'] === 0)
                                                                        <li class="d-flex align-items-center feature-list-item"><i class="ph ph-x-circle ph-fill ph-md text-light-gray mr-3"></i> No teams</li>
                                                                    @else
                                                                        <li class="d-flex align-items-center feature-list-item"><i class="ph ph-check-circle ph-fill ph-md text-primary mr-3"></i> {{ $planDetails['limits']['teams'] }} teams </li>
                                                                    @endif
                                                                    <li class="d-flex align-items-center feature-list-item">
                                                                        @if(in_array($plan, ['super', 'supreme']))
                                                                            <i class="ph ph-check-circle ph-fill ph-md text-primary mr-3"></i>
                                                                        @else
                                                                            <i class="ph ph-x-circle ph-fill ph-md text-light-gray mr-3"></i> No
                                                                        @endif
                                                                        Phone Support
                                                                    </li>
                                                                </ul>
                                                                @if(!$user->hasPaymentMethod())
                                                                    <button type="button" class="btn btn-lg btn-block btn-outline-primary" data-subscription="start" data-plan="{{ $plan }}">
                                                                        Get Started
                                                                    </button>
                                                                @elseif($user->getPlan(true) === $plan)
                                                                    @if($user->subscribedToPlan($plan, 'default'))
                                                                        <button type="button" class="monthly btn btn-lg btn-block btn-outline-danger" data-subscription="cancel" @if($user->subscription('default')->cancelled()) disabled @endif>
                                                                            Cancel
                                                                        </button>
                                                                        <button type="button" class="yearly btn btn-lg btn-block btn-outline-primary" data-subscription="select" data-plan="{{ $plan }}">
                                                                            Select Plan (yearly)
                                                                        </button>
                                                                    @else
                                                                        <button type="button" class="yearly btn btn-lg btn-block btn-outline-danger" data-subscription="cancel" @if($user->subscription('default')->cancelled()) disabled @endif>
                                                                            Cancel
                                                                        </button>
                                                                        <button type="button" class="monthly btn btn-lg btn-block btn-outline-primary" data-subscription="select" data-plan="{{ $plan }}">
                                                                            Select Plan (monthly)
                                                                        </button>
                                                                    @endif
                                                                @else
                                                                    <button type="button" class="btn btn-lg btn-block btn-outline-primary" data-subscription="select" data-plan="{{ $plan }}">
                                                                        Select Plan
                                                                    </button>
                                                                @endif
                                                                <div class="d-flex align-items-center justify-content-between mt-3">

                                                                    @if(!$user->subscription('default'))
                                                                        <div class="">7 days free trial</div>
                                                                    @endif
                                                                    <a class="btn btn-link p-0" href="/pricing" target="_blank">
                                                                        Details <i class="ph ph-arrow-up-right"></i>
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                @endif
                                            @endforeach
                                            <div class="col-12 col-md-6 pl-0 mb-md-4 mb-3">
                                                <div class="row">

                                                    @foreach(config('plans') as $plan => $planDetails)
                                                        @if(in_array($plan, ['free']))
                                                            <div class="col-12">
                                                                <div class="card border">
                                                                    <div class="card-body">
                                                                        <h6>
                                                                            {{ $planDetails['name'] }}
                                                                            @if($user->getOption('subscription_quantity.' . $plan))
                                                                                ({{ $user->getOption('subscription_quantity.' . $plan) }}x)
                                                                            @endif
                                                                        </h6>
                                                                        @if($plan === 'free')
                                                                            <h3 class="">FREE Forever</h3>
                                                                        @else
                                                                            <h3 class="monthly">${{ $planDetails['price'] }} <span class="h4">/mo</span></h3>
                                                                            <h3 class="yearly" style="display: none;">~${{ round($planDetails['price_yearly'] / 12, 2) }} <span class="h4">/mo</span></h3>
                                                                            <p class="yearly text-muted mb-0" style="display: none;">
                                                                                ${{ $planDetails['price_yearly'] }} billed annually
                                                                            </p>
                                                                        @endif
                                                                        <div class="py-20">
                                                                            <hr class="m-0">
                                                                        </div>
                                                                        <ul class="list-unstyled mb-6">
                                                                            <li class="d-flex align-items-center feature-list-item"><i class="ph ph-check-circle ph-fill ph-md text-primary mr-3"></i> {{ $planDetails['limits']['accounts'] }} accounts</li>
                                                                            @if($planDetails['limits']['teams'] === 0)
                                                                                <li class="d-flex align-items-center feature-list-item"><i class="ph ph-x-circle ph-fill ph-md text-light-gray mr-3"></i> No teams</li>
                                                                            @else
                                                                                <li class="d-flex align-items-center feature-list-item"><i class="ph ph-check-circle ph-fill ph-md text-primary mr-3"></i> {{ $planDetails['limits']['teams'] }} teams </li>
                                                                            @endif
                                                                            <li class="d-flex align-items-center feature-list-item">
                                                                                @if(in_array($plan, ['super', 'supreme']))
                                                                                    <i class="ph ph-check-circle ph-fill ph-md text-primary mr-3"></i>
                                                                                @else
                                                                                    <i class="ph ph-x-circle ph-fill ph-md text-light-gray mr-3"></i> No
                                                                                @endif
                                                                                Phone Support
                                                                            </li>
                                                                        </ul>
                                                                        @if($plan === 'free')
                                                                            @if($user->getPlan(true) === 'free')
                                                                                <button type="button" class="btn btn-lg btn-block btn-outline-success" disabled>
                                                                                    Selected
                                                                                </button>
                                                                            @else
                                                                                <button type="button" class="btn btn-lg btn-block btn-outline-primary" data-subscription="free" data-plan="{{ $plan }}">
                                                                                    Select Plan
                                                                                </button>
                                                                            @endif
                                                                        @else
                                                                            @if(!$user->hasPaymentMethod())
                                                                                <button type="button" class="btn btn-lg btn-block btn-outline-primary" data-subscription="start" data-plan="{{ $plan }}">
                                                                                    Get Started
                                                                                </button>
                                                                            @elseif($user->getPlan(true) === $plan)
                                                                                @if($user->subscribedToPlan($plan, 'default'))
                                                                                    <button type="button" class="monthly btn btn-sm btn-block btn-outline-danger" data-subscription="cancel" @if($user->subscription('default')->cancelled()) disabled @endif>
                                                                                        Cancel
                                                                                    </button>
                                                                                    <button type="button" class="yearly btn btn-sm btn-block btn-outline-primary" data-subscription="select" data-plan="{{ $plan }}">
                                                                                        Select Plan (yearly)
                                                                                    </button>
                                                                                @else
                                                                                    <button type="button" class="yearly btn btn-lg btn-block btn-outline-danger" data-subscription="cancel" @if($user->subscription('default')->cancelled()) disabled @endif>
                                                                                        Cancel
                                                                                    </button>
                                                                                    <button type="button" class="monthly btn btn-lg btn-block btn-outline-primary" data-subscription="select" data-plan="{{ $plan }}">
                                                                                        Select Plan (monthly)
                                                                                    </button>
                                                                                @endif
                                                                            @else
                                                                                <button type="button" class="btn btn-lg btn-block btn-outline-primary" data-subscription="select" data-plan="{{ $plan }}">
                                                                                    Select Plan
                                                                                </button>
                                                                            @endif
                                                                            @if(!$user->subscription('default'))
                                                                                <div class="badge text-dark">7 days free trial</div>
                                                                            @endif
                                                                        @endif
                                                                        
                                                                        <div class="mt-3 text-center">
                                                                            <a class="btn btn-link p-0" href="/pricing" target="_blank">
                                                                                Details <i class="ph ph-arrow-up-right"></i>
                                                                            </a>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        @endif
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mb-6 pt-2">
                                    <div class="col-12 col-md-6 pr-md-4 pl-0 pr-0">

                                        <div class="card border mb-4">
                                            <div class="card-body">
                                                <h4 class="mb-4">
                                                    Payment Method
                                                </h4>
                                                @if($user->hasPaymentMethod())
                                                    <div class="pb-5" id="card_details">
                                                        Name on Card: {{ $user->getOption('payment.name') }}<br/>
                                                        Card Number: **** **** **** {{ $user->card_last_four }}<br/>
                                                        Card Type: {{ $user->card_brand }}
                                                        @if($credit > 0)
                                                            <hr/>
                                                            Credit: ${{ $credit }}
                                                        @endif
                                                    </div>
                                                @else
                                                    <div class="pb-20">No payment method found.</div>
                                                @endif
                                                <button class="btn btn-outline-primary btn-sm" data-toggle="modal" data-target="#stripe_card_modal">
                                                    Update
                                                </button>
                                            </div>
                                        </div>

                                        @if($user->hasPaymentMethod())
                                            <div class="card border mb-4">
                                                <div class="card-body">
                                                    <h3>Invoices</h3>
                                                    @if($invoices)
                                                        @if($invoices->count() > 0)
                                                            <table class="table table-striped table-light">
                                                                @foreach ($invoices as $invoice)
                                                                    @if($invoice->rawTotal() > 0 || 1)
                                                                        <tr>
                                                                            <td>{{ $invoice->date()->toFormattedDateString() }}</td>
                                                                            <td>{{ $invoice->total() }}</td>
                                                                            <td><a href="/app/settings/invoices/{{ $invoice->id }}">Download</a></td>
                                                                        </tr>
                                                                    @endif
                                                                @endforeach
                                                            </table>
                                                        @else
                                                            <div class="alert alert-info">No invoices found.</div>
                                                        @endif
                                                    @else
                                                        <a class="btn btn-outline-secondary" href="/app/settings?invoices=true#billing">Load</a>
                                                    @endif
                                                </div>
                                            </div>
                                        @endif

                                    </div>
                                    <div class="col-12 col-md-6 px-0">
                                        <div class="card border">
                                            <div class="card-body">
                                                <h6 class="mb-0">
                                                    Promo Code
                                                </h6>
                                                <div>
                                                    <form action="{{ route('settings.update', ['type' => 'promo']) }}" method="POST">
                                                        {{ csrf_field() }}
                                                        {{ method_field('PATCH') }}
                                                        <div class="pb-20">
                                                            <input name="code" type="text" class="form-control" placeholder="Promo" aria-label="Promo" />
                                                        </div>
                                                        <button class="btn btn-light btn-sm rounded-md" type="submit">Redeem</button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>

                    <!--
                    <div class="row mb-5">
                        <div class="card shadow">
                            <div class="card-body">
                                <h2>Need help?</h2>
                                <p class="lead">
                                    We are here to help you. Use the live chat to connect with our live support. If you want, you can also drop an email to <span class="badge badge-secondary"><EMAIL></span>.
                                </p>
                            </div>
                        </div>
                    </div>
                    -->
                </div>

                <!-- Modal for stripe card -->
                <div class="modal fade" id="stripe_card_modal" tabindex="-1" role="dialog" aria-hidden="true">
                    <div class="modal-dialog" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Setup your payment profile</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true"><i class="ph ph-x ph-lg"></i></span>
                                </button>
                            </div>
                            <div class="modal-body">

                                <div class="container-fluid">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="form-row">
                                                <div class="form-group col-12">
                                                    <label for="card-element">
                                                        Credit or debit card
                                                    </label>
                                                    <div class="form-control" id="card-element">
                                                        <!-- A Stripe Element will be inserted here. -->
                                                    </div>
                                                    <!-- Used to display Element errors. -->
                                                    <small id="card-errors" role="alert" class="alert alert-danger" style="display:none"></small>
                                                </div>
                                            </div>
                                            <div class="form-row">
                                                <div class="form-group col-8">
                                                    <label for="card-holder">
                                                        Name on Card
                                                    </label>
                                                    <input class="form-control card_name" id="card-holder" type="text" required />
                                                </div>
                                                <div class="form-group col-4">
                                                    <label for="card-country">
                                                        Country
                                                    </label>
                                                    <select class="form-control country" title="Country">
                                                        @foreach(getCountries() as $code => $country)
                                                            <option value="{{ $code }}">{{ $country }}</option>
                                                        @endforeach
                                                        <option value="00">Other</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-primary btn-sm card-submit">
                                    @if($user->hasPaymentMethod())
                                        Save
                                    @else
                                        Proceed
                                    @endif
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
            <div class="tab-pane fade" id="account" role="tabpanel">
                <div class="col-12 col-md-6 px-0">
                    <h3 class="mb-6 pb-2">
                        Change Password
                    </h3>
                    <form action="{{ route('settings.update', ['type' => 'password']) }}" method="POST">
                        {{ csrf_field() }}
                        {{ method_field('PATCH') }}

                        @if($user->password !='no_password')
                            <div class="form-group mb-5">
                                <label for="input_current_pass">
                                    @lang('settings.current_password')
                                </label>
                                <input id="input_current_pass" class="form-control" name="current_password"
                                       placeholder="******" type="password"/>
                            </div>
                        @endif
                        <div class="form-group position-relative mb-5">
                            <label for="password">
                                @lang('settings.new_password')
                            </label>
                            <input id="password" class="form-control position-relative" name="new_password" type="password"/>
                            <i class="ph ph-eye ph-md position-absolute cursor-pointer toggle-password-btn" id="togglePassword">
                            </i>
                        </div>

                        <div class="form-group position-relative mb-5">
                            <label for="passwordConfirmation">
                                @lang('settings.retype_password')
                            </label>
                            <input id="passwordConfirmation" class="form-control" name="new_password_confirmation"
                                   type="password"/>
                            <i class="ph ph-eye ph-md position-absolute cursor-pointer toggle-password-btn" id="togglePasswordConfirmation">
                            </i>
                        </div>

                        <div class="form-group mb-6">
                            <button type="submit" class="btn btn-primary btn-sm">@lang('generic.save')</button>
                        </div>

                    </form>
                </div>
                <div class="col-12 mt-2 px-0">
                    <h3>
                        Account Deletion
                    </h3>
                    <p class="d-md-none mb-4">This will permanently delete your account and all of its data. You will not be able to reactivate this account.</p>
                    <button type="button" class="btn btn-outline-danger " id="delete_account">Delete your account</button>
                </div>
            </div>
            <div class="tab-pane fade" id="developers" role="tabpanel">

                <div class="col-12 col-md-6 px-0">
                    <h3>
                        API for Developers
                    </h3>
                    <p>
                        Use our API to integrate SocialBu with your application or third-party services.
                    </p>

                    <p>
                        <a href="/developers/docs" target="_blank">API Documentation</a>
                    </p>
                    <!-- show api token -->
                    <div class="form-group">
                        <label for="api_token">
                            Access Token
                        </label>

                        <input id="api_token" class="form-control" value="{{ user()->getApiToken(false, true) ?? 'NOT AVAILABLE' }}" readonly onclick="this.select()" />
                        <div class="mt-4">
                            <form method="POST" action="{{ route('settings.regenerate_api_token') }}">
                                {{ csrf_field() }}
                                <!-- show copy and regenerate button -->
                                <button class="btn btn-light" type="button" onclick="navigator.clipboard.writeText(document.getElementById('api_token').value)">
                                    <i class="ph ph-copy"></i> Copy
                                </button>

                                <button class="btn btn-light" type="submit">
                                    <i class="ph ph-repeat"></i> Regenerate
                                </button>
                            </form>

                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    @push('footer_html')
        <script src="https://js.stripe.com/v3/"></script>
        <script>
            __loadScript("settings");
        </script>
    @endpush

@endsection
