/*

 Vue.component("example", function (resolve) {
 // This special require syntax will instruct Webpack to
 // automatically split your built code into bundles which
 // are loaded over Ajax requests.
 require(["vue/example.vue"], resolve)
 });

 */

import Vue from "vue";
import Nl2br from "vue-nl2br";
import AsyncComputed from "vue-async-computed";
import Sortable from "sortablejs";
import moment from "moment-timezone";
import lang from "./../lang";
import { appConfig, spinnerHtml } from "../components";
import tour from "./../tour";
import PortalVue from "portal-vue";
import lozad from "lozad";

Vue.use(AsyncComputed, {
    lazy: true
});

Vue.use(PortalVue);

Vue.component("nl2br", Nl2br);
Vue.prototype.$lang = lang;
Vue.prototype.$bus = new Vue(); // event bus (to be used where needed)

Vue.prototype.$moment = moment;
Vue.prototype.$momentUTC = (timestamp, format) => {
    if (timestamp) {
        if (format) return moment.utc(timestamp, format).tz(appConfig.timezone);
        else return moment.utc(timestamp).tz(appConfig.timezone);
    } else {
        return moment.utc().tz(appConfig.timezone);
    }
};
Vue.prototype.$setupDateTimePicker = (elem, format, onChange, options = null) => {
    options = options || {};
    options.format = format;
    if (!options.timeZone) {
        options.timeZone = appConfig.timezone;
    }
    $(elem)
        .on("change.datetimepicker", e => {
            e.date && onChange && onChange(e.date.format(format));
            !e.date && onChange && onChange(null);
        })
        .datetimepicker(options);
    const fn = $(elem).datetimepicker;
    const api = $(elem).data("datetimepicker");
    if ($(elem).is("input")) {
        $(elem)
            .on("focus.datetimepicker", e => {
                $(elem).datetimepicker("show");
            })
            .on("blur.datetimepicker", e => {
                $(elem).datetimepicker("hide");
            });
    }
    return {
        set(date) {
            $(elem)
                .data("datetimepicker")
                .date(date);
            onChange && onChange(this.$momentUTC(date).format(format));
        },
        fn,
        api
    };
};
Vue.prototype.$pagination = (c, m) => {
    let current = c,
        last = m,
        delta = 2,
        left = current - delta,
        right = current + delta + 1,
        range = [],
        rangeWithDots = [],
        l;

    for (let i = 1; i <= last; i++) {
        if (i === 1 || i === last || (i >= left && i < right)) {
            range.push(i);
        }
    }

    for (let i of range) {
        if (l) {
            if (i - l === 2) {
                rangeWithDots.push(l + 1);
            } else if (i - l !== 1) {
                rangeWithDots.push("...");
            }
        }
        rangeWithDots.push(i);
        l = i;
    }

    return rangeWithDots;
};

let _sortables = [];
Vue.directive("sortable", {
    bind(el, binding) {
        let opts = {};
        if (typeof binding.value === "object") opts = binding.value;
        else if (typeof binding.value === "function") opts.onSort = binding.value;
        let sortable = Sortable.create(el, opts);
        el.dataset.sortable = _sortables.length;
        _sortables.push(sortable);
    },
    unbind(el) {
        let _id = Number(el.dataset.sortable);
        if (_sortables[_id]) {
            _sortables[_id].destroy();
            _sortables[_id] = null;
        }
        let _allNull = true;
        _sortables.forEach(s => {
            if (s !== null) _allNull = false;
        });
        if (_allNull) {
            _sortables = [];
        }
    }
});

let _lozadInstance = lozad();
Vue.directive("lazy", {
    bind(el, binding) {
        if (el.tagName.toLowerCase() === "img") {
            el.setAttribute("data-src", el.src);
            el.src = "";
            el.removeAttribute("src");
            el.classList.add("lozad");
        } else if (el.tagName.toLowerCase() === "video") {
            const $sources = $(el).find("source");
            if (!$sources.length && el.src) {
                el.setAttribute("data-src", el.src);
                el.src = "";
                el.removeAttribute("src");
                el.classList.add("lozad");
            } else if ($sources.length) {
                $sources.each(function() {
                    if (this.src) {
                        this.setAttribute("data-src", this.src);
                        this.src = "";
                        this.removeAttribute("src");
                        el.classList.add("lozad");
                    }
                });
            }
        }
    },
    inserted(el) {
        const observer = () => {
            if (document.body.contains(el)) {
                _lozadInstance.observe();
                return;
            }
            setTimeout(observer, 100);
        };
        observer();
    }
});

Vue.directive("tooltip", {
    bind(el, binding) {
        let opts = {};
        let tooltip_timeout = null;
        if (typeof binding.value === "object") {
            opts = binding.value;
            tooltip_timeout = binding.value.timeout ? binding.value.timeout : null;
        }else if (typeof binding.value === "string"){ 
            opts.title = binding.value;
        }
        if (binding.arg) opts.placement = binding.arg;
        opts.container = "body";
        $(el).tooltip(opts);
        $(el).on("click.tooltip_autohide", () => {
            setTimeout(() => {
                try {
                    $(el).tooltip("hide");
                } catch (e) {}
            }, 200);
        });
        $(el).on('shown.bs.tooltip', function () {
            if(tooltip_timeout) {
                setTimeout(function () {
                    $(el).tooltip('hide');
                }, tooltip_timeout);
            }
         })
    },
    componentUpdated(el, binding) {
        if (typeof binding.value === "string")
            $(el)
                .attr("title", binding.value)
                .tooltip("_fixTitle");
    },
    unbind(el) {
        setTimeout(() => {
            $(el)
                .off("click.tooltip_autohide")
                .tooltip("dispose");
        }, 500);
    }
});

Vue.directive("popover", {
    bind(el, binding) {
        let opts = {};
        if (typeof binding.value === "object") opts = binding.value;
        else if (typeof binding.value === "string") opts.content = binding.value;
        if (binding.arg) opts.trigger = binding.arg;
        opts.container = "body";
        $(el).popover(opts);
    },
    unbind(el) {
        setTimeout(() => {
            $(el).popover("dispose");
        }, 500);
    }
});

Vue.directive("datetimepicker", {
    bind(el, binding) {
        let opts = {};
        if (typeof binding.value === "object") opts = binding.value;
        else if (typeof binding.value === "string") opts.format = binding.value;
        Vue.prototype.$setupDateTimePicker(el, opts.format, opts.onChange);
    },
    unbind(el) {
        $(el).datetimepicker("destroy");
    }
});

Vue.directive("tour", {
    inserted(el, binding) {
        let opts = {};
        if (typeof binding.value === "object") opts = binding.value;
        else if (typeof binding.value === "string") opts.text = binding.value;
        if (binding.arg) opts.id = binding.arg;
        if (binding.modifiers) {
            Object.keys(binding.modifiers).forEach(k => {
                if (["top", "bottom", "left", "right"].includes(k)) {
                    opts.position = k;
                } else {
                    opts[k] = true;
                }
            });
        }

        if (opts.noposition) {
            opts.position = "";
        }

        const $elem = $(el);

        const addStep = () => {
            tour.addStep(opts.id, opts.text, el, {
                position: opts.position,
                title: opts.title,
                noArrow: opts.noarrow
            });
        };

        if ($elem.is(":hidden")) {
            // we check force to see if it should really be added. if yes, add attribute instead, so jquery can show instead
            if (opts.force && $elem.closest(".modal").length) {
                // is in modal, so just add attribute and it will be handled by modal open even
                const str = Object.keys(opts)
                    .map(k => k + "=" + opts[k])
                    .join(";");
                $elem.attr("data-tour", str);
                return;
            } else {
                // so elem is hidden, set up a timer which can check again and again, as some ui components load hidden and then show
                let timer;
                let count = 1;
                let fn;
                fn = () => {
                    if (!$elem.is(":hidden")) {
                        // no more hidden
                        addStep();
                        return;
                    }
                    ++count;
                    if (count >= 40) {
                        // to calculate how many seconds would have been passed, see the following line
                        // let c = 0;let f = ()=> c++; let sum = 0; for(let i =0;i<40;i++) {f(); sum += c}
                        // stop waiting any more
                        return;
                    }
                    timer = setTimeout(fn, count * 1000);
                };
                fn();
            }
        } else {
            addStep();
        }
    },
    unbind(el, binding) {
        let opts = {};
        if (typeof binding.value === "object") opts = binding.value;
        else if (typeof binding.value === "string") opts.text = binding.value;
        if (binding.arg) opts.id = binding.arg;
        tour.removeStep(opts.id);
    }
});

const stopProp = event => {
    event.stopPropagation();
};
Vue.directive("click-outside", {
    bind(el, binding) {
        el.addEventListener("click", stopProp);
        document.body.addEventListener("click", binding.value);
    },
    unbind(el, binding) {
        el.removeEventListener("click", stopProp);
        document.body.removeEventListener("click", binding.value);
    }
});

Vue.directive("selectpicker", {
    inserted(el, binding) {
        $(el).selectpicker(binding.value || {});
    },
    componentUpdated(el) {
        $(el).selectpicker("refresh");
    },
    unbind(el) {
        $(el).selectpicker("destroy");
    }
});

// select2
Vue.directive("select2", {
    inserted(el, binding) {
        const $el = $(el);
        const _value = binding.value || {};

        const $modal = $el.closest(".modal");

        if (_value.config) {
            if($modal.length){
                _value.config.dropdownParent = $modal;
            }
            $el.select2(_value.config);
        } else {
            if($modal.length){
                _value.dropdownParent = $modal;
            }
            $el.select2(_value);
        }

        if (_value.onChange) {
            $el.on("select2:select", () => {
                _value.onChange($el.val(), el);
            });
            $el.on("select2:unselect", () => {
                _value.onChange($el.val(), el);
            });
        }
        if (_value.onSelect)
            $el.on("select2:select", d => {
                return _value.onSelect(d, el);
            });
        if (_value.onSelecting)
            $el.on("select2:selecting", d => {
                return _value.onSelecting(d, el);
            });
    },
    componentUpdated(el) {
        $(el).trigger("change");
    },
    unbind(el) {
        $(el).select2("destroy");
    }
});

// generic fn
Vue.directive("jqueryCallback", {
    inserted(el, binding) {
        if (typeof binding.value === "function") {
            binding.value($(el));
        } else if (typeof binding.value === "object") {
            binding.value.init($(el));
        }
    },
    componentUpdated(el, binding) {
        if (typeof binding.value === "object") {
            binding.value.update($(el));
        }
    },
    unbind(el, binding) {
        if (typeof binding.value === "object") {
            binding.value.destroy($(el));
        }
    }
});

// Register a global custom directive called `v-focus`
Vue.directive("focus", {
    // When the bound element is inserted into the DOM...
    inserted: function(el) {
        // Focus the element
        el.focus();
    }
});

// unique runtime uuid for each component instance
Vue.mixin({
    beforeCreate: (() => {
        let uuid = 0;
        return function() {
            this.uuid = uuid.toString();
            uuid += 1;
        };
    })()
});

// to render vue components
window.__loadComponent = (name, selector, cb = null) => {
    const el = document.querySelector(selector);
    if (!el) return console.error("Selector " + selector + " not found in document.");
    el.innerHTML = spinnerHtml; // default loading spinner
    const mountCb = c => {
        if(c.default){
            c = c.default; // fix after mix upgrade
        }
        const instance = new (Vue.extend(c))().$mount(el);
        cb && cb(instance);
    };
    if (name === "post-editor") {
        import("./components/PostEditor.vue").then(mountCb);
    } else if (name === "scheduled-posts" || name === "posts") {
        import("./components/Posts.vue").then(mountCb);
    } else if (name === "feed") {
        import("./components/Feed.vue").then(mountCb);
    } else if (name === "automations") {
        import("./components/Automations.vue").then(mountCb);
    } else if (name === "automation") {
        import("./components/Automation.vue").then(mountCb);
    } else if (name === "team") {
        import("./components/Team.vue").then(mountCb);
    } else if (name === "calendar") {
        import("./components/Calendar.vue").then(mountCb);
    } else if (name === "history") {
        import("./components/History.vue").then(mountCb);
    } else if (name === "bulk_upload" || name === "bulk-upload") {
        import("./components/BulkUpload.vue").then(mountCb);
    } else if (name === "publish_queue" || name === "publish-queue") {
        import("./components/PublishQueue.vue").then(mountCb);
    } else if (name === "extension-editor") {
        import("./components/ExtensionEditor.vue").then(mountCb);
    } else if (name === "link-shortener") {
        import("./components/LinkShortener.vue").then(mountCb);
    } else if (name === "notifications") {
        import("./components/Notifications.vue").then(mountCb);
    } else if (name === "generated_content" || name === "generated-content") {
        import("./components/GeneratedContent.vue").then(mountCb);
    } else if (name === "onboarding") {
        import("./components/Onboarding.vue").then(mountCb);
    } else if (name === "report") {
        import("./components/Report.vue").then(mountCb);
    } else if (name === "dashboard") {
        import("./components/Dashboard.vue").then(mountCb);
    } else if (name === "chat-bu") {
        import("./components/ChatBu.vue").then(mountCb);
    } else if (name === "curation") {
        import("./components/Curation.vue").then(mountCb);
    } else if (name === "generate_tool") {
        import("./components/tools/GenerateTool.vue").then(mountCb);
    }
};
