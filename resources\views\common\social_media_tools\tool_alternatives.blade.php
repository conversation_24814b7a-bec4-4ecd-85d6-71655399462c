@extends('layout.full_width')
@section('title', count($list) . ' ' . $tool['name'] . ' Alternatives: Social Media Scheduling Tools | ' . config('app.name'))
@push('head_html')

    <meta name="description" content="{{ $tool['name'] }} alternatives that you can try for your social media scheduling needs. An overview of {{ count($list) }} {{ $tool['name'] }} alternatives so you can compare them to find the best one for your requirements."/>
    <meta property="og:locale" content="en_US" />
    <meta property="og:type" content="website" />
    <meta property="og:title" content="{{ count($list) }} {{ $tool['name'] }} Alternatives To Try" />
    <meta property="og:description" content="{{ $tool['name'] }} alternatives that you can try for your social media scheduling needs. An overview of {{ count($list) }} {{ $tool['name'] }} alternatives so you can compare them to find the best one for your requirements." />
    <meta property="og:url" content="https://socialbu.com/social-media-scheduling-tools/{{ $tool['slug'] }}-alternatives" />
    <meta property="og:site_name" content="SocialBu" />
    <meta property="og:image" content="https://og-image.osamaejaz1.now.sh/%3Cdiv%20style%3D%22color%3A%236699fd%3B%22%3E{{ rawurlencode($tool['name'] . ' alternatives') }}%3C%2Fdiv%3E.png?theme=light&md=1&fontSize=100px&images=https://socialbu.com/images/logo-icon.png" />
    <meta property="fb:app_id" content="{{ config('services.facebook.client_id') }}" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content="https://og-image.osamaejaz1.now.sh/%3Cdiv%20style%3D%22color%3A%236699fd%3B%22%3E{{ rawurlencode($tool['name'] . ' alternatives') }}%3C%2Fdiv%3E.png?theme=light&md=1&fontSize=100px&images=https://socialbu.com/images/logo-icon.png" />
    <meta name="twitter:title" content="{{ count($list) }} {{ $tool['name'] }} Alternatives To Try" />
    <meta name="twitter:description" content="{{ $tool['name'] }} alternatives that you can try for your social media scheduling needs. An overview of {{ count($list) }} {{ $tool['name'] }} alternatives so you can compare them to find the best one for your requirements." />
    <meta name="twitter:site" content="@socialbuapp" />


    <link rel="canonical" href="https://socialbu.com/social-media-scheduling-tools/{{ $tool['slug'] }}-alternatives" />

@endpush
@section('content')
    <header class="header text-center pb-0">
        <div class="container">
            <h1 class="display-4">{{ $tool['name'] }} Alternatives</h1>
            <p class="lead-2 mt-6">A quick overview of {{ count($list) }} {{ $tool['name'] }} alternatives that you can try and compare.</p>
        </div>
    </header>
    <main class="main-content">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/">Home</a></li>
                    <li class="breadcrumb-item"><a href="/social-media-scheduling-tools/">Social Media Scheduling Tools</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ $tool['name'] }} Alternatives</li>
                </ol>
            </nav>
        </div>
        <section class="section bg-light">
            <div class="container">

                <div class="row gap-y">
                    <div class="d-none d-md-block col-md-2 mx-auto">
                        @include('common.social_media_tools.sidebar')
                    </div>
                    <div class="col-md-9 col-md-offset-1">


                        <div class="card bg-white border mb-4">
                            <div class="card-body">
                                <p class="lead">
                                    {{ $tool['description'] }}
                                </p>
                                <p class="lead">
                                    All {{ $tool['name'] }} alternatives are listed below.
                                </p>
                            </div>
                        </div>

                        @foreach($list as $i => $row)
                            <div class="card hover-shadow-7 border bg-white {{ $row['name'] === 'SocialBu' ? 'border-primary': 'border-secondary' }}{{ $i === 0 ? ' mb-4': ' mb-4'}}">
                                <div class="row">
                                    <div class="col-md-3">
                                        @if($row['name'] === 'SocialBu')
                                            <a href="/auth/register" class="position-absolute h-100 d-flex align-items-start pt-7 px-4">
                                                @if(!empty($row['logo']))
                                                    <img class="d-none d-md-block p-2 w-100 lozad {{ $row['logo_is_white'] ? 'bg-dark':'' }}" src="/images/1x1.gif" data-src="{{ $row['logo'] }}" alt="{{ $row['name'] }} logo">
                                                @else
                                                    <img class="d-none d-md-block p-2 w-100 lozad {{ $row['logo_is_white'] ? 'bg-dark':'' }}" src="/images/email/spacer.gif" alt="{{ $row['name'] }}">
                                                @endif
                                            </a>
                                        @else
                                            <a href="/social-media-scheduling-tools/socialbu-vs-{{ $row['slug'] }}" class="position-absolute h-100 d-flex align-items-start pt-7 px-4">
                                                @if(!empty($row['logo']))
                                                    <img class="d-none d-md-block p-2 w-100 lozad {{ $row['logo_is_white'] ? 'bg-dark':'' }}" src="/images/1x1.gif" data-src="{{ $row['logo'] }}" alt="{{ $row['name'] }} logo">
                                                @else
                                                    <img class="d-none d-md-block p-2 w-100 lozad {{ $row['logo_is_white'] ? 'bg-dark':'' }}" src="/images/email/spacer.gif" alt="{{ $row['name'] }}">
                                                @endif
                                            </a>
                                        @endif
                                    </div>

                                    <div class="col-md-9">
                                        <div class="py-7 pr-4">
                                            <h4>
                                                <a href="{{ $row['name'] === 'SocialBu' ? '/auth/register': '/social-media-scheduling-tools/socialbu-vs-' . strtolower($row['slug']) }}">
                                                    {{ $row['name'] }}
                                                </a>
                                                @if($row['name'] === 'SocialBu')
                                                    <span class="badge badge-warning rounded">
                                                        Best
                                                    </span>
                                                @endif
                                            </h4>
                                            <p class="{{ $row['name'] === 'SocialBu' ? 'lead': '' }}">
                                                @if(!empty($row['description']))
                                                    {{ $row['description'] }}
                                                    <br/><br/>
                                                @endif
                                                Works with: {{ !empty($row['networks']) ? implode(', ', $row['networks']) : 'N/A' }}<br/>
                                                Customer support: {{ $row['live_chat'] ? 'Yes' : 'N/A' }}<br/>
                                            </p>
                                            <hr/>
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    @if(!empty($row['starting_price']))
                                                        Pricing starts at {{ $row['starting_price'] }}
                                                        @if(!empty($row['starting_price_notes']))
                                                            <i class="ph ph-check pr-2" title="{{ implode('. ', $row['starting_price_notes']) }}" data-toggle="tooltip"></i>
                                                        @endif
                                                    @else
                                                        Pricing not available
                                                    @endif
                                                </div>
                                                <div>
                                                    <a href="{{ $row['name'] === 'SocialBu' ? '/auth/register': '/social-media-scheduling-tools/socialbu-vs-' . strtolower($row['slug']) }}" class="btn btn-round {{ $row['name'] === 'SocialBu' ? 'btn-primary': 'btn-light' }}">
                                                        {{ $row['name'] === 'SocialBu' ? 'Get Started': 'Compare' }}
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        @endforeach
                    </div>
                </div>

            </div>
        </section>

        @include('common.internal.contact-block')

        @include('common.internal.join-us-block')
    </main>
@endsection