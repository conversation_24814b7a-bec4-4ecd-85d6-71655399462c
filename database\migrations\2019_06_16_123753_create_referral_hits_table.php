<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateReferralHitsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.insights'))->create('referral_hits', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('referrer_id')->unsigned()->index(); // user id
            $table->timestamp('timestamp')->useCurrent()->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(config('database.insights'))->dropIfExists('referral_hits');
    }
}
