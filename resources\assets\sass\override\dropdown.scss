.dropdown-header {
    padding: 8px 12px !important;
    text-transform: capitalize !important;
}
.dropdown-menu {
    padding: 8px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 1rem;
    min-width: $dropdown-min-width;
    border-radius: 16px;
    border: none;
    *:last-child {
        margin-bottom: 0;
    }
}
.dropdown-item {
    border-radius: 8px;
    padding: 12px 20px;
    color: $gray-800;
    border: none;
    opacity: 1;
    a {
        color: $gray-700;
    }
    &:hover {
        background-color: $gray-100;
        color: $color-primary;
        opacity: 1;
        & svg {
            path {
                fill: $color-primary;
            }
        }
        & a {
            color: $color-primary;
        }
        & .product-description {
            color: $gray-700;
        }
    }
    &:active,
    &:focus {
        background-color: $color-primary;
        color: white;
        & svg {
            path {
                fill: white;
            }
        }
        & a {
            color: white;
        }
        & .product-description {
            color: white;
        }
    }
}

.dropdown-item.selected {
    background-color: $gray-100;
    color: $color-primary;
    opacity: 1;
}
.dropdown-toggle {
    padding: 12px 20px;
    cursor: pointer;
    font-size: 1rem;
    &::after,
    .dropup &::after {
        margin-left: 0.6rem;
        width: 7px;
        height: 7px;
        margin-bottom: 0.2rem;
    }
}

.dropdown-divider {
    display: none;
}
button.dropdown-toggle.dropdown-toggle-split {
    padding: 12px 16px;
}
button.btn-sm.dropdown-toggle.dropdown-toggle-split {
    padding: 8px;
}
button.btn-xs.dropdown-toggle.dropdown-toggle-split {
    padding: 10px;
}
button.btn-lg.dropdown-toggle.dropdown-toggle-split {
    padding: 16px;
}
button.btn-xl.dropdown-toggle.dropdown-toggle-split {
    padding: 16px;
}
.dropdown {
    .dropdown-toggle.btn-light {
        background-color: transparent;
    }
}
.dropdown-toggle::after {
    border: none !important;
    font: normal normal normal 16px/1 FontAwesome;
    content: "\f107" !important;
    transform: rotate(1deg);
    margin-left: 0.2rem;
    margin-right: 0.2rem;
    margin-bottom: 0.5rem;
}
.dropup .dropdown-toggle::after {
    transform: rotate(181deg);
    margin-bottom: -1rem;
}
.dropdown.bootstrap-select {
    border: none !important;
    .btn-light{
      border-color: $gray-200;
    }
}
