<!DOCTYPE html>
<html lang="en">
<head>
    @include('layout.includes.user.head')
</head>

<body class="bg-light">
@include('layout.includes.common_body')
<div id="fb-root"></div>
<script>
    (function(d, s, id) {
        var js, fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) return;
        js = d.createElement(s); js.id = id;
        js.src = 'https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v3.1&appId={{ config('services.facebook.client_id') }}&autoLogAppEvents=1';
        fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));
</script>
<div id="app">
    <div class="container">
        <div class="row">

            <form id="logout-form" action="{{ url('/auth/logout') }}" method="POST" style="display: none;">
                {{ csrf_field() }}
            </form>

            <main class="col">

                <div class="row">
                    <div class="col-12 col-md-10 offset-md-1 mt-4">
                        <div class="mb-2 d-flex justify-content-between align-items-center">
                            <img class="logo lozad" src="/images/redesign/logo.svg" alt="Logo" />
                            <div>
                                <div class="rounded dropdown d-flex align-items-center hover-nav-menu cursor-pointer pr-2 h-40px" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <img alt="avatar" class="avatar avatar-xs border lozad" src="{{ user()->getPic() }}" />
                                    <span class="d-none d-md-block">&nbsp; {{ user()->getFirstName() }}</span>  <i class="ph ph-caret-down"></i>
                                </div>
                                <div class="dropdown-menu dropdown-menu-right">
                                    <a class="dropdown-item" href="#" onclick="event.preventDefault();document.getElementById('logout-form').submit();">
                                        Logout
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="card shadow rounded-xl">
                            <div class="card-body px-md-6 px-4 py-md-6 py-20">

                                @include('layout.partials.flash')
                                @include('layout.partials.errors')

                                @yield('content')
                            </div>
                        </div>

                        <footer class="pt-2">
                            <div class="row gap-y">
                                <div class="col-12">
                                    <div class="d-flex justify-content-between">
                                        <div class="small-2 text-muted">
                                            &copy; <a href="/" class="text-muted" target="_blank">{{ config('app.name') }}</a> {{ date('Y') }} |
                                            <a target="_blank" class="text-muted" href="/about/terms">Terms</a> |
                                            <a target="_blank" class="text-muted" href="/about/privacy">Privacy</a>
                                        </div>
                                        <div class="text-muted">
                                            <a target="_blank" class="text-muted" href="/blog/">Blog</a> |
                                            <a target="_blank" class="text-muted" href="/mobile">Mobile Apps</a> |
                                            <a target="_blank" class="text-muted" href="/browser">Browser Extension</a> |
                                            <a target="_blank" class="text-muted" href="/help">Help</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </footer>


                    </div>
                </div>
            </main>

        </div>
    </div>
</div>

@include('layout.includes.user.footer_html')
@stack('footer_html')
</body>
</html>
