<?php


namespace App\Socialite;

use GuzzleHttp\ClientInterface;
use <PERSON><PERSON>\Socialite\Two\AbstractProvider;
use <PERSON><PERSON>\Socialite\Two\ProviderInterface;
use <PERSON><PERSON>\Socialite\Two\User;


class ThreadsProvider extends AbstractProvider implements ProviderInterface
{

    public static function getBaseUrl($url = null, $domain = 'www.threads.net')
    {
        $baseUrl = 'https://' . $domain .'/';
        return $baseUrl . ( $url ? $url : '' );
    }

    /**
     * {@inheritdoc}
     */
    protected function getAuthUrl($state)
    {
        return $this->buildAuthUrlFromBase( $this::getBaseUrl('oauth/authorize'), $state);
    }

    /**
     * {@inheritdoc}
     */
    protected function getTokenUrl()
    {
        return $this::getBaseUrl('oauth/access_token', 'graph.threads.net');
    }

    /**
     * {@inheritdoc}
     */
    protected function getTokenFields($code)
    {
        return [
            'client_id' => $this->clientId,
            'client_secret' => $this->clientSecret,
            'code' => $code,
            'grant_type' => 'authorization_code',
            'redirect_uri' => $this->redirectUrl,
        ];
    }

    /**
     * {@inheritdoc}
     */
    protected function getCodeFields($state = null)
    {
        $fields = [
            'client_id' => $this->clientId,
            'redirect_uri' => $this->redirectUrl,
            'scope' => $this->formatScopes($this->getScopes(), $this->scopeSeparator),
            'response_type' => 'code',
        ];

        if ($this->usesState()) {
            $fields['state'] = $state;
        }

        return array_merge($fields, $this->parameters);
    }

    /**
     * Get the access token response for the given code.
     *
     * @param  string  $code
     * @return array
     */
    public function getAccessTokenResponse($code)
    {
        $postKey = (version_compare(ClientInterface::VERSION, '6') === 1) ? 'form_params' : 'body';

        $response = $this->getHttpClient()->post($this->getTokenUrl(), [
            'headers' => ['Content-Type' => 'application/x-www-form-urlencoded'],
            $postKey => $this->getTokenFields($code),
        ]);

        return json_decode($response->getBody(), true);
    }

    /**
     * {@inheritdoc}
     */
    protected function getUserByToken($token)
    {
        try {
            $response = $this->getHttpClient()->get($this::getBaseUrl('v1.0/me', 'graph.threads.net') . '?fields=id,username,threads_profile_picture_url,threads_biography&access_token=' . $token, [
//                'headers' => [
//                    'Authorization' => 'Bearer ' . $token,
//                ],
            ]);

            return json_decode($response->getBody(), true);
        }  catch (\Exception $e) {
            \Log::error($e->getMessage());
            \Log::info("Token used for request: " . $token);
            if($e instanceof \GuzzleHttp\Exception\ClientException){
                // log body
                $response = $e->getResponse();
                if($response) {
                    $body = $response->getBody();
                    report(new \Exception('Invalid response from threads: ' . $body));
                }
            } else if ($e instanceof \GuzzleHttp\Exception\ServerException) {
                // log body
                $response = $e->getResponse();
                if($response){
                    $body = $response->getBody();
                    report(new \Exception('Invalid response from threads: ' . $body));
                }
            }
            throw $e;
        }
    }

    /**
     * {@inheritdoc}
     */
    protected function mapUserToObject(array $user)
    {
        return (new User)->setRaw($user)->map([
            'id'       => $user['id'],
            'nickname' => $user['username'],
            'name'     => $user['username'],
            'avatar' => $user['threads_profile_picture_url'] ?? '',
        ]);
    }

}
