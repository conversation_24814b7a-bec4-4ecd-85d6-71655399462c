import * as Sentry from "@sentry/browser";
import { BrowserTracing } from "@sentry/tracing";

import { appConfig } from "./components";

if (process.env.MIX_APP_ENV === "production") {
    const IGNORE_ERRORS = [
        "ResizeObserver", // can be ignored safely
        "datetimepicker", // datetimepicker sends strange errors which are just annoying
        "Non-Error promise rejection", // not helpful
        "cyclic structures.", // not helpful
        "cyclic object value", // ...
        "ga is not defined", // may be caused by ad blockers
    ];
    Sentry.init({
        environment: process.env.MIX_APP_ENV,
        dsn: process.env.MIX_SENTRY_DSN,
        // You may want this to be 100% while
        // in development and sample at a lower rate in production
        replaysSessionSampleRate: 0.001,
        // If the entire session is not sampled, use the below sample rate to sample
        // sessions when an error occurs.
        replaysOnErrorSampleRate: 1.0,
        integrations: [
            new BrowserTracing(),
            new Sentry.Replay(),
        ],
        tracesSampleRate: 0.0001, // % of requests to capture for performance monitoring
        beforeSend(event, hint) {
            const error = hint.originalException || hint.syntheticException;
            let message = "";
            if (error instanceof Error) {
                message = error.message;
            } else if (typeof error === "string") {
                message = error;
            }
            for (let i = 0; i < IGNORE_ERRORS.length; ++i) {
                if (message.includes(IGNORE_ERRORS[i])) {
                    // this must be ignored, so return null to not send this error to sentry
                    return null;
                }
            }
            return event;
        },
        allowUrls: [/https?:\/\/((www)\.)?socialbu\.com/],
    });
    if(window.dataLayer && window.dataLayer[0]?.userName) {
        Sentry.configureScope(scope => {
            scope.setUser({
                id: appConfig.userId,
                username: window.dataLayer[0].userName,
                email: window.dataLayer[0].userEmail
            });
        });
    }
}

/**
 * jQuery and other first-level dependencies
 */
global.jQuery = require("jquery");
require("bootstrap");
require("bootstrap-select");
global.moment = require("moment");
require("tempusdominus-bootstrap-4");
require("select2");
require("jquery-autosize");
require("highlight-within-textarea");
require("jquery-serializejson");
window.Spinner = require("spin.js");
require("./internal/jquery.spin");
/**
 * setup things
 */
$.fn.select2.defaults.set("theme", "bootstrap");
jQuery.ajaxPrefilter(function(options, originalOptions, jqXHR) {
    if (!options.crossDomain) {
        jqXHR.setRequestHeader(
            "X-CSRF-TOKEN",
            document.querySelector('meta[name="csrf-token"]').getAttribute("content")
        );
    }
});

// fix $.modal to allow nested modals
$(document).on("hidden.bs.modal", function() {
    if ($(".modal.show").length) {
        $("body").addClass("modal-open");
    }
});
