<?php


namespace App\Socialite;

use GuzzleHttp\ClientInterface;
use <PERSON><PERSON>\Socialite\Two\AbstractProvider;
use <PERSON><PERSON>\Socialite\Two\ProviderInterface;
use <PERSON><PERSON>\Socialite\Two\User;


class TikTokProvider extends AbstractProvider implements ProviderInterface
{

    public static function getBaseUrl($url = null, $domain = 'www.tiktok.com')
    {
        $baseUrl = 'https://' . $domain .'/';
        return $baseUrl . ( $url ? $url : '' );
    }

    /**
     * {@inheritdoc}
     */
    protected function getAuthUrl($state)
    {
        return $this->buildAuthUrlFromBase( $this::getBaseUrl('v2/auth/authorize/'), $state);
    }

    /**
     * {@inheritdoc}
     */
    protected function getTokenUrl()
    {
        return $this::getBaseUrl('v2/oauth/token/', 'open.tiktokapis.com');
    }

    /**
     * {@inheritdoc}
     */
    protected function getTokenFields($code)
    {
        return [
            'client_key' => $this->clientId,
            'client_secret' => $this->clientSecret,
            'code' => $code,
            'grant_type' => 'authorization_code',
            'redirect_uri' => $this->redirectUrl,
        ];
    }

    /**
     * {@inheritdoc}
     */
    protected function getCodeFields($state = null)
    {
        $fields = [
//            'client_id' => $this->clientId,
            'client_key' => $this->clientId,
            'redirect_uri' => $this->redirectUrl,
            'scope' => $this->formatScopes($this->getScopes(), $this->scopeSeparator),
            'response_type' => 'code',
        ];

        if ($this->usesState()) {
            $fields['state'] = $state;
        }

        return array_merge($fields, $this->parameters);
    }

    /**
     * Get the access token response for the given code.
     *
     * @param  string  $code
     * @return array
     */
    public function getAccessTokenResponse($code)
    {
        $postKey = (version_compare(ClientInterface::VERSION, '6') === 1) ? 'form_params' : 'body';

        $response = $this->getHttpClient()->post($this->getTokenUrl(), [
            'headers' => ['Content-Type' => 'application/x-www-form-urlencoded'],
            $postKey => $this->getTokenFields($code),
        ]);

        $ret = json_decode($response->getBody(), true);

        if(isset($ret['data'])){
            $ret = $ret['data'];
        }
        return $ret;
    }

    /**
     * {@inheritdoc}
     */
    protected function getUserByToken($token)
    {
        try {
            $response = $this->getHttpClient()->get($this::getBaseUrl('v2/user/info/', 'open.tiktokapis.com') . '?fields=open_id,union_id,avatar_url,display_name', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $token,
                ],
            ]);

            return json_decode($response->getBody(), true)['data']['user'];
        }  catch (\Exception $e) {
            \Log::error($e->getMessage());
            \Log::info("Token used for request: " . $token);
            if($e instanceof \GuzzleHttp\Exception\ClientException){
                // log body
                $response = $e->getResponse();
                if($response) {
                    $body = $response->getBody();
                    report(new \Exception('Invalid response from tiktok: ' . $body));
                }
            } else if ($e instanceof \GuzzleHttp\Exception\ServerException) {
                // log body
                $response = $e->getResponse();
                if($response){
                    $body = $response->getBody();
                    report(new \Exception('Invalid response from tiktok: ' . $body));
                }
            }
            throw $e;
        }
    }

    /**
     * {@inheritdoc}
     */
    protected function mapUserToObject(array $user)
    {
        return (new User)->setRaw($user)->map([
            'id'       => $user['open_id'],
            'nickname' => $user['display_name'],
            'name'     => $user['display_name'],
        ]);
    }

}
