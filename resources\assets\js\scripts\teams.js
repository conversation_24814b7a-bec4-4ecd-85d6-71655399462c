import $ from "jquery";
import lang from "../lang";
import { axios, alertify } from "../components";

function init_select2($modal) {
    $modal.find(".input_team_accounts").select2({
        dropdownParent: $modal,
        placeholder: lang.get("generic.type_to_search"),
        ajax: {
            url: "/app/json/search/accounts",
            dataType: "json",
            delay: 250,
            data: function(params) {
                return {
                    q: params.term // search term
                };
            },
            processResults: function(data, params) {
                params.page = params.page || 1;

                $.each(data.items, function(index, item) {
                    item.text = item.name;
                });

                return {
                    results: data.items
                };
            },
            cache: true
        },
        minimumInputLength: 1
    });
    $modal.find(".input_team_members").select2({
        dropdownParent: $modal,
        placeholder: lang.get("generic.type_to_search"),
        ajax: {
            url: "/app/json/search/member",
            dataType: "json",
            delay: 250,
            data: function(params) {
                return {
                    q: params.term // search term
                };
            },
            processResults: function(data, params) {
                params.page = params.page || 1;

                $.each(data.items, function(index, item) {
                    item.text = item.name;
                });

                return {
                    results: data.items
                };
            },
            cache: true
        },
        minimumInputLength: 1
    });
}
$("#add_team_modal").on("shown.bs.modal", function() {
    init_select2($(this));
}).on("hidden.bs.modal", function(){
    const $modal = $(this);
    try {
        if (
            $modal
                .find(".input_team_accounts")
                .hasClass("select2-hidden-accessible")
        ) {
            $modal.find(".input_team_accounts").select2("destroy");
            $modal.find(".input_team_members").select2("destroy");
        }
    } catch (e) {}
});


// team invites
$(".inviteRespondBtn").on("click", async function() {
    const id = $(this).attr("data-id");
    const action = $(this).attr("data-action");
    try {
        await axios.post("/app/teams/" + id + "/invite", {
            action
        });
        document.location.reload();
    } catch (e) {
        alertify.error(e.message || "An error occurred.");
    }
    return false;
});

export function setConfig(config) {

    __loadComponent("team", "#team_view", $vm => {
        $vm.setConfig(config);
    });

}
