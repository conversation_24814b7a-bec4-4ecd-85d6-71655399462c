.select2-container--bootstrap4 {
    .select2-selection__choice {
        color: #757575 !important;
        border: 1px solid #E3E6EB !important;
        padding: 1rem !important;
        .select2-selection__choice__remove {
            color: #757575 !important;
        }
    }
}
.select2-container {
  width: 100% !important; // fix  the annoying bug where width is 1px sometimes (due to calling .select2() on same element again or when its hidden idk)
}

.select2-transparent {
    .select2-selection {
        box-shadow: none !important;
        background: transparent !important;
    }
}
.select2-container .select2-selection--single{
    height: auto;
    padding: 12px 20px;
    border-radius: 0.5rem;
}
.select2-container .select2-selection--multiple{
    height: auto;
    border-radius: 0.5rem !important;
    .select2-selection__rendered{
        padding-top: 9px;
        .select2-selection__choice{
            padding: 2px 4px 2px 6px;
            border: none;
            display: flex;
            flex-direction: row-reverse;
            align-items: center;
            font-size: 14px;
            font-weight: 500;
            .select2-selection__choice__remove {
                font-size: 1rem;
                font-weight: 200;
                margin-left: 4px;
            }
        }
    }
    .select2-dropdown .select2-results .select2-results__options{
        font-weight: 500;
        color: #252A32;
        .select2-results__option--highlighted{
            color: white;
        }
    }
    
}