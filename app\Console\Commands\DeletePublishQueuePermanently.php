<?php

namespace App\Console\Commands;

use App\PublishQueue;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;

class DeletePublishQueuePermanently extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'publish_queues:delete';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete soft deleted publish queues';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        /** @var PublishQueue[]|Collection $queues */
        $queues = PublishQueue::onlyTrashed()->limit(100);

        $queues->each(function($queue){
            /** @var PublishQueue $queue */

            $items = $queue->items()->limit(2000)->get();

            if($items->count() === 0){
                // its now safe to permanently delete the feed
                $queue->forceDelete();
                return;
            }

            foreach ($items as $item){
                // observer will automatically delete attachments
                $item->delete();
            }

        });
    }
}
