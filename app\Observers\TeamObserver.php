<?php

namespace App\Observers;

use App\Helpers\EmailListHelper;
use App\Team;

class TeamObserver
{
    /**
     * Handle to the team "created" event.
     *
     * @param  \App\Team  $team
     * @return void
     */
    public function created(Team $team)
    {
        // add creator as member
        $team->members()->attach($team->user_id, [
            'approved' => true,
        ]);
        // send event
        try {
            EmailListHelper::getInstance()->sendEvent($team->user, 'team_created', $team->name);
        } catch (\Exception $exception){
            report($exception);
        }

        if(user()){
            record_usage_event('team_created', $team->name);
        }
    }

    /**
     * Handle the team "updated" event.
     *
     * @param  \App\Team  $team
     * @return void
     */
    public function updated(Team $team)
    {
        //
    }

    /**
     * Handle the team "deleted" event.
     *
     * @param  \App\Team  $team
     * @return void
     */
    public function deleted(Team $team)
    {
        try {
            $team->logs()->delete();
        } catch (\Exception $exception){
            report($exception);
        }

        // send event
        try {
            EmailListHelper::getInstance()->sendEvent($team->user, 'team_deleted', $team->name);
        } catch (\Exception $exception){
            report($exception);
        }

        if(user()){
            // this only works for logged-in users
            record_usage_event('team_deleted', $team->name);
        }

    }
}
