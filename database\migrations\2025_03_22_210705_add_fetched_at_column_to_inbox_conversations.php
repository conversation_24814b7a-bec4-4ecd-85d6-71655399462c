<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddFetchedAtColumnToInboxConversations extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('inbox_conversations', function (Blueprint $table) {
            $table->timestamp('fetched_at')->nullable()->index(); // this is for storing the time when the conversation was last fetched from the third-party service; useful for polling the data
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('inbox_conversations', function (Blueprint $table) {
            $table->dropColumn('fetched_at');
        });
    }
}
