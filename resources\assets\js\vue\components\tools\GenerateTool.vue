<template>
    <div>
        <h1>Generate Tool</h1>
            <DynamicForm :id="id" :fields="fields" :output-components="outputComponents"  v-if="id"  :on-submit="submit"/>
        <div v-else>
            Loading...
        </div>
    </div>
</template>
<script>
import DynamicForm from "./DynamicForm.vue";
export default {
    name: "GenerateTool",
    components: {
        DynamicForm,
    },
    data() {
        return {
            id: null,
            fields: [],
            outputComponents: [],
        };
    },
    methods: {
        initialize({
            id, form
        }) {
            this.id = id;
            this.fields = form.fields;
            this.outputComponents = form.outputComponents || [];

        },
        submit(inputData){
            // todo: submit form to server and return output object for use with outputComponents or display output in some way
        }

    }
}
</script>

<style scoped lang="scss">
</style>
