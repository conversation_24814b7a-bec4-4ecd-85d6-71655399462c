<template>
    <div class="row">
        <div class="col"
             v-if="!filter.team">
            <div class="mt-6 d-flex align-items-baseline justify-content-center">
                No team selected. Please select a team.
            </div>
        </div>
        <template v-else>
            <div class="col-12 mb-8">
                <h5 class="mb-4">Publishing Overview</h5>
                <div class="border" v-if="isLoading('team_metrics')">
                    <div class="card" v-html="spinnerHtml"></div>
                </div>
                <div class="row" v-else>

                    <div class="col-md-12 publishing-cards d-flex overflow-auto hide-scrollbar">
                        <div class="card border mb-4 mr-4">
                            <div class="card-body p-20">
                                <h6 class="font-weight-500 mb-6">Total Engagements</h6>
                                <h3 class="mb-0">{{ totalEngagements }}</h3>
                            </div>
                        </div>
                        <div class="card border mb-4 mr-4">
                            <div class="card-body p-20">
                                <div>
                                    <h6 class="font-weight-500 mb-6">Scheduled Posts</h6>
                                    <h3 class="mb-0">{{ totalScheduled }}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="card border mb-4 mr-4">
                            <div class="card-body p-20">
                                <div>
                                    <h6 class="font-weight-500 mb-6">Published Posts</h6>
                                    <h3 class="mb-0">{{ totalPublished }}</h3>
                                </div>
                            </div>
                        </div>
                        <div class="card mb-4 border">
                            <div class="card-body p-20">
                                <h6 class="font-weight-500 mb-6">Rejected Posts</h6>
                                <h3 class="mb-0">{{ totalRejected }}</h3>
                            </div>
                        </div>
                    </div>

                    <div class="col-12">
                        <div class="card border pt-4 d-flex justify-content-center">
                            <Chart
                                :options="contentOverviewChart"
                                ref="chart_team_overview"
                            />
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 mb-8">
                <h5 class="mb-4">Top Members</h5>
                <div class="border"
                     v-if="isLoading('team_metrics')">
                    <div class="card" v-html="spinnerHtml"></div>
                </div>
                <div class="row"
                     v-else>
                    <div class="col"
                         v-if="!metricsByUser.length">
                        <div class="card border">
                            <div class="card-body p-20">
                                <div class="d-flex justify-content-center">
                                    <span>No data to show</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 pb-4 px-md-4 px-0">
                        <div class="table-responsive border rounded-md pl-0">
                            <table class="table mb-0">
                                <thead class="card-header">
                                    <tr>
                                        <th scope="col" class="py-3 pl-md-5 pl-4 pr-0">Name</th>
                                        <th scope="col" class="py-3">Engagements</th>
                                        <th scope="col" class="py-3">Scheduled</th>
                                        <th scope="col" class="py-3">Published</th>
                                        <th scope="col" class="py-3 pr-5">Rejected</th>

                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(member, index) in metricsByUser" :key="index + 'person-team' + member.member_id">
                                        <td class="member-name font-weight-500 plus-jakarta pl-md-5 pl-4 pr-0">
                                            <img :src="member.member_photo" class="avatar avatar-xs mr-2"  alt="user"/>
                                            <span class="d-md-inline-block d-none">{{ member.member_name.length > 15 ? member.member_name.substring(0,15).concat('...') : member.member_name }}</span> 
                                            <span class="d-md-none">{{ member.member_name.length > 6 ? member.member_name.substring(0,6).concat('...') : member.member_name }}</span>
                                        </td>
                                        <td>
                                            {{ member.total_engagements }}
                                        </td>
                                        <td>
                                            {{ member.scheduled_posts.reduce((a, b) => a + b.count, 0) }}
                                        </td>
                                        <td>
                                            {{ member.published_posts.reduce((a, b) => a + b.count, 0) }}
                                        </td>
                                        <td class="pr-5">
                                            {{ member.rejected_posts.reduce((a, b) => a + b.count, 0) }}
                                        </td>
                                                
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    
                </div>
            </div>
            <div class="col-12">
                <div class="d-flex justify-content-between">
                    <h5 class="mb-4">
                        Top Posts
                        <span v-if="!topPosts.length">(10)</span>
                    </h5>
                </div>
                <div class="card border" v-if="isLoading('top_posts')" v-html="spinnerHtml"></div>
                <div v-else>
                    <div v-if="!topPosts.length">
                        <div class="card border">
                            <div class="card-body d-flex justify-content-center p-20">
                                <span>No data to show</span>
                            </div>
                        </div>
                    </div>
                    <div
                        v-for="(data, index) in topPosts" :key="index + 'post-container'" v-else>
                        <Post :post="data"/>
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>
<script>
import {axios, axiosErrorHandler, spinnerHtml} from "../../../components";
import Post from "../Post.vue";
import { Chart } from "highcharts-vue";

export default {
    name: "TeamPerformance",
    props: ['filter', 'accounts', 'filterFormOpen'],
    components: {
        Post,
        Chart
    },
    data() {
        return {
            loadingFlags: [],

            topPosts: [],

            metricsByUser: [],
        };
    },
    computed: {
        spinnerHtml: () => spinnerHtml,

        totalRejected() {
            return this.metricsByUser.reduce((prev, u2) => {
                return u2.rejected_posts.reduce((a, b) => {
                    return a + b.count;
                }, 0) + prev;
            }, 0);
        },
        totalPublished() {
            return this.metricsByUser.reduce((prev, u2) => {
                return u2.published_posts.reduce((a, b) => {
                    return a + b.count;
                }, 0) + prev;
            }, 0);
        },
        totalScheduled() {
            return this.metricsByUser.reduce((prev, u2) => {
                return u2.scheduled_posts.reduce((a, b) => {
                    return a + b.count;
                }, 0) + prev;
            }, 0);
        },
        totalEngagements() {
            return this.metricsByUser.reduce((prev, u2) => {
                return u2.total_engagements + prev;
            }, 0);
        },

        // chart configs
        contentOverviewChart() {
            // combine all the stats for all users
            const metricsByDate = {
                published: [],
                scheduled: [],
                rejected: [],
            };
            this.metricsByUser.forEach(u => {
                u.published_posts.forEach(p => {
                    const {date, count} = p;
                    const existing = metricsByDate.published.find(p => p.date === date);
                    if (existing) {
                        existing.count += count;
                    } else {
                        metricsByDate.published.push({date, count});
                    }
                });
                u.scheduled_posts.forEach(p => {
                    const {date, count} = p;
                    const existing = metricsByDate.scheduled.find(p => p.date === date);
                    if (existing) {
                        existing.count += count;
                    } else {
                        metricsByDate.scheduled.push({date, count});
                    }
                });
                u.rejected_posts.forEach(p => {
                    const {date, count} = p;
                    const existing = metricsByDate.rejected.find(p => p.date === date);
                    if (existing) {
                        existing.count += count;
                    } else {
                        metricsByDate.rejected.push({date, count});
                    }
                });
            });

            return {
                chart: {
                    type: "areaspline",
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: "",
                },
                xAxis: {
                    type: "datetime",
                },
                yAxis: {
                    title: {
                        text: "Posts",
                    },
                    labels: {
                        format: '{value}',
                    }
                },
                plotOptions: {
                    series: {
                        fillOpacity: 0.7
                    },
                    area: {
                        marker: {
                            enabled: false, // Simplified markers for area charts
                            symbol: 'circle',
                            radius: 2,
                            states: {
                                hover: {
                                    enabled: true
                                }
                            }
                        }
                    }
                },
                series: [
                    {
                        name: "Published",
                        marker: {
                            enabled: false
                        },
                        data: metricsByDate.published.map(p => [this.timestampForXAxis(p.date), p.count]),
                    },
                    {
                        name: "Scheduled",
                        marker: {
                            enabled: false
                        },
                        data: metricsByDate.scheduled.map(p => [this.timestampForXAxis(p.date), p.count]),
                    },
                    {
                        name: "Rejected",
                        marker: {
                            enabled: false
                        },
                        data: metricsByDate.rejected.map(p => [this.timestampForXAxis(p.date), p.count]),
                    },
                ],
            };
        }
    },
    async mounted() {
        await this.fetchData();
    },
    methods: {
        isLoading(flag) {
            return this.loadingFlags.includes(flag);
        },
        showLoader(flag) {
            if (this.isLoading(flag)) {
                return;
            }
            this.loadingFlags.push(flag);
        },
        hideLoader(flag) {
            this.loadingFlags = this.loadingFlags.filter(itm => itm !== flag);
        },
        getAccount(id) {
            const all = this.accounts;
            let account = {};
            all.forEach((acc) => {
                if (acc.id === id) account = acc;
            })
            return account;
        },
        async fetchData() {
            if(!this.filter.team) {
                this.$bus.$emit('open_filter_form');
                return;
            }
            await this.fetchTeamMetrics();
            await this.fetchTopPosts();
        },
        async fetchTeamMetrics() {
            this.showLoader('team_metrics');
            try {
                const { data } = await axios.get("/api/v1/insights/teams/metrics", {
                    params: {
                        start: this.filter.start,
                        end: this.filter.end,
                        team: this.filter.team,
                        accounts: this.filter.accounts,
                        metrics: 'comments,likes,shares,reactions,saved,video_views,plays,retweets,post_views,post_cta_clicks,views_search',
                    },
                });
                this.metricsByUser = data.data;
            } catch (e) {
                axiosErrorHandler(e);
            }
            this.hideLoader('team_metrics');
        },
        async fetchTopPosts() {
            this.showLoader('top_posts');
            try {
                const {data} = await axios.get('/api/v1/insights/posts/top_posts', {
                    params: {
                        start: this.filter.start,
                        end: this.filter.end,
                        team: this.filter.team,
                        accounts: this.filter.accounts,
                        metrics: 'comments,likes,shares,reactions,saved,video_views,plays,retweets,post_views,post_cta_clicks,views_search',
                    }
                });
                const posts = data.data;
                if (posts && posts.length) {
                    posts.forEach((item) => {
                        item.accounts = [this.getAccount(item.account_id)];
                    });
                }
                this.topPosts = posts;
            } catch (e) {
                axiosErrorHandler(e);
            }
            this.hideLoader('top_posts');
        },
        timestampForXAxis(timestamp) {
            return this.$momentUTC(timestamp, "YYYY-MM-DD").valueOf();
        },
    },
};
</script>
<style lang="scss" scoped>
    .plus-jakarta{
        font-family: "Plus Jakarta Sans" !important;
    }
    .table{
        .card-header{
            font-family: "Plus Jakarta Sans" !important;
        }
    }
    .publishing-cards{
        .card{
            width: 100%;
        }
    }
    .member-name{
        min-width: 128px;
    }
    @media (max-width: 777px){
        .publishing-cards{
            .card{
                min-width: 262px !important;
            }
        }
        .table-responsive{
            border: none !important;
            scrollbar-width: none;
            .card-header{
                border-bottom: 1px solid #F2F4F7 !important;
                background-color: white;
            }    
        }
    }
</style>
