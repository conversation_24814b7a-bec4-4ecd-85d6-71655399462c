const debug = require("debug");
// const Sentry = require("./sentry"); // WARNING: dont import sentry, its not compatible with got/http2
module.exports = ns => {
    const _log = debug(ns);
    const isError = function(e) {
        return e && e.stack && e.message;
    };
    return (...e) => {
        _log(...e);

        // e.forEach(_e => {
        //     if (isError(_e) && _e.message.includes("User Error") === false) Sentry.captureException(_e);
        // });
    };
};
