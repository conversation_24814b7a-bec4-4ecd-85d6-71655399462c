pipelines:
  pull-requests:
    '**':
      - step:
          name: Echo env variables
          script:
            - echo "OPENAI_API_KEY"
            - echo $OPENAI_API_KEY
            - echo "BITBUCKET_ACCESS_TOKEN"
            - echo $BITBUCKET_ACCESS_TOKEN 
            - echo "AI_MODEL"
            - echo $AI_MODEL
      - step:
          name: Build for any pull request created
          script:
            - pipe: atlassian/bitbucket-chatgpt-codereview:0.2.0
              variables:
                OPENAI_API_KEY: $OPENAI_API_KEY
                BITBUCKET_ACCESS_TOKEN: $BITBUCKET_ACCESS_TOKEN
                MODEL: "gpt-4o-mini"
                MESSAGE: "Provide only most important comments."
                DEBUG: $DEBUG
