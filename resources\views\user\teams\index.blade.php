@extends('layout.user')
@section('title', 'Teams | ' . config('app.name'))
@section('content')

    @push('footer_html')
        <script>
            __loadScript("teams", m => {
                m.setConfig({!! json_encode(['permissions' => \App\Team::$allPermissions]) !!});
            });

        </script>
    @endpush

    <div class="row">
        <div class="col-12 py-md-0 py-20">
            <h3 class="d-flex align-items-center justify-content-between mb-4">
                @lang('generic.teams')
                <button class="btn btn-light float-right" type="button" data-toggle="modal" data-target="#team_modal">
                    <div class="d-flex align-items-center">
                        <i class="ph-bold ph-plus mr-2" title="New team"></i> New team
                    </div>
                </button>
            </h3>

            <div data-tour="id=your_teams;title=Creating or joining a team;text=Creating (or joining) a team is a great way to group your social media accounts and work with them together as a team. <br/><br/>You will be able to invite your team members when you create a team. Teams you are invited to also show here.;position=bottom">
            @if($pendingTeams->count() > 0)
                <div class="card bg-light mb-md-5 mb-1">
                    <div class="card-body p-20">
                        <div class="row">
                            <div class="col-12">
                                <h5 class="font-weight-500 mb-3">Pending Invites</h5>
                            </div>
                        @foreach($pendingTeams as $team)
                            <div class="col-md-6 col-12 mb-md-0 mb-3">
                                <div class="card">
                                    <div class="card-body p-4">

                                        <div class="d-flex flex-wrap justify-content-between align-items-center">
                                            <div>
                                                <h6 class="font-weight-500">{{ $team->name }}</h6>
                                                <p class="d-md-none">Invited By: {{ $team->user ? $team->user->name : 'Deleted User' }}</p>

                                            </div>
                                            <div>
                                                <button type="button" class="inviteRespondBtn btn btn-outline-secondary btn-sm mr-md-2 mr-3" data-id="{{ $team->id }}" data-action="approve"><i class="ph ph-check text-success mr-1" aria-hidden="true"></i>Accept</button>
                                                <button type="button" class="inviteRespondBtn btn btn-outline-secondary btn-sm" data-id="{{ $team->id }}" data-action="reject"><i class="ph ph-x ph-md text-danger mr-1" aria-hidden="true"></i>Decline</button>
                                            </div>
                                        </div>
                                        <div>
                                            <p class="d-md-block d-none mb-0">Invited By: {{ $team->user ? $team->user->name : 'Deleted User' }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                        </div>
                    </div>
                </div>
            @endif

            @if(empty($teams) || $teams->count() == 0)

                <div class="card">
                    <div class="card-body pt-10">
                        <div class="text-center">
                            <h4 class="pb-md-2 pb-4">Looks like you have no team</h4>
                            <p class="mb-5">
                                By adding a team, you can share access to your accounts with other users. <a href="https://help.socialbu.com/article/61-creating-a-team" data-beacon-article-modal="5e9a053f04286364bc9890d4">Learn more</a>
                            </p>
                            <button class="btn btn-primary btn-sm" type="button" data-toggle="modal" data-target="#team_modal">
                                <div class="d-flex align-items-center">
                                    <i class="ph-bold ph-plus mr-2"></i>Create A Team
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            @else
                <div class="card">
                    <div class="card-body p-0">
                        <div class="table-responsive table-border rounded-lg mt-md-0 mt-4">
                            <table class="table table-hover mb-0">
                                <thead class="card-header">
                                    <tr>
                                        <th class="py-3 pl-5" scope="col">@lang('generic.name')</th>
                                        <th class="py-3" scope="col">@lang('generic.accounts')</th>
                                        <th class="py-3" scope="col" title="Members including you">@lang('generic.members')</th>
                                        <th class="py-3 pr-5" scope="col">@lang('generic.access_level')</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($teams->items() as $team)
                                    @php($pendingCount = $team->pendingMembers()->count())
                                    @php($membersCount = $team->members()->count())
                                    <tr class="p cursor-pointer" role="button" data-toggle="modal" data-target="#team_modal" data-id="{{ $team->id }}">
                                        <td class="pl-5">{{ $team->name }}</td>
                                        <td>{{ $team->accounts()->count() }}</td>
                                        <td>
                                            @if($membersCount === 1 && $pendingCount === 0)
                                            1 (you)
                                            @else
                                            {{ $team->members()->count() + $pendingCount }}
                                            @if($pendingCount > 0)
                                            ({{ $pendingCount }} pending)
                                            @endif
                                            @endif
                                        </td>
                                        <td class="pr-5">{{ $team->isAdmin(user()) ? trans('generic.admin') : trans('generic.member') }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                @if($teams->hasPages())
                <div class="d-flex justify-content-end pt-2 pb-0">
                    {{ $teams->links() }}
                </div>
                @endif
            @endif

            </div>

        </div>
    </div>

    <div id="team_view"></div>

@endsection
