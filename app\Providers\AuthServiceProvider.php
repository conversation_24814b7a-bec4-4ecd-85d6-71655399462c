<?php

namespace App\Providers;

use App\Team;
use App\User;
use Illuminate\Support\Facades\Gate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Laravel\Passport\Passport;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        // 'App\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        // team permissions
        foreach(Team::$allPermissions as $permission => $permDescription){
            \Gate::define($permission, function (User $user, Team $team) use($permission) {
                return $team->hasPermission($user, $permission);
            });
        }

        Passport::routes();
        Passport::enableImplicitGrant();

        Passport::cookie('sb_api_token');
    }
}
