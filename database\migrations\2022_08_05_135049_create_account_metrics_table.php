<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAccountMetricsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.insights'))->create('account_metrics', function(Blueprint $table){
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');

            $table->integer('account_id')->unsigned()->index();
//            $table->string('account_type')->index();

            $table->string('metric_type')->index();
            $table->unsignedBigInteger('metric_value');

            $table->timestamp('timestamp')->useCurrent()->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(config('database.insights'))->dropIfExists('account_metrics');
    }
}
