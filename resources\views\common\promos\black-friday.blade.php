@extends('layout.full_width')
@php($title = 'Black Friday Sale')
@php($description = 'SocialBu team wishes you a happy Black Friday. Accept our discount-gift of up-to 50% off on all our plans.')
@php($image = 'https://socialbu.com/images/site/link-preview.jpg')
@php($url = 'https://socialbu.com/')
@section('title', $title . ' | ' . config('app.name'))
@push('head_html')
<meta name="description" content="{{ $description }}" />
<link rel="canonical" href="{{ $url }}" />
<link href="https://fonts.googleapis.com/css2?family=Quicksand&family=Secular+One" rel="stylesheet">
<link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@800&display=swap" rel="stylesheet">
<meta property="og:locale" content="en_US" />
<!--
    <meta property="og:type" content="website" />
    -->
<meta property="og:title" content="{{ $title }}" />
<meta property="og:description" content="{{ $description }}" />
<meta property="og:url" content="{{ $url }}" />
<meta property="og:site_name" content="SocialBu" />
<meta property="og:image" content="{{ $image }}" />
<meta property="fb:app_id" content="{{ config('services.facebook.client_id') }}" />

<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:image" content="{{ $image }}" />
<meta name="twitter:title" content="{{ $title }}" />
<meta name="twitter:description" content="{{ $description }}" />
<meta name="twitter:site" content="@socialbuapp" />

<style>
    /* .header{
        padding-bottom: 0px !important;
    }
    .header-main{
        background-color: black !important;
        color: white !important;
    }
    #navbar_main{
        background-color: black !important;
        color: white !important;
    }
    #navbar_main .nav-link-color{
        color: white !important;
    }
    @media only screen and (max-width: 992px) {
        #navbar_main .nav-link-color{
            color: #323d47 !important
        }
        
    } */
    .font-jakarta{
        font-family: 'Plus Jakarta Sans';
    }
    .offer-discount{
        font-size: 60px;
        color: #0557f0;
    }
    .heading{
        color: black;
    }
    .offer-description{
        color: black;
    }
    @media only screen and (max-width: 600px) {
        .heading{
            font-size: 24px;
            text-align: center;
            padding-top: 0px !important;
        }
        .offer-discount{
            font-size: 22px;
        }
        .offer-description{
            font-size: 16px;
            text-align: center;
            padding-top: 16px !important;
            padding-bottom: 16px !important;
        }
    }
    /* .sticky-navbar{
        color: white !important;
        background-color: black !important;
    } */
    .font-secular {
        font-family: 'Secular One';
    }
    .font-quicksand {
        font-family: 'Quicksand';
    }

    .plan_type_input label:not(.active) {
        color: #0557f0 !important;
        background-color: #fff !important;
        }
    .line-through{
        text-decoration: line-through;
    }

</style>
@endpush

@section('content')
<header class="header header-main pt-9 pt-md-10 pb-4 pb-md-8 overflow-hidden">
    <div class="container">
        <div class="row font-jakarta">
            <div class="col-12 col-md-6" id="font-styles">
                <h1 class="display-1">
                    <span>BLACK FRIDAY DEAL</span>  <br />
                    <span class="offer-discount">50% OFF</span>
                </h1>
                <h2 class="pl-0 lead text-left text-muted">Limited-time offer.</h2>

                <!-- get started button that will scroll down to discounts -->
                <a class="btn btn-primary mt-4" href="#discounts">Get Started</a>

            </div>
            <div class="col-12 col-md-6">
                <img class="pb-md-2 img-responsive hover-move-up lozad" src="/images/redesign/homepage/main-section.webp">
            </div>
        </div>

    </div>
</header>
@include('common.internal.third-party-badges-block')
<main class="main-content text-dark" id="main">
    <section class="section" id="discounts">
        <div class="container">
            <header class="section-header">
                <h2 class="text-center display-3">Black Friday Discounts</h2>
                <p class="lead text-center text-muted">50% off on one yearly payment</p>
            </header>
            <div class="row gap-y text-start pt-3">
                    @foreach(config('plans') as $plan => $planDetails)
                        @if(in_array($plan, ['standard2', 'super', 'supreme',]))
                            <div class="col-lg-4 mx-auto">

                                <div class="card card-body text-start shadow-2 pt-6 pb-1">
                                    @if($plan === 'super')
                                        <span class="badge badge-success position-absolute" style="right: 16px;">Best Choice</span>
                                    @endif
                                    <h5 class="mb-1 display-5">{{ $planDetails['name'] }}</h5>
                                    <h2 class="mb-1 display-2">
                                        @if($plan === 'free')
                                            free
                                        @else
                                            <span>$</span>
                                            <span class="margin-right-n10">{{ $planDetails['price'] * 5 }}</span>
                                            <span class="display-3">
                                                <small class="text-muted line-through">${{ $planDetails['price'] * 10 }}</small>
                                                <small>/ yr</small>
                                            </span>
                                        @endif
                                    </h2>

                                    <div class="text-start mt-6 ">
                                        <p class="mb-0 "><i class="ph ph-check text-success"></i> <span class="pl-2 my-2">{{ $planDetails['limits']['accounts'] }} social accounts</span></p>                                       
                                        @if($planDetails['limits']['teams'] === 0)
                                            <p class="my-2 "><i class=""></i><span class="pl-2">No teams</span> </p class="mb-0 "><br/>
                                        @else
                                            <p class="my-2 "><i class="ph ph-check text-success"></i><span class="pl-2"> {{ $planDetails['limits']['teams'] }} teams </span></p class="mb-0 ">
                                        @endif
                                        <p class="my-2 ">
                                            @if(in_array($plan, ['super', 'supreme']))
                                                <i class="ph ph-check text-success"></i><span class="pl-2">Phone Support</span>
                                            @else
                                                <i class=""></i><span class="pl-2"> No phone support </span>
                                            @endif
                                            
                                        </p>
                                    </div>

                                    <p class="text-center pt-7">
                                        <a class="btn @if($plan === 'super') btn-primary @else btn-outline-primary @endif   w-100" href="https://socialbu.com/app?promo=OtvJ2RV4">
                                            @if($plan === 'free')
                                                Sign Up
                                            @else
                                                Get Now
                                            @endif
                                        </a>
                                        <br />
                                        <a class="btn btn-link text-muted" href="/pricing#features" target="_blank">Plan Details</a>
                                    </p>
                                    
                                </div>

                            </div>
                        @endif
                    @endforeach

                </div>

             </div>
     </section>

     <section class="section">
        <div class="container">
            <div class="row gap-y">
                <div class="col-12 text-center">
                    <h2 class="d-none d-md-block display-2 mb-0 pb-1">Everything you need!</h2>
                    <h2 class="d-md-none display-2 mb-3">Everything you <br> need!</h2>
                    <p class="d-none d-md-block mt-4 mb-0 lead-2">Managing social media can be a complex task that requires a variety of <br /> skills to become successful. But we have got you covered!</p>
                    <p class="d-md-none">Managing social media can be a complex <br> that requires a variety of skills to become <br> successful. But we have got you covered!</p>
                </div>
            </div>
            @include('common.internal.everything-you-need')
        </div>
    </section>


</main>
@endsection
