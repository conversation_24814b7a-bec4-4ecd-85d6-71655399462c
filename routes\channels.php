<?php
/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

use App\Feed;
use App\User;
use Illuminate\Support\Facades\Broadcast;

//Broadcast::channel('App.User.{id}', function ($user, $id) {
//    return !(int) $user->id === (int) $id;
//});
//
//Broadcast::channel('feed.{id}', function (User $user, $id) {
//    if(Feed::userFeeds($user->id)->where('id', $id)->exists()){
//        return [
//            'id' => $user->id,
//            'name' => $user->name,
//            'image' => $user->getPic(),
//        ];
//    }
//});
