<?php

namespace Tests\Unit;

use Carbon\Carbon;
use PHPUnit\Framework\TestCase;

class TimestampTest extends TestCase
{
    /**
     * @throws \Exception
     */
    public function testTimestampParsingInAutomation(){
        $validTimeUnits = get_valid_time_units();
        Carbon::setTestNow(now()); // in case its set to custom timestamp in any other test

        $unitMap = [
            'mins' => 'minute',
            'min' => 'minute',
            'minutes' => 'minute',
            'hr' => 'hour',
            'hrs' => 'hour',
            'hours' => 'hour',
            'days' => 'day',
            'weeks' => 'week',
            'months' => 'month',
            'years' => 'year',
            'yr' => 'year',
            'yrs' => 'year',
        ];

        foreach ($validTimeUnits as $timeUnit){
            $finalUnit = isset( $unitMap[$timeUnit] ) ?  $unitMap[$timeUnit] : $timeUnit;
            $this->assertEquals('1 ' . $finalUnit . ' from now', modify_relative_time(now(), '+1 ' . $timeUnit)->diffForHumans());
        }

    }
}
