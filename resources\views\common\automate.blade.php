@extends('layout.full_width')
@php($title = 'Social Media Automation')
@php($description = 'Automate your social media accounts and eliminate repetitive tasks. Create fully dynamic automation rules to support your workflow.')
@php($image = 'https://socialbu.com/images/site/automation-800.png')
@php($url = 'https://socialbu.com/automate')
@section('title', $title . ' | ' . config('app.name'))
@push('head_html')
    <meta name="description" content="{{ $description }}"/>
    <link rel="canonical" href="{{ $url }}" />

    <meta property="og:locale" content="en_US" />
    <!--
    <meta property="og:type" content="website" />
    -->
    <meta property="og:title" content="{{ $title }}" />
    <meta property="og:description" content="{{ $description }}" />
    <meta property="og:url" content="{{ $url }}" />
    <meta property="og:site_name" content="SocialBu" />
    <meta property="og:image" content="{{ $image }}" />
    <meta property="fb:app_id" content="{{ config('services.facebook.client_id') }}" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content="{{ $image }}" />
    <meta name="twitter:title" content="{{ $title }}" />
    <meta name="twitter:description" content="{{ $description }}" />
    <meta name="twitter:site" content="@socialbuapp" />
@endpush
@section('content')

    <header class="header">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h1 class="display-1 text-center text-md-left mb-4">Automate Your Social Media</h1>
                   
                    <p class="lead-2 text-center text-md-left pt-1 mb-0">
                        Create fully dynamic automation rules to support your social media and eliminate repetitive tasks.                    
                    </p>
                    @include('common.internal.start-free-block')
                </div>
                <div class="d-none d-md-block col-12 col-md-6">
                    <img src="/images/1x1.gif" data-src="/images/redesign/automate/automate-post.webp"  alt="social media automation" class="img-responsive hover-move-up lozad position-relative" />
                </div>
            </div>
        </div>
    </header>

    <main class="main-content" id="main">
        <section class="section">
            <div class="container">
                <div class="row gap-y align-items-center">
                    <div class="col-md-6 text-center text-md-left offset-md-1 ml-0">
                        <h2 class="display-2 mb-4">
                            Auto post from RSS feeds
                        </h2>
                        <p class="lead-2 pt-1">
                        Automatically post social media posts from RSS feeds. Automate and publish content to your social media accounts with customizable post formats and schedule.
                        </p>
                    </div>
                    <div class="col-md-6 pb-0 order-md-first">
                        <img src="/images/1x1.gif" data-src="/images/redesign/automate/rss-feed.webp"  alt="Automation events" data-aos="fade-left" class="img-responsive rounded aos-init aos-animate lozad" />
                    </div>
                </div>
            </div>
        </section>

        <section class="section">
            <div class="container">
                <div class="row gap-y align-items-center">
                    <div class="col-md-6 text-center text-md-left">
                        <h2 class="display-2 mb-4">
                            Auto replies without any delay
                        </h2>
                        <p class="lead-2 pt-1">
                            Never let your customers wait on simple questions. Automate replies to common messages or comments.
                        </p>
                    </div>

                    <div class="col-md-6 offset-md-1 pb-0 ml-0">
                        <img src="/images/1x1.gif" data-src="/images/redesign/automate/auto-reply.webp"  alt="Automation conditions" data-aos="fade-left" class="img-responsive rounded aos-init aos-animate lozad">
                    </div>
                </div>
            </div>
        </section>

        <section class="section">
            <div class="container">
                <div class="row gap-y align-items-center">
                    <div class="col-md-6 text-center text-md-left offset-md-1 ml-0">
                        <h2 class="display-2 mb-4">
                            Handle reviews automatically
                        </h2>
                        <p class="lead-2 pt-1">
                            Handle reviews from your social media pages and perform instant actions based on the review.
                        </p>
                    </div>

                    <div class="col-md-6 pb-0 order-md-first">
                        <img src="/images/1x1.gif" data-src="/images/redesign/automate/automatic-reviews.webp"  alt="automation actions" data-aos="fade-left" class="img-responsive rounded aos-init aos-animate lozad" />
                    </div>
                </div>
            </div>
        </section>

        <section class="section">
            <div class="container">
                <div class="row gap-y align-items-center">
                    <div class="col-md-6 text-center text-md-left">
                        <h2 class="display-2 mb-4">
                            Automate Using Webhooks
                        </h2>
                        <p class="lead-2 pt-1">
                            Integrate and automate webhooks for your social media events and actions. Integrate SocialBu with other systems that you use.
                        </p>
                    </div>
                    <div class="col-md-6 pb-0 offset-md-1 ml-0">
                        <img src="/images/1x1.gif" data-src="/images/redesign/automate/webhook.webp" alt="Automation placeholders" data-aos="fade-left" class="img-responsive rounded aos-init aos-animate lozad">
                    </div>
                </div>
            </div>
        </section>
        @include('common.internal.join-us-block')

    </main>

@endsection
