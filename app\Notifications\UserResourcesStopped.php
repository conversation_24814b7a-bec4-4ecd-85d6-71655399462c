<?php

namespace App\Notifications;

use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class UserResourcesStopped extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        /** @var User $notifiable */
        $planDetails = $notifiable->getPlan();
        $usage = $notifiable->getUsage();

        $ret = (new MailMessage)
            ->subject('Critical issue with your current usage of resources')
            ->error()
            ->line('The current usage of resources exceeds the maximum allowed usage of your current plan (' . title_case($planDetails['name']) . ').');

        foreach ($planDetails['limits'] as $key => $value) {
            if (isset($usage[$key]) && $usage[$key] > $value) {
                // user usage is more than the plan's allowed maximum
                $ret = $ret->line('You are using ' . $usage[$key] . ' ' . title_case($key) . ' while maximum allowed in your current plan is ' . $value . ' ' . title_case($key) . '. All ' . title_case($key) . ' have been frozen and will not work unless you resolve this issue.');
            }
        }

        return $ret->line('To resolve this issue, either upgrade your account or delete excess resources that you are using.')
            ->action('Fix This', url('/app/settings#billing'))
            ->line('Please do not hesitate to contact us if you need any assistance or have any questions.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}
