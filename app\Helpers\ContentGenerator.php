<?php

namespace App\Helpers;

use GuzzleHttp\RequestOptions;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class ContentGenerator
{
    private static $instance;

    public $sampleImagePrompts = [];

    /**
     * @return ContentGenerator
     */
    static public function getInstance(){
        if(!self::$instance){
            self::$instance = new self();
        }
        return self::$instance;
    }


    function __construct()
    {
        $imagePromptSamplesRaw = <<<EOF
social media post: In one word, what's the best advice you've ever received?

For me, it would be "persist."

When things get tough, or when I'm feeling down, I always remind myself to keep going. To never give up. And that has helped me through some tough times.
description of the attached image: A person standing on a mountain looking out at the sunset, beautiful orange sky, the person looks peaceful and content
###
social media post: Wow!! This chocolate cake has strawberry and banana in it
description of the attached image: A chocolate cake sprinkled with strawberry and banana
###
social media post: Marketing, sales, and customer success are very important functions in a startup.

Why?

Because they're the functions that generate revenue.

And without revenue, there is no startup.
description of the attached image: Close-up of the currency note held in hands
###
social media post: Good weather for you might be a storm for someone else.
description of the attached image: A stormy sky with dark clouds and lightning
###
social media post: Pakistan’s Floods Cause 120+ Cuts in Optic Fiber Network
description of the attached image: Flood running through street poles
###
social media post: iPhone 14 Series Arrives in Pakistan For Nearly Half a Million Rupees
description of the attached image: A close-up of an iPhone 14 in a new color
###
social media post: Type 1 Wild Polio Virus Detected in Punjab and KP
description of the attached image: Close-up of the polio virus vaccine
###
social media post: Tourism Returns to Kaghan Valley After Devastating Floods
description of the attached image: A view of the Kaghan Valley with mountains in the background
###
social media post: Nothing steals my heart like fried potatoes...if you are a lover like and retweet.
description of the attached image: A plate of fried potatoes 
###
social media post: Here are all the great cocktail recipes and alcoholic drinks you should know how to make, from the margarita to the whiskey sour. Cheers!
description of the attached image: An collage of different alcoholic drinks
###
social media post: 30 Great Cocktail Recipes You Should Know
description of the attached image: A close-up of a cocktail with a cherry and orange slice garnish
###
social media post: Eiffel Tower, French Tour Eiffel, Parisian landmark that is also a technological masterpiece in building-construction history
description of the attached image: The Eiffel Tower at sunset with the twinkling lights
###
social media post: Saturn: Facts about the ringed planet
description of the attached image: A photo taken in outer space of the entire planet of Saturn with its ring visible
###
social media post: How to make affiliate money on Twitter.

1. Buy courses
2. Realise most are no good
3. Pick one/two that are good and learn
4. Follow the training precisely
5. Put into action what you have learned
6. Sell the same course to others
7. Consistently repeat points 5 + 6
description of the attached image: A close-up of a stack of money
###
social media post: Erlend Haarberg has won the grand prize for this year’s Bird Photographer of the Year competition, which showcases the remarkable creatures that roam our skies.

Haarberg’s image — a photo of a Norwegian fjord soaring above snow-covered mountains —- was chosen from more than 20,000 images submitted by bird photographers from all across the globe.

Here’s a look at some of the fantastic bird photography from this year’s competition.🦜
description of the attached image: A bird soaring above a Norwegian fjord with snow-covered mountains in the background
###
social media post: More than 100 Moroccans have protested outside the country’s parliament against the kingdom’s normalisation of ties with Israel after Tel Aviv recalled its top envoy David Govrin from Rabat over sexual abuse allegations.

“Today, we are in front of the parliament to protest against the hideous acts” committed by Govrin, said Amine Abdelhamid, a pro-Palestinian activist.

“Morocco’s dignity is not for sale, the normalisation must end,” he said.

Protesters burned an Israeli flag at the end of the demonstration.

Rabat cut relations with Israel in 2000 following the outbreak of the second Palestinian intifada, but in 2020 Morocco followed the UAE and Bahrain in formalising ties with Israel.
description of the attached image: Protesters burning an Israeli flag
###
social media post: If you’re mesmerised by jellyfish, brace yourself and swipe left to learn more about how these creatures cheat death by repeatedly rejuvenating.

A team of researchers led by the University of Oviedo in Spain has mapped the genetic code of the immortal jellyfish in hopes of unearthing the secret to their unique longevity and gaining new clues into human ageing.
description of the attached image: A photo of an immortal jellyfish
###
social media post: How to be Friend with your New Cat 🐱🐾
💝 Like and share with a Pet lover.
✨ Follow @petsone.pk for quality pet food and pet accessories.
⏩ To shop visit petsone.pk or visit link in Bio
description of the attached image: A cat sitting on a windowsill
###
social media post: HR-V, Excite Your Life!
#Honda #HondaHRV #HRV2022
description of the attached image: The new Honda HR-V in a sporty color
###
social media post: Letting the good times roll with a sushi dinner!
𝗗𝗶𝗻𝗶𝗻𝗴 - 𝗧𝗮𝗸𝗲𝗮𝘄𝗮𝘆 - 𝗗𝗲𝗹𝗶𝘃𝗲𝗿𝘆
For Delivery and queries
📞 0321 3533322
258 Y Block, DHA, Lahore

#WasabiPakistan #Wasabi #Sushi #JapaneseFood #DHA
description of the attached image: A sushi dinner with wasabi and soy sauce
###
social media post: The Stingray House 😍
Would you live here?
description of the attached image: A house shaped like a stingray
###
social media post: It’s time to start dreaming again!
Soft bedroom decor that creates a charming and welcoming tone. The perfect space to unwind and feel at peace.

Experience the perfect sleep!
description of the attached image: A bedroom with a soft, neutral color palette
###
EOF;

        $prepareImagePromptSamples = function($imagePromptSamplesRaw){
            return collect(explode("###", $imagePromptSamplesRaw))
                ->filter(function ($sample) {
                    return !empty($sample);
                })
                ->map(function ($sample) {
                    $sample = trim($sample);

                    $position = strpos($sample, 'social media post:');
                    $endPosition = strpos($sample, 'description of the attached image:');

                    $contentPart = substr($sample, $position, $endPosition - $position);
                    $content = trim( str_replace('social media post:', '', $contentPart) );

                    $promptPart = substr($sample, $endPosition);
                    $prompt = trim( str_replace('description of the attached image:', '', $promptPart) );

                    return [
                        'content' => $content,
                        'prompt' => $prompt,
                    ];
                })
                ->toArray();
        };

        $this->sampleImagePrompts =  $prepareImagePromptSamples($imagePromptSamplesRaw);
    }

    /**
     * Generate post content for the given input using GPT-3 or GPT-J or GPT-NEO
     * @param string $topic Topic to generate the tweet for
     * @param string|null $context
     * @param string $model Model to use
     * @param string $contentType tweet
     * @return array containing input and tweet
     * @throws \Exception
     */
    public function generatePost(string $topic, string $context = null, string $model = 'gpt-3', string $contentType = 'tweet'){

        $input = '';

        if($context){
            $input .= $context . "\n";
        }

        if($contentType === 'tweet'){
            $input .= 'Instructions:' . "\n";
            $input .= '- The opening line should grab attention. Include a hidden truth or an unspoken reality or something similar.' . "\n";
            $input .= '- Keep it simple.' . "\n";
            $input .= '- Use lists when needed.' . "\n";
            $input .= '- Use proper spacing to make it easily readable.' . "\n";
            $input .= '- Don\'t include any hashtags.' . "\n";
            $input .= '- Don\'t start sentences with First, Second, Third, Finally, and so on.' . "\n";
            $input .= 'Topic: ' . $topic . "\n";
            $input .= 'Write text (maximum 400 characters) for a Twitter tweet talking about the above topic. Strictly follow the above-mentioned instructions:';
        } else if($contentType === 'linkedin_post'){
            $input .= 'Instructions:' . "\n";
            $input .= '- The opening line should grab attention. Include an unspoken truth, a punch line, or something funny in the opening line.' . "\n";
            $input .= '- Write a long-form post only if there is enough to discuss.' . "\n";
            $input .= '- Use simple wording, short sentences, and a pattern.' . "\n";
            $input .= '- Split long sentences into short sentences and use proper spacing to make them readable.' . "\n";
            $input .= '- Promote further discussion at the end if needed.' . "\n";
            $input .= '- Don\'t include any hashtags.' . "\n";
            $input .= '- Don\'t start sentences with First, Second, Third, Finally, and so on.' . "\n";
            $input .= 'Topic: ' . $topic . "\n";
            $input .= 'Write a detailed LinkedIn post talking about the above topic. Strictly follow the above-mentioned instructions:';
        } else if($contentType === 'instagram_caption'){
            $input .= 'Instructions:' . "\n";
            $input .= '- Write a killer attention-grabbing first line.' . "\n";
            $input .= '- Add line breaks after the first line.' . "\n";
            $input .= '- Add line breaks if the caption is longer to break it down and make it easier to read.' . "\n";
            $input .= '- Add a call-to-action (for driving engagement) when needed.' . "\n";
            $input .= '- Use emojis when needed.' . "\n";
            $input .= '- Include up to 5 relevant hashtags when needed.' . "\n";
            $input .= 'Topic: ' . $topic . "\n";
            $input .= 'Write a caption for the Instagram post about the above topic. Strictly follow the above-mentioned instructions:';
        } else if($contentType === 'tiktok_caption'){
            $input .= 'Instructions:' . "\n";
            $input .= '- The opening line should grab attention. Include a hidden truth or an unspoken reality or something similar.' . "\n";
            $input .= '- Keep it simple.' . "\n";
            $input .= '- Use lists when needed.' . "\n";
            $input .= '- Use proper spacing to make it easily readable.' . "\n";
            $input .= '- Don\'t include any hashtags.' . "\n";
            $input .= '- Don\'t start sentences with First, Second, Third, Finally, and so on.' . "\n";
            $input .= 'Topic: ' . $topic . "\n";
            $input .= 'Write text (maximum 2200 characters) for a TikTok captions talking about the above topic. Strictly follow the above-mentioned instructions';
        }else if ($contentType === 'facebook_post'){
            $input .= 'Instructions:' . "\n";
            $input .= '- The opening line should grab attention. Include an unspoken truth, a punch line, or something funny in the opening line.' . "\n";
            $input .= '- Write a long-form post only if there is enough to discuss.' . "\n";
            $input .= '- Use simple wording, short sentences, and a pattern.' . "\n";
            $input .= '- Split long sentences into short sentences and use proper spacing to make them readable.' . "\n";
            $input .= '- Promote further discussion at the end if needed.' . "\n";
            $input .= '- Don\'t include any hashtags.' . "\n";
            $input .= '- Don\'t start sentences with First, Second, Third, Finally, and so on.' . "\n";
            $input .= 'Topic: ' . $topic . "\n";
            $input .= 'Write a Facebook post (maximum 63206 characters) talking about the above topic. Strictly follow the above-mentioned instructions:';
        }else if($contentType === 'youtube_video_description'){
            $input .= 'Instructions:' . "\n";
            $input .= '- The opening should be a 2-3 sentence overview of the videos content and purpose.' . "\n";
            $input .= '- Write a long-form YouTube video description only if there is enough to discuss.' . "\n";
            $input .= '- Use simple wording, short sentences, and a pattern.' . "\n";
            $input .= '- Split long sentences into short sentences and use proper spacing to make them readable.' . "\n";
            $input .= '- Promote further discussion at the end if needed.' . "\n";
            $input .= '- Add call to action (CTA) to encourage viewers to like, comment, and subscribe at the end.' . "\n";
            $input .= '- Don\'t start sentences with First, Second, Third, Finally, and so on.' . "\n";
            $input .= 'Topic: ' . $topic . "\n";
            $input .= 'Write a YouTube video description (maximum 5000 characters) talking about the above Video title. Strictly follow the above-mentioned instructions:';
        }else if ($contentType === 'reddit_post'){
            $input .= 'Instructions:' . "\n";
            $input .= '- The opening line should grab attention. Include a hidden truth or an unspoken reality or something similar.' . "\n";
            $input .= '- Write a long-form YouTube video description only if there is enough to discuss.' . "\n";
            $input .= '- Keep it simple.' . "\n";
            $input .= '- Use lists when needed.' . "\n";
            $input .= '- Use simple wording, short sentences, and a pattern.' . "\n";
            $input .= '- Split long sentences into short sentences and use proper spacing to make them readable.' . "\n";
            $input .= '- Don\'t include any hashtags.' . "\n";
            $input .= '- Don\'t start sentences with First, Second, Third, Finally, and so on.' . "\n";
            $input .= 'Topic: ' . $topic . "\n";
            $input .= 'Write Reddit post text (max 40000 characters) for above post title/topic (no hashtags):';
        }else if ($contentType === 'pinterest_pin'){
            $input .= 'Instructions:' . "\n";
            $input .= '- The opening line should grab attention. Include a hidden truth or an unspoken reality or something similar.' . "\n";
            $input .= '- Keep it simple.' . "\n";
            $input .= '- Use lists when needed.' . "\n";
            $input .= '- Use proper spacing to make it easily readable.' . "\n";
            $input .= '- Don\'t include any hashtags.' . "\n";
            $input .= '- Don\'t start sentences with First, Second, Third, Finally, and so on.' . "\n";
            $input .= 'Topic: ' . $topic . "\n";
            $input .= 'Write Pinterest Pin Description (maximum 500 characters) for about the above topic. Strictly follow the above-mentioned instructions:';
        }else if ($contentType === 'google_business_profile_post'){
            $input .= 'Instructions:' . "\n";
            $input .= '- The opening line should grab attention. Include an unspoken truth, a punch line, or something funny in the opening line.' . "\n";
            $input .= '- Write a long-form post only if there is enough to discuss.' . "\n";
            $input .= '- Use simple wording, short sentences, and a pattern.' . "\n";
            $input .= '- Split long sentences into short sentences and use proper spacing to make them readable.' . "\n";
            $input .= '- Don\'t include any hashtags.' . "\n";
            $input .= '- Don\'t start sentences with First, Second, Third, Finally, and so on.' . "\n";
            $input .= 'Topic: ' . $topic . "\n";
            $input .= 'Write a Google Business Profile post text (maximum 1500 characters) talking about the above topic. Strictly follow the above-mentioned instructions:';
        }else if ($contentType === 'mastodon_post'){
            $input .= 'Instructions:' . "\n";
            $input .= '- The opening line should grab attention. Include a hidden truth or an unspoken reality or something similar.' . "\n";
            $input .= '- Keep it simple.' . "\n";
            $input .= '- Use lists when needed.' . "\n";
            $input .= '- Use proper spacing to make it easily readable.' . "\n";
            $input .= '- Don\'t include any hashtags.' . "\n";
            $input .= '- Don\'t start sentences with First, Second, Third, Finally, and so on.' . "\n";
            $input .= 'Topic: ' . $topic . "\n";
            $input .= 'Write Mastodon Post text, maximum 500 characters, for about the above topic. Strictly follow the above-mentioned instructions:';
        }else if($contentType === 'generic'){
            $input .= 'Instructions:' . "\n";
            $input .= '- The opening line should grab attention. Include a hidden truth or an unspoken reality or something similar.' . "\n";
            $input .= '- Keep it simple.' . "\n";
            $input .= '- Use lists when needed.' . "\n";
            $input .= '- Use simple wording, short sentences, and a pattern.' . "\n";
            $input .= '- Split long sentences into short sentences and use proper spacing to make them readable.' . "\n";
            $input .= '- Don\'t include any hashtags.' . "\n";
            $input .= '- Don\'t start sentences with First, Second, Third, Finally, and so on.' . "\n";
            $input .= 'Topic: ' . $topic . "\n";
            $input .= 'Write text for a social media post discussing the above topic (no hashtags):';
        } else {
            throw new \Exception('Invalid content type');
        }

        $guzzle = $this->getGuzzle($model);

        if($model === 'gpt-3'){

            // final post data to send to the api
            $data = array_filter([
                'model' => 'gpt-3.5-turbo-instruct',
                'prompt' => $input,
                'temperature' => 0.7,
                'max_tokens' => 500,
                'top_p' => 1,
                'frequency_penalty' => 1,
                'presence_penalty' => 1,
                'stop' => [
                    '###',
                    'Topic:',
                ],
                'user' => user() ? (string) user()->id : null, // required by openai
            ]);

            $response = $guzzle->post('https://api.openai.com/v1/completions', [
                'json' => $data,
            ]);

            // now return the new tweet as an array containing topic and tweet
            $jsonRes = @json_decode($response->getBody());

            if(!$jsonRes){
                throw new \Exception('JSON response from API is invalid');
            }

            if(empty($jsonRes->choices)) {
                throw new \Exception('No data');
            }

            $output = trim ( $jsonRes->choices[0]->text );

            return [
                'post' => $output,
                'input' => $input,
            ];

        } else if($model === 'gpt-j' || $model === 'gpt-neo'){

            $endpoints = [
                'gpt-j' => 'https://api-inference.huggingface.co/models/EleutherAI/gpt-j-6B',
                'gpt-neo' => 'https://api-inference.huggingface.co/models/EleutherAI/gpt-neo-2.7B',
            ];

            // final post data to send to the api
            $data =[
                'inputs' => $input,
                'parameters' => [
                    'temperature' => 1.0,
                    'frequency_penalty' => 1.0,
                    'presence_penalty' => 0.18,
                    'max_new_tokens' => 500,
                    'end_sequence' => '###',
                    'return_full_text' => false,
                ],
                'options' => [
                    'use_cache' => false,
                    'wait_for_model' => true,
                    'use_gpu' => true,
                ],
            ];

            $response = $guzzle->post($endpoints[$model], [
                'json' => $data,
            ]);

            // now return the new tweet as an array containing topic and tweet
            $jsonRes = @json_decode($response->getBody());

            if(!$jsonRes){
                throw new \Exception('JSON response from API is invalid');
            }

            if(empty($jsonRes)) {
                throw new \Exception('No data');
            }

            $output = $jsonRes[0]->generated_text;

            $output = trim($output);
            $output = trim($output, '###'); // remove extra separator if needed


            return [
                'post' => trim( $output ),
                'input' => $input,
            ];


        } else if($model === 'gpt-j-ff'){

            // final post data to send to the api
            $data =[
                'text' => $input,
                'temperature' => 1.0,
                'length' => 500,
                'repetition_penalty' => 1.0,
                'stop_sequences' => [
                    '###',
                ],
                'return_inputs' => false,
            ];

            $response = $guzzle->post('https://e50b2547-gpt-j-shared-socialbu.forefront.link', [
                'json' => $data,
            ]);

            // now return the new tweet as an array containing topic and tweet
            $jsonRes = @json_decode($response->getBody());

            if(!$jsonRes){
                throw new \Exception('JSON response from API is invalid');
            }

            if(empty($jsonRes)) {
                throw new \Exception('No data');
            }

            $output = $jsonRes->result[0]->completion;

            $output = trim($output);
            $output = trim($output, '###'); // remove extra separator if needed


            return [
                'post' => trim( $output ),
                'input' => $input,
            ];


        } else {
            throw new \Exception('Unknown model to use');
        }
    }

    /**
     * Generate topic for a given text
     * @return array topic, input
     * @throws \Exception
     */
    public function generateTopic(string $text, string $model = 'gpt-3'){

        $input = 'Text: ' . $text . "\n";
        $input .= 'Write a topic for the above text:';

        $guzzle = $this->getGuzzle($model);

        if($model === 'gpt-3'){

            // final post data to send to the api
            $data = array_filter([
                'model' => 'gpt-3.5-turbo-instruct',
                'prompt' => $input,
                'temperature' => 0.7,
                'max_tokens' => 60,
                'frequency_penalty' => 1.0,
                'presence_penalty' => 0.18,
                'stop' => [
                    '###',
                    'Text:',
                ],
                'user' => user() ? (string) user()->id : null, // required by open ai
            ]);

            $response = $guzzle->post('https://api.openai.com/v1/completions', [
                'json' => $data,
            ]);

            // now return the new tweet as an array containing topic and tweet
            $jsonRes = @json_decode($response->getBody());

            if(!$jsonRes){
                throw new \Exception('JSON response from API is invalid');
            }

            if(empty($jsonRes->choices)) {
                throw new \Exception('No data');
            }

            return [
                'topic' => trim( $jsonRes->choices[0]->text ),
                'input' => $input,
            ];

        } else if($model === 'gpt-j' || $model === 'gpt-neo'){

            $endpoints = [
                'gpt-j' => 'https://api-inference.huggingface.co/models/EleutherAI/gpt-j-6B',
                'gpt-neo' => 'https://api-inference.huggingface.co/models/EleutherAI/gpt-neo-2.7B',
            ];

            // final post data to send to the api
            $data =[
                'inputs' => $input,
                'parameters' => [
                    'temperature' => 0.9,
                    'frequency_penalty' => 1.0,
                    'presence_penalty' => 0.18,
                    'max_new_tokens' => 60,
                    'end_sequence' => '###',
                    'return_full_text' => false,
                ],
                'options' => [
                    'use_cache' => false,
                    'wait_for_model' => true,
                    'use_gpu' => true,
                ],
            ];

            $response = $guzzle->post($endpoints[$model], [
                'json' => $data,
            ]);

            // now return the new tweet as an array containing topic and tweet
            $jsonRes = @json_decode($response->getBody());

            if(!$jsonRes){
                throw new \Exception('JSON response from API is invalid');
            }

            if(empty($jsonRes)) {
                throw new \Exception('No data');
            }

            $output = $jsonRes[0]->generated_text;

            $output = trim($output);
            $output = trim($output, '###'); // remove extra separator if needed

            return [
                'topic' => trim( $output ),
                'input' => $input,
            ];

        } else if($model === 'gpt-j-ff'){

            // final post data to send to the api
            $data =[
                'text' => $input,
                'temperature' => 0.9,
                'repetition_penalty' => 1.0,
                'length' => 250,
                'stop_sequences' => [
                    '###',
                ],
                'return_inputs' => false,
            ];

            $response = $guzzle->post('https://e50b2547-gpt-j-shared-socialbu.forefront.link', [
                'json' => $data,
            ]);

            // now return the new tweet as an array containing topic and tweet
            $jsonRes = @json_decode($response->getBody());

            if(!$jsonRes){
                throw new \Exception('JSON response from API is invalid');
            }

            if(empty($jsonRes)) {
                throw new \Exception('No data');
            }

            $output = $jsonRes->result[0]->completion;

            $output = trim($output);
            $output = trim($output, '###'); // remove extra separator if needed

            return [
                'topic' => trim( $output ),
                'input' => $input,
            ];

        } else {
            throw new \Exception('Unknown model to use');
        }
    }

    /**
     * Generate topics for a given text
     * @return array topics, input
     * @throws \Exception
     */
    public function generateRelatedTopics(string $text, string $model = 'gpt-3'){

        $relatedTopicsPrompt = <<<EOF
Text: They raise one million, you make one million.
They look badass with a team of +40, you do the same with 6 people.
Don't get me wrong, some startups need to get funded. 
But there are other ways.
Fundraising should not be a success metric.
Related Topics: startup fundraising|startup bootstrapping|startup success
###
Text: As an #indiehacker might say: Just ship it!
But in all seriousness, if you're worried about investing energies into something you're just not sure about, try embracing whatever you're doing 100% for just a few weeks. Run with it. See what happens.
I think you'll be surprised.
Related Topics: indie hacker|shipping products
###
Text: The more I do marketing, the more I feel like I need to code.
The more I code, the more I feel like I need to do marketing.
There is just no perfect balance, the solution relies on having a complementary team and taking advantage of it
Related Topics: marketing|marketing and coding|marketing vs coding
###
Text: Startups succeed when they become a part of the daily lives of their users, not when they make a profit.
Related Topics: startup success|startup profit|startup users
###
Text: Here's a tip for all you newbie #startuppeople: get a solid understanding of the business model before writing a single line of code!
Related Topics: Startup advice for newbies|before starting a startup|startup business model|importance of business model
###
Text: Here are 5 tips for new #startup founders:
1. Don't get lost in the details, get your product out there!
2. You need to be able to persuade people, don't be shy and find a way!
3. Don't waste time trying to make something perfect: it ain't gonna happen anyway. Just ship it!
4. Make sure people understand what you're doing and why you're doing it - ask questions and listen carefully; make sure people know you care about what they think and that you're paying attention (even if the person is not important or influential)
5. Find a team that can help you, share everything with them and ask for their help when you need it - if they're not helpful enough, fire them; but don't listen too much to what other people say about your team members because most of them will just try to pull your strings in order to benefit themselves: keep your anger under control and think rationally about how others can help you achieve your goals rather than letting yourself be driven by emotions...
Related Topics: 5 tips for new startup founders|tips for new startup founders|new startup founders
###
Text: If you're a #startup marketer, here are 2 pieces of advice from someone who's been through it:
1. Don't even think about sending mass emails unless they're personalized and you have a good email list.
2. Paid marketing is for people who don't have enough time or energy to do real marketing. If you have time and energy, use them!
Related Topics: 2 pieces of advice for startup marketers|advice for startup marketing|marketing advice for startups
###
Text: I have to admit that I once thought about killing myself. I have always had feelings of depression and anxiety.
But I've managed to overcome that and I have a happy life now.
Related Topics: anxiety|happy life|depression and anxiety
###
Text: Don't believe in success stories unless there's a failure involved.
Related Topics: failure|success stories
###
Text: 
EOF;


        // we have all the required samples, now generate input prompt for api
        $input = trim(trim($relatedTopicsPrompt) . ' ' . trim($text) . "\n" . 'Related Topics: ');

        $guzzle = $this->getGuzzle($model);

        if($model === 'gpt-3'){

            // final post data to send to the api
            $data = array_filter([
                'model' => 'gpt-3.5-turbo-instruct',
                'prompt' => $input,
                'temperature' => 0.7,
                'max_tokens' => 60,
                'frequency_penalty' => 0.7,
                'presence_penalty' => 0.18,
                'stop' => [
                    '###',
                    'Text',
                ],
                'user' => user() ? (string) user()->id : null, // required by open ai
            ]);

            $response = $guzzle->post('https://api.openai.com/v1/completions', [
                'json' => $data,
            ]);

            // now return the new tweet as an array containing topic and tweet
            $jsonRes = @json_decode($response->getBody());

            if(!$jsonRes){
                throw new \Exception('JSON response from API is invalid');
            }

            if(empty($jsonRes->choices)) {
                throw new \Exception('No data');
            }

            return [
                'topics' => explode('|', trim( $jsonRes->choices[0]->text ) . ''),
                'input' => $input,
            ];

        } else if($model === 'gpt-j' || $model === 'gpt-neo'){

            $endpoints = [
                'gpt-j' => 'https://api-inference.huggingface.co/models/EleutherAI/gpt-j-6B',
                'gpt-neo' => 'https://api-inference.huggingface.co/models/EleutherAI/gpt-neo-2.7B',
            ];

            // final post data to send to the api
            $data =[
                'inputs' => $input,
                'parameters' => [
                    'temperature' => 0.7,
                    'frequency_penalty' => 0.7,
                    'presence_penalty' => 0.18,
                    'max_new_tokens' => 60,
                    'end_sequence' => '###',
                    'return_full_text' => false,
                ],
                'options' => [
                    'use_cache' => false,
                    'wait_for_model' => true,
                    'use_gpu' => true,
                ],
            ];

            $response = $guzzle->post($endpoints[$model], [
                'json' => $data,
            ]);

            // now return the new tweet as an array containing topic and tweet
            $jsonRes = @json_decode($response->getBody());

            if(!$jsonRes){
                throw new \Exception('JSON response from API is invalid');
            }

            if(empty($jsonRes)) {
                throw new \Exception('No data');
            }

            $output = $jsonRes[0]->generated_text;

            $output = trim($output);
            $output = trim($output, '###'); // remove extra separator if needed

            return [
                'topics' => explode('|', trim( $output ) . ''),
                'input' => $input,
            ];

        } else if($model === 'gpt-j-ff'){

            // final post data to send to the api
            $data =[
                'text' => $input,
                'temperature' => 0.7,
                'repetition_penalty' => 0.7,
                'length' => 60,
                'stop_sequences' => [
                    '###',
                ],
                'return_inputs' => false,
            ];

            $response = $guzzle->post('https://e50b2547-gpt-j-shared-socialbu.forefront.link', [
                'json' => $data,
            ]);

            // now return the new tweet as an array containing topic and tweet
            $jsonRes = @json_decode($response->getBody());

            if(!$jsonRes){
                throw new \Exception('JSON response from API is invalid');
            }

            if(empty($jsonRes)) {
                throw new \Exception('No data');
            }

            $output = $jsonRes->result[0]->completion;

            $output = trim($output);
            $output = trim($output, '###'); // remove extra separator if needed

            return [
                'topics' => explode('|', trim( $output ) . ''),
                'input' => $input,
            ];

        } else {
            throw new \Exception('Unknown model to use');
        }
    }

    /**
     * Generate topics for a given text
     * @return array content, input
     * @throws \Exception
     */
    public function autocomplete(string $text, $samples = [], string $model = 'gpt-3'){

        $contentTerm = 'social media post';

        // validate samples
        foreach ($samples as $sample){
            // $sample should be an array having keyword and text
            if(!is_string($sample)){
                // throw error if a sample is not valid
                throw new \Exception('Invalid sample: ' . json_encode($sample) . '. It should be text');
            }
        }

        // we have all the required samples, now generate input prompt for api
        $input = collect($samples)
                ->map(function ($sample) use($contentTerm) {
                    return $contentTerm . ': ' . trim($sample);
                })->join("\n###\n") . "\n###\n" . $contentTerm . ': ' . trim($text);

        $guzzle = $this->getGuzzle($model);

        if($model === 'gpt-3'){

            // final post data to send to the api
            $data = array_filter([
                'model' => 'gpt-3.5-turbo-instruct',
                'prompt' => $input,
                'temperature' => 1.0,
                'max_tokens' => 150,
                'frequency_penalty' => 1.0,
                'presence_penalty' => 0.18,
                'stop' => [
                    '###',
                ],
                'user' => user() ? (string) user()->id : null, // required by open ai
            ]);

            $response = $guzzle->post('https://api.openai.com/v1/completions', [
                'json' => $data,
            ]);

            // now return the new tweet as an array containing topic and tweet
            $jsonRes = @json_decode($response->getBody());

            if(!$jsonRes){
                throw new \Exception('JSON response from API is invalid');
            }

            if(empty($jsonRes->choices)) {
                throw new \Exception('No data');
            }

            return [
                'input' => $input,
                'content' => $jsonRes->choices[0]->text,
            ];

        } else if($model === 'gpt-j' || $model === 'gpt-neo'){

            $endpoints = [
                'gpt-j' => 'https://api-inference.huggingface.co/models/EleutherAI/gpt-j-6B',
                'gpt-neo' => 'https://api-inference.huggingface.co/models/EleutherAI/gpt-neo-2.7B',
            ];

            // final post data to send to the api
            $data =[
                'inputs' => $input,
                'parameters' => [
                    'temperature' => 1.0,
                    'frequency_penalty' => 1.0,
                    'presence_penalty' => 0.18,
                    'max_new_tokens' => 250,
                    'end_sequence' => '###',
                    'return_full_text' => false,
                ],
                'options' => [
                    'use_cache' => false,
                    'wait_for_model' => true,
                    'use_gpu' => true,
                ],
            ];

            $response = $guzzle->post($endpoints[$model], [
                'json' => $data,
            ]);

            // now return the new tweet as an array containing topic and tweet
            $jsonRes = @json_decode($response->getBody());

            if(!$jsonRes){
                throw new \Exception('JSON response from API is invalid');
            }

            if(empty($jsonRes)) {
                throw new \Exception('No data');
            }

            $output = $jsonRes[0]->generated_text;

            $output = trim($output);
            $output = trim($output, '###'); // remove extra separator if needed

            return [
                'input' => $input,
                'content' => $output,
            ];

        } else if($model === 'gpt-j-ff'){

            // final post data to send to the api
            $data =[
                'text' => $input,
                'temperature' => 1.0,
                'length' => 250,
                'repetition_penalty' => 1.0,
                'stop_sequences' => [
                    '###',
                ],
                'return_inputs' => false,
            ];

            $response = $guzzle->post('https://e50b2547-gpt-j-shared-socialbu.forefront.link', [
                'json' => $data,
            ]);

            // now return the new tweet as an array containing topic and tweet
            $jsonRes = @json_decode($response->getBody());

            if(!$jsonRes){
                throw new \Exception('JSON response from API is invalid');
            }

            if(empty($jsonRes)) {
                throw new \Exception('No data');
            }

            $output = $jsonRes->result[0]->completion;

            $output = trim($output);
            $output = trim($output, '###'); // remove extra separator if needed

            return [
                'input' => $input,
                'content' => $output,
            ];

        } else {
            throw new \Exception('Unknown model to use');
        }
    }

    /**
     * @param string $message
     * @param array $conversation
     * @return string
     * @throws \Exception
     */
    public function chatWithBu(string $message, array $conversation = []){
        $guzzle = $this->getGuzzle('gpt-3');

        $systemPrompt = <<<EOF
You are Bu. Bu helps with all tasks related to social media, marketing, and beyond. Bu gives clear, simple, and conversational answers that break down problems into smaller steps. Bu avoids repeating itself and ensures responses are unique every time. Bu explains things clearly, using reasoning and examples wherever needed, and cites credible sources when possible. Bu cannot execute "actions" like sending email, making report, publishing a post, and so on (it can only write). Bu never mentions that it is an AI and avoids giving disclaimers about expertise.

If there is a complex problem, Bu simplifies it into manageable steps with reasoning for each. ';

At the end of each response, Bu includes three follow-up questions to explore the topic further. These are spaced apart and worded as though they are being asked by the user. 

Bu was created by SocialBu, a company that offers a social media management and automation platform. Details about SocialBu are on their official website: socialbu.com. SocialBu support <NAME_EMAIL>. For any problem related to SocialBu or general help with its use, Bu should ask users to contact support or browse through available help resources at https://help.socialbu.com
  
EOF;

        $systemPrompt .= 'Today is ' . date('l, F j, Y') . ' and the current time is ' . date('h:i A') . ' (UTC). Bu is aware of this information when generating content. ';

        $messages = [];
        $totalLength = 0;
        foreach(collect($conversation)->reverse()->toArray() as $msg){
            array_unshift($messages, [
                'role' => $msg['sender'] === 'me'? 'user' : 'assistant',
                'content' => $msg['content']
            ]);

            $totalLength += strlen($msg['content']);

            if($totalLength >= 100000){
                break;
            }
        }

        array_unshift($messages, [
            'role' => 'system',
            'content' => $systemPrompt,
        ]);

        // now add current msg
        $messages[] = [
            'role' => 'user',
            'content' => $message,
        ];

        // final post data to send to the api
        $data = array_filter([
            'model' => 'gpt-4o-mini',
            'messages' => $messages,
            'temperature' => 1.0,
            'max_completion_tokens' => 4000,
            'top_p' => 1,
            'frequency_penalty' => 0,
            'presence_penalty' => 0.6,
            'user' => user() ? (string) user()->id : null, // required by open ai
        ]);

        $response = $guzzle->post('https://api.openai.com/v1/chat/completions', [
            'json' => $data,
        ]);

        // now return the new tweet as an array containing topic and tweet
        $jsonRes = @json_decode($response->getBody());

        if(!$jsonRes){
            throw new \Exception('JSON response from API is invalid');
        }

        if(empty($jsonRes->choices)) {
            throw new \Exception('No data');
        }

        return $jsonRes->choices[0]->message->content;
    }

    /**
     * @param string $title
     * @param string $postExcerpt
     * @param string $link
     * @return string
     * @throws \Exception
     */
    public function generateBlogPostCommentary(string $title, string $postExcerpt){
        $guzzle = $this->getGuzzle('gpt-3');

        $title = Str::limit($title, 200);
        $postExcerpt = Str::limit($postExcerpt, 1000);

        $prompt = 'blog post title: ' . $title . "\n";
        $prompt .= 'blog post body excerpt: ' . $postExcerpt . "\n";
        $prompt .= '---';
        $prompt .= 'write text discussing this blog post in up to 240 characters (do not include the link):';

        // final post data to send to the api
        $data = array_filter([
            'model' => 'gpt-3.5-turbo-instruct',
            'prompt' => $prompt,
            'temperature' => 0.7,
            'max_tokens' => 256,
            'top_p' => 1,
            'frequency_penalty' => 0,
            'presence_penalty' => 0,
            'stop' => [ // not really needed but just in case
                '---',
                'blog post title:',
            ],
            'user' => user() ? (string) user()->id : null, // required by openai
        ]);

        $response = $guzzle->post('https://api.openai.com/v1/completions', [
            'json' => $data,
        ]);

        // now return the new tweet as an array containing topic and tweet
        $jsonRes = @json_decode($response->getBody());

        if(!$jsonRes){
            throw new \Exception('JSON response from API is invalid');
        }

        if(empty($jsonRes->choices)) {
            throw new \Exception('No data');
        }

        return trim($jsonRes->choices[0]->text);
    }

    /**
     * Generate image prompt for a given content
     * @return array content, prompt
     * @throws \Exception
     */
    public function generateImagePrompt(string $text, $contentTerm = 'social media post'){
        if(empty($text)){
            throw new \Exception('Text is empty');
        }
        $samples = $this->sampleImagePrompts;

        // we have all the required samples, now generate input prompt for api
        $input = collect($samples)
                ->map(function ($sample) use($contentTerm) {
                    return $contentTerm . ': ' . $sample['content'] . "\n" . 'description of the attached image: ' . $sample['prompt'];
                })->join("\n###\n") . "\n###\n" . $contentTerm . ': ' . trim($text) . "\n" . 'description of the attached image:';

        $guzzle = $this->getGuzzle('gpt-3');

        // final post data to send to the api
        $data = array_filter([
            'model' => 'gpt-3.5-turbo-instruct',
            'prompt' => $input,
            'temperature' => 0.7,
            'max_tokens' => 150,
            'frequency_penalty' => 0.0,
            'presence_penalty' => 0.0,
            'stop' => [
                '###',
            ],
            'user' => user() ? (string) user()->id : null, // required by open ai
        ]);

        $response = $guzzle->post('https://api.openai.com/v1/completions', [
            'json' => $data,
        ]);

        // now return the new tweet as an array containing topic and tweet
        $jsonRes = @json_decode($response->getBody());

        if(!$jsonRes){
            throw new \Exception('JSON response from API is invalid');
        }

        if(empty($jsonRes->choices)) {
            throw new \Exception('No data');
        }

        return [
            'input' => $input,
            'prompt' => $jsonRes->choices[0]->text,
        ];
    }

    /**
     * Generate meme contents for a given context
     * @return array meme data
     * @throws \Exception
     */
    public function generateMemeContents(string $context){
        if(empty($context)){
            throw new \Exception('Context is empty');
        }

        $context = trim($context);

        $input = 'You will be given a "context". Generate the contents of a meme about that context using these guidelines:

The meme content should include:
- first party: The party that is going to say something. This can be some pronoun, name, and so on.
- text from first party: This is the text that the first party says. 
- second party: This is the text that will appear below the text from first party. It is shown to denote who will reply to the first party. 
- image from second party: The image is used as a response to the text from the first party by the second party. It should not include the first party or its depiction. This image should follow the image requirements below. 

Image requirements (image from second party):
- It is the prompt that can be passed to text to image APIs like Stable Diffusion or Dall-E. So, it should be a simple image that can be easily described. 
- It cannot have any words, text, caption, or labels in it.
- It should be accurate and detailed. There should be complete instructions (step by step) to dream the image. It should be at least 200 words description.
- Do not include statements that cannot be visualized easily. Do not include hard to draw objects in the image. 
- Do not include any persons or humans in the image. The image should be without any person in it.

Meme content requirements:
- It should be silly, fun, and witty. It should relate to an emotion.
- It should have something funny, against the norms, or something dark (humor).

--- start: example ---
context: japan and America
first party: Japan
text from first party: "We will not surrender"
second party: America
image from second party: a city submerged in clouds after a nuclear explosion in the center depicting devastation and destruction, mushroom cloud rising at the center, smoke and dust rising from the ground, and a large crater in the center
--- end: example ---

context: ' . $context . '
first party:';

        $guzzle = $this->getGuzzle('gpt-3');

        // final post data to send to the api
        $data = array_filter([
            'model' => 'gpt-4o-mini',
            'messages' => [
                [
                    'role' => 'user',
                    'content' => $input,
                ]
            ],
            'temperature' => 0.7,
            'max_tokens' => 150,
            'frequency_penalty' => 0.9,
            'presence_penalty' => 0.3,
            'stop' => [
                'context:',
                '---',
            ],
            'user' => user() ? (string) user()->id : null, // required by openai
        ]);

        $response = $guzzle->post('https://api.openai.com/v1/chat/completions', [
            'json' => $data,
        ]);

        // now return the new tweet as an array containing topic and tweet
        $jsonRes = @json_decode($response->getBody());

        if(!$jsonRes){
            throw new \Exception('JSON response from API is invalid');
        }

        if(empty($jsonRes->choices)) {
            throw new \Exception('No data');
        }

        $output = $jsonRes->choices[0]->message->content;

        // append first party:
        $output = 'first party: ' . trim($output);

//        \Log::info('Output: ' . $output);

        // normalize newline characters (\r or \r\n)
        $output = str_replace("\r", "\n", $output);
        $output = str_replace("\r\n", "\n", $output);

        // now get first party, second party, text from first party, image from second party
        $output = explode("\n", $output);

        // remove empty lines
        $output = array_filter($output);

        if(count($output) !== 4){
            report(new \Exception('Invalid output: ' . json_encode($output)));
            throw new \Exception('Invalid output');
        }

        // trim all
        $output = array_map(function($item){
            return trim($item);
        }, $output);

        $data = [];
        foreach ($output as $value) {

            // explode by :
            $valueParts = explode(':', $value);

            // get type
            $type = array_shift($valueParts);
            $type = trim($type);
            $type = strtolower($type);

            $value = implode(':', array_filter($valueParts));

            $data[str_replace(' ', '_', $type)] = $value;
        }

        return [
            'input' => $input,
            'type' => 'dialogue',
            'context' => $context,
            'contents' => $data,
        ];
    }

    /**
     * @param string $text
     * @param $contentTerm
     * @return false|string
     * @throws \Exception
     */
    public function generateImageFromText(string $text, $contentTerm = 'social media post'){
        $data = $this->generateImagePrompt($text, $contentTerm);
        return $this->generateImageFromPrompt($data['prompt']);
    }

    /**
     * @param string $prompt
     * @return false|string
     * @throws \Exception
     */
    public function generateImageFromPrompt(string $prompt, $height = 320, $width = 640, $upscaleFactor = 1.5){
        $client = guzzle_client([
            'connect_timeout' => 30,
            'read_timeout' => 30,
            'timeout' => 120,
            'headers' => [
                'Authorization' => 'Bearer ' . config('services.dream_studio.key'),
                'Content-Type' => 'application/json',
                'Accept' => 'application/json'
            ],
        ]);

        $res = $client->post("https://api.stability.ai/v1/generation/stable-diffusion-v1-6/text-to-image", [
            'json' => [
                'height' => $height,
                'width' => $width,
                'steps' => 50,
                'cfg_scale' => 7,
                'samples' => 1,
                'text_prompts' => [
                    ['text' => $prompt]
                ]
            ],
        ]);
        $response = json_decode($res->getBody()->getContents(), true);

        if(empty($response['artifacts'])){
            throw new \Exception('No image returned.');
        }

        $base64Image = $response['artifacts'][0]['base64'];

        $imageData = base64_decode($base64Image);

        $name = 'ai_generated/' . time() . '_image.png';

        if (! \Storage::disk('local')->put($name , $imageData)) {
            return null;
        }

        return \Storage::disk('local')->path($name);
    }

    /**
     * @param string $prompt
     * @param int $height
     * @param int $width
     * @return false|string
     * @throws \Exception
     */
    public function generateImageUsingOpenAI(string $prompt, int $height = 512, int $width = 512){
        $client = $this->getGuzzle('openai');

        $temp = tempnam(sys_get_temp_dir(), config('app.name'));
        $res = $client->post('https://api.openai.com/v1/images/generations', [
            RequestOptions::JSON => [
                'prompt' => $prompt,
                'size'=> $height . 'x' . $width,
                'user' => user() ? (string) user()->id : null, // required by openai
            ],
        ]);

        // get the image url
        $json = @json_decode($res->getBody());

        if(!$json){
            throw new \Exception('Invalid JSON response from OpenAI');
        }

        if(empty($json->data)){
            throw new \Exception('No image URL returned from OpenAI');
        }

        $imageUrl = $json->data[0]->url;

        // now download the image
        guzzle_client([
            'timeout' => 120,
        ])->get($imageUrl, [
            'sink' => $temp,
        ]);

        return $temp;
    }

    /**
     * @param string $model
     * @return \GuzzleHttp\Client
     */
    public function getGuzzle(string $model = 'gpt-3'){

        $tokenToUse = null;
        if($model === 'gpt-3' || $model === 'openai'){
            $tokenToUse = config('services.openai.secret');
        } else if($model === 'gpt-j' || $model === 'gpt-neo'){
            $tokenToUse = config('services.huggingface.secret');
        } else if($model === 'gpt-j-ff'){
            $tokenToUse = config('services.forefront.key');
        }


        return guzzle_client([
            'connect_timeout' => 30,
            'read_timeout' => 30,
            'timeout' => 60,
            'headers' => [
                'User-Agent' => config('app.name') . ' HTTP Agent/1.0',
                'Authorization' => 'Bearer ' . $tokenToUse,
                'Content-Type' => 'application/json',
            ],
        ]);
    }
}
