<?php

namespace App\Observers;

use App\Automation;
use App\Helpers\EmailListHelper;

class AutomationObserver
{

    /**
     * Handle the "created" event.
     *
     * @param Automation $automation
     * @return void
     */
    public function created(Automation $automation)
    {
        // send event
        try {
            EmailListHelper::getInstance()->sendEvent($automation->user, 'automation_created', $automation->description);
            trigger_team_activity($automation->team, $automation->user);
        } catch (\Exception $exception){
            report($exception);
        }
    }

    /**
     * Handle the "deleting" event.
     *
     * @param Automation $automation
     * @return void
     */
    public function deleting(Automation $automation)
    {
        //
    }

    /**
     * Handle the "deleted" event.
     *
     * @param Automation $automation
     * @return void
     */
    public function deleted(Automation $automation)
    {
        try {
            $automation->logs()->delete();
        } catch (\Exception $exception){
            report($exception);
        }

        // send event
        try {
            EmailListHelper::getInstance()->sendEvent($automation->user, 'automation_deleted', $automation->description);
        } catch (\Exception $exception){
            report($exception);
        }
    }
}
