<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class SendHTTPRequest implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 60;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 120;

    private $method;
    private $url;
    private $options;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(string $method, string $url, array $options = [])
    {
        $this->method = $method;
        $this->url = $url;
        $this->options = $options;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Exception|\Throwable
     */
    public function handle()
    {
        $client = guzzle_client();
        try {
            $client->request($this->method, $this->url, $this->options);
        } catch (\Exception $exception){
            if ($this->attempts() < 50){
                $this->release(10);
            } else {
                throw new \Exception('Failed to send request');
            }
        }
    }

    /**
     * The job failed to process.
     *
     * @param \Exception $exception
     * @return void
     * @throws \Exception
     */
    public function failed(\Exception $exception)
    {
        if($exception instanceof \Illuminate\Queue\MaxAttemptsExceededException){
            // do nothing?
            return;
        }

        throw $exception;
    }
}
