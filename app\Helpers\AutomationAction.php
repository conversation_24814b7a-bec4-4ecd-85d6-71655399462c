<?php

namespace App\Helpers;


use <PERSON>\TwitterOAuth\TwitterOAuth;
use App\Account;
use App\Automation;
use App\Post;
use App\PublishQueue;
use App\User;
use Carbon\Carbon;
use Facebook\Facebook;
use GuzzleHttp\Client;
use GuzzleHttp\RequestOptions;
use Illuminate\Http\UploadedFile;
use Illuminate\Mail\Message;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Mimey\MimeTypes;

class AutomationAction
{
    protected $automation;
    protected $action;
    protected $data;
    protected $eventData;
    protected $exportedData = null;

    /**
     * AutomationAction constructor
     *
     * @param Automation $automation
     * @param array $action
     * @param array $data
     * @param array $eventData
     * @throws \Exception
     */
    function __construct(Automation $automation, array $action, array $data, array $eventData = [])
    {
        $this->action = $action;
        $this->data = $data;
        $this->eventData = $eventData;
        $this->automation = $automation;

        if(!$automation->user){
            // disable automation
            $automation->deactivate('The original user who created this automation is no longer available.');
            return;
        }

        if(isset($automation->event_data['account'])) { // check if account is available (particularly added for team-shared accounts)

            /** @var Account|null $account */
            $account = Account::find($automation->event_data['account']);

            if(!$account) {
                $automation->deactivate('The attached account is no longer available.');
                return;
            }

            /** @var User $user */
            $user = $automation->user;

            $availableAccountIds = $user->getAvailableAccounts(['id'])->map(function ($acc) {
                return $acc->id;
            })->toArray();

            if (!in_array($account->id, $availableAccountIds)) {
                // disable automation
                $automation->deactivate('The attached account is no longer available.');
                return;
            }

        }

        $fn_to_execute = camel_case(str_replace('.', '_', $action['type'])); // converts new_post to newPost
        if(method_exists($this, $fn_to_execute)) {
            $ret = $this->{$fn_to_execute}();
            if($ret){
                // export data from this action to be available for later actions
                $this->exportedData = $ret;
            }
        } else {
            report(new \Exception('Action method not found: ' . json_encode($action)));
        }
    }

    /**
     * Return exported data from this action
     *
     * @return mixed
     */
    public function getData(){
        return $this->exportedData;
    }

    /**
     * @throws \Exception
     */
    private function sendReply(){

        if(!isset($this->eventData['network'])) return; // don't bother (should never happen)

        $content = placeholders_replace($this->action['data']['content'], $this->data);
        if(mb_strlen(trim($content)) === 0){
            throw new \Exception('Reply content is not available');
        }

        $automation = $this->automation;

        if(!isset($automation->event_data['account'])){
            // should never happen
            report(new \Exception('`account` not set on automation: ' . $automation->id));
            return;
        }

        /** @var Account $account */
        $account = Account::find($automation->event_data['account']);

        if($this->eventData['network'] === 'facebook'){

            /** @var Facebook $fb */
            $fb = $account->getApi();

            // check from eventData, where and how to send reply
            if($this->eventData['type'] === 'conversation'){
                // reply as a message
                try {

                    $fb->post('/me/messages', [
                        'messaging_type' => 'RESPONSE',
                        'recipient' => [
                            'id' => $this->eventData['id'],
                        ],
                        'message' => [
                            'text' => $content
                        ],
                    ]);

                    /*
                    $fb->post('/' . $this->eventData['id'] . '/messages', [
                        'message' => $content,
                    ]);
                    */

                } catch (\Exception $e){
                    throw new \Exception('Unable to reply to message', 0, $e);
                }
            } else if(in_array($this->eventData['type'], ['post', 'comment'])){
                // reply as a comment
                try {
                    $fb->post('/' . $this->eventData['id'] . '/comments', [
                        'message' => $content,
                    ]);
                } catch (\Exception $e){
                    throw new \Exception('Unable to reply to ' . $this->eventData['type'], 0, $e);
                }
            }
        }
        else if($this->eventData['network'] === 'twitter'){

            /** @var TwitterOAuth $tw */
            $tw = $account->getApi();

            if($this->eventData['type'] === 'message'){
                // send reply as a message
                try {
                    $tw->post("direct_messages/events/new", [
                        'event' => [
                            'type' => 'message_create',
                            'message_create' => [
                                'target' => [
                                    'recipient_id' => $this->eventData['id'],
                                ],
                                'message_data' => [
                                    'text' => $content,
                                ],
                            ],
                        ],
                    ], true);
                } catch(\Exception $e) {
                    throw new \Exception('Unable to send reply via Twitter', 0, $e);
                }

                if($tw->getLastHttpCode() != 200){
                    throw new \Exception('Unable to send reply via Twitter', 0, new \Exception('Twitter HTTP error ' . $tw->getLastHttpCode() . ': ' . json_encode($tw->getLastBody())));
                }
            } else if($this->eventData['type'] === 'tweet'){
                // send reply as a tweet
                try {
                    $tw->post("statuses/update", [
                        'status' => $content,
                        'auto_populate_reply_metadata' => 'true',
                        'in_reply_to_status_id' => $this->eventData['id'],
                    ]);
                } catch(\Exception $e) {
                    throw new \Exception('Unable to send reply via Twitter', 0, $e);
                }

                if($tw->getLastHttpCode() != 200){
                    throw new \Exception('Unable to send reply via Twitter', 0, new \Exception('Twitter HTTP error ' . $tw->getLastHttpCode() . ': ' . json_encode($tw->getLastBody())));
                }
            }
        }
        else if($this->eventData['network'] === 'instagram_api'){
            if(in_array($this->eventData['type'], ['comment'])){
                // reply as a comment
                $fb_endpoint = '/' . $this->data['comment_id'] . '/replies';
                $postData = [
                    'message' => $content,
                ];
                /** @var Facebook $fb */
                $fb = $account->getApi();
                try {
                    $res = $fb->post($fb_endpoint, $postData);
                }catch(\Exception $exception){
                    throw new \Exception( 'Unable to reply: ');
                }
            }
        }
    }

    /**
     * @throws \Exception
     */
    private function sendEmail(){
        $email = $this->action['data'];
        $data = $this->data;
        try {
            \Mail::send([], [], function($message) use ($email, $data) {
                /** @var Message $message */

                $recipients = explode(',', $email['recipients']);
                foreach ($recipients as &$em){
                    $em = trim($em);
                }
                $message->to($recipients);
                $message->subject(placeholders_replace($email['subject'], $data));
                $message->setBody(placeholders_replace($email['body'], $data), 'text/html');
            });
        } catch (\Exception $e){
            throw new \Exception('Unable to send email', 0, $e);
        }
    }

    /**
     * @throws \Exception
     */
    private function newPost(){
        $content = placeholders_replace($this->action['data']['content'], $this->data);
        /*
        if(mb_strlen(trim($content)) === 0){
            throw new \Exception('Content is not available');
        }
        */
        $accounts = $this->action['data']['accounts'];

        /** @var User $user */
        $user = $this->automation->user;

        $availableAccounts = $user->getAvailableAccounts();

        $availableAccountIds = $availableAccounts->map(function($acc){
            return $acc->id;
        })->toArray();

        $files = $this->_getFilesForPost();

        // set default delay if not passed
        if(!isset($this->eventData['count'])){
            $this->eventData['count'] = 1;
        }

        // custom publishing timestamp
        if(!isset($this->action['data']['publish_at']))
            $this->action['data']['publish_at'] = '';

        $this->action['data']['publish_at'] = trim($this->action['data']['publish_at']);

        // publish at if set
        $publishAtTimestamp = placeholders_replace($this->action['data']['publish_at'], $this->data);
        // try parsing the timestamp string
        $publishAt = null;
        if($publishAtTimestamp !== null && $publishAtTimestamp !== '') {
            try {
                if (is_valid_unix_timestamp($publishAtTimestamp)) {
                    $publishAt = Carbon::parse('@' . $publishAtTimestamp);
                } elseif(starts_with($publishAtTimestamp, '+')){
                    // relative time
                    $publishAt = modify_relative_time(now(), $publishAtTimestamp);
                } else {
                    $publishAt = Carbon::parse($publishAtTimestamp);
                }
            } catch (\Exception $exception) {
                // invalid publish at string
                throw new \Exception('publish_at value is invalid. Please pass a valid timestamp.', 0, $exception);
            }
        }

        $finalAccounts = [];

        // first we validate
        foreach($accounts as $account_id){

            if(!in_array($account_id, $availableAccountIds)) continue;

            /** @var Account $account */
            $account = $availableAccounts->firstWhere('id', $account_id);

            // avoid loop for automations triggered from account_post event
            if(isset($this->eventData['type'], $this->eventData['account_id'])){
                if($this->eventData['type'] === 'post' && $this->eventData['account_id'] === $account->id){
                    // source and target accounts are same; so skip
                    continue;
                }
            }

            // make the user logged in, because publish post checks current authenticated user
            \Auth::setUser($user);

            // validate
            try {
                $account->publishPost([
                    'content' => $content,
                    'attachments' => $files,
                    'options' => [
                        'post_source_name' => $this->automation->description,
                    ]
                ], $publishAt ? $publishAt : now()->addMinutes($this->eventData['count']), true);
            } catch (\Exception $exception) {
                foreach($files as $file){
                    if ($file && $file->getRealPath()) {
                        @unlink($file->getRealPath());
                    }
                }
                if($exception instanceof ValidationException){
                    throw new \Exception(implode('. ', Arr::flatten($exception->errors())), 0, $exception);
                }
                throw $exception;
            }

            \Auth::logout(); // clear the session

            $finalAccounts[] = $account;

        }

        // now publish
        $retData = [];
        foreach($finalAccounts as $account){

            // make the user logged in, because publish post checks current authenticated user
            \Auth::setUser($user);

            // actually publish the post
            $post = $account->publishPost([
                'content' => $content,
                'attachments' => $files,
                'source' => 'automation',
                'options' => [
                    'post_source_name' => $this->automation->description,
                ]
            ], $publishAt ? $publishAt : now()->addMinutes($this->eventData['count']));

            if(isset($this->action['data']['trim_link_from_content']) && $this->action['data']['trim_link_from_content']) {
                $post->setOption('trim_link_from_content', 'true');
            }

            $post->setOption('source_automation_id', $this->automation->id);

            $retData[] = Post::transform($post);

            \Auth::logout(); // clear the session

        }

        // clean up
        foreach ($files as $file) {
            if ($file && $file->getRealPath()) {
                @unlink($file->getRealPath());
            }
        }

        return $retData;

    }

    /**
     * @throws \Exception
     */
    private function addPostToQueue(){
        $content = placeholders_replace($this->action['data']['content'], $this->data);

        $queueId = $this->action['data']['queue'];

        /** @var User $user */
        $user = $this->automation->user;

        /** @var PublishQueue|null $queue */
        $queue = PublishQueue::available($user->id)->firstWhere('id', $queueId);

        if(!$queue){
            throw new \Exception('Invalid queue specified');
        }

        $files = $this->_getFilesForPost();

        \Auth::setUser($user);

        $queue->addItem([
            'content' => $content,
            'attachments' => $files
        ]);

        try {
            \Auth::logout();
        } catch (\Exception $exception){}

        // cleanup
        foreach($files as $file){
            if ($file && $file->getRealPath()) {
                @unlink($file->getRealPath());
            }
        }

    }

    /**
     * HTTP request
     *
     * @return string|array|null
     * @throws \Exception
     */
    private function webhookRequest(){
        $client = new Client([
            'connect_timeout' => 60,
            'read_timeout' => 30,
            'timeout' => 30,
            'headers' => [
                'User-Agent' => config('app.name') . ' HTTP Agent/1.0',
            ],
            'verify' => false, // we don't need ssl check, do we?
        ]);
        try {
            if($this->action['data']['method'] === 'POST'){
                $response = $client->post($this->action['data']['url'], [
                    RequestOptions::JSON => json_decode(placeholders_replace($this->action['data']['body'], $this->data, true), true),
                ]);
                $body = (string) $response->getBody();
                try {
                    // return as array
                    $data = @json_decode($body, true);
                    if($data)
                        return [
                            'response' => $data,
                            'is_successful' => true
                        ];
                } catch (\Exception $e) {
                    // probably not json
                }
                // return as string
                return [
                    'response' => $body,
                    'is_successful' => true
                ];
            } else {
                throw new \Exception('Invalid method');
            }
        } catch (\Exception $e){
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * Twitter: like tweet
     * @throws \Exception
     */
    private function likeTweet(){

        return;

        $automation = $this->automation;

        /** @var Account $account */
        $account = Account::find($automation->event_data['account']);


        /** @var TwitterOAuth $tw */
        $tw = $account->getApi();

        if($this->eventData['type'] === 'tweet'){
            // like the tweet
            try {
                $tw->post('favorites/create', [
                    'id' => $this->eventData['id'],
                ]);
            } catch(\Exception $e) {
                throw new \Exception('Unable to like tweet', 0, $e);
            }
            if($tw->getLastHttpCode() != 200){
                throw new \Exception('Unable to like tweet', 0, new \Exception('Twitter HTTP error ' . $tw->getLastHttpCode() . ': ' . json_encode($tw->getLastBody())));
            }
        }
    }

    /**
     * Twitter: re tweet
     * @throws \Exception
     */
    private function retweet(){

        $automation = $this->automation;

        /** @var Account $account */
        $account = Account::find($automation->event_data['account']);


        /** @var TwitterOAuth $tw */
        $tw = $account->getApi();

        if($this->eventData['type'] === 'tweet'){
            // like the tweet
            try {
                $tw->post('statuses/retweet/' . $this->eventData['id']);
            } catch(\Exception $e) {
                throw new \Exception('Unable to retweet', 0, $e);
            }
            if($tw->getLastHttpCode() != 200){
                throw new \Exception('Unable to retweet', 0, new \Exception('Twitter HTTP error ' . $tw->getLastHttpCode() . ': ' . json_encode($tw->getLastBody())));
            }
        }
    }

    /**
     * Twitter: Send DM for new followers who are friends
     * @throws \Exception
     */
    private function sendDm(){

        $automation = $this->automation;

        /** @var Account $account */
        $account = Account::find($automation->event_data['account']);

        /** @var TwitterOAuth $tw */
        $tw = $account->getApi();

        $content = placeholders_replace($this->action['data']['content'], $this->data);
        if(mb_strlen(trim($content)) === 0){
            throw new \Exception('Reply content is not available');
        }

        if($this->eventData['type'] === 'new_follower'){
            // like the tweet
            try {
                $res = $tw->post("direct_messages/events/new", [
                    'event' => [
                        'type' => 'message_create',
                        'message_create' => [
                            'target' => [
                                'recipient_id' => $this->eventData['id'],
                            ],
                            'message_data' => [
                                'text' => $content,
                            ],
                        ],
                    ],
                ], true);
                $data = $res->event;
            } catch(\Exception $e) {
                throw new \Exception('Unable to send DM', 0, $e);
            }
            if($tw->getLastHttpCode() != 200){
                throw new \Exception('Unable to send DM', 0, new \Exception('Twitter HTTP error ' . $tw->getLastHttpCode() . ': ' . json_encode($tw->getLastBody())));
            }
        }
    }


    /**
     * Helper fns
     * @return UploadedFile[]
     * @throws \Exception
     */
    private function _getFilesForPost(){

        if(isset($this->action['data']['attach_media']) && $this->action['data']['attach_media']){

            $mediaUrl = '';

            if(isset($this->data['image'])){
                // rss image
                $mediaUrl = $this->data['image'];
            }

            if(isset($this->data['media_url'])){
                // can be image or video, both
                $mediaUrl = $this->data['media_url'];
            }

            if(isset($this->action['data']['attach_media_url'])){
                // user set media
                $mediaUrl = $this->action['data']['attach_media_url'];

                // replace placeholder if any
                $mediaUrl = placeholders_replace($mediaUrl, $this->data);
            }

            if(!empty($mediaUrl)){

                $mediaUrls = str_getcsv($mediaUrl);

                // validate if all urls are correct, otherwise, treat it as one url
                $allValid = true;
                foreach($mediaUrls as $url){
                    if(filter_var($url, FILTER_VALIDATE_URL) === false){
                        $allValid = false;
                        break;
                    }
                }

                if(!$allValid){
                    $mediaUrls = [$mediaUrl];
                }

                $files = [];

                $client = guzzle_client();

                foreach($mediaUrls as $url){
                    // get the file
                    $temp = tempnam(sys_get_temp_dir(), config('app.name'));
                    try {
                        $response = $client->get($url, [
                            'sink' => $temp,
                        ]);

                        $mediaMime = $response->getHeader('content-type');
                        if(!empty($mediaMime)) $mediaMime = $mediaMime[0];
                        if(!$mediaMime){
                            throw new \Exception('Unable to determine type of media: ' . $mediaUrl);
                        }

                        // if codec info is specified, trim that
                        if(Str::contains($mediaMime, ';')){
                            $mediaMime = explode(';', $mediaMime)[0]; // for mimes like video/mp4;codecs=avc1
                        }

                        $mimes = new MimeTypes;
                        $mediaExt = $mimes->getExtension($mediaMime);

                        if(in_array($mediaExt, ['mp4', 'm4v', 'mov', 'qt', 'avi', ])){
                            // is video, ok
                        } else if(in_array($mediaExt, ['jpg', 'png', 'jpeg', 'gif', 'webp', ])){
                            // is img, ok
                        } else {
                            throw new \Exception('Unknown type of media: ' . $mediaMime . ' - ' . $mediaExt);
                        }

                        $files[] = new UploadedFile($temp, 'file.' . $mediaExt);
                    } catch (\Exception $exception) {
                        @unlink($temp);
                        throw $exception;
                    }
                }

                return $files;
            }

        }

        return [];
    }
}
