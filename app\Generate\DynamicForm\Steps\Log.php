<?php

namespace App\Generate\DynamicForm\Steps;

use App\Generate\DynamicForm\Step;

class Log extends Step
{
    public function validate()
    {
        $input = $this->input;
        // input should have a key 'message' and 'type'
        if (!isset($input['message']) || !isset($input['type'])) {
            throw new \Exception('Message and type are required');
        }
    }

    public function execute()
    {
        $input = $this->getData(); // getData returns transformed data (all placeholders are replaced)
        // log the message
        if ($input['type'] === 'info') {
            \Log::info($input['message']);
        } elseif ($input['type'] === 'warning') {
            \Log::warning($input['message']);
        } elseif ($input['type'] === 'error') {
            \Log::error($input['message']);
        }

        $this->setOutput([
            'message' => $input['message'],
            'type' => $input['type'],
        ]);
    }
}
