<?php

namespace Tests\Unit;

use App\Helpers\QueueConcurrencyHelper;
use Carbon\Carbon;
use Tests\TestCase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Foundation\Testing\RefreshDatabase;

class HelperTest extends TestCase
{
    /**
     * Test placeholders_replace()
     *
     * @return void
     */
    public function testPlaceholdersReplace()
    {
        $data = [
            'a' => 'hello',
            'b' => [
                'b' => 'world',
            ],
            'c' => true,
            'd' => '"test"',
            'long' => 'hi there how are you doing',
        ];
        $this->assertEquals('hello', placeholders_replace('{{a}}', $data));
        $this->assertEquals('world', placeholders_replace('{{b.b}}', $data));
        $this->assertEquals('true', placeholders_replace('{{c}}', $data));
        $this->assertEquals(json_encode($data['b']), placeholders_replace('{{b}}', $data));
        $this->assertEquals('{"test": "hello"}', placeholders_replace('{"test": "{{a}}"}', $data, true));
        $this->assertEquals('{"test": ""test""}', placeholders_replace('{"test": "{{d}}"}', $data, false));
        $this->assertEquals('{"test": "\\"test\\" "}', placeholders_replace('{"test": "{{d}} "}', $data, true));
        $this->assertEquals('{"test": "{{aaaa}}"}', placeholders_replace('{"test": "{{aaaa}}"}', $data, true));
        $this->assertEquals('true', placeholders_replace('{{c}}', $data));
        $this->assertEquals('hi there how are you...', placeholders_replace('{{long|truncate:20}}', $data));
    }

    public function testPollingMechanism(){

        self::assertTrue(should_poll_linear(now()->subMinutes(2), 1, 60), 'Return true because time - 2 min + 1 min is still in past');

        self::assertFalse(should_poll_exponential(now()->subMinutes(60), 6, 60), 'should be false because the execute time is base time + 64 mins which should be 4 mins in the future');
        self::assertTrue(should_poll_exponential(now()->subMinutes(64), 6, 60), 'should be true because the execute time is base time + 64 mins which should be right now');

    }

    /**
     * @throws \Exception
     */
    public function testQueueConcurrencyHelper(){

        $helper = new QueueConcurrencyHelper('test', 1, 3600);
        self::assertFalse($helper->shouldWait());
        self::assertTrue($helper->shouldNotWait());

        $helper->start();

        self::assertTrue($helper->shouldWait()); // now it should be true

        $helper->stop();

        self::assertFalse($helper->shouldWait()); // now it should be false

        $helper->start();

        Carbon::setTestNow(now()->addHours(2));

        self::assertFalse($helper->shouldWait()); // now it should be false because now time is too late

        $helper->stop();

    }
}
