<?php

namespace App\Observers;

use App\Automation;
use App\Feed;
use App\Helpers\EmailListHelper;
use App\Mail\UserGoodbye;
use App\Mail\WelcomeEmail;
use App\PublishQueue;
use App\User;
use Carbon;
use Swift_Message;

class UserObserver
{
    /**
     * Listen to the User creating event.
     *
     * @param  User  $user
     * @return void
     */
    public function creating(User $user){
        // referral
        $cookie = request()->cookie('referral');
        if(!$user->referred_by && $cookie) {
            /** @var User|null $referredByUser */
            $referredByUser = User::findByPublicId($cookie);
            if($referredByUser) {
                $user->referred_by = $referredByUser->id;
            }
        }
    }

    /**
     * Listen to the User created event.
     *
     * @param  User  $user
     * @return void
     */
    public function created(User $user)
    {
        if(!$user->verified) {
            $user->sendEmailVerification(true);
        }
        //\Mail::to($user)->later(now()->addDays(1), new WelcomeEmail($user));

        // subscribe to email lists
        try {
            EmailListHelper::getInstance()->syncUserContact($user);
        } catch (\Exception $exception){
            report($exception);
        }

        try {
            EmailListHelper::getInstance()->sendEvent($user, 'new_user');
        } catch (\Exception $exception){
            report($exception);
        }
    }

    /**
     * Listen to the User deleting event.
     *
     * @param  User  $user
     * @return void
     */
    public function deleting(User $user)
    {

        if(!$user->isForceDeleting()){
            // this is a temp delete

            // deactivate user accounts if any
            $user->accounts()->update([
                'active' => false
            ]);

            $subscription = $user->subscription('default');
            if($subscription){
                // cancel the subscription if any
                if($subscription->active())
                    $subscription->cancelNow();
            }

            // send a 'good bye' to the user after 24 hrs
            \Mail::to($user)->later(now()->addDays(1), new UserGoodbye($user));

            // unsubscribe from email lists
            try {
                EmailListHelper::getInstance()->syncUserContact($user);
            } catch (\Exception $exception){
                report($exception);
            }

        } else {
            // user is getting permanently deleted
            // so delete from email list
            try {
                EmailListHelper::getInstance()->deleteUserContact($user);
            } catch (\Exception $exception){
                report($exception);
                \Log::info('[' . $user->email . ']' . $exception->getMessage());
            }
        }

    }

    /**
     * Delete user resources
     * @param User $user
     * @throws \Exception
     */
    public function deleted(User $user){
        // delete feeds
        $feeds = Feed::ofUser($user->id)->get();
        foreach ($feeds as $feed){
            $feed->delete();
        }

        // del accounts
        $accounts = $user->accounts;
        foreach ($accounts as $account){
            $account->delete();
        }

        // del automations
        $automations = Automation::ofUser($user->id)->get();
        foreach ($automations as $automation){
            $automation->delete();
        }

        // del publish queues
        $queues = PublishQueue::ofUser($user->id)->get();
        foreach ($queues as $queue){
            $queue->delete();
        }

        // delete misc metrics
        get_insights_db()->table('misc_metrics')->where('user_id', $user->id)->delete();

        try {
            EmailListHelper::getInstance()->sendEvent($user, 'user_deleted');
        } catch (\Exception $exception){
            report($exception);
        }

    }
}
