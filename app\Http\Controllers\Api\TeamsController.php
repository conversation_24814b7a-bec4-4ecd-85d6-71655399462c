<?php

namespace App\Http\Controllers\Api;

use App\Helpers\EmailListHelper;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\Controller;
use App\Team;
use App\User;
use Illuminate\Http\Request;

class TeamsController extends Controller
{
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request)
    {
        // make sure members exist in db, auto invites new members if needed
        $request->merge([
            'members' => $this->getMembersToSave($request->input('members', [])),
        ]);

        $this->validate($request, [
            'name' => 'required|max:255',
            'accounts' => 'required|array|min:1',
            'members.*.id' => 'integer|exists:users,id',
            'members.*.permissions' => 'required|array|min:1',
            'members.*.permissions.*' => 'in:' . implode(',', array_keys(Team::$allPermissions)),
            'accounts.*.id' => 'integer|exists:accounts,id',
            'requires_content_approval' => 'bool',
        ]);

        /** @var User $user */
        $user = \Auth::user();
        if(getUsage('teams') + 1 > getPlanUsage('teams')){
            return response()->json([
                'success' => false,
                'message' => 'Please upgrade your account for adding a new team.'
            ]);
        }

        $team = new Team([
            'name' => $request->input('name'),
            'user_id' => \Auth::id(),
        ]);
        $team->save();

        $team->log('created', [], $user);

        $team->updateMembers($request->input('members', []));
        $team->updateAccounts(collect($request->input('accounts', []))->map(function ($a){
            return $a['id'];
        })->toArray());

        $team->setOption('requires_content_approval', (bool) $request->input('requires_content_approval', false));
        
        return response()->json([
            'success' => true,
            'message' => trans('teams.added')
        ]);
    }

    public function getTeam(Request $request, $id)
    {
        /** @var Team $team */
        $team =  user()->joinedTeams()->find($id);
        if(!$team){
            return response()->json(
                [
                    'message' => 'Team not found'
                ], 404
            );
        }
        return response()->json(Team::transform($team));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     * @throws \Illuminate\Validation\ValidationException
     * @throws \Exception
     */
    public function update(Request $request, $id)
    {
        // make sure members exist in db, auto invites new members if needed
        $request->merge([
            'members' => $this->getMembersToSave($request->input('members', [])),
        ]);

        $this->validate($request, [
            'name' => 'max:255',
            'accounts' => 'array|min:1',
            'members.*.id' => 'integer|exists:users,id',
            'members.*.permissions' => 'required|array|min:1',
            'members.*.permissions.*' => 'in:' . implode(',', array_keys(Team::$allPermissions)),
            'accounts.*.id' => 'integer|exists:accounts,id',
            'requires_content_approval' => 'bool',
        ]);

        /** @var Team $team */
        $team =  user()->joinedTeams()->find($id);
        if(!$team){
            return response()->json(
                [
                    'message' => 'Team not found'
                ], 404
            );
        }

        if(!$team->isAdmin(user())){
            return response()->json(
                [
                    'message' => 'You are not the team admin'
                ], 401
            );
        }

        // update name
        $team->name = $request->input('name');
        $team->save();


        $teamCreator = $team->user;
        if( count($request->input('members', [])) > $teamCreator->getPlanUsage('team_members')){
            
            return response()->json(
                [
                    'message' => 'Please upgrade the user account for more team members per team.'
                ], 401
            );
        }
        $team->updateMembers($request->input('members', []));
        $team->updateAccounts(collect($request->input('accounts', []))->map(function ($a){
            return $a['id'];
        })->toArray(), user());

        $team->setOption('requires_content_approval', (bool) $request->input('requires_content_approval', false));

        // send event
        try {
            EmailListHelper::getInstance()->sendEvent($team->user, 'team_updated', $team->name);
            trigger_team_activity($team, $team->user);
        } catch (\Exception $exception){
            report($exception);
        }
        record_usage_event('team_updated', $team->name);

        return response()->json(
            [
                'success' => true,
                'message' => trans('teams.updated')
            ]
        );
    }

    /**
     * Will convert emails to new users if needed
     * @param array $members
     * @return array
     */
    private function getMembersToSave(array $members){

        foreach ($members as &$member){

            $idOrEmail = trim($member['id']);

            if(filter_var($idOrEmail, FILTER_VALIDATE_EMAIL)){

                // check if email is good
                if(!RegisterController::verifyEmailIsGood($idOrEmail)){
                    // skip this member
                    $member = null;
                    continue;
                }

                $user = User::withTrashed()->where('email', $idOrEmail)->first();

                if(!$user) {
                    // create new user, yes, do it!
                    $user = (new RegisterController())->create([
                        'name' => email_to_name($idOrEmail),
                        'email' => $idOrEmail,
                        'password' => 'no_password',
                    ]);

                    $user->setOption('was_invited', true);
                }

            } else {
                $user = User::withTrashed()->find($idOrEmail);
            }

            if(!$user){
                // skip this member
                $member = null;
                continue;
            }

            if($user->trashed()){
                // restore deleted user
                $user->restore();
            }

            // in case of new user
            if($idOrEmail != $user->id) {
                // $member is updated because we accessing it by reference
                $member['id'] = $user->id;
                $member['name'] = $user->name;
            }
        }

        return array_filter($members);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function destroy($id)
    {
        /** @var Team $team */
        $team = Team::find($id);

        if(!$team){
            return response()->json(
                [
                    'success' => false,
                    'message' => 'Team not found'
                ], 404
            );
        }

        if($team->user_id === \Auth::id()){
            $team->delete();
        } else {
            // leave
            $team->membersWithoutAdmin()->wherePivot('user_id', \Auth::id())->count() === 1 ? $team->membersWithoutAdmin()->detach(\Auth::id()) : abort(404);

            // send event
            try {
                EmailListHelper::getInstance()->sendEvent($team->user, 'team_left', $team->name);
                trigger_team_activity($team, $team->user);
            } catch (\Exception $exception){
                report($exception);
            }
            record_usage_event('team_left', $team->name);
        }
        return response()->json([
            'success' => true,
            'message' => 'Team deleted successfully.'
        ]);
    }
}
