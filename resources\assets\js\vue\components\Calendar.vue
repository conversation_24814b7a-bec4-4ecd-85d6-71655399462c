<template>
    <div>
        <div class="d-flex justify-content-between align-items-center">
            <h3>
                Content Calendar
            </h3>
            <div>
                <button type="button" class="btn btn-sm btn-outline-light d-flex align-items-center" title="Filter" data-toggle="modal" data-target="#calendar_filter_form"
                    v-tour:filter_posts="'Filter posts by team, accounts, and so on'" v-tooltip>
                    <i class="ph ph-funnel ph-md mr-2"></i>
                    Filter
                </button>
                <span class="badge badge-secondary badge-number" v-if="activeFilterQuery">
                    {{ activeFilterQuery.split("&").length }}
                </span>
            </div>
        </div>

        <div class="card rounded">

            <div class="card-body px-0">
                <div
                    v-html="overlayLoaderHtml" v-if="busy || !loaded">
                </div>
                <div class="row" v-if="loaded">
                    <div class="col-12 order-1 order-md-0">
                        <FullCalendar ref="fullCalendar"
                            :options="calendarConfig"
                        />
                    </div>
                    <!-- Filter form modal -->
                    <div id="calendar_filter_form" ref="filter_form" class="modal fade" role="dialog" tabindex="-1" aria-hidden="true">

                        <div class="modal-dialog modal-dialog-slideout custom-modal-width right">
                            <div class="modal-content">
                                
                                <div class="modal-header px-5 pt-5 pb-4">
                                    <h5 class="modal-title">
                                        Filter Posts
                                    </h5>
                                    <button type="button" class="close" data-dismiss="modal"><i class="ph ph-x"></i></button>
                                </div>
                                <div class="modal-body px-5">
        
                                    <form @submit.prevent="reloadPosts">
        
                                        <div class="form-group" v-if="teams.length">
                                            <label for="filterTeam">Team</label>
                                            <div>
                                                <select class="w-100" id="filterTeam" v-model.number="filter.team" title="..." v-selectpicker>
                                                    <option value="" v-if="filter.team">
                                                        ...
                                                    </option>
                                                    <option v-for="team in teams" :value="team.id">
                                                        {{ team.name }}
                                                    </option>
                                                </select>
                                            </div>
                                            <span class="small-2">
                                                Filter posts by team
                                            </span>
                                        </div>
        
                                        <div class="form-group">
                                            <label for="filterAccount">Accounts</label>
                                            <select class="w-100" id="filterAccount" multiple v-model.number="filter.accounts" title="..." v-selectpicker>
                                                <optgroup v-for="(network, i) in filterNetworks" :label='network.title' :key="network.title + '_' + i">
                                                    <option v-for="account in getAccountsByType(network.type)" :key="network.type + account.id" :data-content="`<img class='border rounded-circle border-white position-relative network-icon' src='${getIconForAccount(account.type, 'circular')}'><span class='p-2'>${account.name.length > 22 ? account.name.slice(0, 22) + '...' : account.name}</span>`" :value="account.id"></option>
                                                </optgroup>
                                            </select>
                                            
                                            <span class="small-2">
                                                Filter posts by social accounts
                                            </span>
                                        </div>
        
                                        <div class="form-group" v-if="filter.team">
                                            <label for="filterUser">Added by</label>
                                            <div>
                                                <select class="w-100" id="filterUser" v-model.number="filter.user" title="..." v-selectpicker>
                                                    <option v-for="user in filterUsers" :value="user.id">
                                                        {{ user.name + (user.id === userId ? ' (me)': '') }}
                                                    </option>
                                                </select>
                                            </div>
                                            <span class="small-2">
                                                Filter posts by post creator
                                            </span>
                                        </div>
                                    </form>
                                    <div class="d-flex justify-content-between pt-2">
                                        <button class="btn btn-sm btn-light"
                                                :class="{'invisible': !filterQuery }" @click="resetFilter">
                                            Reset
                                        </button>
                                        <button class="btn btn-sm btn-primary"
                                                @click="reloadPosts">
                                            Apply
                                        </button>
                                    </div>
                                </div>
                            </div>
                    </div>
                    </div>
                </div>
            </div>

            <!-- post view modal -->
            <div class="modal" ref="viewItemModal" role="dialog" aria-hidden="true">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" v-if="activeItem && activeItem.story_hash">
                                Story
                            </h5>
                            <h5 class="modal-title" v-else>
                                Post
                            </h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true"><i class="ph ph-x"></i></span>
                            </button>
                        </div>
                        <div class="modal-body p-0">

                            <template v-if="activeItem">
                                <post
                                    v-on:set-attachments="attachments = $event"
                                    v-on:updated="reloadPosts"
                                    v-on:republish="hidePostPopup"
                                    :post="activeItem">
                                </post>
                            </template>
                        </div>
                    </div>
                </div>
            </div>

            <!-- schedule time popup -->
            <div id="edit_time_popup" class="modal" role="dialog" tabindex="-1" aria-hidden="true">
                <!-- modal for schedule popup -->
                <div class="modal-dialog">
                    <!-- Modal content-->
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" v-text="$lang.get('publish.schedule_post')"></h5>
                            <button type="button" class="close" data-dismiss="modal"><i class="ph ph-x"></i></button>
                        </div>
                        <div class="modal-body">
                            <div class="d-flex justify-content-center">
                                <div id="edit_post_publish_at"></div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button data-dismiss="modal" type="button"
                                    class="btn btn-primary" v-text="$lang.get('generic.save')"></button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal for attachment view -->
            <div class="modal fade" id="attachmentModal" tabindex="-1" role="dialog"
                 aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content shadow border border-secondary">
                        <div class="modal-header">
                            <h5 class="modal-title">Media Attachments</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                    aria-hidden="true"><i class="ph ph-x"></i></span></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col mx-auto">
                                    <div class="gallery gallery-4-type2">
                                        <div class="gallery-item" v-for="(attachment,i) in attachments" :key="'att' + i">
                                            <video controls :title="attachment.name"  v-if="['mp4', 'm4v', 'mov', 'qt', 'avi'].includes(attachment.type)">
                                                <source :src="attachment.url" />
                                            </video>
                                            <img :src="attachment.url" :alt="attachment.name" :title="attachment.name" v-else
                                                 class="border rounded"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-light" data-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>


<script>
import PostEditor from "./PostEditor.vue";
import Post from "./Post.vue";
import FullCalendarComponent from "@fullcalendar/vue";
import interactionPlugin from "@fullcalendar/interaction";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import bootstrapPlugin from "@fullcalendar/bootstrap";
import momentTimezonePlugin from "@fullcalendar/moment-timezone";
import createContent from "../../scripts/create_content";
import {
    axios,
    appConfig,
    getColorClassForAccount,
    getIconForAccount,
    axiosErrorHandler,
    overlayLoaderHtml
} from "../../components";
import Cookies from "js-cookie";
import {getAccounts, getNetworks} from "../../data";

export default {
    name: "Calendar",
    components: {
        FullCalendar: FullCalendarComponent,
        PostEditor,
        Post,
    },
    data() {
        const _this = this;
        return {
            busy: false,

            loaded: false,

            calendarConfig: {
                height: "auto",
                initialView: "dayGridMonth",

                dayMaxEventRows: 2, // for all non-TimeGrid views
                views: {
                    timeGrid: {
                        dayMaxEventRows: 6 // adjust to 6 only for timeGridWeek/timeGridDay
                    }
                },
                timeZone: appConfig.timezone,
                plugins: [interactionPlugin, dayGridPlugin, timeGridPlugin, bootstrapPlugin, momentTimezonePlugin],
                themeSystem: "bootstrap",
                showNonCurrentDates: false,
                dateClick: this.handleDateClick,
                headerToolbar: {
                    start: "prev,next",
                    center: "title",
                    end: "dayGridWeek,dayGridMonth"
                },
                customButtons: {
                    addPost: {
                        text: "New Post",
                        click: createContent.newPost
                    },
                },
                eventSources: [
                    async (fetchInfo, successCallback, failureCallback) => {
                        _this.busy = true;
                        try {
                            const { data } = await axios.get(
                                "/app/publish/calendar/posts?timeZone=" +
                                    encodeURIComponent(fetchInfo.timeZone) +
                                    "&start=" +
                                    encodeURIComponent(fetchInfo.startStr) +
                                    "&end=" +
                                    encodeURIComponent(fetchInfo.endStr) +
                                    (this.filterQuery.length ? "&" + this.filterQuery : "")
                            );
                            // save current filter query
                            _this.activeFilterQuery = _this.filterQuery;
                            successCallback(data);
                        } catch (e) {
                            failureCallback(e);
                        } finally {
                            _this.busy = false;
                        }
                    },
                ],
                eventContent: this.eventContent,
                eventDidMount: this.eventDidMount,
                eventWillUnmount: this.eventWillUnmount,
                eventClassNames: this.eventClassNames,
                eventDisplay: "block", // auto
                dayCellContent: this.renderDayCell,

                editable: true,
                droppable: true,
                dragRevertDuration: 500,

                eventClick: this.handleEventClick,
                eventDrop: this.handleEventDrop
            },

            activeFilterQuery: null, // filter querystring used to get current results
            filter: {
                team: null,
                accounts: [],
                user: null
            },

            accounts: [],
            networks : [],

            activeItem: null,
            attachments: [] // for attachment showing in modal
        };
    },
    computed: {
        userId: () => appConfig.userId,
        overlayLoaderHtml: () => overlayLoaderHtml,

        teams() {
            const teamsById = {};
            this.accounts.filter(a => a.team).forEach(a => {
                teamsById[a.team.id] = a.team;
            });
            return Object.values(teamsById);
        },

        // filter
        filterAccounts() {
            if (this.filter.team) {
                return this.accounts.filter(a => a.team && a.team.id === this.filter.team);
            } else {
                return this.accounts.filter(a => !a.team);
            }
        },
        filterNetworks(){
            // only return networks that we have in accounts
            return this.networks.filter(n => {
                return this.filterAccounts.find(a => a.type === n.type);
            });
        },
        filterUsers() {
            if (this.filter.team) {
                const a = this.accounts.find(a => a.team && a.team.id === this.filter.team);
                if (!a) return []; // shouldn't happen

                return a.team.members.map(m => {
                    return {
                        id: m.id,
                        name: m.name
                    };
                });
            }
            return [];
        },
        filterQuery() {
            const filters = [];
            if (this.filter.team) {
                filters.push("team=" + encodeURIComponent(this.filter.team));
            }
            if (this.filter.accounts.length) {
                filters.push(...this.filter.accounts.map(id => "accounts[]=" + encodeURIComponent(id)));
            }
            if (this.filter.user) {
                filters.push("user=" + encodeURIComponent(this.filter.user));
            }
            return filters.join("&");
        }
    },
    watch: {
        "filter.team": function(newVal) {
            // whenever team changes, reset accounts and user
            this.filter.accounts = [];
            this.filter.user = null;
        },
        activeFilterQuery(qs) {
            if (qs) {
                // update cookie
                Cookies.set("calendar_filters", JSON.stringify({ ...this.filter }), {
                    sameSite: "None",
                    secure: true
                });
            } else {
                Cookies.remove("calendar_filters");
            }
        }
    },
    methods: {
        getIconForAccount,
        getAccountsByType(type) {        
            return this.filterAccounts.filter(account => account.type === type);
        },
        getCalendar() {
            return this.$refs.fullCalendar.getApi();
        },
        renderDayCell(arg) {
            const { date } = arg;
            const now = new Date();
            now.setHours(0, 0, 0, 0);
            if (date < now) {
                return; // no past date
            } else {
                const html = `
                    <div class="fc-event-time"> 
                        <div class="cell-date">${arg.date.getDate()} </div>
                        <button class="btn btn-primary btn-sm btn-create-post px-2 py-1"><i class="ph ph-plus ph-lg"></i></button>
                    </div>
                `;
                return { html }
            };
        },
        async handleDateClick(info) {
            // show add new post popup
            const { date } = info;
            const now = new Date();

            now.setHours(0, 0, 0, 0);
            if (date < now) {
                return; // no past date
            } else if (
                "" + date.getDate() + date.getMonth() + date.getFullYear() ===
                "" + now.getDate() + now.getMonth() + now.getFullYear()
            ) {
                // same day
                (await createContent.getPostComposer()).setPublishAt(new Date());
            } else {
                (await createContent.getPostComposer()).setPublishAt(date);
            }
            await createContent.newPost();
        },
        eventClassNames(arg) {
            return "social-border " + getColorClassForAccount(arg.event.extendedProps.account_type);
        },
        eventContent(arg) {
            const event = arg.event;

            let att;
            if (event.extendedProps.item.attachments.length) {
                att = event.extendedProps.item.attachments.find(a => {
                    return ["jpg", "png", "jpeg", "gif"].includes(a.type) && !a.deleted;
                });
            }

            const html = `
            <div class="fc-event-time d-flex justify-content-between flex-md-row flex-column">
                <div>
                    ${arg.timeText}
                    ${event.extendedProps.item.draft ? `<span class="d-md-inline-block d-none badge badge-secondary small-7 ml-2">Draft</span>` : ''}
                </div>
                <span>
                    <img class="position-relative account-network" src="${getIconForAccount(event.extendedProps.account_type)}" /></span>
                </span>
            </div>

            <div class="d-md-block d-none fc-event-title" style="text-overflow: ellipsis">
                ${event.extendedProps.titleHtml}
            </div>

            ${ att ? `<div class="d-md-block d-none fc-event-img text-center mt-1"><img src="${att.url}" alt="Media" class="rounded mx-auto fc-event-img" style="height:80px;width:68px;" /></div>` : ''} ${event.extendedProps.item.draft ? `<div class="draft-post-badge d-md-none d-flex justify-content-center align-items-center p-4 rounded mt-1"><i class="ph ph-note-pencil"></i></div>` : ''} `;

            return {
                html
            }

        },
        eventDidMount(arg) {
            const event = arg.event;
            //replace popover icon to phosphor
            if($(".fc-popover-close").length){
                $(".fc-popover-close").each(function () {
                    $(this).removeClass("fa fa-times").html(""); // Remove old icon
                    $(this).addClass("ph ph-x"); // Add Phosphor icon class
                });
            }
            //
            // const $container = $(arg.el).find(".fc-event-main");
            //
            // const $time = $container
            //     .find(".fc-event-time");
            // $time.detach();
            //
            // $container.prepend($time);
            //
            let att;
            if (event.extendedProps.item.attachments.length) {
                att = event.extendedProps.item.attachments.find(a => {
                    return ["jpg", "png", "jpeg", "gif"].includes(a.type);
                });
            }
            //
            // if (event.extendedProps.item.draft) {
            //     $container
            //         .find(".fc-event-time")
            //         .append(`<span class="badge badge-secondary small-7 ml-2">Draft</span>`);
            // }
            //
            // if (att && window.__test) {
            //     $container.append(`<img src="${att.url}" alt="image" class="img-thumbnail mx-auto fc-event-img" />`);
            // }
            //
            // const $titleContainer = $container.find(".fc-event-title-container");
            // // $titleContainer.detach();
            //
            // $titleContainer.addClass("d-flex");
            //
            // $titleContainer.append(truncate(event.extendedProps.title, { length: 40 }));
            //
            // $titleContainer.find(".fc-event-title").css("text-overflow", "ellipsis").prepend(
            //     `<span><i class="${getIconClassForAccount(event.extendedProps.account_type) +
            //         " " +
            //         getColorClassForAccount(event.extendedProps.account_type)}"></i>
            //     </span>`
            // );
            // $container.append($titleContainer);
            // return;
            // $(arg.el).popover({
            //     content:
            //         `<div>${arg.event.extendedProps.description}</div>` +
            //         (att
            //             ? `<div class="text-center"><img src="${
            //                   att.url
            //               }" alt="Media" class="img-thumbnail popup_img" /></div>`
            //             : ""),
            //     title: `<i class="${getIconClassForAccount(
            //         arg.event.extendedProps.account_type,
            //         true
            //     )} social-icon active"></i> ${arg.event.extendedProps.account}`,
            //     html: true,
            //     trigger: "hover",
            //     placement: "auto",
            //     boundary: "window",
            //     delay: {
            //         show: 500,
            //         hide: 100
            //     }
            // });
        },
        eventWillUnmount(arg) {
            $(arg.el).popover("dispose");
        },
        handleNewPosts(data) {
            // new post(s) added
            this.reloadPosts();
        },
        handleEventClick(info) {
            // if there is popover, close it
            if($(".fc-popover-close").length){
                $(".fc-popover-close").click();
            }
            this.activeItem = null;
            this.$nextTick(() => {
                this.activeItem = info.event.extendedProps.item;
                $(this.$refs.viewItemModal)
                    .one("hidden.bs.modal", () => {
                        this.activeItem = null;
                    })
                    .modal("show");
            });
        },
        async handleEventDrop(eventDropInfo) {
            const { event, revert } = eventDropInfo;
            const item = event.extendedProps.item;
            if (event.extendedProps.item.published) {
                // cannot drag published post
                revert();
                return;
            }
            const date = this.$momentUTC(event.start.toUTCString());

            if (date.toDate() < new Date()) {
                // cannot drag to the past
                revert();
                return;
            }

            try {
                await axios.patch("/app/publish/posts/" + event.id, {
                    publish_at: date.format("YYYY-MM-DD HH:mm:ss")
                });

                // set publish_at on the post info, so it updates it too
                event.extendedProps.item.publish_at = date.utc().format("YYYY-MM-DD HH:mm:ss");
            } catch (e) {
                axiosErrorHandler(e);
                revert();
            }
        },
        reloadPosts() {
            // re load posts
            $(this.$refs.addNewContentModal).modal("hide");
            $(this.$refs.viewItemModal).modal("hide");
            $(this.$refs.filter_form).modal("hide");
            this.getCalendar().refetchEvents();
        },

        // filter
        resetFilter() {
            Object.keys(this.filter).forEach(n => {
                if (typeof this.filter[n] === "string") {
                    this.filter[n] = "";
                } else if (typeof this.filter[n] === "object") {
                    if (Array.isArray(this.filter[n])) {
                        this.filter[n] = [];
                    } else {
                        this.filter[n] = "";
                    }
                } else {
                    this.filter[n] = null;
                }
            });
            this.reloadPosts();
        },
        hidePostPopup() {
            // Hide the modal when the republish event is triggered
            $(this.$refs.viewItemModal).modal('hide');
        }
    },
    async mounted() {
        setTimeout(()=> {
            const $editTimeModal = $("#edit_time_popup");
            $editTimeModal
                .modal({
                    backdrop: "static",
                    keyboard: false,
                    show: false
                })
                .on("shown.bs.modal", async function(e) {
                })
                .on("hidden.bs.modal", function(e) {
                });
        }, 1000)

        this.$nextTick(() => {
            $("#edit_post_publish_at").datetimepicker({
                inline: true,
                sideBySide: true,
                minDate: new Date(),
                timeZone: appConfig.timezone,
                icons: {
                    previous: 'ph ph-caret-left ph-md',
                    next: 'ph ph-caret-right ph-md',
                    up: 'ph ph-caret-up ph-bold',
                    down: 'ph ph-caret-down ph-bold'
                }
            });
        });

        // bind listener for new post event
        const composer = await createContent.getPostComposer();
        composer.$on("postsAdded", this.handleNewPosts);

        if (!this.accounts.length) {
            this.accounts = await getAccounts(true);
            this.networks = await getNetworks(true);
        }

        // set filters from cookie if needed
        const filterJson = Cookies.get("calendar_filters") || "";
        if (filterJson) {
            const filters = JSON.parse(filterJson);
            Object.keys(filters).forEach(k => {
                this.filter[k] = filters[k];
            });
        }

        this.loaded = true;
    },
    async beforeDestroy() {
        // bind listener for new post event
        const composer = await createContent.getPostComposer();
        composer.$off("postsAdded", this.handleNewPosts);
    }
};
</script>

<style lang="scss" scoped>
@import "~@fullcalendar/bootstrap/main.css";
</style>

<style lang="scss">
.fc-daygrid-event{
    margin-top: 4px !important;
    padding: 6px 4px 4px 4px;
    background: #FAFAFA;
}
.fc .fc-daygrid-day-top{
    display: flex;
    justify-content: center;
    flex-direction: row;
}
.fc .fc-daygrid-day-frame {
    display: flex;
    justify-content: center;
    flex-direction: column;
}
.fc-daygrid-day {
    border-right-color: white;
    border-left-color: #E3E6EB;
    .btn-create-post{
        display: none;
        margin-top: 4px;
    }
    .cell-date {
        display: flex;
        justify-content: center;
    }
    &:hover {
        
        .btn-create-post{
            display: block;
            color: white !important;
            background-color: #0557f0 !important;
            border-color: #0557f0 !important;
    
        }
        .cell-date{
            display: none;
        }
    }
}
.fc-toolbar {
    .btn-primary {
        text-transform: capitalize !important;
        background-color: #fff;
        color: #4A5465;
        border-color: #E3E6EB;
        &:focus,
        &.focus, 
        &:hover {
            color: #4A5465;
            background-color: #D6DAE0;
            border-color: #D6DAE0;
            box-shadow: none;
        }
        &:not([disabled]):not(.disabled):active,
        &:not([disabled]):not(.disabled).active,
        .show > &.dropdown-toggle {
            color: #4A5465;
            background-color: #F1F3F5;
            border-color: #D6DAE0;
            box-shadow: none;
        }
    }
}
.fc-event-main{
    background-color: #FAFAFA;
    color: #4A5465 !important;
    font-size: 14px;
    
}
.popup_img {
    max-height: 180px;
    max-width: 100%;
    height: auto;
}
.event_img {
    max-height: 24px;
    display: inline-block;
}
.account-network{
    width: 14px;
    height: 14px;
}
.social-border {
    border: 1px solid;
    border-top: 3px solid;
    border-radius: 2px;
    border-color: #0557f0;

    &.facebook {
        border-color: #0866FF;
    }
    &.instagram {
        border-color: #CD486B;
    }
    &.linkedin {
        border-color: #2867B2;
    }
    &.twitter {
        border-color: #000;
    }
    &.mastodon {
        border-color: #CD486B;
    }
    &.youtube {
        border-color: #F00;
    }
    &.tiktok {
        border-color: #010101;
    }
    &.reddit {
        border-color: #FF4500;
    }
}

.fc-more-link{
    display: flex;
    justify-content: center;
    font-size: 16px;
    
}
@media (max-width: 600px){
    
    .fc-header-toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap; /* Ensures responsiveness */
        .fc-toolbar-title{
            margin-bottom: 12px;
        }
    }
    .fc-more-link{
        color: #0557F0 !important;
    }
    .fc-toolbar-chunk:nth-child(1) {
        order: 2;
    }
    
    .fc-toolbar-chunk:nth-child(2) {
        order: 1;
        flex-grow: 1;
        text-align: left;
        width: 100%;
    }
    
    .fc-toolbar-chunk:nth-child(3) {
        order: 3; /* Ensures week/month buttons stay last */
    }
}
.draft-post-badge {
    background-color: rgba(16, 63, 152, 0.06) !important;
    width: 24px;
    height: 24px;
}
.fc-popover{
    padding-right: 4px;
    .fc-popover-body{
       padding: 12px 4px 12px 12px !important;
       height: 250px;
       width: 172px;
       min-width: 172px !important;
       overflow-y: scroll; 
    }
    .fc-more-popover-misc{
        display: none !important;
    }
    .fc-popover-header {
        background-color: #fff !important;
        border-color: 1px solid #E4E7ED !important;
        padding: 8px 8px 8px 12px !important;
        .fc-popover-title{
            margin: 0px;
            color: #1E283B;
            font-weight: 500;
            font-size: 14px;
        }
    
    }
}
@media (max-width: 600px){
    //make popover center aligned in mobile screens
    .fc-popover{
        top: 25% !important;
        left: 50% !important;
        transform: translate(-50%,-50%) !important;
    }
}
</style>
