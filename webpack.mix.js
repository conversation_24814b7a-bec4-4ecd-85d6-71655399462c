const mix = require("laravel-mix"),
    path = require("path");

// require('laravel-mix-criticalcss');

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel application. By default, we are compiling the Sass
 | file for the application as well as bundling up all the JS files.
 |
 */
// Mix.listen("configReady", webpackConfig => {
//     if (Mix.isUsing("hmr")) {
//         // Remove leading '/' from entry keys
//         webpackConfig.entry = Object.keys(webpackConfig.entry).reduce((entries, entry) => {
//             entries[entry.replace(/^\//, "")] = webpackConfig.entry[entry];
//             return entries;
//         }, {});
//
//         // Remove leading '/' from ExtractTextPlugin instances
//         webpackConfig.plugins.forEach(plugin => {
//             if (plugin.constructor.name === "ExtractTextPlugin") {
//                 plugin.filename = plugin.filename.replace(/^\//, "");
//             }
//         });
//     }
// });

mix.webpackConfig({
    resolve: {
        alias: {
            // Force all modules to use the same jquery version.
            jquery: path.join(__dirname, "node_modules/jquery/src/jquery"),
            // vue$: "vue/dist/vue.runtime.esm.js"
        }
    },
    output: {
        publicPath: "/",
        chunkLoadingGlobal: "jasonPee",
        filename: "[name].js",
        chunkFilename: "js/[name]-[chunkhash].js"
    },
    module: {
        rules: [
            {
                // Matches all PHP or JSON files in `resources/lang` directory.
                test: /resources(\\|\/)lang.+\.(php|json)$/,
                loader: "laravel-localization-loader"
            }
        ]
    }
});

// compile js and css
mix
    .js("resources/assets/js/app.js", "public/js")
    .vue({
        version: 2,
        runtimeOnly: true,
    })
    .js("resources/assets/js/main.js", "public/js")
    .vue({
        version: 2,
        runtimeOnly: true,
    })
    .sass("resources/assets/sass/app.scss", "public/css", {
        // implementation: require("sass")
    })
    .sass("resources/assets/sass/new-app.scss", "public/css", {
        // implementation: require("sass")
    })
    .sass("resources/assets/sass/auth.scss", "public/css", {
        // implementation: require("sass")
    })
    .sass("resources/assets/sass/main.scss", "public/css", {
        // implementation: require("sass")
    })
    .sass("resources/assets/sass/new-main.scss", "public/css", {
        // implementation: require("sass")
    });
    // .criticalCss({ // generates a file that is barely enough
    //     enabled: true, // mix.inProduction(),
    //     paths: {
    //         base: 'https://socialbu.com/',
    //         templates: './public/css/',
    //         suffix: '_critical.min'
    //     },
    //     urls: [
    //         { url: 'https://socialbu.com/', template: 'main' },
    //     ],
    //     options: {
    //         minify: true
    //     },
    // });

// append unique version number to file names if in production
if (mix.inProduction()) {
    mix.version();
}

// copy images and fonts
mix.copy("resources/assets/images", "public/images", false);
mix.copy("resources/assets/fonts", "public/fonts", false);
mix.copy("resources/assets/css", "public/css", false);

// Extract all node_modules vendor libraries into a vendor.js file.
// mix.extract();

mix.options({
    clearConsole: false
});
