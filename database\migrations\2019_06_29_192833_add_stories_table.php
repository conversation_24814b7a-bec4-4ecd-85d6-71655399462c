<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddStoriesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('stories', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->increments('id'); // story id

            $table->string('story_hash')->index(); // hash of this post. will be built using attached media name and user_id and timestamp (created_at)

            $table->string('external_id')->nullable()->default(null); // external id if any

            $table->integer('account_id')->unsigned()->index(); // account on which this story will be published
            $table->foreign('account_id')->references('id')->on('accounts')->onDelete('cascade')->onUpdate('cascade');

            $table->integer('user_id')->unsigned()->index(); // user who added this post
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade')->onUpdate('cascade');

            $table->string('type', 16)->default('image'); // story type: image, video

            $table->json('options')->nullable(); // a general json column for storing any data if needed

            $table->timestamp('publish_at')->nullable()->index(); // scheduled time on which this story should be sent

            $table->timestamp('published_at')->nullable()->index(); // the time at which the story was sent

            $table->json('result')->nullable(); // to store success or error response of api while publishing this story. this can help to see reason. for pending stories, it will be null otherwise api response for success/failed stories. will always contain _success (true or false) and _errorMsg (in case of success: false). _errorMsg is user friendly error msg

            $table->boolean('approved')->default(true);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('stories');
    }
}
