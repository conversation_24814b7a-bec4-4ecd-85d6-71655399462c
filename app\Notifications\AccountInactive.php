<?php

namespace App\Notifications;

use App\Account;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class AccountInactive extends Notification implements ShouldQueue
{
    use Queueable;

    protected $account;

    protected $reason;

    /**
     * Create a new notification instance.
     *
     * @param Account $account
     * @param null $reason
     */
    public function __construct(Account $account, $reason = null)
    {
        $this->account = $account;
        $this->reason = $reason;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->error()
            ->subject('Account Connection Broken')
            ->line('Account: ' . e($this->account->name) . ' (' . e($this->account->getType()) . ') is marked as inactive because connection to the account was broken.')
            ->line('Reason: ' . e($this->reason ? $this->reason : 'Not specified'))
            ->line('Please reconnect this account to keep using it with SocialBu.')
            ->action('Fix Account', url('app/accounts'))
            ->line('Please do not hesitate to contact us if you need any assistance or have any questions.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'account_id' => $this->account->id,
            'account_name' => $this->account->name,
            'account_type' => $this->account->getType(),
            'reason' => $this->reason,
            '_level' => 'danger',
        ];
    }
}
