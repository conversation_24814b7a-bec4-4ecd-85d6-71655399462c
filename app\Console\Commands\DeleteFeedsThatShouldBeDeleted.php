<?php

namespace App\Console\Commands;

use App\Feed;
use Illuminate\Console\Command;

class DeleteFeedsThatShouldBeDeleted extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'feeds:delete_unwanted';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete feeds that should be deleted';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     * @throws \Exception
     */
    public function handle()
    {
        Feed::chunk(200, function ($allFeeds) {
            $feeds = $allFeeds->filter(function(/** @var Feed $feed */ $feed){
                if(!$feed->user){
                    // user was deleted
                    return true;
                } else if($feed->user->getPlan(true) === 'free'){
                    // user is on free plan
                    return true;
                }
                return false;
            });
            foreach ($feeds as $feed){
                $feed->delete(); // delete the feed to free our db space
            }
        });
    }
}
