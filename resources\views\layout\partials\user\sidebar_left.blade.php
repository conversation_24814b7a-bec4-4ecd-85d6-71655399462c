<div id="sidebar-menu" class="sidebar-sticky d-flex flex-column">

    <div class="pl-2" data-tour="id=sidebar_left_menu;title=Using SocialBu;text=You can use SocialBu to do a lot of things. This menu is a doorway to all of our features.;position=right">

        <ul class="nav flex-column main-menu">
            <li class="nav-item">
                <a class="nav-link{!! isActiveRoute('publish.', true) ? ' active ' : '' !!}" href="{{ route('publish.posts') }}">
                    <i class="ph ph-paper-plane-tilt ph-lg mr-2"></i>
                    Publish 
                    <span class="btn btn-sm pt-0 pb-0 float-right submenu-caret" data-target="#publish-submenu">
                        <i class="ph ph-caret-down ph-md text-secondary"></i>
                    </span>
                </a>
                <ul id="publish-submenu" class="submenu collapse @if(isActiveRoute('publish.', true))show @endif">
                    
                        <li class="nav-item border-left-1 border-secondary position-relative">
                            <a class="nav-link ml-5 px-4 py-3 {{ isActiveRoute('publish.posts') ? 'bg-lightest text-primary border-2 rounded' : '' }}" href="{{ route('publish.posts') }}">
                                Scheduled
                                <span style="display:none" class="small" data-var="posts.scheduled" data-prepend="(" data-append=")"></span>
                            </a>
                        </li>
                        <li class="nav-item border-left-1 border-secondary position-relative">
                            <a class="nav-link ml-5 px-4 py-3 {{ isActiveRoute('publish.drafts') ? 'bg-lightest text-primary border-2 rounded' : '' }}" href="{{ route('publish.drafts') }}">
                                Drafts
                                <span style="display:none"  class="small" data-var="posts.draft" data-prepend="(" data-append=")"></span>
                            </a>
                        </li>
                        @if(\Auth::user()->joinedTeams()->count() > 0)
                        <li class="nav-item border-left-1 border-secondary position-relative">
                                <a class="nav-link ml-5 pl-4 py-2 {{ isActiveRoute('publish.awaiting_approval') ? 'bg-lightest text-primary border-2 rounded' : '' }}" href="{{ route('publish.awaiting_approval') }}">
                                    Awaiting Approval
                                    <span style="display:none"  class="small" data-var="posts.awaiting_approval" data-prepend="(" data-append=")"></span>
                                </a>
                            </li>
                        @endif
                        <li class="nav-item border-left-1 border-secondary position-relative">
                            <a class="nav-link ml-5 px-4 py-3 {{ isActiveRoute('publish.calendar') ? 'bg-lightest text-primary border-2 rounded' : ' ' }}" href="{{ route('publish.calendar') }}">
                                Calendar
                            </a>
                        </li>
                        <li class="nav-item border-left-1 border-secondary position-relative">
                            <a class="nav-link ml-5 px-4 py-3 {{ isActiveRoute('publish.queues.', true) ? 'bg-lightest text-primary border-2 rounded' : ' ' }}" href="{{ route('publish.queues.index') }}">
                                Queues
                            </a>
                        </li>
                        <li class="nav-item border-left-1 border-secondary position-relative">
                            <a class="nav-link ml-5 px-4 py-3 {{ isActiveRoute('publish.bulk_upload') ? 'bg-lightest text-primary border-2 rounded' : ' ' }}" href="{{ route('publish.bulk_upload') }}">
                                Bulk Import
                            </a>
                        </li>
                        <li class="nav-item border-left-1 border-lightest position-relative">
                            <a class="nav-link ml-5 px-4 py-3 {{ isActiveRoute('publish.history') ? 'bg-lightest text-primary border-2 rounded' : ' ' }}" href="{{ route('publish.history') }}">
                                History
                            </a>
                        </li>
                    </ul>
            </li>
        </ul>
        
        <ul class="nav flex-column main-menu">
            <li class="nav-item">
                <a class="nav-link{!! isActiveRoute('generate.', true) ? ' active' : '' !!}" href="{{ route('generate.index') }}">
                    <i class="ph ph-sparkle ph-lg mr-2"></i>
                    Generate 
                    <span class="btn btn-sm pt-0 pb-0 float-right submenu-caret" data-target="#generate-submenu">
                    </span>
                </a>
            </li>
        </ul>

        <ul class="nav flex-column main-menu">
            <li class="nav-item">
                <a class="nav-link{!! isActiveRoute('curate.', true) ? ' active' : '' !!}" href="{{ route('curate.index') }}">
                    <i class="ph ph-list-magnifying-glass ph-lg mr-2"></i>
                    Curate
                </a>
            </li>
        </ul>

        <ul class="nav flex-column main-menu">
            <li class="nav-item">
                <a class="nav-link{!! isActiveRoute('feeds.', true) ? ' active' : '' !!}" href="{{ route('feeds.index') }}">
                    <i class="ph ph-chat-text ph-lg mr-2"></i>
                    @lang('generic.respond')
                </a>
            </li>
        </ul>

        <ul class="nav flex-column main-menu">
            <li class="nav-item">
                <a class="nav-link{!! isActiveRoute('analyze.', true) ? ' active pt-2 pb-2' : '' !!}" href="{{ route('analyze.content-performance') }}">
                    <i class="ph ph-chart-line ph-lg mr-2"></i> 
                    Analyze 
                    <span class="btn btn-sm pt-0 pb-0 float-right submenu-caret" data-target="#analyze-submenu">
                        <i class="ph ph-caret-down ph-md text-secondary"></i>
                    </span>
                </a>
                <ul id="analyze-submenu" class="submenu collapse @if(isActiveRoute('analyze.', true))show @endif">
                    <li class="nav-item border-left-1 border-secondary position-relative">
                        <a class="nav-link ml-5 px-4 py-3 {{ isActiveRoute('analyze.content-performance') ? 'bg-lightest text-primary border-2 rounded' : ' ' }}" href="{{ route('analyze.content-performance') }}">
                             Content
                        </a>
                    </li>
                    <li class="nav-item border-left-1 border-secondary position-relative">
                        <a class="nav-link ml-5 px-4 py-3 {{ isActiveRoute('analyze.account-performance') ? 'bg-lightest text-primary border-2 rounded' : ' ' }}" href="{{ route('analyze.account-performance') }}">
                             Account
                        </a>
                    </li>
                    <li class="nav-item border-left-1 border-secondary position-relative">
                        <a title="Instagram Captions" class="nav-link ml-5 px-4 py-3 {{ isActiveRoute('analyze.network-performance') ? 'bg-lightest text-primary border-2 rounded' : ' ' }}" href="{{ route('analyze.network-performance') }}">
                             Network
                        </a>
                    </li>
                    <li class="nav-item border-left-1 border-lightest position-relative">
                        <a class="nav-link ml-5 px-4 py-3 {{ isActiveRoute('analyze.team-performance') ? 'bg-lightest text-primary border-2 rounded' : ' ' }}" href="{{ route('analyze.team-performance') }}">
                             Team
                        </a>
                    </li>
                </ul>
            </li>
        </ul>

        <ul class="nav flex-column main-menu mb-5">
            <li class="nav-item">
                <a class="nav-link{!! isActiveRoute('automations.', true) ? ' active' : '' !!}" href="{{ route('automations.index') }}">
                    <i class="ph ph-repeat ph-lg mr-2"></i>
                    @lang('generic.automate')
                </a>
            </li>
        </ul>
    </div>

    @if (\Auth::user()->onboarding()->inProgress())
        <div  class="nav flex-column main-menu mt-auto p-0 border-top-1" id="checklistSection"
             data-tour="id=sidebar_left_menu_onboarding;title=Get Ready;text=Follow these steps to complete your account set up and get the most out of SocialBu;position=right">
            @php($steps = \Auth::user()->onboarding()->steps())
            @php($isVerified = \Auth::user()->verified)
            @php($timeZone = \Auth::user()->getTimezone(null))
            @php($hasAccounts = \Auth::user()->accounts()->exists())
            <div class="p-5 pb-0">
                <div class = "d-inline-flex">
                    <h6 class="font-weight-400">Complete your setup</h6>
                    @if( $isVerified && $timeZone && $hasAccounts)
                        <span id="removeChecklist" title="Remove" class="cursor-pointer pl-3 font-weight-400">
                            <span class="ph ph-x"> </span>
                        </span> 
                    @endif
                </div>
                @foreach($steps as $step)
                    <div class="pl-2 {{ $step->complete() ? ' disabled' : '' }}" href="{{ $step->link }}">
                        <a class="text-body" href="{{ $step->link }}" @if(!$step->complete()) title="{{ $step->title }}" data-toggle="tooltip" @endif>
                            @if(!$step->complete())
                                {{ $loop->iteration }}. {{ $step->cta }}
                            @else
                                <del>{{ $loop->iteration }}. {{ $step->cta }}</del>
                            @endif
                        </a>
                    </div>
                @endforeach
            </div>
        </div>
    @endif

    <div class="nav flex-column main-menu mt-auto chat_button mb-4">
        <div class="">
            <button type="button" class="ml-3 open_chat_bu_modal btn btn-outline-dark btn-lg d-flex align-items-center cursor-pointer position-relative">
                <div>
                    <i class="ph ph-fill ph-circle ph-sm text-success position-absolute chat-success-icon"></i>
                    <div class="d-flex align-items-center">
                        <i class="ph ph-chats-circle ph-lg mr-2"></i>
                        <p class="mb-0">
                            Bu AI
                        </p>
                    </div>
                </div>
                
            </button>
        </div>
    </div>

</div>
