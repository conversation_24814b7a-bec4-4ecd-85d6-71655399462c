<?php

namespace App\Http\Middleware;

use App\Helpers\EmailListHelper;
use App\User;
use Carbon\Carbon;
use Closure;
use Illuminate\Http\Request;

class UpdateUserData
{

    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param \Closure $next
     * @return mixed
     * @throws \Exception
     */
    public function handle(Request $request, Closure $next)
    {

        $user = user();

        if($user){

            $this->applyPromos($request, $user);

            $user->refresh(); // refresh user data because applyPromos() might have changed the user data

            $user->applyCouponIfNeeded();

            $user->refresh();

            $shouldSaveData = true;

            // check if we need to save data
            $oldData = $user->getOption('last_location');
            if($oldData){
                $cbTimestamp = Carbon::createFromTimestamp($oldData['timestamp']);
                if($cbTimestamp->diffInHours(now()) < 6){
                    // at-least wait for 6 hrs before saving the location data again
                    $shouldSaveData = false;
                }
            }

            //no need to update the data
            if(session()->has('support_login')){
                $shouldSaveData = false;
            }

            if($shouldSaveData){

                // set location data
                $user->setOption('last_location', [
                    'ip' => \Request::ip(),
                    'country' => \Request::header('cf-ipcountry'),
                    'user_agent' => \Request::header('user-agent'),
                    'timestamp' => time(),
                ]);

                // also update email list
                try {
                    EmailListHelper::getInstance()->syncUserContact($user);
                } catch (\Exception $exception){
                    report($exception);
                }

            }

        }

        return $next($request);
    }

    /**
     * @param Request $request
     * @param User $user
     */
    private function applyPromos(Request $request, User $user)
    {
        // apply promo if needed and remove cookie
        if($request->cookie('add_promo')){

            $promoCode = $request->cookie('add_promo');

            if($user->getOption('stripe_coupon') !== $promoCode){

                // add coupon for stripe
                $user->setOption('stripe_coupon', $promoCode);

                // notify our chat
                notify_chat('User (with promo code: ' . $promoCode . '): ' . $user->email);

            }

            cookie()->forget('add_promo');

        }
    }
}
