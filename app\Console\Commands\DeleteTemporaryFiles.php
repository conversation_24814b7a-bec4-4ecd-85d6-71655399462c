<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;

class DeleteTemporaryFiles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'delete:temporary_files';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete temporary files from temporary directory';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $rows = \DB::table('temporary_files')->where('updated_at', '<=', Carbon::now()->subDays(30))->limit(1000)->get();

        $count = count($rows);
        $this->info("Found $count temporary files to delete");

        foreach ($rows as $file) {
            if (\Storage::exists($file->path)) {
                \Storage::delete($file->path);
            }

            \DB::table('temporary_files')->where('id', $file->id)->delete();
        }
    }
}
