<template>
    <div class="row">
        <div class="col-12 mb-8">
            <h5 class="font-weight-500">Followers Growth</h5>
            <div class="card border" v-if="isLoading('account_metrics')" v-html="spinnerHtml"></div>
            <div class="row" v-else>
                <div class="col-6 col-md-6">
                    <div class="analyze-card card border">
                        <div class="card-body p-20">
                            <div>
                                <h6 class="font-weight-500 mb-6">New Followers</h6>
                                <h3 class="mb-0">{{ totalFollowers }}</h3>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 mt-4">
                    <div class="card border pt-4 d-flex justify-content-center">
                        <Chart
                            :options="growthChart"
                            ref="chart_growth"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 mb-8">
            <h5 class="font-weight-500">Profile Views</h5>
            <div class="card border" v-if="isLoading('account_metrics')" v-html="spinnerHtml"></div>
            <div class="row" v-else>
                <div class="col-6 col-md-6">
                    <div class="analyze-card card border">
                        <div class="card-body p-20">
                            <div>
                                <h6 class="font-weight-500 mb-6">New Profile Views</h6>
                                <h3 class="mb-0">{{ totalProfileViews }}</h3>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 mt-4">
                    <div class="card border pt-4 d-flex justify-content-center">
                        <Chart
                            :options="profileViewsChart"
                            ref="chart_profile_views"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 mb-8">
            <h5 class="mb-4">Reach vs. Impressions</h5>
            <div class="card border" v-if="isLoading('account_metrics')" v-html="spinnerHtml"></div>
            <div class="row" v-else>
                <div class="d-flex col-12 overflow-auto hide-scrollbar">
                    <div class="analyze-card card border mr-4">
                        <div class="card-body p-20">
                            <div>
                                <h6 class="font-weight-500 mb-6">Total Reach</h6>
                                <h3 class="mb-0">{{ totalReach }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="analyze-card card border">
                        <div class="card-body p-20">
                            <div>
                                <h6 class="font-weight-500 mb-6">Total Impressions</h6>
                                <h3 class="mb-0">{{ totalImpressions }}</h3>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 mt-4">
                    <div class="card border pt-4 d-flex justify-content-center">
                        <Chart
                            :options="reachVsImpressionsChart"
                            ref="chart_reach_impressions"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 mb-8">
            <h5 class="font-weight-500">Profile Actions</h5>
            <div class="card border" v-if="isLoading('account_metrics')" v-html="spinnerHtml"></div>
            <div class="row" v-else>
                <div class="d-flex col-12 overflow-auto hide-scrollbar">
                    <div class="analyze-card card border mr-4">
                        <div class="card-body p-20">
                            <div>
                                <h6 class="font-weight-500 mb-6">Total Email Clicks</h6>
                                <h3 class="mb-0">{{ totalEmailContacts }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="analyze-card card border mr-4">
                        <div class="card-body p-20">
                            <div>
                                <h6 class="font-weight-500 mb-6">Total Website Clicks</h6>
                                <h3 class="mb-0">{{ totalWebsiteClicks }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="analyze-card card border">
                        <div class="card-body p-20">
                            <div>
                                <h6 class="font-weight-500 mb-6">Total Phone Call Clicks</h6>
                                <h3 class="mb-0">{{ totalPhoneCallClicks }}</h3>
    
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 mt-4">
                    <div class="card border pt-4 d-flex justify-content-center">
                        <Chart
                            :options="actionsChart"
                            ref="chart_actions"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12">
            <h5 class="font-weight-500">Followers demographics</h5>
            <div class="row">
                <div class="col-md-12">
                    <div class="card border">
                        <div class="card-body card-body py-3 px-20">
                            <ul class="d-flex justify-content-md-center justify-content-start flex-nowrap nav border-md-bottom-0 border-bottom-1 navbar-reports overflow-auto hide-scrollbar" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link cursor-pointer"
                                       @click="followersDemographicsTab = 'country'"
                                       :class="{'active': followersDemographicsTab === 'country'}">
                                        Country
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link cursor-pointer"
                                       @click="followersDemographicsTab = 'gender_age'"
                                       :class="{'active': followersDemographicsTab === 'gender_age'}">
                                        Gender &amp; Age
                                    </a>
                                </li>
                            </ul>
                            <div class="tab-content pt-5 px-4 pb-4">
                                <div class="tab-pane active show">
                                    <template v-if="followersDemographicsTab === 'country'">
                                        <div
                                            v-if="isLoading('account_metrics')"
                                            v-html="spinnerHtml"></div>
                                        <Chart
                                            :options="countriesChart"
                                            ref="chart_countries"
                                        />
                                    </template>
                                    <template v-else-if="followersDemographicsTab === 'gender_age'">
                                        <div
                                            v-if="isLoading('account_metrics')"
                                            v-html="spinnerHtml"></div>
                                        <Chart
                                            :options="genderAgeChart"
                                            ref="chart_gender_age"
                                        />
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { axios, axiosErrorHandler, spinnerHtml } from "../../../../components";
import { Chart } from "highcharts-vue";

export default {
    name: 'InstagramPerformance',
    components: {
        Chart
    },
    props: ['filter', 'accounts', 'filterFormOpen'],
    data() {
        return {
            loadingFlags: [],

            accountMetrics: [],
            accountMetricsDaily: [],
            accountMetricsTotal: [],

            followersDemographicsTab: "country",
        }
    },
    computed: {
        spinnerHtml: () => spinnerHtml,

        totalFollowers() {
            let total = 0;
            this.accountMetrics.forEach((a) => {
                const {metrics} = a;
                (metrics.followers || []).forEach((d) => {
                    total += d.value;
                });
            });
            return total;
        },
        totalProfileViews() {
            let total = 0;
            this.accountMetricsDaily.forEach((a) => {
                const {metrics} = a;
                (metrics.profile_views || []).forEach((d) => {
                    total += d.value;
                });
            });
            return total;
        },
        totalReach() {
            let total = 0;
            this.accountMetricsDaily.forEach((a) => {
                const {metrics} = a;
                (metrics.reach || []).forEach((d) => {
                    total += d.value;
                });
            });
            return total;
        },
        totalImpressions() {
            let total = 0;
            this.accountMetricsDaily.forEach((a) => {
                const {metrics} = a;
                (metrics.impressions || []).forEach((d) => {
                    total += d.value;
                });
            });
            return total;
        },
        totalEmailContacts() {
            let total = 0;
            this.accountMetricsDaily.forEach((a) => {
                const {metrics} = a;
                (metrics.email_contacts || []).forEach((d) => {
                    total += d.value;
                });
            });
            return total;
        },
        totalWebsiteClicks() {
            let total = 0;
            this.accountMetricsDaily.forEach((a) => {
                const {metrics} = a;
                (metrics.website_clicks || []).forEach((d) => {
                    total += d.value;
                });
            });
            return total;
        },
        totalPhoneCallClicks() {
            let total = 0;
            this.accountMetricsDaily.forEach((a) => {
                const {metrics} = a;
                (metrics.phone_call_clicks || []).forEach((d) => {
                    total += d.value;
                });
            });
            return total;
        },

        // chart configs
        growthChart() {
            const seriesData = this.accountMetrics.map((item) => {
                const {metrics} = item;
                const acc = this.getAccount(item.account_id);
                return {
                    name: acc.name + " (" + acc._type + ")",
                    marker: {
                        enabled: false
                    },
                    data: (metrics.followers || []).map(a => [this.timestampForXAxis(a.date), a.value]),
                };
            });

            return {
                chart: {
                    type: "areaspline",
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: "",
                },
                xAxis: {
                    type: "datetime",
                },
                yAxis: {
                    title: {
                        text: "Followers",
                    },
                    labels: {
                        format: '{value}',
                    }
                },
                plotOptions: {
                    series: {
                        colorByPoint: true,
                        dataLabels: {
                            enabled: true
                        }
                    }
                },
                series: seriesData,
            };
        },
        profileViewsChart() {
            const seriesData = this.accountMetricsDaily.map((item) => {
                const {metrics} = item;
                const acc = this.getAccount(item.account_id);
                return {
                    name: acc.name + " (" + acc._type + ")",
                    marker: {
                        enabled: false
                    },
                    data: (metrics.profile_views || []).map(a => [this.timestampForXAxis(a.date), a.value]),
                };
            });

            return {
                chart: {
                    type: "areaspline",
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: "",
                },
                xAxis: {
                    type: "datetime",
                },
                yAxis: {
                    title: {
                        text: "Profile Views",
                    },
                    labels: {
                        format: '{value}',
                    }
                },
                plotOptions: {
                    series: {
                        colorByPoint: true,
                        dataLabels: {
                            enabled: true
                        }
                    }
                },
                series: seriesData,
            };
        },
        reachVsImpressionsChart() {
            const seriesData = ["reach", "impressions"].map((metric) => {
                const dataByDate = {};
                this.accountMetricsDaily.forEach((item) => {
                    const {metrics} = item;
                    (metrics[metric] || []).forEach((d) => {
                        if (!dataByDate[d.date]) {
                            dataByDate[d.date] = {
                                date: d.date,
                                value: 0,
                            };
                        }
                        dataByDate[d.date].value += d.value;
                    });
                });
                return {
                    name: metric,
                    marker: {
                        enabled: false
                    },
                    data: Object.values(dataByDate).map(a => [this.timestampForXAxis(a.date), a.value]),
                };
            });
            seriesData[1].yAxis = 1

            return {
                chart: {
                    type: "areaspline",
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: "",
                },
                xAxis: {
                    type: "datetime",
                },
                yAxis: [
                    { // Primary yAxis
                        title: {
                            text: "Reach",
                        },
                        labels: {
                            format: '{value}',
                        }
                    },
                    { //secondary yAxis
                        title: {
                            text: "Impressions",
                        },
                        labels: {
                            format: '{value}',
                        },
                        opposite: true
                    }
                ],
                plotOptions: {
                    series: {
                        colorByPoint: true,
                        dataLabels: {
                            enabled: true
                        }
                    }
                },
                series: seriesData,
            };
        },
        actionsChart() {
            const seriesData = ["email_contacts", "website_clicks", "phone_call_clicks"].map((metric) => {
                const dataByDate = {};
                this.accountMetricsDaily.forEach((item) => {
                    const {metrics} = item;
                    (metrics[metric] || []).forEach((d) => {
                        if (!dataByDate[d.date]) {
                            dataByDate[d.date] = {
                                date: d.date,
                                value: 0,
                            };
                        }
                        dataByDate[d.date].value += d.value;
                    });
                });
                return {
                    name: metric,
                    marker: {
                        enabled: false
                    },
                    data: Object.values(dataByDate).map(a => [this.timestampForXAxis(a.date), a.value]),
                };
            });

            return {
                chart: {
                    type: "areaspline",
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: "",
                },
                xAxis: {
                    type: "datetime",
                },
                yAxis: {
                    title: {
                        text: "Count",
                    },
                    labels: {
                        format: '{value}',
                    }
                },
                series: seriesData,
            };
        },
        countriesChart() {
            const valuesByGroup = {};
            this.accountMetrics.forEach((item) => {
                const {metrics} = item;
                Object.keys(metrics).filter(k => k.startsWith("followers_by_country/")).forEach((key) => {
                    const groupName = key.replace("followers_by_country/", "").toUpperCase();
                    // todo: const groupName = this.getCountryName(groupName);
                    if (!valuesByGroup[groupName]) {
                        valuesByGroup[groupName] = 0;
                    }

                    // we only need the data for the last day
                    valuesByGroup[groupName] = metrics[key][metrics[key].length - 1].value;
                });
            });

            // convert to array and sort by value
            const data = Object.keys(valuesByGroup).map((key) => {
                return {
                    group: key,
                    value: valuesByGroup[key]
                };
            }).sort((a, b) => b.value - a.value).slice(0, 10);

            return {
                chart: {
                    type: 'bar',
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: ''
                },
                xAxis: {
                    type: "category",
                    categories: data.map(d => d.group),
                    title: {
                        text: "Country"
                    }
                },
                legend: {
                    enabled: false
                },
                yAxis: {
                    min: 0,
                    title: {
                        text: 'Followers',
                    },
                    opposite: true,
                },
                plotOptions: {
                    series: {
                        colorByPoint: true,
                        dataLabels: {
                            enabled: true
                        }
                    }
                },
                credits: {
                    enabled: false
                },
                series: [{
                    name: '',
                    data: data.map(d => d.value),
                }]
            };
        },
        genderAgeChart() {
            const valuesByGroup = {};
            this.accountMetrics.forEach((item) => {
                const {metrics} = item;
                Object.keys(metrics).filter(k => k.startsWith("followers_by_gender_age/")).forEach((key) => {
                    const groupName = key.replace("followers_by_gender_age/", "").toUpperCase();
                    if (!valuesByGroup[groupName]) {
                        valuesByGroup[groupName] = 0;
                    }

                    // we only need the data for the last day
                    valuesByGroup[groupName] = metrics[key][metrics[key].length - 1].value;                });
            });

            // convert to array and sort by value
            const data = Object.keys(valuesByGroup).map((key) => {
                return {
                    group: key,
                    value: valuesByGroup[key]
                };
            }).sort((a, b) => b.value - a.value).slice(0, 10);

            return {
                chart: {
                    type: 'bar',
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: ''
                },
                xAxis: {
                    type: "category",
                    categories: data.map(d => d.group),
                    title: {
                        text: "Gender & Age"
                    }
                },
                legend: {
                    enabled: false
                },
                yAxis: {
                    min: 0,
                    title: {
                        text: 'Followers',
                    },
                    opposite: true,
                },
                plotOptions: {
                    series: {
                        colorByPoint: true,
                        dataLabels: {
                            enabled: true
                        }
                    }
                },
                credits: {
                    enabled: false
                },
                series: [{
                    name: '',
                    data: data.map(d => d.value),
                }]
            };
        },
    },
    async mounted() {
        await this.fetchData();
    },
    methods: {
        isLoading(flag) {
            return this.loadingFlags.includes(flag);
        },
        showLoader(flag) {
            if (this.isLoading(flag)) {
                return;
            }
            this.loadingFlags.push(flag);
        },
        hideLoader(flag) {
            this.loadingFlags = this.loadingFlags.filter(itm => itm !== flag);
        },
        fetchData() {
            this.fetchAccountMetrics();
            this.fetchDailyAccountMetrics();
            this.fetchTotalAccountMetrics();
        },
        async fetchDailyAccountMetrics() {
            this.showLoader('account_metrics_daily');
            try {
                const { data } = await axios.get("/api/v1/insights/accounts/metrics",
                    {
                        params: {
                            metrics: [
                                "profile_views",
                                "impressions",
                                "email_contacts",
                                "reach",
                                "website_clicks",
                                "phone_call_clicks",
                            ].join(","),
                            calculate_growth: false, // these are daily metrics by default in db
                            start: this.filter.start,
                            end: this.filter.end,
                            team: this.filter.team,
                            accounts: this.filter.accounts,
                            network: this.filter.network,
                        },
                    });
                this.accountMetricsDaily = data.data;
            } catch (e) {
                axiosErrorHandler(e);
            }
            this.hideLoader('account_metrics_daily');
        },
        async fetchAccountMetrics() {
            this.showLoader('account_metrics');
            try {
                const {data} = await axios.get("/api/v1/insights/accounts/metrics",
                    {
                        params: {
                            metrics: [
                                "followers",
                            ].join(","),
                            start: this.filter.start,
                            end: this.filter.end,
                            team: this.filter.team,
                            accounts: this.filter.accounts,
                            network: this.filter.network,
                        },
                    });
                this.accountMetrics = data.data;
            } catch (e) {
                axiosErrorHandler(e);
            }
            this.hideLoader('account_metrics');
        },
        async fetchTotalAccountMetrics() {
            this.showLoader('account_metrics_total');
            try {
                const { data } = await axios.get("/api/v1/insights/accounts/metrics",
                    {
                        params: {
                            metrics: [
                                "followers_by_country/*",
                                "followers_by_gender_age/*",
                            ].join(","),
                            start: this.filter.start,
                            end: this.filter.end,
                            team: this.filter.team,
                            accounts: this.filter.accounts,
                            network: this.filter.network,
                            calculate_growth: false,
                        },
                    });
                this.accountMetricsTotal = data.data;
            } catch (e) {
                axiosErrorHandler(e);
            }
            this.hideLoader('account_metrics_total');
        },
        getAccount(id) {
            const all = this.accounts;
            let account = {};
            all.forEach((acc) => {
                if (acc.id === id) account = acc;
            })
            return account;
        },
        timestampForXAxis(timestamp) {
            return this.$momentUTC(timestamp, "YYYY-MM-DD").valueOf();
        },
    },
}
</script>
