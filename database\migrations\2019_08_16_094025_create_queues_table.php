<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateQueuesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('publish_queues', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->increments('id');
            $table->string('name', 191)->index();

            $table->integer('user_id')->unsigned()->index(); // user who added this
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade')->onUpdate('cascade');

            // if attached to a team; destinations will be team accounts else private accounts only
            $table->integer('team_id')->nullable()->unsigned()->index();
            $table->foreign('team_id')->references('id')->on('teams')->onDelete('cascade')->onUpdate('cascade');

            $table->json('options')->nullable(); // will store data like destination accounts and so on

            $table->boolean('active')->default(true);

            $table->unsignedInteger('times_to_publish')->nullable(); // number of times each post in this queue should be published, null if unlimited

            $table->timestamp('next_publish_at')->nullable();
            $table->date('started_at')->nullable();
            $table->date('ended_at')->nullable();

            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('publish_queues');
    }
}
