<?php

namespace App\Jobs;

use App\Account;
use App\Respond\InboxManager;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Contracts\Redis\LimiterTimeoutException;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class FetchInboxData implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $account;

    /**
     * Delete the job if its models no longer exist.
     *
     * @var bool
     */
    public $deleteWhenMissingModels = true;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Account $account)
    {
        $this->account = $account;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws LimiterTimeoutException
     */
    public function handle()
    {
        \Redis::funnel('fetch_inbox::' . $this->account->id)
            ->releaseAfter(60 * 5) // 5 minutes
            ->limit(1) // only one job at a time for this account
            ->then(function () {

                $handler = new InboxManager($this->account);
                $handler->execute(); // will do polling and back-fill if needed

            }, function () {
                // Could not obtain lock...
                // do nothing: cron will pick it up next time
            });
    }
}
