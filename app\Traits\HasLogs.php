<?php

namespace App\Traits;


use App\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Spatie\Activitylog\Models\Activity;

trait HasLogs
{

    public function log($msg, $props = [], $user = null){
        $logger = activity("model")
            ->performedOn($this);

        if(!empty($props))
            $logger = $logger->withProperties($props);

        if($user)
            $logger = $logger->causedBy($user);

        $logger->log($msg);

        \Log::info(self::class . ': ' . $msg, $props);
    }

    /**
     * @return Builder
     */
    public function logs(){
        return Activity::inLog(['model', 'automations'])
            ->forSubject($this)
            ->orderBy('id', 'desc');
    }

    public function getLogs($limit = 15){
        return $this->transformLogs($this->logs()->limit(15)->get());
    }

    /**
     * @param Activity[]|Collection $logs
     * @return Collection
     */
    private function transformLogs($logs){

        return $logs->map(function (Activity $log){
            return $this->transformLog($log);
        });

    }

    /**
     * @param Activity $log
     * @return Collection
     */
    private function transformLog(Activity $log){

        /** @var Carbon $createdAt */
        $createdAt = $log->created_at;
        return collect([
            'message' => $this->buildMessage($log),
            'timestamp' => $createdAt->diffForHumans(),
            'timeago' => $createdAt->diffForHumans(),
            'user' => $log->causer ? User::transform($log->causer) : null,
            'props' => $log->properties->toArray(),
            'created_at' => $createdAt->toDateTimeString(),
        ]);

    }

    /**
     * @param Activity $log
     * @return string
     */
    private function buildMessage(Activity $log){
        $messages = [];
        if(method_exists($this, 'logMessages')) {
            $messages = $this->logMessages();
        }
        $finalMsg = isset($messages[$log->description]) ? $messages[$log->description] : $log->description;
        /** @var Collection $props */
        $props = $log->properties;
        return placeholders_replace($finalMsg, [
            'props' => $props->toArray(),
            'user' => $log->causer ? User::transform($log->causer) : null,
        ]);
    }

}
