export default {
    create(title, options = {}) {
        const config = {
            type: "right",
            fade: true,
            class: null,
            footerButtons: [
                /*
                {
                    type: "close",
                    class: "btn btn-secondary",
                    content: "Close"
                },
                {
                    class: "btn btn-primary",
                    content: "Save",
                    onClick() {}
                }*/
            ],
            modalConfig: { backdrop: "static", keyboard: false },
            onCreated(el) {}, // modal added in memory
            onAdded(el) {}, // modal added to DOM
            onShow(el) {}, // bootstrap modal event
            onShown(el) {}, // bootstrap modal event
            onHide(el) {}, // bootstrap modal event
            onHidden(el) {}, // bootstrap modal event
            onOpen(el) {},
            onClose(el) {},
            appendTo: "body",
            ...options
        };

        // validate
        const ALLOWED_TYPES = ["left", "right", "bottom", "left full", "right full"];
        if (!ALLOWED_TYPES.includes(config.type)) {
            throw new Error("Unknown type: " + config.type + ". Allowed types: " + ALLOWED_TYPES.join(", "));
        }

        const html = `
            <div class="modal${config.fade ? " fade": ""}" tabindex="-1" role="dialog" aria-hidden="true" 
                    aria-labelledby="_modal_${config.type}_label">
                <div 
                        class="modal-dialog modal-dialog-slideout modal-dialog-width custom-modal-width ${config.type} ${config.class ? config.class : ""}" 
                        role="document">
                    <div class="modal-content p-5">
                        <div class="modal-header p-0 mb-4">
                            <h5 class="modal-title" id="_modal_${config.type}_label">${title || ""}</h5>
                            <button type="button" class="close-button" data-close="true" aria-label="Close">
                                <i class="ph ph-x ph-md"></i>
                            </button>
                        </div>
                        <div class="modal-body p-0">
                            <div class="d-flex justify-content-center align-items-center text-muted">
                                <i class="ph ph-circle-notch ph-spin mr-2" aria-hidden="true"></i> Loading...
                            </div>
                        </div>
                        <div class="modal-footer">
                            ${(config.footerButtons || [])
                                .map((btn, i) => {
                                    return `<button type="button" data-index="${i}" class="${btn.class}"${
                                        btn.type === "close" ? ` data-close="true"` : ``
                                    }>${btn.content}</button>`;
                                })
                                .join("\n")}
                        </div>
                    </div>
                </div>
            </div>
        `;

        const $modal = $(html);

        // set up listeners for footer buttons
        if (config.footerButtons.length) {
            $modal.find(".modal-footer button").on("click", function(e) {
                const index = Number($(this).attr("data-index"));
                const btnConfig = config.footerButtons[index];

                if (btnConfig.type === "close") {
                    return; // special btn
                }

                btnConfig.onClick && btnConfig.onClick(e);
            });
        } else {
            $modal.find(".modal-footer").remove();
        }

        let isOpen = false;
        $modal
            .on("show.bs.modal", () => {
                config.onShow && config.onShow($modal[0], returnResult);
                config.onOpen && config.onOpen($modal[0], returnResult);
                isOpen = true;
            })
            .on("shown.bs.modal", () => {
                config.onShown && config.onShown($modal[0], returnResult);

                window.__recordUsageEvent("slideout_modal_shown", {
                    title
                }, false);
            })
            .on("hide.bs.modal", () => {
                config.onHide && config.onHide($modal[0], returnResult);
                config.onClose && config.onClose($modal[0], returnResult);
                isOpen = false;
            })
            .on("hidden.bs.modal", () => {
                config.onHidden && config.onHidden($modal[0], returnResult);

                window.__recordUsageEvent("slideout_modal_hidden", {
                    title
                }, false);
            });

        let isDestroyed = false;
        const returnResult = {
            el: $modal[0],
            isOpen: () => isOpen,
            open() {
                if (isDestroyed) {
                    throw new Error("Modal is destroyed");
                }
                return new Promise(res => {
                    const complete = () => {
                        res();
                    };
                    $modal.on("shown.bs.modal", complete).modal("show");
                    setTimeout(() => {
                        $modal.off("shown.bs.modal", complete);
                    }, 1000);
                });
            },
            close() {
                if (isDestroyed) {
                    throw new Error("Modal is destroyed");
                }
                return new Promise(res => {
                    const complete = () => {
                        res();
                    };
                    $modal.on("hidden.bs.modal", complete).modal("hide");
                    setTimeout(() => {
                        $modal.off("hidden.bs.modal", complete);
                    }, 1000);
                });
            },
            toggle() {
                if (isDestroyed) {
                    throw new Error("Modal is destroyed");
                }
                return isOpen ? this.close() : this.open();
            },
            destroy() {
                if (isDestroyed) {
                    throw new Error("Modal is already destroyed");
                }
                $modal.modal("dispose"); // dispose
                $modal.remove(); // remove from DOM
                isDestroyed = true;
                return true;
            },
            title(newTitle) {
                if (newTitle) {
                    $modal.find(".modal-title").text(newTitle);
                } else {
                    return $modal.find(".modal-title").text();
                }
            },
            set(obj){
                // update config
                for(let k in obj) {
                    if(!obj.hasOwnProperty(k)){
                        continue;
                    }
                    config[k] = obj[k];
                }
            }
        };

        // close btns
        $modal.find("[data-close]").on("click", () => {
            returnResult.close();
        });

        config.onCreated && config.onCreated($modal[0], returnResult);

        $modal.appendTo($(config.appendTo)); // is now in DOM

        config.onAdded && config.onAdded($modal[0], returnResult);

        // now open modal
        $modal.modal({
            ...(config.modalConfig || {}),
            show: false
        });

        return returnResult;
    }
};
