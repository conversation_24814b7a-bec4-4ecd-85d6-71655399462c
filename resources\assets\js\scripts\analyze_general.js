import $ from "jquery";

$(()=>{
    const $form = $("#filterForm");
    const handleFilterChange = function(){
        const filterName = $(this).attr("data-filter");
        let value = null;
        if($(this).attr("data-value")){
            value = $(this).attr("data-value");
        } else {
            value = $(this).val();
        }
        $form.find("[name='" + filterName + "']").val(value);
        $form.submit();
    };
    $("button[data-filter]").on("click", handleFilterChange);
    $("select[data-filter]").on("change", handleFilterChange);
    $("input[data-filter]").on("change", handleFilterChange);
});