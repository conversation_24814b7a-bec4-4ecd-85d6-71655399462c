<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateGeneratedContentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('generated_contents', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');
            $table->timestamps();

            $table->text('content');
            $table->string('keyword'); // focus keyword or topic if generated by AI
            $table->string('type')->default('tweet'); // tweet, article, etc. This is our internal classification

            $table->boolean('is_bad')->default(false); // if true, content is bad

            $table->integer('user_id')->unsigned()->index(); // user who added this
            $table->foreign('user_id')->references('id')->on('users');

            $table->integer('account_id')->unsigned()->index(); // the account for which the content was generated
            $table->foreign('account_id')->references('id')->on('accounts')->onDelete('cascade')->onUpdate('cascade');

            $table->integer('team_id')->unsigned()->nullable()->index(); // if it's for a team account
            $table->foreign('team_id')->references('id')->on('teams');

            $table->json('options')->nullable(); // will store any extra options
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('generated_contents');
    }
}
