<template>
    <div class="row">
        <div class="col-12" v-if="!loaded">
            <div class="text-center mt-4" v-html="overlayLoaderHtml"></div>
        </div>
        <template v-else>
            <div class="col-12 sticky-top d-flex bg-white fix-top flex-wrap mb-2">
                <div class="col-md-6 col-12 px-0">
                    <h3 class="mb-md-2 mb-3">{{ queue.name.length > 25 ? queue.name.substring(0,25).concat("..."): queue.name }}</h3>
                </div>
                <div class="col-md-6 col-12 d-flex justify-content-md-end justify-content-between align-items-center px-0 pb-md-0 pb-20">

                    <button class="btn mr-2 p-0" type="button">
                        <div class="spinner-border text-primary" role="status" v-if="saving || statusSaving">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <template v-else>
                            <i class="ph ph-toggle-right ph-fill ph-xl text-primary" title="Disable"
                               @click="toggleStatus" v-tooltip v-if="queue.active" key="toggleOff"></i>
                            <i class="ph ph-toggle-left ph-xl" title="Enable"
                               @click="toggleStatus" v-tooltip v-else key="toggleOn"></i>
                        </template>
                    </button>
                    <div class="bulk-actions bg-light shadow rounded mr-2" :style="selectedItems.length ? '' : ''" :class="{'sticky-top': selectedItems.length } ">
                        <div class="nav d-flex align-items-center px-2 pt-1 pb-0" v-if="selectedItems.length">
                            <template v-if="selectedItems.length">
                                <div class="nav-item">
                                    {{ selectedItems.length }} selected 
                                </div>
                                <div class="navbar-divider bg-dark align-self-center mx-2"></div>
                            </template>
                            <div class="nav-item d-flex">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" :checked="hasSelectedAllPosts" @change="onSelectAll" id="select-all-account">
                                    <label class="form-check-label" for="select-all-account">Select All</label>
                                </div>
                            </div>
                            <template v-if="selectedItems.length">
                                <div class="navbar-divider bg-dark align-self-center mx-2"></div>
                                <div class="dropdown show">
                                    <button title="Select actions for the selected posts" class="btn btn-sm btn-outline" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <i class="ph ph-caret-down"></i>
                                    </button>

                                    <div class="dropdown-menu dropdown-menu-right">
                                        <a class="dropdown-item" href="#" @click.prevent="deleteSelected">
                                            <i class="ph ph-trash ph-md"></i> Delete
                                        </a>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                    <div class="btn-group btn-group-xs">
                        <button class="btn btn-light btn-sm pl-3 py-2 pr-2" type="button" title="Configure" @click="openConfigureModal">Settings</button>
                        <button type="button"
                                class="btn btn-sm btn-light border-left px-1 py-2 border-radius-right"
                                data-toggle="dropdown" aria-haspopup="true"
                                aria-expanded="false">
                            <i class="ph ph-caret-down"></i>
                        </button>
                        <div class="dropdown-menu dropdown-menu-right" aria-labelledby="queueOptionsDropdown">
                            <a href="#" class="dropdown-item" @click.prevent="shuffleQueue">
                                <i class="ph ph-shuffle ph-lg mr-2"></i>
                                Shuffle Posts
                            </a>
                            <a href="#" class="dropdown-item" v-if="queue.need_cleanup" @click.prevent="cleanupQueue">
                                <i class="ph ph-recycle ph-md mr-1"></i>
                                Cleanup Queue
                            </a>
                            <a href="#" class="dropdown-item" @click.prevent="deleteQueue">
                                <i class="ph ph-trash ph-lg mr-2"></i>
                                Delete Queue 
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            

            <div class="col-12">

                <div class="list-group list-group-flush">

                    <div class="text-center p-5" v-html="overlayLoaderHtml" v-if="posts.loading"></div>

                    <draggable handle=".draggable-element" v-model="posts.items" @change="handleOrderChange" ghost-class="ghost" >
                        <div class="list-group-item border-0 post-container position-relative px-md-20 px-0 pt-0 pb-3"
                             v-for="(post, index) in posts.items" :key="index" :data-index="index">
                             <input class="position-absolute cursor-pointer post-select-input rounded" title="Select" type="checkbox" id="check" :class="{'d-block' : selectedItems.length}"
                                :checked="selectedItems.includes(post.id)" v-tooltip @change="onSelectPost(post.id)" v-if="posts.items && posts.items.length" />

                            <div class="card border rounded-xl" :class="{'border-primary': selectedItems.includes(post.id)}">
                                <div class="card-body py-4 px-md-4 px-2">

                                    <div class="d-flex flex-md-row flex-column">
                                        <div class="post-order-element d-md-block d-flex mr-4 w-40px pl-2 position-relative">
                                            <div class="input-group input-seamless pl-md-1" title="Position in queue" v-tooltip>
                                                <div class="input-group-prepend">
                                                    <span class="input-group-text p-0 font-weight-400">
                                                        #
                                                    </span>
                                                </div>
                                                <!-- input for post order -->
                                                <input type="text" pattern="[0-9]*" inputmode="numeric" class="form-control p-0 shadow-none border-0" :value="post.order" title="Position in queue" @change="updateOrder($event, post.id, post.order)" />
                                            </div>
                                            <div class="d-flex justify-content-center align-items-center">
                                                <button type="button" class="btn p-0 draggable-element" title="Re-order post" v-tooltip>
                                                    <i class="draggable-element ph ph-dots-six-vertical ph-bold ph-lg" aria-hidden="true"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="flex-grow">
                                            <div class="card border rounded-lg">
                                                <div class="card-body p-20">
                                                    <div class="d-flex justify-content-between">
                                                        <div class="flex-fill flex-fill w-100">
                                                            <div v-if="post.content && editPostId !== post.id">
                                                                <nl2br :text="post.content" tag="div"/>
                                                            </div>
                                                            <div v-if="editPostId === post.id">
                                                                <textarea ref="editedContent" class="form-control edit-post-textarea" title="Edit" v-text="post.content" v-focus></textarea>
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <template v-if="post.options && post.options.attachments && post.options.attachments.length">
                                                                <video class="cursor-pointer post-image border rounded-md" preload="metadata"  
                                                                    v-if="['mp4', 'm4v', 'mov', 'qt', 'avi'].includes(post.options.attachments[0].type)"
                                                                    v-lazy
                                                                    @click="openAttachments(post.options.attachments)">
                                                                    <source :src="post.options.attachments[0].url"/>
                                                                </video>
                                                                <img class="cursor-pointer post-image border rounded-md position-relative"
                                                                     :src="post.options.attachments[0].url"
                                                                     :alt="post.options.attachments[0].name"
                                                                     @click="openAttachments(post.options.attachments)"
                                                                     title="View" v-else/>
                                                                     
                                                                <small class="badge badge-light small-2" style="position: absolute;top: 23px;right: 22px;"
                                                                       v-if="post.options.attachments.length > 1">+{{ post.options.attachments.length - 1 }} more</small>
                                                            </template>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between flex-md-row flex-column mt-3 ml-md-6">
                                        <div class="d-flex pl-md-5 flex-md-row flex-column">
                                            <div class="small-2 mr-3">
                                                    <span class="badge badge-light"
                                                        v-if="queue.options.last_published_item && queue.options.last_published_item === post.id">
                                                        Published recently
                                                    </span>
                                                    <span class="badge badge-light" 
                                                        v-tooltip="$momentUTC(nextPublishTime).format('D MMM, Y h:mm a')" v-if="nextItemId && nextItemId === post.id && nextPublishTime && $momentUTC(nextPublishTime).isAfter()">
                                                        To be published next {{ nextPublishTime ? $momentUTC(nextPublishTime).fromNow() : '' }}
                                                    </span>
                                            </div>
                                            <div class="published-info pr-1 mr-1 my-md-0 my-2" v-if="post.times_published > 0">
                                                Published {{ post.times_published }} times
                                                <template v-if="post.last_published_at">
                                                    • Last published {{ $momentUTC(post.last_published_at).fromNow() }}
                                                </template>
                                            </div>
                                        </div>
                                        <div class="post-actions mt-md-0 mt-2">
                                            <div>
                                                <button class="btn btn-sm mb-2 btn-outline-secondary" @click="deletePost(post.id)"> Delete </button>
                                                <button class="btn btn-sm mb-2 btn-outline-secondary" @click="editPost(post.id)"> Edit </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </draggable>

                    <div class="card text-center border border-secondary" v-if="!posts.items.length">
                        <div class="card-body">
                            <h4 class="font-weight-400 h-100 d-flex justify-content-center align-items-center">No posts to show</h4>
                        </div>
                    </div>

                    <div class="mt-3">
                        <div class="list-group-item border-0 d-flex justify-content-between p-0 mt-1">
                            <button type="button" class="btn btn-light btn-sm"
                                    @click="openPostEditor">
                                <div class="d-flex align-items-center">
                                    <i class="ph-bold ph-plus mr-2"></i> Add Post
                                </div>
                            </button>
                            <div v-if="posts.lastPage && posts.lastPage > 1">
                                <!-- pagination -->
                                <ul class="pagination justify-content-center">
                                    <li class="page-item" :class="{active: posts.currentPage === n}" v-for="n in $pagination(posts.currentPage, posts.lastPage)">
                                        <a class="page-link" v-if="n==='...'">{{ n }}</a>
                                        <a class="page-link" href="#" @click.prevent="navigateToPage(n)" v-else>{{ n }}</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- add post modal -->
                    <div class="modal" id="addNewContentModal" ref="addNewContentModal" tabindex="-1" role="dialog" aria-labelledby="addNewContentLabel" aria-hidden="true">
                        <div class="modal-dialog modal-lg" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="addNewContentLabel">Add new post</h5>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                <div class="modal-body p-0">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Modal for attachment view -->
                    <div class="modal fade" id="attachmentModal" tabindex="-1" role="dialog"
                         aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Media Attachments</h5>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                        aria-hidden="true">&times;</span></button>
                                </div>
                                <div class="modal-body">
                                    <div class="row">
                                        <div class="col mx-auto">
                                            <div class="gallery gallery-4-type2">
                                                <div class="gallery-item" v-for="attachment in attachmentsToShow">
                                                    <video controls :title="attachment.name"  v-if="['mp4', 'm4v', 'mov', 'qt', 'avi'].includes(attachment.type)">
                                                        <source :src="attachment.url" />
                                                    </video>
                                                    <img :src="attachment.url" :alt="attachment.name" :title="attachment.name" v-else
                                                         class="border rounded"/>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-light" data-dismiss="modal">Close</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <!-- modal for config -->
             <div ref="queueConfigureModal" class="modal fade text-left config-modal" role="dialog">
                <div class="modal-dialog modal-dialog-slideout bottom modal-lg">
                    <div class="modal-content">
                        <div class="modal-header pb-5 px-md-6 mt-md-0 mt-md-2 px-4 pt-md-6 pt-4 d-flex align-items-center justify-content-between">
                            <input title="Queue name" type="text" class="input-seamless mr-2 px-0 form-control queue-name-input text-dark font-weight-600" placeholder="Queue name"
                                v-tour:queuevue_name="'This is the name of your queue. You can edit this if needed.'"
                                v-model="queue.name" />
                            <button type="button" class="close-button" @click="closeModal" data-dismiss="modal"><i class="ph ph-x ph-md"></i></button>
                        </div>
                        <div class="modal-body px-md-6 px-4">
                            <div class="row">
                                <div class="container w-75">

                                    <div class="pt-md-7 mb-5">
                                        Publish each post
                                        <input type="number" class="form-control d-inline w-92px pr-0 mx-2 mb-md-0 mb-2" required title="Leave blank for no limit"
                                            v-tour:queuevue_publish_times.bottom="'Each post can be published single or multiple times (whatever you specify). <br/><br/> If you want this queue to publish post unlimited times, do not enter any number.'"
                                            v-model="queue.times_to_publish" v-tooltip/>
                                        times to
                                        <div class="d-inline-block w-200 ml-md-2 mr-2 mb-md-0 mb-2"
                                            v-tour:queue_publish_accounts.bottom="'The posts that are added in this queue will be published to these accounts. You can set multiple accounts here if needed.'">
                                            <select class="form-control" title="Select" multiple required
                                                    v-model="queue.options.publish_to" v-selectpicker @change="$forceUpdate()">
                                                <template v-if="queue.team_id">
                                                    <option
                                                            v-if="queue.team_id === account.team_id" v-for="account in publishableAccounts" :value="account.id">
                                                        {{ account.name }} ({{ account._type }})
                                                    </option>
                                                </template>
                                                <template v-else>
                                                    <option
                                                            v-for="account in publishableAccounts" :value="account.id" v-if="!account.team_id">
                                                        {{ account.name }}  ({{ account._type }})
                                                    </option>
                                                </template>
                                            </select>
                                        </div>
                                        <span>
                                            Using the following schedule <abbr title="The timezone for this queue" v-tour:queuevue_timezone="'The timezone for the queue schedule comes from your SocialBu settings'" v-tooltip>({{ timezone }})</abbr>
                                        </span>
                                    </div>
                                    <div>
                                        <div v-for="(publish_info, index) in queue.options.publish_schedule">
                                            <div class="d-flex align-items-center mb-3">
                                                <div class="w-100">
                                                    <div class="d-inline-block w-117px mr-2 mb-md-0 mb-2" v-tour:queuevue_publish_freq="'You can set the posts to be published weekly or monthly. <br/><br/> Each option will allow you to set further options (example: weekdays, or month days).'">
                                                        <select class="form-control" title="Select" required
                                                                v-selectpicker v-model="publish_info.publish_every" @change="$forceUpdate()">
                                                            <option value="week">
                                                                Weekly
                                                            </option>
                                                            <option value="month" :selected="!publish_info.publish_every">
                                                                Monthly
                                                            </option>
                                                        </select>
                                                    </div>
                                                    <template
                                                            v-if="publish_info.publish_every === 'month'">
                                                        for
                                                    </template>
                                                    <a role="button" class="btn btn-outline-light p-3 d-md-none d-inline-block text-dark mr-4" @click="deleteSchedule(index)">
                                                        <i class="ph ph-trash ph-md"></i>
                                                    </a>
                                                    <div class="d-inline-block w-150 mr-1 mb-md-0 mb-2"
                                                            v-tour:qeueuvue_publish_months="'You can select multiple months if needed'"
                                                            v-if="publish_info.publish_every === 'month'">
                                                        <select class="form-control" title="Months" required multiple
                                                                v-model="publish_info.publish_months" v-selectpicker>
                                                            <option value="January">January</option>
                                                            <option value="February">February</option>
                                                            <option value="March">March</option>
                                                            <option value="April">April</option>
                                                            <option value="May">May</option>
                                                            <option value="June">June</option>
                                                            <option value="July">July</option>
                                                            <option value="August">August</option>
                                                            <option value="September">September</option>
                                                            <option value="October">October</option>
                                                            <option value="November">November</option>
                                                            <option value="December">December</option>
                                                        </select>
                                                    </div>
                                                    <template v-if="publish_info.publish_every">
                                                        on {{ publish_info.publish_every }} day
                                                        <div class="d-inline-block mx-md-2 mr-2 mb-md-0 mb-2"
                                                            :class="{'w-150' : publish_info.publish_every === 'week' }"
                                                                v-tour:queuevue_publish_days="'Select the days when the posts will get published. You can select multiple days if needed.'">
                                                            <select class="form-control" title="Select" multiple required
                                                                    v-selectpicker v-model="publish_info.publish_on" v-if="publish_info.publish_every === 'month'" @change="$forceUpdate()">
                                                                <option v-for="n in 31">
                                                                    {{ n }}
                                                                </option>
                                                            </select>
                                                            <select class="form-control" title="Select" multiple required
                                                                    v-selectpicker v-model="publish_info.publish_on" v-else-if="publish_info.publish_every === 'week'" @change="$forceUpdate()">
                                                                <option v-for="day in ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']">
                                                                    {{ day }}
                                                                </option>
                                                            </select>
                                                        </div>
                                                        at
                                                        <input title="Time" type="time" class="form-control d-inline w-140px pr-0 ml-1" required
                                                                v-tour:queuevue_publish_time="'Your posts will be published at this time'"
                                                                v-model="publish_info.publish_at"/>
                                                    </template>
                                                </div>
                                                <a role="button" class="btn btn-outline-light d-md-block d-none text-dark p-3 mr-4" @click="deleteSchedule(index)">
                                                    <i class="ph ph-trash ph-md"></i>
                                                </a>
                                            </div>
                                            
                                        </div>
                                        <div class="pt-3 mb-6 pb-2">
                                            <button class="btn btn-light btn-sm"
                                                    v-tour:queuevue_add_schedule="'You can add multiple schedules and your posts will get published based on these. <br/><br/> For example, if you add two entries, one for Monday, and one for Tuesday, it will publish the posts on those days.'"
                                                    @click="addSchedule()">
                                                <div class="d-flex align-items-center">
                                                    <i class="ph-bold ph-plus mr-2"></i> Add
                                                </div>
                                            </button>
                                        </div>
                                    </div> 
                                    <div class="row border-top pt-40">
                                        <div class="pt-5 pl-md-0 pl-4" v-tour:queuevue_start_n_end="'If you want this queue to work for a specific time, set a start and end date'">
                                            <abbr title="Leave blank to start now" v-tooltip>Start at</abbr>
                                            <div class="w-140px d-inline-block mx-2 mb-md-0 mb-3">
                                                <input class="form-control" title="Leave blank to start now" type="text" :value="queue.started_at"
                                                            ref="startDateInput" />
                                            </div>
                                            <div class="d-md-inline d-block">
                                                <span class="ml-md-0 ml-auto">and</span> <abbr title="Leave blank to keep publishing" v-tooltip>end at</abbr>
                                                <div class="w-140px d-inline-block mx-2">
                                                    <input class="form-control" title="Leave blank to keep publishing" type="text" :value="queue.ended_at"
                                                        ref="endDateInput" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="my-6">
                                <div class="row">
                                    <div class="col-12">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" :checked="disable_on_failure"  @click="disable_on_failure = !disable_on_failure">
                                            <label for="" class="form-check-label">Disable queue on post failure</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer d-flex justify-content-between">
                            <div></div>
                            <div>
                                <button type="button" class="btn btn-light btn-lg mr-md-4 mr-3" data-dismiss="modal" @click="closeModal" :disabled="saving">Cancel</button>
                                <button type="button" class="btn btn-primary btn-lg" @click="save" :disabled="saving">
                                    <div class="spinner-border text-primary" role="status" v-if="saving">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                    <span v-else>Save</span>
                                </button>
                            </div>
                        </div>
                     </div>
                </div>
             </div> 
        </template>
    </div>
</template>

<script>
    import $ from "jquery";
    import {axios, alertify, axiosErrorHandler, overlayLoaderHtml} from "../../components";
    import PostEditor from "./PostEditor.vue";
    import create_content from "../../scripts/create_content";
    import {getAccounts} from "../../data";
    import draggable from "vuedraggable";
    import {cloneDeep} from "lodash";

    export default {
        name: "PublishQueue",
        components: {
            PostEditor,
            draggable
        },
        data(){
            return {

                queue: {
                    id: null,
                    name: null,
                    user_id: null,
                    user_name: null,
                    options: null,
                    team_id: null,
                    active: null,
                    times_to_publish: null,
                    next_publish_at: null,
                    started_at: null,
                    ended_at: null,
                    created_at: null,
                    updated_at: null,
                    need_cleanup: false,
                },

                copyOfQueue : {}, // needed for resetting queue options when settings modal is closed without saving

                posts: {
                    items: [],
                    edited: [],
                    currentPage: null,
                    lastPage: null,
                    nextPage: null,
                    loading: true
                },

                attachmentsToShow: [],

                nextItemId: null,
                nextPublishTime: null,


                timezone: null,

                loaded: false, // initial load

                saving: false, // saving

                statusSaving: false,

                editPostId: null,

                disable_on_failure: false,

                selectedItems: [], // selected post ids for bulk actions
            }
        },
        asyncComputed: {
            async accounts() {
                let accounts;
                try {
                    accounts = await getAccounts();
                } catch (e) {
                    (() => {
                        if (e.response && e.response.data) {
                            const errors = e.response.data.errors;
                            if (errors) {
                                Object.keys(errors).forEach(k => {
                                    alertify.error(errors[k].join(" \n"));
                                });
                                return;
                            }
                            if (e.response.data && e.response.data.message) {
                                return alertify.error(e.response.data.message);
                            }
                        }
                        alertify.error(e.message || "An error occurred.");
                    })();
                    alert("Reload the page");
                    return [];
                }
                return accounts;
            },
        },
        computed: {
            publishableAccounts(){
                if(!this.accounts) return [];
                return this.accounts;
            },
            hasSelectedAllPosts() {
                return this.posts.items.length === this.selectedItems.length;
            },
            overlayLoaderHtml: () =>overlayLoaderHtml,
        },
        methods: {
            setQueue(queueDetails){

                Object.keys(queueDetails).forEach(k => {
                    this.queue[k] = queueDetails[k];
                });

                // update initial title
                document.title = document.title.replace('Queue', this.queue.name);

            },
            async initialize(queueDetails, timezone){
                this.copyOfQueue = cloneDeep(queueDetails);
                this.timezone = timezone;
                this.setQueue(queueDetails);

                if(!this.queue.options) {
                    this.queue.options = {};
                }

                if(!this.queue.options.publish_to) {
                    this.queue.options.publish_to = [];
                }
                if(this.queue.options.disable_on_failure) {
                    this.disable_on_failure = this.queue.options.disable_on_failure ;
                }

                // if there is no publish schedule, add one
                if(!this.queue.options.publish_schedule || this.queue.options.publish_schedule.length === 0){
                    this.addSchedule(true);
                }

                // load posts
                await this.navigateToPage(1);

                this.loaded = true;

                // set up start/end date input
                this.$nextTick(()=>{
                    let _stDate = this.$setupDateTimePicker(this.$refs.startDateInput, "YYYY-MM-DD", (val)=>{
                        this.queue.started_at = val;
                    });
                    let _enDate = this.$setupDateTimePicker(this.$refs.endDateInput, "YYYY-MM-DD",  (val)=>{
                        this.queue.ended_at = val;
                    });
                    if(!this.posts.items.length){
                        $(this.$refs.queueConfigureModal).modal('show');
                    }
                });

            },
            async toggleStatus() {
                if(this.queue.active === null) return;
                this.statusSaving = true;
                try {
                    const res = await axios.patch(
                        "/app/publish/queues/" + this.queue.id + "/status",
                        {
                            active: !this.queue.active
                        }
                    );
                    this.queue.active = !this.queue.active;
                } catch (e) {
                    axiosErrorHandler(e);
                }
                this.statusSaving = false;
            },
            async updateOrder(event, postId, oldOrder){

                const newOrder = event.target.value;

                // validate order: should be valid integer
                if(!newOrder || isNaN(newOrder) || newOrder < 1){
                    alertify.error("Order must be greater than 0");
                    event.target.value = oldOrder;
                    return;
                }

                if(this.saving) return;
                this.saving = true;
                try {
                    await axios.patch("/app/publish/queues/" + this.queue.id + "/change_order", {
                        itemsByOrder: [{
                            id: postId,
                            order: newOrder
                        }]
                    });

                    // refresh
                    await this.navigateToPage(this.posts.currentPage);
                } catch (e) {
                    axiosErrorHandler(e);
                }
                this.saving = false;
            },
            async handleOrderChange(event){
                if(this.saving) return;
                this.saving = true;

                const allOrders = this.posts.items.map(item => item.order).filter(o => o > 0);

                // get minimum order
                const minOrder = allOrders.length ? Math.min(...allOrders) : 0;

                let items = this.posts.items.map((item, index) => {
                    return {
                        id: item.id,
                        order: minOrder + index // set order to minimum order + index
                    }
                });

                try {
                    await axios.patch("/app/publish/queues/" + this.queue.id + "/change_order", {
                        itemsByOrder: items
                    });

                    this.editPostId = null;
                    await this.navigateToPage(this.posts.currentPage);

                } catch (e) {
                    axiosErrorHandler(e);
                    
                }

                this.saving = false;
                
            },
            async shuffleQueue(){
                if(this.saving) return;
                this.saving = true;
                try {
                    await axios.post("/app/publish/queues/" + this.queue.id + "/shuffle");
                    await this.navigateToPage(this.posts.currentPage);
                } catch (e) {
                    axiosErrorHandler(e);
                }
                this.saving = false;
            },
            async cleanupQueue(){
                if(this.saving) return;
                this.saving = true;
                try {
                    await axios.post("/app/publish/queues/" + this.queue.id + "/cleanup");
                    await this.navigateToPage(this.posts.currentPage);
                } catch (e) {
                    axiosErrorHandler(e);
                }
                this.saving = false;
            },
            closeModal(){
                this.queue = cloneDeep(this.copyOfQueue);           
            },
            openConfigureModal(){
                const $modal = $(this.$refs.queueConfigureModal);
                $modal
                    .on("shown.bs.modal", ()=>{
                        $modal.off("shown.bs.modal");
                        // refresh select picker so it updates its visible state
                        $(".bootstrap-select select").selectpicker("refresh");
                    })
                    .modal("show");
            },
            async save() {
                this.saving = true;

                // post to server
                try {
                    const res = await axios.patch("/app/publish/queues/" + this.queue.id, {
                        ...this.queue,
                        disable_on_failure: this.disable_on_failure,
                    });
                    this.nextItemId = res.data.nextItemId;
                    this.nextPublishTime = res.data.nextPublishTime;
                    $(this.$refs.queueConfigureModal).modal('hide');

                    alertify.success("Successfully saved.");
                } catch (e) {
                    axiosErrorHandler(e);
                }
                this.saving = false;

            },
            deleteQueue() {
                alertify.delete("Are you sure you want to delete this queue?", async () => {
                    try {
                        const res = await axios.delete("/app/publish/queues/" + this.queue.id);
                        document.location.href = "/app/publish/queues";
                    } catch (e) {
                        axiosErrorHandler(e);
                    }
                });
            },

            addSchedule(noRefresh){
                const _queue = this.queue;
                _queue.options.publish_schedule = _queue.options.publish_schedule || [];
                _queue.options.publish_schedule.push({
                    publish_every: "month",
                    publish_on: [],
                    publish_at: null,
                    publish_months: [],
                });
                this.queue = _queue;
                if(!noRefresh) {
                    this.$nextTick(() => {
                        this.$forceUpdate();
                    });
                }
            },
            deleteSchedule(index){
                const _queue = this.queue;
                if(!_queue || !_queue.options || !_queue.options.publish_schedule || _queue.options.publish_schedule.length < 2){
                    return;
                }
                _queue.options.publish_schedule.splice(index, 1);
                this.queue = _queue;
                this.$nextTick(()=>{
                    this.$forceUpdate();
                });
            },

            async navigateToPage(page, cb){
                if(!page) page = 1;
                this.posts.loading = true;
                try {
                    const res = await axios.get(
                        "/app/publish/queues/" + this.queue.id + "/posts?page=" + page
                    );

                    // posts
                    this.posts.items = res.data.items;
                    this.posts.currentPage = res.data.currentPage;
                    this.posts.lastPage = res.data.lastPage;
                    this.posts.nextPage = res.data.nextPage;

                    // set next item id and publish time
                    this.nextItemId = res.data.nextItemId;
                    this.nextPublishTime = res.data.nextPublishTime;

                    cb && cb();
                } catch (e) {
                    axiosErrorHandler(e);
                }
                this.posts.loading = false;
            },
            async openPostEditor(){
                await create_content.newQueuePost(this.queue, formData => {
                    this.addPost(formData);
                });
            },
            async addPost(formData){
                if(this.saving) return;
                this.saving = true;
                try {
                    await axios.post("/app/publish/queues/" + this.queue.id + "/posts", formData);
                    $(this.$refs.addNewContentModal).modal("hide");
                    await this.navigateToPage(this.posts.currentPage);
                } catch (e) {
                    axiosErrorHandler(e);
                }
                this.saving = false;
            },
            openAttachments(attachments) {
                let _this = this;
                this.attachmentsToShow = attachments;
                $("#attachmentModal")
                    .modal("show")
                    .on("hidden.bs.modal", function() {
                        _this.attachmentsToShow = [];
                        $(this).off("hidden.bs.modal");
                    });
            },
            async deletePost(id){
                if(this.saving) return;
                alertify.delete("Are you sure you want to delete this post?", async () => {                    
                    this.saving = true;
                    try {
                        await axios.delete("/app/publish/queues/" + this.queue.id + "/posts/" + id);
                        await this.navigateToPage(this.posts.currentPage);
                    } catch (e) {
                        axiosErrorHandler(e);
                    }
                    this.saving = false;
                });
            },
            async savePostChanges(id, formData){
                this.saving = true;
                try {
                    // Add _method field to spoof the PATCH request
                    formData.append('_method', 'PATCH');
                    
                    await axios.post("/app/publish/queues/" + this.queue.id + "/posts/" + id, formData);
                } catch (e) {
                    axiosErrorHandler(e);
                }
                await this.navigateToPage(this.posts.currentPage);
                this.saving = false;
            },
            async editPost(id){
                let post = this.posts.items.find(post => post.id === id);
                await create_content.editQueuePost(this.queue, post, formData => this.savePostChanges(id, formData));
            },
            onSelectAll() {
                if (!this.hasSelectedAllPosts) {
                    // select all
                    this.selectedItems = this.posts.items.map(post => post.id);
                } else {
                    // unselect all
                    this.selectedItems = [];
                }
            },
            onSelectPost(id) {
                const index = this.selectedItems.indexOf(id);
                if (index > -1) {
                    this.selectedItems.splice(index, 1);
                } else {
                    this.selectedItems.push(id);
                }
            },
            deleteSelected() {
                alertify.delete("All of the selected posts will be deleted. Are you sure?", async () => {
                    if(this.saving) return;
                    this.saving = true;
                    try { 
                        
                        await axios.post("/app/publish/queues/" + this.queue.id + "/bulk_delete",{
                            queue_id:this.queue.id,
                            post_ids:this.selectedItems
                        });

                        // delete posts from collection
                        this.posts.items = this.posts.items.filter(p => !this.selectedItems.includes(p.id));

                        this.selectedItems = []; // unselect all posts if needed
                    } catch (e) {
                        axiosErrorHandler(e);
                    } finally {
                        this.saving = false;
                    }
                    // if no post, refresh posts
                    if (!this.posts.items.length) {
                        await this.navigateToPage(this.posts.currentPage);
                    }
                });
            },
        }
    }
</script>

<style lang="scss" scoped>
.w-200px {
    width: 200px !important;
}
.draggable-element{
    color: #657085;
    cursor: grab !important;
}
.fix-top{
    top: 60px !important;
    z-index: 100 !important;
}

.post-select-input {
    width: 15px;
    height: 15px;
    top: 12px;
    z-index: 1;
    left: 12px;
    display: none;
    border: 1.4px solid #E3E6EB !important;

}
.post-select-input:checked{
    border: 1.4px solid #0557F0 !important;
}
.post-order-element{
    margin-top: -16px;
}
.post-actions{
    opacity: 0;
    visibility: hidden;
    transition: visibility 0s, opacity 0.2s linear;
}
.post-container{
    &:hover{
        .post-actions{
            opacity: 1;
            visibility: visible;            
        }
        .post-select-input{
            display: block !important;
        }
    }
}

.ghost {
    border: 1px solid #ffba00;
    transition: border 0.6s;
}
.bulk-actions{
    height: 46px;
}
.post-image{
    max-width: 100px !important;
    width: 100px;
    height: 100px;
    object-fit: cover;
}
.queue-name-input{
    font-size: 20px;

}
.edit-post-textarea {
    resize: none;
    border: none;
    box-shadow: none;
    min-height: 100px;
    padding: 0;
}
// .select-all{
//     width: 16px;
//     height: 16px;
//     margin-top: 2px;
// }
@media (max-width:767px){
    .post-order-element{
        width: 100% !important;
        margin-top: 0px !important;
    }
    .post-actions{
        visibility: visible !important;
        opacity: 1 !important;
    }
    .config-modal{
        .container{
            width: 100% !important;
        }
    }
}
.w-92px{
    width: 92px;
}
.w-117px{
    width: 117px;
}
.w-140px{
    width: 140px;
}
.badge{
    padding: 2px 6px;
    font-size: 14px !important;
}
.published-info{
    color: #657085;
}
</style>

