<template>
    <div class="card border-0">
        <div class="card-body p-0">

            <template v-if="loaded">
                <div class="list-group" v-if="type === 'posts'">
                    <div class="post-container card border border-secondary mb-4" v-for="post in posts" :key="post.id">
                        <post :post="post" />
                    </div>
                </div>
            </template>

            <div class="text-center" v-else>
                <div v-if="error">
                    Error loading posts. Please reload the page.
                </div>
                <div v-else>
                    <i class="ph ph-circle-notch ph-spin ph-lg text-muted"></i><span class="sr-only">Loading...</span>
                </div>
            </div>

        </div>
    </div>
</template>

<script>
    import Post from "./Post.vue";
    import { alertify } from "../../components";
    import {getAccounts} from "../../data";

    export default {
        name: "History",
        components: {
            Post,
        },
        data(){
            return {
                type: "",
                posts: [],
                error: false,
                loaded: false,
                accounts: [],
            }
        },
        methods: {
            async setPosts(type, p){
                this.type = type;
                const _posts = p;
                try {
                    this.accounts = await getAccounts();

                    _posts.forEach(post =>{
                        let acc = this.getAccount(post.account_id);
                        if (acc.id) {
                            post.accounts = [acc];
                        }
                    });

                    this.posts = _posts;

                    this.loaded = true;

                } catch (e) {
                    (() => {
                        if (e.response && e.response.data) {
                            const errors = e.response.data.errors;
                            if (errors) {
                                Object.keys(errors).forEach(k => {
                                    alertify.error(errors[k].join(" \n"));
                                });
                                return;
                            }
                            if (e.response.data && e.response.data.message) {
                                return alertify.error(e.response.data.message);
                            }
                        }
                        alertify.error(e.message || "An error occurred.");
                    })();
                    this.error = true;
                }
            },
            getAccount(id) {
                let all = this.accounts,
                    account = {};
                all.forEach(acc => {
                    if (acc.id === id) account = acc;
                });
                return account;
            },
        }
    }
</script>

<style lang="scss" scoped>

    @import "./../../../sass/variables";
    @import "./../../../thesaas/src/assets/plugin/thesaas/scss/variables";
    @import "~bootstrap/scss/_functions";
    @import "~bootstrap/scss/_variables";
    @import "~bootstrap/scss/mixins/_breakpoints";

    .card-columns {
        column-count: 1;
        @include media-breakpoint-up(lg) {
            column-count: 2;
        }
    }
</style>
