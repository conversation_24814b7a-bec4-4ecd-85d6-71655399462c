<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateCurationItemsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('curation_items', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');

            // the parent curation feed
            $table->unsignedBigInteger('feed_id')->index();
            $table->foreign('feed_id')->references('id')->on('curation_feeds')->onDelete('cascade')->onUpdate('cascade');

            $table->string('title', 191)->index()->nullable(); // title of the post
            $table->string('link', 255)->nullable(); // link to the original post
            $table->text('description')->nullable(); // description of the item
            $table->string('media', 255)->nullable(); // media url (image, video, etc)

            $table->timestamp('published_at')->nullable(); // date when the post was published

            $table->string('authors', 191)->index()->nullable(); // comma separated
            $table->string('tags', 191)->index()->nullable(); // comma separated tags/categories

            // unique guid for each item
            $table->string('guid', 191)->index();

            // for scoring
            $table->unsignedInteger('score')->default(0);
            $table->timestamp('scored_at')->nullable();

            $table->json('options')->nullable(); // for storing any key-value data

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('curation_items');
    }
}
