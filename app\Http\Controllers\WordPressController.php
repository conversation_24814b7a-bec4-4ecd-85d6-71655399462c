<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Cache;

class WordPressController extends Controller
{
    
    /**
     * Display a listing of glossary terms.
     *
     * @return \Illuminate\View\View
     */
    public function glossary(Request $request)
    {
        $perPage = $request->query('per_page', 100);
        
        // Cache results to improve performance
        $cacheKey = "wp_glossary_page_items";
        
        $data = Cache::remember($cacheKey, 3600, function () use ($perPage) {
            
            $items = [];
            $page = 1;

            //WP allows max 100 per page, so we need to loop through all pages
            while(true){
                try {
                    $client = guzzle_client();
                    
                    $response = $client->get("https://socialbu.com/blog/wp-json/wp/v2/glossary?page=" . $page . "&per_page=" . $perPage);
                    
                    $page++;

                    $response = json_decode($response->getBody(), true);

                    $items = array_merge($items, $response);
                    
                    if($response && count($response) < 100){
                        //if last response had less than 100, it means we've reached end
                        break;
                    }

                    
                } catch (\Exception $e) {
                    \Log::error('WordPress API Error: ' . $e->getMessage());
                    return [];
                }
            }

            $sortedItems = [];
            foreach($items as $item){
                $title = $item['title']['rendered'];
                $firstLetter = strtoupper($title[0]);
                if(!array_key_exists($firstLetter, $sortedItems)){
                    $sortedItems[$firstLetter] = [];
                }
                array_push($sortedItems[$firstLetter], $item);
            }
            //sort the array by key
            ksort($sortedItems);

            return $sortedItems;

        });
        
        return view('common.wordpress.glossary', [
            'data' => $data
        ]);
    }
    
    /**
     * Display a specific glossary term.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function glossaryItem($slug)
    {
        $cacheKey = "wp_glossary_item_{$slug}";
        
        $items = Cache::remember($cacheKey, 3600, function () use ($slug) {

            try {
                $client =  guzzle_client();

                $response = $client->get("https://socialbu.com/blog/wp-json/wp/v2/glossary?slug={$slug}");
                
                if ($response->getStatusCode() == 200) {
                    return json_decode($response->getBody(), true);
                }
                
                return [];
            } catch (\Exception $e) {
                \Log::error('WordPress API Error: ' . $e->getMessage());
                return [];
            }
        });
        
        if (count($items) < 1) {
            abort(404);
        }
        return view('common.wordpress.glossary-item', [
            'item' => $items[0],
        ]);
    }
    
}
