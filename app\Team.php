<?php

namespace App;

use App\Helpers\EmailListHelper;
use App\Notifications\TeamInvite;
use App\Traits\HasLogs;
use App\Traits\HasOptions;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * App\Team
 *
 * @property int $id
 * @property string $name
 * @property int $user_id
 * @property array|null $options
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Account[] $accounts
 * @property-read int|null $accounts_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\User[] $allMembersWithoutAdmin
 * @property-read int|null $all_members_without_admin_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\User[] $approvers
 * @property-read int|null $approvers_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\User[] $members
 * @property-read int|null $members_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\User[] $membersWithoutAdmin
 * @property-read int|null $members_without_admin_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\User[] $pendingMembers
 * @property-read int|null $pending_members_count
 * @property-read \App\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Team newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Team newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Team ofUser($id = null)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Team query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Team whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Team whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Team whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Team whereOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Team whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Team whereUserId($value)
 * @mixin \Eloquent
 * @property-read Collection|\App\User[] $allMembers
 * @property-read int|null $all_members_count
 */
class Team extends Model
{
    use HasOptions, HasLogs;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id', 'name'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'options' => 'array',
    ];


    /**
     * @param Team $team
     * @param bool $onlyTeam - exclude accounts when attaching team to account object and prevent endless loop
     * @return \Illuminate\Support\Collection
     */
    public static function transform(Team $team, $onlyTeam = false)
    {
        $data = collect([
            'id' => (int) $team->id,
            'name' => $team->name,
            'user' => $team->user ? User::transform($team->user) : 'User #' . $team->user_id,
            'created_at' => $team->created_at->toDateTimeString(),
            'members' => $team->allMembers()->withPivot(['approved',])->get()->map(function (User $user) use($team){
                $member = User::transform($user);
                $member->put('approved', !!$user->pivot->approved);
                $member->put('permissions', $team->getPermissionsForUser($user));
                return $member;
            }),
            'requires_content_approval' => $team->requiresContentApproval(),
        ]);
        if(!$onlyTeam){
            // Account::transform also includes team data, so to prevent recursion, we pass onlyTeam = true from Account::transform
            $data->put('accounts', $team->accounts->map(function(Account $a) use($team){
                return Account::transform($a, $team);
            }));
            $data->put('logs', $team->getLogs(15));
        }
        return $data;
    }

    /**
     * Scope a query to fetch resources of a specified user.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $id
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfUser($query, $id = null)
    {
        if(!$id)
            $id = \Auth::id();
        return $query->where('user_id', $id);
    }

    /**
     * Get the user that added the team.
     */
    public function user()
    {
        return $this->belongsTo('App\User');
    }

    /**
     * Get the associated accounts.
     */
    public function accounts()
    {
        return $this->belongsToMany(Account::class);
    }

    /**
     * Get the team members including admin. Also includes non-verified/pending-members
     * @return BelongsToMany
     */
    public function allMembers()
    {
        return $this->belongsToMany(User::class);
    }

    /**
     * Get the team members excluding admin. Also includes non-verified/pending-members
     * @return BelongsToMany
     */
    public function allMembersWithoutAdmin()
    {
        return $this->allMembers()->wherePivot('user_id', '<>', $this->user_id);
    }

    /**
     * Get the team members including the user who created the team.
     * @return BelongsToMany
     */
    public function members()
    {
        return $this->allMembers()->wherePivot('approved', true);
    }

    /**
     * Get the team members excluding the user who created the team.
     * @return BelongsToMany
     */
    public function membersWithoutAdmin()
    {
        return $this->members()->wherePivot('user_id', '<>', $this->user_id);
    }


    /**
     * Get the team members.
     * @return BelongsToMany
     */
    public function pendingMembers()
    {
        return $this->allMembers()->wherePivot('approved', false);
    }

    /**
     * @param bool $update
     * @return bool - to update
     */
    public function requiresContentApproval($update = null){
        if($update !== null){
            $this->setOption('requires_content_approval', (bool) $update);
            return (bool) $update;
        }
        if($this->getOption('requires_content_approval') === null && $this->approvers()->exists()){
            // handle old way: backwards compatible
            $this->setOption('requires_content_approval', true);
            return true;
        }
        return (bool) $this->getOption('requires_content_approval', false);
    }

    /**
     * Get the team members including the user who created the team.
     * @return BelongsToMany
     * @deprecated use `Team::getApprovers()`
     */
    public function approvers()
    {
        return $this->belongsToMany(User::class, 'approver_team');
    }


    /**
     * Get the team members who can approve content
     * @return User[]|Collection
     */
    public function getApprovers()
    {
        return $this->members->filter(function (User $member){
            return in_array('approvals.approve', $this->getPermissionsForUser($member));
        });
    }

    /**
     * Get the team members who can approve content
     * @return User[]|Collection
     */
    public function getAdmins()
    {
        return $this->members->filter(function (User $member){
            return in_array('team.edit', $this->getPermissionsForUser($member));
        });
    }

    public static $allPermissions = [
        'team.edit' => 'Edit/Delete Team',

        'posts.create' => 'Create posts',
        'posts.view' => 'View all posts', // see all scheduled/published posts instead of just mine
        'posts.edit' => 'Edit all posts',
        'posts.view_drafts' => 'View all draft posts',

        'approvals.approve' => 'Approve content',

        'queues.create' => 'Create custom queues',
        'queues.edit' => 'Edit custom queues',
        'queues.add_post' => 'Add content to queue',

        'generated_content.view' => 'View generated content',
        'generated_content.generate' => 'Generate content',

        'analyze.view' => 'See all insights',

        'feeds.create' => 'Create Feeds',
        'feeds.view' => 'View all Feeds',
        'feeds.edit' => 'Edit all feeds',

        'automation.create' => 'Create Automations',
        'automation.edit' => 'View/Edit all Automations',

        'inbox.manage' => 'Manage Inbox',
        'inbox.config' => 'Edit Inbox settings',

    ];

    /**
     * @param null $user
     * @return array
     */
    public function getPermissionsForUser($user = null){
        $userId = null;
        if($user instanceof User){
            $userId = $user->id;
        } else if($user > 0) {
            $userId = $user;
        } else if(user()) {
            $userId = user()->id; // current user
        }

        if(!$userId) {
            return [];
        }

        $member = $this->allMembers()->wherePivot('user_id', $userId)->withPivot(['permissions'])->first();
        if(!$member) {
            // not member
            return [];
        }
        $perms = (array) json_decode($member->pivot->permissions);
        if(empty($perms)){
            // not possible, we require atleast 1 perm
            // possible: perms not yet set after our perms update
            // so build default perms

            if($userId == $this->user_id){
                // admin has all perms
                $perms = array_keys(self::$allPermissions);
            } else {

                $perms = [
                    'posts.create', // everyone could create posts
                    'queues.edit', // team members could see/edit queue
                    'queues.add_post', // ...
                    'feeds.view', // team members could see the feed and interact
                ];

                // if someone is an approver, we had extra permissions
                if($this->approvers()->where('id', $userId)->exists()){
                    $perms = array_merge($perms, [
                        'posts.view',
                        'posts.edit',
                        'approvals.approve', // major perm
                    ]);
                }

            }

        }

        $allPerms = array_keys(self::$allPermissions);
        // we only return permissions which are available/active and defined
        return collect($perms)->filter(function($p) use($allPerms){
            return in_array($p, $allPerms);
        })->values()->toArray();
    }

    /**
     * @var array - cache of data in-memory
     */
    private static $permissionCache = [];

    /**
     * @param User $user
     * @param $permission - permission to check
     * @return bool
     */
    public function hasPermission(User $user, $permission){
        try {
            if(!isset(self::$allPermissions[$permission])){
                throw new \Exception('Invalid permission: ' . $permission);
            }
            if(isset(self::$permissionCache[$user->id])){
                // use cache
                $perms = self::$permissionCache[$user->id];
            } else {
                $perms = $this->getPermissionsForUser($user->id);
                // make sure cache array is max 100 items
                if(count(self::$permissionCache) > 100){
                    self::$permissionCache = [];
                }
                self::$permissionCache[$user->id] = $perms;
            }
            if(in_array($permission, $perms)){
                return true;
            }
        } catch (\Exception $exception){
            report($exception);
        }
        return false;
    }

    /**
     * @param User $user
     * @return bool
     */
    public function isAdmin(User $user){
        if(!$user) return false;
        try {
            return $this->hasPermission($user, 'team.edit'); // if user can edit team, then that means admin, because ideally, user can set their own perms
        } catch (\Exception $exception){
            return false;
        }
    }

    /**
     * Update members
     * @param array $members
     * @return Team
     */
    public function updateMembers($members){

        if(!is_array($members)) return $this;

        $addedMembersIds = $this->members()->get(['id'])->map(function($u){
            return $u->id;
        });

        $pendingMembersIds = $this->pendingMembers()->get(['id'])->map(function($u){
            return $u->id;
        });

        $oldMembers = $addedMembersIds->merge($pendingMembersIds)->toArray();


        $toAttach = [];
        $toDetach = [];
        $newMembersIds = [];
        $currentMembersIds = [];
        $memberIdToPermissions = [];
        foreach($members as $member){
            $currentMembersIds[] = $member['id'];
            if(!in_array($member['id'], $oldMembers)){
                $toAttach[$member['id']] = [
                    'permissions' => json_encode((array) $member['permissions']),
                ];
                $newMembersIds[] = $member['id'];
            }
            $memberIdToPermissions[$member['id']] = (array) $member['permissions'];
        }
        foreach ($oldMembers as $user_id){
            // check if the member should no longer in team
            if($user_id != $this->user_id && !in_array($user_id, $currentMembersIds)){
                // member to be removed from team
                $toDetach[] = $user_id;
            }
        }

        // now update members
        if(!empty($toAttach)) {
            $this->members()->attach($toAttach);
            // send notification
            /** @var User[] $users */
            $users = User::find($newMembersIds);
            foreach ($users as $user){
                $user->notify(new TeamInvite($this, user()));
                $this->log('added_user', [
                    'permissions' => $toAttach[$user->id]['permissions'],
                    'user' => User::transform($user),
                ], user());
            }
        }

        if(!empty($toDetach)) {
            $this->allMembersWithoutAdmin()->detach($toDetach);
            foreach ($toDetach as $id) {
                $this->log('removed_user', [
                    'user' => User::transform(User::find($id)),
                ], user());
            }
        }

        // update current
        foreach($members as $member){
            if(!isset($toAttach[$member['id']])){
                $mUser = User::find($member['id']);
                if($member['permissions'] != $this->getPermissionsForUser($mUser)){
                    $this->allMembers()->updateExistingPivot($member['id'], [
                        'permissions' => json_encode((array) $member['permissions']),
                    ]);
                    $this->log('updated_permissions', [
                        'permissions' => $member['permissions'],
                        'user' => User::transform($mUser),
                    ], user());
                }
            }
        }
        return $this;
    }

    /**
     * Update accounts
     * @param array $accounts
     * @param null $user
     * @return Team
     */
    public function updateAccounts($accounts, $user = null){

        // collection of all accounts that can be attached
        $accountsAllowedToAttach = collect();

        // already added accounts
        $accountsAllowedToAttach = $accountsAllowedToAttach->merge($this->accounts);

        if(!$user){
            $user = $this->user;
        }

        // user accounts
        $accountsAllowedToAttach = $accountsAllowedToAttach->merge($user->accounts);

        $oldAccounts = $this->accounts;

        // update accounts
        $accountIds = array_unique($accounts);
        $currAccounts = collect();
        foreach($accountIds as $accountId){
            $acc = $accountsAllowedToAttach->firstWhere('id', $accountId);
            if($acc){
                $currAccounts->push($acc);
            }
        }


        $accountsAdded = $currAccounts->filter(function ($a) use($oldAccounts){
            return $oldAccounts->where('id', $a->id)->count() === 0;
        });

        $accountsRemoved = $oldAccounts->filter(function ($a) use($currAccounts){
            return $currAccounts->where('id', $a->id)->count() === 0;
        });

        $this->accounts()->sync($currAccounts->map(function (Account $account){
            return $account->id;
        }));

        foreach ($accountsAdded as $account){
            $this->log('added_account', [
                'account' => Account::transform($account),
            ], user());
        }
        foreach ($accountsRemoved as $account){
            $this->log('removed_account', [
                'account' => Account::transform($account),
            ], user());
        }

        return $this;
    }

    /**
     * Approve a current user as member
     * @param bool $action
     * @return bool
     */
    public function approveInvite($action = true){
        // why will someone do this?
        if(\Auth::id() == $this->user_id) return false;

        if($action){
            if($this->pendingMembers()->wherePivot('user_id', \Auth::id())->count() === 1){
                $this->pendingMembers()->updateExistingPivot(\Auth::id(), [
                    'approved' => true,
                ]);
                $this->log('invite_accepted', [], user());

                if(!user()->getOption('team_joined', false)){
                    user()->setOption('team_joined', true);
                }

                try {
                    EmailListHelper::getInstance()->sendEvent(user(), 'team_joined', $this->name);
                    trigger_team_activity($this, user());
                } catch (\Exception $exception){
                    report($exception);
                }

                return true;
            }
        } else {
            if($this->pendingMembers()->wherePivot('user_id', \Auth::id())->count() === 1){
                $this->pendingMembers()->detach(\Auth::id());
                $this->log('invite_rejected', [], user());
                return true;
            }
        }

        return false;
    }

    private function logMessages(){
        return [
            'added_account' => '{{ user.name }} added account {{ props.account.name }}',
            'removed_account' => '{{ user.name }} removed account {{ props.account.name }}',
            'added_user' => '{{ user.name }} added {{ props.user.name }}',
            'removed_user' => '{{ user.name }} removed {{ props.user.name }}',
            'updated' => '{{ user.name }} updated the team',
            'created' => '{{ user.name }} created the team',
            'invite_accepted' => '{{ user.name }} joined the team',
            'invite_rejected' => '{{ user.name }} rejected the team invite',
            'updated_permissions' => '{{ user.name }} updated permissions for {{ props.user.name }}',
        ];
    }

    private function getOptions()
    {
        return (array)$this->options;
    }

    private function setOptions($options)
    {
        $this->options = $options;
        $this->save();
        return $this;
    }

}
