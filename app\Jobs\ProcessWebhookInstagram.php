<?php

namespace App\Jobs;

use App\Account;
use App\Automation;
use App\Feed;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Facebook\Facebook;
use Illuminate\Bus\Queueable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Illuminate\Http\File;

class ProcessWebhookInstagram implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $data = null; // webhook event object / data

    /**
     * Create a new job instance.
     *
     * @param array $data
     */
    public function __construct(array $data)
    {
        $this->data = $data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {   
        if (!isset($this->data['entry'])) {
            // something is wrong
            $e = new \Exception('`entry` not present in instagram webhook data: ' . json_encode($this->data));
            report($e);
            $this->fail($e);
            return;
        }

        $triggerEvent = function ($method_name, $update, $pageId){
            $method = camel_case($method_name);

            if (method_exists($this, $method)) {
                $this->{$method}($update, $pageId);
                return true;
            } else {

                // if it's a test event, ignore
                if(Str::contains(json_encode($this->data), ['random_mid', 'random_text', '"id":"0"'])){
                    return true;
                }


                // something is wrong
                $e = new \Exception('`' . $method . '` method not found. instagram webhook data: ' . json_encode($this->data));
                report($e);
                $this->fail($e);
                return false;
            }
        };

        foreach ($this->data['entry'] as $entry) {
            if(isset($entry['changes'])) {
                foreach ($entry['changes'] as $change) {
                    $ret = $triggerEvent($this->data['object'] . '_' . $change['field'], $change['value'], $entry['id']);
                    if(!$ret){
                        // unsuccessful, stop
                        return;
                    }
                }
            } else if(isset($entry['messaging'])){
                // for messenger webhooks
                foreach ($entry['messaging'] as $item){
                    $ret = $triggerEvent($this->data['object'] . '_messaging', $item, $entry['id']);
                    if(!$ret){
                        // unsuccessful, stop
                        return;
                    }
                }
            }
        }
    }

    private function instagramMessaging($item, $pageId){

        if(!isset($item['message'])){
            // no message, read event not handled right now
            return;
        }

        
        $accountsQuery = $this->getAccountsQuery($pageId);
        if(!$accountsQuery->exists()){
            // no account matched
            return;
        }

        $accounts = $accountsQuery->get();

        /** @var Account $account */
        $account = $accounts->first(function($account){
            return $account->getOption('via_instagram_login');
        });

        if(!$account){
            return;
        }
        
        // trigger automation if needed
        $automations = $this->getAutomations($pageId, 'instagram.incoming_message');
        $feeds = $this->getFeeds($account->account_id);
        
        if($automations->count() + $feeds->count() === 0 ){
            // no need to do anything
            return;
        }


        $access = json_decode($account->token);

        $accountUsername = $access->username;

        /** @var Facebook $fb */
        $fb = $account->getApi();

        // last timestamp
        $last_msg_timestamp = $account->getOption('last_msg_timestamp');
        if (!$last_msg_timestamp) $last_msg_timestamp = time() - (5 * 60);
        $new_last_msg_timestamp = null;

        if(isset($item['timestamp'])){
            // not sure if we really need this timestamp any more
            $new_last_msg_timestamp = $item['timestamp'];
        }

        $isSentByPage = $item['sender']['id'] === $pageId;

        if($isSentByPage){
            // no need to proceed
            return;
        }

        // we were using a collection; not needed any more but still untouched
        $newMessages = new Collection();

        $newMessages->push($item);

        $senderId = $item['sender']['id'];
        $message = $item['message'];

    
        try {
            $secondPartyDetails = $fb->get('/' . $senderId . '?fields=name,username,profile_pic,follower_count,is_user_follow_business,is_business_follow_user')->getDecodedBody(); //https://developers.facebook.com/docs/instagram-platform/instagram-api-with-instagram-login/messaging-api/user-profile
        } catch (\Exception $exception){
            \Log::info('error fetching second party details in instagram message webhook :: ' . $exception->getMessage());
            if(Str::contains($exception->getMessage(), ['nonexisting field (profile_pic)'])){ //probably its IG User and not Insta scoped used Id (IGSID)
                $secondPartyDetails = $fb->get('/' . $senderId . '?fields=username,name,profile_picture_url,followers_count,follows_count')->getDecodedBody(); 
            }else {
                $secondPartyDetails = [
                    'id' => $senderId,
                    'name' => 'A person',
                ];
            } 
            report($exception);
        }

        if(array_get($secondPartyDetails, 'username') == $accountUsername){
            return; //comment from same profile so no need to proceed
        } 

        // normalize
        $newMessages = $newMessages->map(function($msg) use($isSentByPage, $account, $secondPartyDetails) {
            $medias = collect(isset($msg['message']['attachments']) ? $msg['message']['attachments'] : [])->map(function ($itm) {
                $itm['_data'] = isset($itm['payload']) ? $itm['payload'] : []; // not sure why; scared to remove
                $itm['sticker_url'] = isset($itm['payload']['sticker_id']) ? $itm['payload']['sticker_id'] : null; // backwards compatibility
                return $itm;
            });
            $msg['media_url'] = $medias->first() && isset($medias->first()['_data']['url']) ? $medias->first()['_data']['url'] : null;

            // set data that we expect
            $msg['created_time'] = time();

            $firstPartyDetails = [
                'id' => $account->account_id,
                'name' => $account->name,
            ];

            $msg['from'] = $isSentByPage ? $firstPartyDetails : $secondPartyDetails;
            $msg['to'] = $isSentByPage ? $secondPartyDetails : $firstPartyDetails;
            $msg['thread_id'] = $secondPartyDetails['id']; // always the id of the other party

            $msg['id'] = $msg['message']['mid'];
            $msg['_message'] = $msg['message'];
            $msg['message'] = isset($msg['message']['text']) ? $msg['message']['text'] : '';
            $msg['sticker_url'] = null;

            return array_to_object($msg);
        });

        if($automations->count() > 0) {
            foreach ($automations as $automation) {
                // trigger automations
                $userMsgs = $newMessages->filter(function($msg) use($pageId) {
                    return $msg->from->id != $pageId;
                });

                foreach ($userMsgs as $message) {
                    $automation->execute([
                        'content' => $message->message,
                        'has_media' => !is_null($message->media_url),
                        'media_url' => $message->media_url,
                        'has_sticker' => !is_null($message->sticker_url),
                        'sticker_url' => $message->sticker_url,
                        'facebook' => [
                            'name' => $message->from->name,
                        ],
                    ], [
                        'network' => 'facebook',
                        'type' => 'conversation',
                        'id' => $message->from->id,
                        'from' => [
                            'id' => $message->from->id,
                            'name' => isset($message->from->name) ? $message->from->name : '',
                        ]
                    ]);
                }
            }
        };

        /** @var Feed[]|Collection $feeds */

        $feeds->each(function($feed) use($newMessages, $pageId) {
            
            /** @var Feed $feed */
            if(!in_array('messages', $feed->getItemsToCatch())){
                // skip
                return;
            }

            $messagesToSave = $newMessages->map(function($msg) use($feed){

                $msg->attachments = [];

                $client = new Client();

                if($msg->media_url){
                    $temp = tempnam(sys_get_temp_dir(), config('app.name'));
                    $tries = 0;
                    $done = false;
                    $e = null;
                    do {
                        $e = null;
                        try {
                            $client->get($msg->media_url, [
                                'sink' => $temp,
                            ]);
                            $file = new File($temp);
                            $path = \Storage::putFile('feeds/' . $feed->id, $file);
                            $msg->attachments[] = (object) [
                                'path' => $path,
                                'type' => $file->extension(),
                            ];
                            @unlink($temp);
                            $done = true;
                        } catch (\Exception $exception){
                            $e = $exception;
                            if(Str::contains(strtolower($exception->getMessage()), ['not supported'])){
                                // ignore
                                $e = null;
                                break;
                            }
                        }
                        ++$tries;
                    } while(!$done && $tries < 5);
                    if($e){
                        report($e);
                    }
                }

                return $msg;
            });
            $account = $feed->accounts()->where('accounts.account_id', $pageId)->first();
            $feed->addPosts($account, 'message', $messagesToSave->toArray());

        });
        
        // update last msg timestamp
        if($new_last_msg_timestamp) {
            $accountsQuery->chunk(200, function ($accounts) use ($new_last_msg_timestamp) {
                /** @var Account[]|Collection $accounts */
                // update last timestamp of account
                foreach ($accounts as $account) {
                    $account->setOption('last_msg_timestamp', $new_last_msg_timestamp);
                }
            });
        }
    }

    /**
     * @param $update
     * @param $id
     * @throws \Exception
     */
    private function instagramComments($update, $id){
        $accountsQuery = $this->getAccountsQuery($id);
        if(!$accountsQuery->exists()){
            // no account matched
            return;
        }

        // trigger automation if needed
        $automations = $this->getAutomations($id, 'instagram.new_comment');
        $feeds = $this->getFeeds($id);
        if($automations->count() + $feeds->count() === 0 ){
            // no need to do anything
            return;
        }

        /** @var Account $account */
        $account = $accountsQuery->first();

        $access = json_decode($account->token);

        $accountUsername = $access->username;

        /** @var Facebook $fb */
        $fb = $account->getApi();

        // fetch comment details
        try {
            $comment = $fb->get('/' . $update['id'] . '?fields=id,text,timestamp,username,from,media{id,caption,media_type,media_url,timestamp,permalink,shortcode,username}')->getDecodedBody();
        } catch (\Exception $exception){
            if(Str::contains($exception->getMessage(), ['does not exist', 'deleted comment',])){
                return;
            }
            throw $exception;
        }
        if(array_get($comment, 'username') == $accountUsername){
            return; //comment from same profile so no need to proceed
        }

        if(!isset($update['from'])){
            if(isset($comment['from'])){
                $update['from'] = $comment['from'];
            }else {
                $update['from'] = [
                    'id' => array_get($comment, 'username') == $accountUsername ? $id : null,
                    'username' => isset($comment['username']) ? $comment['username'] : 'instagram_user',
                ];
            }
        }

        if($automations->count() > 0) {
            foreach ($automations as $automation) {
                // trigger automation
                // trigger automation
                $automation->execute([
                    'media_id' => array_get($comment, 'media.id'),
                    'comment_id' => array_get($comment, 'id'),
                    'text' => array_get($comment, 'text'),
                    'content' => array_get($comment, 'text'),
                    'instagram' => [
                        'username' => array_get($comment, 'username'),
                    ],
                ], [
                    'network' => 'instagram_api',
                    'type' => 'comment',
                    'username' => array_get($comment, 'username'),
                    'media_id' => array_get($comment, 'media.id'),
                    'comment_id' => array_get($comment, 'id'),
                ]);
            }
        }

        if($feeds->count() > 0){

            $feedUpdate = array_to_object([
                'media_id' => array_get($comment, 'media.id'),
                'comment_id' => array_get($comment, 'id'),
                'text' => array_get($comment, 'text'),
                'user' => [
                    'username' => $update['from']['username'],
                    'pk' => $update['from']['id'],
                    'id' => $update['from']['id'],
                ],
                'from' => [
                    'id' => $update['from']['id'],
                    'pk' => $update['from']['id'],
                    'username' => $update['from']['username'],
                ],
                '_comment' => [
                    'id' => array_get($comment, 'id'),
                    'text' => array_get($comment, 'text'),
                    'timestamp' => array_get($comment, 'timestamp'),
                    'username' => $update['from']['username'],
                ],
                'timestamp' => Carbon::parse(array_get($comment, 'timestamp'))->getTimestamp(),
                'media' => array_merge(array_get($comment, 'media'), [
                    'user' => [
                        'username' => array_get((array) $comment, 'media.username'),
                        'pk' => array_get($comment, 'media.id'),
                        'id' => array_get($comment, 'media.id')
                    ]
                ]),
            ]);

            foreach($feeds as $feed) {
                if(!in_array('comments', $feed->getItemsToCatch())){
                    // skip if we are not supposed to catch comments
                    return;
                }

                $account = $feed->accounts()->where('accounts.account_id', $id)->first();
                $feed->addPost($account, 'comment', $feedUpdate);
            }
        }

    }

    /**
     * @param $update
     * @param $id
     * @throws \Exception
     */
    private function instagramMentions($update, $id){

        //insta mentions not working with Instagram Business Login
        return;
        
        $accountsQuery = $this->getAccountsQuery($id);
        if(!$accountsQuery->exists()){
            // no account matched
            return;
        }

        // trigger automation if needed
        $automations = $this->getAutomations($id, 'instagram.mention');
        $feeds = $this->getFeeds($id);

        if($automations->count() + $feeds->count() === 0 ){
            // no need to do anything
            return;
        }

        /** @var Account $account */
        $account = $accountsQuery->first();

        $access = json_decode($account->token);

        $accountUsername = $access->username;

        /** @var Facebook $fb */
        $fb = $account->getApi();

        $type = isset($update['comment_id']) ? 'comment' : 'media';
        try {
            if ($type === 'comment') {
                // mentioned in comment
                $data = $fb->get('/' . $id . '?fields=mentioned_comment.comment_id(' . $update['comment_id'] . '){id,text,timestamp,username,media{id,caption,media_type,media_url,timestamp,permalink,username}}')->getDecodedBody()['mentioned_comment'];
            } else {
                // mentioned in caption
                $data = $fb->get('/' . $id . '?fields=mentioned_media.media_id(' . $update['media_id'] . '){id,caption,media_type,media_url,timestamp,permalink,username}')->getDecodedBody()['mentioned_media'];
            }
        } catch (\Exception $exception){
            if(Str::contains($exception->getMessage(), ['not a valid', 'Unsupported', 'invalidated', ])){
                return;
            }
            throw $exception;
        }

        if($automations->count() > 0) {
            foreach ($automations as $automation) {
                // trigger automation
                if($type === 'comment'){
                    $automation->execute([
                        'media_id' => array_get($data, 'media.id'),
                        'comment_id' => array_get($data, 'id'),
                        'text' => array_get($data, 'text'),
                        'content' => array_get($data, 'text'),
                        'link' => array_get($data, 'media.permalink'),
                        'instagram' => [
                            'username' => array_get($data, 'username'),
                        ],
                    ], [
                        'network' => 'instagram_api',
                        'type' => 'comment',
                        'username' => array_get($data, 'username'),
                        'media_id' => array_get($data, 'media.id'),
                        'comment_id' => array_get($data, 'id'),
                    ]);
                } else {
                    $automation->execute([
                        'media_id' => array_get($data, 'id'),
                        'in_media' => true,
                        'text' => array_get($data, 'caption'),
                        'content' => array_get($data, 'caption'),
                        'link' => array_get($data, 'permalink'),
                        'instagram' => [
                            'username' => array_get($data, 'username'),
                        ],
                    ], [
                        'network' => 'instagram_api',
                        'type' => 'media',
                        'username' => array_get($data, 'username'),
                        'media_id' => array_get($data, 'id'),
                    ]);
                }
            }
        }

        if($feeds->count() > 0){

            if($type === 'comment'){
                $feedUpdate = array_to_object([
                    'mentioned' => true,
                    'media_id' => array_get($data, 'media.id'),
                    'comment_id' => array_get($data, 'id'),
                    'text' => array_get($data, 'text'),
                    'user' => [
                        'username' => array_get($data, 'username'),
                        'pk' => $accountUsername === array_get($data, 'username') ? $id : null,
                        'id' => $accountUsername === array_get($data, 'username') ? $id : null,
                    ],
                    'from' => [
                        'id' => $accountUsername === array_get($data, 'username') ? $id : null,
                        'pk' => $accountUsername === array_get($data, 'username') ? $id : null,
                        'username' => array_get($data, 'username'),
                    ],
                    '_comment' => [
                        'id' => array_get($data, 'id'),
                        'text' => array_get($data, 'text'),
                        'timestamp' => array_get($data, 'timestamp'),
                        'username' => array_get($data, 'username'),
                    ],
                    'timestamp' => Carbon::parse(array_get($data, 'timestamp'))->getTimestamp(),
                    'media' => array_merge(array_get($data, 'media'), [
                        'user' => [
                            'username' => array_get($data, 'media.username'),
                            'pk' => $accountUsername === array_get($data, 'media.username') ? $id : null,
                            'id' => $accountUsername === array_get($data, 'media.username') ? $id : null,
                        ]
                    ]),
                ]);
            } else {
                $feedUpdate = array_to_object([
                    'mentioned' => true,
                    'media_id' => array_get($data, 'id'),
                    'text' => array_get($data, 'text'),
                    'user' => [
                        'username' => array_get($data, 'username'),
                        'pk' => $accountUsername === array_get($data, 'username') ? $id : null,
                        'id' => $accountUsername === array_get($data, 'username') ? $id : null,
                    ],
                    'from' => [
                        'id' => $accountUsername === array_get($data, 'username') ? $id : null,
                        'pk' => $accountUsername === array_get($data, 'username') ? $id : null,
                        'username' => array_get($data, 'username'),
                    ],
                    'timestamp' => Carbon::parse(array_get($data, 'timestamp'))->getTimestamp(),
                    'media' => array_merge($data, [
                        'user' => [
                            'username' => array_get($data, 'username'),
                            'pk' => $accountUsername === array_get($data, 'username') ? $id : null,
                            'id' => $accountUsername === array_get($data, 'username') ? $id : null,
                        ]
                    ]),
                ]);
            }

            foreach($feeds as $feed) {
                if(!in_array('mentions', $feed->getItemsToCatch())){
                    // skip if we are not supposed to catch comments
                    return;
                }

                $account = $feed->accounts()->where('accounts.account_id', $id)->first();
                $feed->addPost($account, $type === 'comment' ? 'comment' : 'post', $feedUpdate);
            }
        }

    }

    private function instagramStoryInsights($update, $id){
        // not doing anything yet
    }

    private function getAccountsQuery($id){
        /** @var Builder $accountsQuery */
        return Account::where('account_id', $id)->where('type', 'instagram.api')->where('active', true)->latest('updated_at');
    }

    private function getFeeds($id){
        return Feed::whereHas('accounts', function (/** @var Builder $query */ $query) use($id) {
            $query->where('accounts.account_id', $id)->where('accounts.active', true);
        })->orWhereHas('team.accounts', function (/** @var Builder $query */ $query) use($id) {
            $query->where('accounts.account_id', $id)->where('accounts.active', true);
        })->get();
    }

    private function getAutomations($id, $event){
        return Automation::where('event', $event)
            ->where('active', true)
            ->whereIn('tag', ($this->getAccountsQuery($id)->get(['id']))->map(function ($account) {
                return 'instagram:' . $account->id;
            }))->get();
    }

}
