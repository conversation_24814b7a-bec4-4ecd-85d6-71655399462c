<template>
    <!-- Modal -->
    <div class="modal fade shadow" ref="modal" id="notificationsModal" tabindex="-1" role="dialog" aria-labelledby="notificationsBtn" aria-hidden="true">
        <div class="modal-dialog modal-dialog-slideout right custom-modal-width" role="document">
            <div class="modal-content">
                <div class="modal-header px-5 pt-5 pb-4">
                    <h5 class="modal-title">
                        Notifications
                        <template
                            v-if="unreadCount">
                                <span class="unread_count">
                               ({{ unreadCount }})
                            </span>
                            <span class="mark_all_read cursor-pointer" @click="markAllRead"><i class="ph ph-check-all"></i> Mark all as read </span>
                        </template>
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true"><i class="ph ph-x ph-md"></i></span>
                    </button>
                </div>
                <div class="modal-body px-3">
                    <div v-if="loading" v-html="spinnerHtml"></div>
                    <template v-else>
                        <div class="list-group">
                            <div class="list-group-item cursor-pointer border-none p-0 mb-2"
                                 @click="onClick($event, noti)"
                                 v-for="noti in items">
                                
                                <div class="px-3 pt-4 pb-0 rounded-md notification" :class="{'bg-lightest': noti.is_unread}">
                                    <div class="d-flex align-items-center justify-content-between mb-1">
                                        <h6 class="mb-0 font-weight-500">{{ noti.title }}</h6>
                                        <div class="d-flex align-items-baseline">
                                            <small class="small-2 mr-2" v-tooltip 
                                                :title="$momentUTC(noti.created_at).format('D MMM, Y h:mm a')">{{ $momentUTC(noti.created_at).fromNow() }}
                                            </small>
                                            <div>
                                                
                                                <span v-if="noti.is_unread" @click.stop="markAsRead(noti)" title="Mark as Read">
                                                    <i class="ph ph-circle ph-fill mr-1 text-danger" v-if="noti.level === 'danger'"></i>
                                                    <i class="ph ph-circle ph-fill text-warning mr-1" v-else-if="noti.level === 'warning'"></i>
                                                    <i class="ph ph-circle ph-fill text-primary mr-1" v-else></i>
                                                </span>
                                                <span class="small circle-icon" title="Mark as Unread"
                                                    v-tooltip
                                                    @click.stop="markAsUnread(noti)" v-else>
                                                    <i class="ph ph-circle ph-bold text-danger mr-1" v-if="noti.level === 'danger'"></i>
                                                    <i class="ph ph-circle ph-bold text-warning mr-1" v-else-if="noti.level === 'warning'"></i>
                                                    <i class="ph ph-circle ph-bold text-primary mr-1" v-else></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-between align-items-baseline">

                                        <p class="text-break">
                                            {{ noti.body }}
                                        </p>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
                <div class="modal-footer d-flex justify-content-between"
                     v-if="lastPage > 1">
                    <div>
                        <button class="btn btn-sm btn-outline-secondary"
                                v-show="currentPage > 1" @click.prevent="navigateToPage(currentPage - 1)">
                            &lsaquo; Previous page
                        </button> &nbsp;
                    </div>
                    <div> &nbsp;
                        <button class="btn btn-sm btn-outline-secondary"
                                v-show="nextPage" @click.prevent="navigateToPage(nextPage)">
                            Next page &rsaquo;
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <portal target-el="#notificationsIcon">
            <!-- append icon to the normal html header menu item -->
            <i class="ph ph-bell ph-lg"></i> <span v-if="unreadCount" class="badge badge-number badge-warning ">{{ unreadCount }}</span>
        </portal>
    </div>
</template>

<script>
import { axios, spinnerHtml } from "../../components";
import { extend } from "lodash";
import { backOff } from "exponential-backoff";

export default {
    name: "Notifications",
    data() {
        return {
            loading: false,
            unreadCount: 0,
            busyIds: [],
            updatingUnreadCount: false,

            items: [],
            total: null,
            nextPage: null,
            lastPage: null,
            currentPage: null
        };
    },
    computed: {
        spinnerHtml: () => spinnerHtml
    },
    methods: {
        async updateCount() {
            if(this.updatingUnreadCount) return;
            this.updatingUnreadCount = true;
            try {
                const { data } = await axios.get("/api/v1/notifications/unread");

                this.unreadCount = data.total;
            } catch (e) {}
            this.updatingUnreadCount = false;
        },
        async load(n = 1) {
            this.loading = true;
            try {
                const { data } = await axios.get("/api/v1/notifications?page=" + (n || 1));

                extend(this, data);
            } catch (e) {
                console.log(e);
            }

            this.updateCount();

            this.loading = false;
        },
        navigateToPage(n = 1) {
            window.__recordUsageEvent("notifications_navigate_page", {
                page: n
            });
            this.load(n);
        },
        async onClick(e, notification) {

            window.__recordUsageEvent("notifications_item_click");

            if(this.isBusy(notification)) return;

            if(notification.is_unread) {
                // mark as read
                await this.markAsRead(notification);
            }

            if (notification.url) {
                if (e.ctrlKey) {
                    window.open(notification.url, "_blank");
                } else {
                    document.location.href = notification.url;
                }
            }
        },
        async markAsRead(notification) {
            window.__recordUsageEvent("notifications_mark_read");
            if(this.isBusy(notification)) return;

            this.markBusy(notification, true);

            this.updateNotification(notification, noti => {
                const notification = Object.assign({}, noti);
                notification.read_at = (new Date()).toUTCString();
                notification.is_unread = false;
                return notification;
            });

            try {
                const { data } = await axios.post("/api/v1/notifications/" + notification.id + "/mark_read");

                this.updateNotification(notification, () => data);

                // update count on initial load
                this.updateCount();

            } catch (e) {
                this.updateNotification(notification, () => notification);

                alert(e.message);
            }

            this.markBusy(notification, false);

        },
        async markAsUnread(notification) {
            window.__recordUsageEvent("notifications_mark_unread");
            if(this.isBusy(notification)) return;

            this.markBusy(notification, true);

            this.updateNotification(notification, noti => {
                const notification = Object.assign({}, noti);
                notification.read_at = null;
                notification.is_unread = true;
                console.log(notification);
                return notification;
            });

            try {
                const { data } = await axios.post("/api/v1/notifications/" + notification.id + "/mark_unread");

                this.updateNotification(notification, () => data);

                // update count on initial load
                this.updateCount();

            } catch (e) {
                this.updateNotification(notification, () => notification);

                alert(e.message);
            }

            this.markBusy(notification, false);

        },
        async markAllRead() {
            window.__recordUsageEvent("notifications_mark_all_read");
            if(this.loading) return;

            this.loading = true;
            try {
                await axios.post("/api/v1/notifications/mark_all_read");

                // load and also update count
                this.load();
                this.updateCount();

            } catch (e) {
                alert(e.message);
                this.loading = false;
            }
        },
        updateNotification(notification, cb){
            const index = this.items.indexOf(notification);
            if(index > -1){
                const newObj = cb(notification);
                this.items.splice(index, 1, newObj);
            }
        },
        markBusy(notification, really){
            const id = notification.id;
            const index = this.busyIds.indexOf(id);
            const isBusy = index > -1;
            if(isBusy){
                if(really === undefined) really = false;
            } else {
                if(really === undefined) really = true;
            }
            if(really && !isBusy) {
                this.busyIds.push(id);
            } else if(!really && isBusy) {
                this.busyIds.splice(index, 1);
            }
        },
        isBusy(notification){
            return this.busyIds.indexOf(notification.id) > -1;
        },
        async setupPolling(){

            const getNewCount = async () => {
                // must get new count, else thrown an exception, this fn is used by the exponential backOff helper lib
                const oldVal = this.unreadCount;
                await this.updateCount();
                if(oldVal === this.unreadCount){
                    // no update
                    throw new Error('No new notification');
                }
            };

            // refresh notifications with exponential back off strategy
            try {
                await backOff(() => getNewCount(), {
                    delayFirstAttempt: true,
                    maxDelay: 5 * (1000 * 60), // 5 min
                    timeMultiple: 2,
                    startingDelay: 2000,
                });
                // process response
            } catch (e) {
                // do nothing
            }
            // we setup polling again
            this.setupPolling();
        }
    },
    async mounted() {
        const $modal = $(this.$refs.modal);
        $modal.on("show.bs.modal", () => {
            // load notifications when the popup opens
            this.load();
            window.__recordUsageEvent("notifications_open");
        });

        // update count on initial load
        await this.updateCount();

        this.setupPolling();
    }
};
</script>

<style lang="scss" scoped>
    .modal-header {
        .mark_all_read {
            color: #4A5465;
            font-size: 12px;
        }
    }

    .notification{
        &:hover{
            background-color: #F1F3F5;
            .circle-icon {
              visibility: visible;
          }
        }
    }
    .circle-icon{
        visibility: hidden;
    }
</style>
