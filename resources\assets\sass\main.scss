// core scss
@import "bootstrap";
//@import "./../thesaas/src/assets/plugin/thesaas/scss/vendor/et-line-icon";

#overlay {
    position: fixed; /* Sit on top of the page content */
    display: none; /* Hidden by default */
    width: 100%; /* Full width (cover the whole page) */
    height: 100%; /* Full height (cover the whole page) */
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5); /* Black background with opacity */
    z-index: 1048; /* Specify a stack order in case you're using a different order for other elements */
    cursor: pointer; /* Add a pointer on hover */
}

#navbar_main .nav-link,
.nav-link_main {
    // color: $color-text-secondary;
    // color: #050038;
    color: $color-text-darker !important;
    &:hover,
    &.active {
        // color: $color-text-dark;
        color: $socialbu-color !important;
    }
    &.border-top-hover {
        border-top: 2px solid transparent;
        &:hover {
            border-top-color: $socialbu-color;
        }
    }
    &.border-left-hover {
        border-left: 2px solid transparent;
        &:hover {
            border-left-color: $socialbu-color;
        }
    }
}
#navbar_main {
    z-index: 1049;
    .dropdown-item {
        opacity: 1 !important;
    }
    .dropdown-fullwidth {
        position: initial;
        .dropdown-menu {
            width: 100%;
            border-radius: 0;
            max-height: 70vh;
            overflow-y: auto;
            @include media-breakpoint-down(md) {
                max-height: unset;
            }
        }
        .card {
            border-top: 2px solid transparent;
            border-radius: 0;
            &:hover {
                background-color: $socialbu-bg-light-color;
                border-top: 2px solid $socialbu-color;
            }
            .card-title {
                color: $socialbu-color;
                // color: $color-text-darker;
                font-weight: 400;
                i {
                    // display:none
                }
            }
        }
    }

    @include media-breakpoint-down(md) {
        max-height: 98vh;
        overflow-y: auto;
        .nav-menu-mob {
            border: 1px solid $border-color;
        }
    }
    #menu_content > ul {
        position: initial;
    }
}

h2,
h4 {
    font-weight: 600 !important;
}
p {
    font-weight: 400 !important;
}
small {
    font-weight: 500 !important;
}
.header-main {
    // background-color: #f8f9fa;
    // background-image: linear-gradient(120deg, #f8f9fa 50%, #adc8fd80 80%);
    color: $color-text-darker;
    h1 {
        @include media-breakpoint-down(md) {
            font-size: 24px;
        }
    }
}
.section_overview {
    .nav-link {
        color: $color-text-darker;
        cursor: pointer;
        padding-top: 0;
        h5 {
            color: $color-text-darker;
            font-weight: 500;
        }
        p {
            display: none;
            // height: 60px !important;
        }
        &.active {
            background-color: $socialbu-bg-light-color;
            border-color: $socialbu-color;
            cursor: default;
            padding-top: 20px;
            margin-bottom: 20px;
            h5 {
                color: $socialbu-color;
            }
            p {
                display: block;
            }
            @include media-breakpoint-down(md) {
                p {
                    display: none;
                }
            }
        }
        @include media-breakpoint-down(md) {
            padding-bottom: 5px;
            padding-left: 5px;
            padding-right: 5px;
            &.active {
                padding-top: 5px;
                margin-bottom: 5px;
            }
            p {
                display: none;
            }
        }
    }
    .tab-pane {
        img {
            border-radius: 16px;
            overflow: hidden;
        }
    }
}
.socialbu-workswith {
    background-image: linear-gradient(15deg, #f8f9fa 50%, #adc8fd80 100%);
}
.socialbu-networks a {
    border-radius: 50%;
    font-size: 42px;
    height: 62px;
    width: 62px;
    padding: 10px;
    color: #fff !important;
    &.coming-soon:first-of-type {
        margin-left: 38px;
    }
    &.coming-soon:after {
        content: "Coming Soon";
        position: absolute;
        font-size: 12px;
        width: 42px;
        margin-top: 56px;
        line-height: 16px;
        margin-left: -35px;
        color: $text-muted;
    }
}

// different icon font so have to adjust
.social-google-business-profile {
    background-color: #4285f4;
    position: relative;
    bottom: 4px;
    i {
        position: relative;
        top: 4px;
    }
}
.social-mastodon{
    background-color: #595aff;
    position: relative;
    bottom: 4px;
    i {
        position: relative;
        top: 4px;
    }
}
.social-tiktok{
    background-color: black;
    position: relative;
    bottom: 4px;
    i {
        position: relative;
        top: -32px;
    }
}
.hover-zoom {
    transition: transform 0.2s; /* Animation */
    &:hover {
        transform: scale(1.2); /* (150% zoom - Note: if the zoom is too large, it will go outside of the viewport) */
    }
}

/** blog **/
.embedded_image {
    text-align: center;
    padding: 20px;
    img + p {
        text-align: center;
    }
}

.nav-menu-mob {
    background: #fff;
    padding: 20px;
    @include media-breakpoint-up(lg) {
        background: transparent;
        padding: 0;
    }
}

.socialbu-color {
    color: $socialbu-color !important;
}

.socialbu-bg-color {
    color: $socialbu-bg-color !important;
}
.sticky-navbar {
    top: 0;
    border-bottom: 1px solid #eeeeee;
    background-color: #fff;
    position: fixed;
    height: 90px;
    @include media-breakpoint-down(md) {
        height: auto;
    }
}

.socialbu-nav_item-border {
    border-bottom: 0.5px solid #e0e3e6 !important;
}
.display-4 {
    font-weight: 400;
}
.socialbu-images {
    margin-top: 0;
}
@media (max-width: 500px) {
    .socialbu-images {
        margin: 40px 0;
    }
}
.table {
    width: 100%;
    margin-bottom: 0 !important;
}
.sticked {
    position: sticky;
    top: 55px;
    margin-bottom: -1 !important;
}
.landing-page__card:hover {
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05) !important;
}
.billing-cycle {
    vertical-align: text-bottom;
}

.pricing-3 {
    .margin-right-n10 {
        margin-right: -10px;
    }
    .card-footer {
        margin: 0 -32px;
        a:hover {
            color: $color-primary !important;
        }
    }
}

.comparison-table {
    border: none;
    td,
    th {
        vertical-align: middle;
    }
    th:not(:first-of-type) {
        // background-color: $white;
        text-align: center;
    }
    td:not(:first-of-type) {
        // background-color: $white;
        text-align: center;
        //font-size: 1.25rem;
        font-size: 1.171875rem;
    }
    td:first-of-type {
        // background-color: $white;
        // font-size: 1.125rem;
        font-size: 1.171875rem;
        font-weight: 500;
    }
    td:not(.section-main):first-of-type {
        padding-left: 40px;
    }
    .section-main:not(.no-sub-items) {
        color: $socialbu-color;
        // font-size: 1.25rem;
        font-size: 1.171875rem;
        font-weight: 600;
    }
    i.fa-check,
    i.fa-times {
        font-size: 0.8rem;
    }
}
.free_tools_link_border > .nav-link.active{
    border-bottom: 2px solid transparent;
    border-bottom-color: $socialbu-color;

}
.border-bottom-hover {
    border-bottom:2px solid transparent;
    &:hover {
        border-bottom-color: $socialbu-color;
    }
}
.generate-content-card{
    background: #FAFBFF;
}
.generated-content{
    color: rgba(50, 61, 71, 0.60);
}
.content-square-logo{
    width: 48px;
    height:48px
}
.content-input{
    height: 60px;
}
.content-button{
    right:36px;
    top:21px;
    height:50px;
    width:23.5%;
    background:#50A1FF;
    i{
        font-size: larger;
    }
}
.content-display-card{
    width:85%;
    .action-container {
        bottom:20px;
        right:20px;
    }
    .icon {
        font-size:large;
    }
}
.content-suggestion{
    width: 92.5%;
    .content-suggestion-card{
        width: 89%;
        height:60px;
    }
}

.content-box{
    width: 46px; 
    height:46px;
    background:#323D47;
    border-radius: 100%;
    border: 1px solid #fff;
    border-width: 0;
    div{
        top: 8px;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 20px;
        line-height: 1.35;
        color: #fff;
    }
}
.more_feature_heading{
    font-size: 1.3rem !important;
}
