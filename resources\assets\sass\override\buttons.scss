.btn {
    border-radius: 8px;
    text-transform: none;
    line-height: 1.5;
    letter-spacing: 1px;
    padding: 10px 18px;
    font-size: 1rem;
    &:disabled{
        opacity: 0.6;
    }
    &:focus{
        box-shadow: none;
    }
}
.btn-primary{
    &:hover{
        background-color: #0B46C2;
    }
    &:active,
    &.active{
        background-color: #103F98;
    }
    &:disabled,
    &.disabled{
        background-color: #0557F0;
    }
}
.btn-secondary{
    color: $color-text-dark;
    background-color: #D5E1F6;
    &:hover{
        background-color: #B8CBEF;
    }
    &:active,
    &.active{
        background-color: #93B0E7;
    }
    &:disabled,
    &.disabled{
        background-color: #D5E1F6;
    }
}
.btn-light{
    background: rgba(16, 63, 152, 0.06);
    color: $gray-700;
    &:hover{
        background: rgba(16, 63, 152, 0.08);
    }
    &:active,
    &.active{
        background: rgba(16, 63, 152, 0.06);
    }
    &:disabled,
    &.disabled{
        background-color: $gray-100;
        border-color: $gray-100;
    }
}

.btn-white{
    color: #0B46C2;

    background-color: white;
    border-color: white;
    &:hover{
        background-color: white;
        border-color: white;
        color: #0B46C2;
    }
    &:active,
    &.active{
        background-color: white;
        border-color: white;
        color: #0B46C2;
    }
    &:disabled,
    &.disabled{
        background-color: white;
        border-color: white;
    }
}

.btn-orange{
    color: white;
    background-color: #ff8267;
    border-color: #ff8267;
    &:hover{
        color: white;
        background-color: #ff8267;
        border-color: #ff8267;
    }
    &:active,
    &.active{
        color: white;
        background-color: #ff8267;
        border-color: #ff8267;
    }
    &:disabled,
    &.disabled{
        background-color: #dc8c7b;
        border-color: #dc8c7b;
    }
}

// ----------------------------------------------------/
// Sizes
// ----------------------------------------------------/
// button-size($padding-top, $padding-bottom, $padding-x, $font-size)
.btn-xs { @include button-size(8px, 8px, 14px, 14px);}
.btn-sm { @include button-size(8px, 8px, 16px, 16px);}
.btn-lg { @include button-size(12px, 12px, 20px, 16px);}
.btn-xl { @include button-size(14px, 14px, 28px, 20px);}
// Outline
//
.btn-outline-primary{
    &:focus,
    &.focus, 
    &:hover {
        box-shadow: none;
    }
    &:not([disabled]):not(.disabled):active,
    &:not([disabled]):not(.disabled).active,
    .show > &.dropdown-toggle {
        color: $white;
        background-color: $color-primary;
        border-color: $color-primary;
        box-shadow: none;
    }
}
.btn-outline-secondary{
    color: $color-text-dark;
}
.btn-outline-success{
    &:focus,
    &.focus, 
    &:hover {
        box-shadow: none;
    }
    &:not([disabled]):not(.disabled):active,
    &:not([disabled]):not(.disabled).active,
    .show > &.dropdown-toggle {
        color: $color-success;
        background-color: #B8F6AB;
        border-color: $color-success;
        box-shadow: none;
    }
}
.btn-outline-danger{
    &:focus,
    &.focus, 
    &:hover {
        box-shadow: none;
    }
    &:not([disabled]):not(.disabled):active,
    &:not([disabled]):not(.disabled).active,
    .show > &.dropdown-toggle {
        color: $color-danger;
        background-color: #FEC0AB;
        border-color: $color-danger;
        box-shadow: none;
    }
}
.btn-outline-warning{
    &:focus,
    &.focus, 
    &:hover {
        box-shadow: none;
    }
    &:not([disabled]):not(.disabled):active,
    &:not([disabled]):not(.disabled).active,
    .show > &.dropdown-toggle {
        color: $color-warning;
        background-color: #FEE49A;
        border-color: $color-warning;
        box-shadow: none;
    }
}
.btn-outline-info{
    &:focus,
    &.focus, 
    &:hover {
        box-shadow: none;
    }
    &:not([disabled]):not(.disabled):active,
    &:not([disabled]):not(.disabled).active,
    .show > &.dropdown-toggle {
        color: $color-info;
        background-color: #99DFFE;
        border-color: $color-info;
        box-shadow: none;
    }
}
.btn-outline-light{
    color: $gray-700;
    border: 1px solid #E4E7ED;
    &:focus,
    &.focus, 
    &:hover {
        color: $gray-700;
        background-color: rgba(16, 63, 152, 0.06);
        border-color: #E4E7ED;
        box-shadow: none;
    }
    &:not([disabled]):not(.disabled):active,
    &:not([disabled]):not(.disabled).active,
    .show > &.dropdown-toggle {
        color: $gray-700;
        background-color: $gray-100;
        border-color: $gray-200;
        box-shadow: none;
    }
}
.btn-outline-dark{
    &:focus,
    &.focus, 
    &:hover {
        box-shadow: none;
    }
    &.active {
        background-color: black;
        color: white;
    }
    &:not([disabled]):not(.disabled):active,
    &:not([disabled]):not(.disabled).active,
    .show > &.dropdown-toggle {
        color: $color-dark;
        background-color: $gray-200;
        border-color: $color-dark;
        box-shadow: none;
    }
}
