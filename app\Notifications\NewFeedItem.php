<?php

namespace App\Notifications;

use App\Feed;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class NewFeedItem extends Notification
{
    use Queueable;

    /** @var Feed */
    private $feed;

    /** @var int */
    private $numberOfUpdates;

    /**
     * Create a new notification instance.
     *
     * @param Feed $feed
     * @param $numberOfUpdates
     */
    public function __construct(Feed $feed, $numberOfUpdates)
    {
        $this->feed = $feed;
        $this->numberOfUpdates = $numberOfUpdates;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->greeting('Hey ' . explode(' ', ($notifiable->name . ''))[0])
            ->subject($this->feed->getName() . ' has ' . $this->numberOfUpdates . ' unread items')
            ->line('Your feed ' . e($this->feed->getName()) . ' has ' . $this->numberOfUpdates . ' new items.')
            ->line('')
            ->action('See Feed', url("app/respond/feeds/{$this->feed->id}"));
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'feed_id' => $this->feed->id,
            'feed_name' => $this->feed->getName(),
            'new_updates' => $this->numberOfUpdates,
        ];
    }
}
