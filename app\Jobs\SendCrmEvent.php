<?php

namespace App\Jobs;

use App\Helpers\EmailListHelper;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class SendCrmEvent implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $user;
    protected $event;
    protected $value;

    /**
     * Create a new job instance.
     *
     * @param User $user
     * @param null $event
     * @param null $val
     */
    public function __construct(User $user, $event = null, $val = null)
    {
        $this->user = $user;
        $this->event = $event;
        $this->value = $val;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Exception
     */
    public function handle()
    {
        try {
            EmailListHelper::getInstance()->sendEvent($this->user, $this->event, $this->value, true);
        } catch (\Exception $exception){

            $errCount = $this->user->getOption('crm_event_error', 0);
            if($errCount < 5) {
                ++$errCount;
                $this->user->setOption('crm_event_error', $errCount);
                // can be temporary, so delay the job and schedule for later
                dispatch((new self($this->user, $this->event, $this->value))->delay(now()->addSeconds(30)));
                return;
            }

            throw $exception;
        }
    }
}
