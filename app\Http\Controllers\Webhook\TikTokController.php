<?php

namespace App\Http\Controllers\Webhook;

use App\Notifications\PostFailure;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class TikTokController extends Controller
{
    /**
     * @param Request $request
     * @return bool
     */
    public function verify(Request $request){
        // example signature in header
        // "Tiktok-Signature": "t=1633174587,s=18494715036ac4416a1d0a673871a2edbcfc94d94bd88ccd2c5ec9b3425afe66"

        // Extract the timestamp and signatures from the header
        $signatureHeader = $request->header('Tiktok-Signature', 't=null,s=null');
        $signatureParts = explode(',', $signatureHeader);

        $timestamp = explode('=', $signatureParts[0])[1];
        $signature = explode('=', $signatureParts[1])[1];

        // signed_payload can be created by concatenating:
        // - The timestamp as a string
        // - The character .
        // - The actual JSON payload (request body)
        $payload = $timestamp . '.' . $request->getContent();

        // An HMAC with the SHA256 hash function is computed with your client_secret as the key and your signed_payload string as the message.
        $hash = hash_hmac('sha256', $payload, config('services.tiktok.client_secret'));

        // Compare the hash with the signature
        if (hash_equals($signature, $hash)) {
            return true;
        } else {
            return false;
        }
    }

    public function handle(Request $request){
        // validate the integrity and payload
        if(!$this->verify($request)) abort(403);

        if($request->input('client_key') !== config('services.tiktok.client_key')) {
            // ignore
            return;
        }

        $event = $request->input('event');

        $account_id = $request->input('user_openid');
        $account = \App\Account::where('account_id', $account_id)->where('type', 'tiktok.profile')->where('active', true)->first();

        if(!$account){
            return;
        }

        $content = json_decode($request->input('content'));

        if($event === 'authorization.removed' && $account_id){
            // user removed the app, find account and deactivate it
            $account->deactivate(true, 'User removed the app or its access to their account.');
        } else if($event === 'post.publish.failed' && $content->publish_type === 'INBOX_SHARE'){
            // video upload failed
            $external_id = $content->publish_id;
            $post = \App\Post::where('external_id', $external_id)->where('account_id', $account->id)->first();
            if($post){
                // fail the post
                $post->result = [
                    '_success' => false,
                    '_errorMsg' => 'Post publishing failed: ' . $content->reason,
                    '_timestamp' => time(),
                ];
                $post->save();

                // notify user
                if($post->user) {
                    // notify user of this failure
                    $post->user->notify(new PostFailure($post));
                }
            }
        } else if($event === 'post.publish.complete' && $content->publish_type === 'INBOX_SHARE'){
            // video upload failed
            $external_id = $content->publish_id;
            $post = \App\Post::where('external_id', $external_id)->where('account_id', $account->id)->first();
            if($post){
                $result = $post->result;
                if(!isset($result['_complete'])){
                    $result['_complete'] = true;
                    $result['_complete_timestamp'] = time();
                    $post->result = $result;
                    $post->save();

                    $post->setOption('badge', 'Published');
                }
            }
        } else if($event === 'post.publish.inbox_delivered' && $content->publish_type === 'INBOX_SHARE'){
            // video upload done - pending user review
            $post = \App\Post::where('external_id', $content->publish_id)->where('account_id', $account->id)->first();
            if($post){
                $post->setOption('badge', 'Sent to TikTok Inbox');
            }
        }
    }
}
