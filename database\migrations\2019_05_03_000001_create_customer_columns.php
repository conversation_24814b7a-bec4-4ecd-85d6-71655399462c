<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/*
 * Since we already created these columns in the previous migration, we only need to modify them.
 */
class CreateCustomerColumns extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            // make stripe_id nullable and index
            $table->string('stripe_id')->nullable()->change();

            // add index
            $table->index(['stripe_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('users', function (Blueprint $table) {
            // make stripe_id nullable and index
            $table->string('stripe_id')->nullable(false)->change();

            // remove index
            $table->dropIndex(['stripe_id']);
        });
    }
}
