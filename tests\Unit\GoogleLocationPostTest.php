<?php

namespace Tests\Unit;

use App\Account;
use App\Post;
use App\User;
use Illuminate\Database\Eloquent\Model;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Tests\TestCase;

class GoogleLocationPostTest extends TestCase
{
    private $account = null;

    public function testStandardPost()
    {
        if(1){
            $this->markTestSkipped(
                'only test when needed'
            );
            return;
        }

        $post = $this->publishPost('testing post');

        // now delete the post from google
        $this->getAccount()->getApi()->accounts_locations_localPosts->delete($post->result['name']);

        $this->assertNotNull($post->published_at);
    }

    public function testStandardPostWithPhotos()
    {
        if(1){
            $this->markTestSkipped(
                'only test when needed'
            );
            return;
        }

        $post = $this->publishPost('testing post', $this->getAttachmentsArray([
            resource_path('assets/images/logo-icon.png'),
        ]));

        // now delete the post from google
        $this->getAccount()->getApi()->accounts_locations_localPosts->delete($post->result['name']);

        $this->assertNotNull($post->published_at);
    }

    public function testStandardPostWithCallToAction(){
        if(1){
            $this->markTestSkipped(
                'only test when needed'
            );
            return;
        }

        $post = $this->publishPost('testing post', [], [
            'call_to_action' => 'LEARN_MORE',
            'call_to_action_url' => 'https://socialbu.com/?test',
        ]);

        // now delete the post from google
        $this->getAccount()->getApi()->accounts_locations_localPosts->delete($post->result['name']);

        $this->assertNotNull($post->published_at);
    }

    public function testStandardPostWithCallToActionCall(){
        if(1){
            $this->markTestSkipped(
                'only test when needed'
            );
            return;
        }

        $post = $this->publishPost('testing post', [], [
            'call_to_action' => 'CALL',
        ]);

        // now delete the post from google
        $this->getAccount()->getApi()->accounts_locations_localPosts->delete($post->result['name']);

        $this->assertNotNull($post->published_at);
    }

    public function testEventPost(){
        if(1){
            $this->markTestSkipped(
                'only test when needed'
            );
            return;
        }

        $post = $this->publishPost('testing post', [], [
            'topic_type' => 'EVENT',
            'event_title' => 'My awesome new event',
            'event_start' => now()->addDay(5)->format('d/m/Y h:i a'),
            'event_end' => now()->addDay(15)->format('d/m/Y h:i a'),
        ]);

        // now delete the post from google
        $this->getAccount()->getApi()->accounts_locations_localPosts->delete($post->result['name']);

        $this->assertNotNull($post->published_at);
    }

    public function testEventPostWithPhotos(){
        if(1){
            $this->markTestSkipped(
                'only test when needed'
            );
            return;
        }

        $post = $this->publishPost('testing event with photo', $this->getAttachmentsArray([
            resource_path('assets/images/logo-icon.png'),
        ]), [
            'topic_type' => 'EVENT',
            'event_title' => 'My awesome new event with photo',
            'event_start' => now()->addDay(5)->format('d/m/Y h:i a'),
            'event_end' => now()->addDay(15)->format('d/m/Y h:i a'),
        ]);

        // now delete the post from google
        $this->getAccount()->getApi()->accounts_locations_localPosts->delete($post->result['name']);

        $this->assertNotNull($post->published_at);
    }

    public function testOfferPostWithoutOfferDetails(){
        if(1){
            $this->markTestSkipped(
                'only test when needed'
            );
            return;
        }

        $post = $this->publishPost('testing offer post without details on offer', [], [
            'topic_type' => 'OFFER',
            'event_title' => 'My offer title',
            'event_start' => now()->addDay(5)->format('d/m/Y h:i a'),
            'event_end' => now()->addDay(15)->format('d/m/Y h:i a'),
        ]);

        // now delete the post from google
        $this->getAccount()->getApi()->accounts_locations_localPosts->delete($post->result['name']);

        $this->assertNotNull($post->published_at);
    }

    public function testOfferPost(){
        if(1){
            $this->markTestSkipped(
                'only test when needed'
            );
            return;
        }

        $post = $this->publishPost('testing offer post', [], [
            'topic_type' => 'OFFER',
            'event_title' => 'My offer title',
            'event_start' => now()->addDay(5)->format('d/m/Y h:i a'),
            'event_end' => now()->addDay(15)->format('d/m/Y h:i a'),
            'offer_coupon' => 'v4etrcd-vtvrd',
            'offer_link' => 'https://socialbu.com'
        ]);

        // now delete the post from google
        $this->getAccount()->getApi()->accounts_locations_localPosts->delete($post->result['name']);

        $this->assertNotNull($post->published_at);
    }

    public function testOfferPostWithPhotos(){
        if(1){
            $this->markTestSkipped(
                'only test when needed'
            );
            return;
        }

        $post = $this->publishPost('testing offer post with photo', $this->getAttachmentsArray([
            resource_path('assets/images/logo-icon.png'),
        ]), [
            'topic_type' => 'OFFER',
            'event_title' => 'My offer title',
            'event_start' => now()->addDay(5)->format('d/m/Y h:i a'),
            'event_end' => now()->addDay(15)->format('d/m/Y h:i a'),
            'offer_coupon' => 'v4etrcd-vtvrd',
            'offer_link' => 'https://socialbu.com'
        ]);

        // now delete the post from google
        $this->getAccount()->getApi()->accounts_locations_localPosts->delete($post->result['name']);

        $this->assertNotNull($post->published_at);
    }


    // private helping methods below

    private function getAccount(){
        if(!$this->account){
            // set to use local mysql db so can test with real account
            $settings = config('database.connections.mysql');
            $connFactory = new \Illuminate\Database\Connectors\ConnectionFactory(new \Illuminate\Container\Container());
            $conn = $connFactory->make($settings);
            $resolver = new \Illuminate\Database\ConnectionResolver();
            $resolver->addConnection('default', $conn);
            $resolver->setDefaultConnection('default');
            Model::setConnectionResolver($resolver);
            // Post::setConnectionResolver($resolver);
            // User::setConnectionResolver($resolver);
            // get whatever account is available
            $this->account = Account::whereActive(true)->whereType('google.location')->first();
        }
        return $this->account;
    }

    private function getAttachmentsArray($paths){

        $attachments = [];
        foreach ($paths as $filePath) {
            $tmpPath = @tempnam('temp', 'sb');
            file_put_contents($tmpPath, file_get_contents($filePath));
            $fName = array_last(explode('/', $filePath));
            $attPath = 'attachments/' . time() . $fName;
            $newPath = storage_path('app/public/' . $attPath);
            rename($tmpPath, $newPath);
            $file = new UploadedFile($newPath, $fName);
            $attType = strtolower($file->getClientOriginalExtension());
            $attachments[] = [
                'path' => $attPath,
                'type' => $attType,
                'name' => $file->getClientOriginalName(),
            ];
        }

        return $attachments;
    }

    private function publishPost($text, $attachments = [], $options = []){
        $account = $this->getAccount();

        \Auth::setUser($account->user);

        $post = $account->publishPost([
            'content' => $text,
            'is_draft' => false,
            'options' => $options,
        ], now(), false, null, $attachments);

        // post published now
        return $post->refresh();

    }

}
