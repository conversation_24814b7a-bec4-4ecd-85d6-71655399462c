import $ from "jquery";
import { cloneDeep } from "lodash";

// ready component
const postComposer = new Promise(res => {
    window.__loadComponent("post-editor", "#create_post__editor_container", c => {
        res(c);
    });
});

let $postModal;

// setup post composer
(async () => {
    await postComposer;

    $("#create_post__editor_container").removeClass("d-none"); // un-hide the container

    $postModal = $("#create_post_modal");
    $postModal
        .modal({
            backdrop: "static",
            keyboard: false,
            show: false
        })
        .on("shown.bs.modal", async function(e) {
            // the following are needed so pressing escape key doesn't propagate from the child modals
            if (e.target === this) {
                (await postComposer).focusTextArea();
            }
        })
        .on("hidden.bs.modal", function(e) {
            // the following are needed so pressing escape key doesn't propagate from the child modals
            if (e.target === this) {
            }
        });
})();

const handlers = {
    async newPost(attachments = []) {
        if (!$postModal) {
            return alert("Post composer component has not loaded yet.");
        }
        let composer = await postComposer;
        if (attachments && attachments.length) {
            composer.populateAttachments(attachments);
        }

        $postModal.modal("show");

        window.__recordUsageEvent("post_composer_opened");
    },
    async editPost(post, cb) {
        if (!$postModal) {
            return alert("Post composer component has not loaded yet.");
        }

        const onDone = post => {
            $postModal.modal("hide");
            cb && cb(post);
        };

        const composer = await postComposer;

        await composer.reset(true);

        // set mode to edit
        composer.setMode("edit");

        await composer.populate(post); // populate current post data

        // set post data
        composer.post = post;

        // dont need to show accounts selector
        composer.show.accounts = false;

        // set account
        composer.selectedAccountsIds = [post.account_id];

        // bind callback
        composer.$once("postUpdated", onDone);

        // show the composer
        $postModal.modal("show");

        // reset on close modal
        $postModal.one("hidden.bs.modal", function(e) {
            // the following are needed so pressing escape key doesn't propagate from the child modals
            if (e.target === this) {
                composer.reset(true);
                composer.$off("postUpdated", onDone);
            }
        });

        window.__recordUsageEvent("post_edit");
    },
    async duplicatePost(post) {
        if (!$postModal) {
            return alert("Post composer component has not loaded yet.");
        }

        let onClose, onSave;
        onClose = function(e) {
            // the following are needed so pressing escape key doesn't propagate from the child modals
            if (e.target === $postModal[0]) {
                composer.reset(true);
                composer.$off("postsAdded", onSave);
            }
        };

        onSave = () => {
            $postModal.off("hidden.bs.modal", onClose);
        };

        const composer = await postComposer;

        await composer.reset(true);

        await composer.populate(post); // populate current post data

        // bind callback
        composer.$once("postsAdded", onSave);

        // show the composer
        $postModal.modal("show");

        // reset on close modal
        $postModal.one("hidden.bs.modal", onClose);

        window.__recordUsageEvent("post_duplicate");
    },
    async newQueuePost(queue, cb) {
        if (!$postModal) {
            return alert("Post composer component has not loaded yet.");
        }

        const onDone = data => {
            $postModal.modal("hide");
            cb && cb(data);
        };

        const composer = await postComposer;

        await composer.reset(true);

        // queue add mode
        composer.setMode("queue_add");

        // dont need to show accounts selector
        composer.show.accounts = false;

        // queue team if needed
        composer.selectedTeam = queue.team_id;

        // queue accounts
        composer.selectedAccountsIds = [...queue.options.publish_to];

        // save callback
        composer.$once("postAdded", onDone);

        // show the composer
        $postModal.modal("show");

        // reset on close modal
        $postModal.one("hidden.bs.modal", function(e) {
            // the following are needed so pressing escape key doesn't propagate from the child modals
            if (e.target === this) {
                composer.reset(true);
                composer.$off("postAdded", onDone);
            }
        });

        window.__recordUsageEvent("new_queue_post");

    },
    async editQueuePost(queue, post, cb) {
        if (!$postModal) {
            return alert("Post composer component has not loaded yet.");
        }

        const onDone = data => {
            $postModal.modal("hide");
            cb && cb(data);
        };

        const composer = await postComposer;

        await composer.reset(true);

        // queue add mode
        composer.setMode("queue_edit");

        // dont need to show accounts selector
        composer.show.accounts = false;

        let postData = cloneDeep(post); //so we don't change the original post object

        postData.attachments = postData.options.attachments ? postData.options.attachments : [];
        
        //unset the attachments from options
        delete postData.options.attachments

        await composer.populate(postData);
        // composer.post = post;
        // queue team if needed
        composer.selectedTeam = queue.team_id;

        // queue accounts
        composer.selectedAccountsIds = [...queue.options.publish_to];

        // save callback
        composer.$once("postAdded", onDone);

        // show the composer
        $postModal.modal("show");

        // reset on close modal
        $postModal.one("hidden.bs.modal", function(e) {
            // the following are needed so pressing escape key doesn't propagate from the child modals
            if (e.target === this) {
                composer.reset(true);
                composer.$off("postAdded", onDone);
            }
        });

        window.__recordUsageEvent("edit_queue_post");

    },
};

// bind btn click
$("#menu_create_post_btn").on("click", e => {
    e && e.preventDefault();
    handlers.newPost();
});

export default {
    getPostComposer: () => postComposer,
    ...handlers
};
