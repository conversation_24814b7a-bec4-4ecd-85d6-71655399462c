
// Theme
//
// Change the default theme color, fonts, sizing, etc. by modifying
// the following variables.

// Feel free to override other variables here. You can see the list of
// all the available variables in `/plugin/thesaas/scss/_variables.scss`

// Google fonts
//@import url('https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700|Dosis:200,400,500,600|Quicksand:400,600,700&display=swap');

//----------------------------------------
// Color system
//----------------------------------------
$white:    #fff;
$gray-100: #F2F4F7;
$gray-200: #E4E7ED;
$gray-300: #D1D7E3;
$gray-400: #98A1B2;
$gray-500: #657085;
$gray-600: #465166;
$gray-700: #353F54;
$gray-800: #1E283B;
$gray-900: #101929;
$black:    #000;

$grays: (
  "100": $gray-100, 
  "200": $gray-200,
  "300": $gray-300,
  "400": $gray-400,
  "500": $gray-500,
  "600": $gray-600,
  "700": $gray-700,
  "800": $gray-800,
  "900": $gray-900
);

$blue:    #0557f0;
$indigo:  #6610f2;
$info:    #0286F9;
$purple:  #6f42c1;
$pink:    #e83e8c;
$red:     #D82030;
$orange:  #fd7e14;
$yellow:  #D87E03;
$green:   #1D9033;
$teal:    #20c997;
$cyan:    #0286F9;

// Context colors
$color-primary:                   $blue;
$color-secondary:                 #D5E1F6;
$color-success:                   $green;
$color-info:                      $info;
$color-warning:                   $yellow;
$color-danger:                    $red;
$color-light:                     #F9FAFC;
$color-dark:                      $gray-900;
$socialbu-color:                  $blue;


//----------------------------------------
// Background colors
//----------------------------------------
$color-bg-lightest:               $gray-100;
$color-bg-lighter:                $gray-200;
$color-bg-light:                  $gray-300;
$color-bg-body:                  #FFFFFF;
$color-bg-dark:                   $gray-800;
$color-bg-darker:                 $gray-900;
$color-bg-gray:                   $gray-600;
$socialbu-bg-light-color:         $gray-200;
$socialbu-bg-color:               $gray-300;
$body-bg:                         $color-bg-body;

// Pale colors
//
$pale-colors:(
  "primary":   #E6F1FD,
  "secondary": #E8EDFA,
  "success":   #E9F6EA,
  "danger":    #FFEAEA,
  "warning":   #FFF5E6,
  "info":      #E5F4FE,
  "light":     #DCDDE0,
  "dark":      #E8E8EA,
);

//----------------------------------------
// Text colors
//----------------------------------------
$color-text-darker:               $gray-900;
$color-text-dark:                 $gray-800;
$color-text:                      $gray-600;
$color-text-light:                $gray-200;
$color-text-lighter:              $gray-100;
$color-text-lightest:             $gray-100;
$color-text-secondary:            $gray-700;
$color-text-disable:              $gray-400;
$color-text-placeholder:          $gray-400;
$color-title:                     $gray-800;
$color-subtitle:                  $gray-700;
$body-color:                      $color-text;
$text-muted:                      $gray-600;


// Divider color
$color-divider:                   $gray-300;
$color-divider-light:             $gray-200;
$color-divider-dark:              $gray-600;

// Set a specific jump point for requesting color jumps
$theme-color-interval:        8% !default;
$box-shadow-xs:               0px 4px 8px -1px rgba(29, 33, 41, 0.04);
$box-shadow-sm:               0px 8px 20px -4px rgba(29, 33, 41, 0.08);
$box-shadow:                  0px 12px 24px -6px rgba(29, 33, 41, 0.10);
$box-shadow-md:               0px 12px 24px -6px rgba(29, 33, 41, 0.10);
$box-shadow-lg:               0px 16px 32px -8px rgba(29, 33, 41, 0.12);
$box-shadow-xl:               0px 20px 40px -10px rgba(29, 33, 41, 0.16);
$box-shadow-2xl:              0px 24px 48px -12px rgba(29, 33, 41, 0.20);
$box-shadow-3xl:              0px 28px 60px -14px rgba(29, 33, 41, 0.24);
$box-shadow-blue:             0px 24px 60px -14px rgba(18, 74, 178, 0.80);


$font-size-base:                  1rem;

$font-lead: (
  1: ($font-size-base * 1.125),
  2: ($font-size-base * 1.25),
  3: ($font-size-base * 1.5),
  4: ($font-size-base * 1.75),
  5: ($font-size-base * 2),
  6: ($font-size-base * 2.5),
  7: ($font-size-base * 3.25),
  8: ($font-size-base * 4),
  9: ($font-size-base * 5),
);

$font-small: (
  1: ($font-size-base * 0.9375),
  2: ($font-size-base * 0.875),
  3: ($font-size-base * 0.8125),
  4: ($font-size-base * 0.75),
  5: ($font-size-base * 0.6875),
  6: ($font-size-base * 0.625),
  7: ($font-size-base * 0.5625),
  8: ($font-size-base * 0.5),
  9: ($font-size-base * 0.4375),
);

// Typography
//
// Font, line-height, and color for body text, headings, and more.
// stylelint-disable value-keyword-case
$font-family-sans-serif:      "DM Sans","Nunito", "proxima nova", "helvetica neue", helvetica, sans-serif,-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
$font-family-monospace:       SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace !default;
$font-family-base:            $font-family-sans-serif;
$font-family-title:           'DM Sans', sans-serif;
$font-icon-fontawesome:       FontAwesome !default;
$font-icon-themify:           themify !default;

// stylelint-enable value-keyword-case
$font-size-xs:                0.75rem;// 12px
$font-size-sm:                $font-size-base * .875; // 14px 
$font-size-lg:                $font-size-base * 1.25;// 20px
$font-size-xl:                1.5rem;// 24px

$font-weight-lighter:         lighter !default;
$font-weight-light:           200;
$font-weight-normal:          400;
$font-weight-semibold:        500;
$font-weight-bold:            700;
$font-weight-bolder:          bolder !default;
$font-weight-base:            $font-weight-normal;

$h1-font-size:                $font-size-base * 2.75;
$h2-font-size:                $font-size-base * 2.25;
$h3-font-size:                $font-size-base * 1.75;
$h4-font-size:                $font-size-base * 1.5;
$h5-font-size:                $font-size-base * 1.25;
$h6-font-size:                $font-size-base;

$h1-font-weight:              800;
$h2-font-weight:              700;
$h3-font-weight:              700;
$h4-font-weight:              700;
$h5-font-weight:              600;
$h6-font-weight:              600;
$headings-font-weight:        600;
$headings-line-height:        1.4;

$display1-size:               4rem;
$display2-size:               3rem;
$display3-size:               2.125rem;
$display4-size:               1.5rem;
$display5-size:               1.25rem;

$display1-weight:             800;
$display2-weight:             800;
$display3-weight:             700;
$display4-weight:             700;
$display5-weight:             700;


// Grid columns
//
// Set the number of columns and specify the width of the gutters.
$grid-columns:                12;
$grid-gutter-width:           30px;
$grid-row-columns:            6;
// Components
//
// Define common padding and border radius sizes and more.
$line-height-xs:              1.3;
$line-height-sm:              1.5;
$line-height-base:            1.5;
$line-height-lg:              1.5;
$line-height-xl:              1.5;

$border-width:                .0625rem;
$border-radius:               .25rem;
$border-color:                $color-divider-light;

$border-radius-xs:            .3rem;
$border-radius-sm:            .35rem;
$border-radius-md:            .375rem;
$border-radius-lg:            .5rem; 
$border-radius-xl:            .75rem;
$border-radius-2xl:           1rem;

$rounded-pill:                50rem;
$btn-border-radius:           $border-radius-lg;
//dropdown
$component-active-color:      $blue;
$component-active-bg:         $gray-100;

// // Elements height
// $height-xs:            24px;
// $height-sm:            32px;
// $height-md:            38px;
// $height-lg:            48px;
// $height-xl:            64px;
// $height-xxl:           96px;
// $height-xxxl:          128px;

//----------------------------------------
// Navbar
//----------------------------------------
$navbar-light-color:                $gray-900;
$navbar-light-color-hover:          $gray-900;
$navbar-light-color-active:         $gray-900;
$navbar-light-hover-color:          $gray-900;
$navbar-light-active-color:         $color-primary;
$navbar-light-disabled-color:       $color-text-disable;
$navbar-light-toggler-border-color: $color-divider-light;
$navbar-min-height:         50px;
$navbar-offset-top:         10px;


// Navs
//
$nav-tabs-border-color:             $color-divider;

// Tables
//
// Customizes the `.table` component with basic values, each used across all table variations.
$table-cell-padding:          1.25rem;
$table-cell-padding-sm:       .5rem;
$table-bg:                    $white;


// Buttons + Forms
//
// Shared variables that are reassigned to `$input-` and `$btn-` specific variables.
$input-btn-padding-y:         .75rem;
$input-btn-padding-x:         1.255rem;
$input-btn-line-height:       $line-height-base;
$input-btn-focus-box-shadow:  0px 12px 24px -6px rgba(29, 33, 41, 0.10);

$input-btn-padding-y-xs:      .375rem;
$input-btn-padding-x-xs:      1rem;
$input-btn-line-height-xs:    $line-height-xs;

$input-btn-padding-y-sm:      .75rem;
$input-btn-padding-x-sm:      1rem;
$input-btn-line-height-sm:    $line-height-sm;

$input-btn-padding-y-lg:      1rem;
$input-btn-padding-x-lg:      1.5rem;
$input-btn-line-height-lg:    $line-height-lg;

$input-btn-padding-y-xl:      1.375rem;
$input-btn-padding-x-xl:      1.8rem;
$input-btn-line-height-xl:    $line-height-xl;

$input-btn-border-width:      $border-width;

$input-btn-font-family:       'DM Sans';
$input-btn-font-size:          1rem;
$input-btn-font-size-sm:       .875rem;
$input-btn-font-size-lg:       1.25rem;

$btn-font-weight:             $font-weight-semibold;

//copied form node moduless
// Forms
$label-margin-bottom:                   1rem;

$input-padding-y:                       $input-btn-padding-y;
$input-padding-x:                       $input-btn-padding-x;
$input-font-family:                     $input-btn-font-family;
$input-font-size:                       $input-btn-font-size;
$input-font-weight:                     $font-weight-base;
$input-line-height:                     $input-btn-line-height;

$input-padding-y-sm:                    $input-btn-padding-y-sm;
$input-padding-x-sm:                    $input-btn-padding-x-sm;
$input-font-size-sm:                    $input-btn-font-size-sm;
$input-line-height-sm:                  $input-btn-line-height-sm;

$input-padding-y-lg:                    $input-btn-padding-y-lg;
$input-padding-x-lg:                    $input-btn-padding-x-lg;
$input-font-size-lg:                    $input-btn-font-size-lg;
$input-line-height-lg:                  $input-btn-line-height-lg;

$input-bg:                              $white;
$input-disabled-bg:                     $gray-100;
$input-color:                           $color-text;

$input-border-color:                    $gray-300;
$input-border-width:                    $input-btn-border-width;
$input-border-radius:                   $border-radius-lg;
$input-border-radius-lg:                $border-radius-lg;
$input-border-radius-sm:                $border-radius-lg;

$input-focus-bg:                        $input-bg;
$input-focus-border-color:              lighten($component-active-bg, 25%);
$input-focus-color:                     $input-color;
$input-focus-box-shadow:                $input-btn-focus-box-shadow;

$input-placeholder-color:               $color-text-placeholder;

$input-transition:                      border-color .15s ease-in-out, box-shadow .15s ease-in-out;

$form-text-margin-top:                  .25rem;
$form-check-input-gutter:               1.5rem;
$form-check-input-margin-y:             .25rem;


// Form validation
$form-feedback-valid-color:         $color-success;
$form-feedback-invalid-color:       $color-danger;

// Badges
//
$badge-font-size:             75%;
$badge-font-weight:           500;
$badge-padding-x:             .5em;
$badge-border-radius:         $border-radius-xs;

// Pagination
$pagination-padding-y:              .5rem;
$pagination-padding-x:              1.25rem;
$pagination-padding-y-sm:           .5rem;
$pagination-padding-x-sm:           .75rem;
$pagination-padding-y-lg:           .75rem;
$pagination-padding-x-lg:           1.5rem;
$pagination-line-height:            1.5;

$pagination-color:                  $color-text;
$pagination-bg:                     $body-bg;
$pagination-border-width:           $border-width;
$pagination-border-color:           $gray-300;

$pagination-hover-color:            $white;
$pagination-hover-bg:               $color-primary;
$pagination-hover-border-color:     $color-primary;

$pagination-active-color:           $white;
$pagination-active-bg:              $color-primary;
$pagination-active-border-color:    $color-primary;

// Popovers
//
$popover-border-color:              $border-color;
$popover-header-bg:                 $color-bg-light;

// Dropdowns
//
$dropdown-min-width:                15rem;
$dropdown-padding-y:                .5rem;
$dropdown-spacer:                   .125rem;
$dropdown-border-color:             $color-divider-light;
$dropdown-border-radius:            $border-radius-md;
$dropdown-divider-bg:               $color-divider-light;
$dropdown-box-shadow:               0px 10px 40px -6px rgba(29, 33, 41, 0.12);

$dropdown-link-color:               $color-dark;
$dropdown-link-hover-bg:            $gray-300 ;
$dropdown-link-disabled-color:      $color-text-disable;

$dropdown-item-padding-y:           .75rem;
$dropdown-item-padding-x:           1.25rem;


// Modals
// Padding applied to the modal body
$modal-inner-padding:               2rem;

// Margin between elements in footer, must be lower than or equal to 2 * $modal-inner-padding

$modal-title-line-height:           $line-height-lg;

$modal-content-border-color:        none;
$modal-header-border-color:         none;
$modal-header-padding-y:            2rem;
$modal-header-padding-x:            2rem;
$modal-header-padding:              2rem 2rem 1rem 2rem;
// $modal-header-padding:

// Cards
$card-spacer-y:                     2rem;
$card-spacer-x:                     2rem;
$card-border-width:                 $border-width;
$card-border-radius:                $border-radius-lg;
$card-border-color:                 $border-color;
$card-color:                        $color-text;

$card-group-margin:                 $grid-gutter-width * .5;


// Toasts
$toast-max-width:                   400px;
$toast-font-size:                   1.25rem;
$toast-color:                       $color-text;
$toast-background-color:            $white;
$toast-border-width:                1px;
$toast-border-color:                $gray-200;
$toast-border-radius:               .5rem;

$toast-header-color:                $gray-800;
$toast-header-background-color:     $white;

// Timeline
//
$timeline-color: $color-primary;

// List group
$list-group-item-padding-y:         1rem;
$list-group-item-padding-x:         1.25rem;

$list-group-active-color:           $white;
$list-group-active-bg:              $color-primary;
$list-group-active-border-color:    $color-primary;


$app-sidebar-width: 280px;
$app-footer-width: 100%;
$app-footer-height: 50px;

$brand-colors: (
  "youtube":     #ff0000
);

$enable-responsive-font-sizes: true; //for display properties
