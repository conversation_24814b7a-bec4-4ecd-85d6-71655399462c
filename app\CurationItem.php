<?php

namespace App;

use App\Helpers\ApiHelper;
use App\Traits\HasOptions;
use Illuminate\Database\Eloquent\Model;

/**
 * App\CurationItem
 *
 * @property int $id
 * @property int $feed_id
 * @property string|null $title
 * @property string|null $link
 * @property string|null $description
 * @property string|null $media
 * @property string|null $published_at
 * @property string|null $authors
 * @property string|null $tags
 * @property string $guid
 * @property int $score
 * @property string|null $scored_at
 * @property array|null $options
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|CurationItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CurationItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CurationItem query()
 * @method static \Illuminate\Database\Eloquent\Builder|CurationItem whereAuthors($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationItem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationItem whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationItem whereFeedId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationItem whereGuid($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationItem whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationItem whereLink($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationItem whereMedia($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationItem whereOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationItem wherePublishedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationItem whereScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationItem whereScoredAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationItem whereTags($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationItem whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationItem whereUpdatedAt($value)
 * @mixin \Eloquent
 * @property-read \App\CurationFeed $feed
 */
class CurationItem extends Model
{
    use HasOptions;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'feed_id', 'title', 'link', 'description', 'media', 'published_at', 'authors', 'tags', 'guid', 'score',
    ];

    protected $hidden = [
        'options',
        'guid',
        'scored_at',
        'created_at',
        'updated_at',
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'options' => 'array',
    ];

    public static function transformGUID($guid)
    {
        // guid must be maximum 191 characters
        return ensure_utf8(substr($guid, 0, 191));
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function feed(){
        return $this->belongsTo(CurationFeed::class, 'feed_id');
    }

    /**
     * @return void
     * @throws \Exception
     */
    public function updateScore(){
        $score = 0;

        $score += $this->getFacebookScore();

        $score += $this->getTwitterScore();

        try {
            $score += $this->getRedditScore();
        } catch (\Exception $e) {
            // do nothing
        }

        try {
            $score += $this->getPinterestScore();
        } catch (\Exception $e) {
            // do nothing
        }

        $score += $this->getInternalScore();

        $this->score = $score;
        $this->scored_at = now();

        $this->save();
    }

    /**
     * @return int
     * @throws \Exception
     */
    public function getFacebookScore(){
        // first we find the score using fb api
        $fb = ApiHelper::getFacebook(config('services.facebook.client_id') . '|' . config('services.facebook.client_secret'));
        $response = $fb->get('/?id=' . urlencode($this->link) . '&fields=engagement')->getDecodedBody();

        $score = 0;

        if($response && isset($response['engagement'])){
            // add likes/reactions as is
            $score = $response['engagement']['reaction_count'];
            // give more weight to shares and comments
            $score += ($response['engagement']['share_count'] * 3) + ($response['engagement']['comment_count'] * 2);
        }

        return (int) $score;
    }

    /**
     * @param bool $forceOurAccount
     * @return int
     * @throws \Exception
     */
    public function getTwitterScore(bool $forceOurAccount = false){
        // find the score using Twitter api
        $score = $this->getOption('twitter.score', 0);
        $last_tweet_id = $this->getOption('twitter.last_tweet_id');

        $twitterAccount = null;

        if($forceOurAccount){
            // we force to use our account
            $twitterAccount = Account::where('type', 'twitter.profile')->where('active', true)->where('name', 'socialbuapp')->orderBy('updated_at')->first();
        }

        $user =  $this->feed->user;
        if($user && !$twitterAccount){
            $userAccounts = $this->feed->user ->getAvailableAccounts()->filter(function($account){
                return $account->type === 'twitter.profile';
            })->map(function($account){
                return $account->id;
            });
            $twitterAccount = Account::where('type', 'twitter.profile')
                ->where('active', true)
                ->whereIn('id', $userAccounts)
                ->orderBy('updated_at')
                ->first();
        }

        if(!$twitterAccount){
            // if no account is found, we use any available account
            $twitterAccount = Account::where('type', 'twitter.profile')
                ->where('active', true)
                ->orderBy('updated_at')
                ->first();
        }

        if(!$twitterAccount){
            throw new \Exception('No twitter account available');
        }

        $twitter = $twitterAccount->getApi();

        // we have to find newer tweets than the last one we have and then count them
        // and, we will always add the new score to our current score
        try {
            $response = $twitter->get("search/tweets", array_filter([
                'q' => 'filter:links url:' . $this->link,
                'count' => 100,
                'since_id' => $last_tweet_id,
                'result_type' => 'recent',
            ]));

            if ($twitter->getLastHttpCode() === 200) {
                if (!empty($response->statuses)) {
                    // order desc by id (new - old)
                    $tweets = collect($response->statuses)->sortByDesc('id');
                    $last_tweet_id = $tweets->first()->id_str;

                    $score += $tweets->count() * 3;

                    // also count the retweets
                    $score += $tweets->sum('retweet_count') * 2;

                    // also count the favorites
                    $score += $tweets->sum('favorite_count');
                }
            }
        } catch (\Exception $e) {

        }

        // we save the last tweet id
        if($last_tweet_id){
            $this->setOption('twitter.last_tweet_id', $last_tweet_id);
        }

        if($score > 0){
            $this->setOption('twitter.score', (int) $score);
        }

        return (int) $score;
    }

    /**
     * @return int
     * @throws \Exception
     */
    public function getPinterestScore(){
        // url: https://api.pinterest.com/v1/urls/count.json?callback=json&url=http://www.example.com
        $score = 0;

        $client = guzzle_client();
        $response = $client->get('https://api.pinterest.com/v1/urls/count.json?callback=json&url=' . urlencode($this->link));
        $body = $response->getBody()->getContents();

        // we have to remove the callback part from the start and from the end
        $body = substr($body, 5, -1);

        $data = json_decode($body, true);

        if($data && isset($data['count'])){
            $score = $data['count'];
        }

        return (int) $score;
    }

    /**
     * @return int
     * @throws \Exception
     */
    public function getRedditScore(){
        // url: https://www.reddit.com/search.rss?q=url%3Ahttps%3A%2F%2Fsocialbu.com%2F
        $score = 0;

        $feed = new \SimplePie();
        $feed->set_feed_url('https://www.reddit.com/search.rss?q=' . urlencode('url:' . $this->link));

        $feed->enable_cache(false);

        try {
            $feed->init();
        } catch (\Exception $e) {
            throw new \Exception('Error while fetching reddit score');
        }

        $items = $feed->get_items();

        if($items){
            // count the items
            $score = count($items);
        }

        return (int) $score;
    }

    /**
     * @return int
     * @throws \Exception
     */
    public function getInternalScore(){
        // find the current score if any
        $score = $this->getOption('internal.score', 0);

        if(!$score){
            // if no score is found, we will calculate
            $feed = $this->feed;
            $domain = parse_url($feed->url, PHP_URL_HOST);

            // if this is our feed, we will inflate the score
            if($domain === 'socialbu.com'){
                // set a random score between 100 and 1000
                $score = rand(100, 2500);
            }

            if($score > 0){
                $this->setOption('internal.score', (int) $score);
            }
        }

        return (int) $score;
    }

    private function getOptions()
    {
        return (array)$this->options;
    }

    private function setOptions($options)
    {
        $this->options = $options;
        $this->save();
        return $this;
    }
}
