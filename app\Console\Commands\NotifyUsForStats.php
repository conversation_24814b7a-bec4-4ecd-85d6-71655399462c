<?php

namespace App\Console\Commands;

use App\Post;
use App\User;
use Carbon\Carbon;
use Illuminate\Console\Command;

class NotifyUsForStats extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'socialbu:notify_stats';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Notify our chat about some stats';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $yesterday = Carbon::yesterday()->timezone('Asia/Karachi');

        $usersCount = User::whereDate('created_at', $yesterday)->count();
        $postsCreatedCount = Post::whereDate('created_at', $yesterday)->count();
        $postsPublishedCount = Post::whereDate('published_at', $yesterday)->count();

        notify_chat(
            '<b>Stats for ' . $yesterday->format('Y-m-d') . '</b><br/>' .
            'New users: ' . $usersCount . '<br/>' .
            'Posts created: ' . $postsCreatedCount . '<br/>' .
            'Posts published: ' . $postsPublishedCount . '<br/>'
        );
    }
}
