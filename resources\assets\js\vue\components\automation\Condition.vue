<template>
    <div class="w-100">
        <div>
            <div v-for="(condition, index) in data" v-if="data.length">
                <div class="mt-4" v-if="condition.conditions">
                    <div class="condition-operator d-flex justify-content-between align-items-center">
                        <div
                            v-tour:atmvue_conditon_operator="'This is the condition group operator. You can set it to satisfy every or any condition in this group.'">
                            <button class="btn btn-outline-light btn-sm"
                                @click="setGroupOperator(index, 'AND')"
                                :class="{'active': condition.operator === 'AND'}">
                                ALL
                            </button>
                            <button class="btn btn-outline-light btn-sm ml-2"
                                @click="setGroupOperator(index, 'OR')"
                                :class="{'active': condition.operator === 'OR'}">
                                ANY
                            </button>
                            <i class="icon ph ph-warning-circle ph-lg text-danger ml-2 mt-2" title="Operator is not set"
                                v-if="condition.conditions && !condition.operator" v-tooltip></i>
                        </div>
                        <button class="btn btn-outline-light btn-sm p-2" title="Delete condition group" @click="deleteCondition(index)" v-tooltip>
                            <i class="ph ph-trash ph-md"></i>
                        </button>
                    </div>
                    <condition :action-group-index="actionGroupIndex" :data="condition.conditions" :update="conds => updateConditions(index, conds)" />
                </div>
                <div class="d-flex mt-4" v-else>
                    <div class="container p-0">
                        <div class="row align-items-center flex-md-row flex-column-reverse">
                            <div class="col-md-11">
                                <div class="row">

                                    <div class="col-md-4 col-12 mb-md-0 mb-4 pr-0">
                                        <select title="Select property" class="form-control"
                                            v-model="condition.key" v-selectpicker>
                                            <option v-for="cond in $root.availableConditions" :value="cond.key" v-if="!cond.key.split('.')[1]">
                                                {{ cond.description }}
                                            </option>
                                            <optgroup label="Facebook User">
                                                <option v-for="cond in $root.availableConditions" :value="cond.key" v-if="cond.key.split('.')[0] === 'facebook'">
                                                    {{ cond.description }}
                                                </option>
                                            </optgroup>
                                            <optgroup label="Instagram User">
                                                <option v-for="cond in $root.availableConditions" :value="cond.key" v-if="cond.key.split('.')[0] === 'instagram'">
                                                    {{ cond.description }}
                                                </option>
                                            </optgroup>
                                            <optgroup label="Twitter User">
                                                <option v-for="cond in $root.availableConditions" :value="cond.key" v-if="cond.key.split('.')[0] === 'twitter'">
                                                    {{ cond.description }}
                                                </option>
                                            </optgroup>
                                                <!--
                                                <optgroup label="Actions">
                                                    <option v-for="cond in $root.availableConditions" :value="cond.key" v-if="cond.key.startsWith('action_') && cond.groupIndex < actionGroupIndex">
                                                        {{ cond.description }}
                                                    </option>
                                                </optgroup>
                                                -->
                                            <optgroup :label="'Group #' + (grpIndex + 1)"
                                                v-for="(conds, grpIndex) in $root.availableConditionsFromActionsGrouped" v-if="grpIndex < actionGroupIndex">
                                                <option v-for="cond in conds" :value="cond.key">
                                                    Action #{{ cond.actionIndex + 1 }}: {{ cond.description }}
                                                </option>
                                            </optgroup>
                                        </select>
                                    </div>
                                    <div class="col-md-4 col-12 mb-md-0 mb-4 px-2"
                                        v-if="condition.key && $root.availableConditions[condition.key] && $root.availableConditions[condition.key].operator">
                                        <select title="Select operator" class="form-control"
                                            v-model="condition.operator" v-selectpicker>
                                            <option v-for="op in $root.availableConditions[condition.key].operator" :value="op.type">
                                                {{ op.description }}
                                            </option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 col-12 mb-md-0 mb-4 px-0"
                                        v-if="condition.operator && $root.availableConditions[condition.key] && $root.availableConditions[condition.key].value">
                                        <div v-if="$root.availableConditions[condition.key].value.type === 'text'">
                                            <input type="text" class="form-control"
                                                :placeholder="$root.availableConditions[condition.key].description" v-model.trim="condition.value"/>
                                        </div>
                                        <div v-else-if="$root.availableConditions[condition.key].value.type === 'textarea'">
                                            <textarea class="form-control"
                                                :placeholder="$root.availableConditions[condition.key].description" v-model.trim="condition.value"></textarea>
                                        </div>
                                        <div v-else-if="$root.availableConditions[condition.key].value.type === 'number'">
                                            <input type="number" class="form-control"
                                                :placeholder="$root.availableConditions[condition.key].description" v-model.trim="condition.value"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-1 text-right mb-md-0 mb-3">
                                <span class="btn btn-outline-light btn-xs p-2" @click="deleteCondition(index)"><i class="ph ph-trash ph-md" title="Delete"></i></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-2" :class="'ml-5' ? data.length : 'ml-0'">
                <a role="button" class="btn btn-light btn-sm mr-2 mb-md-0 mb-3" @click="addItem">
                    <div class="d-flex align-items-center">
                        <i class="ph-bold ph-plus mr-2"></i> {{ "Add" + (data.length ? " another":"") + " condition" }}
                    </div>
                </a>
                <a role="button" class="btn btn-light btn-sm" @click="addGroup">
                    <div class="d-flex align-items-center">
                        <i class="ph-bold ph-plus mr-2"></i> Add condition group
                    </div>
                </a>
            </div>
        </div>    
    </div>
</template>

<script>
export default {
    name: "Condition",
    data() {
        return {};
    },
    props: ["data", "update", "actionGroupIndex"],
    methods: {
        addItem() {
            this.update([
                ...this.data,
                {
                    key: null,
                    operator: null,
                    value: null
                }
            ]);
        },
        addGroup() {
            this.update([
                ...this.data,
                {
                    conditions: [],
                    operator: null
                }
            ]);
        },
        updateConditions(index, conditions) {
            const curData = [...this.data];
            curData[index].conditions = conditions;
            this.update(curData);
        },
        deleteCondition(index) {
            const curData = [...this.data];
            curData.splice(index, 1);
            this.update(curData);
        },
        setGroupOperator(index, operator) {
            const curData = [...this.data];
            curData[index].operator = operator;
            this.update(curData);
        }
    },
    mounted() {
        //  TODO: this is a temp fix, selectpicker doesn't show actual values in ui. find the reason and fix
        setTimeout(() => {
            this.$forceUpdate();
            //console.log('force update');
        }, 50);
    }
};
</script>

<style scoped lang="scss">
</style>
