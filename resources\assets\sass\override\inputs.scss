input[type="range"] {
    /* removing default appearance */
    -webkit-appearance: none;
    appearance: none; 
    /* creating a custom design */
    width: 100%;
    cursor: pointer;
    outline: none;
    border-radius: 15px;
    overflow: hidden;

    /* New additions */
    height: 15px;
    background: #ccc;
}
/* Thumb: webkit */
input[type="range"]::-webkit-slider-thumb {
    /* removing default appearance */
    -webkit-appearance: none;
    appearance: none; 
    /* creating a custom design */
    height: 15px;
    width: 15px;
    margin: 0px;
    background-color: #fff;
    border: 2px solid $color-primary;
    border-radius: 50%;
    box-shadow: -690px 1px 1px 680px $color-primary;
    transition: .2s ease-in-out;
}
/* Thumb: Firefox */
input[type="range"]::-moz-range-thumb {
    height: 15px;
    width: 15px;
    background-color: #fff;
    border-radius: 50%;
    border: none;
    box-shadow: -690px 1px 1px 680px $color-primary;
    transition: .2s ease-in-out;
}
/* Track: webkit browsers */
input[type="range"]::-webkit-slider-runnable-track {
    height: 15px;
    background: $gray-300;
    border-radius: 16px;
}
/* Track: Mozilla Firefox */
input[type="range"]::-moz-range-track {
    height: 15px;
    background: $gray-300;
    border-radius: 16px;
}
input[type=checkbox] {
    position: relative;
    border: 1.4px solid $gray-600;
    border-radius: 4px;
    background: none;
    cursor: pointer;
    line-height: 0;
    margin: 0 .3em 0 0;
    outline: 0;
    padding: 0 !important;
    vertical-align: text-top;
    background: #FFF;
    height: 16px;
    width: 16px;
    -webkit-appearance: none;
    &:hover{
        border: 1.4px solid $color-primary;
        background: #D7ECFF;
    }
    &:focus-visible{
        background : #FFF;
        border: 1.4px solid $color-primary;
        box-shadow: 0px 0px 0px 4px #B7E0FF;
    }
}
input[type=checkbox]:checked{
    border: 1.4px solid $color-primary;
    box-shadow: none;
    &:hover{
        border: 1.4px solid $color-primary;
        background: #D7ECFF;
    }
    &:focus-visible{
        background : #FFF;
        border: 1.4px solid $color-primary;
        box-shadow: 0px 0px 0px 4px #B7E0FF;
    }
    &:disabled{
        background-color: #fff;
        border: 1.4px solid $color-primary;
        opacity: 0.6;
    }
}
input[type=checkbox]:disabled{
    background-color: #FFF;
    border: 1.4px solid $gray-600;
    border-radius: 4px;
    opacity: 0.6;
}
input[type=checkbox]:checked:before {
    content: '';
    position: absolute;
    right: 45%;
    top: 45%;
    width: 5px;
    height: 10px;
    border: none;
    border-bottom: 1.4px solid $color-primary;
    border-right: 1.4px solid $color-primary;
    transform: rotate(45deg) translate(-50%, -50%);
}