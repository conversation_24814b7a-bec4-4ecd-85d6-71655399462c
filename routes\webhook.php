<?php

use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register Webhook routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "webhook" middleware group. Enjoy building your WEBHOOK endpoints!
|
*/

Route::get('/facebook', 'Webhook\FacebookController@verify');
Route::post('/facebook', 'Webhook\FacebookController@handle');
Route::post("/facebook/delete", "Webhook\FacebookController@delete");
Route::post('/automation/{public_id}', 'Webhook\AutomationController@handle');
Route::post('/stripe', 'Webhook\StripeController@handleWebhook');
Route::get('/instagram', 'Webhook\InstagramController@verify');
Route::post('/instagram', 'Webhook\InstagramController@handle');
Route::post('/tiktok', 'Webhook\TikTokController@handle');
// Route::post("/threads", "Webhook\ThreadsController@handle");
Route::post("/threads/delete", "Webhook\ThreadsController@delete");
Route::post("/threads/uninstall", "Webhook\ThreadsController@uninstalled");
