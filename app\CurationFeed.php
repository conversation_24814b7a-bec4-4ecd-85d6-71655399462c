<?php

namespace App;

use App\Jobs\AddCurationItem;
use App\Traits\HasOptions;
use Carbon\Carbon;
use GuzzleHttp\Psr7\UriResolver;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

/**
 * App\CurationFeed
 *
 * @property int $id
 * @property string $url
 * @property \Illuminate\Support\Carbon|null $fetched_at
 * @property int $active
 * @property array|null $options
 * @property int|null $user_id
 * @property int|null $team_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\CurationTopic[] $topics
 * @property-read int|null $topics_count
 * @method static \Illuminate\Database\Eloquent\Builder|CurationFeed newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CurationFeed newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CurationFeed query()
 * @method static \Illuminate\Database\Eloquent\Builder|CurationFeed whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationFeed whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationFeed whereFetchedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationFeed whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationFeed whereOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationFeed whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationFeed whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationFeed whereUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationFeed whereUserId($value)
 * @mixin \Eloquent
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\CurationItem[] $items
 * @property-read int|null $items_count
 * @property-read \App\User|null $user
 */
class CurationFeed extends Model
{
    use HasOptions;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'url', 'user_id', 'team_id'
    ];

    protected $hidden = [
        'fetched_at',
        'options'
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'options' => 'array',
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'fetched_at',
    ];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user(){
        return $this->belongsTo(User::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function topics()
    {
        return $this->belongsToMany(CurationTopic::class, 'curation_feed_topic', 'feed_id', 'topic_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function items(){
        return $this->hasMany(CurationItem::class, 'feed_id');
    }

    /**
     * Fetch the feed and return the SimplePie object
     * @return \SimplePie|null
     * @throws \Exception
     */
    public function getFeed($rawBody = null){

        $feed = new \SimplePie();

        if($rawBody){
            $rawBody = ensure_utf8($rawBody);
            $feed->set_raw_data($rawBody);
        } else {
            $feed->set_timeout(30);
            $feed->set_feed_url($this->url);
            // disable caching
            $feed->enable_cache(false);
        }

        $feed->enable_order_by_date(true);

        $feed->init();

        if ($feed->error()) { // check if it's a valid feed
            throw new \Exception($feed->error());
        }

        return $feed;
    }

    /**
     * @return bool
     */
    public function shouldFetch(){

        $lastSuccessfulFetchAt = $this->getOption('last_successful_fetch_at'); // successful fetch means the feed is valid and has new items
        if(!$lastSuccessfulFetchAt){
            // last successful fetch is not set, so we can set it to any last fetch attempt
            $lastSuccessfulFetchAt = $this->fetched_at ? $this->fetched_at : Carbon::now()->subYear(1);
        } else {
            $lastSuccessfulFetchAt = \Carbon\Carbon::createFromTimestamp($lastSuccessfulFetchAt);
        }

        $attemptsWithoutYield = $this->getOption('attempts_without_yield', 0);

        if(!should_poll_exponential($lastSuccessfulFetchAt, $attemptsWithoutYield + 1, 60 * 60 * 3, 60 * 60 * 24 * 7)){
            // we delay exponentially based on the last successful fetch
            // we delay exponentially by 3 hrs and up to 7 days
            return false;
        }

        return true;
    }

    /**
     * Fetch the feed if needed and add the items to the database
     * @return false|int The number of new items or false if the feed is invalid or was not fetched
     */
    public function fetch($rawFeed = null, $error = null){

        $onFailure = function($updateYield = true){
            $attemptsWithoutYield = $this->getOption('attempts_without_yield', 0);
            if($updateYield) {
                $this->setOption('attempts_without_yield', $attemptsWithoutYield + 1);
            }
            $this->fetched_at = now();
            $this->save();
        };
        $onSuccess = function(){
            $this->setOption('last_successful_fetch_at', time()); // set last successful fetch to now
            $this->setOption('attempts_without_yield', 0); // reset attempts without yield
            $this->fetched_at = now();
            $this->save();
        };

        if(!$this->shouldFetch()){
            $onFailure(false); // we still update the last fetch time, so we don't keep trying to fetch
            return false;
        }

        if($error){
            // we have an error already, so just update the timestamp
            $onFailure();
            return false;
        }

        try {
            // notice start time
            $feed = $this->getFeed($rawFeed);
        } catch (\Exception $e) {
            $onFailure();
            return false;
        }
        if(!$feed){
            $onFailure();
            return false;
        }

        // we have the feed here, check for new items
        $items = $feed->get_items(0, 50); // get the first 50 items
        if(!$items){
            $onFailure();
            return false;
        }

        // we have items, check if we have any new items
        $newItems = 0;
        foreach($items as $item){

            $url = $item->get_permalink();
            if(!$url || !filter_var($url, FILTER_VALIDATE_URL)){
                continue;
            }

            // first, find possible guids
            $guids = array_values(array_filter([
                CurationItem::transformGUID($item->get_id()),
                CurationItem::transformGUID($url),
            ]));

            // then, check if we have any of the guids in the database
            $existingItem = CurationItem::whereIn('guid', $guids)->first();
            if($existingItem){
                // we have an existing item, so we don't need to add it
                continue;
            }

            // dispatch job: we will add the item to the database in a job
            dispatch( (new AddCurationItem($this, $item)) );

            $newItems++;

        }

        // update the last fetch time
        if($newItems > 0){
            $onSuccess(); // we got new items
        } else {
            $onFailure(); // we didn't get new items
        }

        return $newItems;
    }

    private function getOptions()
    {
        return (array)$this->options;
    }

    private function setOptions($options)
    {
        $this->options = $options;
        $this->save();
        return $this;
    }
}
