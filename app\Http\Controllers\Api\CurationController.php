<?php

namespace App\Http\Controllers\Api;

use App\CurationFeed;
use App\CurationItem;
use App\CurationTopic;
use App\Team;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Validation\ValidationException;

class CurationController extends Controller
{
    public function getTopics(Request $request){
        $this->validate($request, [
            'q' => 'string|max:180',
        ]);

        return response()->json(
            CurationTopic::orderBy('name')
                ->when($request->has('q'), function(/** @var $query Builder */ $query) use($request){
                    return $query->where('name', 'like', '%'.$request->input('q').'%');
                })
                ->get()
        );
    }

    public function getItems(Request $request){
        $this->validate($request, [
            'page' => 'integer|min:1',
            'per_page' => 'integer|min:1|max:100',
            'authors' => 'array',
            'authors.*' => 'string|max:180',
            'feed_id' => 'integer',
            'topics' => 'array',
            'topics.*' => 'integer|exists:curation_topics,id',
            'search' => 'string|min:3',
            'from' => 'date_format:Y-m-d',
            'to' => 'date_format:Y-m-d',
        ]);

        $perPage = $request->input('per_page', 20);
        $page = $request->input('page', 1);

        $fromCarbon = $request->has('from') ? \Carbon\Carbon::parse($request->input('from')) : null;
        if(!$fromCarbon){
            $fromCarbon = \Carbon\Carbon::now()->subDays(7);
        }
        $toCarbon = $request->has('to') ? \Carbon\Carbon::parse($request->input('to')) : null;
        if(!$toCarbon){
            $toCarbon = \Carbon\Carbon::now();
        }

        // make sure from is before to
        if($fromCarbon->gt($toCarbon)){
            throw ValidationException::withMessages([
                'from' => 'From date must be before to date',
            ]);
        }

        $paginatedResponse = $this->items()
            ->when($request->has('feed_id'), function(/** @var $query Builder */ $query) use($request){
                return $query->where('feed_id', $request->input('feed_id'));
            })
            ->when($request->has('topics'), function(/** @var $query Builder */ $query) use($request){
                // important: use whereIn for fast query; whereHas is slow for too many rows of items
                return $query->whereIn('feed_id', function(/** @var $query Builder */ $query) use($request){
                    return $query->select('feed_id')
                        ->from('curation_feed_topic')
                        ->whereIn('topic_id', $request->input('topics'));
                });
                /*
                return $query->whereHas('feed.topics', function($query) use($request){
                    return $query->whereIn('curation_topics.id', $request->input('topics'));
                });
                */
            })
            ->when($request->has('authors'), function(/** @var $query Builder */ $query) use($request){
                // search authors csv for any of the given authors
                $i = 0;
                foreach ($request->input('authors') as $author) {
                    if ($i === 0) {
                        $query = $query->where('authors', 'like', '%' . $author . '%');
                    } else {
                        $query = $query->orWhere('authors', 'like', '%' . $author . '%');
                    }
                    $i++;
                }
                return $query;
            })
            ->when($request->has('search'), function(/** @var $query Builder */ $query) use($request){
                return $query->where('title', 'like', '%' . $request->input('search') . '%');
            })
            ->whereBetween('published_at', [$fromCarbon->toDateTimeString(), $toCarbon->toDateTimeString()])
            ->paginate($perPage, ['*'], 'page', $page);

        return response()->json([
            'items' => $paginatedResponse->items(),
            'currentPage' => $paginatedResponse->currentPage(),
            'lastPage' => $paginatedResponse->lastPage(),
            'nextPage' => $paginatedResponse->hasMorePages() ? $paginatedResponse->currentPage() + 1 : null,
            'total' => $paginatedResponse->total(),
        ]);
    }

    public function getItem(Request $request, $id){
        return response()->json(
            $this->items()->findOrFail($id)
        );
    }

    public function getFeeds(Request $request){
        $this->validate($request, [
            'page' => 'integer|min:1',
            'per_page' => 'integer|min:1|max:100',
        ]);

        $perPage = $request->input('per_page', 20);
        $page = $request->input('page', 1);

        $paginatedResponse = $this->feeds()
            ->orderBy('name')
            ->paginate($perPage, ['*'], 'page', $page);

        return response()->json([
            'items' => $paginatedResponse->items(),
            'total' => $paginatedResponse->total(),
            'per_page' => 100,
            'current_page' => 1,
            'last_page' => $paginatedResponse->lastPage(),
            'next_page' => $page < $paginatedResponse->lastPage() ? $page + 1 : null,
            'prev_page' => $page > 1 ? $page - 1 : null,
        ]);
    }

    public function getFeed(Request $request, $id){
        $feed = $this->feeds()->findOrFail($id);
        return response()->json($feed);
    }

    public function addFeed(Request $request){
        $this->validate($request, [
            'url' => 'required|active_url|max:180',
            'topics' => 'array',
            'topics.*' => 'integer|exists:curation_topics,id',
            'team_id' => 'integer|exists:teams,id',
        ]);

        // validate url using simple pie
        $simplePie = new \SimplePie();
        $simplePie->set_feed_url($request->input('url'));
        $simplePie->set_timeout(30);
        try {
            $simplePie->init();
        } catch (\Exception $e) {
            throw ValidationException::withMessages([
                'url' => 'Invalid feed url',
            ]);
        }

        if($simplePie->error()){
            throw ValidationException::withMessages([
                'url' => $simplePie->error(),
            ]);
        }

        // validate team id
        if($request->has('team_id')){
            // get all available teams where user can create feed
            $teamIds = user()->joinedTeams->filter(function(/** @var $team Team */ $team){
                return $team->hasPermission(user(), 'curation.add_feed');
            })->map(function (/** @var $team Team */ $team) {
                return $team->id;
            })->toArray();

            // check if team id is in the list
            if(!in_array($request->input('team_id'), $teamIds)) {
                throw ValidationException::withMessages([
                    'team_id' => 'Invalid team id',
                ]);
            }
        }

        $feed = new CurationFeed();
        $feed->url = $request->input('url');
        $feed->user_id = user()->id;
        $feed->team_id = $request->input('team_id');
        $feed->save();

        if($request->has('topics')){
            $feed->topics()->sync($request->input('topics'));
        }

        return response()->json($feed);
    }

    /**
     * Get query for user or team added feeds
     * @return Builder
     */
    private function feeds(){
        $teamIds = user()->joinedTeams->filter(function(/** @var $team Team */ $team){
            if(!isset(Team::$allPermissions['curation.view_feeds'])){
                return false; // no permission defined yet
            }
            return $team->hasPermission(user(), 'curation.view_feeds');
        })->map(function (/** @var $team Team */ $team) {
            return $team->id;
        })->toArray();
        return CurationFeed::query()
            ->where(function(/** @var $query Builder */ $query) use($teamIds){
                // only show for teams where user has permission or where user added the feed
                return $query
                    ->whereIn('team_id', $teamIds)
                    ->orWhere('user_id', user()->id);
            });
    }

    /**
     * Get query for all available items
     * @return Builder
     */
    private function items(){
        return CurationItem::query()
            ->whereHas('feed', function(/** @var $query Builder */ $query){
                return $query
                    ->whereIn('feed.id', $this->feeds()->pluck('id')) // only show items from feeds user has access to
                    ->orWhere(function (/** @var $query Builder */ $query){ // or from global feeds
                        // ensure it's a global feed
                        return $query
                            ->where('curation_feeds.team_id', null)
                            ->where('curation_feeds.user_id', null);
                    });
            })
            ->orderBy('score', 'desc') // order by score
            ->orderBy('published_at', 'desc'); // order by published date
    }
}
