<template>
    <div class="row">
        <div class="col"
             v-if="!filter.network">
            <div class="mt-6 d-flex align-items-baseline justify-content-center">
                Please select a social network to see its performance.
            </div>
        </div>
        <div class="col"
             v-else-if="filteredAccounts.length === 0">
            <div class="mt-6 d-flex align-items-baseline justify-content-center">
                No accounts found for this social network type.
            </div>
        </div>
        <div class="col" v-else>
            <InstagramPerformance
                :filter="filter"
                :filterFormOpen="filterFormOpen"
                :accounts="filteredAccounts"
                ref="reportComponent"
                v-if="filter.network === 'instagram.api'"
            />
            <FacebookPerformance
                :filter="filter"
                :filterFormOpen="filterFormOpen"
                :accounts="filteredAccounts"
                ref="reportComponent"
                v-else-if="filter.network === 'facebook.page'"
            />
            <LinkedInPerformance
                :filter="filter"
                :filterFormOpen="filterFormOpen"
                :accounts="filteredAccounts"
                ref="reportComponent"
                v-else-if="filter.network === 'linkedin.org' || filter.network === 'linkedin.brand'"
            />
            <TwitterPerformance
                :filter="filter"
                :filterFormOpen="filterFormOpen"
                :accounts="filteredAccounts"
                ref="reportComponent"
                v-else-if="filter.network === 'twitter.profile'"
            />
            <GMBPerformance
                :filter="filter"
                :filterFormOpen="filterFormOpen"
                :accounts="filteredAccounts"
                ref="reportComponent"
                v-else-if="filter.network === 'google.location'"
            />
            <PinterestPerformance 
                :filter="filter"
                :filterFormOpen="filterFormOpen"
                :accounts="filteredAccounts"
                ref="reportComponent"
                v-else-if="filter.network === 'pinterest.profile'"
            />
        </div>
    </div>
</template>

<script>
import InstagramPerformance from './NetworkPerformance/InstagramPerformance.vue'
import FacebookPerformance from './NetworkPerformance/FacebookPerformance.vue'
import LinkedInPerformance from './NetworkPerformance/LinkedInPerformance.vue'
import TwitterPerformance from './NetworkPerformance/TwitterPerformance.vue'
import GMBPerformance from './NetworkPerformance/GMBPerformance.vue'
import PinterestPerformance from './NetworkPerformance/PinterestPerformance.vue'

export default {
    name: 'NetworkPerformance',
    props: ['filter', 'accounts', 'filterFormOpen'],
    components: {
        InstagramPerformance,
        FacebookPerformance,
        LinkedInPerformance,
        TwitterPerformance,
        GMBPerformance,
        PinterestPerformance
    },
    data() {
        return {
            loading: false
        }
    },
    computed: {
        filteredAccounts(){
            // filter accounts by network
            return this.accounts.filter(account => account.type === this.filter.network);
        },
        firstNetwork(){
            const firstAcc = this.accounts.filter(a => {
                return [
                    "instagram.api",
                    "facebook.page",
                    "twitter.profile",
                    "linkedin.org",
                    "linkedin.brand",
                    "google.location",
                    "pinterest.profile",
                ].includes(a.type);
            }).find(a => a.type); // find first account
            return firstAcc ? firstAcc.type : null; // return first account type
        }
    },
    methods: {
        fetchData() {
            if(!this.filter.network || this.filteredAccounts.length === 0) {
                this.$bus.$emit('open_filter_form');
                return;
            }
            this.$refs.reportComponent && this.$refs.reportComponent.fetchData();
        }
    },
    mounted() {
        this.fetchData();
        // Right now we not need to auto select first network
        // if(!this.filter.network && this.firstNetwork) {
        //     // auto select the first available network
        //     const newFilter = { ... this.filter};
        //     newFilter.network = this.firstNetwork;
        //     this.$bus.$emit('update_filter', newFilter);
        // }
    }
}
</script>
