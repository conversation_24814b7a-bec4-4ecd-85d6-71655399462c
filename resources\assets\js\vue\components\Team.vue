<template>
    <div id="team_modal" class="modal fade" tabindex="-1" ref="modal">
        <div class="modal-dialog modal-dialog-slideout bottom modal-lg">
            <div class="modal-content p-md-6 px-4 py-20">
                <div class="modal-header pt-0 pb-5 px-md-6 px-0">
                    <h5 class="modal-title font-weight-500" v-if="type === 'add'">
                        Create New Team
                    </h5>
                    <h5 class="modal-title font-weight-500" v-if="type === 'edit' && loading">
                        View team
                    </h5>
                    <template v-else-if="type === 'edit' && !loading && id">
                        <h5 class="modal-title font-weight-500">
                            {{ name }} <small>(Created by: {{ user.name }})</small>
                        </h5>
                    </template>
                    <button type="button" data-dismiss="modal" class="close"><i class="ph ph-x ph-md"></i></button>
                </div>
                <div class="modal-body px-md-6 px-0">
                    <div class="row" v-if="type">
                        <div class="col-12 text-center" v-if="loading" v-html="spinnerHtml"></div>
                        <div class="col-12 text-center" v-else-if="type === 'edit' && !loading && !id">
                            Unable to load data for this team. Please try again.
                        </div>
                        <template v-if="!loading && (type === 'add' || (type === 'edit' && id))">
                            <div class="col-12 mb-6" v-if="type === 'add' || isAdmin">
                                <div>
                                    <h5 class="font-weight-500 mb-3">
                                        Settings
                                    </h5>
                                    <label class="font-weight-500" for="teamNameInput">Team Name</label>
                                    <input title="Team name" id="teamNameInput" type="text" placeholder="Enter your team name" class="form-control mb-1" aria-describedby="teamNameInputHelp"
                                        v-model="name" />
                                    <small id="teamNameInputHelp" class="small-2 mb-0">Example: Your Brand Name</small>
                                </div>

                                

                                <div class="col-12 form-check mt-5">
                                    <input type="checkbox" class="form-check-input" id="enableContentApprovalCheckbox"
                                        v-model="requires_content_approval" :disabled="!isAdmin && type === 'edit'" />
                                    <label class="form-check-label" for="enableContentApprovalCheckbox">
                                        Enable Content Approval
                                    </label>
                                </div>
                                <div class="col-12 pl-0">
                                    <p class="small-2">
                                        If enabled, the content will only be published once approved. If it is not enabled, the content will not require any approval before getting published.
                                    </p>
                                </div>
                            </div>

                            <div class="col-12 mb-6 mt-2">
                                <h5 class="font-weight-500 mb-3">
                                    Social Accounts
                                </h5>
                                <div class="table-responsive border rounded-lg">
                                    <table class="table table-hover mb-0">
                                        <thead class="card-header">
                                            <tr>
                                                <th scope="col" class="py-3 pl-5">Name</th>
                                                <th scope="col" class="py-3">Type</th>
                                                <th scope="col" class="py-3">Status</th>
                                                <th scope="col" class="py-3 pr-5"></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="(account, i) in accounts" :key="i + 'team_acc_'">
                                                <td class="pl-5 py-20">
                                                    <div class="d-md-block d-flex align-items-center justify-content-between">

                                                        <div class="d-md-block d-flex">
                                                            <div class="position-relative">
                                                                <img :src="account.image" class="account-profile position-relative mr-1"  alt="account image"/>
                                                                <span class="d-md-inline-block d-none">{{account.name.length > 22 ? account.name.slice(0, 22) + '...' : account.name }}</span>
                                                                <img 
                                                                    class="d-md-none rounded-circle border border-white network-icon position-absolute"
                                                                    alt="account type"
                                                                    :src="getIconForAccount(account.type, 'circular')"
                                                                    :title="account._type" v-tooltip />
                                                            </div>
                                                            <div class="d-md-none ml-3">
                                                                <h6 class="font-weight-500 mb-1">{{account.name.length > 22 ? account.name.slice(0, 22) + '...' : account.name }}</h6>
                                                                <p class="mb-0">{{ account._type }}</p>
                                                            </div>
                                                        </div>
                                                        <div class="d-md-none">
                                                            <i class="ph ph-md mr-2"  :class="[ account.active ? 'ph-check text-success' : 'ph-x text-danger' ]"></i>
                                                        </div>
                                                    </div>
                                                    <div class="d-md-none mt-3">
                                                        <button class="btn btn-sm btn-light" title="Remove Account"
                                                            v-tooltip @click="accounts.splice(i, 1)" v-if="type === 'add' || (type === 'edit' && isAdmin)">
                                                            Remove
                                                        </button>
                                                    </div>
                                                </td>
                                                <td class="d-md-table-cell d-none p-20">
                                                    <p class="mb-0">
                                                        <img class="position-relative network-icon mr-1" :src="getIconForAccount(account.type, 'circular')">
                                                        <span>{{ account._type }}</span>
                                                    </p>
                                                </td>
                                                <td class="d-md-table-cell d-none p-20">
                                                    <i class="ph ph-md mr-2"  :class="[ account.active ? 'ph-check text-success' : 'ph-x text-danger' ]"></i> <span class="d-md-inline-block d-none">{{ account.active ? 'Connected' : 'Disconnected'}}</span>
                                                </td>
                                                <td class="d-md-table-cell d-none pr-5 py-20">
                                                    <button class="btn btn-sm btn-light" title="Remove Account"
                                                        v-tooltip @click="accounts.splice(i, 1)" v-if="type === 'add' || (type === 'edit' && isAdmin)">
                                                        Remove
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="mt-4" v-if="type === 'add' || (type === 'edit' && isAdmin)">
                                    <select  class="form-control input_team_accounts" name="accounts[]" multiple="multiple" id="input_team_accounts2" title="Accounts"
                                        v-select2="select2Configs.accounts"></select>
                                    <p class="small-2">
                                        You can only add active accounts to your team.
                                    </p>
                                </div>
                            </div>

                            <div class="col-12 mt-2">
                                <h5 class="font-weight-500 mb-3">
                                    Members and Access
                                </h5>
                                
                                <div class="list-group list-group-flush border border-light rounded-md mb-4" v-if="members.length"
                                    v-tour:creating_team__members.bottom="'Your team members can be invited by email. <br/><br/>If a team member does not have a SocialBu account, an account will be automatically created for them. <br/><br/>Your team members get access after they accept the invite.'">
                                    <div class="list-group-item" v-for="(member, index) in members">
                                        <div class="d-flex justify-content-between align-items-center flex-wrap">
                                            <div>
                                                <div class="d-flex">
                                                    <div class="account">
                                                        <img class="avatar avatar-xs" alt="avatar" :src="member.image || '/images/no-image.png'" />
                                                    </div>
                                                    <div class="ml-3 line-height-1">
                                                        <h6 class="font-weight-500 mb-0">
                                                            {{ truncate(member.name, 20) }}
                                                            <template v-if="member.id === userId">
                                                                (you)
                                                            </template>
                                                            <template v-else-if="!member.image">
                                                                (invite)
                                                            </template>
                                                            <template v-else-if="member.image && !member.approved">
                                                                (pending invite)
                                                            </template>
                                                        </h6>
                                                        <small class="text-muted small-2">
                                                            {{ member.email }}
                                                        </small>
                                                    </div>
                                                </div>
                                                <div></div>
                                            </div>
                                            <div class="mt-md-0 mt-3">
                                                <span class="badge badge-warning" v-if="!member.permissions.length">No permission selected</span>
                                                <button class="btn btn-sm btn-light border-light" title="Toggle Permissions"
                                                    @click.prevent="togglePermissions(member.email)" v-tooltip>
                                                    <i class="ph" :class="{'ph-caret-up': showPermsFor.includes(member.email), 'ph-caret-down': !showPermsFor.includes(member.email) }"></i>
                                                    Permissions
                                                </button>
                                                <button class="btn btn-sm btn-light border-light" title="Remove Member"
                                                    v-tooltip @click="members.splice(index, 1)" v-if="type === 'add' || (type === 'edit' && isAdmin && member.id !== user.id)">
                                                    Remove
                                                </button>
                                            </div>
                                        </div>
                                        <div class="p-4 my-md-2" v-show="showPermsFor.includes(member.email)">
                                            <div class="row">
                                                <div class="col-12 col-md-8 order-1 order-md-0">
                                                    <div class="row">
                                                        <div class="col-12" v-for="group in permissionsByGroup">
                                                            <h6 class="cursor-pointer text-capitalize mb-3 font-weight-500" @click.prevent="togglePermissions(member.email + group.title)">
                                                                <i class="ph" :class="{'ph-caret-up': showPermsFor.includes(member.email + group.title), 'ph-caret-down': !showPermsFor.includes(member.email + group.title) }"></i>
                                                                {{ group.title.replace(/_/g, ' ') }}
                                                            </h6>

                                                            <div :class="{'d-block': showPermsFor.includes(member.email + group.title), 'd-none': !showPermsFor.includes(member.email + group.title)}">
                                                                <div class="form-check d-flex align-items-center mb-3" v-for="perm in group.permissions">
                                                                    <input type="checkbox" class="form-check-input mb-1 cursor-pointer"
                                                                        @change="onPermissionChange($event, member.id)" :value="perm.permission" :checked="member.permissions.includes(perm.permission)" :id="'permInput' + member.email + '_' + perm.permission" :title="perm.title" :disabled="type === 'edit' && ((member.id === user.id && perm.permission === 'team.edit') || !isAdmin)" />
                                                                    <label class="form-check-label cursor-pointer mb-1" :for="'permInput' + member.email + '_' + perm.permission">
                                                                        {{  perm.title }}
                                                                    </label>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-12 col-md-4 text-md-right order-0 order-md-1 px-0 mb-md-0 mb-3">
                                                    <button class="btn btn-sm btn-outline-light text-muted"
                                                        @click="member.permissions.length < totalPermissions ? member.permissions = Object.keys(config.permissions) : member.permissions = []">
                                                        Selected:
                                                        <template v-if="member.permissions.length === totalPermissions">
                                                            All
                                                        </template>
                                                        <template v-else>
                                                            {{ member.permissions.length }}/{{ totalPermissions }}
                                                        </template>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div
                                    v-if="type === 'add' || (type === 'edit' && isAdmin)">
                                    <select  class="form-control input_team_members" multiple="multiple" id="input_team_members2" title="Team Members"
                                        v-select2="select2Configs.members"
                                    ></select>
                                    <p class="small-2">
                                        Enter the email of a user to invite
                                    </p>
                                </div>
                            </div>

                            <div class="col-12" v-if="isAdmin && logs.length">
                                <h5 class="font-weight-500">
                                    Logs
                                </h5>
                                <div class="card border border-light logs-card">
                                    <div class="card-body p-md-6 p-0">
                                        <div class="list-group list-group-flush">
                                            <div class="list-group-item border-light  px-md-5 px-0 py-md-4 py-0"
                                                 v-for="log in logs">
                                                {{ log.message }} • {{ log.timeago }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </template>
                    </div>
                </div>
                <div class="modal-footer d-flex justify-content-between" v-if="type && !loading">
                    <div>
                        <button type="button" class="btn  btn-outline-danger"
                            @click.prevent="deleteTeam"
                            v-if="type === 'edit' && user && isAdmin"> 
                            Delete
                        </button>
                        <button type="button" class="btn  btn-outline-danger"
                            @click.prevent="leaveTeam"
                            v-else-if="type === 'edit'"> 
                            Leave
                        </button>
                    </div>
                    <div>
                        <button type="button" class="btn  btn-light" data-dismiss="modal">
                            Close
                        </button>
                        <button type="button" class="btn  btn-primary"
                            @click="save"
                            v-if="type === 'add' || (type === 'edit' && isAdmin)">
                            Save
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import $ from "jquery";
import { axios, appConfig, alertify, getIconForAccount, axiosErrorHandler, spinnerHtml } from "../../components";
import { truncate, upperFirst } from "lodash";

export default {
    name: "Team",
    data() {
        return {
            id: null,
            name: "",
            user: null,

            accounts: [],
            members: [],

            requires_content_approval: false,

            logs: [], // some recent logs

            // non-standard fields
            loading: false,
            type: null,
            showPermsFor: [],
            config: {
                permissions: {} // permission ->
            }
        };
    },
    computed: {
        userId() {
            return appConfig.userId;
        },
        spinnerHtml: () => spinnerHtml,
        select2Configs() {
            return {
                accounts: {
                    config: {
                        placeholder: this.$lang.get("generic.type_to_search"),
                        ajax: {
                            url: "/app/json/search/accounts",
                            dataType: "json",
                            delay: 250,
                            data: function(params) {
                                return {
                                    q: params.term // search term
                                };
                            },
                            processResults: (data, params) => {
                                params.page = params.page || 1;
                                const aa = [];
                                data.items.forEach((item, index) => {
                                    item.text = item.name;
                                    item.value = item.id;
                                    if (!this.accounts.find(a => a.id === item.id)) {
                                        aa.push(item);
                                    }
                                });

                                return {
                                    results: aa
                                };
                            },
                            cache: true
                        },
                        minimumInputLength: 1,

                        templateResult: function(item) {
                            if(item.type){
                                return $(
                                    `<div class="select2-item">
                                        <img src="${getIconForAccount(item.type, 'circular')}" alt="${item.text}" class="rounded-circle position-relative network-icon mr-2" />
                                        <span>${item.text}</span>
                                    </div>`
                                );
                            }else {
                                return $(
                                    `<div class="select2-item">
                                        <i class="ph ph-magnifying-glass"></i>
                                        <span>${item.text}</span>
                                    </div>`
                                );
                            }
                        },
                    },
                    onSelecting: (d, el) => {
                        const account = d.params.args.data;
                        this.accounts.push(account);
                        $(el)
                            .select2("close")
                            .val(null);
                        return false;
                    }
                },
                members: {
                    config: {
                        placeholder: this.$lang.get("generic.type_to_search"),
                        ajax: {
                            url: "/app/json/search/member",
                            dataType: "json",
                            delay: 250,
                            data: function(params) {
                                return {
                                    q: params.term // search term
                                };
                            },
                            processResults: (data, params) => {
                                params.page = params.page || 1;

                                const aa = [];
                                data.items.forEach((item, index) => {
                                    item.text = item.name;
                                    item.value = item.id;
                                    if (!this.members.find(m => m.id === item.id)) {
                                        aa.push(item);
                                    }
                                });

                                return {
                                    results: aa
                                };
                            },
                            cache: true
                        },
                        minimumInputLength: 1
                    },
                    onSelecting: (d, el) => {
                        const member = d.params.args.data;
                        member.permissions = [];
                        if (!member.email) member.email = member.id;
                        this.members.push(member);

                        // show perms for this member so user can set them
                        this.togglePermissions(member.email);

                        $(el)
                            .select2("close")
                            .val(null);
                        return false;
                    }
                }
            };
        },
        permissionsByGroup(){
            // convert config.permissions into groups so we can display in ui
            const perms = Object.keys(this.config.permissions);
            const groupsByType = {};
            perms.forEach(perm => {
                const type = perm.split(".")[0];
                if(!groupsByType[type]) {
                    groupsByType[type] = groupsByType[type] || {
                        title: upperFirst(type),
                        permissions: []
                    };
                }
                groupsByType[type].permissions.push({
                    title: this.config.permissions[perm],
                    permission: perm
                });
            });
            return Object.values(groupsByType);
        },
        totalPermissions(){
            return Object.keys(this.config.permissions).length;
        },
        isAdmin(){
            // if curr user is admin
            return this.members.some(m => m.permissions.includes('team.edit') && m.id === this.userId);
        }
    },
    methods: {
        truncate,
        getIconForAccount,
        setConfig(config) {
            this.config.permissions = config.permissions;
        },
        togglePermissions(email) {
            const index = this.showPermsFor.indexOf(email);
            if (index > -1) {
                this.showPermsFor.splice(index, 1);
            } else {
                this.showPermsFor.push(email);
            }
        },
        onPermissionChange(event, memberId) {
            this.members = this.members.map(m => {
                if(m.id === memberId){
                    const permIndex = m.permissions.indexOf(event.target.value);
                    if (permIndex > -1) {
                        m.permissions.splice(permIndex, 1);
                    } else {
                        m.permissions.push(event.target.value);
                    }
                }
                return m;
            });
        },
        async loadTeam(id) {
            this.loading = true;
            try {
                const res = await axios.get("/app/json/data/team/" + id);
                const team = res.data;

                this.id = team.id;
                this.name = team.name;
                this.user = team.user;

                this.accounts = team.accounts;
                this.members = team.members;

                this.requires_content_approval = team.requires_content_approval;

                this.logs = team.logs;

            } catch (e) {
                console.error(e);
                alert("Unable to load details: " + e.message);
            }
            this.loading = false;
        },
        async save() {
            this.loading = true;
            try {
                const { data } = await axios[this.id ? 'patch' : 'post'](this.id ? "/app/teams/" + this.id : "/app/teams/", {
                    name: this.name,
                    accounts: this.accounts,
                    members: this.members,
                    requires_content_approval: this.requires_content_approval
                });
                if (data.success) {
                    document.location.reload();
                } else if (data.redirect) {
                    document.location.href = data.redirect;
                }
                // no need to hide loading anim coz we redirect
            } catch (e) {
                axiosErrorHandler(e);
                this.loading = false; // hide the loading anim
            }
        },
        async deleteTeam() {
            alertify.delete(
                "Are you sure you want to delete this team?",
                () => {
                    // delete
                    axios
                        .delete("/app/teams/" + this.id)
                        .then(() => {
                            document.location.reload();
                        })
                        .catch(e => {
                            alert(e);
                        });
                }
            );
            return false;
        },
        async leaveTeam() {
            alertify.confirm("Are you sure?", () => {
                // leave
                axios
                    .delete("/app/teams/" + this.id)
                    .then(() => {
                        document.location.reload();
                    })
                    .catch(e => {
                        alert(e);
                    });
            });
            return false;
        }
    },
    async mounted() {
        this.loading = false;
        const modal = this.$refs.modal;
        const $modal = $(modal);
        $modal
            .on("shown.bs.modal", event => {
                const $button = $(event.relatedTarget);
                const id = $button.data("id");
                if (id) {
                    this.type = "edit";
                    this.loadTeam(id);
                } else {
                    this.type = "add";
                }
                window.__recordUsageEvent("team_" + this.type);
            })
            .on("hidden.bs.modal", () => {
                this.type = null;
                this.accounts = [];
                this.members = [];

                this.id = null;
                this.name = null;
                this.user = null;

                window.__recordUsageEvent("team_close");
            });

    }
};
</script>

<style lang="scss" scoped>
    @media (max-width: 600px){

        .card-header{
            display: none;
        }
        .account-profile{
            width: 40px !important;
            height: 40px !important;
            border-radius: 50%;
        }
        .network-icon{
            top: 24px;
            left: 24px;
        }
        .logs-card{
            border: none !important;
            .list-group-item{
                border: none !important;
            }
        }
    }
    .account-profile{
        width: 32px;
        height: 32px;
        .network-icon{
            width: 20px;
            height: 20px;
        }
    }
</style>
