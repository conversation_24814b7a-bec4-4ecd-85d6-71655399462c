// core scss
@import "new-bootstrap";


// add bootstrap select
@import "~bootstrap-select/dist/css/bootstrap-select";
// custom scss for it
@import "bootstrap-select";

// select2
@import "~select2/src/scss/core";
@import "~select2-theme-bootstrap4/dist/select2-bootstrap.min.css";
@import "select2"; // custom css for select2

// datetimepicker
@import "~tempusdominus-bootstrap-4/src/sass/tempusdominus-bootstrap-4";

// alertify css
@import "~alertifyjs/build/css/alertify";
@import "~alertifyjs/build/css/themes/bootstrap";
@import "alertify";

// sortablejs
@import "sortable";

// tour
@import "tour";
// highlight textarea
@import "~highlight-within-textarea/jquery.highlight-within-textarea.css";

.hwt-container {
    display: block;
    .hwt-backdrop {
        padding: 6px 12px;
        right: 0 !important;
        padding-right: 12px !important;
    } 
}

// prosemirror editor
@import "~prosemirror-view/style/prosemirror.css";
.ProseMirror .placeholder {
    color: #aaa;
    pointer-events: none;
    height: 0;
}
.ProseMirror p{
    margin-bottom: 0px !important;
}

.ProseMirror:focus .placeholder {
    display: none;
}

.ProseMirror[contenteditable] {
    outline: 0 solid transparent;
    height: 100%;
}

html {
    position: relative;
    min-height: 100%;
}

.hover-zoom {
    transition: transform 0.2s; /* Animation */
    &:hover {
        transform: scale(1.2); /* (150% zoom - Note: if the zoom is too large, it will go outside of the viewport) */
    }
}

/* simple icons middle position fix */
.si {
    vertical-align: middle;
}
.referral-content-box{
    width: 40px; 
    height:40px;
    border-radius: 4px;
    i {
        top: 8px;
        left: 8px;
        right: 8px;
        bottom: 8px;
        text-align: center;
        font-size: 20px;
        line-height: 21px;
        color: #4A5465;
    }
}
.referral_card_bg{
    background: #FAFBFF;
}
.readonly{
    background-color: #fff !important;
}
.win-card{
    height: 160px;
}
.win-box{
    background: #FEF3CC;
}
.referral-box{
    background:#D7ECFF;
}

[role="main"] {
    // min-height: 100vh;
    padding-top: 60px; /* Space for fixed navbar */
    padding-bottom: 16px;
    margin-left: $app-sidebar-width;
    margin-bottom: 20px; /* Margin bottom by footer height + margin-bottom normal */
    // margin-bottom: 40px; /* Margin bottom by footer height + margin-bottom normal */
    //min-height: 1024px; // so it looks good with footer
    &.has-sidebar-toggle {
        margin-left: 0;
    }
}
.main-content{
    min-height: 83vh;
}

.position-sticky-top-fix {
    top: 55px;
    z-index: 8;
}
/* sticky footer */
.footer {
    position: absolute;
    bottom: 0;
    z-index: 102;
    width: 100%;
    height: $app-footer-height; /* Set the fixed height of the footer here */
}


// accounts selector: editor and story publisher, and somewhere else (accounts-container)
// accounts selector: editor and story publisher, and somewhere else (accounts-container)
.accounts-selector,
.accounts-container {
    min-height: 66px;
    &.invisible {
        visibility: hidden;
    }

    .add-account {
        padding: 8px !important;
        border-radius: 20px;
        background: rgba(16, 63, 152, 0.06);
        color: #eeeeee;
    }

    .add-account:hover {
        color: #636b6f;
    }

    .account-pic {
        height: 40px;
        width: 40px;
    }
    .account-pic img {
        border-radius: 50%;
        height: 40px;
        width: 40px;
        cursor: pointer;
    }
    .account-pic img.network-icon {
        position: absolute;
        width: 20px;
        height: 20px;
        top: 21px;
        left: 21px;
    }

    .account-pic input[type="checkbox"] {
        display: none;
    }

    .account-pic label {
        display: block;
        position: relative;
        cursor: pointer;
    }

    .account-pic label:before {
        display: block;
        position: absolute;
        top: 25px;
        left: 30px;
        font-size: 8px;
        transition-duration: 0.4s;
        transform: scale(0);
    }

    .account-pic label img {
        transition-duration: 0.2s;
        transform-origin: 50% 50%;
    }

    .account-pic label .profile-pic {
        opacity: 0.4;
    }
    .account-pic :checked + label .profile-pic {
        opacity: 1;
    }
    .show-more{
        width: 36px; 
        height: 36px;
        color: #0557f0;
        border-radius: 50%;
        &.expand-icon {
            transform: rotate(140deg);
        }
    }
}

.bootstrap-select .dropdown-menu li a.opt{
    padding: 8px 12px;
}
.bootstrap-select.show-tick .dropdown-menu .selected span.check-mark {
    top: 10px;
}
.bootstrap-select .dropdown-toggle:focus{
    outline: none !important;
}

.input-seamless {
    border: none !important;
    background-color: transparent !important;
    box-shadow: none !important;
}

.input-seamless.h3 {
    font-size: 24px;
}

.chat_button{
    padding-bottom: 20px !important;
}
.chat-success-icon{
    position: absolute !important;
    right: 14px;
    top: -6px;
}
a {
    line-height: normal;
}
.show-inactive{
    width: 16px;
    height: 16px;
}
.border-radius-right{
    border-bottom-right-radius: 8px !important;
    border-top-right-radius: 8px !important;
}
.font-size-18{
    font-size: 18px !important;
}
@media (max-width: 776px){
    h3{
        font-size: 24px !important;
    }
    h4{
        font-size: 20px !important;
    }
    .ajs-modal{
        padding: 0px !important;
        bottom: 0 !important;
        top: auto !important;
        overflow-y: hidden !important;
        .ajs-dialog{
            margin-bottom: 0px !important;
            border-top-left-radius: 16px;
            border-top-right-radius: 16px;
            border-bottom-left-radius: 0px;
            border-bottom-right-radius: 0px;
        }
        .ajs-header{
            padding-top: 20px;
            padding-bottom: 24px;
        }
        .ajs-content{
            padding-bottom: 6px;
        }
        .ajs-footer{
            padding-top: 40px;
            padding-bottom: 0px;
        }
        .ajs-buttons{
            display: flex;
            justify-content: space-between;
        }
    }
}
.analyze-card{
    width: 262px;
    min-width: 262px;
}
h1, h2, h3, h4, h5, h6,.h1, .h2, .h3, .h4, .h5, .h6 {
    font-family: 'Plus Jakarta Sans';
}
@media (max-width: 776px){
    h3{
        font-size: 24px !important;
    }
    h4{
        font-size: 20px !important;
    }
    .hide-scrollbar{
        scrollbar-width: none;
    }
}
.app-container{
    overflow: hidden;
    .reports{
        h3{
            letter-spacing: -0.14px;
        }
        .card,.highcharts-container{
            border-radius: 1rem;
        }
        .highcharts-axis-title{
            fill: rgba(70, 81, 102, 1) !important;
            font-size: 14px;
            font-family: 'DM Sans';
        }
        .highcharts-axis-labels{
            text{
                fill: rgba(101, 112, 133, 1) !important;
                font-size: 14px !important;
                font-family: "DM Sans";
            }
        
        }
        .highcharts-area-series, .highcharts-no-data{
            text{
                font-size: 16px !important;
                fill: rgba(70, 81, 102, 1) !important;
                font-family: "DM Sans";
        
            }
        }
        .highcharts-no-data{
            text{
                font-weight: 400 !important;
            }
        }
        .highcharts-pie-series{
            text{
                font-size: 16px !important;
                fill: rgba(70, 81, 102, 1) !important;
                font-family: "DM Sans";
            }
        }
        
        .highcharts-menu{
            padding: 0.5rem !important;
            border-radius: 16px !important;
            border: 1px solid #F2F4F7 !important;
            background: #FFF !important;
            box-shadow: 0px 8px 20px -4px rgba(29, 33, 41, 0.08) !important;
        
            .highcharts-menu-item {
                font-size: 16px !important;
                color: #353F54 !important;
                font-family: "DM Sans" !important;
        
            }
            .highcharts-menu-item:hover{
                border-radius: 8px !important;
                background: #F1F3F5 !important;
            }
        }
    }
}
.list-styles {
    list-style: none; /* Remove default bullets */
    li{
        padding: 4px !important;
        display:flex;
        align-items: center;
    }
    li::before {
        content: "•";
        color: $gray-700; /* or whatever color you prefer */
        font-size: 14px;
        padding-right: 8px;
    }
}
.alert-dismissible{
    .close{
        padding: 0.5rem 0.75rem;
    }
}