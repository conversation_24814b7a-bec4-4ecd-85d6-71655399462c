<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class MonitorOurLiveChat extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'monitor:live_chat';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor our live chat and notify us if its inactive';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $CACHE_KEY = 'monitor.live_chat.state';
        $currState = \Cache::get($CACHE_KEY);
        if($currState === null){
            $currState = [
                'is_active' => true,
                'timestamp' => time(),
            ]; // assume state
        }

        $guzzle = guzzle_client([
            'headers' => [
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
            ],
            'connect_timeout' => 30,
            'read_timeout' => 30,
            'timeout' => 60,
        ]);

        try {
            // check current state
            $response = $guzzle->get('https://chatapi.helpscout.net/v2/beacon/9c24072e-ae33-4dfc-987e-90ea6ec2aede/agents?deviceId=afd5836f-6627-4a88-8bc2-46039d6ba76d'); // not sure why deviceId is passed

            $this->info('Response from HelpScout: ' . $response->getBody());

            $json = json_decode($response->getBody());

            $activeAgents = count($json->agents);

            if ($activeAgents === 0 && $currState['is_active'] === true) {
                // now off but was on
                notify_chat('🔌 Live chat is now inactive', 'livechat_monitor');
                $currState['is_active'] = false;
                $currState['timestamp'] = time();
                \Cache::put($CACHE_KEY, $currState, now()->addWeeks(1));
            } else if ($activeAgents > 0 && $currState['is_active'] === false) {
                notify_chat('⚡ Live chat is now active', 'livechat_monitor');
                $currState['is_active'] = true;
                $currState['timestamp'] = time();
                \Cache::put($CACHE_KEY, $currState, now()->addWeeks(1));
            }

            $this->info('Current state: ' . json_encode($currState));
        } catch (\Exception $e) {
            $this->error('Error: ' . $e->getMessage());
        }
    }
}
