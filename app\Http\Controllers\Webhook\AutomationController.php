<?php

namespace App\Http\Controllers\Webhook;

use App\Automation;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Str;

class AutomationController extends Controller
{
    public function handle(Request $request, $public_id){
        /** @var Automation|null $automation */
        $automation = Automation::findByPublicId($public_id);

        // check if it's ok to trigger the automation
        if(!$automation || $automation->event !== 'webhook' || !$automation->active) abort(404);

        if(Str::contains($request->userAgent(), config('app.name'))){
            // request sent from our own app? no need to process it
            abort(403);
        }

        // execute it
        $data = $request->input();
        $data['request_body'] = $request->input();

        if($request->get('execute')){
            // execute it synchronously
            return $automation->execute($data, [], true);
        } else {
            // async
            $automation->execute($data);
            return [
                'triggered' => true,
                'automation' => [ // to be removed
                    'description' => $automation->description,
                ],
            ];
        }
    }
}
