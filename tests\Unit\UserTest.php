<?php

namespace Tests\Unit;

use App\User;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class UserTest extends TestCase
{
    use RefreshDatabase;

    public function testOptions()
    {
        $user = new User([
            'name' => 'test user 1',
            'email' => '<EMAIL>',
            'password' => bcrypt('test'),
            'verified' => false,
        ]);
        $user->save();
        $user->setOption('test.test', 1);
        $this->assertEquals(1, $user->getOption('test.test'));
    }

    public function testConfirmEmail()
    {
        $user = new User([
            'name' => 'test user 1',
            'email' => '<EMAIL>',
            'password' => bcrypt('test'),
            'verified' => false,
        ]);
        $user->save();
        $this->assertFalse($user->verified);
        $user->confirmEmail();
        $this->assertTrue($user->verified);
    }

    public function testTimezone()
    {
        $user = new User([
            'name' => 'test user 1',
            'email' => '<EMAIL>',
            'password' => bcrypt('test'),
            'verified' => true,
        ]);
        $user->save();
        $user->setTimezone('UTC');
        $this->assertEquals('UTC', $user->getTimezone());
    }
}
