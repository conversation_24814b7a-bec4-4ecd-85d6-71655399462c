<?php

namespace App\Http\Controllers\User;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Str;

class GenerateController extends Controller
{
    /**
     * Returns boolean for feature access for all teams and that of user [teams => array of teamIds that have access, user => boolean]
     * @return array
     */
    private function featureAvailability()
    {
        $teamsThatCanGenerateContent = user()->joinedTeams->filter(function(/** @var $team \App\Team */ $team){
            return planHasFeature('content_generation', $team);
        });
        $userAccountsCanGenerateContent = user()->planHasFeature('content_generation');
        return [
            'allowedTeams' => $teamsThatCanGenerateContent->pluck('id')->toArray(),
            'userAllowed' => $userAccountsCanGenerateContent,
            'limits' => [
                'teams' => collect($teamsThatCanGenerateContent)->mapWithKeys(function($team){
                    return [
                        $team->id => [
                            'used' => getUsage('generated_content', $team),
                            'total' => getPlanUsage('generated_content', $team),
                        ]
                    ];
                })->toArray(),
                'user' => [
                    'used' => getUsage('generated_content'),
                    'total' => getPlanUsage('generated_content'),
                ],
            ],
        ];
    }

    public function index()
    {
        return view('user.generate.index', [
            'availability' => $this->featureAvailability(),
        ]);
    }

    public function tweets(){
        return view('user.generate.tweets', [
            'availability' => $this->featureAvailability(),
        ]);
    }

    public function linkedinPosts(){
        return view('user.generate.linkedin_post', [
            'availability' => $this->featureAvailability(),
        ]);
    }

    public function instagramCaptions(){
        return view('user.generate.instagram_caption', [
            'availability' => $this->featureAvailability(),
        ]);
    }

    public function tiktokCaptions(){
        return view('user.generate.tiktok_caption', [
            'availability' => $this->featureAvailability(),
        ]);
    }
    public function facebookPosts(){
        return view('user.generate.facebook_post', [
            'availability' => $this->featureAvailability(),
        ]);
    }
    public function youtubeVideos(){
        return view('user.generate.youtube_video_description', [
            'availability' => $this->featureAvailability(),
        ]);
    }
    public function redditPosts(){
        return view('user.generate.reddit_post', [
            'availability' => $this->featureAvailability(),
        ]);
    }
    public function pinterestPins(){
        return view('user.generate.pinterest_pin', [
            'availability' => $this->featureAvailability(),
        ]);
    }
    public function googleBusinessProfilePosts(){
        return view('user.generate.google_business_profile_post', [
            'availability' => $this->featureAvailability(),
        ]);
    }
    public function mastodonPosts(){
        return view('user.generate.mastodon_post', [
            'availability' => $this->featureAvailability(),
        ]);
    }
    public function genericPosts(){
        return view('user.generate.generic', [
            'availability' => $this->featureAvailability(),
        ]);
    }

    public function test()
    {
        return view('user.generate.test', [
            'availability' => $this->featureAvailability(),
        ]);
    }

    /**
     * @param Request $request
     * @param $id
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View|void
     */
    public function dynamicForm(Request $request, $id)
    {
        try {
            $form = new \App\Generate\DynamicForm\Form($id);

            return view('user.generate.dynamic_form', [
                'id' => $id,
                'form' => [
                    'fields' => $form->getFields(),
                    'outputComponents' => $form->getOutputComponents(),
                ],
                'availability' => $this->featureAvailability(),
            ]);

        } catch (\Exception $e) {
            if(Str::contains($e->getMessage(), 'not found')){
                abort(404);
            }
        }

        abort(400, $e->getMessage());
    }
}
