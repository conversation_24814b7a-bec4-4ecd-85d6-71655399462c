<?php


namespace App\Socialite;

use <PERSON><PERSON>\Socialite\Two\AbstractProvider;
use <PERSON><PERSON>\Socialite\Two\ProviderInterface;
use <PERSON><PERSON>\Socialite\Two\User;


class LinkMngrProvider extends AbstractProvider implements ProviderInterface
{

    public static function getBaseUrl($url = null, $type = 'app'){
        $baseUrl = 'https://app.linkmngr.com/';
        if($type !== 'app'){
            $baseUrl = str_replace(['http://app.', 'https://app.'], ['http://'  . $type . '.', 'https://'  . $type . '.'], $baseUrl);
        }
        return $baseUrl . ( $url ? $url : '' );
    }

    /**
     * {@inheritdoc}
     */
    protected function getAuthUrl($state)
    {
        return $this->buildAuthUrlFromBase($this::getBaseUrl('oauth/authorize'), $state);
    }

    /**
     * {@inheritdoc}
     */
    protected function getTokenUrl()
    {
        return $this::getBaseUrl('oauth/token');
    }

    /**
     * {@inheritdoc}
     */
    protected function getTokenFields($code)
    {
        return array_merge(
            parent::getTokenFields($code), [
                'grant_type' => 'authorization_code'
            ]
        );
    }

    /**
     * {@inheritdoc}
     */
    protected function getUserByToken($token)
    {
        $response = $this->getHttpClient()->get($this::getBaseUrl('v1/auth/user', 'api'), [
            'headers' => [
                'Authorization' => 'Bearer ' . $token,
            ],
        ]);

        return json_decode($response->getBody(), true);
    }

    /**
     * {@inheritdoc}
     */
    protected function mapUserToObject(array $user)
    {
        return (new User)->setRaw($user)->map([
            'id'       => $user['id'],
            'nickname' => $user['name'],
            'name'     => $user['name'],
            'email_verified'     => $user['email_verified'],
            'default_brand_id'     => $user['default_brand_id'],
        ]);
    }

}
