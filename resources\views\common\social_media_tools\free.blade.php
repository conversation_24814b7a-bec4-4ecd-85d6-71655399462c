@extends('layout.full_width')
@section('title', count($list) . ' Best Social Media Scheduling Tools That Work for Free | ' . config('app.name'))
@push('head_html')

    <meta name="description" content="Discover 31 free social media schedulers that let you plan, automate, and track posts across all your social media platforms. "/>
    <meta property="og:locale" content="en_US" />
    <meta property="og:type" content="website" />
    <meta property="og:title" content="Social Media Scheduling Tools That Work For Free" />
    <meta property="og:description" content="Social Media Scheduling tools that you can use for free. Compare and find out the best free social media scheduling tool to use." />
    <meta property="og:url" content="https://socialbu.com/social-media-scheduling-tools/free" />
    <meta property="og:site_name" content="SocialBu" />
    <meta property="og:image" content="https://socialbu.com/images/site/link-preview.jpg" />
    <meta property="fb:app_id" content="{{ config('services.facebook.client_id') }}" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content="https://socialbu.com/images/site/link-preview.jpg" />
    <meta name="twitter:title" content="Social Media Scheduling Tools That Work For Free" />
    <meta name="twitter:description" content="Social Media Scheduling tools that you can use for free. Compare and find out the best free social media scheduling tool to use." />
    <meta name="twitter:site" content="@socialbuapp" />


    <link rel="canonical" href="https://socialbu.com/social-media-scheduling-tools/free" />
    <style>       
        .sticky-top {
            position: sticky;
            top: 35px;
            z-index: 1000;
        }
    </style>
@endpush
@section('content')
    <header class="header text-center pb-0">
        <div class="container">
            <h1 class="display-2">{{ count($list) }} Free Social Media Scheduling Tools in 2025</h1>
            <p class="lead-2 mt-6">Trying to streamline your social media without breaking the bank? Managing social media is easier than ever in 2025 with free scheduling tools. These {{ count($list) }} free social media scheduling tools let you plan, automate, and track posts across all your platforms, saving you tons of time. </p>
        </div>
    </header>

    <main class="main-content">
        <section class="section bg-light">
            <div class="container">

                <div class="row gap-y">
                    <div class="col-md-8 col-md-offset-1 tools-page pt-0">

                        @foreach($list as $i => $row)
                            <div class="card p-40 hover-shadow-7 border bg-white {{ $row['name'] === 'SocialBu' ? 'border-primary': 'border-secondary' }}{{ $i === 0 ? ' mt-0 mb-4': ' mb-4'}}">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="">
                                            <h4 class="d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <span class="mr-2">{{ $i + 1 }}.</span>
                                                    <a href="{{ $row['name'] === 'SocialBu' ? '/auth/register': '/compare/' . strtolower($row['slug']) . '-alternative' }}">
                                                        {{ $row['name'] }}
                                                    </a>
                                                    @if($row['name'] === 'SocialBu')
                                                        <span class="badge badge-warning rounded">
                                                            Best
                                                        </span>
                                                    @endif
                                                </div>
                                                <strong class="bold d-none d-sm-inline" style="font-size: 18px;">{{ $row['starting_price'] ?  $row['starting_price'] : 'N/A' }}</strong>
                                                    <strong class="bold d-inline d-sm-none" style="font-size: 16px;">
                                                        {{ $row['starting_price'] ? $row['starting_price'] : 'N/A' }}
                                                    </strong>
                                            </h4>
                                            <div class="d-flex justify-content-center">
                                                @if($row['name'] === 'SocialBu')
                                                    <a href="/auth/register" class="h-100 d-flex align-items-start">
                                                        @if(!empty($row['logo']))
                                                        <img class="d-none d-md-block lozad {{ $row['logo_is_white'] ? 'bg-dark':'' }}" style="width:{{ strtolower($row['logo_category']) === 'square' ? '100px' : '160px' }};" src="/images/1x1.gif" data-src="{{ $row['logo'] }}" alt="{{ $row['name'] }} logo">
                                                        @else
                                                            <img class="d-none d-md-block {{ $row['logo_is_white'] ? 'bg-dark':'' }}" style="width:100px" src="/images/email/spacer.gif" alt="{{ $row['name'] }}">
                                                        @endif
                                                    </a>
                                                @else
                                                    <a href="/compare/{{ $row['slug'] }}-alternative" class="h-100 d-flex align-items-start">
                                                        @if(!empty($row['logo']))
                                                        <img class="d-none d-md-block lozad {{ $row['logo_is_white'] ? 'bg-dark':'' }}"  style="width:{{ strtolower($row['logo_category']) === 'square' ? '100px' : '160px' }};" src="/images/1x1.gif" data-src="{{ $row['logo'] }}" alt="{{ $row['name'] }} logo">
                                                        @else
                                                            <img class="d-none d-md-block {{ $row['logo_is_white'] ? 'bg-dark':'' }}" style="width:{{ strtolower($row['logo_category']) === 'square' ? '100px' : '160px' }};" src="/images/email/spacer.gif" alt="{{ $row['name'] }}">
                                                        @endif
                                                    </a>
                                                @endif
                                            </div>
                                            <p class="{{ $row['name'] === 'SocialBu' ? 'lead': '' }}">
                                                @if(!empty($row['markdown']))
                                                    {!! str_replace('<strong>', '<strong class="strong">', Illuminate\Mail\Markdown::parse($row['markdown'])) !!}
                                                    <br/><br/>
                                                @endif
                                               <strong class="strong">Works with:</strong>  {{ !empty($row['networks']) ? implode(', ', $row['networks']) : 'N/A' }}<br/>
                                                <strong class="strong">Support:</strong>  {{ $row['live_chat'] ? 'Yes' : 'N/A' }}<br/>
                                            </p>
                                            <hr/>
                                            <div class="d-flex flex-column flex-md-row justify-content-between">
                                                <div>
                                                    @if(!empty($row['starting_price']))
                                                       <strong class="strong">Starting Price:</strong>  {{ $row['starting_price'] }}
                                                        @if(!empty($row['starting_price_notes']))
                                                            <i class="ph ph-info ph-fill" title="{{ implode('. ', $row['starting_price_notes']) }}" data-toggle="tooltip"></i>
                                                        @endif
                                                    @else
                                                        Paid pricing not available
                                                    @endif
                                                </div>
                                                <div>
                                                    <a href="{{ $row['name'] === 'SocialBu' ? '/auth/register': '/compare/' . strtolower($row['slug']) . '-alternative' }}" class="btn btn-round {{ $row['name'] === 'SocialBu' ? 'btn-primary': 'btn-light' }} mt-3 mt-md-0">
                                                        {{ $row['name'] === 'SocialBu' ? 'Get Started': 'Compare with SocialBu' }}
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @if(($i + 1) % 5 === 0)
                                <section class="section rounded-xl p-40 bg-primary mb-4">
                                    <div class="container">
                                        <div class="row">
                                            <div class="col-lg-4 col-12 p-0 mt-md-0 mt-5 mb-5 mb-md-0">
                                                <a href="/auth/register">
                                                    <img class="lozad rounded-xl h-100" src="/images/redesign/tools_page_card.webp">
                                                </a>
                                            </div>
                                            <div class="col-lg-8 p-0 order-md-first">
                                                <div>
                                                    <h2 class="display-3 font-weight-bold text-white">
                                                        Streamline Your Social Media Management with SocialBu!
                                                    </h2>
                                                    <div class="pt-4 d-flex flex-column flex-md-row">
                                                        <a href="/auth/register" class="btn bg-white text-primary mb-2 mb-md-0 mr-md-3" aria-label="Try for Free">
                                                            Try for Free <i class="ph ph-arrow-right ph-md" aria-hidden="true"></i>
                                                        </a>
                                                        <a class="btn btn-primary border" aria-label="Book a Demo" data-toggle="modal" data-target="#demo_modal">
                                                            Book a Demo
                                                        </a>
                                                    </div>                                                
                                                </div>
                                            </div>
                                            
                                        </div>
                                    </div>
                                </section>
                            @endif
                        @endforeach
                    </div>            
                    <section class="col-md-3 col-11 mx-4 h-100 rounded-xl p-40 bg-primary mb-4 sticky-top ml-auto d-none d-md-block">
                        <div class="row">
                            <div class="p-0">
                                <a href="#" title="Try SocialBu Free" aria-label="Try SocialBu Free">
                                    <img class="lozad rounded-xl pb-5" src="/images/redesign/tools_page_side_card.webp">
                                </a>
                                <p class="text-white font-weight-400">
                                    Create, schedule, organize, analyze, and manage all your social media accounts – All from One Dashboard
                                </p>
                                <div class="pt-4">
                                    <a href="/auth/register" class="btn text-primary bg-white w-100" aria-label="Try SocialBu Free">
                                        Try SocialBu Free <i class="ph ph-arrow-right ph-md" aria-hidden="true"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </section> 
                </div>
                <div class="d-flex justify-content-between flex-column flex-md-row py-7 py-md-8">
                    <div class="card bg-white col-12 col-md-6 mr-md-2 mb-4 mb-md-0 p-6">
                        <div class="card-body p-6">
                            <h4 class="mb-3 display-4 font-weight-800 ml-5 ml-md-0">Get 7 days for free</h4>
                            <p class="lead">
                                SocialBu gives you 7 days free trial. You can cancel anytime or downgrade to the free plan.
                            </p>
                            <div class="text-center text-md-left mt-4 w-100">
                                <a href="/auth/register" class="btn btn-primary w-100 w-md-auto" aria-label="Try for Free">
                                    Try for Free <i class="ph ph-arrow-right ph-md" aria-hidden="true"></i>
                                </a>
                            </div> 
                        </div>
                    </div>
                    <div class="card bg-white col-12 col-md-6 p-6">
                        <div class="card-body p-6">
                            <h4 class="mb-3 display-4 d-none d-md-block mb-6">Have questions? Let's talk.</h4>
                            <h4 class="display-4 d-block d-md-none mb-5 text-center">Have questions? <br>Let's talk.</h4>
                            <div class="d-flex flex-column align-items-center align-items-md-start w-100 mt-4">
                                <a class="btn btn-outline-primary w-100 w-md-auto mb-3" href="#" onclick="typeof Beacon !== 'undefined' && Beacon('open');return false;">Live Chat</a>
                                <a class="btn btn-outline-primary w-100 w-md-auto" href="mailto:<EMAIL>">Email</a>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </section>

        <!-- contact section -->
        {{-- <section class="section py-7 bg-secondary">
            <div class="container">
                <div class="row gap-y align-items-center d-block d-md-flex justify-content-between align-items-baseline">
                    <div>
                        <h4 class="mb-0 text-center text-md-left mb-4">Have questions? Let's talk.</h4>
                    </div>

                    <div class="gap-x text-center">
                        <a class="btn btn-lg btn-round btn-light border-dark text-dark" href="#" onclick="typeof Beacon !== 'undefined' && Beacon('open');return false;">Live Chat</a>
                        <a class="btn btn-lg btn-round btn-light border-dark text-dark" href="mailto:<EMAIL>">Email</a>
                    </div>
                </div>
            </div>
        </section> --}}
        
        
        <div class="container mt-5 pt-5">
            <h2 class="text-center display-4 display-md-3">Frequently Asked Questions</h2>
            <p class="text-center lead text-md-body">
                Got a question? We've got answers. If you have some other questions, contact us.
            </p>
        </div> 

        <section class="mt-4">
            <div class="d-flex justify-content-center">
                <div class="col-md-8 mt-5 pb-10">
                    <div class="container px-0">
                        @include('common.internal.tools-faq')
                    </div>
                </div>
            </div>
        </section>
    </main>
@endsection
