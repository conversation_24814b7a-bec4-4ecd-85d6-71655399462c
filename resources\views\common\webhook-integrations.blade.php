@extends('layout.full_width')
@php($title = 'Integrate and Automate Social Media Using Webhooks')
@php($description = 'Two way webhooks for your social media events and actions.')
@php($image = 'https://socialbu.com/images/site/11_2020/webhook-integrations.jpg')
@php($url = 'https://socialbu.com/webhook-integrations')
@section('title', $title . ' | ' . config('app.name'))
@push('head_html')
    <meta name="description" content="{{ $description }}"/>
    <link rel="canonical" href="{{ $url }}" />

    <meta property="og:locale" content="en_US" />
    <!--
    <meta property="og:type" content="website" />
    -->
    <meta property="og:title" content="{{ $title }}" />
    <meta property="og:description" content="{{ $description }}" />
    <meta property="og:url" content="{{ $url }}" />
    <meta property="og:site_name" content="SocialBu" />
    <meta property="og:image" content="{{ $image }}" />
    <meta property="fb:app_id" content="{{ config('services.facebook.client_id') }}" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content="{{ $image }}" />
    <meta name="twitter:title" content="{{ $title }}" />
    <meta name="twitter:description" content="{{ $description }}" />
    <meta name="twitter:site" content="@socialbuapp" />
@endpush
@section('content')

    <header class="header">
        <div class="container">
            <div class="row">
                <div class="col-12 col-md-6 py-6" style="">
                    <h1 class="font-weight-600">
                        Integrate and Automate Using Webhooks
                    </h1>
                    <p class="lead mt-5 mb-6 lead-2">
                        Two way webhooks for your social media events and actions. Integrate SocialBu with other systems that you use.
                    </p>
                    <a class="btn btn-lg btn-primary btn-round" href="/auth/register">
                        Try now
                    </a>
                </div>
                <div class="d-none d-md-block col-12 col-md-6">
                    <img src="/images/1x1.gif" data-src="/images/site/11_2020/webhook-integrations.jpg" alt="webhook integrations" class="img-responsive hover-move-up lozad" />
                </div>
            </div>
        </div>
    </header>

    <main class="main-content text-dark" id="main">
        <section class="section bg-light border-top border-bottom border-secondary">
            <div class="container">

                <div class="row gap-y align-items-center">

                    <div class="col-md-6 h-200">
                        <h2 class="h4">
                            Perform actions by receiving data
                        </h2>
                        <p>
                            With incoming webhooks, you can execute quick actions in SocialBu whenever you receive an incoming request.
                        </p>
                    </div>

                    <div class="col-md-6 h-200">
                        <h2 class="h4">
                            Send data to other systems
                        </h2>
                        <p>
                            Whenever something happens, an HTTP request with data can be sent by SocialBu to your endpoint.
                        </p>
                    </div>

                </div>
            </div>
        </section>

        @include('common.internal.testimonials-block')

        @include('common.internal.join-us-block')

    </main>

@endsection
