<script type="text/javascript">
    /* load additional css font */
    (function(){
        var loadCss = function(cssId, href){
            if (!document.getElementById(cssId))
            {
                var head  = document.getElementsByTagName('head')[0];
                var link  = document.createElement('link');
                link.id   = cssId;
                link.rel  = 'stylesheet';
                link.type = 'text/css';
                link.href = href;
                link.media = 'all';
                head.appendChild(link);
            }
        };
        loadCss('faCSS', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css');
        loadCss('mdiCSS', 'https://cdnjs.cloudflare.com/ajax/libs/MaterialDesign-Webfont/6.5.95/css/materialdesignicons.min.css');
        loadCss('siCSS', 'https://cdn.jsdelivr.net/npm/simple-icons-font@v13/font/simple-icons.min.css');

        // phosphor icons
        loadCss('ph-regular', 'https://cdn.jsdelivr.net/npm/@phosphor-icons/web@2.1.1/src/regular/style.min.css');
        loadCss('ph-bold', 'https://cdn.jsdelivr.net/npm/@phosphor-icons/web@2.1.1/src/bold/style.min.css');
        loadCss('ph-fill', 'https://cdn.jsdelivr.net/npm/@phosphor-icons/web@2.1.1/src/fill/style.min.css');
    })();
</script>
<script src="{{ mix('/js/app.js') }}"></script>
