.modal-content{
    border-radius: 1rem;
}
.modal-header {
    .close {
        font-weight: 700;
        color: $color-title;
        padding: 2rem 2rem 0rem 2rem;
    }
}
  
.modal-title {
    margin-bottom: 0;
    line-height: $modal-title-line-height;
    font-family: $font-family-title;
    font-size: 20px;
    font-weight: 500;
}
  
.modal-body {
    font-size: 16px; 
    font-weight: 400; 
    // line-height: 1.875;
    padding: 0 2rem;
}

.modal-header,
.modal-footer {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

// modal slide out from right or left
.modal {
  // bootstrap adds unexpected padding some times, on right
  padding-left: 0 !important;
  padding-right: 0 !important;
  .modal-dialog.modal-dialog-slideout {
      min-height: 100%;
      margin: 0 0 0 auto;
      transform: translate(100%, 0) !important; // slide out from right
      &.left {
          margin: 0 auto 0 0;
          transform: translate(-100%, 0) !important; // slides out from left
      }
      &.bottom {
          margin: 0 0 auto 0;
          transform: translate(0, 100%) !important; // slides out from bottom
          width: 100%;
          max-width: 100%;
      }
      &.full {
          max-width: 100%;
          width: 100%;
      }
      .modal-content {
          border: 0;
          border-radius: 0;
          height: 100%;
      }
  }
  &.show {
      .modal-dialog.modal-dialog-slideout {
          transform: translate(0, 0) !important;
          display: flex;
          align-items: stretch;
          height: 100%;
          .modal-body {
              overflow-y: auto;
              overflow-x: hidden;
          }
      }
  }
}
@media (min-width: 576px) {
    .modal-dialog.custom-modal-width{
        max-width: 470px !important;
    }
}