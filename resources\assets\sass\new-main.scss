// core scss
@import "new-bootstrap";
//@import "./../thesaas/src/assets/plugin/thesaas/scss/vendor/et-line-icon";

// alertify css
@import "~alertifyjs/build/css/alertify";
@import "~alertifyjs/build/css/themes/bootstrap";
@import "alertify";

.test-css-loaded {
    z-index: 22;
}

.carousel-indicators li,
.carousel-indicators li.active {
    width: 16px;
    height: 16px;
    margin: 1px 3px;
    border-radius: 50%;
}
.carousel-indicators li {
    background: #fff;
    border: 2px solid #999;
}
.carousel-indicators li.active {
    background: #555;
    box-shadow: inset 0 0 0 rgba(0, 0, 0, 0.2);
    border: 2px solid #555;
}

.feature-card {
    height: 176px;
    .icon-component{
        width: 72px;
        height: 72px;
    }
}
.bg-primary-light{
    background-color: #CCF1FE;
}
.bg-success-light{
    background-color: #DEFAD4; 
}
.bg-danger-light{
    background-color: #FEE3D4;
}
.bg-warning-light{
    background-color: #FEF3CC;
}
.affiliate-card{
    height: 198px;
    .affiliate-box {
        width: 72px;
        height: 70px;
    }
}

.supported-platform {
    width: 40px;
    height: 38px;
}

.platform-menu {
    .network-icon {
        width: 22px;
        height: 22px;
    }
}

.team-member-card {
    // border: 1px solid red;
    border-radius: 16px;
    & .card-header {
        border-radius: 16px;
        border-bottom: 0px;
        background: white;
    }
    .member-image {
        background-repeat: no-repeat;
        background-position: center;
        height: 300px;
        .member-info {
            bottom: -35px;
            left: 14%;
            box-shadow: 0px 4px 16px -2px rgba(37, 42, 50, 0.04);
        }
    }
}

.benefit-section {
    width: 262px;
    height: 362px;
    div {
        top: 0px;
        background: linear-gradient(180deg, rgba(29, 33, 41, 0) 50%, #1d2129 100%) !important;
    }
}

.free_tools_link_border > .nav-link.active {
    border-bottom: 2px solid transparent;
    border-bottom-color: $socialbu-color;
    
}

.list-styles {
    list-style: none; /* Remove default bullets */
    li{
        padding: 4px !important;
        display:flex;
        align-items: center;
    }
    li::before {
        content: "•";
        color: $gray-700; /* or whatever color you prefer */
        font-size: 14px;
        padding-right: 8px;
    }
}

.products-nav-dropdown{
    width: 710px;
}

.platforms-nav-dropdown{
    width: 520px;
}

@media (max-width: 450px) {
    .products-nav-dropdown{
        width: 350px;
    }
    .header{
        padding-top: 3.75rem;
        padding-bottom: 3.75rem;
    }
    .section{
        padding-top: 3.75rem;
        padding-bottom: 3.75rem;
    }
    .max-width{
        width: max-content;
    }
    .supported-platform{
        width: 24px;
        height: 24px;
    }
    .card-overflow{
        overflow: auto !important;

    }
    .benefit-mobile-image{
        width: 262px !important;
    }
    .bg-none-mobile{
        background: none !important;
    }
    .action-container{
        left: 16px !important;
        justify-content: start !important;
    }
}
/* Hide scrollbar for Chrome, Safari and Opera */
.card-overflow::-webkit-scrollbar {
    display: none;
}
  /* Hide scrollbar for IE, Edge and Firefox */
.card-overflow {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
}
.testimonial-mobile-block{
    white-space: nowrap;
    .card-component{
        display: inline-block;
        height:224px
    }
}
.chat-success-icon{
    position: absolute !important;
    right: 16px;
    top: -6px;
}

.content-button{
    right: 8px;
    top: 4px;
}
.content-display-card {
    min-height: 244px;
    .action-container {
        bottom: 16px;
        right: 16px;
    }
    .icon {
        font-size: 20px;
    }
}

.content-box{
    width: 78px;
    height: 78px;
    h2 {
        border-radius: 4px;
        background: #fff;
        box-shadow: 0px 4px 8px -1px rgba(29, 33, 41, 0.04);
        padding: 10px;
    }
}
.generated-content{
    max-height: 160px;
    overflow: auto;
}
.bg-light{
    background-color: #F9FAFC !important;
}
.testimonial-section:hover{

    .carousel-control-prev{
        display: block !important;
        left: -66px;
        width: 44px !important;
        height: 44px !important;
        top: 52%;
        opacity: 1 !important;
    }
    .carousel-control-next{
        display: block !important;
        right: -66px;
        width: 44px !important;
        height: 44px !important;
        top: 52%;
        opacity: 1 !important;
    
    }
}
.carousel-btn{
    background: #FFF !important;
    border-color: #F1F3F5 !important;

}
.font-plus-jakarta{
    font-family:"Plus Jakarta Sans";
}
.resource-section{
    .card:hover{
        box-shadow: 0px 8px 20px -4px rgba(29, 33, 41, 0.08);
    }
}
//for cookie pop up 
.cc-window{
    box-shadow: 0px 12px 24px -6px rgba(29, 33, 41, 0.10);
    border-radius: 16px;
}
.cc-btn{
    border-radius: 8px;
}
.featured-on {
    .product-hunt-logo{
        height: 34px;
    }
    .mashable-logo{
        height: 32px;
    }
    .entrepreneur-logo{
        height: 32px;
    }
    .indiehackers-logo{
        height: 19px;
    }
}
@media (max-width: 450px) {
    .featured-on {
        .product-hunt-logo{
            height: 28px;
        }
        .mashable-logo{
            height: 24px;
        }
        .entrepreneur-logo{
            height: 24px;
        }
        .indiehackers-logo{
            height: 16px;
        }
    }
}
.everything-section-button{
    &:active,
    &.active,&:hover{
        border-color:#F1F3F5 !important;
        background: #F9FAFC !important;
    }
}
.border-radius-top{
    border-top-right-radius: 16px;
    border-top-left-radius: 16px;
}

.company-logo{
    width: 60px;
    height: 60px;
    border-radius: 4px;
}
.testimonial-img{
    width: 60px;
    height: 60px;
    border-radius: 50%;
}
.text-light-gray{
    color: $color-text-placeholder;
}
.glossary-sticky-section{
    position: sticky;
    top: 0;
    z-index: 1000;
}
.cta-image-container {
    position: relative;
}
@media (max-width: 768px){
    .cta-image-container {
        padding-left: 20px;
        padding-right: 20px;
    }
}

.cta-image-text {
    position: absolute;
    width: 528px;
    top: 80px;
    left: 80px;
}

@media (max-width: 1200px) {
    .cta-image-text {
        top: 14px;
        left: 24px;
        width: 280px;
    }
}

@media (max-width: 768px) {
    .cta-image {
        height: 506px !important;
    }
}
.bg-gradient-1{
    background: linear-gradient(180deg, #FFF 0%, rgba(255, 255, 255, 0.00) 85%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
.bg-dark-1{
    background-color: #002677;
}
.bg-transparent-6{
    background: rgba(255, 255, 255, 0.06);
}
.bg-transparent-4{
    background: rgba(255, 255, 255, 0.04);
}