<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg>
<metadata>
Created by FontForge 20120731 at Tue Aug 27 06:37:39 2019
 By www
Copyright (c) 2000 by <PERSON>, A2/SW/HK. All rights reserved.
</metadata>
<defs>
<font id="AvenyTWEB" horiz-adv-x="419" >
  <font-face 
    font-family="Aveny T WEB"
    font-weight="400"
    font-stretch="normal"
    units-per-em="1000"
    panose-1="2 0 5 6 3 0 0 2 0 4"
    ascent="750"
    descent="-250"
    x-height="490"
    cap-height="700"
    bbox="-20 -253 1162 900"
    underline-thickness="20"
    underline-position="-113"
    unicode-range="U+000D-FB02"
  />
<missing-glyph horiz-adv-x="0" 
 />
    <glyph glyph-name="fi" unicode="fi" horiz-adv-x="456" 
d="M96 490v84q0 56 32.5 91t102.5 35h146q10 0 16.5 -7t6.5 -16v-22q0 -10 -6.5 -16.5t-16.5 -6.5h-144q-35 0 -49.5 -17t-14.5 -46v-79h208q10 0 16.5 -6.5t6.5 -16.5v-444q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v402h-158v-402q0 -10 -6.5 -16.5
t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v402h-54q-10 0 -16.5 6.5t-6.5 15.5v20q0 10 6.5 16.5t16.5 6.5h54z" />
    <glyph glyph-name="fl" unicode="fl" horiz-adv-x="464" 
d="M169 425v-402q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v402h-54q-10 0 -16.5 6.5t-6.5 15.5v20q0 10 6.5 16.5t16.5 6.5h54v84q0 56 32.5 91t102.5 35h12q10 0 16.5 -7t6.5 -16v-22q0 -10 -6.5 -16.5t-16.5 -6.5h-10q-35 0 -49.5 -17t-14.5 -46v-79
h157v187q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-654q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v402h-157z" />
    <glyph glyph-name=".notdef" horiz-adv-x="0" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name=".null" horiz-adv-x="0" 
 />
    <glyph glyph-name="nonmarkingreturn" unicode="&#xd;" horiz-adv-x="250" 
 />
    <glyph glyph-name="space" unicode=" " horiz-adv-x="191" 
 />
    <glyph glyph-name="exclam" unicode="!" horiz-adv-x="211" 
d="M96 194q-11 0 -17 6.5t-6 16.5l-12 418q-1 17 -1 28.5v16.5q0 9 6 14.5t15 5.5h49q9 0 15 -5.5t6 -14.5v-16.5t-1 -28.5l-12 -418q0 -10 -6 -16.5t-17 -6.5h-19zM157 23q0 -10 -6.5 -16.5t-16.5 -6.5h-57q-9 0 -16 6.5t-7 16.5v78q0 10 7 16t16 6h57q10 0 16.5 -6
t6.5 -16v-78z" />
    <glyph glyph-name="quotedbl" unicode="&#x22;" horiz-adv-x="324" 
d="M263 700q11 0 15 -5.5t4 -13.5q0 -3 -0.5 -14.5t-1.5 -19.5l-18 -246q-1 -9 -5 -16.5t-19 -7.5h-16q-20 0 -20 23v278q0 9 5.5 15.5t16.5 6.5h39zM113 700q11 0 15 -5.5t4 -13.5q0 -3 -0.5 -14.5t-1.5 -19.5l-18 -246q-1 -9 -5 -16.5t-19 -7.5h-16q-20 0 -20 23v278
q0 9 5.5 15.5t16.5 6.5h39z" />
    <glyph glyph-name="numbersign" unicode="#" horiz-adv-x="531" 
d="M497 237q0 -9 -6.5 -16t-16.5 -7h-72v-191q0 -11 -6.5 -17t-17.5 -6h-21q-11 0 -17.5 6t-6.5 17v191h-134v-191q0 -11 -7 -17t-17 -6h-22q-10 0 -16.5 6t-6.5 17v191h-73q-9 0 -16 7t-7 16v18q0 9 7 15.5t16 6.5h73v146h-73q-9 0 -16 6.5t-7 16.5v17q0 10 7 16.5t16 6.5
h73v192q0 10 6.5 16t16.5 6h22q10 0 17 -6t7 -16v-192h134v192q0 10 6.5 16t17.5 6h21q11 0 17.5 -6t6.5 -16v-192h72q10 0 16.5 -6.5t6.5 -16.5v-17q0 -10 -6.5 -16.5t-16.5 -6.5h-72v-146h72q10 0 16.5 -6.5t6.5 -15.5v-18zM333 423h-134v-146h134v146z" />
    <glyph glyph-name="dollar" unicode="$" horiz-adv-x="421" 
d="M40 145v90q0 10 6.5 16.5t16.5 6.5h27q9 0 16 -6.5t7 -16.5v-84q0 -41 19.5 -62.5t58.5 -21.5h37q36 0 54 24t18 60v45q0 24 -3.5 41t-11.5 29.5t-20.5 23t-31.5 20.5l-106 59q-38 21 -58 53.5t-20 86.5v45q0 66 37.5 103t94.5 42v78q0 10 6.5 16.5t16.5 6.5h22
q10 0 16.5 -6.5t6.5 -16.5v-78q57 -5 94 -41.5t37 -102.5v-70q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-9 0 -16 6.5t-7 16.5v64q0 41 -19.5 62.5t-58.5 21.5h-35q-36 0 -54 -23t-18 -59v-33q0 -28 10 -51t46 -43l118 -67q41 -23 59 -57t18 -84v-69q0 -64 -35 -101.5t-89 -44.5
v-78q0 -10 -6.5 -16.5t-16.5 -6.5h-22q-10 0 -16.5 6.5t-6.5 16.5v77q-60 2 -100.5 39t-40.5 106z" />
    <glyph glyph-name="percent" unicode="%" horiz-adv-x="662" 
d="M278 482q0 -55 -30 -89t-85 -34h-10q-55 0 -85 34t-30 89v95q0 55 30 89t85 34h10q55 0 85 -34t30 -89v-95zM213 588q0 24 -13.5 41t-41.5 17q-29 0 -42 -17t-13 -41v-117q0 -24 13 -41t42 -17q28 0 41.5 17t13.5 41v117zM624 123q0 -55 -30 -89t-85 -34h-10
q-55 0 -85 34t-30 89v95q0 55 30 89t85 34h10q55 0 85 -34t30 -89v-95zM559 229q0 24 -13.5 41t-41.5 17q-29 0 -42 -17t-13 -41v-117q0 -24 13 -41t42 -17q28 0 41.5 17t13.5 41v117zM145 18q-6 -8 -14 -13t-23 -5h-10q-10 0 -15 5t-5 13t7 19l432 645q5 8 11.5 13t21.5 5
h14q11 0 15.5 -5.5t4.5 -13.5q0 -9 -8 -20z" />
    <glyph glyph-name="ampersand" unicode="&#x26;" horiz-adv-x="531" 
d="M148 545q0 -23 6.5 -40.5t15.5 -28.5l186 -241l33 141q4 17 26 17h25q11 0 16.5 -6.5t5.5 -14.5q0 -7 -1.5 -13t-3.5 -17l-45 -175l95 -125q6 -8 8.5 -12.5t2.5 -11.5t-5.5 -12.5t-15.5 -5.5h-35q-13 0 -18.5 4t-10.5 12l-49 68q-14 -50 -45 -67t-72 -17h-103
q-61 0 -89 33t-28 88v158q0 40 17.5 73t69.5 48l-18 26q-14 20 -22 34.5t-12.5 28.5t-5.5 30t-1 40q0 35 11.5 61.5t31 44t45 26.5t54.5 9h40q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-50q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-9 0 -16 6.5t-7 16.5v44q0 41 -19.5 62.5
t-58.5 21.5h-27q-39 0 -58.5 -21.5t-19.5 -62.5v-4zM255 67q36 0 49.5 18.5t22.5 55.5l3 12l-157 196q-18 -4 -28.5 -12t-16 -17t-7 -19t-1.5 -18v-157q0 -23 12 -41t40 -18h83z" />
    <glyph glyph-name="quotesingle" unicode="'" horiz-adv-x="173" 
d="M112 700q11 0 15 -5.5t4 -13.5q0 -3 -0.5 -14.5t-1.5 -19.5l-18 -246q-1 -9 -5.5 -16.5t-21.5 -7.5h-13q-9 0 -14.5 6.5t-5.5 16.5v278q0 9 6 15.5t17 6.5h38z" />
    <glyph glyph-name="parenleft" unicode="(" horiz-adv-x="277" 
d="M127 44q0 -44 22 -64t58 -20h22q10 0 16.5 -6.5t6.5 -16.5v-22q0 -9 -6.5 -15.5t-16.5 -6.5h-17q-85 0 -121.5 43t-36.5 113v495q0 70 36.5 113t121.5 43h17q10 0 16.5 -6.5t6.5 -15.5v-22q0 -10 -6.5 -16.5t-16.5 -6.5h-22q-36 0 -58 -20t-22 -64v-505z" />
    <glyph glyph-name="parenright" unicode=")" horiz-adv-x="277" 
d="M150 549q0 44 -22 64t-58 20h-22q-10 0 -16.5 6.5t-6.5 16.5v22q0 9 6.5 15.5t16.5 6.5h17q85 0 121.5 -43t36.5 -113v-495q0 -70 -36.5 -113t-121.5 -43h-17q-10 0 -16.5 6.5t-6.5 15.5v22q0 10 6.5 16.5t16.5 6.5h22q36 0 58 20t22 64v505z" />
    <glyph glyph-name="asterisk" unicode="*" horiz-adv-x="404" 
d="M202 485l-58 -110q-12 -24 -32 -24q-18 0 -31.5 11t-13.5 30q0 11 6 18q8 10 23 25.5t30 31.5q17 18 36 37q-26 6 -48 11q-21 4 -38 8t-25 6q-9 2 -15 9.5t-6 21.5q0 17 10.5 30.5t25.5 13.5q5 0 11 -1.5t14 -6.5l91 -57q-6 25 -10 49q-4 22 -7 43.5t-3 38.5
q0 16 11 24.5t29 8.5t29 -8.5t11 -24.5q0 -17 -3 -39.5t-7 -42.5q-4 -24 -10 -49l91 57q8 5 14 6.5t11 1.5q15 0 25.5 -13.5t10.5 -30.5q0 -14 -6 -21.5t-15 -9.5q-8 -2 -26 -6t-37 -8q-22 -5 -48 -11q19 -19 36 -37q16 -17 30.5 -32t22.5 -25q6 -7 6 -18q0 -19 -13.5 -30
t-31.5 -11q-20 0 -32 24z" />
    <glyph glyph-name="plus" unicode="+" horiz-adv-x="456" 
d="M262 391h144q10 0 16.5 -6.5t6.5 -16.5v-19q0 -9 -6.5 -15.5t-16.5 -6.5h-144v-148q0 -10 -6.5 -16.5t-15.5 -6.5h-23q-10 0 -16.5 6.5t-6.5 16.5v148h-144q-10 0 -16.5 6.5t-6.5 15.5v19q0 10 6.5 16.5t16.5 6.5h144v161q0 10 6.5 16.5t16.5 6.5h23q9 0 15.5 -6.5
t6.5 -16.5v-161z" />
    <glyph glyph-name="comma" unicode="," horiz-adv-x="174" 
d="M110 193q11 0 15 -5.5t4 -13.5q0 -3 -0.5 -14.5t-1.5 -19.5l-18 -246q-1 -9 -5.5 -16.5t-21.5 -7.5h-13q-9 0 -14.5 6.5t-5.5 16.5v278q0 9 6 15.5t17 6.5h38z" />
    <glyph glyph-name="hyphen" unicode="-" horiz-adv-x="334" 
d="M268 324q10 0 16.5 -6.5t6.5 -16.5v-19q0 -9 -6.5 -15.5t-16.5 -6.5h-202q-10 0 -16.5 6.5t-6.5 15.5v19q0 10 6.5 16.5t16.5 6.5h202z" />
    <glyph glyph-name="period" unicode="." horiz-adv-x="193" 
d="M148 23q0 -10 -6.5 -16.5t-16.5 -6.5h-57q-9 0 -16 6.5t-7 16.5v78q0 10 7 16t16 6h57q10 0 16.5 -6t6.5 -16v-78z" />
    <glyph glyph-name="slash" unicode="/" horiz-adv-x="255" 
d="M100 -77q-2 -10 -7.5 -16.5t-20.5 -6.5h-28q-15 0 -15 15q0 8 3 23l126 740q3 22 26 22h25q17 0 17 -16q0 -6 -1 -12t-3 -20z" />
    <glyph glyph-name="zero" unicode="0" horiz-adv-x="450" 
d="M319 549q0 41 -19.5 62.5t-58.5 21.5h-32q-39 0 -58.5 -21.5t-19.5 -62.5v-398q0 -84 78 -84h32q39 0 58.5 21.5t19.5 62.5v398zM392 145q0 -35 -11.5 -62t-31 -45.5t-46.5 -28t-58 -9.5h-40q-31 0 -58 9.5t-46.5 28t-31 45.5t-11.5 62v410q0 35 11.5 62t31 45.5t46.5 28
t58 9.5h40q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-410z" />
    <glyph glyph-name="one" unicode="1" horiz-adv-x="260" 
d="M123 552h-75q-12 0 -18 6.5t-6 15.5v22q0 9 6.5 15.5t15.5 6.5h31q23 1 37.5 10.5t14.5 46.5q0 15 7.5 20t17.5 5h21q11 0 16 -6t5 -15v-656q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v529z" />
    <glyph glyph-name="two" unicode="2" horiz-adv-x="416" 
d="M300 550q0 40 -19.5 61.5t-58.5 21.5h-25q-39 0 -58.5 -21.5t-19.5 -62.5v-74q0 -10 -7 -16.5t-16 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v80q0 35 11.5 62t31 45.5t46.5 28t58 9.5h33q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-47q0 -27 -8 -57.5t-43 -71.5l-168 -198
q-17 -20 -23.5 -38.5t-6.5 -35.5v-40h226q10 0 16.5 -6.5t6.5 -15.5v-22q0 -10 -6.5 -16.5t-16.5 -6.5h-276q-9 0 -16 6.5t-7 16.5v67q0 40 9 69t40 65l170 201q11 14 20.5 35.5t9.5 38.5v51z" />
    <glyph glyph-name="three" unicode="3" horiz-adv-x="430" 
d="M377 145q0 -35 -11.5 -62t-31 -45.5t-46.5 -28t-58 -9.5h-40q-31 0 -58 9.5t-46.5 28t-31 45.5t-11.5 62v40q0 10 6.5 16.5t16.5 6.5h27q9 0 16 -6.5t7 -16.5v-34q0 -41 19.5 -62.5t58.5 -21.5h32q78 0 78 84v86q0 36 -15 60t-51 24h-70q-10 0 -16.5 6.5t-6.5 16.5v22
q0 9 6.5 15.5t16.5 6.5h66q36 0 51 24t15 60v78q0 40 -19.5 61.5t-58.5 21.5h-25q-39 0 -58.5 -21.5t-19.5 -62.5v-26q0 -10 -7 -16.5t-16 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v32q0 35 11.5 62t31 45.5t46.5 28t58 9.5h33q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-80
q0 -45 -15.5 -73t-47.5 -45q32 -15 49.5 -45t17.5 -75v-92z" />
    <glyph glyph-name="four" unicode="4" horiz-adv-x="427" 
d="M214 679q4 7 10.5 14t19.5 7h56q9 0 16 -7t7 -16v-415h58q10 0 16.5 -7t6.5 -16v-17q0 -10 -6.5 -16.5t-16.5 -6.5h-58v-176q0 -10 -6.5 -16.5t-16.5 -6.5h-25q-10 0 -16.5 6.5t-6.5 16.5v176h-208q-9 0 -15 5.5t-6 17.5v26q0 11 5 19zM252 615l-158 -353h158v353z" />
    <glyph glyph-name="five" unicode="5" horiz-adv-x="436" 
d="M128 332q0 -10 -7 -16.5t-16 -6.5h-22q-10 0 -16.5 6.5t-6.5 16.5v346q0 9 6.5 15.5t16.5 6.5h279q10 0 16.5 -6.5t6.5 -15.5v-22q0 -10 -6.5 -16.5t-16.5 -6.5h-234v-201q13 15 38 26.5t60 11.5h12q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-180q0 -35 -11.5 -62
t-31 -45.5t-46.5 -28t-58 -9.5h-40q-31 0 -58 9.5t-46.5 28t-31 45.5t-11.5 62v40q0 10 6.5 16.5t16.5 6.5h27q9 0 16 -6.5t7 -16.5v-34q0 -41 19.5 -62.5t58.5 -21.5h32q78 0 78 84v168q0 84 -78 84h-27q-37 0 -58 -17.5t-21 -48.5v-5z" />
    <glyph glyph-name="six" unicode="6" horiz-adv-x="443" 
d="M245 427q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-137q0 -35 -11.5 -62t-31 -45.5t-46.5 -28t-58 -9.5h-40q-31 0 -58 9.5t-46.5 28t-31 45.5t-11.5 62v410q0 35 11.5 62t31 45.5t46.5 28t58 9.5h40q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-30q0 -10 -6.5 -16.5
t-16.5 -6.5h-27q-9 0 -16 6.5t-7 16.5v24q0 41 -19.5 62.5t-58.5 21.5h-32q-39 0 -58.5 -21.5t-19.5 -62.5v-165q16 20 43.5 31.5t58.5 11.5h12zM241 67q39 0 58.5 21.5t19.5 62.5v125q0 41 -19.5 62.5t-58.5 21.5h-29q-39 0 -60 -21.5t-21 -63.5v-124q0 -84 78 -84h32z" />
    <glyph glyph-name="seven" unicode="7" horiz-adv-x="398" 
d="M35 676q0 11 7 17.5t16 6.5h294q9 0 16 -6.5t7 -17.5v-32q0 -8 -5 -18q-27 -64 -54.5 -134t-50 -143.5t-37 -149.5t-14.5 -152v-24q0 -10 -6.5 -16.5t-15.5 -6.5h-34q-9 0 -15.5 6.5t-6.5 16.5v28q0 60 14.5 133t38.5 150t54.5 154t61.5 145h-201v-130q0 -10 -6.5 -16.5
t-16.5 -6.5h-23q-9 0 -16 6.5t-7 16.5v173z" />
    <glyph glyph-name="eight" unicode="8" horiz-adv-x="447" 
d="M205 337q-39 0 -59 -22t-20 -63v-101q0 -84 79 -84h37q39 0 59 21.5t20 62.5v101q0 41 -20 63t-59 22h-37zM317 549q0 41 -19.5 62.5t-58.5 21.5h-31q-39 0 -58.5 -21.5t-19.5 -62.5v-61q0 -42 18 -63.5t57 -21.5h37q39 0 57 21.5t18 63.5v61zM394 145q0 -35 -11.5 -62
t-31.5 -45.5t-47 -28t-58 -9.5h-45q-31 0 -58 9.5t-47 28t-31.5 45.5t-11.5 62v110q0 38 20 70.5t55 43.5q-35 12 -53 44.5t-18 70.5v71q0 35 11.5 62t31 45.5t46.5 28t58 9.5h39q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-71q0 -38 -18 -70.5t-53 -44.5q35 -11 55 -43.5
t20 -70.5v-110z" />
    <glyph glyph-name="nine" unicode="9" horiz-adv-x="444" 
d="M199 273q-31 0 -58 9.5t-46.5 28t-31 45.5t-11.5 62v137q0 35 11.5 62t31 45.5t46.5 28t58 9.5h40q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-410q0 -35 -11.5 -62t-31 -45.5t-46.5 -28t-58 -9.5h-40q-31 0 -58 9.5t-46.5 28t-31 45.5t-11.5 62v40q0 10 6.5 16.5
t16.5 6.5h27q9 0 16 -6.5t7 -16.5v-34q0 -41 19.5 -62.5t58.5 -21.5h32q39 0 58.5 21.5t19.5 62.5v165q-16 -20 -43.5 -31.5t-58.5 -11.5h-12zM203 633q-39 0 -58.5 -21.5t-19.5 -62.5v-125q0 -41 19.5 -62.5t58.5 -21.5h29q39 0 60 21.5t21 63.5v124q0 84 -78 84h-32z" />
    <glyph glyph-name="colon" unicode=":" horiz-adv-x="213" 
d="M158 23q0 -10 -6.5 -16.5t-16.5 -6.5h-57q-9 0 -16 6.5t-7 16.5v78q0 10 7 16t16 6h57q10 0 16.5 -6t6.5 -16v-78zM158 390q0 -10 -6.5 -16.5t-16.5 -6.5h-57q-9 0 -16 6.5t-7 16.5v78q0 10 7 16t16 6h57q10 0 16.5 -6t6.5 -16v-78z" />
    <glyph glyph-name="semicolon" unicode=";" horiz-adv-x="198" 
d="M120 193q11 0 15 -5.5t4 -13.5q0 -3 -0.5 -14.5t-1.5 -19.5l-18 -246q-1 -9 -5.5 -16.5t-21.5 -7.5h-13q-9 0 -14.5 6.5t-5.5 16.5v278q0 9 6 15.5t17 6.5h38zM152 390q0 -10 -6.5 -16.5t-16.5 -6.5h-57q-9 0 -16 6.5t-7 16.5v78q0 10 7 16t16 6h57q10 0 16.5 -6t6.5 -16
v-78z" />
    <glyph glyph-name="less" unicode="&#x3c;" horiz-adv-x="654" 
d="M24 357q0 9 6 15l212 226q11 12 20 16t26 4h25q17 0 17 -16q0 -6 -2.5 -10.5t-7.5 -9.5l-185 -191h468q10 0 16.5 -6.5t6.5 -16.5v-22q0 -9 -6.5 -15.5t-16.5 -6.5h-468l185 -191q5 -5 7.5 -9.5t2.5 -10.5q0 -16 -17 -16h-25q-17 0 -26 4t-20 16l-212 226q-6 6 -6 14z
" />
    <glyph glyph-name="equal" unicode="=" horiz-adv-x="494" 
d="M81 241q-10 0 -16.5 6.5t-6.5 15.5v19q0 10 6.5 16.5t16.5 6.5h332q10 0 16.5 -6.5t6.5 -16.5v-19q0 -9 -6.5 -15.5t-16.5 -6.5h-332zM81 408q-10 0 -16.5 6.5t-6.5 15.5v19q0 10 6.5 16.5t16.5 6.5h332q10 0 16.5 -6.5t6.5 -16.5v-19q0 -9 -6.5 -15.5t-16.5 -6.5h-332z
" />
    <glyph glyph-name="greater" unicode="&#x3e;" horiz-adv-x="654" 
d="M630 357q0 -8 -6 -14l-212 -226q-11 -12 -20 -16t-26 -4h-25q-17 0 -17 16q0 6 2.5 10.5t7.5 9.5l185 191h-468q-10 0 -16.5 6.5t-6.5 15.5v22q0 10 6.5 16.5t16.5 6.5h468l-185 191q-5 5 -7.5 9.5t-2.5 10.5q0 16 17 16h25q17 0 26 -4t20 -16l212 -226q6 -6 6 -15z" />
    <glyph glyph-name="question" unicode="?" horiz-adv-x="390" 
d="M174 194q-9 0 -16 6.5t-7 16.5v23q0 20 1 36.5t5.5 32.5t13.5 31.5t24 33.5l60 71q11 14 20.5 35.5t9.5 38.5v31q0 40 -19.5 61.5t-58.5 21.5h-25q-39 0 -58.5 -21.5t-19.5 -62.5v-44q0 -10 -7 -16.5t-16 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v50q0 35 11.5 62t31 45.5
t46.5 28t58 9.5h33q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-27q0 -27 -8 -57t-42 -71l-56 -69q-17 -20 -22.5 -38.5t-5.5 -35.5v-40q0 -10 -7 -16.5t-16 -6.5h-27zM244 23q0 -10 -6.5 -16.5t-16.5 -6.5h-57q-9 0 -16 6.5t-7 16.5v78q0 10 7 16t16 6h57q10 0 16.5 -6
t6.5 -16v-78z" />
    <glyph glyph-name="at" unicode="@" horiz-adv-x="824" 
d="M524 64q-14 -29 -47.5 -46.5t-79.5 -17.5h-24q-29 0 -52.5 10t-39.5 27.5t-24.5 40.5t-8.5 49v19q0 55 33.5 90.5t103.5 35.5h116v73q0 36 -18 58t-54 22h-20q-36 0 -55.5 -19.5t-19.5 -55.5v-10q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v8
q0 70 38.5 106t101.5 36h36q63 0 100 -38.5t37 -108.5v-205q0 -35 14 -54t50 -19q35 0 51 21.5t16 59.5v267q0 92 -51 137.5t-137 45.5h-202q-86 0 -137 -45.5t-51 -138.5v-337q0 -92 53 -137.5t138 -45.5h307q10 0 16.5 -6.5t6.5 -15.5v-20q0 -10 -6.5 -16.5t-16.5 -6.5
h-311q-120 0 -190 63t-70 180v337q0 58 17 104.5t49.5 79.5t79.5 50.5t107 17.5h218q60 0 107 -17.5t79.5 -50.5t49.5 -79.5t17 -104.5v-266q0 -60 -36 -100.5t-104 -40.5q-54 0 -80.5 21t-33.5 43zM419 65q38 0 60 24t22 66v54h-111q-37 0 -53 -16.5t-16 -44.5v-16
q0 -29 14.5 -48t52.5 -19h31z" />
    <glyph glyph-name="A" unicode="A" 
d="M286 198h-158l-32 -178q-3 -20 -23 -20h-29q-9 0 -15 4t-6 14q0 5 3 21l124 638q2 10 8 16.5t16 6.5h72q10 0 16 -6.5t8 -16.5l121 -631q2 -8 2.5 -15.5t0.5 -10.5q0 -20 -20 -20h-32q-20 0 -23 20zM140 261h135l-67 370z" />
    <glyph glyph-name="B" unicode="B" horiz-adv-x="446" 
d="M388 491q0 -43 -15.5 -77t-56.5 -54q45 -15 62.5 -51.5t17.5 -84.5v-77q0 -35 -11.5 -62.5t-31 -46t-46.5 -28.5t-58 -10h-163q-9 0 -16 6.5t-7 16.5v655q0 10 7 16t16 6h155q31 0 58 -10t46.5 -28.5t31 -46t11.5 -62.5v-62zM136 324v-257h115q36 0 54 24t18 60v84
q0 36 -19 62.5t-60 26.5h-108zM239 391q34 0 55 24.5t21 59.5v74q0 36 -19.5 60t-55.5 24h-104v-242h103z" />
    <glyph glyph-name="C" unicode="C" horiz-adv-x="439" 
d="M319 235q0 10 7 16.5t16 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-90q0 -35 -11.5 -62t-31 -45.5t-46.5 -28t-58 -9.5h-40q-31 0 -58 9.5t-46.5 28t-31 45.5t-11.5 62v410q0 35 11.5 62t31 45.5t46.5 28t58 9.5h40q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-70
q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-9 0 -16 6.5t-7 16.5v64q0 41 -19.5 62.5t-58.5 21.5h-32q-39 0 -58.5 -21.5t-19.5 -62.5v-398q0 -84 78 -84h32q39 0 58.5 21.5t19.5 62.5v84z" />
    <glyph glyph-name="D" unicode="D" horiz-adv-x="455" 
d="M252 67q36 0 54 24t18 60v398q0 36 -18 60t-54 24h-116v-566h116zM86 0q-9 0 -16 6.5t-7 16.5v655q0 10 7 16t16 6h164q31 0 58 -10t46.5 -28.5t31 -46t11.5 -62.5v-406q0 -35 -11.5 -62.5t-31 -46t-46.5 -28.5t-58 -10h-164z" />
    <glyph glyph-name="E" unicode="E" horiz-adv-x="404" 
d="M86 0q-9 0 -16 6.5t-7 16.5v655q0 9 7 15.5t16 6.5h257q10 0 16.5 -6.5t6.5 -15.5v-22q0 -10 -6.5 -16.5t-16.5 -6.5h-207v-242h177q10 0 16.5 -6.5t6.5 -16.5v-22q0 -9 -6.5 -15.5t-16.5 -6.5h-177v-257h217q10 0 16.5 -6.5t6.5 -15.5v-22q0 -10 -6.5 -16.5t-16.5 -6.5
h-267z" />
    <glyph glyph-name="F" unicode="F" horiz-adv-x="389" 
d="M86 0q-9 0 -16 6.5t-7 16.5v655q0 9 7 15.5t16 6.5h254q10 0 16.5 -6.5t6.5 -15.5v-22q0 -10 -6.5 -16.5t-16.5 -6.5h-204v-252h191q10 0 16.5 -6.5t6.5 -16.5v-22q0 -9 -6.5 -15.5t-16.5 -6.5h-191v-291q0 -10 -7 -16.5t-16 -6.5h-27z" />
    <glyph glyph-name="G" unicode="G" horiz-adv-x="444" 
d="M198 0q-63 0 -101.5 37.5t-38.5 107.5v410q0 35 11.5 62t31 45.5t46.5 28t58 9.5h40q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-70q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-9 0 -16 6.5t-7 16.5v64q0 41 -19.5 62.5t-58.5 21.5h-32q-39 0 -58.5 -21.5t-19.5 -62.5v-398
q0 -84 78 -84h29q38 0 56.5 24t18.5 60v153q0 10 7 16.5t16 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-281q0 -10 -7 -16.5t-16 -6.5h-21q-10 0 -16.5 6.5t-6.5 16.5v26q-13 -20 -38.5 -34.5t-65.5 -14.5h-17z" />
    <glyph glyph-name="H" unicode="H" horiz-adv-x="462" 
d="M398 23q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v301h-188v-301q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v654q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-286h188v286q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5
t6.5 -16.5v-654z" />
    <glyph glyph-name="I" unicode="I" horiz-adv-x="200" 
d="M137 23q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v654q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-654z" />
    <glyph glyph-name="J" unicode="J" horiz-adv-x="438" 
d="M303 633h-169v-148q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-9 0 -16 6.5t-7 16.5v193q0 9 6.5 15.5t16.5 6.5h269q9 0 16 -6.5t7 -15.5v-533q0 -35 -11.5 -62t-31 -45.5t-46.5 -28t-58 -9.5h-40q-31 0 -58 9.5t-46.5 28t-31 45.5t-11.5 62v90q0 10 6.5 16.5t16.5 6.5h27
q9 0 16 -6.5t7 -16.5v-84q0 -41 19.5 -62.5t58.5 -21.5h32q78 0 78 84v482z" />
    <glyph glyph-name="K" unicode="K" horiz-adv-x="454" 
d="M323 368q24 -8 38.5 -22t22.5 -31.5t10.5 -37t2.5 -38.5v-178q0 -13 2.5 -25.5t2.5 -18.5q0 -17 -21 -17h-18q-21 0 -30 11.5t-9 32.5v206q0 74 -74 74h-114v-301q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-9 0 -16 6.5t-7 16.5v655q0 10 7 16t16 6h27q9 0 16 -6t7 -16v-287h112
q12 0 16 12l76 273q3 11 9 17.5t20 6.5h17q27 0 27 -23q0 -4 -2 -14z" />
    <glyph glyph-name="L" unicode="L" horiz-adv-x="391" 
d="M63 677q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-610h215q10 0 16.5 -6.5t6.5 -16.5v-22q0 -9 -6.5 -15.5t-16.5 -6.5h-265q-9 0 -16 6.5t-7 15.5v655z" />
    <glyph glyph-name="M" unicode="M" horiz-adv-x="507" 
d="M133 23q0 -10 -6.5 -16.5t-16.5 -6.5h-24q-10 0 -16.5 6.5t-6.5 16.5v655q0 9 7 15.5t16 6.5h42q10 0 16.5 -6t8.5 -15l100 -314l100 314q2 9 8.5 15t16.5 6h43q9 0 16 -6.5t7 -15.5v-655q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v513l-79 -244
q-2 -6 -6.5 -12t-15.5 -6h-36q-11 0 -15.5 6t-6.5 12l-79 244v-513z" />
    <glyph glyph-name="N" unicode="N" horiz-adv-x="454" 
d="M63 677q0 9 6.5 16t16.5 7h39q11 0 17 -4t11 -17l168 -501v499q0 9 6.5 16t16.5 7h24q10 0 16.5 -7t6.5 -16v-654q0 -10 -6.5 -16.5t-16.5 -6.5h-38q-10 0 -15.5 4t-10.5 17l-171 507v-505q0 -10 -6.5 -16.5t-16.5 -6.5h-24q-10 0 -16.5 6.5t-6.5 16.5v654z" />
    <glyph glyph-name="O" unicode="O" horiz-adv-x="450" 
d="M319 549q0 41 -19.5 62.5t-58.5 21.5h-32q-39 0 -58.5 -21.5t-19.5 -62.5v-398q0 -84 78 -84h32q39 0 58.5 21.5t19.5 62.5v398zM392 145q0 -35 -11.5 -62t-31 -45.5t-46.5 -28t-58 -9.5h-40q-31 0 -58 9.5t-46.5 28t-31 45.5t-11.5 62v410q0 35 11.5 62t31 45.5t46.5 28
t58 9.5h40q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-410z" />
    <glyph glyph-name="P" unicode="P" horiz-adv-x="421" 
d="M241 361q36 0 54 24t18 60v104q0 36 -18 60t-54 24h-105v-272h105zM136 23q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-9 0 -16 6.5t-7 16.5v655q0 10 7 16t16 6h153q31 0 58 -10t46.5 -28.5t31 -46t11.5 -62.5v-112q0 -35 -11.5 -62.5t-31 -46t-46.5 -28.5t-58 -10h-103v-271z
" />
    <glyph glyph-name="Q" unicode="Q" horiz-adv-x="455" 
d="M392 145q0 -45 -18 -78t-58 -46l81 -29q9 -3 12 -8t3 -14v-28q0 -8 -5 -13t-13 -5q-5 0 -10 1.5t-10 3.5l-185 72q-60 3 -95.5 40.5t-35.5 103.5v410q0 35 11.5 62t31 45.5t46.5 28t58 9.5h40q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-410zM319 549q0 41 -19.5 62.5
t-58.5 21.5h-32q-39 0 -58.5 -21.5t-19.5 -62.5v-398q0 -84 78 -84h32q39 0 58.5 21.5t19.5 62.5v398z" />
    <glyph glyph-name="R" unicode="R" horiz-adv-x="457" 
d="M136 23q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-9 0 -16 6.5t-7 16.5v655q0 10 7 16t16 6h164q31 0 58 -10t46.5 -28.5t31 -46t11.5 -62.5v-72q0 -43 -18.5 -76t-56.5 -47q44 -18 59.5 -52t15.5 -67v-178q0 -13 2.5 -25.5t2.5 -18.5q0 -17 -21 -17h-18q-21 0 -30 11.5t-9 32.5
v193q0 43 -23.5 65t-67.5 22h-99zM248 389q37 0 56.5 24.5t19.5 60.5v75q0 36 -19.5 61t-55.5 25h-113v-246h112z" />
    <glyph glyph-name="S" unicode="S" horiz-adv-x="434" 
d="M194 0q-31 0 -58 9.5t-46.5 28t-31 45.5t-11.5 62v90q0 10 6.5 16.5t16.5 6.5h27q9 0 16 -6.5t7 -16.5v-84q0 -41 19.5 -62.5t58.5 -21.5h37q36 0 54 24t18 60v45q0 24 -3.5 41t-11.5 29.5t-20.5 23t-31.5 20.5l-106 59q-38 21 -58 53.5t-20 86.5v45q0 35 11.5 62t31 46
t46.5 28.5t58 9.5h37q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-70q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-9 0 -16 6.5t-7 16.5v64q0 41 -19.5 62.5t-58.5 21.5h-35q-36 0 -54 -23t-18 -59v-33q0 -28 10 -51t46 -43l118 -67q41 -23 59 -57t18 -84v-69q0 -35 -11.5 -62.5
t-31 -46t-46.5 -28.5t-58 -10h-39z" />
    <glyph glyph-name="T" unicode="T" horiz-adv-x="397" 
d="M379 656q0 -10 -7 -16.5t-16 -6.5h-121v-610q0 -10 -7 -16.5t-16 -6.5h-27q-9 0 -16 6.5t-7 16.5v610h-121q-9 0 -16 6.5t-7 16.5v21q0 10 7 16.5t16 6.5h315q9 0 16 -6.5t7 -16.5v-21z" />
    <glyph glyph-name="U" unicode="U" horiz-adv-x="458" 
d="M396 677v-530q0 -35 -11.5 -62.5t-31 -46t-46.5 -28.5t-58 -10h-40q-31 0 -58 10t-46.5 28.5t-31 46t-11.5 62.5v530q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-526q0 -36 18 -60t54 -24h44q36 0 54 24t18 60v526q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5
t6.5 -16.5z" />
    <glyph glyph-name="V" unicode="V" 
d="M323 680q3 20 23 20h28q20 0 20 -20q0 -3 -0.5 -10.5t-2.5 -15.5l-122 -631q-2 -10 -8 -16.5t-16 -6.5h-72q-10 0 -16 6.5t-8 16.5l-121 631q-2 8 -2.5 15.5t-0.5 10.5q0 20 20 20h32q20 0 23 -20l111 -611z" />
    <glyph glyph-name="W" unicode="W" horiz-adv-x="676" 
d="M469 0q-10 0 -16 6.5t-8 16.5l-107 547l-107 -547q-2 -10 -8 -16.5t-16 -6.5h-65q-10 0 -16 6.5t-8 16.5l-84 631q-3 20 -3 26q0 20 20 20h30q20 0 23 -20l73 -590l116 590q3 20 23 20h47q17 0 20 -20l116 -590l73 590q3 20 23 20h30q20 0 20 -20q0 -3 -0.5 -10.5
t-2.5 -15.5l-84 -631q-2 -10 -8 -16.5t-16 -6.5h-65z" />
    <glyph glyph-name="X" unicode="X" horiz-adv-x="405" 
d="M207 429l104 250q5 14 11.5 17.5t15.5 3.5h27q17 0 17 -15q0 -5 -2 -10.5t-7 -17.5l-131 -311l129 -307q7 -17 7 -23q0 -8 -5.5 -12t-13.5 -4h-34q-9 0 -14 4.5t-10 18.5l-101 248l-99 -248q-5 -14 -10 -18.5t-14 -4.5h-36q-8 0 -13.5 4t-5.5 12q0 6 7 22l135 316
l-126 301q-6 14 -7.5 19t-1.5 10q0 8 5 12t13 4h30q9 0 15.5 -3.5t11.5 -17.5z" />
    <glyph glyph-name="Y" unicode="Y" horiz-adv-x="410" 
d="M376 700q19 0 19 -16q0 -5 -2 -11l-7 -21l-144 -409v-220q0 -10 -7 -16.5t-16 -6.5h-27q-9 0 -16 6.5t-7 16.5v219l-150 420q-3 8 -4 12t-1 9q0 9 6 13t12 4h36q18 0 25 -22l114 -340l110 340q6 22 26 22h33z" />
    <glyph glyph-name="Z" unicode="Z" horiz-adv-x="391" 
d="M359 23q0 -10 -7 -16.5t-16 -6.5h-283q-10 0 -16.5 6.5t-6.5 16.5v22q0 16 6 29l244 559h-220q-9 0 -16 6.5t-7 16.5v22q0 10 7 16t16 6h275q11 0 17.5 -6t6.5 -16v-22q0 -9 -4 -19l-246 -570h227q9 0 16 -7t7 -16v-21z" />
    <glyph glyph-name="bracketleft" unicode="[" horiz-adv-x="313" 
d="M133 -40h132q10 0 16.5 -6.5t6.5 -15.5v-22q0 -10 -6.5 -16.5t-16.5 -6.5h-182q-9 0 -16 6.5t-7 16.5v762q0 9 7 15.5t16 6.5h182q10 0 16.5 -6.5t6.5 -15.5v-22q0 -10 -6.5 -16.5t-16.5 -6.5h-132v-673z" />
    <glyph glyph-name="backslash" unicode="\" horiz-adv-x="255" 
d="M155 -77l-122 729q-2 14 -3 20t-1 12q0 16 17 16h25q23 0 26 -22l126 -740q3 -15 3 -23q0 -15 -15 -15h-28q-15 0 -20.5 6.5t-7.5 16.5z" />
    <glyph glyph-name="bracketright" unicode="]" horiz-adv-x="313" 
d="M180 633h-132q-10 0 -16.5 6.5t-6.5 16.5v22q0 9 6.5 15.5t16.5 6.5h182q9 0 16 -6.5t7 -15.5v-762q0 -10 -7 -16.5t-16 -6.5h-182q-10 0 -16.5 6.5t-6.5 16.5v22q0 9 6.5 15.5t16.5 6.5h132v673z" />
    <glyph glyph-name="asciicircum" unicode="^" horiz-adv-x="452" 
d="M178 686q3 8 9 11t13 3h52q7 0 13 -3t9 -11l116 -371q2 -8 2 -10q0 -15 -18 -15h-31q-14 0 -18.5 4.5t-6.5 12.5l-92 312l-92 -312q-2 -8 -6.5 -12.5t-18.5 -4.5h-31q-18 0 -18 15q0 2 2 10z" />
    <glyph glyph-name="underscore" unicode="_" horiz-adv-x="414" 
d="M318 -65q10 0 16.5 -6.5t6.5 -16.5v-22q0 -9 -6.5 -15.5t-16.5 -6.5h-222q-10 0 -16.5 6.5t-6.5 15.5v22q0 10 6.5 16.5t16.5 6.5h222z" />
    <glyph glyph-name="grave" unicode="`" horiz-adv-x="232" 
d="M143 682l25 -105q2 -8 3 -14t1 -8q0 -18 -18 -18h-17q-11 0 -16.5 5t-8.5 13l-47 108q-5 11 -5 20q0 17 20 17h40q19 0 23 -18z" />
    <glyph glyph-name="a" unicode="a" 
d="M365 23q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v18q-12 -13 -35 -27t-69 -14h-23q-29 0 -52.5 10t-40 27.5t-25.5 40.5t-9 49v19q0 55 33.5 90.5t103.5 35.5h117v73q0 36 -18 58t-54 22h-19q-36 0 -56.5 -19.5t-20.5 -55.5v-10q0 -10 -6.5 -16.5
t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v8q0 70 39.5 106t102.5 36h35q63 0 100 -38.5t37 -108.5v-320zM220 65q36 0 54 22t18 58v64h-112q-37 0 -53 -16.5t-16 -44.5v-16q0 -29 14.5 -48t52.5 -19h42z" />
    <glyph glyph-name="b" unicode="b" horiz-adv-x="428" 
d="M131 448q16 19 40.5 30.5t55.5 11.5h15q63 0 100 -38.5t37 -108.5v-196q0 -70 -37 -108.5t-100 -38.5h-21q-29 0 -52.5 10.5t-37.5 30.5v-18q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v654q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-229z
M306 345q0 36 -18 58t-54 22h-31q-36 0 -54 -22t-18 -58v-200q0 -36 18 -58t54 -22h31q36 0 54 22t18 58v200z" />
    <glyph glyph-name="c" unicode="c" horiz-adv-x="407" 
d="M365 147q0 -70 -37 -108.5t-100 -38.5h-42q-63 0 -100 38.5t-37 108.5v196q0 70 37 108.5t100 38.5h42q63 0 100 -38.5t37 -108.5v-18q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v20q0 36 -18 58t-54 22h-26q-36 0 -54 -22t-18 -58v-200q0 -36 18 -58
t54 -22h26q36 0 54 22t18 58v40q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-38z" />
    <glyph glyph-name="d" unicode="d" horiz-adv-x="428" 
d="M122 145q0 -36 18 -58t54 -22h31q36 0 54 22t18 58v200q0 36 -18 58t-54 22h-31q-36 0 -54 -22t-18 -58v-200zM297 677q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-654q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v18q-14 -20 -37.5 -30.5
t-52.5 -10.5h-21q-63 0 -100 38.5t-37 108.5v196q0 70 37 108.5t100 38.5h15q31 0 55.5 -11.5t40.5 -30.5v229z" />
    <glyph glyph-name="e" unicode="e" horiz-adv-x="410" 
d="M122 237v-92q0 -36 18 -58t54 -22h26q36 0 54 22t18 58v10q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-8q0 -70 -37 -108.5t-100 -38.5h-42q-63 0 -100 38.5t-37 108.5v196q0 70 37 108.5t100 38.5h42q63 0 100 -38.5t37 -108.5v-83q0 -10 -6.5 -16.5
t-16.5 -6.5h-220zM122 302h170v43q0 36 -18 58t-54 22h-26q-36 0 -54 -22t-18 -58v-43z" />
    <glyph glyph-name="f" unicode="f" horiz-adv-x="289" 
d="M96 574q0 56 32.5 91t102.5 35h42q10 0 16.5 -7t6.5 -16v-22q0 -10 -6.5 -16.5t-16.5 -6.5h-40q-35 0 -49.5 -17t-14.5 -46v-79h80q10 0 16.5 -6.5t6.5 -16.5v-20q0 -9 -6.5 -15.5t-16.5 -6.5h-80v-402q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v402
h-54q-10 0 -16.5 6.5t-6.5 15.5v20q0 10 6.5 16.5t16.5 6.5h54v84z" />
    <glyph glyph-name="g" unicode="g" horiz-adv-x="441" 
d="M335 425q1 0 7 -5t13 -15t13 -25.5t6 -37.5v-53q0 -70 -39 -107t-102 -37h-47q-32 0 -39.5 -13t-7.5 -26q0 -24 9 -31.5t40 -10.5l106 -10q55 -2 78 -30t23 -83v-23q0 -55 -32 -86.5t-91 -31.5h-121q-55 0 -84 27.5t-29 82.5v28q0 10 6.5 16.5t16.5 6.5h27q11 0 17 -6.5
t6 -16.5v-29q0 -44 44 -44h116q21 0 36 11.5t15 36.5v40q0 16 -11 24t-22 9l-132 10q-89 6 -89 83v20q0 22 12.5 43t35.5 28q-32 13 -50 45t-18 77v51q0 70 37.5 108.5t111.5 38.5h204q10 0 16.5 -6.5t6.5 -16.5v-20q0 -9 -6.5 -15.5t-16.5 -6.5h-66zM301 345q0 36 -19.5 58
t-55.5 22h-30q-36 0 -55.5 -22t-19.5 -58v-55q0 -36 19.5 -58t55.5 -22h30q36 0 55.5 22t19.5 58v55z" />
    <glyph glyph-name="h" unicode="h" horiz-adv-x="429" 
d="M231 490h6q63 0 100 -37t37 -107v-323q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v322q0 36 -18 58t-54 22h-25q-36 0 -54.5 -22t-18.5 -58v-322q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v654q0 10 6.5 16.5t16.5 6.5h27
q10 0 16.5 -6.5t6.5 -16.5v-229q36 42 100 42z" />
    <glyph glyph-name="i" unicode="i" horiz-adv-x="185" 
d="M129 23q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v444q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-444zM134 601q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-10 0 -16.5 6.5t-6.5 16.5v66q0 10 6.5 16.5t16.5 6.5h37q10 0 16.5 -6.5t6.5 -16.5v-66z
" />
    <glyph glyph-name="j" unicode="j" horiz-adv-x="188" 
d="M3 -200q-10 0 -16.5 7t-6.5 16v21q0 10 6.5 16.5t16.5 6.5h6q27 0 37.5 14.5t10.5 35.5v550q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-534q0 -27 -6 -51.5t-20 -42.5t-37 -28.5t-57 -10.5h-7zM135 601q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-10 0 -16.5 6.5
t-6.5 16.5v66q0 10 6.5 16.5t16.5 6.5h37q10 0 16.5 -6.5t6.5 -16.5v-66z" />
    <glyph glyph-name="k" unicode="k" horiz-adv-x="428" 
d="M301 177q0 28 -15.5 40.5t-40.5 12.5h-114v-207q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-9 0 -16 6.5t-7 16.5v655q0 10 7 16t16 6h27q9 0 16 -6t7 -16v-385h106q36 0 50 16.5t14 41.5v95q0 21 6.5 32.5t27.5 11.5h23q21 0 21 -17q0 -6 -2.5 -18.5t-2.5 -25.5v-56
q0 -52 -18.5 -75.5t-45.5 -34.5q34 -10 49 -34.5t15 -59.5v-108q0 -13 1.5 -26.5t1.5 -19.5q0 -15 -19 -15h-26q-21 0 -26 11.5t-5 32.5v133z" />
    <glyph glyph-name="l" unicode="l" horiz-adv-x="203" 
d="M138 23q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v654q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-654z" />
    <glyph glyph-name="m" unicode="m" horiz-adv-x="658" 
d="M229 490q41 0 69 -15.5t43 -41.5q21 30 51.5 43.5t68.5 13.5q63 0 102.5 -37t39.5 -107v-323q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v322q0 36 -18 58t-54 22h-19q-35 0 -54 -20t-19 -53v-329q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5
t-6.5 16.5v322q0 36 -18 58t-54 22h-19q-36 0 -54.5 -22t-18.5 -58v-322q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v444q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-19q36 42 100 42z" />
    <glyph glyph-name="n" unicode="n" horiz-adv-x="427" 
d="M229 490h6q63 0 100 -37t37 -107v-323q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v322q0 36 -18 58t-54 22h-25q-36 0 -54.5 -22t-18.5 -58v-322q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v444q0 10 6.5 16.5t16.5 6.5h27
q10 0 16.5 -6.5t6.5 -16.5v-19q36 42 100 42z" />
    <glyph glyph-name="o" unicode="o" horiz-adv-x="414" 
d="M49 147v196q0 70 37 108.5t100 38.5h42q63 0 100 -38.5t37 -108.5v-196q0 -70 -37 -108.5t-100 -38.5h-42q-63 0 -100 38.5t-37 108.5zM292 345q0 36 -18 58t-54 22h-26q-36 0 -54 -22t-18 -58v-200q0 -36 18 -58t54 -22h26q36 0 54 22t18 58v200z" />
    <glyph glyph-name="p" unicode="p" horiz-adv-x="427" 
d="M130 448q16 19 40.5 30.5t55.5 11.5h15q63 0 100 -38.5t37 -108.5v-196q0 -70 -37 -108.5t-100 -38.5h-21q-29 0 -52.5 10.5t-37.5 30.5v-218q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v644q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-19z
M305 345q0 36 -18 58t-54 22h-31q-36 0 -54 -22t-18 -58v-200q0 -36 18 -58t54 -22h31q36 0 54 22t18 58v200z" />
    <glyph glyph-name="q" unicode="q" horiz-adv-x="428" 
d="M122 145q0 -36 18 -58t54 -22h31q36 0 54 22t18 58v200q0 36 -18 58t-54 22h-31q-36 0 -54 -22t-18 -58v-200zM297 467q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-644q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v218q-14 -20 -37.5 -30.5
t-52.5 -10.5h-21q-63 0 -100 38.5t-37 108.5v196q0 70 37 108.5t100 38.5h15q31 0 55.5 -11.5t40.5 -30.5v19z" />
    <glyph glyph-name="r" unicode="r" horiz-adv-x="302" 
d="M233 417q-26 0 -46.5 -10t-33.5 -27q-14 -18 -19 -42.5t-5 -54.5v-260q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v444q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-42q20 35 50 50t70 15h14q10 0 16.5 -7t6.5 -16v-27q0 -10 -6.5 -16.5
t-16.5 -6.5h-30z" />
    <glyph glyph-name="s" unicode="s" horiz-adv-x="403" 
d="M124 237q-48 14 -63.5 43t-15.5 73v21q0 53 33.5 84.5t86.5 31.5h73q55 0 87 -29.5t32 -86.5v-29q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-9 0 -16 7t-7 18v30q0 25 -14 35.5t-30 10.5h-78q-21 0 -32.5 -14.5t-11.5 -35.5v-26q0 -11 6.5 -24.5t23.5 -18.5l137 -44
q35 -11 55 -36.5t20 -75.5v-31q0 -48 -31 -82.5t-89 -34.5h-83q-55 0 -84 28t-29 83v40q0 10 6.5 16.5t16.5 6.5h27q9 0 16 -6.5t7 -16.5v-32q0 -29 12 -40.5t34 -11.5h71q22 0 37.5 11.5t15.5 40.5v15q0 35 -11 48t-40 21z" />
    <glyph glyph-name="t" unicode="t" horiz-adv-x="307" 
d="M165 425v-311q0 -10 1.5 -20t6.5 -18t14 -10.5t19 -2.5q26 0 41 3.5t29 3.5q7 0 11 -5t4 -12v-27q0 -5 -3 -11.5t-17 -9.5q-16 -4 -36.5 -6t-34.5 -2q-31 0 -52 8t-33.5 22.5t-17.5 34t-5 41.5v322h-54q-10 0 -16.5 6.5t-6.5 16.5v20q0 9 6.5 15.5t16.5 6.5h54v126
q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-126h100q10 0 16.5 -6.5t6.5 -15.5v-20q0 -10 -6.5 -16.5t-16.5 -6.5h-100z" />
    <glyph glyph-name="u" unicode="u" horiz-adv-x="421" 
d="M191 0q-63 0 -99.5 37t-36.5 107v323q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-322q0 -36 18 -58t54 -22h19q36 0 54.5 22t18.5 58v322q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-444q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5
t-6.5 16.5v19q-36 -42 -101 -42z" />
    <glyph glyph-name="v" unicode="v" horiz-adv-x="384" 
d="M186 73q2 -6 6 -6q5 0 6 6l88 397q5 20 25 20h24q14 0 19.5 -7t5.5 -16q0 -4 -0.5 -8.5t-1.5 -9.5l-102 -426q-3 -10 -10 -16.5t-18 -6.5h-73q-11 0 -18.5 6.5t-9.5 16.5l-101 428q-3 14 -3 19q0 20 24 20h27q20 0 25 -20z" />
    <glyph glyph-name="w" unicode="w" horiz-adv-x="633" 
d="M28 451q-3 14 -3 19q0 20 24 20h27q20 0 25 -20l79 -397q2 -6 6 -6q5 0 6 6l82 397q2 8 8 14t17 6h46q20 0 25 -20l75 -397q2 -6 6 -6q5 0 6 6l78 397q5 20 25 20h22q14 0 19.5 -7t5.5 -16q0 -4 -0.5 -8.5t-1.5 -9.5l-90 -426q-3 -10 -10 -16.5t-18 -6.5h-72
q-11 0 -18 6.5t-10 16.5l-66 363l-74 -363q-3 -10 -10 -16.5t-18 -6.5h-72q-11 0 -18 6.5t-10 16.5z" />
    <glyph glyph-name="x" unicode="x" horiz-adv-x="393" 
d="M237 251l126 -213q2 -4 4.5 -9.5t2.5 -10.5q0 -18 -24 -18h-22q-10 0 -16.5 2.5t-13.5 14.5l-98 171l-95 -171q-7 -12 -12.5 -14.5t-14.5 -2.5h-28q-23 0 -23 19q0 5 2.5 10l4.5 9l125 211l-119 204q-4 7 -5.5 11t-1.5 9q0 7 5 12t14 5h30q9 0 15.5 -3t12.5 -14l90 -163
l88 158q12 22 30 22h31q9 0 14 -5t5 -12q0 -5 -1.5 -9t-5.5 -11z" />
    <glyph glyph-name="y" unicode="y" horiz-adv-x="389" 
d="M195 65q6 0 7 6l88 399q4 20 26 20h24q25 0 25 -23q0 -8 -3 -20l-126 -528q-8 -32 -18 -52.5t-24 -34.5q-32 -32 -99 -32q-24 0 -38 1.5t-21.5 4.5t-10 8t-2.5 13v16q0 12 5.5 16.5t13.5 4.5t19 -1.5t38 -1.5q33 0 47.5 16t23.5 56l15 67h-29q-11 0 -18.5 6.5t-9.5 16.5
l-102 428q-3 14 -3 19q0 20 24 20h27q20 0 25 -20l89 -399q2 -6 7 -6z" />
    <glyph glyph-name="z" unicode="z" horiz-adv-x="374" 
d="M341 23q0 -10 -7 -16.5t-16 -6.5h-267q-21 0 -21 21v22q0 9 1.5 14t5.5 12l229 358h-206q-9 0 -16 6.5t-7 16.5v18q0 10 7 16t16 6h257q11 0 17.5 -6t6.5 -16v-21q0 -9 -2 -15.5t-5 -11.5l-226 -355h210q9 0 16 -7t7 -16v-19z" />
    <glyph glyph-name="braceleft" unicode="{" horiz-adv-x="331" 
d="M171 41q0 -44 22 -62.5t58 -18.5h32q10 0 16.5 -6.5t6.5 -16.5v-22q0 -9 -6.5 -15.5t-16.5 -6.5h-32q-85 0 -119 41.5t-34 111.5v225h-58q-10 0 -16.5 6.5t-6.5 15.5v22q0 10 6.5 16.5t16.5 6.5h58v209q0 70 34 111.5t119 41.5h32q10 0 16.5 -6.5t6.5 -15.5v-22
q0 -10 -6.5 -16.5t-16.5 -6.5h-32q-36 0 -58 -18.5t-22 -62.5v-180q0 -28 -12 -44.5t-28 -23.5q16 -6 28 -22.5t12 -43.5v-197z" />
    <glyph glyph-name="bar" unicode="|" horiz-adv-x="193" 
d="M133 -77q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v854q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-854z" />
    <glyph glyph-name="braceright" unicode="}" horiz-adv-x="331" 
d="M160 238q0 27 12 43.5t28 22.5q-16 7 -28 23.5t-12 44.5v180q0 44 -22 62.5t-58 18.5h-32q-10 0 -16.5 6.5t-6.5 16.5v22q0 9 6.5 15.5t16.5 6.5h32q85 0 119 -41.5t34 -111.5v-209h58q10 0 16.5 -6.5t6.5 -16.5v-22q0 -9 -6.5 -15.5t-16.5 -6.5h-58v-225
q0 -70 -34 -111.5t-119 -41.5h-32q-10 0 -16.5 6.5t-6.5 15.5v22q0 10 6.5 16.5t16.5 6.5h32q36 0 58 18.5t22 62.5v197z" />
    <glyph glyph-name="asciitilde" unicode="~" horiz-adv-x="466" 
d="M121 345q-11 0 -17 -5t-6 -20q0 -22 -21 -22h-14q-15 0 -19 7.5t-4 21.5q0 34 17.5 58.5t55.5 24.5q17 0 51 -7.5t71 -16.5t68.5 -16.5t43.5 -7.5t16.5 7t4.5 23q0 21 21 21h14q15 0 19 -7.5t4 -21.5q0 -17 -3 -32t-11 -26q-9 -15 -26 -21t-33 -6q-19 0 -53.5 7t-70.5 16
t-66.5 16t-41.5 7z" />
    <glyph glyph-name="exclamdown" unicode="&#xa1;" horiz-adv-x="211" 
d="M157 599q0 -10 -6.5 -16t-16.5 -6h-57q-9 0 -16 6t-7 16v78q0 10 7 16.5t16 6.5h57q10 0 16.5 -6.5t6.5 -16.5v-78zM115 506q11 0 17 -6.5t6 -16.5l12 -418q1 -17 1 -28.5v-16.5q0 -9 -6 -14.5t-15 -5.5h-49q-9 0 -15 5.5t-6 14.5v16.5t1 28.5l12 418q0 10 6 16.5t17 6.5
h19z" />
    <glyph glyph-name="cent" unicode="&#xa2;" horiz-adv-x="401" 
d="M170 0q-58 4 -92 42t-34 105v196q0 66 34 104.5t92 41.5v79q0 10 6.5 16.5t16.5 6.5h20q10 0 16.5 -6.5t6.5 -16.5v-79q57 -4 90.5 -42t33.5 -104v-18q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v20q0 36 -18 58t-54 22h-26q-36 0 -54 -22t-18 -58v-200
q0 -36 18 -58t54 -22h26q36 0 54 22t18 58v40q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-38q0 -66 -33.5 -104t-90.5 -43v-77q0 -10 -6.5 -16.5t-16.5 -6.5h-20q-10 0 -16.5 6.5t-6.5 16.5v77z" />
    <glyph glyph-name="sterling" unicode="&#xa3;" horiz-adv-x="494" 
d="M193 410h153q10 0 16.5 -6.5t6.5 -16.5v-22q0 -9 -6.5 -15.5t-16.5 -6.5h-153v-80h153q10 0 16.5 -6.5t6.5 -16.5v-22q0 -9 -6.5 -15.5t-16.5 -6.5h-153v-129h238q10 0 16.5 -6.5t6.5 -16.5v-22q0 -9 -6.5 -15.5t-16.5 -6.5h-350q-10 0 -16.5 6.5t-6.5 15.5v22
q0 10 6.5 16.5t16.5 6.5h39v129h-59q-10 0 -16.5 6.5t-6.5 15.5v22q0 10 6.5 16.5t16.5 6.5h59v80h-59q-10 0 -16.5 6.5t-6.5 15.5v22q0 10 6.5 16.5t16.5 6.5h59v143q0 36 11.5 63t31 46t46.5 28.5t58 9.5h39q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-50
q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-9 0 -16 6.5t-7 16.5v44q0 41 -19.5 62.5t-58.5 21.5h-31q-39 0 -58.5 -21.5t-19.5 -62.5v-139z" />
    <glyph glyph-name="currency" unicode="&#xa4;" horiz-adv-x="646" 
d="M579 350q0 -89 -53 -160l81 -78l-49 -49l-81 79q-35 -24 -74 -38t-80 -14t-80 14t-74 38l-81 -79l-49 49l81 78q-53 71 -53 160t53 160l-81 78l49 49l81 -79q35 24 74 38t80 14t80 -14t74 -38l81 79l49 -49l-81 -78q53 -71 53 -160zM133 350q0 -41 13.5 -77.5t38.5 -63.5
t60 -42.5t78 -15.5t78 15.5t60 42.5t38.5 63.5t13.5 77.5t-13.5 77.5t-38.5 63.5t-60 42.5t-78 15.5t-78 -15.5t-60 -42.5t-38.5 -63.5t-13.5 -77.5z" />
    <glyph glyph-name="yen" unicode="&#xa5;" horiz-adv-x="463" 
d="M274 241v-218q0 -10 -7 -16.5t-16 -6.5h-27q-9 0 -16 6.5t-7 16.5v218h-138q-10 0 -16.5 6.5t-6.5 15.5v19q0 10 6.5 16.5t16.5 6.5h115l-37 103h-78q-10 0 -16.5 6.5t-6.5 15.5v19q0 10 6.5 16.5t16.5 6.5h55l-65 184q-6 17 -6 27q0 17 19 17h34q18 0 25 -22l114 -340
l110 340q6 22 26 22h33q18 0 18 -17q0 -5 -2 -13.5t-7 -23.5l-62 -174h40q10 0 16.5 -6.5t6.5 -16.5v-19q0 -9 -6.5 -15.5t-16.5 -6.5h-63l-36 -103h99q10 0 16.5 -6.5t6.5 -16.5v-19q0 -9 -6.5 -15.5t-16.5 -6.5h-121z" />
    <glyph glyph-name="brokenbar" unicode="&#xa6;" horiz-adv-x="193" 
d="M133 413q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v364q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-364zM133 -77q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v364q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5
v-364z" />
    <glyph glyph-name="section" unicode="&#xa7;" horiz-adv-x="431" 
d="M355 320q9 -5 13 -12t4 -19v-31q0 -21 -17 -21q-7 0 -14 4l-213 119q-38 21 -58 52t-20 88v54q0 36 11.5 63t31 45.5t46.5 28t58 9.5h37q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-70q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-9 0 -16 6.5t-7 16.5v64q0 41 -19.5 62.5
t-58.5 21.5h-35q-36 0 -54 -23t-18 -59v-41q0 -28 10 -51t46 -43zM76 215q-9 5 -13 12t-4 19v31q0 21 17 21q7 0 14 -4l213 -119q38 -21 58 -52t20 -88v-66q0 -36 -11.5 -63t-31 -45.5t-46.5 -28t-58 -9.5h-37q-31 0 -58 9.5t-46.5 28t-31 45.5t-11.5 62v70q0 10 6.5 16.5
t16.5 6.5h27q9 0 16 -6.5t7 -16.5v-64q0 -41 19.5 -62.5t58.5 -21.5h35q36 0 54 23t18 59v53q0 28 -10 51t-46 43z" />
    <glyph glyph-name="dieresis" unicode="&#xa8;" horiz-adv-x="344" 
d="M143 590q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-9 0 -16 6.5t-7 16.5v48q0 10 7 16t16 6h37q10 0 16.5 -6t6.5 -16v-48zM284 590q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-9 0 -16 6.5t-7 16.5v48q0 10 7 16t16 6h37q10 0 16.5 -6t6.5 -16v-48z" />
    <glyph glyph-name="copyright" unicode="&#xa9;" horiz-adv-x="671" 
d="M488 277q0 -35 -11.5 -61.5t-31 -44.5t-46.5 -27t-58 -9h-12q-31 0 -58 10t-46.5 28.5t-31 46t-11.5 62.5v137q0 35 11.5 62.5t31 46t46.5 28.5t58 10h12q31 0 58 -9t46.5 -27t31 -44.5t11.5 -61.5v-10q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-9 0 -16 6.5t-7 16.5v6
q0 36 -18 57.5t-54 21.5h-16q-36 0 -54 -24t-18 -60v-129q0 -36 18 -60t54 -24h16q36 0 54 21.5t18 57.5v6q0 10 7 16.5t16 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-10zM51 473q0 48 17.5 89.5t48 72t73 48t92.5 17.5h107q50 0 92.5 -17.5t73 -48t48 -72t17.5 -89.5v-246
q0 -48 -17.5 -89.5t-48 -72t-73 -48t-92.5 -17.5h-107q-50 0 -92.5 17.5t-73 48t-48 72t-17.5 89.5v246zM120 232q0 -36 12.5 -66.5t34.5 -53t53 -35t67 -12.5h97q36 0 67 12.5t53 35t34.5 53t12.5 66.5v236q0 36 -12.5 66.5t-34.5 53t-53 35t-67 12.5h-97q-36 0 -67 -12.5
t-53 -35t-34.5 -53t-12.5 -66.5v-236z" />
    <glyph glyph-name="ordfeminine" unicode="&#xaa;" horiz-adv-x="326" 
d="M277 389q0 -23 -23 -23h-12q-11 0 -18.5 4.5t-7.5 18.5v7q-9 -17 -29 -23.5t-43 -6.5h-15q-42 0 -66 25t-24 62v16q0 39 24 64.5t74 25.5h77v39q0 20 -9.5 32t-29.5 12h-23q-41 0 -41 -38v-6q0 -23 -23 -23h-17q-23 0 -23 23v5q0 49 27 73t72 24h33q45 0 71 -27t26 -76
v-208zM175 424q20 0 29.5 12t9.5 32v37h-72q-20 0 -29.5 -11.5t-9.5 -25.5v-8q0 -15 7.5 -25.5t27.5 -10.5h37z" />
    <glyph glyph-name="guillemotleft" unicode="&#xab;" horiz-adv-x="462" 
d="M23 245q0 7 5 15l139 210q9 14 16.5 17t21.5 3h22q8 0 14 -5.5t6 -13.5q0 -9 -6 -18l-139 -208l139 -208q6 -9 6 -18q0 -8 -6 -13.5t-14 -5.5h-22q-14 0 -21.5 3t-16.5 17l-139 210q-5 8 -5 15zM206 245q0 7 5 15l139 210q9 14 16.5 17t21.5 3h22q8 0 14 -5.5t6 -13.5
q0 -9 -6 -18l-139 -208l139 -208q6 -9 6 -18q0 -8 -6 -13.5t-14 -5.5h-22q-14 0 -21.5 3t-16.5 17l-139 210q-5 8 -5 15z" />
    <glyph glyph-name="logicalnot" unicode="&#xac;" horiz-adv-x="500" 
d="M443 179q0 -10 -6.5 -16.5t-15.5 -6.5h-28q-10 0 -16.5 6.5t-6.5 16.5v145h-311q-10 0 -16.5 6.5t-6.5 15.5v22q0 10 6.5 16.5t16.5 6.5h361q10 0 16.5 -6.5t6.5 -16.5v-189z" />
    <glyph glyph-name="registered" unicode="&#xae;" horiz-adv-x="671" 
d="M51 473q0 48 17.5 89.5t48 72t73 48t92.5 17.5h107q50 0 92.5 -17.5t73 -48t48 -72t17.5 -89.5v-246q0 -48 -17.5 -89.5t-48 -72t-73 -48t-92.5 -17.5h-107q-50 0 -92.5 17.5t-73 48t-48 72t-17.5 89.5v246zM120 232q0 -36 12.5 -66.5t34.5 -53t53 -35t67 -12.5h97
q36 0 67 12.5t53 35t34.5 53t12.5 66.5v236q0 36 -12.5 66.5t-34.5 53t-53 35t-67 12.5h-97q-36 0 -67 -12.5t-53 -35t-34.5 -53t-12.5 -66.5v-236zM272 170q0 -10 -6 -16.5t-17 -6.5h-24q-11 0 -17 6.5t-6 16.5v371q0 10 7 16t16 6h126q63 0 95 -33t32 -79
q0 -12 -2.5 -26.5t-9.5 -29t-19.5 -27t-33.5 -20.5q39 -14 50.5 -41.5t11.5 -59.5v-39q0 -13 2.5 -25.5t2.5 -18.5q0 -17 -21 -17h-15q-21 0 -30 8.5t-9 29.5v51q0 42 -14.5 61t-50.5 19h-68v-146zM334 377q36 0 55 15t19 50q0 29 -15 43t-51 14h-70v-122h62z" />
    <glyph glyph-name="macron" unicode="&#xaf;" horiz-adv-x="368" 
d="M285 630q10 0 16.5 -6.5t6.5 -16.5v-19q0 -9 -6.5 -15.5t-16.5 -6.5h-202q-10 0 -16.5 6.5t-6.5 15.5v19q0 10 6.5 16.5t16.5 6.5h202z" />
    <glyph glyph-name="degree" unicode="&#xb0;" horiz-adv-x="324" 
d="M282 482q0 -55 -30 -89t-85 -34h-10q-55 0 -85 34t-30 89v95q0 55 30 89t85 34h10q55 0 85 -34t30 -89v-95zM217 588q0 24 -13.5 41t-41.5 17q-29 0 -42 -17t-13 -41v-117q0 -24 13 -41t42 -17q28 0 41.5 17t13.5 41v117z" />
    <glyph glyph-name="plusminus" unicode="&#xb1;" horiz-adv-x="480" 
d="M274 391h144q10 0 16.5 -6.5t6.5 -16.5v-19q0 -9 -6.5 -15.5t-16.5 -6.5h-144v-148q0 -10 -6.5 -16.5t-15.5 -6.5h-23q-10 0 -16.5 6.5t-6.5 16.5v148h-144q-10 0 -16.5 6.5t-6.5 15.5v19q0 10 6.5 16.5t16.5 6.5h144v161q0 10 6.5 16.5t16.5 6.5h23q9 0 15.5 -6.5
t6.5 -16.5v-161zM418 64q10 0 16.5 -6.5t6.5 -16.5v-19q0 -9 -6.5 -15.5t-16.5 -6.5h-356q-10 0 -16.5 6.5t-6.5 15.5v19q0 10 6.5 16.5t16.5 6.5h356z" />
    <glyph glyph-name="twosuperior" unicode="&#xb2;" horiz-adv-x="284" 
d="M186 619q0 29 -27 29h-33q-27 0 -27 -30v-35q0 -8 -5.5 -14.5t-14.5 -6.5h-13q-8 0 -14.5 6.5t-6.5 14.5v38q0 39 22.5 59t56.5 20h37q34 0 56.5 -20t22.5 -59v-22q0 -14 -3 -32.5t-23 -35.5l-100 -89q-6 -8 -9 -12.5t-3 -11.5v-9h117q21 0 21 -20v-11q0 -20 -21 -20
h-151q-8 0 -14.5 5.5t-6.5 14.5v32q0 21 4 35t22 31l100 89q5 5 9 14t4 15v25z" />
    <glyph glyph-name="threesuperior" unicode="&#xb3;" horiz-adv-x="285" 
d="M240 436q0 -38 -23 -58t-57 -20h-39q-34 0 -57 20t-23 58v19q0 22 22 22h12q8 0 14.5 -6t6.5 -16v-16q0 -30 27 -30h36q27 0 27 30v36q0 13 -5 21.5t-17 8.5h-33q-21 0 -21 21v11q0 8 5.5 14t15.5 6h31q12 0 17 8.5t5 21.5v32q0 29 -27 29h-33q-27 0 -27 -30v-12
q0 -8 -5.5 -14.5t-14.5 -6.5h-13q-8 0 -14.5 6.5t-6.5 14.5v15q0 39 22.5 59t56.5 20h37q34 0 56.5 -20t22.5 -59v-33q0 -38 -32 -56q15 -8 24.5 -23.5t9.5 -33.5v-39z" />
    <glyph glyph-name="acute" unicode="&#xb4;" horiz-adv-x="232" 
d="M89 682q4 18 23 18h40q20 0 20 -17q0 -9 -5 -20l-47 -108q-3 -8 -8.5 -13t-16.5 -5h-17q-18 0 -18 18q0 2 1 8t3 14z" />
    <glyph glyph-name="mu" unicode="&#xb5;" horiz-adv-x="423" 
d="M62 467q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-322q0 -36 18 -58t54 -22h19q36 0 54.5 22t18.5 58v322q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-444q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v19q-17 -20 -36.5 -31
t-53.5 -11q-25 0 -46.5 7.5t-31.5 21.5v-206q0 -10 -6.5 -16.5t-16.5 -6.5h-23q-10 0 -16.5 6.5t-6.5 16.5v644z" />
    <glyph glyph-name="paragraph" unicode="&#xb6;" horiz-adv-x="503" 
d="M200 288h-28q-31 0 -55.5 10.5t-42 28.5t-26.5 42.5t-9 51.5v146q0 27 9 51.5t26.5 42.5t42 28.5t55.5 10.5h78q9 0 16 -6t7 -16v-655q0 -10 -7 -16.5t-16 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v265zM440 23q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5
v654q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-654z" />
    <glyph glyph-name="periodcentered" unicode="&#xb7;" horiz-adv-x="195" 
d="M149 327q0 -10 -6.5 -16.5t-16.5 -6.5h-57q-9 0 -16 6.5t-7 16.5v68q0 10 7 16t16 6h57q10 0 16.5 -6t6.5 -16v-68z" />
    <glyph glyph-name="cedilla" unicode="&#xb8;" horiz-adv-x="340" 
d="M279 -158q0 -44 -29 -69.5t-75 -25.5h-8q-51 0 -78.5 22.5t-27.5 66.5v11q0 8 5.5 13.5t13.5 5.5h27q7 0 12.5 -5t5.5 -13v-9q0 -35 33 -35h21q17 0 26.5 8t9.5 27v23q0 17 -11 26.5t-34 9.5h-49q-10 0 -17.5 5.5t-7.5 16.5q0 7 1.5 13.5t9.5 24.5l26 61h56l-27 -67h14
q55 0 78.5 -25.5t24.5 -64.5v-20z" />
    <glyph glyph-name="onesuperior" unicode="&#xb9;" horiz-adv-x="253" 
d="M59 358q-21 0 -21 20v11q0 20 21 20h48v201h-45q-14 0 -18 5t-4 15v11q0 20 21 20h31q10 0 12 7.5t4 15.5t6 12t15 4h12q12 0 16.5 -5t4.5 -15v-271h44q21 0 21 -20v-11q0 -20 -21 -20h-147z" />
    <glyph glyph-name="ordmasculine" unicode="&#xba;" horiz-adv-x="319" 
d="M44 597q0 49 26 76t71 27h37q45 0 71 -27t26 -76v-128q0 -49 -26 -76t-71 -27h-37q-45 0 -71 27t-26 76v128zM212 598q0 20 -9.5 32t-29.5 12h-27q-20 0 -29 -12t-9 -32v-130q0 -20 9 -32t29 -12h27q20 0 29.5 12t9.5 32v130z" />
    <glyph glyph-name="guillemotright" unicode="&#xbb;" horiz-adv-x="462" 
d="M256 245q0 -7 -5 -15l-139 -210q-9 -14 -16.5 -17t-21.5 -3h-22q-8 0 -14 5.5t-6 13.5q0 9 6 18l139 208l-139 208q-6 9 -6 18q0 8 6 13.5t14 5.5h22q14 0 21.5 -3t16.5 -17l139 -210q5 -8 5 -15zM439 245q0 -7 -5 -15l-139 -210q-9 -14 -16.5 -17t-21.5 -3h-22
q-8 0 -14 5.5t-6 13.5q0 9 6 18l139 208l-139 208q-6 9 -6 18q0 8 6 13.5t14 5.5h22q14 0 21.5 -3t16.5 -17l139 -210q5 -8 5 -15z" />
    <glyph glyph-name="onequarter" unicode="&#xbc;" horiz-adv-x="633" 
d="M532 683q5 8 11.5 12.5t16.5 4.5h15q8 0 12 -4.5t4 -10.5q0 -8 -5 -15l-463 -655q-5 -8 -11 -11.5t-17 -3.5h-14q-8 0 -12 5t-4 11q0 7 5 14zM58 358q-21 0 -21 20v11q0 20 21 20h48v201h-45q-14 0 -18 5t-4 15v11q0 20 21 20h31q10 0 12 7.5t4 15.5t6 12t15 4h12
q12 0 16.5 -5t4.5 -15v-271h44q21 0 21 -20v-11q0 -20 -21 -20h-147zM482 326q3 5 8 10.5t15 5.5h30q8 0 14.5 -6.5t6.5 -14.5v-177h28q8 0 14 -4.5t6 -16.5v-7q0 -21 -20 -21h-28v-75q0 -9 -6.5 -14.5t-14.5 -5.5h-12q-8 0 -14.5 5.5t-6.5 14.5v75h-117q-19 0 -19 21v19
q0 8 4 14zM502 272l-81 -128h81v128z" />
    <glyph glyph-name="onehalf" unicode="&#xbd;" horiz-adv-x="647" 
d="M532 683q5 8 11.5 12.5t16.5 4.5h15q8 0 12 -4.5t4 -10.5q0 -8 -5 -15l-463 -655q-5 -8 -11 -11.5t-17 -3.5h-14q-8 0 -12 5t-4 11q0 7 5 14zM58 358q-21 0 -21 20v11q0 20 21 20h48v201h-45q-14 0 -18 5t-4 15v11q0 20 21 20h31q10 0 12 7.5t4 15.5t6 12t15 4h12
q12 0 16.5 -5t4.5 -15v-271h44q21 0 21 -20v-11q0 -20 -21 -20h-147zM552 261q0 29 -27 29h-33q-27 0 -27 -30v-35q0 -8 -5.5 -14.5t-14.5 -6.5h-13q-8 0 -14.5 6.5t-6.5 14.5v38q0 39 22.5 59t56.5 20h37q34 0 56.5 -20t22.5 -59v-22q0 -14 -3 -32.5t-23 -35.5l-100 -89
q-6 -8 -9 -12.5t-3 -11.5v-9h117q21 0 21 -20v-11q0 -20 -21 -20h-151q-8 0 -14.5 5.5t-6.5 14.5v32q0 21 4 35t22 31l100 89q5 5 9 14t4 15v25z" />
    <glyph glyph-name="threequarters" unicode="&#xbe;" horiz-adv-x="626" 
d="M239 436q0 -38 -23 -58t-57 -20h-39q-34 0 -57 20t-23 58v19q0 22 22 22h12q8 0 14.5 -6t6.5 -16v-16q0 -30 27 -30h36q27 0 27 30v36q0 13 -5 21.5t-17 8.5h-33q-21 0 -21 21v11q0 8 5.5 14t15.5 6h31q12 0 17 8.5t5 21.5v32q0 29 -27 29h-33q-27 0 -27 -30v-12
q0 -8 -5.5 -14.5t-14.5 -6.5h-13q-8 0 -14.5 6.5t-6.5 14.5v15q0 39 22.5 59t56.5 20h37q34 0 56.5 -20t22.5 -59v-33q0 -38 -32 -56q15 -8 24.5 -23.5t9.5 -33.5v-39zM525 683q5 8 11.5 12.5t16.5 4.5h15q8 0 12 -4.5t4 -10.5q0 -8 -5 -15l-463 -655q-5 -8 -11 -11.5
t-17 -3.5h-14q-8 0 -12 5t-4 11q0 7 5 14zM475 326q3 5 8 10.5t15 5.5h30q8 0 14.5 -6.5t6.5 -14.5v-177h28q8 0 14 -4.5t6 -16.5v-7q0 -21 -20 -21h-28v-75q0 -9 -6.5 -14.5t-14.5 -5.5h-12q-8 0 -14.5 5.5t-6.5 14.5v75h-117q-19 0 -19 21v19q0 8 4 14zM495 272l-81 -128
h81v128z" />
    <glyph glyph-name="questiondown" unicode="&#xbf;" horiz-adv-x="389" 
d="M163 483q0 10 6.5 16.5t16.5 6.5h27q9 0 16 -6.5t7 -16.5v-23q0 -20 -1 -36.5t-5 -32.5t-12.5 -31.5t-23.5 -33.5l-59 -71q-11 -14 -20.5 -35.5t-9.5 -38.5v-31q0 -40 19.5 -61.5t58.5 -21.5h25q39 0 58.5 21.5t19.5 62.5v44q0 10 7 16.5t16 6.5h27q10 0 16.5 -6.5
t6.5 -16.5v-50q0 -35 -11.5 -62t-31 -45.5t-46.5 -28t-58 -9.5h-33q-31 0 -58 9.5t-46.5 28t-31 45.5t-11.5 62v27q0 27 8 57t42 71l56 69q17 20 21.5 38.5t3.5 35.5v40zM146 677q0 10 6.5 16.5t16.5 6.5h57q9 0 16 -6.5t7 -16.5v-78q0 -10 -7 -16t-16 -6h-57q-10 0 -16.5 6
t-6.5 16v78z" />
    <glyph glyph-name="Agrave" unicode="&#xc0;" 
d="M286 198h-158l-32 -178q-3 -20 -23 -20h-29q-9 0 -15 4t-6 14q0 5 3 21l124 638q2 10 8 16.5t16 6.5h72q10 0 16 -6.5t8 -16.5l121 -631q2 -8 2.5 -15.5t0.5 -10.5q0 -20 -20 -20h-32q-20 0 -23 20zM140 261h135l-67 370zM236 882l25 -105q2 -8 3 -14t1 -8q0 -18 -18 -18
h-17q-11 0 -16.5 5t-8.5 13l-47 108q-5 11 -5 20q0 17 20 17h40q19 0 23 -18z" />
    <glyph glyph-name="Aacute" unicode="&#xc1;" 
d="M286 198h-158l-32 -178q-3 -20 -23 -20h-29q-9 0 -15 4t-6 14q0 5 3 21l124 638q2 10 8 16.5t16 6.5h72q10 0 16 -6.5t8 -16.5l121 -631q2 -8 2.5 -15.5t0.5 -10.5q0 -20 -20 -20h-32q-20 0 -23 20zM140 261h135l-67 370zM182 882q4 18 23 18h40q20 0 20 -17q0 -9 -5 -20
l-47 -108q-3 -8 -8.5 -13t-16.5 -5h-17q-18 0 -18 18q0 2 1 8t3 14z" />
    <glyph glyph-name="Acircumflex" unicode="&#xc2;" 
d="M286 198h-158l-32 -178q-3 -20 -23 -20h-29q-9 0 -15 4t-6 14q0 5 3 21l124 638q2 10 8 16.5t16 6.5h72q10 0 16 -6.5t8 -16.5l121 -631q2 -8 2.5 -15.5t0.5 -10.5q0 -20 -20 -20h-32q-20 0 -23 20zM140 261h135l-67 370zM142 737h-26q-17 0 -17 16q0 7 5 15l67 118
q8 14 24 14h26q16 0 24 -14l67 -118q5 -8 5 -15q0 -16 -17 -16h-26q-13 0 -17 4.5t-8 12.5l-41 80l-41 -80q-4 -8 -8 -12.5t-17 -4.5z" />
    <glyph glyph-name="Atilde" unicode="&#xc3;" 
d="M286 198h-158l-32 -178q-3 -20 -23 -20h-29q-9 0 -15 4t-6 14q0 5 3 21l124 638q2 10 8 16.5t16 6.5h72q10 0 16 -6.5t8 -16.5l121 -631q2 -8 2.5 -15.5t0.5 -10.5q0 -20 -20 -20h-32q-20 0 -23 20zM140 261h135l-67 370zM156 799q-10 0 -15.5 -3.5t-5.5 -17.5
q0 -20 -20 -20h-14q-14 0 -18 7t-4 20q0 32 13 51q9 14 23.5 20t29.5 6q16 0 33 -6.5t33 -14.5t30 -14.5t24 -6.5q11 0 15 5.5t4 19.5q0 20 20 20h14q14 0 18 -7t4 -20q0 -35 -13 -54q-9 -14 -23.5 -19.5t-29.5 -5.5q-18 0 -35.5 6.5t-32.5 14t-28 13.5t-22 6z" />
    <glyph glyph-name="Adieresis" unicode="&#xc4;" 
d="M286 198h-158l-32 -178q-3 -20 -23 -20h-29q-9 0 -15 4t-6 14q0 5 3 21l124 638q2 10 8 16.5t16 6.5h72q10 0 16 -6.5t8 -16.5l121 -631q2 -8 2.5 -15.5t0.5 -10.5q0 -20 -20 -20h-32q-20 0 -23 20zM140 261h135l-67 370zM179 790q0 -10 -6.5 -16.5t-16.5 -6.5h-37
q-9 0 -16 6.5t-7 16.5v48q0 10 7 16t16 6h37q10 0 16.5 -6t6.5 -16v-48zM320 790q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-9 0 -16 6.5t-7 16.5v48q0 10 7 16t16 6h37q10 0 16.5 -6t6.5 -16v-48z" />
    <glyph glyph-name="Aring" unicode="&#xc5;" 
d="M320 736q0 -22 -12 -45.5t-36 -33.5l118 -611q2 -8 2.5 -15.5t0.5 -10.5q0 -20 -20 -20h-32q-20 0 -23 20l-33 178h-158l-32 -178q-3 -20 -23 -20h-28q-20 0 -20 20q0 3 0.5 10.5t2.5 15.5l118 610q-25 8 -37 32.5t-12 47.5v54q0 17 6 33t17.5 28.5t29.5 20.5t42 8h34
q24 0 42 -8t29.5 -20.5t17.5 -28.5t6 -33v-54zM139 261h135l-67 370zM159 747q0 -26 10.5 -36t26.5 -11h20q17 0 29 10.5t12 36.5v32q0 26 -12 36.5t-29 10.5h-16q-17 0 -29 -10.5t-12 -36.5v-32z" />
    <glyph glyph-name="AE" unicode="&#xc6;" horiz-adv-x="636" 
d="M318 0q-11 0 -17 6.5t-7 16.5v194h-145l-57 -199q-6 -18 -23 -18h-34q-19 0 -19 18q0 5 4 20l192 639q3 10 8.5 16.5t15.5 6.5h338q10 0 16.5 -6.5t6.5 -15.5v-22q0 -10 -6.5 -16.5t-16.5 -6.5h-207v-242h177q10 0 16.5 -6.5t6.5 -16.5v-22q0 -9 -6.5 -15.5t-16.5 -6.5
h-177v-257h217q10 0 16.5 -6.5t6.5 -15.5v-22q0 -10 -6.5 -16.5t-16.5 -6.5h-266zM169 284h125v349h-24z" />
    <glyph glyph-name="Ccedilla" unicode="&#xc7;" horiz-adv-x="439" 
d="M334 -158q0 -44 -29 -69.5t-75 -25.5h-8q-51 0 -78.5 22.5t-27.5 66.5v11q0 8 5.5 13.5t13.5 5.5h27q7 0 12.5 -5t5.5 -13v-9q0 -35 33 -35h21q17 0 26.5 8t9.5 27v23q0 17 -11 26.5t-34 9.5h-49q-10 0 -17.5 5.5t-7.5 16.5q0 7 1.5 13.5t9.5 24.5l18 43q-54 8 -88 44.5
t-34 99.5v410q0 35 11.5 62t31 45.5t46.5 28t58 9.5h40q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-70q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-9 0 -16 6.5t-7 16.5v64q0 41 -19.5 62.5t-58.5 21.5h-32q-39 0 -58.5 -21.5t-19.5 -62.5v-398q0 -84 78 -84h32q39 0 58.5 21.5
t19.5 62.5v84q0 10 7 16.5t16 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-90q0 -35 -11.5 -62t-31 -45.5t-46.5 -28t-58 -9.5h-9l-19 -48h14q55 0 78.5 -25.5t24.5 -64.5v-20z" />
    <glyph glyph-name="Egrave" unicode="&#xc8;" horiz-adv-x="404" 
d="M86 0q-9 0 -16 6.5t-7 16.5v655q0 9 7 15.5t16 6.5h257q10 0 16.5 -6.5t6.5 -15.5v-22q0 -10 -6.5 -16.5t-16.5 -6.5h-207v-242h177q10 0 16.5 -6.5t6.5 -16.5v-22q0 -9 -6.5 -15.5t-16.5 -6.5h-177v-257h217q10 0 16.5 -6.5t6.5 -15.5v-22q0 -10 -6.5 -16.5t-16.5 -6.5
h-267zM234 882l25 -105q2 -8 3 -14t1 -8q0 -18 -18 -18h-17q-11 0 -16.5 5t-8.5 13l-47 108q-5 11 -5 20q0 17 20 17h40q19 0 23 -18z" />
    <glyph glyph-name="Eacute" unicode="&#xc9;" horiz-adv-x="404" 
d="M86 0q-9 0 -16 6.5t-7 16.5v655q0 9 7 15.5t16 6.5h257q10 0 16.5 -6.5t6.5 -15.5v-22q0 -10 -6.5 -16.5t-16.5 -6.5h-207v-242h177q10 0 16.5 -6.5t6.5 -16.5v-22q0 -9 -6.5 -15.5t-16.5 -6.5h-177v-257h217q10 0 16.5 -6.5t6.5 -15.5v-22q0 -10 -6.5 -16.5t-16.5 -6.5
h-267zM209 882q4 18 23 18h40q20 0 20 -17q0 -9 -5 -20l-47 -108q-3 -8 -8.5 -13t-16.5 -5h-17q-18 0 -18 18q0 2 1 8t3 14z" />
    <glyph glyph-name="Ecircumflex" unicode="&#xca;" horiz-adv-x="404" 
d="M86 0q-9 0 -16 6.5t-7 16.5v655q0 9 7 15.5t16 6.5h257q10 0 16.5 -6.5t6.5 -15.5v-22q0 -10 -6.5 -16.5t-16.5 -6.5h-207v-242h177q10 0 16.5 -6.5t6.5 -16.5v-22q0 -9 -6.5 -15.5t-16.5 -6.5h-177v-257h217q10 0 16.5 -6.5t6.5 -15.5v-22q0 -10 -6.5 -16.5t-16.5 -6.5
h-267zM141 737h-26q-17 0 -17 16q0 7 5 15l67 118q8 14 24 14h26q16 0 24 -14l67 -118q5 -8 5 -15q0 -16 -17 -16h-26q-13 0 -17 4.5t-8 12.5l-41 80l-41 -80q-4 -8 -8 -12.5t-17 -4.5z" />
    <glyph glyph-name="Edieresis" unicode="&#xcb;" horiz-adv-x="404" 
d="M86 0q-9 0 -16 6.5t-7 16.5v655q0 9 7 15.5t16 6.5h257q10 0 16.5 -6.5t6.5 -15.5v-22q0 -10 -6.5 -16.5t-16.5 -6.5h-207v-242h177q10 0 16.5 -6.5t6.5 -16.5v-22q0 -9 -6.5 -15.5t-16.5 -6.5h-177v-257h217q10 0 16.5 -6.5t6.5 -15.5v-22q0 -10 -6.5 -16.5t-16.5 -6.5
h-267zM188 790q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-9 0 -16 6.5t-7 16.5v48q0 10 7 16t16 6h37q10 0 16.5 -6t6.5 -16v-48zM329 790q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-9 0 -16 6.5t-7 16.5v48q0 10 7 16t16 6h37q10 0 16.5 -6t6.5 -16v-48z" />
    <glyph glyph-name="Igrave" unicode="&#xcc;" horiz-adv-x="200" 
d="M137 23q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v654q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-654zM128 882l25 -105q2 -8 3 -14t1 -8q0 -18 -18 -18h-17q-11 0 -16.5 5t-8.5 13l-47 108q-5 11 -5 20q0 17 20 17h40q19 0 23 -18z" />
    <glyph glyph-name="Iacute" unicode="&#xcd;" horiz-adv-x="200" 
d="M137 23q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v654q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-654zM74 882q4 18 23 18h40q20 0 20 -17q0 -9 -5 -20l-47 -108q-3 -8 -8.5 -13t-16.5 -5h-17q-18 0 -18 18q0 2 1 8t3 14z" />
    <glyph glyph-name="Icircumflex" unicode="&#xce;" horiz-adv-x="200" 
d="M137 23q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v654q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-654zM35 737h-26q-17 0 -17 16q0 7 5 15l67 118q8 14 24 14h26q16 0 24 -14l67 -118q5 -8 5 -15q0 -16 -17 -16h-26q-13 0 -17 4.5t-8 12.5
l-41 80l-41 -80q-4 -8 -8 -12.5t-17 -4.5z" />
    <glyph glyph-name="Idieresis" unicode="&#xcf;" horiz-adv-x="200" 
d="M137 23q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v654q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-654zM72 790q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-9 0 -16 6.5t-7 16.5v48q0 10 7 16t16 6h37q10 0 16.5 -6t6.5 -16v-48zM213 790
q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-9 0 -16 6.5t-7 16.5v48q0 10 7 16t16 6h37q10 0 16.5 -6t6.5 -16v-48z" />
    <glyph glyph-name="Eth" unicode="&#xd0;" horiz-adv-x="459" 
d="M67 391v287q0 10 7 16t16 6h164q31 0 58 -10t46.5 -28.5t31 -46t11.5 -62.5v-406q0 -35 -11.5 -62.5t-31 -46t-46.5 -28.5t-58 -10h-164q-9 0 -16 6.5t-7 16.5v301h-30q-10 0 -16.5 6.5t-6.5 15.5v22q0 10 6.5 16.5t16.5 6.5h30zM256 67q36 0 54 24t18 60v398
q0 36 -18 60t-54 24h-116v-242h99q10 0 16.5 -6.5t6.5 -16.5v-22q0 -9 -6.5 -15.5t-16.5 -6.5h-99v-257h116z" />
    <glyph glyph-name="Ntilde" unicode="&#xd1;" horiz-adv-x="456" 
d="M64 677q0 9 6.5 16t16.5 7h39q11 0 17 -4t11 -17l168 -501v499q0 9 6.5 16t16.5 7h24q10 0 16.5 -7t6.5 -16v-654q0 -10 -6.5 -16.5t-16.5 -6.5h-38q-10 0 -15.5 4t-10.5 17l-171 507v-505q0 -10 -6.5 -16.5t-16.5 -6.5h-24q-10 0 -16.5 6.5t-6.5 16.5v654zM175 799
q-10 0 -15.5 -3.5t-5.5 -17.5q0 -20 -20 -20h-14q-14 0 -18 7t-4 20q0 32 13 51q9 14 23.5 20t29.5 6q16 0 33 -6.5t33 -14.5t30 -14.5t24 -6.5q11 0 15 5.5t4 19.5q0 20 20 20h14q14 0 18 -7t4 -20q0 -35 -13 -54q-9 -14 -23.5 -19.5t-29.5 -5.5q-18 0 -35.5 6.5t-32.5 14
t-28 13.5t-22 6z" />
    <glyph glyph-name="Ograve" unicode="&#xd2;" horiz-adv-x="450" 
d="M319 549q0 41 -19.5 62.5t-58.5 21.5h-32q-39 0 -58.5 -21.5t-19.5 -62.5v-398q0 -84 78 -84h32q39 0 58.5 21.5t19.5 62.5v398zM392 145q0 -35 -11.5 -62t-31 -45.5t-46.5 -28t-58 -9.5h-40q-31 0 -58 9.5t-46.5 28t-31 45.5t-11.5 62v410q0 35 11.5 62t31 45.5t46.5 28
t58 9.5h40q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-410zM252 882l25 -105q2 -8 3 -14t1 -8q0 -18 -18 -18h-17q-11 0 -16.5 5t-8.5 13l-47 108q-5 11 -5 20q0 17 20 17h40q19 0 23 -18z" />
    <glyph glyph-name="Oacute" unicode="&#xd3;" horiz-adv-x="450" 
d="M319 549q0 41 -19.5 62.5t-58.5 21.5h-32q-39 0 -58.5 -21.5t-19.5 -62.5v-398q0 -84 78 -84h32q39 0 58.5 21.5t19.5 62.5v398zM392 145q0 -35 -11.5 -62t-31 -45.5t-46.5 -28t-58 -9.5h-40q-31 0 -58 9.5t-46.5 28t-31 45.5t-11.5 62v410q0 35 11.5 62t31 45.5t46.5 28
t58 9.5h40q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-410zM219 882q4 18 23 18h40q20 0 20 -17q0 -9 -5 -20l-47 -108q-3 -8 -8.5 -13t-16.5 -5h-17q-18 0 -18 18q0 2 1 8t3 14z" />
    <glyph glyph-name="Ocircumflex" unicode="&#xd4;" horiz-adv-x="450" 
d="M319 549q0 41 -19.5 62.5t-58.5 21.5h-32q-39 0 -58.5 -21.5t-19.5 -62.5v-398q0 -84 78 -84h32q39 0 58.5 21.5t19.5 62.5v398zM392 145q0 -35 -11.5 -62t-31 -45.5t-46.5 -28t-58 -9.5h-40q-31 0 -58 9.5t-46.5 28t-31 45.5t-11.5 62v410q0 35 11.5 62t31 45.5t46.5 28
t58 9.5h40q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-410zM159 737h-26q-17 0 -17 16q0 7 5 15l67 118q8 14 24 14h26q16 0 24 -14l67 -118q5 -8 5 -15q0 -16 -17 -16h-26q-13 0 -17 4.5t-8 12.5l-41 80l-41 -80q-4 -8 -8 -12.5t-17 -4.5z" />
    <glyph glyph-name="Otilde" unicode="&#xd5;" horiz-adv-x="450" 
d="M319 549q0 41 -19.5 62.5t-58.5 21.5h-32q-39 0 -58.5 -21.5t-19.5 -62.5v-398q0 -84 78 -84h32q39 0 58.5 21.5t19.5 62.5v398zM392 145q0 -35 -11.5 -62t-31 -45.5t-46.5 -28t-58 -9.5h-40q-31 0 -58 9.5t-46.5 28t-31 45.5t-11.5 62v410q0 35 11.5 62t31 45.5t46.5 28
t58 9.5h40q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-410zM171 799q-10 0 -15.5 -3.5t-5.5 -17.5q0 -20 -20 -20h-14q-14 0 -18 7t-4 20q0 32 13 51q9 14 23.5 20t29.5 6q16 0 33 -6.5t33 -14.5t30 -14.5t24 -6.5q11 0 15 5.5t4 19.5q0 20 20 20h14q14 0 18 -7t4 -20
q0 -35 -13 -54q-9 -14 -23.5 -19.5t-29.5 -5.5q-18 0 -35.5 6.5t-32.5 14t-28 13.5t-22 6z" />
    <glyph glyph-name="Odieresis" unicode="&#xd6;" horiz-adv-x="450" 
d="M319 549q0 41 -19.5 62.5t-58.5 21.5h-32q-39 0 -58.5 -21.5t-19.5 -62.5v-398q0 -84 78 -84h32q39 0 58.5 21.5t19.5 62.5v398zM392 145q0 -35 -11.5 -62t-31 -45.5t-46.5 -28t-58 -9.5h-40q-31 0 -58 9.5t-46.5 28t-31 45.5t-11.5 62v410q0 35 11.5 62t31 45.5t46.5 28
t58 9.5h40q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-410zM196 790q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-9 0 -16 6.5t-7 16.5v48q0 10 7 16t16 6h37q10 0 16.5 -6t6.5 -16v-48zM337 790q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-9 0 -16 6.5t-7 16.5v48q0 10 7 16t16 6h37
q10 0 16.5 -6t6.5 -16v-48z" />
    <glyph glyph-name="multiply" unicode="&#xd7;" horiz-adv-x="562" 
d="M89 94q-7 -7 -16 -7q-11 0 -23 11.5t-12 22.5q0 10 8 18l193 191l-193 192q-8 8 -8 18q0 11 12.5 22.5t22.5 11.5q9 0 16 -7l192 -195l192 195q7 7 16 7q10 0 22.5 -11.5t12.5 -22.5q0 -10 -8 -18l-193 -192l193 -191q8 -8 8 -18q0 -11 -12 -22.5t-23 -11.5q-9 0 -16 7
l-192 194z" />
    <glyph glyph-name="Oslash" unicode="&#xd8;" horiz-adv-x="450" 
d="M392 145q0 -35 -11.5 -62t-31 -45.5t-46.5 -28t-58 -9.5h-40q-31 0 -58 9.5t-46.5 28t-31 45.5t-11.5 62v410q0 35 11.5 62t31 45.5t46.5 28t58 9.5h40q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-410zM319 497l-181 -390q16 -40 71 -40h32q39 0 58.5 21.5t19.5 62.5v346
zM131 229l174 374q-18 30 -64 30h-32q-39 0 -58.5 -21.5t-19.5 -62.5v-320z" />
    <glyph glyph-name="Ugrave" unicode="&#xd9;" horiz-adv-x="458" 
d="M396 677v-530q0 -35 -11.5 -62.5t-31 -46t-46.5 -28.5t-58 -10h-40q-31 0 -58 10t-46.5 28.5t-31 46t-11.5 62.5v530q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-526q0 -36 18 -60t54 -24h44q36 0 54 24t18 60v526q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5
t6.5 -16.5zM256 882l25 -105q2 -8 3 -14t1 -8q0 -18 -18 -18h-17q-11 0 -16.5 5t-8.5 13l-47 108q-5 11 -5 20q0 17 20 17h40q19 0 23 -18z" />
    <glyph glyph-name="Uacute" unicode="&#xda;" horiz-adv-x="458" 
d="M396 677v-530q0 -35 -11.5 -62.5t-31 -46t-46.5 -28.5t-58 -10h-40q-31 0 -58 10t-46.5 28.5t-31 46t-11.5 62.5v530q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-526q0 -36 18 -60t54 -24h44q36 0 54 24t18 60v526q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5
t6.5 -16.5zM202 882q4 18 23 18h40q20 0 20 -17q0 -9 -5 -20l-47 -108q-3 -8 -8.5 -13t-16.5 -5h-17q-18 0 -18 18q0 2 1 8t3 14z" />
    <glyph glyph-name="Ucircumflex" unicode="&#xdb;" horiz-adv-x="458" 
d="M396 677v-530q0 -35 -11.5 -62.5t-31 -46t-46.5 -28.5t-58 -10h-40q-31 0 -58 10t-46.5 28.5t-31 46t-11.5 62.5v530q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-526q0 -36 18 -60t54 -24h44q36 0 54 24t18 60v526q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5
t6.5 -16.5zM163 737h-26q-17 0 -17 16q0 7 5 15l67 118q8 14 24 14h26q16 0 24 -14l67 -118q5 -8 5 -15q0 -16 -17 -16h-26q-13 0 -17 4.5t-8 12.5l-41 80l-41 -80q-4 -8 -8 -12.5t-17 -4.5z" />
    <glyph glyph-name="Udieresis" unicode="&#xdc;" horiz-adv-x="458" 
d="M396 677v-530q0 -35 -11.5 -62.5t-31 -46t-46.5 -28.5t-58 -10h-40q-31 0 -58 10t-46.5 28.5t-31 46t-11.5 62.5v530q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-526q0 -36 18 -60t54 -24h44q36 0 54 24t18 60v526q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5
t6.5 -16.5zM200 790q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-9 0 -16 6.5t-7 16.5v48q0 10 7 16t16 6h37q10 0 16.5 -6t6.5 -16v-48zM341 790q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-9 0 -16 6.5t-7 16.5v48q0 10 7 16t16 6h37q10 0 16.5 -6t6.5 -16v-48z" />
    <glyph glyph-name="Yacute" unicode="&#xdd;" horiz-adv-x="410" 
d="M376 700q19 0 19 -16q0 -5 -2 -11l-7 -21l-144 -409v-220q0 -10 -7 -16.5t-16 -6.5h-27q-9 0 -16 6.5t-7 16.5v219l-150 420q-3 8 -4 12t-1 9q0 9 6 13t12 4h36q18 0 25 -22l114 -340l110 340q6 22 26 22h33zM203 882q4 18 23 18h40q20 0 20 -17q0 -9 -5 -20l-47 -108
q-3 -8 -8.5 -13t-16.5 -5h-17q-18 0 -18 18q0 2 1 8t3 14z" />
    <glyph glyph-name="Thorn" unicode="&#xde;" horiz-adv-x="422" 
d="M136 555h103q31 0 58 -10t46.5 -28.5t31 -46t11.5 -62.5v-112q0 -35 -11.5 -62.5t-31 -46t-46.5 -28.5t-58 -10h-103v-126q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-9 0 -16 6.5t-7 16.5v654q0 10 7 16.5t16 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-122zM241 216q36 0 54 24t18 60
v104q0 36 -18 60t-54 24h-105v-272h105z" />
    <glyph glyph-name="germandbls" unicode="&#xdf;" horiz-adv-x="429" 
d="M131 23q0 -10 -7 -16.5t-16 -6.5h-27q-9 0 -16 6.5t-7 16.5v530q0 70 37 108.5t100 38.5h42q63 0 100 -38.5t37 -108.5v-86q0 -35 -13 -64.5t-51 -43.5q70 -24 70 -104v-134q0 -55 -28.5 -88t-88.5 -33h-49q-10 0 -16.5 6.5t-6.5 16.5v20q0 9 6.5 15.5t16.5 6.5h49
q44 0 44 44v157q0 30 -16 44t-45 14h-32q-10 0 -16.5 5.5t-6.5 15.5v22q0 9 6.5 15.5t16.5 6.5h37q21 0 35.5 13.5t14.5 39.5v113q0 36 -18 58t-54 22h-26q-36 0 -54 -22t-18 -58v-532z" />
    <glyph glyph-name="agrave" unicode="&#xe0;" 
d="M365 23q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v18q-12 -13 -35 -27t-69 -14h-23q-29 0 -52.5 10t-40 27.5t-25.5 40.5t-9 49v19q0 55 33.5 90.5t103.5 35.5h117v73q0 36 -18 58t-54 22h-19q-36 0 -56.5 -19.5t-20.5 -55.5v-10q0 -10 -6.5 -16.5
t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v8q0 70 39.5 106t102.5 36h35q63 0 100 -38.5t37 -108.5v-320zM220 65q36 0 54 22t18 58v64h-112q-37 0 -53 -16.5t-16 -44.5v-16q0 -29 14.5 -48t52.5 -19h42zM234 682l25 -105q2 -8 3 -14t1 -8q0 -18 -18 -18h-17q-11 0 -16.5 5
t-8.5 13l-47 108q-5 11 -5 20q0 17 20 17h40q19 0 23 -18z" />
    <glyph glyph-name="aacute" unicode="&#xe1;" 
d="M365 23q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v18q-12 -13 -35 -27t-69 -14h-23q-29 0 -52.5 10t-40 27.5t-25.5 40.5t-9 49v19q0 55 33.5 90.5t103.5 35.5h117v73q0 36 -18 58t-54 22h-19q-36 0 -56.5 -19.5t-20.5 -55.5v-10q0 -10 -6.5 -16.5
t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v8q0 70 39.5 106t102.5 36h35q63 0 100 -38.5t37 -108.5v-320zM220 65q36 0 54 22t18 58v64h-112q-37 0 -53 -16.5t-16 -44.5v-16q0 -29 14.5 -48t52.5 -19h42zM206 682q4 18 23 18h40q20 0 20 -17q0 -9 -5 -20l-47 -108
q-3 -8 -8.5 -13t-16.5 -5h-17q-18 0 -18 18q0 2 1 8t3 14z" />
    <glyph glyph-name="acircumflex" unicode="&#xe2;" 
d="M365 23q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v18q-12 -13 -35 -27t-69 -14h-23q-29 0 -52.5 10t-40 27.5t-25.5 40.5t-9 49v19q0 55 33.5 90.5t103.5 35.5h117v73q0 36 -18 58t-54 22h-19q-36 0 -56.5 -19.5t-20.5 -55.5v-10q0 -10 -6.5 -16.5
t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v8q0 70 39.5 106t102.5 36h35q63 0 100 -38.5t37 -108.5v-320zM220 65q36 0 54 22t18 58v64h-112q-37 0 -53 -16.5t-16 -44.5v-16q0 -29 14.5 -48t52.5 -19h42zM141 537h-26q-17 0 -17 16q0 7 5 15l67 118q8 14 24 14h26
q16 0 24 -14l67 -118q5 -8 5 -15q0 -16 -17 -16h-26q-13 0 -17 4.5t-8 12.5l-41 80l-41 -80q-4 -8 -8 -12.5t-17 -4.5z" />
    <glyph glyph-name="atilde" unicode="&#xe3;" 
d="M365 23q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v18q-12 -13 -35 -27t-69 -14h-23q-29 0 -52.5 10t-40 27.5t-25.5 40.5t-9 49v19q0 55 33.5 90.5t103.5 35.5h117v73q0 36 -18 58t-54 22h-19q-36 0 -56.5 -19.5t-20.5 -55.5v-10q0 -10 -6.5 -16.5
t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v8q0 70 39.5 106t102.5 36h35q63 0 100 -38.5t37 -108.5v-320zM220 65q36 0 54 22t18 58v64h-112q-37 0 -53 -16.5t-16 -44.5v-16q0 -29 14.5 -48t52.5 -19h42zM154 599q-10 0 -15.5 -3.5t-5.5 -17.5q0 -20 -20 -20h-14
q-14 0 -18 7t-4 20q0 32 13 51q9 14 23.5 20t29.5 6q16 0 33 -6.5t33 -14.5t30 -14.5t24 -6.5q11 0 15 5.5t4 19.5q0 20 20 20h14q14 0 18 -7t4 -20q0 -35 -13 -54q-9 -14 -23.5 -19.5t-29.5 -5.5q-18 0 -35.5 6.5t-32.5 14t-28 13.5t-22 6z" />
    <glyph glyph-name="adieresis" unicode="&#xe4;" 
d="M365 23q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v18q-12 -13 -35 -27t-69 -14h-23q-29 0 -52.5 10t-40 27.5t-25.5 40.5t-9 49v19q0 55 33.5 90.5t103.5 35.5h117v73q0 36 -18 58t-54 22h-19q-36 0 -56.5 -19.5t-20.5 -55.5v-10q0 -10 -6.5 -16.5
t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v8q0 70 39.5 106t102.5 36h35q63 0 100 -38.5t37 -108.5v-320zM220 65q36 0 54 22t18 58v64h-112q-37 0 -53 -16.5t-16 -44.5v-16q0 -29 14.5 -48t52.5 -19h42zM178 590q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-9 0 -16 6.5t-7 16.5v48
q0 10 7 16t16 6h37q10 0 16.5 -6t6.5 -16v-48zM319 590q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-9 0 -16 6.5t-7 16.5v48q0 10 7 16t16 6h37q10 0 16.5 -6t6.5 -16v-48z" />
    <glyph glyph-name="aring" unicode="&#xe5;" 
d="M365 23q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v18q-12 -13 -35 -27t-69 -14h-23q-29 0 -52.5 10t-40 27.5t-25.5 40.5t-9 49v19q0 55 33.5 90.5t103.5 35.5h117v73q0 36 -18 58t-54 22h-19q-36 0 -56.5 -19.5t-20.5 -55.5v-10q0 -10 -6.5 -16.5
t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v8q0 70 39.5 106t102.5 36h35q63 0 100 -38.5t37 -108.5v-320zM220 65q36 0 54 22t18 58v64h-112q-37 0 -53 -16.5t-16 -44.5v-16q0 -29 14.5 -48t52.5 -19h42zM321 634q0 -16 -6 -32.5t-17.5 -29t-29.5 -20.5t-42 -8h-34
q-24 0 -42 8t-29.5 20.5t-17.5 29t-6 32.5v54q0 17 6 33t17.5 28.5t29.5 20.5t42 8h34q24 0 42 -8t29.5 -20.5t17.5 -28.5t6 -33v-54zM160 644q0 -26 12 -36t29 -10h16q17 0 29 10t12 36v34q0 26 -12 36t-29 10h-16q-17 0 -29 -10t-12 -36v-34z" />
    <glyph glyph-name="ae" unicode="&#xe6;" horiz-adv-x="653" 
d="M220 65q35 0 53.5 20.5t18.5 54.5v89h-111q-37 0 -53 -17t-16 -45v-35q0 -29 14.5 -48t52.5 -19h41zM365 292h170v53q0 36 -18 58t-54 22h-26q-36 0 -54 -22t-18 -58v-53zM164 0q-29 0 -52.5 10t-39.5 27.5t-24.5 40.5t-8.5 49v38q0 55 33.5 91t103.5 36h116v53
q0 36 -18 58t-54 22h-20q-36 0 -55.5 -19.5t-19.5 -55.5v-10q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v8q0 70 38.5 106t101.5 36h32q35 0 63.5 -12.5t42.5 -40.5q15 26 43 39.5t62 13.5h36q63 0 100 -38.5t37 -108.5v-93q0 -10 -6.5 -16.5t-16.5 -6.5
h-220v-82q0 -36 18 -58t54 -22h26q36 0 54 22t18 58v10q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-8q0 -70 -37 -108.5t-100 -38.5h-35q-37 0 -65.5 13t-43.5 38q-15 -25 -43.5 -38t-65.5 -13h-54z" />
    <glyph glyph-name="ccedilla" unicode="&#xe7;" horiz-adv-x="407" 
d="M316 -158q0 -44 -29 -69.5t-75 -25.5h-8q-51 0 -78.5 22.5t-27.5 66.5v11q0 8 5.5 13.5t13.5 5.5h27q7 0 12.5 -5t5.5 -13v-9q0 -35 33 -35h21q17 0 26.5 8t9.5 27v23q0 17 -11 26.5t-34 9.5h-49q-10 0 -17.5 5.5t-7.5 16.5q0 7 1.5 13.5t9.5 24.5l18 43
q-52 8 -82.5 45.5t-30.5 100.5v196q0 70 37 108.5t100 38.5h42q63 0 100 -38.5t37 -108.5v-18q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v20q0 36 -18 58t-54 22h-26q-36 0 -54 -22t-18 -58v-200q0 -36 18 -58t54 -22h26q36 0 54 22t18 58v40
q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-38q0 -70 -37 -108.5t-100 -38.5h-10l-19 -48h14q55 0 78.5 -25.5t24.5 -64.5v-20z" />
    <glyph glyph-name="egrave" unicode="&#xe8;" horiz-adv-x="410" 
d="M122 237v-92q0 -36 18 -58t54 -22h26q36 0 54 22t18 58v10q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-8q0 -70 -37 -108.5t-100 -38.5h-42q-63 0 -100 38.5t-37 108.5v196q0 70 37 108.5t100 38.5h42q63 0 100 -38.5t37 -108.5v-83q0 -10 -6.5 -16.5
t-16.5 -6.5h-220zM122 302h170v43q0 36 -18 58t-54 22h-26q-36 0 -54 -22t-18 -58v-43zM234 682l25 -105q2 -8 3 -14t1 -8q0 -18 -18 -18h-17q-11 0 -16.5 5t-8.5 13l-47 108q-5 11 -5 20q0 17 20 17h40q19 0 23 -18z" />
    <glyph glyph-name="eacute" unicode="&#xe9;" horiz-adv-x="410" 
d="M122 237v-92q0 -36 18 -58t54 -22h26q36 0 54 22t18 58v10q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-8q0 -70 -37 -108.5t-100 -38.5h-42q-63 0 -100 38.5t-37 108.5v196q0 70 37 108.5t100 38.5h42q63 0 100 -38.5t37 -108.5v-83q0 -10 -6.5 -16.5
t-16.5 -6.5h-220zM122 302h170v43q0 36 -18 58t-54 22h-26q-36 0 -54 -22t-18 -58v-43zM180 682q4 18 23 18h40q20 0 20 -17q0 -9 -5 -20l-47 -108q-3 -8 -8.5 -13t-16.5 -5h-17q-18 0 -18 18q0 2 1 8t3 14z" />
    <glyph glyph-name="ecircumflex" unicode="&#xea;" horiz-adv-x="410" 
d="M122 237v-92q0 -36 18 -58t54 -22h26q36 0 54 22t18 58v10q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-8q0 -70 -37 -108.5t-100 -38.5h-42q-63 0 -100 38.5t-37 108.5v196q0 70 37 108.5t100 38.5h42q63 0 100 -38.5t37 -108.5v-83q0 -10 -6.5 -16.5
t-16.5 -6.5h-220zM122 302h170v43q0 36 -18 58t-54 22h-26q-36 0 -54 -22t-18 -58v-43zM141 537h-26q-17 0 -17 16q0 7 5 15l67 118q8 14 24 14h26q16 0 24 -14l67 -118q5 -8 5 -15q0 -16 -17 -16h-26q-13 0 -17 4.5t-8 12.5l-41 80l-41 -80q-4 -8 -8 -12.5t-17 -4.5z" />
    <glyph glyph-name="edieresis" unicode="&#xeb;" horiz-adv-x="410" 
d="M122 237v-92q0 -36 18 -58t54 -22h26q36 0 54 22t18 58v10q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-8q0 -70 -37 -108.5t-100 -38.5h-42q-63 0 -100 38.5t-37 108.5v196q0 70 37 108.5t100 38.5h42q63 0 100 -38.5t37 -108.5v-83q0 -10 -6.5 -16.5
t-16.5 -6.5h-220zM122 302h170v43q0 36 -18 58t-54 22h-26q-36 0 -54 -22t-18 -58v-43zM178 590q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-9 0 -16 6.5t-7 16.5v48q0 10 7 16t16 6h37q10 0 16.5 -6t6.5 -16v-48zM319 590q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-9 0 -16 6.5t-7 16.5v48
q0 10 7 16t16 6h37q10 0 16.5 -6t6.5 -16v-48z" />
    <glyph glyph-name="igrave" unicode="&#xec;" horiz-adv-x="185" 
d="M129 23q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v444q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-444zM120 682l25 -105q2 -8 3 -14t1 -8q0 -18 -18 -18h-17q-11 0 -16.5 5t-8.5 13l-47 108q-5 11 -5 20q0 17 20 17h40q19 0 23 -18z" />
    <glyph glyph-name="iacute" unicode="&#xed;" horiz-adv-x="185" 
d="M129 23q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v444q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-444zM66 682q4 18 23 18h40q20 0 20 -17q0 -9 -5 -20l-47 -108q-3 -8 -8.5 -13t-16.5 -5h-17q-18 0 -18 18q0 2 1 8t3 14z" />
    <glyph glyph-name="icircumflex" unicode="&#xee;" horiz-adv-x="185" 
d="M129 23q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v444q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-444zM27 537h-26q-17 0 -17 16q0 7 5 15l67 118q8 14 24 14h26q16 0 24 -14l67 -118q5 -8 5 -15q0 -16 -17 -16h-26q-13 0 -17 4.5t-8 12.5
l-41 80l-41 -80q-4 -8 -8 -12.5t-17 -4.5z" />
    <glyph glyph-name="idieresis" unicode="&#xef;" horiz-adv-x="185" 
d="M129 23q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v444q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-444zM64 590q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-9 0 -16 6.5t-7 16.5v48q0 10 7 16t16 6h37q10 0 16.5 -6t6.5 -16v-48zM205 590
q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-9 0 -16 6.5t-7 16.5v48q0 10 7 16t16 6h37q10 0 16.5 -6t6.5 -16v-48z" />
    <glyph glyph-name="eth" unicode="&#xf0;" horiz-adv-x="421" 
d="M288 650l3 -3q41 -45 57.5 -105t16.5 -147v-248q0 -70 -37 -108.5t-100 -38.5h-42q-63 0 -100 38.5t-37 108.5v196q0 70 37 108.5t100 38.5h7q34 0 60 -12.5t41 -32.5q-2 54 -16.5 92.5t-37.5 64.5l-55 -55q-7 -7 -17 -7q-9 0 -15 6l-11 11q-7 7 -7 17q0 9 7 16l50 50
q-11 7 -23 12l-24 10q-11 4 -21.5 10t-10.5 22q0 6 3 14.5t7 13.5q8 11 22 11q9 0 28 -8q39 -15 69 -36l63 63q7 7 16 7t16 -7l11 -11q7 -7 7 -15q0 -9 -8 -17zM292 345q0 36 -20.5 58t-56.5 22h-21q-36 0 -54 -22t-18 -58v-200q0 -36 18 -58t54 -22h26q36 0 54 22t18 58
v200z" />
    <glyph glyph-name="ntilde" unicode="&#xf1;" horiz-adv-x="427" 
d="M229 490h6q63 0 100 -37t37 -107v-323q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v322q0 36 -18 58t-54 22h-25q-36 0 -54.5 -22t-18.5 -58v-322q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v444q0 10 6.5 16.5t16.5 6.5h27
q10 0 16.5 -6.5t6.5 -16.5v-19q36 42 100 42zM161 599q-10 0 -15.5 -3.5t-5.5 -17.5q0 -20 -20 -20h-14q-14 0 -18 7t-4 20q0 32 13 51q9 14 23.5 20t29.5 6q16 0 33 -6.5t33 -14.5t30 -14.5t24 -6.5q11 0 15 5.5t4 19.5q0 20 20 20h14q14 0 18 -7t4 -20q0 -35 -13 -54
q-9 -14 -23.5 -19.5t-29.5 -5.5q-18 0 -35.5 6.5t-32.5 14t-28 13.5t-22 6z" />
    <glyph glyph-name="ograve" unicode="&#xf2;" horiz-adv-x="414" 
d="M49 147v196q0 70 37 108.5t100 38.5h42q63 0 100 -38.5t37 -108.5v-196q0 -70 -37 -108.5t-100 -38.5h-42q-63 0 -100 38.5t-37 108.5zM292 345q0 36 -18 58t-54 22h-26q-36 0 -54 -22t-18 -58v-200q0 -36 18 -58t54 -22h26q36 0 54 22t18 58v200zM234 682l25 -105
q2 -8 3 -14t1 -8q0 -18 -18 -18h-17q-11 0 -16.5 5t-8.5 13l-47 108q-5 11 -5 20q0 17 20 17h40q19 0 23 -18z" />
    <glyph glyph-name="oacute" unicode="&#xf3;" horiz-adv-x="414" 
d="M49 147v196q0 70 37 108.5t100 38.5h42q63 0 100 -38.5t37 -108.5v-196q0 -70 -37 -108.5t-100 -38.5h-42q-63 0 -100 38.5t-37 108.5zM292 345q0 36 -18 58t-54 22h-26q-36 0 -54 -22t-18 -58v-200q0 -36 18 -58t54 -22h26q36 0 54 22t18 58v200zM180 682q4 18 23 18h40
q20 0 20 -17q0 -9 -5 -20l-47 -108q-3 -8 -8.5 -13t-16.5 -5h-17q-18 0 -18 18q0 2 1 8t3 14z" />
    <glyph glyph-name="ocircumflex" unicode="&#xf4;" horiz-adv-x="414" 
d="M49 147v196q0 70 37 108.5t100 38.5h42q63 0 100 -38.5t37 -108.5v-196q0 -70 -37 -108.5t-100 -38.5h-42q-63 0 -100 38.5t-37 108.5zM292 345q0 36 -18 58t-54 22h-26q-36 0 -54 -22t-18 -58v-200q0 -36 18 -58t54 -22h26q36 0 54 22t18 58v200zM141 537h-26
q-17 0 -17 16q0 7 5 15l67 118q8 14 24 14h26q16 0 24 -14l67 -118q5 -8 5 -15q0 -16 -17 -16h-26q-13 0 -17 4.5t-8 12.5l-41 80l-41 -80q-4 -8 -8 -12.5t-17 -4.5z" />
    <glyph glyph-name="otilde" unicode="&#xf5;" horiz-adv-x="414" 
d="M49 147v196q0 70 37 108.5t100 38.5h42q63 0 100 -38.5t37 -108.5v-196q0 -70 -37 -108.5t-100 -38.5h-42q-63 0 -100 38.5t-37 108.5zM292 345q0 36 -18 58t-54 22h-26q-36 0 -54 -22t-18 -58v-200q0 -36 18 -58t54 -22h26q36 0 54 22t18 58v200zM154 599
q-10 0 -15.5 -3.5t-5.5 -17.5q0 -20 -20 -20h-14q-14 0 -18 7t-4 20q0 32 13 51q9 14 23.5 20t29.5 6q16 0 33 -6.5t33 -14.5t30 -14.5t24 -6.5q11 0 15 5.5t4 19.5q0 20 20 20h14q14 0 18 -7t4 -20q0 -35 -13 -54q-9 -14 -23.5 -19.5t-29.5 -5.5q-18 0 -35.5 6.5t-32.5 14
t-28 13.5t-22 6z" />
    <glyph glyph-name="odieresis" unicode="&#xf6;" horiz-adv-x="414" 
d="M49 147v196q0 70 37 108.5t100 38.5h42q63 0 100 -38.5t37 -108.5v-196q0 -70 -37 -108.5t-100 -38.5h-42q-63 0 -100 38.5t-37 108.5zM292 345q0 36 -18 58t-54 22h-26q-36 0 -54 -22t-18 -58v-200q0 -36 18 -58t54 -22h26q36 0 54 22t18 58v200zM178 590
q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-9 0 -16 6.5t-7 16.5v48q0 10 7 16t16 6h37q10 0 16.5 -6t6.5 -16v-48zM319 590q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-9 0 -16 6.5t-7 16.5v48q0 10 7 16t16 6h37q10 0 16.5 -6t6.5 -16v-48z" />
    <glyph glyph-name="divide" unicode="&#xf7;" horiz-adv-x="438" 
d="M271 165q0 -10 -6.5 -16.5t-16.5 -6.5h-57q-9 0 -16 6.5t-7 16.5v71q0 10 7 16t16 6h57q10 0 16.5 -6t6.5 -16v-71zM385 391q10 0 16.5 -6.5t6.5 -16.5v-19q0 -9 -6.5 -15.5t-16.5 -6.5h-332q-10 0 -16.5 6.5t-6.5 15.5v19q0 10 6.5 16.5t16.5 6.5h332zM271 482
q0 -10 -6.5 -16t-16.5 -6h-57q-9 0 -16 6t-7 16v71q0 10 7 16.5t16 6.5h57q10 0 16.5 -6.5t6.5 -16.5v-71z" />
    <glyph glyph-name="oslash" unicode="&#xf8;" horiz-adv-x="414" 
d="M49 343q0 70 37 108.5t100 38.5h42q63 0 100 -38.5t37 -108.5v-196q0 -70 -37 -108.5t-100 -38.5h-42q-63 0 -100 38.5t-37 108.5v196zM292 345q0 7 -1 11l-149 -272q19 -19 52 -19h26q36 0 54 22t18 58v200zM194 425q-36 0 -54 -22t-18 -58v-193l142 260q-19 13 -44 13
h-26z" />
    <glyph glyph-name="ugrave" unicode="&#xf9;" horiz-adv-x="421" 
d="M191 0q-63 0 -99.5 37t-36.5 107v323q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-322q0 -36 18 -58t54 -22h19q36 0 54.5 22t18.5 58v322q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-444q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5
t-6.5 16.5v19q-36 -42 -101 -42zM237 682l25 -105q2 -8 3 -14t1 -8q0 -18 -18 -18h-17q-11 0 -16.5 5t-8.5 13l-47 108q-5 11 -5 20q0 17 20 17h40q19 0 23 -18z" />
    <glyph glyph-name="uacute" unicode="&#xfa;" horiz-adv-x="421" 
d="M191 0q-63 0 -99.5 37t-36.5 107v323q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-322q0 -36 18 -58t54 -22h19q36 0 54.5 22t18.5 58v322q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-444q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5
t-6.5 16.5v19q-36 -42 -101 -42zM203 682q4 18 23 18h40q20 0 20 -17q0 -9 -5 -20l-47 -108q-3 -8 -8.5 -13t-16.5 -5h-17q-18 0 -18 18q0 2 1 8t3 14z" />
    <glyph glyph-name="ucircumflex" unicode="&#xfb;" horiz-adv-x="421" 
d="M191 0q-63 0 -99.5 37t-36.5 107v323q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-322q0 -36 18 -58t54 -22h19q36 0 54.5 22t18.5 58v322q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-444q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5
t-6.5 16.5v19q-36 -42 -101 -42zM144 537h-26q-17 0 -17 16q0 7 5 15l67 118q8 14 24 14h26q16 0 24 -14l67 -118q5 -8 5 -15q0 -16 -17 -16h-26q-13 0 -17 4.5t-8 12.5l-41 80l-41 -80q-4 -8 -8 -12.5t-17 -4.5z" />
    <glyph glyph-name="udieresis" unicode="&#xfc;" horiz-adv-x="421" 
d="M191 0q-63 0 -99.5 37t-36.5 107v323q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-322q0 -36 18 -58t54 -22h19q36 0 54.5 22t18.5 58v322q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-444q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5
t-6.5 16.5v19q-36 -42 -101 -42zM181 590q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-9 0 -16 6.5t-7 16.5v48q0 10 7 16t16 6h37q10 0 16.5 -6t6.5 -16v-48zM322 590q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-9 0 -16 6.5t-7 16.5v48q0 10 7 16t16 6h37q10 0 16.5 -6t6.5 -16v-48z" />
    <glyph glyph-name="yacute" unicode="&#xfd;" horiz-adv-x="389" 
d="M195 65q6 0 7 6l88 399q4 20 26 20h24q25 0 25 -23q0 -8 -3 -20l-126 -528q-8 -32 -18 -52.5t-24 -34.5q-32 -32 -99 -32q-24 0 -38 1.5t-21.5 4.5t-10 8t-2.5 13v16q0 12 5.5 16.5t13.5 4.5t19 -1.5t38 -1.5q33 0 47.5 16t23.5 56l15 67h-29q-11 0 -18.5 6.5t-9.5 16.5
l-102 428q-3 14 -3 19q0 20 24 20h27q20 0 25 -20l89 -399q2 -6 7 -6zM193 682q4 18 23 18h40q20 0 20 -17q0 -9 -5 -20l-47 -108q-3 -8 -8.5 -13t-16.5 -5h-17q-18 0 -18 18q0 2 1 8t3 14z" />
    <glyph glyph-name="thorn" unicode="&#xfe;" horiz-adv-x="428" 
d="M131 448q16 19 40.5 30.5t55.5 11.5h15q63 0 100 -38.5t37 -108.5v-196q0 -70 -37 -108.5t-100 -38.5h-21q-29 0 -52.5 10.5t-37.5 30.5v-218q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v854q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-229z
M306 345q0 36 -18 58t-54 22h-31q-36 0 -54 -22t-18 -58v-200q0 -36 18 -58t54 -22h31q36 0 54 22t18 58v200z" />
    <glyph glyph-name="ydieresis" unicode="&#xff;" horiz-adv-x="389" 
d="M195 65q6 0 7 6l88 399q4 20 26 20h24q25 0 25 -23q0 -8 -3 -20l-126 -528q-8 -32 -18 -52.5t-24 -34.5q-32 -32 -99 -32q-24 0 -38 1.5t-21.5 4.5t-10 8t-2.5 13v16q0 12 5.5 16.5t13.5 4.5t19 -1.5t38 -1.5q33 0 47.5 16t23.5 56l15 67h-29q-11 0 -18.5 6.5t-9.5 16.5
l-102 428q-3 14 -3 19q0 20 24 20h27q20 0 25 -20l89 -399q2 -6 7 -6zM165 590q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-9 0 -16 6.5t-7 16.5v48q0 10 7 16t16 6h37q10 0 16.5 -6t6.5 -16v-48zM306 590q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-9 0 -16 6.5t-7 16.5v48q0 10 7 16t16 6
h37q10 0 16.5 -6t6.5 -16v-48z" />
    <glyph glyph-name="dotlessi" unicode="&#x131;" horiz-adv-x="185" 
d="M129 23q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v444q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-444z" />
    <glyph glyph-name="Lslash" unicode="&#x141;" horiz-adv-x="397" 
d="M69 352v325q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-302l124 39q5 2 9.5 3t9.5 1q17 0 17 -20v-10q0 -18 -4.5 -24t-18.5 -11l-137 -43v-243h215q10 0 16.5 -6.5t6.5 -16.5v-22q0 -9 -6.5 -15.5t-16.5 -6.5h-265q-9 0 -16 6.5t-7 15.5v266l-30 -9
q-6 -2 -10 -2q-9 0 -12 6t-3 13v18q0 11 4 17t15 10z" />
    <glyph glyph-name="lslash" unicode="&#x142;" horiz-adv-x="269" 
d="M97 371v306q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-280l34 13q5 2 10.5 4t10.5 2q17 0 17 -20v-20q0 -11 -4.5 -17t-18.5 -11l-49 -18v-307q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v282l-45 -16q-6 -2 -10 -2q-9 0 -12 6t-3 13v19
q0 11 2.5 16t13.5 10z" />
    <glyph glyph-name="OE" unicode="&#x152;" horiz-adv-x="661" 
d="M205 0q-31 0 -58 10t-46.5 28.5t-31 46t-11.5 62.5v406q0 35 11.5 62.5t31 46t46.5 28.5t58 10h394q10 0 16.5 -6.5t6.5 -15.5v-22q0 -10 -6.5 -16.5t-16.5 -6.5h-207v-242h177q10 0 16.5 -6.5t6.5 -16.5v-22q0 -9 -6.5 -15.5t-16.5 -6.5h-177v-257h217q10 0 16.5 -6.5
t6.5 -15.5v-22q0 -10 -6.5 -16.5t-16.5 -6.5h-404zM319 67v566h-116q-36 0 -54 -24t-18 -60v-398q0 -36 18 -60t54 -24h116z" />
    <glyph glyph-name="oe" unicode="&#x153;" horiz-adv-x="653" 
d="M292 345q0 36 -20.5 58t-56.5 22h-21q-36 0 -54 -22t-18 -58v-200q0 -36 18 -58t54 -22h21q36 0 56.5 22t20.5 58v200zM365 302h170v43q0 36 -18 58t-54 22h-23q-35 0 -54.5 -20.5t-20.5 -54.5v-48zM471 490q63 0 100 -38.5t37 -108.5v-83q0 -10 -6.5 -16.5t-16.5 -6.5
h-220v-97q0 -34 20 -54.5t55 -20.5h23q36 0 54 22t18 58v10q0 10 6.5 16.5t16.5 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-8q0 -70 -37 -108.5t-100 -38.5h-32q-32 0 -63.5 13.5t-47.5 48.5q-16 -35 -47 -48.5t-63 -13.5h-32q-63 0 -100 38.5t-37 108.5v196q0 70 37 108.5
t100 38.5h32q32 0 63 -13.5t47 -48.5q16 35 47.5 48.5t63.5 13.5h32z" />
    <glyph glyph-name="Scaron" unicode="&#x160;" horiz-adv-x="434" 
d="M194 0q-31 0 -58 9.5t-46.5 28t-31 45.5t-11.5 62v90q0 10 6.5 16.5t16.5 6.5h27q9 0 16 -6.5t7 -16.5v-84q0 -41 19.5 -62.5t58.5 -21.5h37q36 0 54 24t18 60v45q0 24 -3.5 41t-11.5 29.5t-20.5 23t-31.5 20.5l-106 59q-38 21 -58 53.5t-20 86.5v45q0 35 11.5 62t31 46
t46.5 28.5t58 9.5h37q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-70q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-9 0 -16 6.5t-7 16.5v64q0 41 -19.5 62.5t-58.5 21.5h-35q-36 0 -54 -23t-18 -59v-33q0 -28 10 -51t46 -43l118 -67q41 -23 59 -57t18 -84v-69q0 -35 -11.5 -62.5
t-31 -46t-46.5 -28.5t-58 -10h-39zM153 900q13 0 17 -4.5t8 -12.5l41 -80l41 80q4 8 8 12.5t17 4.5h26q17 0 17 -16q0 -7 -5 -15l-67 -118q-8 -14 -24 -14h-26q-16 0 -24 14l-67 118q-5 8 -5 15q0 16 17 16h26z" />
    <glyph glyph-name="scaron" unicode="&#x161;" horiz-adv-x="403" 
d="M124 237q-48 14 -63.5 43t-15.5 73v21q0 53 33.5 84.5t86.5 31.5h73q55 0 87 -29.5t32 -86.5v-29q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-9 0 -16 7t-7 18v30q0 25 -14 35.5t-30 10.5h-78q-21 0 -32.5 -14.5t-11.5 -35.5v-26q0 -11 6.5 -24.5t23.5 -18.5l137 -44
q35 -11 55 -36.5t20 -75.5v-31q0 -48 -31 -82.5t-89 -34.5h-83q-55 0 -84 28t-29 83v40q0 10 6.5 16.5t16.5 6.5h27q9 0 16 -6.5t7 -16.5v-32q0 -29 12 -40.5t34 -11.5h71q22 0 37.5 11.5t15.5 40.5v15q0 35 -11 48t-40 21zM131 700q13 0 17 -4.5t8 -12.5l41 -80l41 80
q4 8 8 12.5t17 4.5h26q17 0 17 -16q0 -7 -5 -15l-67 -118q-8 -14 -24 -14h-26q-16 0 -24 14l-67 118q-5 8 -5 15q0 16 17 16h26z" />
    <glyph glyph-name="Ydieresis" unicode="&#x178;" horiz-adv-x="410" 
d="M376 700q19 0 19 -16q0 -5 -2 -11l-7 -21l-144 -409v-220q0 -10 -7 -16.5t-16 -6.5h-27q-9 0 -16 6.5t-7 16.5v219l-150 420q-3 8 -4 12t-1 9q0 9 6 13t12 4h36q18 0 25 -22l114 -340l110 340q6 22 26 22h33zM168 790q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-9 0 -16 6.5
t-7 16.5v48q0 10 7 16t16 6h37q10 0 16.5 -6t6.5 -16v-48zM309 790q0 -10 -6.5 -16.5t-16.5 -6.5h-37q-9 0 -16 6.5t-7 16.5v48q0 10 7 16t16 6h37q10 0 16.5 -6t6.5 -16v-48z" />
    <glyph glyph-name="Zcaron" unicode="&#x17d;" horiz-adv-x="391" 
d="M359 23q0 -10 -7 -16.5t-16 -6.5h-283q-10 0 -16.5 6.5t-6.5 16.5v22q0 16 6 29l244 559h-220q-9 0 -16 6.5t-7 16.5v22q0 10 7 16t16 6h275q11 0 17.5 -6t6.5 -16v-22q0 -9 -4 -19l-246 -570h227q9 0 16 -7t7 -16v-21zM142 900q13 0 17 -4.5t8 -12.5l41 -80l41 80
q4 8 8 12.5t17 4.5h26q17 0 17 -16q0 -7 -5 -15l-67 -118q-8 -14 -24 -14h-26q-16 0 -24 14l-67 118q-5 8 -5 15q0 16 17 16h26z" />
    <glyph glyph-name="zcaron" unicode="&#x17e;" horiz-adv-x="374" 
d="M341 23q0 -10 -7 -16.5t-16 -6.5h-267q-21 0 -21 21v22q0 9 1.5 14t5.5 12l229 358h-206q-9 0 -16 6.5t-7 16.5v18q0 10 7 16t16 6h257q11 0 17.5 -6t6.5 -16v-21q0 -9 -2 -15.5t-5 -11.5l-226 -355h210q9 0 16 -7t7 -16v-19zM119 700q13 0 17 -4.5t8 -12.5l41 -80l41 80
q4 8 8 12.5t17 4.5h26q17 0 17 -16q0 -7 -5 -15l-67 -118q-8 -14 -24 -14h-26q-16 0 -24 14l-67 118q-5 8 -5 15q0 16 17 16h26z" />
    <glyph glyph-name="florin" unicode="&#x192;" horiz-adv-x="347" 
d="M152 325h-78q-10 0 -16.5 6.5t-6.5 15.5v20q0 10 6.5 16.5t16.5 6.5h78v184q0 56 32.5 91t102.5 35h42q10 0 16.5 -7t6.5 -16v-22q0 -10 -6.5 -16.5t-16.5 -6.5h-40q-35 0 -49.5 -17t-14.5 -46v-179h80q10 0 16.5 -6.5t6.5 -16.5v-20q0 -9 -6.5 -15.5t-16.5 -6.5h-80
v-199q0 -56 -32.5 -91t-102.5 -35h-42q-10 0 -16.5 7t-6.5 16v22q0 10 6.5 16.5t16.5 6.5h40q35 0 49.5 17t14.5 46v194z" />
    <glyph glyph-name="circumflex" unicode="&#x2c6;" horiz-adv-x="338" 
d="M103 537h-26q-17 0 -17 16q0 7 5 15l67 118q8 14 24 14h26q16 0 24 -14l67 -118q5 -8 5 -15q0 -16 -17 -16h-26q-13 0 -17 4.5t-8 12.5l-41 80l-41 -80q-4 -8 -8 -12.5t-17 -4.5z" />
    <glyph glyph-name="caron" unicode="&#x2c7;" horiz-adv-x="338" 
d="M103 700q13 0 17 -4.5t8 -12.5l41 -80l41 80q4 8 8 12.5t17 4.5h26q17 0 17 -16q0 -7 -5 -15l-67 -118q-8 -14 -24 -14h-26q-16 0 -24 14l-67 118q-5 8 -5 15q0 16 17 16h26z" />
    <glyph glyph-name="breve" unicode="&#x2d8;" horiz-adv-x="330" 
d="M209 648v30q0 10 6.5 16t16.5 6h15q10 0 16.5 -6t6.5 -16v-51q0 -16 -5.5 -32t-17.5 -29t-30.5 -21t-45.5 -8h-12q-27 0 -45.5 8t-30.5 21t-17.5 29t-5.5 32v51q0 10 6.5 16t16.5 6h15q10 0 16.5 -6t6.5 -16v-30q0 -19 6 -36t31 -17h14q25 0 31 17t6 36z" />
    <glyph glyph-name="dotaccent" unicode="&#x2d9;" horiz-adv-x="223" 
d="M163 590q0 -10 -6.5 -16.5t-16.5 -6.5h-57q-9 0 -16 6.5t-7 16.5v68q0 10 7 16t16 6h57q10 0 16.5 -6t6.5 -16v-68z" />
    <glyph glyph-name="ring" unicode="&#x2da;" horiz-adv-x="344" 
d="M284 634q0 -16 -6 -32.5t-17.5 -29t-29.5 -20.5t-42 -8h-34q-24 0 -42 8t-29.5 20.5t-17.5 29t-6 32.5v54q0 17 6 33t17.5 28.5t29.5 20.5t42 8h34q24 0 42 -8t29.5 -20.5t17.5 -28.5t6 -33v-54zM123 644q0 -26 12 -36t29 -10h16q17 0 29 10t12 36v34q0 26 -12 36t-29 10
h-16q-17 0 -29 -10t-12 -36v-34z" />
    <glyph glyph-name="ogonek" unicode="&#x2db;" horiz-adv-x="291" 
d="M231 -173q0 -17 -18 -17h-63q-43 0 -66.5 19.5t-23.5 63.5v23q0 17 6.5 31t17.5 24l57 49h67v-5l-65 -62q-10 -10 -13.5 -16.5t-3.5 -15.5v-19q0 -19 7 -26.5t27 -7.5h53q18 0 18 -17v-24z" />
    <glyph glyph-name="tilde" unicode="&#x2dc;" horiz-adv-x="381" 
d="M137 599q-10 0 -15.5 -3.5t-5.5 -17.5q0 -20 -20 -20h-14q-14 0 -18 7t-4 20q0 32 13 51q9 14 23.5 20t29.5 6q16 0 33 -6.5t33 -14.5t30 -14.5t24 -6.5q11 0 15 5.5t4 19.5q0 20 20 20h14q14 0 18 -7t4 -20q0 -35 -13 -54q-9 -14 -23.5 -19.5t-29.5 -5.5
q-18 0 -35.5 6.5t-32.5 14t-28 13.5t-22 6z" />
    <glyph glyph-name="hungarumlaut" unicode="&#x2dd;" horiz-adv-x="347" 
d="M89 682q5 18 23 18h30q20 0 20 -17q0 -9 -5 -20l-47 -108q-3 -8 -9.5 -13t-17.5 -5h-5q-18 0 -18 18q0 2 1 8t3 14zM214 682q5 18 23 18h30q20 0 20 -17q0 -9 -5 -20l-47 -108q-3 -8 -9.5 -13t-17.5 -5h-5q-18 0 -18 18q0 2 1 8t3 14z" />
    <glyph glyph-name="pi" unicode="&#x3c0;" horiz-adv-x="600" 
d="M546 490q10 0 16.5 -6.5t6.5 -15.5v-20q0 -10 -6.5 -16.5t-16.5 -6.5h-100v-311q0 -10 1.5 -20t6.5 -18t14 -10.5t19 -2.5q26 0 41 3.5t29 3.5q7 0 11 -5t4 -12v-27q0 -5 -3 -11.5t-17 -9.5q-16 -4 -41 -6t-39 -2q-31 0 -50.5 9.5t-30 26t-14.5 37.5t-4 43v312h-196v-402
q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-10 0 -16.5 6.5t-6.5 16.5v402h-63q-10 0 -16.5 6.5t-6.5 16.5v20q0 9 6.5 15.5t16.5 6.5h505z" />
    <glyph glyph-name="endash" unicode="&#x2013;" horiz-adv-x="464" 
d="M398 324q10 0 16.5 -6.5t6.5 -16.5v-19q0 -9 -6.5 -15.5t-16.5 -6.5h-332q-10 0 -16.5 6.5t-6.5 15.5v19q0 10 6.5 16.5t16.5 6.5h332z" />
    <glyph glyph-name="emdash" unicode="&#x2014;" horiz-adv-x="807" 
d="M741 324q10 0 16.5 -6.5t6.5 -16.5v-19q0 -9 -6.5 -15.5t-16.5 -6.5h-675q-10 0 -16.5 6.5t-6.5 15.5v19q0 10 6.5 16.5t16.5 6.5h675z" />
    <glyph glyph-name="quoteleft" unicode="&#x2018;" horiz-adv-x="174" 
d="M62 377q-11 0 -15 5.5t-4 13.5q0 3 0.5 14.5t1.5 19.5l18 246q1 9 5.5 16.5t21.5 7.5h13q9 0 14.5 -6.5t5.5 -16.5v-278q0 -9 -6 -15.5t-17 -6.5h-38z" />
    <glyph glyph-name="quoteright" unicode="&#x2019;" horiz-adv-x="173" 
d="M112 700q11 0 15 -5.5t4 -13.5q0 -3 -0.5 -14.5t-1.5 -19.5l-18 -246q-1 -9 -5.5 -16.5t-21.5 -7.5h-13q-9 0 -14.5 6.5t-5.5 16.5v278q0 9 6 15.5t17 6.5h38z" />
    <glyph glyph-name="quotesinglbase" unicode="&#x201a;" horiz-adv-x="174" 
d="M110 193q11 0 15 -5.5t4 -13.5q0 -3 -0.5 -14.5t-1.5 -19.5l-18 -246q-1 -9 -5.5 -16.5t-21.5 -7.5h-13q-9 0 -14.5 6.5t-5.5 16.5v278q0 9 6 15.5t17 6.5h38z" />
    <glyph glyph-name="quotedblleft" unicode="&#x201c;" horiz-adv-x="304" 
d="M62 377q-11 0 -15 5.5t-4 13.5q0 3 0.5 14.5t1.5 19.5l18 246q1 9 5.5 16.5t21.5 7.5h13q9 0 14.5 -6.5t5.5 -16.5v-278q0 -9 -6 -15.5t-17 -6.5h-38zM192 377q-11 0 -15 5.5t-4 13.5q0 3 0.5 14.5t1.5 19.5l18 246q1 9 5.5 16.5t21.5 7.5h13q9 0 14.5 -6.5t5.5 -16.5
v-278q0 -9 -6 -15.5t-17 -6.5h-38z" />
    <glyph glyph-name="quotedblright" unicode="&#x201d;" horiz-adv-x="303" 
d="M242 700q11 0 15 -5.5t4 -13.5q0 -3 -0.5 -14.5t-1.5 -19.5l-18 -246q-1 -9 -5.5 -16.5t-21.5 -7.5h-13q-9 0 -14.5 6.5t-5.5 16.5v278q0 9 6 15.5t17 6.5h38zM112 700q11 0 15 -5.5t4 -13.5q0 -3 -0.5 -14.5t-1.5 -19.5l-18 -246q-1 -9 -5.5 -16.5t-21.5 -7.5h-13
q-9 0 -14.5 6.5t-5.5 16.5v278q0 9 6 15.5t17 6.5h38z" />
    <glyph glyph-name="quotedblbase" unicode="&#x201e;" horiz-adv-x="304" 
d="M240 193q11 0 15 -5.5t4 -13.5q0 -3 -0.5 -14.5t-1.5 -19.5l-18 -246q-1 -9 -5.5 -16.5t-21.5 -7.5h-13q-9 0 -14.5 6.5t-5.5 16.5v278q0 9 6 15.5t17 6.5h38zM110 193q11 0 15 -5.5t4 -13.5q0 -3 -0.5 -14.5t-1.5 -19.5l-18 -246q-1 -9 -5.5 -16.5t-21.5 -7.5h-13
q-9 0 -14.5 6.5t-5.5 16.5v278q0 9 6 15.5t17 6.5h38z" />
    <glyph glyph-name="dagger" unicode="&#x2020;" horiz-adv-x="405" 
d="M357 490q9 0 16 -6.5t7 -16.5v-21q0 -10 -7 -16.5t-16 -6.5h-118v-400q0 -10 -7 -16.5t-16 -6.5h-27q-9 0 -16 6.5t-7 16.5v400h-118q-9 0 -16 6.5t-7 16.5v21q0 10 7 16.5t16 6.5h118v187q0 10 7 16.5t16 6.5h27q9 0 16 -6.5t7 -16.5v-187h118z" />
    <glyph glyph-name="daggerdbl" unicode="&#x2021;" horiz-adv-x="415" 
d="M362 490q9 0 16 -6.5t7 -16.5v-21q0 -10 -7 -16.5t-16 -6.5h-118v-356h118q9 0 16 -6.5t7 -16.5v-21q0 -10 -7 -16.5t-16 -6.5h-118v-187q0 -10 -7 -16.5t-16 -6.5h-27q-9 0 -16 6.5t-7 16.5v187h-118q-9 0 -16 6.5t-7 16.5v21q0 10 7 16.5t16 6.5h118v356h-118
q-9 0 -16 6.5t-7 16.5v21q0 10 7 16.5t16 6.5h118v187q0 10 7 16.5t16 6.5h27q9 0 16 -6.5t7 -16.5v-187h118z" />
    <glyph glyph-name="bullet" unicode="&#x2022;" horiz-adv-x="308" 
d="M136 240q-42 0 -69 27.5t-27 69.5v40q0 42 27 69t69 27h35q42 0 69.5 -27t27.5 -69v-40q0 -42 -27.5 -69.5t-69.5 -27.5h-35z" />
    <glyph glyph-name="ellipsis" unicode="&#x2026;" horiz-adv-x="739" 
d="M148 23q0 -10 -6.5 -16.5t-16.5 -6.5h-57q-9 0 -16 6.5t-7 16.5v78q0 10 7 16t16 6h57q10 0 16.5 -6t6.5 -16v-78zM421 23q0 -10 -6.5 -16.5t-16.5 -6.5h-57q-9 0 -16 6.5t-7 16.5v78q0 10 7 16t16 6h57q10 0 16.5 -6t6.5 -16v-78zM694 23q0 -10 -6.5 -16.5t-16.5 -6.5
h-57q-9 0 -16 6.5t-7 16.5v78q0 10 7 16t16 6h57q10 0 16.5 -6t6.5 -16v-78z" />
    <glyph glyph-name="perthousand" unicode="&#x2030;" horiz-adv-x="957" 
d="M278 482q0 -55 -30 -89t-85 -34h-10q-55 0 -85 34t-30 89v95q0 55 30 89t85 34h10q55 0 85 -34t30 -89v-95zM145 18q-6 -8 -14 -13t-23 -5h-10q-10 0 -15 5t-5 13t7 19l432 645q5 8 11.5 13t21.5 5h14q11 0 15.5 -5.5t4.5 -13.5q0 -9 -8 -20zM213 588q0 24 -13.5 41
t-41.5 17q-29 0 -42 -17t-13 -41v-117q0 -24 13 -41t42 -17q28 0 41.5 17t13.5 41v117zM624 123q0 -55 -30 -89t-85 -34h-10q-55 0 -85 34t-30 89v95q0 55 30 89t85 34h10q55 0 85 -34t30 -89v-95zM559 229q0 24 -13.5 41t-41.5 17q-29 0 -42 -17t-13 -41v-117q0 -24 13 -41
t42 -17q28 0 41.5 17t13.5 41v117zM923 123q0 -55 -30 -89t-85 -34h-10q-55 0 -85 34t-30 89v95q0 55 30 89t85 34h10q55 0 85 -34t30 -89v-95zM858 229q0 24 -13.5 41t-41.5 17q-29 0 -42 -17t-13 -41v-117q0 -24 13 -41t42 -17q28 0 41.5 17t13.5 41v117z" />
    <glyph glyph-name="guilsinglleft" unicode="&#x2039;" horiz-adv-x="279" 
d="M23 245q0 7 5 15l139 210q9 14 16.5 17t21.5 3h22q8 0 14 -5.5t6 -13.5q0 -9 -6 -18l-139 -208l139 -208q6 -9 6 -18q0 -8 -6 -13.5t-14 -5.5h-22q-14 0 -21.5 3t-16.5 17l-139 210q-5 8 -5 15z" />
    <glyph glyph-name="guilsinglright" unicode="&#x203a;" horiz-adv-x="279" 
d="M256 245q0 -7 -5 -15l-139 -210q-9 -14 -16.5 -17t-21.5 -3h-22q-8 0 -14 5.5t-6 13.5q0 9 6 18l139 208l-139 208q-6 9 -6 18q0 8 6 13.5t14 5.5h22q14 0 21.5 -3t16.5 -17l139 -210q5 -8 5 -15z" />
    <glyph glyph-name="fraction" unicode="&#x2044;" horiz-adv-x="554" 
d="M475 683q5 8 11.5 12.5t16.5 4.5h27q8 0 12 -4.5t4 -10.5q0 -8 -5 -15l-463 -655q-5 -8 -11 -11.5t-17 -3.5h-26q-8 0 -12 5t-4 11q0 7 5 14z" />
    <glyph glyph-name="Euro" unicode="&#x20ac;" horiz-adv-x="490" 
d="M374 185q0 10 7 16.5t16 6.5h27q10 0 16.5 -6.5t6.5 -16.5v-40q0 -35 -11.5 -62t-31 -45.5t-46.5 -28t-58 -9.5h-40q-31 0 -58 9.5t-46.5 28t-31 45.5t-11.5 62v106h-57q-10 0 -16.5 6.5t-6.5 15.5v19q0 10 6.5 16.5t16.5 6.5h57v83h-57q-10 0 -16.5 6.5t-6.5 15.5v19
q0 10 6.5 16.5t16.5 6.5h57v93q0 35 11.5 62t31 45.5t46.5 28t58 9.5h40q31 0 58 -9.5t46.5 -28t31 -45.5t11.5 -62v-20q0 -10 -6.5 -16.5t-16.5 -6.5h-27q-9 0 -16 6.5t-7 16.5v14q0 41 -19.5 62.5t-58.5 21.5h-32q-39 0 -58.5 -21.5t-19.5 -62.5v-87h162q10 0 16.5 -6.5
t6.5 -16.5v-19q0 -9 -6.5 -15.5t-16.5 -6.5h-162v-83h162q10 0 16.5 -6.5t6.5 -16.5v-19q0 -9 -6.5 -15.5t-16.5 -6.5h-162v-100q0 -84 78 -84h32q39 0 58.5 21.5t19.5 62.5v34z" />
    <glyph glyph-name="trademark" unicode="&#x2122;" horiz-adv-x="780" 
d="M342 659q0 -20 -20 -20h-94v-249q0 -20 -20 -20h-27q-20 0 -20 20v249h-94q-20 0 -20 20v21q0 20 20 20h255q20 0 20 -20v-21zM466 390q0 -20 -20 -20h-25q-20 0 -20 20v288q0 9 5.5 15.5t14.5 6.5h52q10 0 16 -6t9 -15l65 -184l65 184q3 9 9 15t16 6h50q9 0 14.5 -6.5
t5.5 -15.5v-288q0 -20 -20 -20h-25q-20 0 -20 20v221l-56 -159q-2 -6 -7.5 -12t-16.5 -6h-32q-11 0 -16.5 6t-7.5 12l-56 153v-215z" />
    <glyph glyph-name="Omega" unicode="&#x2126;" horiz-adv-x="623" 
d="M502 549q0 36 -18 60t-54 24h-237q-36 0 -54 -24t-18 -60v-193q0 -36 19.5 -60t55.5 -24h26q44 0 44 -43v-207q0 -9 -7 -15.5t-16 -6.5h-152q-10 0 -16.5 6.5t-6.5 15.5v22q0 10 6.5 16.5t16.5 6.5h102v138q-31 0 -57.5 10.5t-46 29t-30.5 46t-11 61.5v201
q0 35 11.5 62.5t31 46t46.5 28.5t58 10h233q31 0 58 -10t46.5 -28.5t31 -46t11.5 -62.5v-201q0 -34 -11 -61.5t-30.5 -46t-46 -29t-57.5 -10.5v-138h102q10 0 16.5 -6.5t6.5 -16.5v-22q0 -9 -6.5 -15.5t-16.5 -6.5h-152q-9 0 -16 6.5t-7 15.5v207q0 43 44 43h26
q36 0 55.5 24t19.5 60v193z" />
    <glyph glyph-name="partialdiff" unicode="&#x2202;" horiz-adv-x="413" 
d="M44 343q0 70 37 108.5t100 38.5h7q34 0 60 -12.5t41 -32.5q-2 63 -21.5 105t-48.5 68q-19 16 -38.5 26t-40.5 18q-11 4 -21.5 10t-10.5 22q0 6 3 14.5t7 13.5q8 11 22 11q4 0 11.5 -2.5l16.5 -5.5q73 -27 118 -78q41 -45 57.5 -105t16.5 -147v-248q0 -70 -37 -108.5
t-100 -38.5h-42q-63 0 -100 38.5t-37 108.5v196zM287 345q0 36 -20.5 58t-56.5 22h-21q-36 0 -54 -22t-18 -58v-200q0 -36 18 -58t54 -22h26q36 0 54 22t18 58v200z" />
    <glyph glyph-name="Delta" unicode="&#x2206;" horiz-adv-x="473" 
d="M245 606q-2 7 -8 7t-8 -7l-111 -498q-4 -16 -4 -24t5 -12.5t13 -4.5h211q8 0 13 4.5t5 12.5q0 7 -4 23zM53 0q-9 0 -16 6.5t-7 16.5v34v13.5t2 14.5l144 592q2 10 10 16.5t18 6.5h69q10 0 18 -6.5t10 -16.5l140 -591q2 -6 2 -14.5v-13.5v-35q0 -10 -7 -16.5t-16 -6.5
h-367z" />
    <glyph glyph-name="product" unicode="&#x220f;" horiz-adv-x="533" 
d="M26 677q0 10 7 16.5t16 6.5h435q9 0 16 -6.5t7 -16.5v-21q0 -10 -7 -16.5t-16 -6.5h-51v-610q0 -10 -7 -16.5t-16 -6.5h-27q-9 0 -16 6.5t-7 16.5v610h-187v-610q0 -10 -7 -16.5t-16 -6.5h-27q-9 0 -16 6.5t-7 16.5v610h-51q-9 0 -16 6.5t-7 16.5v21z" />
    <glyph glyph-name="summation" unicode="&#x2211;" horiz-adv-x="409" 
d="M159 325q4 8 5 14t1 11t-1 11t-5 14l-106 223q-7 14 -11 28.5t-4 33.5v18q0 9 7 15.5t16 6.5h292q10 0 16.5 -6.5t6.5 -15.5v-23q0 -10 -6.5 -16.5t-16.5 -6.5h-241l113 -241q3 -8 6.5 -18.5t3.5 -19.5q0 -12 -3 -24t-7 -21l-113 -240h241q10 0 16.5 -6.5t6.5 -15.5v-23
q0 -10 -6.5 -16.5t-16.5 -6.5h-292q-9 0 -16 6.5t-7 16.5v16q0 19 4 33.5t11 28.5z" />
    <glyph glyph-name="minus" unicode="&#x2212;" horiz-adv-x="470" 
d="M69 327q-10 0 -16.5 6.5t-6.5 15.5v19q0 10 6.5 16.5t16.5 6.5h332q10 0 16.5 -6.5t6.5 -16.5v-19q0 -9 -6.5 -15.5t-16.5 -6.5h-332z" />
    <glyph glyph-name="radical" unicode="&#x221a;" horiz-adv-x="400" 
d="M301 680q3 20 23 20h31q20 0 20 -20q0 -3 -0.5 -10.5t-2.5 -15.5l-145 -631q-2 -10 -8 -16.5t-16 -6.5h-69q-10 0 -16 6.5t-8 16.5l-82 331q-2 9 -3 16t-1 10q0 10 6 15t15 5h29q18 0 23 -20l72 -321z" />
    <glyph glyph-name="infinity" unicode="&#x221e;" horiz-adv-x="783" 
d="M391 232q-11 -28 -42 -42.5t-69 -14.5h-95q-70 0 -103.5 37t-33.5 100v80q0 63 33.5 100t103.5 37h95q38 0 69 -15t42 -43q11 28 42.5 43t69.5 15h95q70 0 103.5 -37t33.5 -100v-80q0 -63 -33.5 -100t-103.5 -37h-95q-38 0 -69.5 14.5t-42.5 42.5zM276 243
q36 0 57.5 15.5t21.5 51.5v84q0 36 -21.5 51.5t-57.5 15.5h-83q-36 0 -53 -15.5t-17 -51.5v-84q0 -36 17 -51.5t53 -15.5h83zM590 243q36 0 53 15.5t17 51.5v84q0 36 -17 51.5t-53 15.5h-83q-36 0 -57.5 -15.5t-21.5 -51.5v-84q0 -36 21.5 -51.5t57.5 -15.5h83z" />
    <glyph glyph-name="integral" unicode="&#x222b;" horiz-adv-x="365" 
d="M146 574q0 56 32.5 91t102.5 35h42q10 0 16.5 -7t6.5 -16v-22q0 -10 -6.5 -16.5t-16.5 -6.5h-40q-35 0 -49.5 -17t-14.5 -46v-443q0 -56 -32.5 -91t-102.5 -35h-42q-10 0 -16.5 7t-6.5 16v22q0 10 6.5 16.5t16.5 6.5h40q35 0 49.5 17t14.5 46v443z" />
    <glyph glyph-name="approxequal" unicode="&#x2248;" horiz-adv-x="491" 
d="M135 259q-11 0 -17 -5t-6 -20q0 -22 -21 -22h-14q-15 0 -19 7.5t-4 21.5q0 34 17.5 58.5t55.5 24.5q17 0 51 -7.5t71 -16.5t68.5 -16.5t43.5 -7.5t16.5 7t4.5 23q0 21 21 21h14q15 0 19 -7.5t4 -21.5q0 -17 -3 -32t-11 -26q-9 -15 -26 -21t-33 -6q-19 0 -53.5 7t-70.5 16
t-66.5 16t-41.5 7zM135 432q-11 0 -17 -5t-6 -21q0 -21 -21 -21h-14q-15 0 -19 7.5t-4 21.5q0 33 17.5 58t55.5 25q17 0 51 -7.5t71 -17t68.5 -17t43.5 -7.5t16.5 7.5t4.5 22.5q0 22 21 22h14q15 0 19 -7.5t4 -21.5q0 -38 -14 -58q-9 -16 -26 -21.5t-33 -5.5q-19 0 -53.5 7
t-70.5 16t-66.5 16t-41.5 7z" />
    <glyph glyph-name="notequal" unicode="&#x2260;" horiz-adv-x="458" 
d="M239 241l-25 -154q-2 -10 -6 -16.5t-18 -6.5h-25q-17 0 -17 15q0 5 3 23l24 139h-112q-10 0 -16.5 6.5t-6.5 15.5v19q0 10 6.5 16.5t16.5 6.5h122l17 103h-139q-10 0 -16.5 6.5t-6.5 15.5v19q0 10 6.5 16.5t16.5 6.5h150l27 159q3 22 25 22h19q9 0 14.5 -5.5t5.5 -12.5
q0 -6 -0.5 -11t-2.5 -19l-23 -133h117q10 0 16.5 -6.5t6.5 -16.5v-19q0 -9 -6.5 -15.5t-16.5 -6.5h-128l-16 -103h144q10 0 16.5 -6.5t6.5 -16.5v-19q0 -9 -6.5 -15.5t-16.5 -6.5h-156z" />
    <glyph glyph-name="lessequal" unicode="&#x2264;" horiz-adv-x="335" 
d="M285 67q10 0 16.5 -6.5t6.5 -16.5v-22q0 -9 -6.5 -15.5t-16.5 -6.5h-225q-10 0 -16.5 6.5t-6.5 15.5v22q0 10 6.5 16.5t16.5 6.5h225zM36 385q0 7 5 15l139 210q9 14 16.5 17t21.5 3h22q8 0 14 -5.5t6 -13.5q0 -9 -6 -18l-139 -208l139 -208q6 -9 6 -18q0 -8 -6 -13.5
t-14 -5.5h-22q-14 0 -21.5 3t-16.5 17l-139 210q-5 8 -5 15z" />
    <glyph glyph-name="greaterequal" unicode="&#x2265;" horiz-adv-x="335" 
d="M299 385q0 -7 -5 -15l-139 -210q-9 -14 -16.5 -17t-21.5 -3h-22q-8 0 -14 5.5t-6 13.5q0 9 6 18l139 208l-139 208q-6 9 -6 18q0 8 6 13.5t14 5.5h22q14 0 21.5 -3t16.5 -17l139 -210q5 -8 5 -15zM275 67q10 0 16.5 -6.5t6.5 -16.5v-22q0 -9 -6.5 -15.5t-16.5 -6.5h-225
q-10 0 -16.5 6.5t-6.5 15.5v22q0 10 6.5 16.5t16.5 6.5h225z" />
    <glyph glyph-name="lozenge" unicode="&#x25ca;" horiz-adv-x="390" 
d="M212 700q10 0 17.5 -6.5t10.5 -16.5l119 -301q2 -6 3 -13t1 -12q0 -6 -1 -13t-3 -14l-119 -301q-3 -10 -10.5 -16.5t-17.5 -6.5h-34q-10 0 -17.5 6.5t-10.5 16.5l-119 301q-2 7 -3 14t-1 13q0 5 1 12t3 13l119 301q3 10 10.5 16.5t17.5 6.5h34zM97 350l98 -270l97 270
l-97 269z" />
    <glyph glyph-name="apple" horiz-adv-x="1237" 
d="M1119 473q0 69 -24 129.5t-68 105.5q-81 79 -194 79q-65 0 -118.5 -24.5t-95.5 -62.5q-49 43 -102.5 65.5t-113.5 22.5q-112 0 -192 -78q-44 -45 -68.5 -106.5t-24.5 -132.5q0 -74 25 -141.5t63 -124.5q62 -94 168 -180q45 -37 87 -64.5t74 -46.5t52 -28.5t21 -9.5
q9 -4 18 0q3 1 23.5 11t53 28.5t74 45.5t85.5 63q51 42 93.5 86.5t74.5 92.5q41 61 65 130.5t24 139.5zM1063 734q48 -50 73.5 -118.5t25.5 -144.5q0 -72 -23 -141t-62 -132t-91 -118.5t-109 -101.5t-116.5 -81.5t-113.5 -58.5q-20 -8 -29.5 -8.5t-30.5 8.5
q-56 24 -116 60.5t-117 83.5t-107.5 103t-89 118.5t-60.5 130.5t-22 138q0 77 26 146.5t74 118.5q45 47 104.5 70t123.5 23q69 0 123 -24t97 -62q93 84 213 84q66 0 124 -23.5t103 -70.5zM617 506q-48 0 -90 14q20 35 43 68.5t49 61.5q24 -26 47 -59t44 -70
q-21 -8 -45 -11.5t-48 -3.5zM1002 492q-3 7 -10 10q-7 2 -13 -1l-127 -62q-8 -5 -9.5 -12t0.5 -12l46 -105q7 -16 23 -9q8 3 10.5 10t-0.5 13l-39 90l32 15l15 -33q7 -16 23 -9t9 24l-15 34l31 15l31 -72q3 -7 10 -9.5t14 0.5q6 3 8.5 10t-0.5 14zM832 545q11 -22 34 -30
q12 -4 26.5 -2.5t27.5 9.5q23 13 33.5 38.5t-2.5 50.5l-32 67q-4 9 -12.5 9.5t-12.5 -1.5l-132 -74q-7 -4 -9 -11t2 -14t11 -9t14 2q11 6 19 11q4 2 7 4zM763 97q0 8 -5 13t-13 5h-175q25 14 55 32t55.5 41t42.5 52t17 65q0 23 -8 45t-23 39t-38 28t-53 11q-27 0 -50 -8.5
t-40 -24t-27 -36t-10 -44.5q0 -18 6 -35.5t17 -31t26.5 -21.5t34.5 -8q13 0 26.5 4t25 13t18.5 22.5t7 32.5q0 28 -12.5 36.5t-23.5 8.5q-14 0 -22.5 -9.5t-8.5 -23.5q0 -9 4 -15t10 -11q2 -2 -2 -12t-23 -10q-23 0 -34.5 19t-11.5 41q0 33 24.5 55.5t62.5 22.5
q44 0 64.5 -27t20.5 -59q0 -24 -10 -44.5t-25 -37.5t-34 -32t-37 -27q-38 -24 -67 -38.5t-31 -17.5q-8 -10 -4 -20q5 -11 16 -11h236q19 0 19 18zM252 412l112 -40q8 -3 14.5 0t8.5 11q3 7 0 14t-11 9l-113 41l15 47q2 8 -1 14.5t-11 8.5q-7 2 -13.5 -1t-8.5 -11l-42 -132
q-2 -7 0.5 -14t10.5 -9t14.5 1t8.5 11zM280 575q-8 -2 -12 -8t-2 -14t8 -12t14 -2l102 23l8 -22q-2 -4 -4 -8.5t-2 -9.5q0 -11 7 -18.5t18 -7.5t17 8q8 7 7.5 16.5t-3.5 17.5l-62 159q-3 7 -9.5 10t-14.5 0q-6 -3 -9 -9.5t0 -14.5l33 -86zM444 655q0 -14 8.5 -23.5
t22.5 -9.5t22.5 9.5t8.5 23.5t-8.5 23t-22.5 9t-22.5 -9.5t-8.5 -22.5zM834 751q48 0 90.5 -17t76.5 -52q39 -39 60.5 -93.5t21.5 -117.5q0 -70 -24 -132.5t-59 -114.5q-32 -48 -71 -89t-90 -82q-39 -32 -75.5 -56.5t-65.5 -42.5t-50.5 -29t-30.5 -15q-11 6 -33.5 18
t-51.5 30t-64 42t-72 54q-53 43 -92 85t-68 87q-38 57 -60 120.5t-22 126.5t22 117.5t61 93.5q70 68 166 68q58 0 105.5 -22.5t85.5 -54.5q-29 -30 -53.5 -65t-44.5 -70q-23 -40 -42 -85.5t-34.5 -88.5t-26.5 -81t-17 -64l-49 10q-7 2 -13 -2t-8 -11t2 -12.5t12 -7.5
l132 -29q7 -2 12.5 2t7.5 11q2 8 -2 13.5t-11 7.5l-47 10q5 21 13.5 51.5t20.5 66.5t28 76t36 80q25 -9 51.5 -14t55.5 -5q58 0 110 20q20 -39 35 -79t27 -76t20.5 -67t13.5 -54l-47 -10q-7 -2 -11 -7.5t-3 -12.5t7.5 -11.5t13.5 -2.5l132 28q7 2 11 7.5t2 12.5q-1 8 -7 12
t-13 2l-48 -10q0 1 -0.5 1.5t-0.5 1.5q-6 26 -16.5 62t-25 78.5t-34 88.5t-44.5 91q-19 34 -42.5 68t-51.5 63q42 36 88.5 56.5t100.5 20.5zM838 613l57 32l25 -50q6 -12 0 -24t-17 -18q-11 -5 -22.5 -4t-17.5 12zM586 3q0 -14 8.5 -23.5t22.5 -9.5t22.5 9.5t8.5 23.5
t-8.5 23t-22.5 9t-22.5 -9.5t-8.5 -22.5z" />
    <hkern u1="&#x22;" u2="&#x201e;" k="96" />
    <hkern u1="&#x22;" u2="&#x2e;" k="96" />
    <hkern u1="&#x23;" u2="&#x31;" k="13" />
    <hkern u1="&#x26;" u2="&#x2019;" k="14" />
    <hkern u1="&#x26;" u2="t" k="10" />
    <hkern u1="&#x26;" u2="W" k="11" />
    <hkern u1="&#x26;" u2="V" k="16" />
    <hkern u1="&#x26;" u2="T" k="28" />
    <hkern u1="&#x28;" u2="&#xef;" k="-6" />
    <hkern u1="&#x28;" u2="&#xc6;" k="15" />
    <hkern u1="&#x28;" u2="&#x7b;" k="18" />
    <hkern u1="&#x28;" u2="x" k="17" />
    <hkern u1="&#x28;" u2="w" k="31" />
    <hkern u1="&#x28;" u2="v" k="31" />
    <hkern u1="&#x28;" u2="t" k="20" />
    <hkern u1="&#x28;" u2="&#x34;" k="30" />
    <hkern u1="&#x2a;" u2="&#xef;" k="-16" />
    <hkern u1="&#x2a;" u2="&#xee;" k="-19" />
    <hkern u1="&#x2a;" u2="&#xc6;" k="32" />
    <hkern u1="&#x2a;" u2="g" k="8" />
    <hkern u1="&#x2a;" u2="T" k="16" />
    <hkern u1="&#x2b;" u2="&#x37;" k="15" />
    <hkern u1="&#x2b;" u2="&#x33;" k="10" />
    <hkern u1="&#x2b;" u2="&#x31;" k="15" />
    <hkern u1="&#x2d;" u2="&#x39;" k="14" />
    <hkern u1="&#x2d;" u2="&#x37;" k="42" />
    <hkern u1="&#x2d;" u2="&#x33;" k="17" />
    <hkern u1="&#x2d;" u2="&#x32;" k="29" />
    <hkern u1="&#x2d;" u2="&#x31;" k="26" />
    <hkern u1="&#x2e;" u2="&#x201d;" k="94" />
    <hkern u1="&#x2e;" u2="&#x22;" k="94" />
    <hkern u1="&#x2f;" u2="&#xc6;" k="16" />
    <hkern u1="&#x2f;" u2="g" k="10" />
    <hkern u1="&#x2f;" u2="&#x2f;" k="74" />
    <hkern u1="&#x30;" u2="]" k="10" />
    <hkern u1="&#x32;" u2="&#x2212;" k="15" />
    <hkern u1="&#x32;" u2="&#xb7;" k="10" />
    <hkern u1="&#x32;" u2="&#x34;" k="13" />
    <hkern u1="&#x32;" u2="&#x2d;" k="32" />
    <hkern u1="&#x33;" u2="&#x2212;" k="10" />
    <hkern u1="&#x34;" u2="&#x2212;" k="18" />
    <hkern u1="&#x34;" u2="&#x2044;" k="11" />
    <hkern u1="&#x34;" u2="&#xb7;" k="16" />
    <hkern u1="&#x34;" u2="&#xb0;" k="12" />
    <hkern u1="&#x34;" u2="&#x7d;" k="14" />
    <hkern u1="&#x34;" u2="]" k="15" />
    <hkern u1="&#x34;" u2="Y" k="10" />
    <hkern u1="&#x34;" u2="T" k="11" />
    <hkern u1="&#x34;" u2="&#x37;" k="10" />
    <hkern u1="&#x34;" u2="&#x29;" k="14" />
    <hkern u1="&#x37;" u2="&#x2212;" k="27" />
    <hkern u1="&#x37;" u2="&#x2044;" k="40" />
    <hkern u1="&#x37;" u2="&#xb7;" k="25" />
    <hkern u1="&#x37;" u2="&#xa2;" k="23" />
    <hkern u1="&#x37;" u2="z" k="14" />
    <hkern u1="&#x37;" u2="y" k="10" />
    <hkern u1="&#x37;" u2="x" k="10" />
    <hkern u1="&#x37;" u2="w" k="11" />
    <hkern u1="&#x37;" u2="v" k="10" />
    <hkern u1="&#x37;" u2="u" k="19" />
    <hkern u1="&#x37;" u2="s" k="24" />
    <hkern u1="&#x37;" u2="g" k="28" />
    <hkern u1="&#x37;" u2="a" k="27" />
    <hkern u1="&#x37;" u2="A" k="22" />
    <hkern u1="&#x37;" u2="&#x3d;" k="20" />
    <hkern u1="&#x37;" u2="&#x34;" k="27" />
    <hkern u1="&#x37;" u2="&#x2f;" k="26" />
    <hkern u1="&#x37;" u2="&#x2d;" k="35" />
    <hkern u1="&#x37;" u2="&#x2b;" k="18" />
    <hkern u1="&#x37;" u2="&#x23;" k="11" />
    <hkern u1="&#x38;" u2="&#x2212;" k="10" />
    <hkern u1="&#x39;" u2="]" k="10" />
    <hkern u1="&#x3d;" u2="&#x37;" k="10" />
    <hkern u1="&#x3d;" u2="&#x31;" k="23" />
    <hkern u1="&#x40;" u2="Y" k="12" />
    <hkern u1="&#x40;" u2="T" k="26" />
    <hkern u1="A" u2="&#xdf;" k="13" />
    <hkern u1="A" u2="&#x37;" k="16" />
    <hkern u1="A" u2="&#x31;" k="13" />
    <hkern u1="B" u2="&#xdf;" k="15" />
    <hkern u1="B" u2="]" k="10" />
    <hkern u1="B" u2="X" k="8" />
    <hkern u1="B" u2="T" k="7" />
    <hkern u1="C" u2="&#xdf;" k="13" />
    <hkern u1="D" u2="&#xdf;" k="14" />
    <hkern u1="E" u2="&#xdf;" k="19" />
    <hkern u1="F" u2="&#xef;" k="-6" />
    <hkern u1="F" u2="&#xdf;" k="27" />
    <hkern u1="F" u2="&#xc6;" k="47" />
    <hkern u1="F" u2="x" k="34" />
    <hkern u1="F" u2="t" k="9" />
    <hkern u1="F" u2="J" k="6" />
    <hkern u1="F" u2="&#x2f;" k="16" />
    <hkern u1="G" u2="&#xdf;" k="14" />
    <hkern u1="G" u2="T" k="5" />
    <hkern u1="H" u2="&#xdf;" k="13" />
    <hkern u1="I" u2="&#xdf;" k="13" />
    <hkern u1="J" u2="&#xdf;" k="16" />
    <hkern u1="K" u2="&#xdf;" k="17" />
    <hkern u1="K" u2="w" k="6" />
    <hkern u1="K" u2="v" k="6" />
    <hkern u1="K" u2="t" k="7" />
    <hkern u1="L" u2="&#xb7;" k="63" />
    <hkern u1="L" u2="&#x37;" k="24" />
    <hkern u1="L" u2="&#x34;" k="23" />
    <hkern u1="L" u2="&#x31;" k="19" />
    <hkern u1="M" u2="&#xdf;" k="13" />
    <hkern u1="N" u2="&#xdf;" k="13" />
    <hkern u1="O" u2="&#xdf;" k="14" />
    <hkern u1="P" u2="&#xdf;" k="15" />
    <hkern u1="P" u2="&#xc6;" k="56" />
    <hkern u1="P" u2="X" k="9" />
    <hkern u1="P" u2="&#x2f;" k="16" />
    <hkern u1="Q" u2="&#x2122;" k="9" />
    <hkern u1="Q" u2="&#xdf;" k="17" />
    <hkern u1="Q" u2="T" k="11" />
    <hkern u1="R" u2="&#xdf;" k="15" />
    <hkern u1="R" u2="&#x7d;" k="10" />
    <hkern u1="R" u2="]" k="10" />
    <hkern u1="R" u2="T" k="12" />
    <hkern u1="R" u2="&#x29;" k="10" />
    <hkern u1="S" u2="&#xdf;" k="14" />
    <hkern u1="T" u2="&#x131;" k="52" />
    <hkern u1="T" u2="&#xef;" k="-13" />
    <hkern u1="T" u2="&#xdf;" k="40" />
    <hkern u1="T" u2="&#xc6;" k="50" />
    <hkern u1="T" u2="&#xae;" k="18" />
    <hkern u1="T" u2="x" k="46" />
    <hkern u1="T" u2="w" k="49" />
    <hkern u1="T" u2="v" k="48" />
    <hkern u1="T" u2="t" k="33" />
    <hkern u1="T" u2="g" k="56" />
    <hkern u1="T" u2="&#x40;" k="30" />
    <hkern u1="T" u2="&#x34;" k="33" />
    <hkern u1="T" u2="&#x2f;" k="28" />
    <hkern u1="T" u2="&#x2a;" k="16" />
    <hkern u1="T" u2="&#x26;" k="12" />
    <hkern u1="U" u2="&#xdf;" k="16" />
    <hkern u1="V" u2="&#x131;" k="11" />
    <hkern u1="V" u2="&#xdf;" k="23" />
    <hkern u1="V" u2="&#xc6;" k="34" />
    <hkern u1="V" u2="g" k="18" />
    <hkern u1="V" u2="&#x34;" k="11" />
    <hkern u1="V" u2="&#x2f;" k="19" />
    <hkern u1="W" u2="&#xdf;" k="21" />
    <hkern u1="W" u2="&#xc6;" k="21" />
    <hkern u1="W" u2="g" k="8" />
    <hkern u1="W" u2="&#x2f;" k="14" />
    <hkern u1="X" u2="&#xdf;" k="19" />
    <hkern u1="X" u2="w" k="17" />
    <hkern u1="X" u2="v" k="19" />
    <hkern u1="X" u2="t" k="11" />
    <hkern u1="X" u2="g" k="12" />
    <hkern u1="Y" u2="&#x131;" k="34" />
    <hkern u1="Y" u2="&#xef;" k="-8" />
    <hkern u1="Y" u2="&#xdf;" k="29" />
    <hkern u1="Y" u2="&#x40;" k="14" />
    <hkern u1="Y" u2="&#x34;" k="28" />
    <hkern u1="Z" u2="&#xdf;" k="22" />
    <hkern u1="Z" u2="&#x34;" k="15" />
    <hkern u1="[" u2="&#xef;" k="-6" />
    <hkern u1="[" u2="&#xc6;" k="15" />
    <hkern u1="[" u2="&#x7b;" k="20" />
    <hkern u1="[" u2="x" k="17" />
    <hkern u1="[" u2="w" k="40" />
    <hkern u1="[" u2="v" k="40" />
    <hkern u1="[" u2="t" k="22" />
    <hkern u1="[" u2="&#x36;" k="10" />
    <hkern u1="[" u2="&#x34;" k="38" />
    <hkern u1="[" u2="&#x30;" k="10" />
    <hkern u1="\" u2="&#x2019;" k="21" />
    <hkern u1="\" u2="w" k="11" />
    <hkern u1="\" u2="v" k="12" />
    <hkern u1="\" u2="t" k="12" />
    <hkern u1="\" u2="W" k="14" />
    <hkern u1="\" u2="V" k="19" />
    <hkern u1="\" u2="T" k="28" />
    <hkern u1="\" u2="&#x37;" k="20" />
    <hkern u1="\" u2="&#x31;" k="18" />
    <hkern u1="a" u2="Y" k="39" />
    <hkern u1="a" u2="W" k="9" />
    <hkern u1="a" u2="V" k="17" />
    <hkern u1="a" u2="T" k="53" />
    <hkern u1="a" u2="&#x37;" k="24" />
    <hkern u1="a" u2="&#x31;" k="21" />
    <hkern u1="c" u2="Y" k="45" />
    <hkern u1="c" u2="X" k="5" />
    <hkern u1="c" u2="W" k="5" />
    <hkern u1="c" u2="V" k="13" />
    <hkern u1="c" u2="T" k="50" />
    <hkern u1="c" u2="&#x37;" k="20" />
    <hkern u1="c" u2="&#x31;" k="19" />
    <hkern u1="e" u2="Y" k="43" />
    <hkern u1="e" u2="W" k="5" />
    <hkern u1="e" u2="V" k="13" />
    <hkern u1="e" u2="T" k="49" />
    <hkern u1="e" u2="&#x37;" k="21" />
    <hkern u1="e" u2="&#x31;" k="20" />
    <hkern u1="f" u2="g" k="6" />
    <hkern u1="f" u2="Y" k="-9" />
    <hkern u1="f" u2="T" k="-5" />
    <hkern u1="f" u2="&#x34;" k="10" />
    <hkern u1="f" u2="&#x31;" k="13" />
    <hkern u1="f" u2="&#x2f;" k="11" />
    <hkern u1="g" u2="&#x2122;" k="19" />
    <hkern u1="g" u2="Y" k="9" />
    <hkern u1="g" u2="T" k="52" />
    <hkern u1="g" u2="&#x31;" k="16" />
    <hkern u1="k" u2="&#x2122;" k="32" />
    <hkern u1="k" u2="&#x7d;" k="28" />
    <hkern u1="k" u2="]" k="29" />
    <hkern u1="k" u2="Y" k="32" />
    <hkern u1="k" u2="W" k="5" />
    <hkern u1="k" u2="V" k="11" />
    <hkern u1="k" u2="T" k="49" />
    <hkern u1="k" u2="&#x31;" k="20" />
    <hkern u1="k" u2="&#x2a;" k="10" />
    <hkern u1="k" u2="&#x29;" k="28" />
    <hkern u1="l" u2="&#xb7;" k="37" />
    <hkern u1="r" u2="&#x2122;" k="19" />
    <hkern u1="r" u2="&#x7d;" k="34" />
    <hkern u1="r" u2="g" k="4" />
    <hkern u1="r" u2="]" k="40" />
    <hkern u1="r" u2="Z" k="42" />
    <hkern u1="r" u2="Y" k="9" />
    <hkern u1="r" u2="X" k="25" />
    <hkern u1="r" u2="T" k="53" />
    <hkern u1="r" u2="&#x34;" k="11" />
    <hkern u1="r" u2="&#x31;" k="17" />
    <hkern u1="r" u2="&#x2f;" k="16" />
    <hkern u1="r" u2="&#x29;" k="31" />
    <hkern u1="s" u2="Y" k="43" />
    <hkern u1="s" u2="W" k="5" />
    <hkern u1="s" u2="V" k="12" />
    <hkern u1="s" u2="T" k="49" />
    <hkern u1="s" u2="&#x37;" k="18" />
    <hkern u1="s" u2="&#x31;" k="19" />
    <hkern u1="t" u2="&#x2122;" k="20" />
    <hkern u1="t" u2="&#x7d;" k="19" />
    <hkern u1="t" u2="]" k="19" />
    <hkern u1="t" u2="Y" k="11" />
    <hkern u1="t" u2="T" k="50" />
    <hkern u1="t" u2="&#x34;" k="11" />
    <hkern u1="t" u2="&#x31;" k="13" />
    <hkern u1="t" u2="&#x29;" k="19" />
    <hkern u1="v" u2="&#x2122;" k="22" />
    <hkern u1="v" u2="&#x7d;" k="35" />
    <hkern u1="v" u2="]" k="40" />
    <hkern u1="v" u2="Z" k="26" />
    <hkern u1="v" u2="Y" k="14" />
    <hkern u1="v" u2="X" k="18" />
    <hkern u1="v" u2="T" k="48" />
    <hkern u1="v" u2="&#x31;" k="18" />
    <hkern u1="v" u2="&#x2f;" k="12" />
    <hkern u1="v" u2="&#x29;" k="31" />
    <hkern u1="w" u2="&#x2122;" k="22" />
    <hkern u1="w" u2="&#x7d;" k="35" />
    <hkern u1="w" u2="]" k="40" />
    <hkern u1="w" u2="Z" k="24" />
    <hkern u1="w" u2="Y" k="14" />
    <hkern u1="w" u2="X" k="17" />
    <hkern u1="w" u2="T" k="49" />
    <hkern u1="w" u2="&#x31;" k="18" />
    <hkern u1="w" u2="&#x2f;" k="11" />
    <hkern u1="w" u2="&#x29;" k="31" />
    <hkern u1="x" u2="&#x2122;" k="23" />
    <hkern u1="x" u2="&#x7d;" k="17" />
    <hkern u1="x" u2="g" k="16" />
    <hkern u1="x" u2="]" k="17" />
    <hkern u1="x" u2="Y" k="16" />
    <hkern u1="x" u2="T" k="46" />
    <hkern u1="x" u2="&#x31;" k="15" />
    <hkern u1="x" u2="&#x29;" k="17" />
    <hkern u1="y" u2="Z" k="26" />
    <hkern u1="y" u2="Y" k="14" />
    <hkern u1="y" u2="X" k="18" />
    <hkern u1="y" u2="T" k="49" />
    <hkern u1="y" u2="&#x31;" k="18" />
    <hkern u1="z" u2="Y" k="19" />
    <hkern u1="z" u2="T" k="53" />
    <hkern u1="z" u2="&#x34;" k="10" />
    <hkern u1="z" u2="&#x31;" k="18" />
    <hkern u1="&#x7b;" u2="&#xef;" k="-6" />
    <hkern u1="&#x7b;" u2="&#xc6;" k="15" />
    <hkern u1="&#x7b;" u2="&#x7b;" k="19" />
    <hkern u1="&#x7b;" u2="x" k="17" />
    <hkern u1="&#x7b;" u2="w" k="35" />
    <hkern u1="&#x7b;" u2="v" k="34" />
    <hkern u1="&#x7b;" u2="t" k="21" />
    <hkern u1="&#x7b;" u2="&#x34;" k="33" />
    <hkern u1="&#x7d;" u2="&#x7d;" k="19" />
    <hkern u1="&#x7d;" u2="]" k="20" />
    <hkern u1="&#x7d;" u2="&#x29;" k="18" />
    <hkern u1="&#xa3;" u2="&#x34;" k="13" />
    <hkern u1="&#xae;" u2="T" k="18" />
    <hkern u1="&#xb0;" u2="&#x34;" k="18" />
    <hkern u1="&#xb7;" u2="l" k="37" />
    <hkern u1="&#xb7;" u2="&#x37;" k="38" />
    <hkern u1="&#xb7;" u2="&#x33;" k="22" />
    <hkern u1="&#xb7;" u2="&#x32;" k="14" />
    <hkern u1="&#xb7;" u2="&#x31;" k="24" />
    <hkern u1="&#xbf;" u2="V" k="15" />
    <hkern u1="&#xbf;" u2="T" k="23" />
    <hkern u1="&#xde;" u2="&#x2122;" k="24" />
    <hkern u1="&#xde;" u2="&#xc6;" k="12" />
    <hkern u1="&#xde;" u2="&#x7d;" k="31" />
    <hkern u1="&#xde;" u2="x" k="6" />
    <hkern u1="&#xde;" u2="]" k="35" />
    <hkern u1="&#xde;" u2="X" k="44" />
    <hkern u1="&#xde;" u2="T" k="60" />
    <hkern u1="&#xde;" u2="&#x29;" k="27" />
    <hkern u1="&#xee;" u2="&#x2a;" k="-20" />
    <hkern u1="&#xef;" u2="&#x7d;" k="-7" />
    <hkern u1="&#xef;" u2="]" k="-8" />
    <hkern u1="&#xef;" u2="&#x2a;" k="-17" />
    <hkern u1="&#xef;" u2="&#x29;" k="-7" />
    <hkern u1="&#xf0;" u2="&#x2122;" k="9" />
    <hkern u1="&#xf0;" u2="&#x7d;" k="13" />
    <hkern u1="&#xf0;" u2="]" k="13" />
    <hkern u1="&#xf0;" u2="&#x29;" k="12" />
    <hkern u1="&#x2019;" u2="&#x2f;" k="23" />
    <hkern u1="&#x201c;" u2="&#x2e;" k="94" />
    <hkern u1="&#x201d;" u2="&#x2e;" k="96" />
    <hkern u1="&#x201e;" u2="&#x22;" k="94" />
    <hkern u1="&#x2044;" u2="&#x34;" k="44" />
    <hkern u1="&#x2122;" u2="&#xc6;" k="29" />
    <hkern u1="&#x2212;" u2="&#x37;" k="44" />
    <hkern u1="&#x2212;" u2="&#x33;" k="29" />
    <hkern u1="&#x2212;" u2="&#x32;" k="20" />
    <hkern u1="&#x2212;" u2="&#x31;" k="25" />
    <hkern g1="quotedbl,quotesingle"
	g2="hyphen,endash,emdash"
	k="32" />
    <hkern g1="quotedbl,quotesingle"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="87" />
    <hkern g1="quotedbl,quotesingle"
	g2="slash"
	k="23" />
    <hkern g1="quotedbl,quotesingle"
	g2="four"
	k="18" />
    <hkern g1="quotedbl,quotesingle"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="18" />
    <hkern g1="quotedbl,quotesingle"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="9" />
    <hkern g1="quotedbl,quotesingle"
	g2="AE"
	k="33" />
    <hkern g1="quotedbl,quotesingle"
	g2="guillemotleft,guilsinglleft"
	k="28" />
    <hkern g1="parenleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="17" />
    <hkern g1="parenleft"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="32" />
    <hkern g1="parenleft"
	g2="f,germandbls,fi,fl"
	k="19" />
    <hkern g1="parenleft"
	g2="m,n,p,r,ntilde"
	k="30" />
    <hkern g1="parenleft"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe"
	k="33" />
    <hkern g1="parenleft"
	g2="s,scaron"
	k="32" />
    <hkern g1="parenleft"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="33" />
    <hkern g1="parenleft"
	g2="y,yacute,ydieresis"
	k="29" />
    <hkern g1="parenleft"
	g2="z,zcaron"
	k="21" />
    <hkern g1="asterisk"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="18" />
    <hkern g1="asterisk"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="10" />
    <hkern g1="asterisk"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe"
	k="9" />
    <hkern g1="asterisk"
	g2="z,zcaron"
	k="9" />
    <hkern g1="asterisk"
	g2="Z,Zcaron"
	k="11" />
    <hkern g1="hyphen,endash,emdash"
	g2="quotedbl,quotesingle"
	k="28" />
    <hkern g1="hyphen,endash,emdash"
	g2="Y,Yacute,Ydieresis"
	k="36" />
    <hkern g1="hyphen,endash,emdash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="8" />
    <hkern g1="hyphen,endash,emdash"
	g2="AE"
	k="15" />
    <hkern g1="hyphen,endash,emdash"
	g2="f,germandbls,fi,fl"
	k="17" />
    <hkern g1="hyphen,endash,emdash"
	g2="z,zcaron"
	k="32" />
    <hkern g1="hyphen,endash,emdash"
	g2="Z,Zcaron"
	k="27" />
    <hkern g1="hyphen,endash,emdash"
	g2="T"
	k="39" />
    <hkern g1="hyphen,endash,emdash"
	g2="V"
	k="16" />
    <hkern g1="hyphen,endash,emdash"
	g2="W"
	k="11" />
    <hkern g1="hyphen,endash,emdash"
	g2="X"
	k="28" />
    <hkern g1="hyphen,endash,emdash"
	g2="t"
	k="13" />
    <hkern g1="hyphen,endash,emdash"
	g2="x"
	k="24" />
    <hkern g1="hyphen,endash,emdash"
	g2="quoteright,quotedblright"
	k="28" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="quotedbl,quotesingle"
	k="87" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="Y,Yacute,Ydieresis"
	k="39" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="four"
	k="15" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="f,germandbls,fi,fl"
	k="13" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="y,yacute,ydieresis"
	k="20" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="T"
	k="37" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="V"
	k="30" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="W"
	k="19" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="t"
	k="13" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="quoteright,quotedblright"
	k="87" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="one"
	k="22" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="seven"
	k="29" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="v"
	k="20" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="w"
	k="17" />
    <hkern g1="comma,period,quotesinglbase,quotedblbase"
	g2="quoteleft,quotedblleft"
	k="87" />
    <hkern g1="slash"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="12" />
    <hkern g1="slash"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="11" />
    <hkern g1="slash"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe"
	k="10" />
    <hkern g1="four"
	g2="quotedbl,quotesingle"
	k="12" />
    <hkern g1="four"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="16" />
    <hkern g1="seven"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="43" />
    <hkern g1="seven"
	g2="m,n,p,r,ntilde"
	k="20" />
    <hkern g1="seven"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe"
	k="27" />
    <hkern g1="bracketleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="18" />
    <hkern g1="bracketleft"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="40" />
    <hkern g1="bracketleft"
	g2="f,germandbls,fi,fl"
	k="21" />
    <hkern g1="bracketleft"
	g2="m,n,p,r,ntilde"
	k="32" />
    <hkern g1="bracketleft"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe"
	k="41" />
    <hkern g1="bracketleft"
	g2="s,scaron"
	k="39" />
    <hkern g1="bracketleft"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="41" />
    <hkern g1="bracketleft"
	g2="y,yacute,ydieresis"
	k="36" />
    <hkern g1="bracketleft"
	g2="z,zcaron"
	k="22" />
    <hkern g1="bracketleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE"
	k="10" />
    <hkern g1="backslash"
	g2="quotedbl,quotesingle"
	k="21" />
    <hkern g1="backslash"
	g2="Y,Yacute,Ydieresis"
	k="25" />
    <hkern g1="backslash"
	g2="f,germandbls,fi,fl"
	k="11" />
    <hkern g1="backslash"
	g2="y,yacute,ydieresis"
	k="12" />
    <hkern g1="braceleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="18" />
    <hkern g1="braceleft"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="36" />
    <hkern g1="braceleft"
	g2="f,germandbls,fi,fl"
	k="20" />
    <hkern g1="braceleft"
	g2="m,n,p,r,ntilde"
	k="32" />
    <hkern g1="braceleft"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe"
	k="37" />
    <hkern g1="braceleft"
	g2="s,scaron"
	k="35" />
    <hkern g1="braceleft"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="36" />
    <hkern g1="braceleft"
	g2="y,yacute,ydieresis"
	k="32" />
    <hkern g1="braceleft"
	g2="z,zcaron"
	k="22" />
    <hkern g1="braceleft"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE"
	k="10" />
    <hkern g1="trademark"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="16" />
    <hkern g1="questiondown"
	g2="Y,Yacute,Ydieresis"
	k="22" />
    <hkern g1="questiondown"
	g2="f,germandbls,fi,fl"
	k="10" />
    <hkern g1="quoteleft,quotedblleft"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="87" />
    <hkern g1="quoteleft,quotedblleft"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="16" />
    <hkern g1="quoteleft,quotedblleft"
	g2="AE"
	k="30" />
    <hkern g1="quoteright,quotedblright"
	g2="hyphen,endash,emdash"
	k="32" />
    <hkern g1="quoteright,quotedblright"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="87" />
    <hkern g1="quoteright,quotedblright"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="18" />
    <hkern g1="quoteright,quotedblright"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="9" />
    <hkern g1="quoteright,quotedblright"
	g2="AE"
	k="33" />
    <hkern g1="quoteright,quotedblright"
	g2="guillemotleft,guilsinglleft"
	k="28" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="Y,Yacute,Ydieresis"
	k="12" />
    <hkern g1="guillemotleft,guilsinglleft"
	g2="T"
	k="35" />
    <hkern g1="guillemotright,guilsinglright"
	g2="quotedbl,quotesingle"
	k="24" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Y,Yacute,Ydieresis"
	k="34" />
    <hkern g1="guillemotright,guilsinglright"
	g2="f,germandbls,fi,fl"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="z,zcaron"
	k="17" />
    <hkern g1="guillemotright,guilsinglright"
	g2="Z,Zcaron"
	k="12" />
    <hkern g1="guillemotright,guilsinglright"
	g2="T"
	k="36" />
    <hkern g1="guillemotright,guilsinglright"
	g2="V"
	k="14" />
    <hkern g1="guillemotright,guilsinglright"
	g2="X"
	k="12" />
    <hkern g1="guillemotright,guilsinglright"
	g2="t"
	k="10" />
    <hkern g1="guillemotright,guilsinglright"
	g2="x"
	k="15" />
    <hkern g1="guillemotright,guilsinglright"
	g2="quoteright,quotedblright"
	k="24" />
    <hkern g1="Thorn"
	g2="Y,Yacute,Ydieresis"
	k="19" />
    <hkern g1="Thorn"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="34" />
    <hkern g1="Thorn"
	g2="Z,Zcaron"
	k="29" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="quotedbl,quotesingle"
	k="16" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="Y,Yacute,Ydieresis"
	k="36" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="hyphen,endash,emdash"
	k="8" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="f,germandbls,fi,fl"
	k="6" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="y,yacute,ydieresis"
	k="6" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="T"
	k="43" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="V"
	k="19" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="W"
	k="12" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="t"
	k="6" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="quoteright,quotedblright"
	k="16" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="v"
	k="6" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="w"
	k="5" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="quoteleft,quotedblleft"
	k="14" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="parenright"
	k="18" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="asterisk"
	k="17" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="question"
	k="16" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="backslash"
	k="12" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="bracketright"
	k="18" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="braceright"
	k="18" />
    <hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	g2="trademark"
	k="31" />
    <hkern g1="C,Ccedilla"
	g2="X"
	k="5" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="hyphen,endash,emdash"
	k="13" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="guillemotleft,guilsinglleft"
	k="13" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="f,germandbls,fi,fl"
	k="12" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="y,yacute,ydieresis"
	k="7" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="t"
	k="14" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="v"
	k="7" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="w"
	k="7" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE"
	k="6" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="J"
	k="7" />
    <hkern g1="E,AE,Egrave,Eacute,Ecircumflex,Edieresis,OE"
	g2="S,Scaron"
	k="6" />
    <hkern g1="F"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="49" />
    <hkern g1="F"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="17" />
    <hkern g1="F"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="8" />
    <hkern g1="F"
	g2="f,germandbls,fi,fl"
	k="11" />
    <hkern g1="F"
	g2="z,zcaron"
	k="32" />
    <hkern g1="F"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE"
	k="5" />
    <hkern g1="F"
	g2="S,Scaron"
	k="8" />
    <hkern g1="F"
	g2="guillemotright,guilsinglright"
	k="13" />
    <hkern g1="K"
	g2="f,germandbls,fi,fl"
	k="8" />
    <hkern g1="K"
	g2="y,yacute,ydieresis"
	k="6" />
    <hkern g1="L,Lslash"
	g2="quotedbl,quotesingle"
	k="60" />
    <hkern g1="L,Lslash"
	g2="Y,Yacute,Ydieresis"
	k="72" />
    <hkern g1="L,Lslash"
	g2="hyphen,endash,emdash"
	k="64" />
    <hkern g1="L,Lslash"
	g2="guillemotleft,guilsinglleft"
	k="28" />
    <hkern g1="L,Lslash"
	g2="f,germandbls,fi,fl"
	k="59" />
    <hkern g1="L,Lslash"
	g2="y,yacute,ydieresis"
	k="57" />
    <hkern g1="L,Lslash"
	g2="T"
	k="82" />
    <hkern g1="L,Lslash"
	g2="V"
	k="64" />
    <hkern g1="L,Lslash"
	g2="W"
	k="61" />
    <hkern g1="L,Lslash"
	g2="t"
	k="23" />
    <hkern g1="L,Lslash"
	g2="quoteright,quotedblright"
	k="60" />
    <hkern g1="L,Lslash"
	g2="v"
	k="57" />
    <hkern g1="L,Lslash"
	g2="w"
	k="57" />
    <hkern g1="L,Lslash"
	g2="quoteleft,quotedblleft"
	k="60" />
    <hkern g1="L,Lslash"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE"
	k="8" />
    <hkern g1="L,Lslash"
	g2="parenright"
	k="17" />
    <hkern g1="L,Lslash"
	g2="asterisk"
	k="61" />
    <hkern g1="L,Lslash"
	g2="question"
	k="26" />
    <hkern g1="L,Lslash"
	g2="backslash"
	k="22" />
    <hkern g1="L,Lslash"
	g2="bracketright"
	k="17" />
    <hkern g1="L,Lslash"
	g2="braceright"
	k="17" />
    <hkern g1="L,Lslash"
	g2="trademark"
	k="65" />
    <hkern g1="L,Lslash"
	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis"
	k="11" />
    <hkern g1="L,Lslash"
	g2="registered"
	k="20" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash"
	g2="T"
	k="7" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash"
	g2="X"
	k="10" />
    <hkern g1="D,O,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash"
	g2="bracketright"
	k="10" />
    <hkern g1="P"
	g2="hyphen,endash,emdash"
	k="16" />
    <hkern g1="P"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="62" />
    <hkern g1="P"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="22" />
    <hkern g1="P"
	g2="guillemotleft,guilsinglleft"
	k="17" />
    <hkern g1="P"
	g2="Z,Zcaron"
	k="5" />
    <hkern g1="Q"
	g2="Y,Yacute,Ydieresis"
	k="6" />
    <hkern g1="R"
	g2="Y,Yacute,Ydieresis"
	k="5" />
    <hkern g1="S,Scaron"
	g2="X"
	k="6" />
    <hkern g1="T"
	g2="hyphen,endash,emdash"
	k="39" />
    <hkern g1="T"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="37" />
    <hkern g1="T"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="43" />
    <hkern g1="T"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="50" />
    <hkern g1="T"
	g2="guillemotleft,guilsinglleft"
	k="36" />
    <hkern g1="T"
	g2="f,germandbls,fi,fl"
	k="30" />
    <hkern g1="T"
	g2="m,n,p,r,ntilde"
	k="51" />
    <hkern g1="T"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe"
	k="52" />
    <hkern g1="T"
	g2="s,scaron"
	k="49" />
    <hkern g1="T"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="54" />
    <hkern g1="T"
	g2="y,yacute,ydieresis"
	k="48" />
    <hkern g1="T"
	g2="z,zcaron"
	k="53" />
    <hkern g1="T"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE"
	k="7" />
    <hkern g1="T"
	g2="S,Scaron"
	k="6" />
    <hkern g1="T"
	g2="guillemotright,guilsinglright"
	k="35" />
    <hkern g1="T"
	g2="colon,semicolon"
	k="36" />
    <hkern g1="V"
	g2="hyphen,endash,emdash"
	k="16" />
    <hkern g1="V"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="30" />
    <hkern g1="V"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="19" />
    <hkern g1="V"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="18" />
    <hkern g1="V"
	g2="guillemotleft,guilsinglleft"
	k="14" />
    <hkern g1="V"
	g2="m,n,p,r,ntilde"
	k="11" />
    <hkern g1="V"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe"
	k="17" />
    <hkern g1="V"
	g2="s,scaron"
	k="12" />
    <hkern g1="V"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="10" />
    <hkern g1="V"
	g2="z,zcaron"
	k="5" />
    <hkern g1="W"
	g2="hyphen,endash,emdash"
	k="11" />
    <hkern g1="W"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="19" />
    <hkern g1="W"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="12" />
    <hkern g1="W"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="8" />
    <hkern g1="W"
	g2="m,n,p,r,ntilde"
	k="5" />
    <hkern g1="W"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe"
	k="7" />
    <hkern g1="W"
	g2="s,scaron"
	k="5" />
    <hkern g1="W"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="5" />
    <hkern g1="X"
	g2="hyphen,endash,emdash"
	k="29" />
    <hkern g1="X"
	g2="guillemotleft,guilsinglleft"
	k="13" />
    <hkern g1="X"
	g2="f,germandbls,fi,fl"
	k="11" />
    <hkern g1="X"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe"
	k="8" />
    <hkern g1="X"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="7" />
    <hkern g1="X"
	g2="y,yacute,ydieresis"
	k="19" />
    <hkern g1="X"
	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE"
	k="10" />
    <hkern g1="X"
	g2="S,Scaron"
	k="6" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="hyphen,endash,emdash"
	k="35" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="39" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="slash"
	k="25" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="36" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="46" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="AE"
	k="56" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="guillemotleft,guilsinglleft"
	k="34" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="f,germandbls,fi,fl"
	k="8" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="m,n,p,r,ntilde"
	k="34" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe"
	k="37" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="s,scaron"
	k="43" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="36" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="y,yacute,ydieresis"
	k="13" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="z,zcaron"
	k="23" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="t"
	k="6" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="x"
	k="16" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="v"
	k="13" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="w"
	k="14" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="guillemotright,guilsinglright"
	k="12" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="colon,semicolon"
	k="17" />
    <hkern g1="Y,Yacute,Ydieresis"
	g2="g"
	k="41" />
    <hkern g1="Z,Zcaron"
	g2="hyphen,endash,emdash"
	k="43" />
    <hkern g1="Z,Zcaron"
	g2="guillemotleft,guilsinglleft"
	k="25" />
    <hkern g1="Z,Zcaron"
	g2="f,germandbls,fi,fl"
	k="11" />
    <hkern g1="Z,Zcaron"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe"
	k="8" />
    <hkern g1="Z,Zcaron"
	g2="u,ugrave,uacute,ucircumflex,udieresis"
	k="11" />
    <hkern g1="Z,Zcaron"
	g2="y,yacute,ydieresis"
	k="22" />
    <hkern g1="Z,Zcaron"
	g2="t"
	k="12" />
    <hkern g1="Z,Zcaron"
	g2="v"
	k="22" />
    <hkern g1="Z,Zcaron"
	g2="w"
	k="21" />
    <hkern g1="Z,Zcaron"
	g2="g"
	k="10" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="f,germandbls,fi,fl"
	k="6" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="t"
	k="5" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="parenright"
	k="30" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="asterisk"
	k="10" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="question"
	k="24" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="backslash"
	k="11" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="bracketright"
	k="31" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="braceright"
	k="31" />
    <hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring"
	g2="trademark"
	k="36" />
    <hkern g1="c,ccedilla"
	g2="x"
	k="12" />
    <hkern g1="c,ccedilla"
	g2="parenright"
	k="32" />
    <hkern g1="c,ccedilla"
	g2="question"
	k="19" />
    <hkern g1="c,ccedilla"
	g2="bracketright"
	k="40" />
    <hkern g1="c,ccedilla"
	g2="braceright"
	k="35" />
    <hkern g1="c,ccedilla"
	g2="trademark"
	k="33" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="x"
	k="14" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="parenright"
	k="32" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="question"
	k="20" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="bracketright"
	k="40" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="braceright"
	k="36" />
    <hkern g1="e,ae,egrave,eacute,ecircumflex,edieresis,oe"
	g2="trademark"
	k="33" />
    <hkern g1="f"
	g2="hyphen,endash,emdash"
	k="26" />
    <hkern g1="f"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="22" />
    <hkern g1="f"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="10" />
    <hkern g1="f"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="6" />
    <hkern g1="f"
	g2="guillemotleft,guilsinglleft"
	k="17" />
    <hkern g1="f"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe"
	k="6" />
    <hkern g1="h,m,n,ntilde"
	g2="Y,Yacute,Ydieresis"
	k="40" />
    <hkern g1="h,m,n,ntilde"
	g2="f,germandbls,fi,fl"
	k="5" />
    <hkern g1="h,m,n,ntilde"
	g2="T"
	k="53" />
    <hkern g1="h,m,n,ntilde"
	g2="V"
	k="17" />
    <hkern g1="h,m,n,ntilde"
	g2="W"
	k="9" />
    <hkern g1="h,m,n,ntilde"
	g2="t"
	k="5" />
    <hkern g1="h,m,n,ntilde"
	g2="one"
	k="20" />
    <hkern g1="h,m,n,ntilde"
	g2="seven"
	k="24" />
    <hkern g1="h,m,n,ntilde"
	g2="parenright"
	k="30" />
    <hkern g1="h,m,n,ntilde"
	g2="asterisk"
	k="10" />
    <hkern g1="h,m,n,ntilde"
	g2="question"
	k="23" />
    <hkern g1="h,m,n,ntilde"
	g2="backslash"
	k="11" />
    <hkern g1="h,m,n,ntilde"
	g2="bracketright"
	k="31" />
    <hkern g1="h,m,n,ntilde"
	g2="braceright"
	k="31" />
    <hkern g1="h,m,n,ntilde"
	g2="trademark"
	k="35" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn"
	g2="Y,Yacute,Ydieresis"
	k="37" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn"
	g2="f,germandbls,fi,fl"
	k="4" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn"
	g2="T"
	k="52" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn"
	g2="V"
	k="16" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn"
	g2="W"
	k="7" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn"
	g2="X"
	k="6" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn"
	g2="x"
	k="12" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn"
	g2="one"
	k="20" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn"
	g2="seven"
	k="24" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn"
	g2="parenright"
	k="33" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn"
	g2="asterisk"
	k="9" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn"
	g2="question"
	k="23" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn"
	g2="backslash"
	k="10" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn"
	g2="bracketright"
	k="41" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn"
	g2="braceright"
	k="37" />
    <hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,oslash,thorn"
	g2="trademark"
	k="35" />
    <hkern g1="r"
	g2="hyphen,endash,emdash"
	k="40" />
    <hkern g1="r"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="39" />
    <hkern g1="r"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="30" />
    <hkern g1="r"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="5" />
    <hkern g1="r"
	g2="guillemotleft,guilsinglleft"
	k="24" />
    <hkern g1="r"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe"
	k="5" />
    <hkern g1="s,scaron"
	g2="x"
	k="6" />
    <hkern g1="s,scaron"
	g2="parenright"
	k="32" />
    <hkern g1="s,scaron"
	g2="question"
	k="17" />
    <hkern g1="s,scaron"
	g2="bracketright"
	k="39" />
    <hkern g1="s,scaron"
	g2="braceright"
	k="35" />
    <hkern g1="s,scaron"
	g2="trademark"
	k="32" />
    <hkern g1="t"
	g2="hyphen,endash,emdash"
	k="31" />
    <hkern g1="t"
	g2="guillemotleft,guilsinglleft"
	k="18" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis"
	g2="Y,Yacute,Ydieresis"
	k="34" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis"
	g2="T"
	k="51" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis"
	g2="V"
	k="11" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis"
	g2="W"
	k="5" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis"
	g2="one"
	k="19" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis"
	g2="parenright"
	k="30" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis"
	g2="bracketright"
	k="32" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis"
	g2="braceright"
	k="31" />
    <hkern g1="q,u,ugrave,uacute,ucircumflex,udieresis"
	g2="trademark"
	k="31" />
    <hkern g1="v"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="20" />
    <hkern g1="v"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="6" />
    <hkern g1="w"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="17" />
    <hkern g1="w"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="5" />
    <hkern g1="x"
	g2="hyphen,endash,emdash"
	k="24" />
    <hkern g1="x"
	g2="a,agrave,aacute,acircumflex,atilde,adieresis,aring,ae"
	k="9" />
    <hkern g1="x"
	g2="guillemotleft,guilsinglleft"
	k="15" />
    <hkern g1="x"
	g2="c,d,e,o,q,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe"
	k="12" />
    <hkern g1="x"
	g2="s,scaron"
	k="5" />
    <hkern g1="y,yacute,ydieresis"
	g2="comma,period,quotesinglbase,quotedblbase,ellipsis"
	k="20" />
    <hkern g1="y,yacute,ydieresis"
	g2="slash"
	k="12" />
    <hkern g1="y,yacute,ydieresis"
	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring"
	k="6" />
    <hkern g1="y,yacute,ydieresis"
	g2="parenright"
	k="28" />
    <hkern g1="y,yacute,ydieresis"
	g2="bracketright"
	k="32" />
    <hkern g1="y,yacute,ydieresis"
	g2="braceright"
	k="30" />
    <hkern g1="y,yacute,ydieresis"
	g2="trademark"
	k="22" />
    <hkern g1="z,zcaron"
	g2="hyphen,endash,emdash"
	k="23" />
    <hkern g1="z,zcaron"
	g2="guillemotleft,guilsinglleft"
	k="16" />
    <hkern g1="z,zcaron"
	g2="parenright"
	k="23" />
    <hkern g1="z,zcaron"
	g2="bracketright"
	k="24" />
    <hkern g1="z,zcaron"
	g2="braceright"
	k="23" />
    <hkern g1="z,zcaron"
	g2="trademark"
	k="25" />
  </font>
</defs></svg>
