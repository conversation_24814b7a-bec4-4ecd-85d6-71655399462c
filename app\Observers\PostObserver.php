<?php

namespace App\Observers;

use App\Helpers\EmailListHelper;
use App\Jobs\OptimizeVideo;
use App\Post;

class PostObserver
{
    /**
     * Listen to the Post creating event.
     *
     * @param  Post  $post
     * @return void
     */
    public function creating(Post $post)
    {
        if(!$post->post_hash)
            // set post_hash of this post
            $post->post_hash = sha1($post->user_id . $post->content . $post->created_at);
    }

    /**
     * <PERSON><PERSON> created
     * @param Post $post
     */
    public function created(Post $post){
        if($post->type === 'video'){
            // start converting the video attachments if any
            if(app()->environment('production')) {
                dispatch((new OptimizeVideo($post))->onQueue('default_long')->onConnection('redis_long'));
            } else {
                dispatch((new OptimizeVideo($post)));
            }
        }

        // send event
        try {
            EmailListHelper::getInstance()->sendEvent($post->user, 'post_created', $post->account->name . ' (' . $post->account->type . ')');
            trigger_team_activity($post->getTeam(), $post->user);
        } catch (\Exception $exception){
            report($exception);
        }

        // if postback_url is set, send a postback
        Post::postbackIfNeeded($post, 'created');
    }

    public function updated(Post $post){

    }

    /**
     * Listen to the Post deleting event.
     *
     * @param Post $post
     * @return bool
     */
    public function deleting(Post $post)
    {
        // see if user can delete
        $currUser = user();
        if($currUser){
            if(!$post->canBeEditedBy($currUser)){
                // current user cannot delete
                return false;
            }
        }

        // delete attachment if post is normally added (i.e. not from queue)
        $options = $post->options;
        if(!empty($options['attachments']) && $post->source !== 'queue'){
            // attachments cleanup
            $same_attachment_posts = Post::where('post_hash', $post->post_hash)->where('external_id', null)->where('id', '<>', $post->id)->count();
            if($same_attachment_posts == 0){
                // no pending posts with same attachments found; delete the attachments
                foreach($options['attachments'] as $attachment){
                    if(\Storage::exists($attachment['path']))
                        \Storage::delete($attachment['path']);
                }
                //unset($options['attachments']); # keep record
            }
        }

        // delete attachments from options if needed
        if(isset($options['threaded_replies'])){
            foreach($options['threaded_replies'] as $reply){
                if(isset($reply['media'])){
                    foreach($reply['media'] as $media){
                        if(\Storage::cloud()->exists($media['key']))
                            \Storage::cloud()->delete($media['key']);
                    }
                }
            }
        }

        // options that are file uploads
        $fUploads = ['link_thumbnail', 'thumbnail'];
        foreach($fUploads as $key){
            if(isset($options[$key])){
                if(\Storage::cloud()->exists($options[$key]['key'])){
                    \Storage::cloud()->delete($options[$key]['key']);
                }
            }
        }

        // delete all hashtags
        get_insights_db()->table('post_hashtags')->where('post_id', $post->id)->delete();
        // delete post metrics
        get_insights_db()->table('post_metrics')->where('post_id', $post->id)->delete();
        // delete misc metrics about this post
        get_insights_db()->table('misc_metrics')->where('object', 'post:' . $post->id)->delete();

        return true;
    }

    public function deleted(Post $post){
        // send event
        try {
            EmailListHelper::getInstance()->sendEvent($post->user, 'post_deleted', $post->account->name . ' (' . $post->account->type . ')');
        } catch (\Exception $exception){
            report($exception);
        }
    }
}
