<?php

namespace App\Jobs;

use App\Post;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class OptimizeVideo implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $post;

    /**
     * Delete the job if its models no longer exist.
     *
     * @var bool
     */
    public $deleteWhenMissingModels = true;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 1200;

    /**
     * Create a new job instance.
     *
     * @param Post $post
     */
    public function __construct(Post $post)
    {
        $this->post = $post;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Exception
     */
    public function handle()
    {

        if($this->post->getOption('preparation_started_at')) return; // already started

        $this->post->setOption('preparation_started_at', now());

        $e = null;
        try {
            $this->post->prepareVideo(true);
        } catch (\Exception $exception){
            $e = $exception;
        }

        // this is very important
        // because it doesn't auto refresh
        // and, we get old options data
        $this->post->refresh();

        // mark it by removing the timestamp, so it can be further processed
        $this->post->removeOption('preparation_started_at');

        if($e) throw $e; // fail the job if needed

    }
}
