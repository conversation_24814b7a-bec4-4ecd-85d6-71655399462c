<?php return array (
  'activitylog' => 
  array (
    'enabled' => true,
    'delete_records_older_than_days' => 365,
    'default_log_name' => 'default',
    'default_auth_driver' => NULL,
    'subject_returns_soft_deleted_models' => false,
    'activity_model' => 'Spatie\\Activitylog\\Models\\Activity',
    'table_name' => 'activity_log',
    'database_connection' => NULL,
  ),
  'app' => 
  array (
    'name' => 'SocialBu',
    'env' => 'local',
    'debug' => true,
    'url' => 'http://127.0.0.1:8000',
    'timezone' => 'UTC',
    'locale' => 'en',
    'fallback_locale' => 'en',
    'key' => 'base64:8Xx1V8W+iowYImiJMSi53FIw7HNseL8immCMv8MJlFU=',
    'cipher' => 'AES-256-CBC',
    'providers' => 
    array (
      0 => 'Illuminate\\Auth\\AuthServiceProvider',
      1 => 'Illuminate\\Broadcasting\\BroadcastServiceProvider',
      2 => 'Illuminate\\Bus\\BusServiceProvider',
      3 => 'Illuminate\\Cache\\CacheServiceProvider',
      4 => 'Illuminate\\Foundation\\Providers\\ConsoleSupportServiceProvider',
      5 => 'Illuminate\\Cookie\\CookieServiceProvider',
      6 => 'Illuminate\\Database\\DatabaseServiceProvider',
      7 => 'Illuminate\\Encryption\\EncryptionServiceProvider',
      8 => 'Illuminate\\Filesystem\\FilesystemServiceProvider',
      9 => 'Illuminate\\Foundation\\Providers\\FoundationServiceProvider',
      10 => 'Illuminate\\Hashing\\HashServiceProvider',
      11 => 'Illuminate\\Mail\\MailServiceProvider',
      12 => 'Illuminate\\Notifications\\NotificationServiceProvider',
      13 => 'Illuminate\\Pagination\\PaginationServiceProvider',
      14 => 'Illuminate\\Pipeline\\PipelineServiceProvider',
      15 => 'Illuminate\\Queue\\QueueServiceProvider',
      16 => 'Illuminate\\Redis\\RedisServiceProvider',
      17 => 'Illuminate\\Auth\\Passwords\\PasswordResetServiceProvider',
      18 => 'Illuminate\\Session\\SessionServiceProvider',
      19 => 'Illuminate\\Translation\\TranslationServiceProvider',
      20 => 'Illuminate\\Validation\\ValidationServiceProvider',
      21 => 'Illuminate\\View\\ViewServiceProvider',
      22 => 'Laravel\\Tinker\\TinkerServiceProvider',
      23 => 'SocialiteProviders\\Manager\\ServiceProvider',
      24 => 'Mariuzzo\\LaravelJsLocalization\\LaravelJsLocalizationServiceProvider',
      25 => 'Camroncade\\Timezone\\TimezoneServiceProvider',
      26 => 'Laravel\\Cashier\\CashierServiceProvider',
      27 => 'Calebporzio\\Onboard\\OnboardServiceProvider',
      28 => 'App\\Providers\\AppServiceProvider',
      29 => 'App\\Providers\\AuthServiceProvider',
      30 => 'App\\Providers\\EventServiceProvider',
      31 => 'App\\Providers\\HorizonServiceProvider',
      32 => 'App\\Providers\\RouteServiceProvider',
    ),
    'aliases' => 
    array (
      'App' => 'Illuminate\\Support\\Facades\\App',
      'Artisan' => 'Illuminate\\Support\\Facades\\Artisan',
      'Auth' => 'Illuminate\\Support\\Facades\\Auth',
      'Blade' => 'Illuminate\\Support\\Facades\\Blade',
      'Bus' => 'Illuminate\\Support\\Facades\\Bus',
      'Cache' => 'Illuminate\\Support\\Facades\\Cache',
      'Config' => 'Illuminate\\Support\\Facades\\Config',
      'Cookie' => 'Illuminate\\Support\\Facades\\Cookie',
      'Crypt' => 'Illuminate\\Support\\Facades\\Crypt',
      'DB' => 'Illuminate\\Support\\Facades\\DB',
      'Eloquent' => 'Illuminate\\Database\\Eloquent\\Model',
      'Event' => 'Illuminate\\Support\\Facades\\Event',
      'File' => 'Illuminate\\Support\\Facades\\File',
      'Gate' => 'Illuminate\\Support\\Facades\\Gate',
      'Hash' => 'Illuminate\\Support\\Facades\\Hash',
      'Lang' => 'Illuminate\\Support\\Facades\\Lang',
      'Log' => 'Illuminate\\Support\\Facades\\Log',
      'Mail' => 'Illuminate\\Support\\Facades\\Mail',
      'Notification' => 'Illuminate\\Support\\Facades\\Notification',
      'Password' => 'Illuminate\\Support\\Facades\\Password',
      'Queue' => 'Illuminate\\Support\\Facades\\Queue',
      'Redirect' => 'Illuminate\\Support\\Facades\\Redirect',
      'Redis' => 'Illuminate\\Support\\Facades\\Redis',
      'Request' => 'Illuminate\\Support\\Facades\\Request',
      'Response' => 'Illuminate\\Support\\Facades\\Response',
      'Route' => 'Illuminate\\Support\\Facades\\Route',
      'Schema' => 'Illuminate\\Support\\Facades\\Schema',
      'Session' => 'Illuminate\\Support\\Facades\\Session',
      'Storage' => 'Illuminate\\Support\\Facades\\Storage',
      'URL' => 'Illuminate\\Support\\Facades\\URL',
      'Validator' => 'Illuminate\\Support\\Facades\\Validator',
      'View' => 'Illuminate\\Support\\Facades\\View',
      'Socialite' => 'Laravel\\Socialite\\Facades\\Socialite',
      'Onboard' => 'Calebporzio\\Onboard\\OnboardFacade',
    ),
    'max_file_size' => '**********',
    'proxies' => 
    array (
      0 => 'http://10.132.14.186:3128',
      1 => 'http://10.132.75.142:3128',
      2 => 'http://10.132.170.249:3128',
    ),
    'chat_update' => 
    array (
      'url' => 
      array (
        'management' => 'https://3.basecamp.com/4644022/integrations/bGdZCfTt1K7vFHXYzLMvJoLC/buckets/35550260/chats/6851833615/lines',
        'sales' => 'https://3.basecamp.com/4644022/integrations/bGdZCfTt1K7vFHXYzLMvJoLC/buckets/17680134/chats/**********/lines',
        'livechat_monitor' => 'https://3.basecamp.com/4644022/integrations/bGdZCfTt1K7vFHXYzLMvJoLC/buckets/17680134/chats/**********/lines',
        'customer_success' => 'https://3.basecamp.com/4644022/integrations/bGdZCfTt1K7vFHXYzLMvJoLC/buckets/17680134/chats/**********/lines',
        'product_marketing' => 'https://3.basecamp.com/4644022/integrations/bGdZCfTt1K7vFHXYzLMvJoLC/buckets/40390653/chats/**********/lines',
      ),
      'param' => 'content',
    ),
    'upvoty_private_key' => '',
  ),
  'auth' => 
  array (
    'defaults' => 
    array (
      'guard' => 'web',
      'passwords' => 'users',
    ),
    'guards' => 
    array (
      'web' => 
      array (
        'driver' => 'session',
        'provider' => 'users',
      ),
      'api' => 
      array (
        'driver' => 'passport',
        'provider' => 'users',
      ),
    ),
    'providers' => 
    array (
      'users' => 
      array (
        'driver' => 'eloquent',
        'model' => 'App\\User',
      ),
    ),
    'passwords' => 
    array (
      'users' => 
      array (
        'provider' => 'users',
        'table' => 'password_resets',
        'expire' => 60,
        'throttle' => 60,
      ),
    ),
  ),
  'broadcasting' => 
  array (
    'default' => 'log',
    'connections' => 
    array (
      'echo-server' => 
      array (
        'driver' => 'pusher',
        'key' => '',
        'secret' => NULL,
        'app_id' => '',
        'options' => 
        array (
          'host' => 'localhost',
          'port' => 6001,
          'scheme' => 'http',
        ),
      ),
      'pusher' => 
      array (
        'driver' => 'pusher',
        'key' => '',
        'secret' => NULL,
        'app_id' => '',
        'options' => 
        array (
        ),
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
      ),
      'log' => 
      array (
        'driver' => 'log',
      ),
      'null' => 
      array (
        'driver' => 'null',
      ),
    ),
  ),
  'cache' => 
  array (
    'default' => 'redis',
    'stores' => 
    array (
      'apc' => 
      array (
        'driver' => 'apc',
      ),
      'array' => 
      array (
        'driver' => 'array',
      ),
      'database' => 
      array (
        'driver' => 'database',
        'table' => 'cache',
        'connection' => NULL,
      ),
      'file' => 
      array (
        'driver' => 'file',
        'path' => 'C:\\xampp\\htdocs\\socialtool\\storage\\framework/cache',
      ),
      'memcached' => 
      array (
        'driver' => 'memcached',
        'persistent_id' => NULL,
        'sasl' => 
        array (
          0 => NULL,
          1 => NULL,
        ),
        'options' => 
        array (
        ),
        'servers' => 
        array (
          0 => 
          array (
            'host' => '127.0.0.1',
            'port' => 11211,
            'weight' => 100,
          ),
        ),
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
      ),
    ),
    'prefix' => 'laravel',
  ),
  'cashier' => 
  array (
    'key' => '',
    'secret' => '',
    'path' => 'stripe',
    'webhook' => 
    array (
      'secret' => NULL,
      'tolerance' => 300,
    ),
    'model' => 'App\\User',
    'currency' => 'usd',
    'currency_locale' => 'en',
    'payment_notification' => NULL,
    'paper' => 'letter',
    'logger' => NULL,
  ),
  'charts' => 
  array (
    'default_library' => 'Chartjs',
  ),
  'compile' => 
  array (
    'files' => 
    array (
    ),
    'providers' => 
    array (
    ),
  ),
  'database' => 
  array (
    'fetch' => 5,
    'default' => 'mysql',
    'insights' => 'mysql',
    'connections' => 
    array (
      'sqlite_testing' => 
      array (
        'driver' => 'sqlite',
        'database' => 'C:\\xampp\\htdocs\\socialtool\\database\\test_database.sqlite',
        'prefix' => '',
      ),
      'sqlite' => 
      array (
        'driver' => 'sqlite',
        'database' => 'socialtool',
        'prefix' => '',
      ),
      'mysql' => 
      array (
        'driver' => 'mysql',
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'socialtool',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
        'strict' => true,
        'engine' => 'InnoDB',
        'timezone' => '+00:00',
      ),
      'mysql_insights' => 
      array (
        'driver' => 'mysql',
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'socialtool_insights',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
        'strict' => true,
        'engine' => 'InnoDB',
        'timezone' => '+00:00',
      ),
      'pgsql' => 
      array (
        'driver' => 'pgsql',
        'host' => '127.0.0.1',
        'port' => '3306',
        'database' => 'socialtool',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8',
        'prefix' => '',
        'schema' => 'public',
        'sslmode' => 'prefer',
      ),
    ),
    'migrations' => 'migrations',
    'redis' => 
    array (
      'driver' => 'redis',
      'client' => 'predis',
      'connection' => 'default',
      'queue' => 'default',
      'retry_after' => 90,
      'block_for' => 5,
      'cluster' => false,
      'default' => 
      array (
        'host' => '127.0.0.1',
        'password' => '',
        'port' => '6379',
        'database' => 0,
        'read_write_timeout' => 60,
      ),
      'horizon' => 
      array (
        'host' => '127.0.0.1',
        'password' => '',
        'port' => '6379',
        'database' => 0,
        'read_write_timeout' => 60,
        'options' => 
        array (
          'prefix' => 'horizon:',
        ),
      ),
    ),
  ),
  'excel' => 
  array (
    'exports' => 
    array (
      'chunk_size' => 1000,
      'pre_calculate_formulas' => false,
      'csv' => 
      array (
        'delimiter' => ',',
        'enclosure' => '"',
        'line_ending' => '
',
        'use_bom' => false,
        'include_separator_line' => false,
        'excel_compatibility' => false,
      ),
    ),
    'imports' => 
    array (
      'read_only' => true,
      'heading_row' => 
      array (
        'formatter' => 'slug',
      ),
      'csv' => 
      array (
        'delimiter' => ',',
        'enclosure' => '"',
        'escape_character' => '\\',
        'contiguous' => false,
        'input_encoding' => 'UTF-8',
      ),
    ),
    'extension_detector' => 
    array (
      'xlsx' => 'Xlsx',
      'xlsm' => 'Xlsx',
      'xltx' => 'Xlsx',
      'xltm' => 'Xlsx',
      'xls' => 'Xls',
      'xlt' => 'Xls',
      'ods' => 'Ods',
      'ots' => 'Ods',
      'slk' => 'Slk',
      'xml' => 'Xml',
      'gnumeric' => 'Gnumeric',
      'htm' => 'Html',
      'html' => 'Html',
      'csv' => 'Csv',
      'tsv' => 'Csv',
      'pdf' => 'Dompdf',
    ),
    'value_binder' => 
    array (
      'default' => 'Maatwebsite\\Excel\\DefaultValueBinder',
    ),
    'cache' => 
    array (
      'driver' => 'memory',
      'batch' => 
      array (
        'memory_limit' => 60000,
      ),
      'illuminate' => 
      array (
        'store' => NULL,
      ),
    ),
    'transactions' => 
    array (
      'handler' => 'db',
    ),
    'temporary_files' => 
    array (
      'local_path' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp',
      'remote_disk' => NULL,
    ),
  ),
  'filesystems' => 
  array (
    'default' => 'public',
    'cloud' => 'spaces',
    'disks' => 
    array (
      'local' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\xampp\\htdocs\\socialtool\\storage\\app',
      ),
      'public' => 
      array (
        'driver' => 'local',
        'root' => 'C:\\xampp\\htdocs\\socialtool\\storage\\app/public',
        'url' => 'http://127.0.0.1:8000/storage',
        'visibility' => 'public',
      ),
      's3' => 
      array (
        'driver' => 's3',
        'key' => 'your-key',
        'secret' => 'your-secret',
        'region' => 'your-region',
        'bucket' => 'your-bucket',
      ),
      'spaces' => 
      array (
        'driver' => 's3',
        'key' => '4B3SKIWGWG44HGIRKHHD',
        'secret' => '4UQ5n3tyz8bXFiAWYBuCmA+iJih7o/IByvVur6V9/QY',
        'endpoint' => 'https://nyc3.digitaloceanspaces.com',
        'region' => 'nyc3',
        'bucket' => 'socialbu-content',
      ),
    ),
  ),
  'hashing' => 
  array (
    'driver' => 'bcrypt',
  ),
  'horizon' => 
  array (
    'domain' => NULL,
    'path' => 'sb_horizon',
    'use' => 'default',
    'prefix' => 'horizon:',
    'middleware' => 
    array (
      0 => 'web',
    ),
    'waits' => 
    array (
      'redis:default' => 300,
    ),
    'trim' => 
    array (
      'recent' => 60,
      'completed' => 60,
      'recent_failed' => 10080,
      'failed' => 10080,
      'monitored' => 10080,
    ),
    'fast_termination' => false,
    'memory_limit' => 1024,
    'environments' => 
    array (
      'production' => 
      array (
        'supervisor-1' => 
        array (
          'connection' => 'redis',
          'queue' => 
          array (
            0 => 'default',
          ),
          'balance' => 'auto',
          'minProcesses' => 4,
          'maxProcesses' => 16,
          'tries' => 3,
          'timeout' => 120,
        ),
        'supervisor-2' => 
        array (
          'connection' => 'redis',
          'queue' => 
          array (
            0 => 'posts',
          ),
          'balance' => 'auto',
          'minProcesses' => 40,
          'maxProcesses' => 400,
          'tries' => 3,
          'timeout' => 900,
        ),
        'supervisor-3' => 
        array (
          'connection' => 'redis',
          'queue' => 
          array (
            0 => 'automations',
          ),
          'balance' => 'auto',
          'minProcesses' => 1,
          'maxProcesses' => 20,
          'tries' => 2,
          'timeout' => 120,
        ),
        'supervisor-4' => 
        array (
          'connection' => 'redis',
          'queue' => 
          array (
            0 => 'fb_webhook',
          ),
          'balance' => 'auto',
          'minProcesses' => 1,
          'maxProcesses' => 8,
          'tries' => 3,
          'timeout' => 60,
        ),
        'supervisor-5' => 
        array (
          'connection' => 'redis_long',
          'queue' => 
          array (
            0 => 'default_long',
          ),
          'balance' => 'auto',
          'minProcesses' => 1,
          'maxProcesses' => 30,
          'tries' => 3,
          'timeout' => 1200,
        ),
        'supervisor-7' => 
        array (
          'connection' => 'redis',
          'queue' => 
          array (
            0 => 'inbox',
          ),
          'balance' => 'auto',
          'minProcesses' => 1,
          'maxProcesses' => 30,
          'tries' => 3,
          'timeout' => 900,
        ),
      ),
      'local' => 
      array (
        'supervisor-1' => 
        array (
          'connection' => 'redis',
          'queue' => 
          array (
            0 => 'default',
            1 => 'posts',
            2 => 'automations',
            3 => 'fb_webhook',
          ),
          'balance' => 'simple',
          'processes' => 3,
          'tries' => 1,
        ),
        'supervisor-2' => 
        array (
          'connection' => 'redis',
          'queue' => 
          array (
            0 => 'default_long',
          ),
          'balance' => 'simple',
          'processes' => 3,
          'tries' => 1,
          'timeout' => 900,
        ),
      ),
    ),
  ),
  'ide-helper' => 
  array (
    'filename' => '_ide_helper',
    'format' => 'php',
    'meta_filename' => '.phpstorm.meta.php',
    'include_fluent' => false,
    'include_factory_builders' => false,
    'write_model_magic_where' => true,
    'write_model_relation_count_properties' => true,
    'write_eloquent_model_mixins' => false,
    'include_helpers' => false,
    'helper_files' => 
    array (
      0 => 'C:\\xampp\\htdocs\\socialtool/vendor/laravel/framework/src/Illuminate/Support/helpers.php',
    ),
    'model_locations' => 
    array (
      0 => 'app',
    ),
    'ignored_models' => 
    array (
    ),
    'extra' => 
    array (
      'Eloquent' => 
      array (
        0 => 'Illuminate\\Database\\Eloquent\\Builder',
        1 => 'Illuminate\\Database\\Query\\Builder',
      ),
      'Session' => 
      array (
        0 => 'Illuminate\\Session\\Store',
      ),
    ),
    'magic' => 
    array (
    ),
    'interfaces' => 
    array (
    ),
    'custom_db_types' => 
    array (
    ),
    'model_camel_case_properties' => false,
    'type_overrides' => 
    array (
      'integer' => 'int',
      'boolean' => 'bool',
    ),
    'include_class_docblocks' => false,
  ),
  'localization-js' => 
  array (
    'messages' => 
    array (
    ),
    'path' => 'C:\\xampp\\htdocs\\socialtool\\resources\\assets/js/messages.js',
  ),
  'logging' => 
  array (
    'default' => 'daily',
    'channels' => 
    array (
      'stack' => 
      array (
        'driver' => 'stack',
        'channels' => 
        array (
          0 => 'daily',
          1 => 'papertrail',
        ),
      ),
      'papertrail' => 
      array (
        'driver' => 'monolog',
        'level' => 'debug',
        'handler' => 'Monolog\\Handler\\SyslogUdpHandler',
        'handler_with' => 
        array (
          'host' => '',
          'port' => '',
        ),
      ),
      'single' => 
      array (
        'driver' => 'single',
        'path' => 'C:\\xampp\\htdocs\\socialtool\\storage\\logs/laravel.log',
        'level' => 'debug',
      ),
      'daily' => 
      array (
        'driver' => 'daily',
        'path' => 'C:\\xampp\\htdocs\\socialtool\\storage\\logs/laravel.log',
        'level' => 'debug',
        'days' => 7,
      ),
      'slack' => 
      array (
        'driver' => 'slack',
        'url' => NULL,
        'username' => 'Laravel Log',
        'emoji' => ':boom:',
        'level' => 'critical',
      ),
      'syslog' => 
      array (
        'driver' => 'syslog',
        'level' => 'debug',
      ),
      'errorlog' => 
      array (
        'driver' => 'errorlog',
        'level' => 'debug',
      ),
    ),
  ),
  'mail' => 
  array (
    'driver' => 'smtp',
    'host' => 'sandbox.smtp.mailtrap.io',
    'port' => '2525',
    'from' => 
    array (
      'address' => '<EMAIL>',
      'name' => 'SocialBu',
    ),
    'encryption' => 'tls',
    'username' => '90af88b35da396',
    'password' => '81cbaef21a5bd4',
    'sendmail' => '/usr/sbin/sendmail -bs',
  ),
  'plans' => 
  array (
    'free' => 
    array (
      'name' => 'Free',
      'price' => 0,
      'price_yearly' => 0,
      'limits' => 
      array (
        'monthly_posts' => 40,
        'teams' => 0,
        'team_members' => 0,
        'accounts' => 2,
        'feeds' => 0,
        'queues' => 0,
        'automations' => 0,
        'media_storage' => 0,
      ),
    ),
    'starter' => 
    array (
      'name' => 'Starter',
      'price' => 8,
      'price_yearly' => 80,
      'limits' => 
      array (
        'monthly_posts' => 150,
        'teams' => 1,
        'team_members' => 2,
        'accounts' => 4,
        'feeds' => 1,
        'queues' => 2,
        'automations' => 4,
        'media_storage' => **********,
      ),
      'features' => 
      array (
      ),
    ),
    'standard2' => 
    array (
      'name' => 'Standard',
      'price' => 19,
      'price_yearly' => 190,
      'limits' => 
      array (
        'monthly_posts' => 800,
        'teams' => 2,
        'team_members' => 4,
        'accounts' => 12,
        'feeds' => 5,
        'queues' => 5,
        'automations' => 16,
        'media_storage' => **********,
        'generated_content' => 100,
      ),
      'features' => 
      array (
        'content_generation' => true,
        'reels_publishing' => true,
      ),
    ),
    'super' => 
    array (
      'name' => 'Super',
      'price' => 59,
      'price_yearly' => 590,
      'limits' => 
      array (
        'monthly_posts' => 50000,
        'teams' => 6,
        'team_members' => 8,
        'accounts' => 30,
        'feeds' => 15,
        'queues' => 15,
        'automations' => 60,
        'media_storage' => **********,
        'generated_content' => 250,
      ),
      'features' => 
      array (
        'content_generation' => true,
        'content_autocomplete' => true,
        'reels_publishing' => true,
        'curation_generate' => true,
      ),
    ),
    'supreme' => 
    array (
      'name' => 'Supreme',
      'price' => 199,
      'price_yearly' => 1990,
      'limits' => 
      array (
        'monthly_posts' => 50000,
        'teams' => 25,
        'team_members' => 20,
        'accounts' => 150,
        'feeds' => 50,
        'queues' => 50,
        'automations' => 400,
        'media_storage' => ***********,
        'generated_content' => 600,
      ),
      'features' => 
      array (
        'content_generation' => true,
        'content_autocomplete' => true,
        'reels_publishing' => true,
        'curation_generate' => true,
      ),
    ),
    'custom' => 
    array (
      'name' => 'Custom',
      'price' => 0,
      'price_yearly' => 0,
      'limits' => 
      array (
        'monthly_posts' => 0,
        'teams' => 0,
        'team_members' => 0,
        'accounts' => 0,
        'feeds' => 0,
        'queues' => 0,
        'automations' => 0,
        'media_storage' => 0,
        'generated_content' => 0,
      ),
      'features' => 
      array (
        'content_generation' => false,
        'content_autocomplete' => false,
        'reels_publishing' => false,
      ),
    ),
    'basic' => 
    array (
      'name' => 'Basic',
      'price' => 4.99,
      'price_yearly' => 49.***************,
      'limits' => 
      array (
        'monthly_posts' => 150,
        'teams' => 1,
        'team_members' => 3,
        'accounts' => 4,
        'feeds' => 1,
        'queues' => 0,
        'automations' => 4,
        'media_storage' => 0,
      ),
    ),
    'standard' => 
    array (
      'name' => 'Standard',
      'price' => 12,
      'price_yearly' => 120,
      'limits' => 
      array (
        'monthly_posts' => 800,
        'teams' => 3,
        'team_members' => 5,
        'accounts' => 10,
        'feeds' => 4,
        'queues' => 0,
        'automations' => 15,
        'media_storage' => 0,
      ),
      'features' => 
      array (
      ),
    ),
    'plus' => 
    array (
      'name' => 'Plus',
      'price' => 49,
      'price_yearly' => 490,
      'limits' => 
      array (
        'monthly_posts' => 50000,
        'teams' => 8,
        'team_members' => 20,
        'accounts' => 25,
        'feeds' => 10,
        'queues' => 0,
        'automations' => 40,
        'media_storage' => 0,
      ),
    ),
    'plus-ultra' => 
    array (
      'name' => 'Plus Ultra',
      'price' => 199,
      'price_yearly' => 1990,
      'limits' => 
      array (
        'monthly_posts' => 50000,
        'teams' => 20,
        'team_members' => 40,
        'accounts' => 150,
        'feeds' => 30,
        'queues' => 0,
        'automations' => 200,
        'media_storage' => 0,
      ),
    ),
  ),
  'promocodes' => 
  array (
    'table' => 'promocodes',
    'relation_table' => 'promocode_user',
    'characters' => '23456789ABCDEFGHJKLMNPQRSTUVWXYZ',
    'prefix' => 'BU',
    'suffix' => false,
    'mask' => '****-****',
    'separator' => '-',
    'user_model' => 'App\\User',
    'foreign_pivot_key' => 'promocode_id',
    'related_pivot_key' => 'user_id',
  ),
  'queue' => 
  array (
    'default' => 'redis',
    'connections' => 
    array (
      'sync' => 
      array (
        'driver' => 'sync',
      ),
      'database' => 
      array (
        'driver' => 'database',
        'table' => 'jobs',
        'queue' => 'default',
        'retry_after' => 90,
      ),
      'beanstalkd' => 
      array (
        'driver' => 'beanstalkd',
        'host' => 'localhost',
        'queue' => 'default',
        'retry_after' => 90,
      ),
      'sqs' => 
      array (
        'driver' => 'sqs',
        'key' => 'your-public-key',
        'secret' => 'your-secret-key',
        'prefix' => 'https://sqs.us-east-1.amazonaws.com/your-account-id',
        'queue' => 'your-queue-name',
        'region' => 'us-east-1',
      ),
      'redis' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
        'queue' => 'default',
        'retry_after' => 210,
      ),
      'redis_long' => 
      array (
        'driver' => 'redis',
        'connection' => 'default',
        'queue' => 'default_long',
        'retry_after' => 1400,
      ),
    ),
    'failed' => 
    array (
      'database' => 'mysql',
      'table' => 'failed_jobs',
    ),
  ),
  'sentry' => 
  array (
    'dsn' => '',
    'release' => NULL,
    'environment' => NULL,
    'sample_rate' => 1.0,
    'traces_sample_rate' => 0.005,
    'profiles_sample_rate' => NULL,
    'send_default_pii' => true,
    'ignore_transactions' => 
    array (
      0 => '/up',
    ),
    'breadcrumbs' => 
    array (
      'logs' => true,
      'queue_info' => true,
      'sql_queries' => true,
      'sql_bindings' => true,
    ),
    'tracing' => 
    array (
      'queue_job_transactions' => true,
      'queue_jobs' => true,
      'sql_queries' => true,
      'sql_bindings' => false,
      'sql_origin' => true,
      'sql_origin_threshold_ms' => 100,
      'views' => true,
      'livewire' => true,
      'http_client_requests' => true,
      'cache' => true,
      'redis_commands' => false,
      'redis_origin' => true,
      'notifications' => true,
      'missing_routes' => false,
      'continue_after_response' => true,
      'default_integrations' => true,
    ),
  ),
  'services' => 
  array (
    'mailgun' => 
    array (
      'domain' => '',
      'secret' => '',
    ),
    'ses' => 
    array (
      'key' => NULL,
      'secret' => NULL,
      'region' => 'us-east-1',
    ),
    'sparkpost' => 
    array (
      'secret' => NULL,
    ),
    'stripe' => 
    array (
      'model' => 'App\\User',
      'key' => '',
      'secret' => '',
    ),
    'facebook' => 
    array (
      'client_id' => '308100123704882',
      'client_secret' => '2e99a0cd8f9dc1a058e723caaba16215',
      'redirect' => 'http://127.0.0.1:8000/auth/facebook/callback',
      'default_graph_version' => 'v22.0',
      'verification_token' => 'elohel',
      'required_scopes' => 
      array (
        0 => 'business_management',
        1 => 'pages_manage_ads',
        2 => 'pages_manage_metadata',
        3 => 'pages_manage_posts',
        4 => 'pages_messaging',
        5 => 'pages_read_engagement',
        6 => 'pages_manage_engagement',
        7 => 'pages_read_user_content',
        8 => 'read_insights',
        9 => 'instagram_basic',
        10 => 'instagram_content_publish',
        11 => 'instagram_manage_comments',
        12 => 'instagram_manage_insights',
      ),
      'page_perms' => 
      array (
        0 => 'CREATE_CONTENT',
        1 => 'MODERATE',
        2 => 'ANALYZE',
      ),
    ),
    'threads' => 
    array (
      'client_id' => '',
      'client_secret' => '',
      'redirect' => 'http://127.0.0.1:8000/auth/threads/callback',
      'required_scopes' => 
      array (
        0 => 'threads_basic',
        1 => 'threads_content_publish',
        2 => 'threads_manage_replies',
        3 => 'threads_read_replies',
        4 => 'threads_manage_insights',
      ),
    ),
    'linkedin' => 
    array (
      'client_id' => '7715lrq1o7i6xe',
      'client_secret' => 'qcNNMb99SSXlhBbf',
      'redirect' => 'http://127.0.0.1:8000/auth/linkedin/callback',
      'required_scopes' => 
      array (
        0 => 'r_basicprofile',
        1 => 'w_member_social',
        2 => 'r_organization_social',
        3 => 'w_organization_social',
        4 => 'rw_organization_admin',
        5 => 'w_member_social_feed',
        6 => 'r_organization_social_feed',
        7 => 'w_organization_social_feed',
      ),
    ),
    'twitter' => 
    array (
      'client_id' => 'cE9PXzFxZ2dSQUF6Z1J4dmt0SU46MTpjaQ',
      'client_secret' => 'pqe7qqy-MqI9xE2T5pEMJGOq0RcGC1gDCW-JmmeJt0OfWQ7a4k',
      'consumer_key' => NULL,
      'consumer_secret' => NULL,
      'redirect' => 'http://127.0.0.1:8000/auth/twitter/callback',
      'app_hosts' => 
      array (
        0 => NULL,
        1 => 'socialtool.glaxosoft.com',
      ),
      'required_scopes' => 
      array (
        0 => 'users.read',
        1 => 'tweet.read',
        2 => 'tweet.write',
        3 => 'media.write',
        4 => 'like.read',
        5 => 'like.write',
        6 => 'offline.access',
        7 => 'tweet.moderate.write',
        8 => 'follows.read',
        9 => 'follows.write',
        10 => 'mute.write',
        11 => 'block.read',
        12 => 'mute.read',
        13 => 'dm.read',
        14 => 'dm.write',
        15 => 'users.email',
      ),
    ),
    'google' => 
    array (
      'client_id' => NULL,
      'client_secret' => NULL,
      'redirect' => 'http://127.0.0.1:8000/auth/google/callback',
      'required_scopes' => 
      array (
        'gmb' => 
        array (
          0 => 'https://www.googleapis.com/auth/business.manage',
        ),
        'youtube' => 
        array (
          0 => 'https://www.googleapis.com/auth/youtube',
        ),
      ),
    ),
    'instagram' => 
    array (
      'authenticate_server' => 'http://127.0.0.1:3601/',
      'server' => 'http://127.0.0.1:3602/',
      'client_id' => '',
      'client_secret' => '',
      'default_graph_version' => 'v22.0',
      'redirect' => 'http://127.0.0.1:8000/auth/instagram/callback',
      'required_scopes' => 
      array (
        0 => 'instagram_business_basic',
        1 => 'instagram_business_content_publish',
        2 => 'instagram_business_manage_messages',
        3 => 'instagram_business_manage_comments',
      ),
    ),
    'bitly' => 
    array (
      'client_id' => '',
      'client_secret' => '',
      'redirect' => 'http://127.0.0.1:8000/app/link_shorteners/bitly/callback',
    ),
    'linkmngr' => 
    array (
      'client_id' => '',
      'client_secret' => '',
      'redirect' => 'http://127.0.0.1:8000/app/link_shorteners/linkmngr/callback',
    ),
    'mastodon' => 
    array (
      'domain' => '',
      'client_id' => '',
      'client_secret' => '',
      'redirect' => 'http://127.0.0.1:8000/auth/mastodon/callback',
      'required_scopes' => 
      array (
        0 => 'read',
        1 => 'write',
      ),
    ),
    'tiktok' => 
    array (
      'client_id' => '',
      'client_key' => '',
      'client_secret' => '',
      'redirect' => 'http://127.0.0.1:8000/auth/tiktok/callback',
      'required_scopes' => 
      array (
        0 => 'user.info.basic',
        1 => 'video.list',
        2 => 'video.upload',
        3 => 'video.publish',
      ),
    ),
    'pinterest' => 
    array (
      'client_id' => '',
      'client_secret' => '',
      'redirect' => 'http://127.0.0.1:8000auth/pinterest/callback',
      'required_scopes' => 
      array (
        0 => 'boards:read',
        1 => 'boards:read_secret',
        2 => 'boards:write',
        3 => 'boards:write_secret',
        4 => 'pins:read',
        5 => 'pins:read_secret',
        6 => 'pins:write',
        7 => 'pins:write_secret',
        8 => 'user_accounts:read',
      ),
    ),
    'reddit' => 
    array (
      'client_id' => '',
      'client_secret' => '',
      'redirect' => 'http://127.0.0.1:8000/auth/reddit/callback',
      'required_scopes' => 
      array (
        0 => 'identity',
        1 => 'read',
        2 => 'submit',
        3 => 'mysubreddits',
        4 => 'privatemessages',
        5 => 'modposts',
        6 => 'vote',
        7 => 'flair',
      ),
    ),
    'openai' => 
    array (
      'secret' => '',
    ),
    'huggingface' => 
    array (
      'secret' => '',
    ),
    'bing' => 
    array (
      'key' => '',
    ),
    'dream_studio' => 
    array (
      'key' => '',
    ),
    'forefront' => 
    array (
      'key' => '',
    ),
    'recaptcha' => 
    array (
      'key' => '6Lf2GJIqAAAAAEKRp_cUPOGviMeiBjVKjidnvWxc',
      'secret' => '6Lf2GJIqAAAAAMoSja62LHJnoECPq05dkkumGDRw',
    ),
    'rapidapi' => 
    array (
      'key' => '',
    ),
  ),
  'session' => 
  array (
    'driver' => 'redis',
    'lifetime' => 10080,
    'expire_on_close' => false,
    'encrypt' => false,
    'files' => 'C:\\xampp\\htdocs\\socialtool\\storage\\framework/sessions',
    'connection' => NULL,
    'table' => 'sessions',
    'store' => NULL,
    'lottery' => 
    array (
      0 => 2,
      1 => 100,
    ),
    'cookie' => 'user_session',
    'path' => '/',
    'domain' => NULL,
    'secure' => true,
    'same_site' => 'none',
    'http_only' => true,
  ),
  'view' => 
  array (
    'paths' => 
    array (
      0 => 'C:\\xampp\\htdocs\\socialtool\\resources\\views',
    ),
    'compiled' => 'C:\\xampp\\htdocs\\socialtool\\storage\\framework\\views',
  ),
  'image' => 
  array (
    'driver' => 'gd',
  ),
  'passport' => 
  array (
    'private_key' => NULL,
    'public_key' => NULL,
    'client_uuids' => false,
    'personal_access_client' => 
    array (
      'id' => '1',
      'secret' => 'YTYGSC8IsOBwCT3AYitZ9EWWyJDvirC9TWYlzmUO',
    ),
    'storage' => 
    array (
      'database' => 
      array (
        'connection' => 'mysql',
      ),
    ),
  ),
  'sitemap' => 
  array (
    'guzzle_options' => 
    array (
      'cookies' => true,
      'connect_timeout' => 10,
      'timeout' => 10,
      'allow_redirects' => false,
    ),
    'execute_javascript' => false,
    'chrome_binary_path' => NULL,
    'crawl_profile' => 'Spatie\\Sitemap\\Crawler\\Profile',
  ),
  'trustedproxy' => 
  array (
    'proxies' => NULL,
    'headers' => 30,
  ),
  'tinker' => 
  array (
    'commands' => 
    array (
    ),
    'dont_alias' => 
    array (
      0 => 'App\\Nova',
    ),
  ),
);
