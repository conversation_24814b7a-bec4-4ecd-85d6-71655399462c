<?php

namespace App\Http\Controllers\Api;
use App\Account;
use Carbon\Carbon;
use App\Team;
use App\Post;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class AnalyzeController extends Controller
{
    /**
     * @throws ValidationException
     */
    private function checkAccess(Request $request){
        $team = $this->getTeamFromRequest($request);
        $user = user();
        if($team){
            // check if user has access to view insights
            if(!$team->hasPermission(user(), 'analyze.view')){
                return response()->json([
                    'success' => false,
                    'error_code' => 'access_needed',
                ]);
            }
            $user = $team->user;
        }

        // check if user plan allows insights
        $planId = $user->getPlan(true);
        $referer = $request->header('referer');

        $reportType = 'content';
        if(Str::contains($referer, 'analyze/account')){
            $reportType = 'account';
        } else if(Str::contains($referer, 'analyze/network')){
            $reportType = 'network';
        } else if(Str::contains($referer, 'analyze/team')){
            $reportType = 'team';
        }

        $planAccessByType = [
            'content' => ['starter', 'standard2', 'super', 'supreme', 'custom',],
            'account' => ['standard2', 'super', 'supreme', 'custom',],
            'network' => ['super', 'supreme', 'custom',],
            'team' => ['super', 'supreme', 'custom',],
        ];

        $accessPlans = array_merge($planAccessByType[$reportType], $user->getOption('custom_reports.' . $reportType, []));

        if(!in_array($planId, $accessPlans)){
            // redirect to settings
            return response()->json([
                'success' => false,
                'error_code' => 'upgrade_needed',
            ]);
        }
        return null;
    }

    /**
     * @throws ValidationException
     */
    private function validateDates(Request $request, $required = false){
        $this->validate($request, [
            'start' => ($required ? 'required|' : '') . 'date_format:Y-m-d',
            'end' => ($required ? 'required|' : '') . 'date_format:Y-m-d',
        ]);
        $start = Carbon::createFromFormat('Y-m-d', $request->input('start'));
        $end = Carbon::createFromFormat('Y-m-d', $request->input('end'));

        if($end < $start){
            throw ValidationException::withMessages([
                'end' => 'End date must be greater than start date',
            ]);
        }
    }

    /**
     * @throws ValidationException
     */
    private function validateAccounts(Request $request, $required = false){

        if($request->input('ignore_validation')){
            $this->validate($request, [
                'accounts' => ($required ? 'required|' : '') . 'array',
            ]);
            return;
        }

        $this->validate($request, [
            'accounts' => ($required ? 'required|' : '') . 'array',
            'accounts.*' => 'exists:accounts,id',
        ]);
    }

    /**
     * @throws ValidationException
     */
    private function validateTeam(Request $request, $required = false){
        $this->validate($request, [
            'team' => ($required ? 'required' : 'nullable') . '|exists:teams,id',
        ]);
    }

    /**
     * @param Request $request
     * @return Team|null
     */
    private function getTeamFromRequest(Request $request){
        /** @var Team $t */
        $t = $request->input('team') ? user()->joinedTeams()->findOrFail($request->input('team')) : null;
        return $t;
    }

    /**
     * @param Request $request
     * @return Account[]|\Illuminate\Database\Eloquent\Collection|\Illuminate\Support\Collection
     */
    private function getAvailableAccountsFromRequest(Request $request){
        $team = $this->getTeamFromRequest($request);
        // all available accounts
        if($team){
            $allAccounts = $team->accounts;
        } else {
            $allAccounts = user()->getAvailableAccounts();
        }

        // filter by network if needed
        if($request->input('network')){
            $allAccounts = $allAccounts->filter(function(Account $account) use ($request){
                return $account->type == $request->input('network');
            });
        }

        return $allAccounts;
    }

    /**
     * @param Request $request
     * @return array
     */
    private function getAccountIdsFromRequest(Request $request){
        $allAccounts = $this->getAvailableAccountsFromRequest($request);
        // accounts to filter by
        return !empty($request->input('accounts')) ? array_intersect($request->input('accounts'), $allAccounts->pluck('id')->toArray()) : $allAccounts->pluck('id')->toArray();
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function stats(Request $request){
        // simple numbers like unread counts, inactive accounts, and so on
        return response()->json([
            'unreadFeeds' => \App\Feed::userFeeds()
                ->whereHas('posts', function($query){
                    return $query->where('feed_posts.read', false);
                })->count(),
            'userAutomations' => \App\Automation::userAutomations()->count(),
            'userPendingPosts' => \App\Post::pending()
                ->ofUser()
                ->count(),
            'userFailedPosts' => \App\Post::ofUser()
                ->where('external_id', null)
                ->where('result', '<>', null)
                ->count(),
            'inactiveAccounts' => \App\Account::ofUser()
                ->where('active', false)
                ->count(),
        ]);
    }

    /**
     * @throws ValidationException
     * @return \Illuminate\Http\JsonResponse
     */
    public function postCounts(Request $request) {

        if(($upgradeResponse = $this->checkAccess($request))) {
            return $upgradeResponse;
        }

        $this->validateDates($request, true);
        $this->validateAccounts($request);
        $this->validateTeam($request);

        $this->validate($request, [
            'post_type' => 'in:image,video,text',
        ]);

        $start = Carbon::createFromFormat('Y-m-d', $request->input('start'));
        $end = Carbon::createFromFormat('Y-m-d', $request->input('end'));

        // accounts to filter by
        $accountIds = $this->getAccountIdsFromRequest($request);

        $countByDate = Post::selectRaw("DATE_FORMAT(published_at, '%Y-%m-%d') as date, count(*) as count")
            ->whereIn('account_id', $accountIds)
            ->whereBetween('published_at', [$start, $end])
            ->when($request->input('post_type'), function (/** @var $query Builder */ $query, $post_type){
                return $query->where('type', $post_type);
            })
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // create an array with all dates between start and end
        $dates = [];
        $current = clone $start;
        while($current <= $end){
            $dates[] = $current->format('Y-m-d');
            $current->addDay();
        }

        // fill the array with zeros
        $counts = array_fill_keys($dates, 0);

        // fill the array with actual counts
        foreach($countByDate as $item){
            $counts[$item->date] = $item->count;
        }

        // re-construct the array to be an array of objects
        $finalData = [];
        foreach($counts as $date => $count){
            $finalData[] = [
                'date' => $date,
                'count' => $count,
            ];
        }

        return response()->json([
            'data' => $finalData,
        ]);
    }

    /**
     * @throws ValidationException
     * @return \Illuminate\Http\JsonResponse
     */
    public function postMetrics(Request $request) {

        if(($upgradeResponse = $this->checkAccess($request))) {
            return $upgradeResponse;
        }

        $this->validateDates($request, true);
        $this->validateTeam($request);
        $this->validateAccounts($request);

        $this->validate($request, [
            'post_type' => 'in:image,video,text',
            'metrics' => 'string|required',
        ]);

        $start = Carbon::createFromFormat('Y-m-d', $request->input('start'));
        $startMinusOneDay = $start->copy()->subDays(1);
        $end = Carbon::createFromFormat('Y-m-d', $request->input('end'));

        $metricsToFetch = array_filter(explode(',', $request->input('metrics', '')));

        if(count($metricsToFetch) == 0) {
            throw ValidationException::withMessages([
                'metrics' => 'At least one metric type is required',
            ]);
        }

        // accounts to filter by
        $accountIds = $this->getAccountIdsFromRequest($request);

        $calculateGrowth = $request->input('calculate_growth', 'true') === 'true';

        $postIds = Post::whereIn('account_id', $accountIds)
            ->whereBetween('published_at', [$start, $end])
            ->when($request->input('post_type'), function (/** @var $query Builder */ $query, $post_type){
                return $query->where('type', $post_type);
            })
            ->pluck('id')->toArray();

        $data = [];
        if(count($postIds) > 0){
            //post metrics
            $metricsData = get_insights_db()
                ->table('post_metrics')
                ->whereBetween('timestamp', [$calculateGrowth ? $startMinusOneDay : $start, $end])
                ->whereIn('post_id', $postIds)
                ->whereIn('metric_type', $metricsToFetch)
                ->selectRaw("MAX(`metric_value`) as metric_value, DATE_FORMAT(`timestamp`, '%Y-%m-%d') as date, metric_type, post_id, account_id")
                ->groupBy(["metric_type", "date", "post_id", "account_id"])
                ->orderBy('date')
                ->get();

            // arrange data in a form based on account id and metric type
            $_data = [];
            foreach($metricsData as $datum){
                if(!isset($_data[$datum->account_id])){
                    $_data[$datum->account_id] = [];
                }
                if(!isset($_data[$datum->account_id][$datum->metric_type])){
                    $_data[$datum->account_id][$datum->metric_type] = [];
                }
                if(!isset($_data[$datum->account_id][$datum->metric_type][$datum->date])) {
                    $_data[$datum->account_id][$datum->metric_type][$datum->date] = 0;
                }

                // we sum because we get each row for each post
                $_data[$datum->account_id][$datum->metric_type][$datum->date] += $datum->metric_value;
            }

            $lastValuesByAccountAndMetric = [];
            foreach($_data as $account_id => $metrics){
                $accountData = [
                    'account_id' => $account_id,
                    'metrics' => [],
                ];
                foreach($metrics as $metric_type => $dailyData){
                    $accountData['metrics'][$metric_type] = [];
                    foreach($dailyData as $date => $value){

                        if($calculateGrowth && $date === $startMinusOneDay->format('Y-m-d')){
                            $lastValuesByAccountAndMetric[$account_id][$metric_type] = $value;
                            continue;
                        }

                        if($calculateGrowth){
                            if(!isset($lastValuesByAccountAndMetric[$account_id][$metric_type])){
                                $lastValuesByAccountAndMetric[$account_id][$metric_type] = $value;
                                continue; // we don't have a value for the previous day, so we can't calculate growth; skip this day
                            } else {
                                $finalValue = $value - $lastValuesByAccountAndMetric[$account_id][$metric_type];
                                $lastValuesByAccountAndMetric[$account_id][$metric_type] = $value; // update last value
                                $value = $finalValue;
                            }
                        }

                        $accountData['metrics'][$metric_type][] = [
                            'date' => $date,
                            'value' => $value,
                        ];
                    }
                }
                $data[] = $accountData;
            }
        }

        // sum data for all accounts if input has group_by_account = false
        if(!$request->input('group_by_account', false)){
            $newData = [];
            foreach($data as $accountData){
                foreach($accountData['metrics'] as $metric_type => $metricData){
                    $mType = '' . $metric_type;
                    if(!isset($newData[$mType])){
                        $newData[$mType] = [];
                    }
                    foreach($metricData as $datum){
                        if(!isset($newData[$mType][$datum['date']])){
                            $newData[$mType][$datum['date']] = 0;
                        }
                        $newData[$mType][$datum['date']] += $datum['value'];
                    }
                }
            }
            $data = [];
            foreach($newData as $metric_type => $metricData){
                foreach ($metricData as $date => $value){
                    $data[$metric_type] = $data[$metric_type] ?? [];
                    $data[$metric_type][] = [
                        'date' => $date,
                        'value' => $value,
                    ];
                }
            }
        }

        return response()->json([
            'data' => $data,
        ]);
    }

    /**
     * @throws \Exception
     * @return \Illuminate\Http\JsonResponse
     */
    public function topPosts(Request $request) {

        if(($upgradeResponse = $this->checkAccess($request))) {
            return $upgradeResponse;
        }

        $this->validateDates($request, true);
        $this->validateAccounts($request);
        $this->validateTeam($request);

        $this->validate($request, [
            'metrics' => 'string|required',
        ]);

        $metricsToFetch = array_filter(explode(',', $request->input('metrics', '')));

        if(count($metricsToFetch) == 0) {
            throw ValidationException::withMessages([
                'metrics' => 'At least one metric type is required',
            ]);
        }

        $start = Carbon::createFromFormat('Y-m-d', $request->input('start'));
        $end = Carbon::createFromFormat('Y-m-d', $request->input('end'));

        // accounts to filter by
        $accountIds = $this->getAccountIdsFromRequest($request);

        $allPosts = collect();
        $allMetrics = collect();
        Post::with('account')
            ->whereIn('account_id', $accountIds)
            ->whereBetween('published_at', [$start, $end])
            ->chunk( 1000, function($posts) use (&$allMetrics, &$allPosts) {
                $postIds = $posts->pluck('id')->toArray();
                $metricsData = Post::getMetricsForPosts($postIds);

                $allMetrics = $allMetrics->merge($metricsData);

                $allPosts = $allPosts->merge($posts);
            });

        $posts = $allPosts->sortByDesc(function (/** @var Post $post */ $post) use ($metricsToFetch, $allMetrics) {
            $metricsData = $post->getMetrics($allMetrics);
            $score = 0;
            foreach($metricsData as $metricData){
                if(in_array($metricData['type'], $metricsToFetch)){
                    $score += $metricData['value'];
                }
            }
            return $score;
        })->take(10)->map(function ($post) use ($allMetrics) {
            return Post::transform($post, $allMetrics);
        })->values()->toArray();

        return response()->json([
            'data'=> $posts,
        ]);
    }

    /**
     * @throws ValidationException
     * @throws \Exception
     */
    public function performanceByHashtags(Request $request) {

        if(($upgradeResponse = $this->checkAccess($request))) {
            return $upgradeResponse;
        }

        $this->validateDates($request, true);
        $this->validateAccounts($request);
        $this->validateTeam($request);

        $start = Carbon::createFromFormat('Y-m-d', $request->input('start'));
        $end = Carbon::createFromFormat('Y-m-d', $request->input('end'));

        $accountIds = $this->getAccountIdsFromRequest($request);

        $metric = $request->input('metric', 'impressions'); // todo: fetch all engagements; make this accept an array of metric types

        $postIdsAndHashtags = [];

        get_insights_db()
            ->table('post_hashtags')
            ->whereBetween('timestamp', [$start, $end])
            ->whereIn('account_id', $accountIds)
            ->selectRaw("post_id, GROUP_CONCAT(DISTINCT `hashtag`) as hashtags")
            ->groupBy(['post_id'])
            ->orderBy('post_id')
            ->chunk(1000, function($rows) use(&$postIdsAndHashtags){
                foreach($rows as $row){
                    $postIdsAndHashtags[] = [
                        'post_id' => $row->post_id,
                        'hashtags' => $row->hashtags,
                    ];
                }
            });

        $postIdsAndHashtags = collect($postIdsAndHashtags);

        // get insights for all posts
        $postIds = $postIdsAndHashtags->pluck('post_id')->toArray();
        $metricsData = Post::getMetricsForPosts($postIds);

        unset($postIds); // free memory

        $scoreByHashtags = [];
        foreach($postIdsAndHashtags as $row){

            $value = 0;

            $metricRow = $metricsData->where('post_id', $row['post_id'])->where('metric_type', $metric)->first();
            if($metricRow && $metricRow->metric_value){
                $value = $metricRow->metric_value;
            }

            $hashtags = explode(',', $row['hashtags']);
            foreach($hashtags as $hashtag){
                if(!isset($scoreByHashtags[$hashtag])){
                    $scoreByHashtags[$hashtag] = 0;
                }
                $scoreByHashtags[$hashtag] += $value;
            }
        }

        $data = [];
        foreach($scoreByHashtags as $hashtag => $score){
            $data[] = [
                'hashtag' => $hashtag,
                'score' => $score,
            ];
        }

        // order the array by score
        usort($data, function($a, $b){
            return $b['score'] - $a['score'];
        });

        // take the top 50
        $data = array_slice($data, 0, 30);

        return response()->json([
            'data' => $data,
        ]);
    }

    /**
     * Returns account metrics
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ValidationException
     */
    public function accountMetrics(Request $request) {

        if(($upgradeResponse = $this->checkAccess($request))) {
            return $upgradeResponse;
        }

        $this->validateDates($request, true);
        $this->validateAccounts($request);
        $this->validateTeam($request);

        $this->validate($request, [
            'metrics' => 'string|required',
        ]);

        $start = Carbon::createFromFormat('Y-m-d', $request->input('start'));
        $startMinusOneDay = $start->copy()->subDays(1);
        $end = Carbon::createFromFormat('Y-m-d', $request->input('end'));

        $metricsToFetch = array_filter(explode(',', $request->input('metrics', '')));

        // wildcard metrics
        $wildCardMetricsToFetch = collect($metricsToFetch)->filter(function($metric){
            return strpos($metric, '*') !== false;
        })->toArray();

        // remove wildcard metrics from the list
        $metricsToFetch = collect($metricsToFetch)->filter(function($metric){
            return strpos($metric, '*') === false;
        })->toArray();

        if(count($metricsToFetch) == 0 && count($wildCardMetricsToFetch) == 0){
            throw ValidationException::withMessages([
                'metrics' => 'At least one metric type is required',
            ]);
        }

        // accounts to filter by
        $accountIds = $this->getAccountIdsFromRequest($request);

        $calculateGrowth = $request->input('calculate_growth', 'true') === 'true';

        $metricsData = get_insights_db()->table('account_metrics')
            ->whereBetween('timestamp', [$calculateGrowth ? $startMinusOneDay : $start, $end])
            ->whereIn('account_id', $accountIds)
            ->where(function($query) use($metricsToFetch, $wildCardMetricsToFetch){
                if(count($metricsToFetch) > 0){
                    $query->whereIn('metric_type', $metricsToFetch);
                } else {
                    // add dummy where
                    $query->where('metric_type', '_dummy_');
                }
                foreach($wildCardMetricsToFetch as $wildCardMetric){
                    $query->orWhere('metric_type', 'like', str_replace('*', '%', $wildCardMetric));
                }
            })
            ->selectRaw("DATE_FORMAT(timestamp, '%Y-%m-%d') as date, MAX(`metric_value`) as metric_value, metric_type, account_id")
            ->groupBy(["metric_type", "date", "account_id"])
            ->orderBy('date')
            ->get();

        // arrange data by account_id
        $dataByAccountId = [];
        $lastValuesByAccountId = [];
        foreach($metricsData as $metricData){
            if(!isset($dataByAccountId[$metricData->account_id])){
                $dataByAccountId[$metricData->account_id] = [];
                $lastValuesByAccountId[$metricData->account_id] = [];
            }

            // if the date is start - 1 day, and we need to calculate growth, just store the value and do nothing
            if($calculateGrowth && $metricData->date === $startMinusOneDay->format('Y-m-d')){
                $lastValuesByAccountId[$metricData->account_id][$metricData->metric_type] = $metricData->metric_value;
                continue;
            }

            $value = $metricData->metric_value;

            if($calculateGrowth){
                if(!isset($lastValuesByAccountId[$metricData->account_id][$metricData->metric_type])){
                    $lastValuesByAccountId[$metricData->account_id][$metricData->metric_type] = $value;
                    continue; // we don't have a value for the previous day, so we can't calculate growth; skip this day
                } else {
                    $finalValue = $value - $lastValuesByAccountId[$metricData->account_id][$metricData->metric_type];
                    $lastValuesByAccountId[$metricData->account_id][$metricData->metric_type] = $value; // update last value
                    $value = $finalValue;
                }
            }

            $dataByAccountId[$metricData->account_id][$metricData->metric_type] = $dataByAccountId[$metricData->account_id][$metricData->metric_type] ?? [];

            $dataByAccountId[$metricData->account_id][$metricData->metric_type][] = [
                'date' => $metricData->date,
                'value' => $value,
            ];
        }

        $latestDataByAccountId = [];
        if(!empty($accountIds)){
            // fetch the latest row for each metric type and account_id
            $whereInPlaceholders = str_repeat('?, ',  count($accountIds) - 1) . '?';
            $latestData = get_insights_db()->table('account_metrics')
                ->whereIn('account_id', $accountIds)
                ->where(function($query) use($metricsToFetch, $wildCardMetricsToFetch){
                    $query->whereIn('metric_type', $metricsToFetch);
                    foreach($wildCardMetricsToFetch as $wildCardMetric){
                        $query->orWhere('metric_type', 'like', str_replace('*', '%', $wildCardMetric));
                    }
                })
                ->whereRaw(
                    'id in (SELECT max(`id`) FROM `account_metrics` WHERE `account_id` IN (' . $whereInPlaceholders . ') GROUP BY `metric_type`, `account_id` ) order by `id` desc',
                    [$accountIds]
                )
                ->get([
                    'account_id',
                    'metric_type',
                    'metric_value',
                    'timestamp',
                ]);
            foreach ($latestData as $accData) {
                if (!isset($latestDataByAccountId[$accData->account_id])) {
                    $latestDataByAccountId[$accData->account_id] = [];
                }
                $latestDataByAccountId[$accData->account_id][] = [
                    'type' => $accData->metric_type,
                    'value' => $accData->metric_value,
                    'timestamp' => $accData->timestamp,
                ];
            }
        }

        $data = [];

        foreach($dataByAccountId as $accountId => $accountData){
            $data[] = [
                'account_id' => $accountId,
                'metrics' => $accountData, // key-value array of metric type and values
                'latest' => $latestDataByAccountId[$accountId] ?? [], // snapshot of the latest data for each metric type
            ];
        }

        return response()->json([
            'data' => $data,
        ]);
    }

    /**
     * @throws ValidationException
     */
    public function teamMetricsByUser(Request $request){

        if(($upgradeResponse = $this->checkAccess($request))) {
            return $upgradeResponse;
        }

        $this->validateDates($request, true);
        $this->validateTeam($request, true);
        $this->validateAccounts($request);

        $this->validate($request, [
            'metrics' => 'string|required',
        ]);

        $metricsToFetch = array_filter(explode(',', $request->input('metrics', '')));

        if(count($metricsToFetch) == 0) {
            throw ValidationException::withMessages([
                'metrics' => 'At least one metric type is required',
            ]);
        }

        $start = Carbon::createFromFormat('Y-m-d', $request->input('start'));
        $end = Carbon::createFromFormat('Y-m-d', $request->input('end'));

        $team = $this->getTeamFromRequest($request);

        $accountIds = $this->getAccountIdsFromRequest($request);

        $data = [];

        $teamMembers = $team->members;

        $teamMetrics = get_insights_db()->table('misc_metrics')
            ->whereIn('account_id', $accountIds)
            ->whereBetween('timestamp', [$start, $end])
            ->whereIn('action', [
                'post_scheduled',
                'post_published',
                'post_rejected',
                'post_approved',
            ])
            ->whereIn('user_id', $teamMembers->pluck('id'))
            ->selectRaw("user_id, action, count(*) as count, DATE_FORMAT(timestamp, '%Y-%m-%d') as date")
            ->groupBy(['user_id', 'action', 'date'])
            ->orderBy('date')
            ->get();

        // group post ids by user_id
        $postIdsByUser = Post::whereIn('account_id', $accountIds)
            ->whereBetween('published_at', [$start, $end])
            ->selectRaw("user_id, group_concat(id) as post_ids")
            ->groupBy(['user_id'])
            ->get()
            ->keyBy('user_id')
            ->map(function($item){
                return explode(',', $item->post_ids);
            })->toArray();

        foreach($teamMembers as $member){

            $postIds = $postIdsByUser[$member->id] ?? [];

            $postMetrics = get_insights_db()->table('post_metrics')
                ->whereIn('metric_type', $metricsToFetch)
                ->whereIn('post_id', $postIds)
                ->selectRaw("MAX(`metric_value`) as metric_value, DATE_FORMAT(timestamp, '%Y-%m-%d') as date, metric_type")
                ->groupBy(['metric_type', 'post_id', 'date',])
                ->get();

            // sum all engagement metrics for each date
            $engagements = $postMetrics->sum('metric_value');

            $data[] = [
                'member_id' => $member->id,
                'member_name' => $member->name,
                'member_photo' => $member->getPic(),
                'total_engagements' => $engagements,
                'scheduled_posts' => $teamMetrics->where('user_id', $member->id)->where('action', 'post_scheduled')->map(function($item){
                    return [
                        'date' => $item->date,
                        'count' => $item->count,
                    ];
                })->values(),
                'published_posts' => $teamMetrics->where('user_id', $member->id)->where('action', 'post_published')->map(function($item){
                    return [
                        'date' => $item->date,
                        'count' => $item->count,
                    ];
                })->values(),
                'rejected_posts' => $teamMetrics->where('user_id', $member->id)->where('action', 'post_rejected')->map(function($item){
                    return [
                        'date' => $item->date,
                        'count' => $item->count,
                    ];
                })->values(),
            ];
        }

        // sort by total engagements
        usort($data, function($a, $b){
            return $b['total_engagements'] - $a['total_engagements'];
        });

        return response()->json([
            'data' => $data,
        ]);
    }
}
