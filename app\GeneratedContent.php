<?php

namespace App;

use App\Traits\HasOptions;
use Illuminate\Database\Eloquent\Model;

/**
 * App\GeneratedContent
 *
 * @property int $id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string $content
 * @property string $keyword
 * @property string $type
 * @property bool $is_bad
 * @property int $user_id
 * @property int $account_id
 * @property int|null $team_id
 * @property array|null $options
 * @property-read \App\Account $account
 * @property-read \App\Team|null $team
 * @property-read \App\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|GeneratedContent newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|GeneratedContent newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|GeneratedContent query()
 * @method static \Illuminate\Database\Eloquent\Builder|GeneratedContent whereAccountId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GeneratedContent whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GeneratedContent whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GeneratedContent whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GeneratedContent whereIsBad($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GeneratedContent whereKeyword($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GeneratedContent whereOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GeneratedContent whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GeneratedContent whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GeneratedContent whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GeneratedContent whereUserId($value)
 * @mixin \Eloquent
 */
class GeneratedContent extends Model
{
    use HasOptions;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'type',
        'content',
        'keyword',
        'user_id',
        'team_id',
        'account_id',
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'options' => 'array',
        'is_bad' => 'boolean',
    ];

    public static function transform(self $generatedContent){
        return [
            'id' => $generatedContent->id,
            'type' => $generatedContent->type,
            'content' => $generatedContent->content,
            'keyword' => $generatedContent->keyword,
            'user_id' => $generatedContent->user_id,
            'team_id' => $generatedContent->team_id,
            'account_id' => $generatedContent->account_id,
            'is_bad' => $generatedContent->is_bad,
            'created_at' => $generatedContent->created_at->toIso8601String(),
            'model' => with($generatedContent->getOption('model'), function ($v) {
                if(!app()->environment('production')){
                    // no need to rewrite model name if not in production
                    return $v;
                }
                // we rewrite the model name to make sure public can't guess what model is being used
                if($v === 'gpt-3'){
                    return '1';
                } else if($v === 'gpt-j'){
                    return '2';
                } else if($v === 'gpt-j-ff'){
                    return '2_ff';
                } else if($v === 'gpt-neo'){
                    return '3';
                }
                return null;
            }),
        ];
    }

    public function user()
    {
        return $this->belongsTo('App\User');
    }

    public function team()
    {
        return $this->belongsTo('App\Team');
    }

    public function account()
    {
        return $this->belongsTo('App\Account');
    }

    private function getOptions(){
        return (array) $this->options;
    }

    private function setOptions($options)
    {
        $this->options = $options;
        $this->save();
        return $this;
    }

}
