<?php

namespace App\Http\Controllers\User;

use App\Charts\ReferralsChart;
use App\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;

class ReferralsController extends Controller
{
    public function index(Request $request){
        /** @var User $user */
        $user = \Auth::user();

        $earnings = $user->getOption('referrals_earning');
        if(!$earnings)
            $earnings = 0;

        //$this->test();

        $chart = $this->getChart();

        return view('user.referrals.index', [
            'user' => $user,
            'earnings' => number_format((float) ($earnings / 100), 2), // cents to dollar
            'referralsCount' => $user->referrals()->count('id'),
            'customersCount' => $user->referrals()->whereHas('subscriptions', function($query){
                /*** @var Builder $query **/
                return $query->where('ends_at', null);
            })->count('id'),
            'hits' => DB::connection('mysql_insights')->table('referral_hits')
                ->where('referrer_id', \Auth::id())->count('id'),
            'chart' => $chart,
            'email_hide' =>  function ($email) {
                $mail_parts = explode("@", $email);
                $length = strlen($mail_parts[0]);
                $show = floor($length/2);
                $hide = $length - $show;
                $replace = str_repeat("*", $hide);

                return substr_replace ( $mail_parts[0] , $replace , $show, $hide ) . "@" . substr_replace($mail_parts[1], "**", 0, 2);
            },
        ]);
    }

    public function getChart(){
        /** @var User $user */
        $user = \Auth::user();

        $chart = new ReferralsChart;
        $labels = $hits = $signups = $customers = [];
        // last 7 weeks
        for($i=6;$i>=0;--$i){
            $ts = now()->subDays($i);
            $labels[] = $ts->format('d M, Y');
            $hits[] = DB::connection('mysql_insights')->table('referral_hits')
                ->where('referrer_id', \Auth::id())
                ->whereDay('timestamp', $ts)->count('id');
            $signups[] = $user->referrals()->whereDay('created_at', $ts)->count('id');
            $customers[] = $user->referrals()->whereHas('subscriptions', function($query){
                /*** @var Builder $query **/
                return $query->where('ends_at', null);
            })->whereDay('created_at', $ts)->count('id');
        }
        $chart->labels($labels);
        $chart->dataset('Customers', 'line', $customers)
            ->color(ReferralsChart::$colors['green'])
            ->backgroundColor(ReferralsChart::$colors['green'])
            ->fill(false);
        $chart->dataset('Sign Ups', 'line', $signups)
            ->color(ReferralsChart::$colors['purple'])
            ->backgroundColor(ReferralsChart::$colors['purple'])
            ->fill(false);
        $chart->dataset('Unique Visitors', 'line', $hits)
            ->color(ReferralsChart::$colors['orange'])
            ->backgroundColor(ReferralsChart::$colors['orange'])
            ->fill(false);

        $chart->options([
            'responsive' => true,
            'tooltips' => [
                'mode' => 'index',
                'intersect' => false,
            ],
            'hover' => [
                'mode' => 'nearest',
                'intersect' => true,
            ],
        ]);
        return $chart;
    }


    private function test(){

        $user = User::find(5);

        // referral earnings if any
        if($user->referred_by && $user->referred_by > 0){

            /** @var User $referredByUser */
            $referredByUser = $user->referrer;

            // credit the referrer
            $referrerEarnings = $referredByUser->getOption('referrals_earning');
            if(!$referrerEarnings)
                $referrerEarnings = 0;

            $commission = 499 * 0.2; // 20% flat

            $referrerEarnings = $referrerEarnings + $commission;

            $referredByUser->setOption('referrals_earning', $referrerEarnings);
        }


    }
}
