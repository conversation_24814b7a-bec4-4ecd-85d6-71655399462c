<template>
    <div class="post mt-4">
        <div v-if="attachment">
            <div v-if="options.post_as_short" class="attachment-container d-flex align-items-center justify-content-center overflow-hidden"
                 style="width: 100%;">
                <video class="w-100" ref="video" @click="playOrPause" title="attachment.name" style="max-height: 100%; object-fit: cover">
                    <source :src="attachment.url" />
                </video>
                <div class="position-absolute" style="right: 10px;bottom: 20px;z-index: 1;">
                    <div class="post-actions">
                        <div class="text-white text-center small-2 font-weight-500">
                            
                            <div class="group">
                                <i class="ph ph-thumbs-up ph-fill"></i>
                                <p class="mb-0">51K</p> 
                            </div>
                            <div class="group">
                                <i class="ph ph-thumbs-down ph-fill"></i>
                                <p class="mb-0">Dislike</p>
                                
                            </div>
                            <div class="group">
                                <i class="ph ph-chat-centered-text ph-fill"></i>
                                <p class="mb-0">90</p>
                                
                            </div>
                            <div class="share group">
                                <i class="ph ph-share-fat ph-fill"></i>
                                <p class="mb-0">Share</p>
                            </div>
                            <img :src="account.image" class="avatar rounded-md" alt="avatar" />
                            
                        </div>
                    </div>
                </div>
                <div class="user-info position-absolute text-white">
                    <div class="account-info d-flex align-items-center">
                        <img :src="account.image" class="youtube-avatar rounded-circle" alt="avatar" />
                        <span class="font-weight-500 text-white">@{{ account.name }}</span>
                    </div>
                    <div class="d-flex align-items-center justify-content-between">
                        <RichText :value="content" :readonly="true" class="mb-0 w-90" :marks="richTextMarksSchema" />
                    </div>
                </div>
            </div>
            <div class="regular-post" v-else>
                <video ref="video" @click="playOrPause" title="attachment.name" style="max-height: 100%; object-fit: cover">
                    <source :src="attachment.url" />
                </video>
                <div class="detail">

                    <p class="font-weight-700 title mb-2" v-if="options.video_title">{{ options.video_title }}</p>
                    <div class="views small-2 mb-1">120 views 1m ago</div>
                    <div class="d-flex align-items-center mt-4 mb-3">
                        <img :src="account.image" class="youtube-avatar rounded-circle" alt="avatar" />
                        <span class="font-weight-500 account-name mr-2">{{ account.name }}</span>
                        <span class="time">12.6M</span>
                    </div>
                    <div class="d-flex flex-wrap align-items-center font-weight-500" v-if="tags">
                        <div class="tags-component mb-2 mr-2" v-for="(tag,index) in tags" :key="index">
                            #{{ tag }}
                        </div> 
                    </div>
                    <div class="description" v-if="content">
                        <RichText :value="content" :readonly="true" class="mb-0" :marks="richTextMarksSchema" />
                    </div>
                </div>
            </div>
        </div>
        <div class="d-flex justify-content-center align-items-center w-100" v-else>
            <p> Please attach media</p>
        </div>
    </div>
</template>

<script>
import LinkPreview from "../LinkPreview.vue";
import RichText from "../../common/RichText.vue";
import { sanitizeHtml } from "../../../../components";
import { truncate } from "lodash";
export default {
    name: "YoutubeVideo",
    props: ["account", "text", "attachments", "link", "options"],
    components: {
        LinkPreview,
        RichText
    },
    data(){
        return {
            showFull: false
        }
    },
    computed: {
        content() {
            // normalize linebreaks
            let text = (this.text || "").replace(/(\r\n|\r|\n){2,}/g, "$1\n");
            return sanitizeHtml(this.showFull ? text : text.length >= 80 ? (`${text.substring(0, 80)}<span class="_see_all cursor-pointer"> ...</span>`) : text);

        },
        richTextMarksSchema() {
            return {
                span: {
                    attrs: {
                        className: {}, // Define class attribute for the span
                        styles:{default:null}
                    },
                    inclusive: false,
                    parseDOM: [
                        {
                            tag: "span[class]", // Match span elements with a class attribute
                            getAttrs: dom => {
                                const className = dom.getAttribute('class');
                                const styles = dom.getAttribute('style');
                                return { className, styles };
                            }
                        }
                    ],
                    toDOM(node) {
                        const { className, styles } = node.attrs;
                        return ["span", { class: className, style: styles }, 0];
                    }
                }
            }
        },
        attachment(){
            return this.attachments && this.attachments[0];
        },
        tags(){
            if(this.options.video_tags){
                return this.options.video_tags.split(',').map(tag => tag.trim());
            }
        }
    },
    methods: {
        truncate,
        playOrPause(){
            if(this.$refs.video.paused){
                this.$refs.video.play();
            } else {
                this.$refs.video.pause();
            }
        },
        showMore() {
            this.showFull = true;
            return false;
        }
    },
    mounted() {
        $(document).on("click.see_all", "._see_all", this.showMore);
    },
    beforeDestroy() {
        $(document).off("click.see_all", "._see_all", this.showMore);
    }
};
</script>
<style lang="scss" scoped>
.post{
    font-family: Roboto, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Ubuntu, "Helvetica Neue", sans-serif;
}
.regular-post{
    .detail{

        padding: 0px 14px;
        margin-top: 14px;
        .title{
            color: #0F0F0F;
            line-height: 25.3px !important;
            font-size: 22px;
        }
        .views{
            color: #606060;
            line-height: 16.1px !important;
        }
        .account-name{
            color: #0F0F0F;
            line-height: 18.4px !important
        }
        .time{
            color: #606060;
            line-height: 16.1px !important;
        }
        .tags-component{
            padding: 10px 14px;
            border-radius: 100px;
            background: #F2F2F2;
            line-height: 16.1px !important;
            color: #0F0F0F;
        }
        .description{
            padding: 14px 14px 8px 14px;
            margin: 5px 0px 10px 0px;
            border-radius: 15px;
            background: #F2F2F2;
            p{
                color: #0F0F0F !important;
            }
        }
    }
}
.youtube-avatar{
    width: 38px;
    height: 38px;
    margin-right: 10px;
}
.attachment-container{
    height: 754px;
    background: #000000;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}
.user-info{
    bottom:18px; 
    left: 18px;
    right: 14px; 
    z-index: 1;
    .account-info{
        margin-bottom: 13px;
    }
    p{
        font-size: 18px;
        line-height: 27px !important;
    }
}

.post-actions{
    .group{
        margin-bottom: 28px;
        i{
            filter: drop-shadow(0px 1px 4px rgba(0, 0, 0, 0.20));
            font-size: 34px;
            margin-bottom: 6px;
        }
        p{
            text-shadow: 0px 1px 4px rgba(0, 0, 0, 0.20);
        }
    }
    .share{
        margin-bottom: 25px;
    }

}
</style>
