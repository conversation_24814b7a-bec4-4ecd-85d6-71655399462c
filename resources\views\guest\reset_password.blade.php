@extends('guest.layout')
@section('title', 'Reset Password | ' . config('app.name'))
@section('content')

    
    <form class="form-auth form-horizontal" role="form" method="POST" action="@if (!empty($token)){{ url('/auth/reset_password/reset') }}@else{{ url('/auth/reset_password') }}@endif">
    
        <div class="text-center mb-5">
            <a href="/" title="{{ config('app.name') }}"><img class="logo lozad" src="/images/redesign/logo.svg" alt="logo" /></a>
        </div>

        {{ csrf_field() }}

        @if (!empty($token))
            <h5 class="text-center mb-5 font-weight-500">@lang('auth.new_password')</h5>
        @else
            <h5 class="text-center mb-5 font-weight-500">@lang('auth.forgot_password')</h5>
        @endif

        @include('layout.partials.errors')

        @include('layout.partials.flash') 

        <div class="form-group{{ $errors->has('email') ? ' has-error' : '' }} mb-5">
            <input type="text" class="form-control @if (!empty($token)) first @else with-margin @endif" name="email" placeholder="@lang('generic.email')" value="{{ $email ?? old('email') }}" required="" autofocus="" />
        </div>

        @if (!empty($token))
            <div class="form-group{{ $errors->has('password') ? ' has-error' : '' }} position-relative mb-4">
                <input type="password" id="password" class="form-control single" name="password" placeholder="@lang('generic.password')" required=""/>
                <i class="ph ph-eye ph-md position-absolute cursor-pointer toggle-password-btn" id="togglePassword">
            </i>
            </div>

            <div class="form-group position-relative mb-4">
                <input type="password" id="passwordConfirmation" class="form-control" name="password_confirmation" placeholder="@lang('auth.retype_password')" required=""/>
                <i class="ph ph-eye ph-md position-absolute cursor-pointer toggle-password-btn" id="togglePasswordConfirmation">
            </i>
            </div>

            <input type="hidden" name="token" value="{{ $token ?? null }}" />

        @endif

        <button class="btn btn-lg btn-primary btn-lg btn-block" type="submit">@lang('auth.reset_password')</button>
        
        <p class="text-center mt-4">
            <a href="{{ url('/auth/login') }}" rel="noreferrer">@lang('auth.back_to_login')</a>
        </p>

    </form>

@endsection