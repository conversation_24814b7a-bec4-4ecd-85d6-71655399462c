<?php

namespace App\Http\Middleware;

use App\Http\Controllers\User\OnboardingController;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class RequireOnboardingCompleted
{
    /**
     * Handle an incoming request.
     *
     * @param  Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $user = user();
        if($user){
            $currentRouteName = $request->route()->getName();
            $routeNames = [
                'publish.',
                'feeds.',
                'automations.',
                'analyze.',
                'generate.',
                'respond.',
                'home',
            ];
            $isTargetRoute = collect($routeNames)->contains(function ($str) use($currentRouteName) {
                return Str::contains($currentRouteName, $str);
            });
            // we only redirect if no input is sent to avoid interrupting other processes
            if($isTargetRoute && empty($request->input())){
                // check if all steps are completed
                $isDone = (new OnboardingController())->isOnboardingCompleted();
                if( !$isDone ) {
                    return redirect()->route('onboarding', ['from' => $request->fullUrl()]);
                }
            }
        }
        return $next($request);
    }
}
