<?php

namespace App\Jobs;

use App\Account;
use App\Helpers\TwitterOauthCustom;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Str;

class SetupAccount implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $account;

    protected $force = false;

    /**
     * Delete the job if its models no longer exist.
     *
     * @var bool
     */
    public $deleteWhenMissingModels = true;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Account $account, $force = false)
    {
        $this->account = $account;
        $this->force = $force;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        \Redis::funnel('setup_account::' . $this->account->id)
            ->releaseAfter(60 * 5) // 5 minutes
            ->limit(1)
            ->then(function () {
                $method = 'setup' . Str::studly(str_replace('.', '_', $this->account->type));
                if (method_exists($this, $method)) {
                    $this->{$method}();
                }
            }, function () {
                // Could not obtain lock...
                $this->release(10);
            });
    }

    /**
     * @throws \Exception
     */
    private function setupRedditSubreddit(){

        $lastFetched = $this->account->getOption('extra_data.flairs_last_fetched_at');

        if($lastFetched && !$this->force){
            $lastTs = Carbon::createFromTimestamp($lastFetched);

            if(now()->diffInDays($lastTs) < 7){
                //return if the last fetched time is less than 7 days;
                return ;
            }
        }

        $token = json_decode($this->account->token, true);

        sleep(20); // sleep for 20 seconds to avoid rate limiting

        /** @var Client $reddit */
        $reddit = $this->account->getApi();

        $subreddit = $token['subreddit'];

        // fetch flairs
        $flairs = [];
        try {
            $flairsRes = $reddit->get('/r/' . $subreddit . '/api/link_flair_v2', [
                'query' => [
                    'raw_json' => '1',
                ],
            ]);
            $flairsResJson = json_decode($flairsRes->getBody()->getContents(), true);

            foreach ($flairsResJson as $flair) {
                if(!isset($flair['text']) || $flair['text'] === ''){
                    continue;
                }
                $flairs[] = [
                    'id' => $flair['id'],
                    'text' => $flair['text'],
                ];
            }
            $flairs_last_fetched_at = time();
            $this->account->setOption('extra_data.flairs_last_fetched_at', $flairs_last_fetched_at);

            // set flairs in extra_data option
            $this->account->setOption('extra_data.flairs', $flairs);
        } catch (\Exception $exception){

            if($exception instanceof \GuzzleHttp\Exception\ClientException){
                $response = $exception->getResponse();
                if($response && $response->getStatusCode() === 403){
                    $this->account->setOption('extra_data.flairs', []);
                } else {
                    $responseBodyAsString = $response->getBody()->getContents();
                    \Log::error('Error fetching flairs for r/' . $subreddit . ': ' . $responseBodyAsString);
                    if(!Str::contains(strtolower($responseBodyAsString), ['not found', 'banned'])) {
                        report($exception);
                    }
                }
                return;
            }

            // not available for this subreddit probably
            report($exception);
        }

    }

    private function setupTiktokProfile(){

        $lastFetched = $this->account->getOption('extra_data.creator_info.last_fetched_at');

        if($lastFetched && !$this->force){
            $lastTs = Carbon::createFromTimestamp($lastFetched);

            if(now()->diffInHours($lastTs) < 24){
                //return if the last fetched time is less than 24;
                return ;
            }
        }

        /** @var Client $reddit */
        $tiktok = null;
        try {
            $tiktok = $this->account->getApi();
        } catch (\Exception $exception){
            if(Str::contains(strtolower($exception->getMessage()), ['the user did not authorize the scope', 'scope not authorized', 'expired', 'invalid'])){
                $this->account->testConnection(true, 'Unable to connect with TikTok. Please reconnect your account.');
                return;
            }
            report($exception);
            return;
        }

        try {
            $res = $tiktok->post('/v2/post/publish/creator_info/query/');
            $resData = json_decode($res->getBody()->getContents(), true);

            if(isset($resData['error']) && $resData['error']['code'] !== 'ok'){
                // some error occurred
                if($this->account->testConnection(true, 'Unable to fetch creator info. Please reconnect your account.')){

                    if($resData['error']['code'] == 'spam_risk_user_banned_from_posting'){
                        return;
                    }

                    // its connected, so report issue
                    report(new \Exception('Error fetching creator info: ' . json_encode($resData['error'])));
                }
                return;
            }

            $data = $resData['data'];
            $data['last_fetched_at'] = time();

            $this->account->setOption('extra_data.creator_info', $data);

        } catch (\Exception $exception){
            if(Str::contains(strtolower($exception->getMessage()), ['the user did not authorize the scope', 'scope not authorized'])){
                // sentry overloading, no need to report. we're already asking users to reconnect accounts when posts fail.
                return;
            }
            if($exception instanceof \GuzzleHttp\Exception\ClientException){
                $response = $exception->getResponse();
                $responseBodyAsString = $response->getBody()->getContents();

                \Log::error('Error fetching creator info for ' . $this->account->name . ': ' . $responseBodyAsString);

                if( in_array($response->getStatusCode(), [401, 400, 403] )){
                    return;
                }
            }
            report($exception);
        }
    }

    /**
     * @throws \Exception
     */
    private function setupPinterestProfile(){

        $lastFetched = $this->account->getOption('extra_data.boards_last_fetched_at');

        if($lastFetched && !$this->force){
            $lastTs = Carbon::createFromTimestamp($lastFetched);

            if(now()->diffInDays($lastTs) < 2){
                // return if the last fetched time is less than 2 days;
                return;
            }
        }

        $pinterestClient = $this->account->getApi();
      

        $boards = [];
        $bookmark = null;
        do {
            try {
                $res = $pinterestClient->get('v5/boards?page_size=250' . ($bookmark ? '&bookmark=' . $bookmark : ''));
                $jsonRes = json_decode($res->getBody()->getContents(), true);

                $bookmark = $jsonRes['bookmark'] ?? null;
                foreach($jsonRes['items'] as $res){
                    $boards[] = [
                        'id' => $res['id'],
                        'name' => $res['name'],
                    ];
                }

                if(!empty($boards)){
                    $this->account->setOption('extra_data.boards', $boards);
                }

                $this->account->setOption('extra_data.boards_last_fetched_at', time());

            } catch (\Exception $e) {

                if($e instanceof \GuzzleHttp\Exception\ClientException){
                    $response = $e->getResponse();
                    if($response && in_array($response->getStatusCode(), [403, 401])){
                        $this->account->testConnection(true, 'Unable to fetch boards. Please reconnect your account.');
                    } else {
                        $responseBodyAsString = $response->getBody()->getContents();
                        \Log::error('Error fetching boards for ' . $this->account->name . ': ' . $responseBodyAsString);
                        report($e);
                    }
                    return;
                }

                report($e);
                return;
            }
        } while($bookmark);

    }

    /**
     * @throws \Exception
     */
    private function setupTwitterProfile()
    {
        $lastFetched = $this->account->getOption('extra_data.subscription_type_last_fetched_at');

        if($lastFetched && !$this->force){
            $lastTs = Carbon::createFromTimestamp($lastFetched);

            if(now()->diffInDays($lastTs) < 7){
                // return if the last fetched time is less than x days;
                return;
            }
        }

        try {
            /** @var TwitterOauthCustom $twitter */
            $twitter = $this->account->getApi();
        } catch (\Exception $exception) {
            if (Str::contains(strtolower($exception->getMessage()), ['token was invalid'])) {
                $this->account->testConnection(true, 'Unable to connect with Twitter. Please reconnect your account.');
                return;
            }
            report($exception);
            return;
        }

        // we need to use v2 to get user details
        $twitter->setApiVersion('2');

        $res = $twitter->get('users/me', [
            'user.fields' => 'subscription_type',
        ]);

        if($twitter->getLastHttpCode() === 401){
            $this->account->testConnection(true, 'Unable to fetch Twitter subscription type. Please reconnect your account.');
            return;
        }
        
        if($twitter->getLastHttpCode() === 200){
            
            $subType = $res->data->subscription_type;

            $this->account->setMultipleOptions([
                'extra_data.subscription_type' => $subType,
                'extra_data.subscription_type_last_fetched_at' => time(),
            ]);
        }else {
            $this->account->testConnection(true, 'Unable to fetch Twitter subscription type (#2). Please reconnect your account.');
            return;
        }
    }
}
