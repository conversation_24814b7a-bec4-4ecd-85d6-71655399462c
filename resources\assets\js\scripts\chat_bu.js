import $ from "jquery";

// ready component
const componentPromise = new Promise(res => {
    window.__loadComponent("chat-bu", "#chat_bu_container", c => {
        res(c);
    });
});

let $modal;

// setup chat bu
(async () => {
    const component = await componentPromise;

    $("#chat_bu_container").removeClass("d-none"); // un-hide the container

    $modal = $("#chat_bu_modal");
    $modal
        .modal({
            show: false
        })
        .on("show.bs.modal", async function(e) {
            component.load();
        })
        .on("shown.bs.modal", async function(e) {
            component.focusTextArea();
        })
        .on("hidden.bs.modal", function(e) {

        });

    // open chat bu
    $(".open_chat_bu_modal").on("click", async function() {
        const $sidebarToggle = $("#sidebar_toggle");
        const mobMenuState = $sidebarToggle.attr("data-state");
        if (mobMenuState === "open") {
            $sidebarToggle.trigger("click");
            await new Promise(res => setTimeout(res, 250));
        }
        $modal.modal("show");
    });

})();
