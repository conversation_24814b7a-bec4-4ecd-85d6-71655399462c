<template>
    <div class="post">
        
        <div v-if="!attachments.length" class="p-8 bg-light rounded-md mt-4">
            Please attach media for Pinterest.
        </div>
        <template v-else>
            <div class="w-100">
                <video class="w-100 video" controls :title="attachments[0].name" v-if="attachments[0].type.includes('video')">
                    <source :src="attachments[0].url" />
                </video>
                <img class="w-100" :src="attachments[0].url" :alt="attachments[0].name" :title="attachments[0].name"
                     v-else/>
            </div>
            <div class="pinterest-content d-flex flex-column">
                <div class="d-flex mb-4 pb-1">
                    <div>
                        <img :src="account.image" class="pinterest-avatar rounded-circle" alt="avatar" />
                    </div>
                    <div>
                        <div class="account-name font-weight-500 mb-0">{{ account.name }}</div>
                        <p class="followers mb-0">58k Followers</p>
                    </div>
                </div>
                <p v-if="title" class="pin-title font-weight-500"> <span v-html="title"></span></p>
                <RichText class="text" style="min-height: 8px;" placeholder="Write something..."
                    :value="content"
                    :readonly="true" :marks="richTextMarksSchema"/>
            </div>
        </template>
    </div>
</template>

<script>
import _ from "lodash";
import { sanitizeHtml} from "../../../../components";
import RichText from "../../common/RichText.vue";

export default {
    name: "PinterestPost",
    props: ["account", "text", "attachments", "options"],
    data() {
        return {
            showFull: false,
        };
    },
    components: {RichText},
    computed: {
        content(){
            let text = this.text.replace(/(\r\n|\r|\n){2,}/g, "$1\n"); // normalize linebreaks
            return sanitizeHtml(this.showFull ? text : text.length >= 110 ? (`${text.substring(0, 110)}<span class="_see_all cursor-pointer font-weight-500">... more</span>`) : text);
        },
        title(){
            if(!this.options.pin_title) return null;
            return this.options.pin_title;
        },
        richTextMarksSchema() {
            return {
                span: {
                    attrs: {
                        className: {}, // Define class attribute for the span
                        styles:{default:null}
                    },
                    inclusive: false,
                    parseDOM: [
                        {
                            tag: "span[class]", // Match span elements with a class attribute
                            getAttrs: dom => {
                                const className = dom.getAttribute('class');
                                const styles = dom.getAttribute('style');
                                return { className, styles };
                            }
                        }
                    ],
                    toDOM(node) {
                        const { className, styles } = node.attrs;
                        return ["span", { class: className, style: styles }, 0];
                    }
                }
            }
        }
    },
    methods: {
        showMore() {
            this.showFull = true;
            return false;
        }
    },
    mounted() {
        $(document).on("click.see_all", "._see_all", this.showMore);
    },
    beforeDestroy() {
        $(document).off("click.see_all", "._see_all", this.showMore);
    },
};
</script>

<style lang="scss" scoped>
.post {
    font-family: Roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
    color: #111111 !important;
}
.pinterest-avatar{
    widows: 54px;
    height: 54px;
    margin-right: 10px;
}
.account-name{
    font-size: 20px;
    letter-spacing:-0.5px;
    line-height: 26px !important;
}
.video{
    object-fit: cover;
}
.followers{
    font-size: 18px;
    line-height: 23.4px;
}
.pinterest-content{
    padding: 20px 20px 40px 20px;
}
.pin-title {
    font-size: 23px;
    line-height: 32.2px;
    margin-bottom: 10px;
}
.text {
    line-height: 23.4px;
    font-size: 18px;
}
</style>
