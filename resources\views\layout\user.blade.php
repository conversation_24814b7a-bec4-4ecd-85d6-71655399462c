<!DOCTYPE html>
<html lang="en">
<head>
    @include('layout.includes.user.head')
</head>

<body>
@include('layout.includes.common_body')
<div id="fb-root"></div>
<script>
    (function(d, s, id) {
        var js, fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) return;
        js = d.createElement(s); js.id = id;
        js.src = 'https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v3.1&appId={{ config('services.facebook.client_id') }}&autoLogAppEvents=1';
        fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));
</script>
<div id="app">
    @include('layout.partials.user.navigation')

    <div class="container-fluid app-container">
        <div class="row">

            <div class="sidebar border-secondary pr-1 pb-0 pl-3 bg-light" id="sidebar_menu_wrapper">
                @include('layout.partials.user.sidebar_left')
            </div>
            <main role="main" class="col main px-0">
                <div class="container-fluid px-md-6 px-4 py-md-6 py-20 pt-6 bg-white rounded-lg main-content">
                    <div class="row">
                        <div class="col-12">

                            @if(user()->hasIncompletePayment('default') && !isActiveRoute('settings'))
                                @php($latestPayment = user()->subscription('default')->latestPayment())
                                @if($latestPayment)
                                    <div class="alert alert-warning">
                                        Your subscription is not active yet.

                                        Please <a class="alert-link border-bottom border-info" href="{{ route('cashier.payment', [$latestPayment->id, 'redirect' => route('settings', ['#billing'])]) }}">complete the payment</a> to activate your subscription.
                                    </div>
                                @endif
                            @endif

                            @if(user() && !(new \App\Http\Controllers\User\OnboardingController())->isOnboardingCompleted())
                                <!-- onboarding alert -->
                                <div class="alert alert-info d-flex align-items-center">
                                    <div class="ph-2xl mr-4 pr-4">
                                        {{ \Illuminate\Support\Arr::random(['🤗', '😍', '🥳', '🤩', '🤞', '😻']) }}
                                    </div>
                                    <div class="row w-100">
                                        <div class="col-12 col-lg-8">
                                            <h4 class="alert-heading">Welcome to {{ config('app.name') }}</h4>
                                            <p>
                                                We're glad you're here. Before you can start, you need to complete your {{ config('app.name') }} profile.
                                            </p>
                                        </div>
                                        <div class="col-12 col-lg-4">
                                            <div class="d-lg-flex align-items-center justify-content-end h-100">
                                                <a href="{{ route('onboarding', ['from' => request()->fullUrl()]) }}" class="alert-link">Get Started Now &rarr;</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            @include('layout.partials.flash')
                            @include('layout.partials.errors')
                            @yield('content')
                        </div>
                    </div>
                </div>
            </main>
        </div>

    </div>

</div>
@include('common.internal.install-extension-modal')
@include('layout.partials.user.footer')

@include('layout.includes.user.footer_html')
@stack('footer_html')
</body>
</html>
