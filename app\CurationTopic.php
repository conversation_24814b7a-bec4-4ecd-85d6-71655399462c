<?php

namespace App;

use App\Traits\HasOptions;
use Illuminate\Database\Eloquent\Model;

/**
 * App\CurationTopic
 *
 * @property int $id
 * @property string $name
 * @property array|null $options
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\CurationFeed[] $feeds
 * @property-read int|null $feeds_count
 * @method static \Illuminate\Database\Eloquent\Builder|CurationTopic newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CurationTopic newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CurationTopic query()
 * @method static \Illuminate\Database\Eloquent\Builder|CurationTopic whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationTopic whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationTopic whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationTopic whereOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CurationTopic whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class CurationTopic extends Model
{
    use HasOptions;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
    ];

    protected $hidden = [
        'options',
        'updated_at',
        'created_at',
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'options' => 'array',
    ];


    public function feeds()
    {
        return $this->belongsToMany(CurationFeed::class, 'curation_feed_topic', 'topic_id', 'feed_id');
    }

    private function getOptions()
    {
        return (array)$this->options;
    }

    private function setOptions($options)
    {
        $this->options = $options;
        $this->save();
        return $this;
    }}
