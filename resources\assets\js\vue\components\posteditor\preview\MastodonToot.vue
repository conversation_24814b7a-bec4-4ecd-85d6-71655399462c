<template> 
    <div class="post mt-4">
        <div v-for="(tweet, i) in tweets" :key="i + '-' + tweet.media.length + 'm_preview'">
            <div class="user-info d-flex justify-content-between align-items-start">
                <div class="d-flex">
                    <img :src="account.image" class="avatar" alt="avatar" />
                    <div style="min-width: 80%;">
                        <div class="fullname font-weight-500">{{ account.name }}</div>
                        <div class="username">1m ago • @username</div>
                    </div>
                </div>
                <div>
                    <i></i> 
                </div>
            </div>

            <RichText v-if="tweet.content" class="text" :value="tweet.content" :highlight="[ 'hashtag', 'mention', 'mastodon_mention', 'link', ]" :readonly="true"  />

            <div class="overflow-hidden mt-2" v-if="tweet.media && tweet.media.length">
                <div class="gallery gallery-4-type-2" v-if="tweet.media.length > 1">
                    <div v-for="(attachment, index) in tweet.media" class="gallery-item" :key="index + attachment.url">
                        <video controls :title="attachment.name" v-if="attachment.type.includes('video')">
                            <source :src="attachment.url" />
                        </video>
                        <img :src="attachment.url" :alt="attachment.name" :title="attachment.name" v-else/>
                    </div>
                </div>
                <div class="w-100 text-center" v-else-if="tweet.media.length === 1">
                    <video class="w-100" controls :title="tweet.media[0].name"  v-if="tweet.media[0].type.includes('video')">
                        <source :src="tweet.media[0].url" />
                    </video>
                    <img class="w-100" :src="tweet.media[0].url" :alt="tweet.media[0].name" :title="tweet.media[0].name"
                         v-else/>
                </div>
            </div>

            <LinkPreview
                v-if="link && attachments.length === 0"
                :url="link"
                type="mastodon" />
            <div class="post-footer">
                <div class="post-actions">
                    <div class="d-flex align-items-center justify-content-between">
                        <div class="d-flex align-items-center">
                            <i class="ph ph-ph ph-arrow-bend-up-left ph-bold ph-md"></i>
                            <span>11</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="ph ph-repeat ph-bold ph-md"></i>
                            <span>154</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="ph ph-star ph-bold ph-md"></i>
                            <span>52</span>
                        </div>
                        <i class="ph ph-share-network ph-bold ph-md"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import LinkPreview from "../LinkPreview.vue";
import RichText from "../../common/RichText.vue";

export default {
    name: "MastodonToot",
    props: ["account", "text", "attachments", "link", "options"],
    components: {
        LinkPreview,
        RichText
    },
    computed: {
        content() {
             // normalize linebreaks
            return (this.text || "").replace(/(\r\n|\r|\n){2,}/g, "$1\n");
        },
        tweets() {
            const tweets = [];
            tweets.push({ content: this.content, media: this.attachments });
            return tweets;
        }
    }
};
</script>

<style lang="scss" scoped>

.post {
    font-family: Roboto, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Ubuntu, "Helvetica Neue", sans-serif;
}
.user-info{
    padding: 0px 20px 16px 18px;
    .avatar{
        border-radius: 10px;
        margin-right: 10px;
    }

    .fullname {
        color: #000000 !important;
        line-height: 27.9px;
        font-size: 18px;
    }
    .username{
        color: #45455F !important;
        line-height: 24.8px;
    }

}
.text {
    overflow-wrap: break-word;
    word-break: break-word;
    color: #000000 !important;
    font-size: 18px;
    line-height: 27.9px !important;
    padding: 0px 20px 0px 18px !important;
    a {
        color: #6364ff !important;
    }
}

.post-actions{
    padding: 16px 20px 20px 21px;
    color: #606085 !important;
    i{
        margin-right: 7px;
    }
}
</style>
