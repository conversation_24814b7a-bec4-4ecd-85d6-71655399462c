<template>
    <div>
        <div class="card border" v-if="!loaded">
            <div class="card-body text-center">
                <i class="ph ph-circle-notch ph-spin ph-lg text-muted"></i><span class="sr-only">Loading...</span>
            </div>
        </div>
        <div v-else v-cloak>
            <div class="card border" v-if="error">
                <div class="card-body">
                    <div class="text-center alert alert-danger">{{ error }}</div>
                </div>
            </div>
            <portal target-el="#onlineUsers">
                <div
                        v-if="onlineUsers.length">
                    <div class="d-inline-block mr-1 live-user" v-for="(u,i) in onlineUsers" :key="i">
                        <img :src="u.image" v-tooltip="u.name" class="avatar-xs rounded-circle" />
                        <i class="ph ph-fill ph-circle text-success small-6" :class="{'opacity-0': !u.postId || selectedPostId !== u.postId}"></i>
                    </div>
                </div>
            </portal>
            <div class="card border mb-5">
                <div class="card-body p-0">

                    <div class="row">
                        <div class="col-4 pr-0 border-right border-secondary">
                            <input class="form-control rounded-0 shadow-none border-right-0 border-top-0 border-left-0" type="search" placeholder="Search" v-model="search" />
                            <!-- <div class="p-4 bg-light "
                                    v-if="hasNewData && !loadingPosts">
                                <div class="card border border-primary">
                                    <div class="card-body">
                                        <h5 class="card-title">New Items Available</h5>
                                        <button class="btn btn-outline-primary btn-block"
                                            @click="reloadData">
                                            <i class="ph ph-repeat"></i> Reload
                                        </button>
                                    </div>
                                </div>
                            </div> -->
                            <div v-if="hasNewData && !loadingPosts" >
                                <button class="btn -1 btn-block" @click="reloadData">
                                    <i class="ph ph-repeat"></i> 
                                    Reload to see new items
                                </button>
                            </div>
                            <div is="VuePerfectScrollbar" class="posts-container">
                                <div class="post list-group-item-action flex-column align-items-start p-3 cursor-pointer"
                                     :class="{'active bg-light text-dark': selectedPost && selectedPost.id === post.id, 'border-secondary border-top': i > 0, 'read bg-light': post.read && !unread[post.id], 'unread bg-white': !post.read || unread[post.id], 'deleting': deletingPostId === post.id}" v-for="(post, i) in posts.data" :key="i" @click="selectedPostId = post.id">
                                    <div class="d-flex">
                                        <!-- image -->
                                        <div class="post-img mr-3 d-none d-md-block">
                                            <img class="avatar"
                                                 :src="post.data.last_user ? post.data.last_user.profile_image_url_https : post.data.user.profile_image_url_https" @error="setAltImage" v-if="accountsById[post.account_id].type === 'twitter.profile'" />

                                            <template  v-else-if="accountsById[post.account_id].type === 'instagram.api'">
                                                <template v-if="post.type === 'message'">
                                                    <img  class="avatar"
                                                          :src="post.data.from.profile_pic" v-if="post.data.from" @error="setAltImage"/>
                                                    <img  class="avatar"
                                                          :src="post.data.from.image" v-else @error="setAltImage"/>
                                                </template>
                                                <template v-if="post.type === 'post'">
                                                    <!-- last_from is the from object for the other user who was last involved -->
                                                    <img alt="avatar" class="avatar"
                                                          src="/images/no-image.png" @error="setAltImage"/>
                                                </template>
                                            </template>
                                            <template v-else>
                                                <template v-if="post.type === 'message'">
                                                    <img  class="avatar"
                                                          :src="'/app/_facebook/picture/' + accountsById[post.account_id].public_id + '/' + post.data.from.id" v-if="post.data.from.id !== accountsById[post.account_id].account_id" @error="setAltImage" />
                                                    <img  class="avatar"
                                                          :src="'/app/_facebook/picture/' + accountsById[post.account_id].public_id + '/' + post.data.to[0].id" v-else @error="setAltImage"/>
                                                </template>
                                                <template v-else-if="post.type === 'post'">
                                                    <!-- last_from is the from object for the other user who was last involved -->
                                                    <img  class="avatar"
                                                          :src="'/app/_facebook/picture/' + accountsById[post.account_id].public_id + '/' + post.data.last_from.id" v-if="post.data.last_from" @error="setAltImage"/>
                                                    <img  class="avatar"
                                                          :src="'/app/_facebook/picture/' + accountsById[post.account_id].public_id + '/' + post.data.from.id" v-else @error="setAltImage" />
                                                </template>
                                            </template>
                                            <i class="network-type fa social-icon active"
                                               :class="{'fa-facebook-square facebook': accountsById[post.account_id].type === 'facebook.page', 'fa-twitter-square twitter': accountsById[post.account_id].type === 'twitter.profile', 'fa-instagram instagram': accountsById[post.account_id].type === 'instagram.direct'}"></i>
                                        </div>

                                        <!-- name and timestamp -->
                                        <div class="w-100">
                                            <div class="d-block d-md-flex justify-content-between">
                                                <!-- name -->
                                                <div>
                                                    <template v-if="accountsById[post.account_id].type === 'twitter.profile'">
                                                        <h5>
                                                            {{ post.data.user.name }}
                                                        </h5>
                                                        <!-- <small>@{{ post.data.user.screen_name }}</small> -->
                                                    </template>
                                                    <h5 v-else-if="accountsById[post.account_id].type === 'instagram.direct'">
                                                        <template v-if="post.type === 'message' && post.data.from.id === accountsById[post.account_id].account_id">
                                                            {{ accountsById[post.account_id].name }}
                                                        </template>
                                                        <template v-else>
                                                            {{ post.data.last_from ? post.data.last_from.name : post.data.from.name }}
                                                        </template>
                                                    </h5>
                                                    <h5 v-else-if="accountsById[post.account_id].type === 'instagram.api'">
                                                        @{{ post.data.last_from ? post.data.last_from.username : post.data.from.username }}
                                                    </h5>
                                                    <h5 v-else>
                                                        <template v-if="post.type === 'message' && post.data.from.id === accountsById[post.account_id].account_id">
                                                            {{ post.data.to[0].name }}
                                                        </template>
                                                        <template v-else>
                                                            {{ post.data.last_from ? post.data.last_from.name : post.data.from.name }}
                                                        </template>
                                                    </h5>
                                                </div>

                                                <!-- timestamp -->
                                                <small class="small-6">
                                                    {{ getTimestamp(post).fromNow() }}
                                                    <span class="unread-count badge badge-danger" v-if="!post.read || unread[post.id]">NEW</span>
                                                </small>

                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex mt-3 justify-content-between post-text">
                                        <!-- text -->
                                        <div class="d-flex justify-content-between align-items-start" v-if="accountsById[post.account_id].type === 'twitter.profile'">
                                            <template
                                                    v-if="post.type === 'message'">
                                                <p
                                                        v-html="truncate(post.data.latest ? post.data.latest.message_create.message_data.text : post.data.message_create.message_data.text, { length: 80 })"
                                                        v-if="(post.data.latest ? post.data.latest.message_create.message_data.text : (post.data.message_create.message_data.text || '')).length">
                                                </p>
                                                <p v-else>
                                                    ...
                                                </p>
                                            </template>
                                            <template
                                                    v-else>
                                                <p
                                                        v-if="(post.data.latest ? post.data.latest.full_text : (post.data.full_text || '')).length"
                                                        v-html="truncate(post.data.latest ? post.data.latest.full_text : post.data.full_text, { length: 80 })">
                                                </p>
                                                <p v-else>
                                                    ...
                                                </p>
                                            </template>
                                        </div>

                                        <div class="d-flex justify-content-between align-items-start" v-else-if="accountsById[post.account_id].type === 'instagram.direct'">
                                            <template
                                                v-if="post.type === 'message'">
                                                <p
                                                    v-html="truncate(post.data.latest ? post.data.latest.text || '' : post.data.text || '', { length: 80 })"
                                                    v-if="(post.data.latest ? post.data.latest.text || '' : (post.data.text || '')).length">
                                                </p>
                                                <p v-else>
                                                    ...
                                                </p>
                                            </template>
                                            <template
                                                v-else>
                                                <p
                                                    v-if="(post.data.latest ? post.data.latest.text || '' : (post.data.text || '')).length"
                                                    v-html="truncate(post.data.latest ? post.data.latest.text || '' : post.data.text || '', { length: 80 })">
                                                </p>
                                                <p v-else v-html="truncate(post.data.caption ? post.data.caption.text || '' : '...', { length: 80 })">
                                                </p>
                                            </template>
                                        </div>

                                        <div class="d-flex justify-content-between align-items-start" v-else-if="accountsById[post.account_id].type === 'instagram.api'">
                                            <template
                                                v-if="post.type === 'message'">
                                                <p
                                                    v-html="truncate(post.data.latest && post.data.latest.message ? post.data.latest.message || '' : post.data.message || '', { length: 80 })"
                                                    v-if="(post.data.latest ? post.data.latest.message || '' : (post.data.message || '')).length">
                                                </p>
                                                <p v-else>
                                                    ...
                                                </p>
                                            </template>
                                            <template>
                                                <p
                                                    v-if="(post.data.latest ? post.data.latest.text || '' : (post.data.text || '')).length"
                                                    v-html="truncate(post.data.latest ? post.data.latest.text || '' : post.data.text || '', { length: 80 })">
                                                </p>
                                            </template>
                                        </div>

                                        <div class="d-flex justify-content-between align-items-start" v-else>
                                            <p>
                                                <template v-if="(post.data.latest ? post.data.latest.message : (post.data.message || '')).length">
                                                    {{ truncate(post.data.latest ? post.data.latest.message : post.data.message, { length: 80 }) }}
                                                </template>
                                                <template v-else>
                                                    ...
                                                </template>
                                            </p>
                                        </div>

                                        <!-- options -->
                                        <div class="post-options d-flex align-items-end">
                                            <button class="btn btn-sm btn-light border-secondary" type="button" title="Delete"
                                                    @click.stop.prevent="deletePost(post.id)" v-if="!deletingPostId" v-tooltip>
                                                <i class="ph ph-trash ph-md"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div v-if="(post.type === 'tweet'||post.type === 'post') && post.data.from_search">
                                        <small :title="post.data.keyword" class="small-5 badge badge-secondary badge-pill">
                                            {{ truncate(post.data.keyword, 20) }}
                                        </small>
                                    </div>
                                </div>

                                <div class="post flex-column align-items-start border-secondary border-top p-3 cursor-pointer" v-if="posts.data && posts.next_page_url">
                                    <button class="btn btn-outline-light btn-block active text-center" @click="loadPosts(posts.current_page + 1)" :disabled="loadingPosts">
                                        <span v-if="loadingPosts">
                                            Loading
                                        </span>
                                        <span v-else>
                                            Load More
                                        </span>
                                    </button>
                                </div>
                                <div class="post list-group-item-action flex-column align-items-start p-3" v-else-if="loadingPosts">
                                    Loading...
                                </div>
                                <div v-if="posts.data && posts.data.length === 0">
                                    <div class="text-center mt-3 text-muted">
                                        Conversations will appear here
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-8 pl-0">
                            <div class="card border-0 h-100">
                                <div class="card-body h-100 d-flex align-items-center justify-content-center bg-light text-muted" style="min-height: 400px;"
                                     v-if="!selectedPost">
                                    <h3 v-if="posts.data && posts.data.length">
                                        Select a conversation
                                    </h3>
                                    <span v-else>
                                        Nothing here at the moment
                                    </span>
                                </div>
                                <div class="card-body bg-light p-0 conversation-container h-100" v-else>
                                    <div class="m-4"
                                            v-if="updatedPosts.indexOf(selectedPost.id) > -1">
                                        <div class="card border border-primary">
                                            <div class="card-body">
                                                <h5 class="card-title">Thread updated</h5>
                                                <p class="card-text">This thread has been updated. Reload it to see updated thread.</p>
                                                <button class="btn btn-outline-primary"
                                                    @click="reloadPost">
                                                    <i class="ph ph-repeat"></i> Reload
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <feed-post ref="rootFeedPost" :get-user="getUser" :feed-has-team="!!(this.team && this.team.id)" :show-image="true" :is-root="true" :post="selectedPost" :account="currentAccount" :key="selectedPost.id" :event-handler="handleFeedPostEvent" />
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <!-- modal for config -->
            <div ref="feedConfigureModal" class="modal text-left" role="dialog">
                <div class="modal-dialog modal-dialog-slideout bottom modal-lg">
                    <div class="modal-content">
                        <div class="modal-header d-flex align-items-center">
                            <input title="Feed name" type="text" class="modal-title input-seamless form-control h3" v-model="settings.name" :placeholder="name"/>
                            <button type="button" class="close-button" data-dismiss="modal"><i class="ph ph-x ph-md"></i></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-12 col-md-6 order-md-2">
                                    <div class="pl-4" v-tour:feedvue_acc_or_team.left.force="'You can either attach accounts or a team to your Feed'">
                                        <div
                                            v-tour:feedvue_cfg_acc.left.force="'The accounts attached to this Feed are here'"
                                                :class="{'opacity-40': settings.isTeamFeed}" @click="settings.isTeamFeed = false">
                                            <h4>
                                                Accounts
                                            </h4>
                                            <div class="list-group list-group-flush">
                                                <div class="list-group-item d-flex justify-content-between align-items-center border-light"
                                                     v-for="(acc, index) in settings.accounts" :key="index">
                                                     <div>
                                                         <img class="network-icon" :src="getIconForAccount(acc.type,'circular')" :alt="acc.name">
                                                        <span>
                                                            {{ acc.name }}
                                                        </span>
                                                     </div>
                                                    <span>
                                                        <i class="ph ph-trash ph-md text-muted cursor-pointer"
                                                           v-tooltip="'Remove'" @click="settings.accounts.splice(index, 1)" v-if="!settings.isTeamFeed"></i>
                                                    </span>
                                                </div>
                                                <div class="list-group-item border-light select2-transparent px-0" v-show="!settings.isTeamFeed">
                                                    <select class="form-control input_accounts" id="input_accounts" title="Accounts"></select>
                                                </div>
                                            </div>
                                        </div>
                                        <hr/>
                                        <div
                                            v-tour:feedvue_cfg_team.left.force="'If you want this feed to be shared and attached with a team, select the team here. <br/><br/>The accounts from the team will automatically be attached.'"
                                                :class="{'opacity-40': !settings.isTeamFeed}"  @click="settings.isTeamFeed = true">
                                            <h4>or select a team <i class="ph ph-check text-muted small-1" v-if="settings.isTeamFeed"></i> </h4>
                                            <select class="form-control input_team" id="input_team" title="Team">
                                                <option v-if="team && team.id" :value="team.id" selected>{{ team.name }}</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 col-md-6 order-md-1">
                                    <h4>Notifications</h4>
                                    <div class="form-group" v-tour:feed_cfg_noti.right.force="'When there are new unread items in this feed, you can select one or more members to be notified via email'">
                                        <label for="input_users">
                                            Select users to notify of new items via email
                                        </label>
                                        <select multiple class="form-control input_users" id="input_users" v-model="settings.notify" v-selectpicker>
                                            <option v-for="(user,i) in settings.users" :key="i" :value="user.id">{{ user.name }}</option>
                                        </select>
                                    </div>
                                    <div class="form-group"
                                         v-tour:feedvue_cfg_noti_freq.right.force="'We suggest Near Instant notifications if you expect low number of new items. For high number of new items, select Daily for once-a-day notifications.'"
                                         v-if="settings.notify && settings.notify.length">
                                        <label for="input_users">
                                            Notification Frequency
                                        </label>
                                        <select title="Frequency" class="form-control" v-model="settings.notifyFrequency">
                                            <option value="near_instant">
                                                Near Instant
                                            </option>
                                            <option value="daily">
                                                Daily
                                            </option>
                                        </select>
                                    </div>
                                    <br />
                                    <h4>Catch Items</h4>
                                    <div class="form-group" v-tour:feed_cfg_catch_itms.right.force="'You can set to catch only specific type of items such as messages, comments, or so on'">
                                        <label for="catch_items">
                                            Populate this feed with specific items
                                        </label>
                                        <select multiple class="form-control catch_items" id="catch_items" v-model="settings.catchItems" v-selectpicker>
                                            <option v-for="(item,i) in availableItemsToCatch" :key="i" :value="item">{{ item }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <!-- <div class="row" v-if="settings.accounts.some(a=>a.type === 'twitter.profile')">
                                <div class="col">

                                    <hr />
                                    <h4>Monitor Twitter Keywords</h4>
                                    <div class="list-group list-group-flush">
                                        <div class="list-group-item d-flex align-items-center border-light"
                                             v-for="(monitorInfo, i) in settings.twitterKeywords" :key="i">
                                            <div class="mr-4">
                                                <i class="ph ph-trash ph-md cursor-pointer"
                                                   @click="settings.twitterKeywords.splice(i, 1)" v-tooltip="'Remove'"></i>
                                            </div>
                                            <div class="row w-100">
                                                <div class="form-group col-md-6" v-tour:feedvue_cfg_twtr_keyword_inp.right.force="'You can enter a simple keyword, a #hashtag, or any search operator supported by Twitter'">
                                                    <label :for="'twQ' + i">Track keyword</label>
                                                    <input required type="text" class="input-transparent form-control" title="Twitter Search Query" placeholder="from:socialbuapp"
                                                           v-model="monitorInfo.keyword" :id="'twQ' + i"/>
                                                </div>
                                                <div class="form-group col-md-6">
                                                    <label :for="'twQA' + i">using account</label>
                                                    <select required class="form-control"
                                                            v-model="monitorInfo.account" :id="'twQA' + i" v-selectpicker>
                                                        <option :value="a.id" v-for="(a,i) in settings.accounts" :key="i" v-if="a.type === 'twitter.profile'">
                                                            {{ a.name }}
                                                        </option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <button class="btn btn-outline-secondary btn-sm border border-secondary text-muted"
                                                    v-tour:feedvue_cfg_twtr_add_keyword.right.force="'You can track twitter search keywords. The matching tweets will be collected and added to this feed.'"
                                                    @click="settings.twitterKeywords.push({keyword: '', account: null})">
                                                <i class="ph-bold ph-plus"></i> Add Keyword
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div> -->


                            <div class="row" v-if="settings.accounts.some(a=>a.type === 'instagram.direct')">
                                <div class="col">

                                    <hr />
                                    <h4>Monitor Instagram Hashtags</h4>
                                    <div class="list-group list-group-flush">
                                        <div class="list-group-item d-flex align-items-center border-light"
                                             v-for="(monitorInfo, i) in settings.instagramKeywords" :key="i">
                                            <div class="mr-4">
                                                <i class="ph ph-trash ph-md cursor-pointer"
                                                   @click="settings.instagramKeywords.splice(i, 1)" v-tooltip="'Remove'"></i>
                                            </div>
                                            <div class="row w-100">
                                                <div class="form-group col-md-6" v-tour:feedvue_cfg_insta_keyword_inp.right.force="'Enter hash tags without the # (hash-tag) character. For example, enter <code>socialbu</code> to track the posts containing (hashtag) #socialbu'">
                                                    <label :for="'igQ' + i">Track hashtag</label>
                                                    <input required type="text" class="input-transparent form-control" title="Instagram Hashtag without #" placeholder="socialbu"
                                                           v-model="monitorInfo.keyword" :id="'igQ' + i"/>
                                                </div>
                                                <div class="form-group col-md-6">
                                                    <label :for="'igQA' + i">using account</label>
                                                    <select required class="form-control"
                                                            v-model="monitorInfo.account" :id="'igQA' + i" v-selectpicker>
                                                        <option :value="a.id" v-for="(a,i) in settings.accounts" :key="i" v-if="a.type === 'instagram.direct'">
                                                            {{ a.name }}
                                                        </option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <button class="btn btn-outline-secondary btn-sm border border-secondary text-muted"
                                                    v-tour:feedvue_cfg_insta_add_keyword.right.force="'You can track Instagram hash-tags. The matching posts will be collected and added to this feed.'"
                                                    @click="settings.instagramKeywords.push({keyword: '', account: null})">
                                                <i class="ph-bold ph-plus"></i> Add Hashtag
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="modal-footer d-flex justify-content-between">
                            <div>
                                <button type="button" class="btn btn-danger" @click="deleteFeed">Delete</button>
                            </div>
                            <div>
                                <button type="button" class="btn btn-light" data-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-primary" @click="saveFeed">Save</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- <div ref="feedConfigureModal" class="modal text-left" role="dialog">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <input title="Feed name" type="text" class="modal-title input-seamless form-control h3" v-model="settings.name" :placeholder="name"/>
                            <button type="button" class="close" data-dismiss="modal">&times;</button>
                        </div>
                        <div class="modal-body">

                            <div class="form-group" v-if="settings.isTeamFeed">
                                <label for="input_team">
                                    Team
                                </label>
                                <select class="form-control input_team" name="team_id" id="input_team" title="Team">
                                    <option v-if="team && team.id" :value="team.id" selected>{{ team.name }}</option>
                                </select>
                            </div>

                            <div class="form-group" v-else>
                                <label for="input_accounts">
                                    Accounts
                                </label>
                                <select multiple class="form-control input_accounts" name="accounts[]" id="input_accounts" title="Accounts">
                                    <option v-if="!team || !team.id" v-for="acc in accounts" :value="acc.id" selected>
                                        {{ acc.name }}
                                    </option>
                                </select>
                            </div>

                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" :checked="settings.isTeamFeed" @click.prevent="()=>{closeConfigureModal();settings.isTeamFeed = !settings.isTeamFeed;openConfigureModal()}" id="input_is_team_feed">
                                <label class="form-check-label" for="input_is_team_feed">
                                    Is Team Feed
                                </label>
                            </div>

                        </div>
                        <div class="modal-footer d-flex justify-content-between">
                            <div>
                                <button type="button" class="btn btn-danger" @click="deleteFeed">Delete</button>
                            </div>
                            <div>
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-primary" @click="saveFeed">Save</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            -->
        </div>
    </div>
</template>

<script>
import _ from "lodash";
import VuePerfectScrollbar from "vue-perfect-scrollbar";
import {axios, alertify, getEcho, appConfig, axiosErrorHandler, getIconForAccount} from "../../components";
import FeedPost from "./feed/FeedPost";
let channel = null,
    searchTimeout;
export default {
    name: "feed",
    components: { FeedPost, VuePerfectScrollbar },
    data() {
        return {
            id: null,
            name: null,
            accounts: [],
            users: [],
            team: {
                id: null,
                name: null
            },

            settings: {
                name: "",
                isTeamFeed: false,
                accounts: [],
                users: [],
                notify: [],
                notifyFrequency: "daily",
                teamId: null,
                twitterKeywords: [], // {account, query}
                instagramKeywords: [], // {account, query}
                catchItems: []
            },

            config: {},

            posts: {},
            selectedPostId: null,
            loadingPosts: false,

            onlineUsers: [],
            hasNewData: false,
            updatedPosts: [],

            unread: {},

            search: "",

            deletingPostId: null,

            loaded: false,
            error: null
        };
    },
    computed: {
        selectedPost() {
            if (this.selectedPostId !== null) {
                return this.posts.data && this.posts.data.find(post => post.id === this.selectedPostId);
            }
            return null;
        },
        currentAccount() {
            if (this.selectedPost !== null) {
                return this.getAccountForPost(this.selectedPost);
            }
            return null;
        },
        accountsById() {
            const obj = {};
            this.accounts.forEach(account => (obj[account.id] = account));
            return obj;
        },
        isTeamFeed_computed() {
            // for watcher only
            return this.settings.isTeamFeed;
        },
        availableItemsToCatch() {
            // get all unique networks
            const networks = this.settings.accounts.map(a => a.type.split(".")[0]).filter((v, i, a) => a.indexOf(v) === i);
            const ITEMS = {
                twitter: ["messages", "mentions"],
                facebook: ["messages", "comments", "posts", "ratings"],
                instagram: ["messages", "comments"]
            };
            const all = [];
            networks.forEach(n => {
                ITEMS[n] &&
                    ITEMS[n].forEach(itm => {
                        if (!all.includes(itm)) {
                            all.push(itm);
                        }
                    });
            });
            return all;
        }
    },
    watch: {
        selectedPostId(v) {
            if (v) document.location.hash = "post=" + v;
            else document.location.hash = "";

            channel &&
                channel.whisper("loadedPost", {
                    userId: appConfig.userId,
                    postId: v
                });
        },
        isTeamFeed_computed(v) {
            if (!v) {
                $(".input_users,.input_team")
                    .val(null)
                    .trigger("change");
                this.settings.teamId = null;
                if (this.team.id) {
                    // team was set, so unset all team accounts
                    this.settings.accounts = [];
                } else {
                    this.settings.accounts = this.accounts.map(a => {
                        return {
                            id: a.id,
                            name: a.name,
                            type: a.type
                        };
                    });
                }
                this.settings.users = this.users.map(a => {
                    return {
                        id: a.id,
                        name: a.name
                    };
                });
            }
        },
        search(v) {
            if (searchTimeout) {
                clearTimeout(searchTimeout);
                searchTimeout = null;
            }
            searchTimeout = setTimeout(() => {
                this.selectedPostId = null;
                this.loadPosts(1, true);
            }, 500);
        }
    },
    methods: {
        getIconForAccount,
        setAltImage(event) {
            event.target.src = "/images/no-image.png";
        },
        getTimestamp(post) {
            let account = this.accountsById[post.account_id];
            if (account.type === "twitter.profile") {
                if (post.type === "message") {
                    return this.$momentUTC(
                        Number(post.data.latest ? post.data.latest.created_timestamp : post.data.created_timestamp),
                        "x"
                    );
                } else {
                    return this.$momentUTC(
                        post.data.latest ? post.data.latest.created_at : post.data.created_at,
                        "ddd MMM DD HH:mm:ss ZZ Y"
                    );
                }
            } else if (account.type === "facebook.page") {
                let timestamp =
                    post.data.latest && post.data.latest.created_time
                        ? post.data.latest.created_time
                        : post.data.created_time;
                if (timestamp && !timestamp.date) {
                    if (isNaN(Number(timestamp))) {
                        return this.$momentUTC(timestamp);
                    } else {
                        return this.$momentUTC(Number(timestamp), "X");
                    }
                } else return this.$momentUTC(timestamp.date);
            } else if (account.type === "instagram.api") {
                let timestamp = post.data.latest && post.data.latest.timestamp ? post.data.latest.timestamp : post.data.timestamp;
                if (timestamp && !timestamp.date) {
                    if (isNaN(Number(timestamp))) {
                        return this.$momentUTC(timestamp);
                    } else {
                        if( String(timestamp).length > 10){ //it is in milliseconds, convert to seconds
                            return this.$momentUTC(Number(timestamp / 1000), "X");
                        } else {
                            return this.$momentUTC(Number(timestamp), "X");
                        }
                    }
                } else return this.$momentUTC(timestamp.date);
            }else if (account.type === "instagram.direct" ) {
                if (post.data.latest && post.data.latest.timestamp) {
                    const unixM = this.$momentUTC(post.data.latest.timestamp, "X");

                    if (Number(unixM.format("X")) === Number(post.data.latest.timestamp)) return unixM;
                    else if (String(post.data.latest.timestamp).length > 14)
                        return this.$momentUTC(post.data.latest.timestamp / 1000, "x");
                    else return this.$momentUTC(post.data.latest.timestamp);
                } else if (typeof post.created_at === "string") return this.$momentUTC(post.created_at);
                else return this.$momentUTC(post.created_at, "X");
            }
        },
        getAccountForPost(post) {
            return this.accounts.find(account => account.id === post.account_id);
        },
        getUser(id) {
            let user = this.users.find(u => Number(u.id) === Number(id));
            if (!user) {
                return {
                    name: "A User",
                    image: "/images/no-image.png"
                };
            }
            return user;
        },
        truncate: _.truncate,
        closeConfigureModal() {
            $(this.$refs.feedConfigureModal).modal("hide");
        },
        openConfigureModal() {
            this.settings.name = this.name;
            this.settings.accounts = this.accounts.map(a => {
                return {
                    id: a.id,
                    name: a.name,
                    type: a.type
                };
            });
            this.settings.users = this.users.map(a => {
                return {
                    id: a.id,
                    name: a.name
                };
            });
            this.settings.isTeamFeed = !!(this.team && this.team.id);
            this.settings.teamId = this.team.id;
            this.settings.twitterKeywords = [...(this.config.twitterKeywords || [])];
            this.settings.instagramKeywords = [...(this.config.instagramKeywords || [])];
            this.settings.notify = [...this.config.notify];
            if (this.config.notifyFrequency) {
                this.settings.notifyFrequency = this.config.notifyFrequency;
            }

            this.settings.catchItems = [...(this.config.catchItems || [])];

            const _this = this;
            setTimeout(() => {
                const $modal = $(this.$refs.feedConfigureModal);
                // open the configure popup so user can setup the feed
                $modal
                    .off("shown.bs.modal")
                    .on("shown.bs.modal", () => {
                        $modal
                            .find(".input_team")
                            .select2({
                                dropdownParent: $modal,
                                placeholder: "Type to select",
                                ajax: {
                                    url: "/app/json/search/teams",
                                    dataType: "json",
                                    delay: 250,
                                    data: function(params) {
                                        return {
                                            q: params.term // search term
                                        };
                                    },
                                    processResults: function(data, params) {
                                        params.page = params.page || 1;

                                        data.items.forEach(function(item) {
                                            item.text = item.name;
                                        });

                                        return {
                                            results: data.items
                                        };
                                    },
                                    cache: true
                                },
                                minimumInputLength: 1
                            })
                            .off("select2:selecting")
                            .on("select2:selecting", function(d) {
                                const team = d.params.args.data;
                                _this.settings.users = team.members;
                                _this.settings.accounts = team.accounts.filter(
                                    a => ["facebook.page", "twitter.profile", "instagram.direct", "instagram.api"].indexOf(a.type) > -1
                                );
                                _this.settings.teamId = team.id;
                            });
                        $modal
                            .find(".input_accounts")
                            .select2({
                                dropdownParent: $modal,
                                placeholder: "Add account",
                                ajax: {
                                    url: "/app/json/search/accounts",
                                    dataType: "json",
                                    delay: 250,
                                    data: function(params) {
                                        return {
                                            q: params.term // search term
                                        };
                                    },
                                    processResults: function(data, params) {
                                        params.page = params.page || 1;
                                        const items = [];
                                        data.items.forEach(function(item, index) {
                                            if (
                                                ["facebook.page", "twitter.profile", "instagram.direct", "instagram.api"].indexOf(
                                                    item.type
                                                ) > -1 &&
                                                !_this.settings.accounts.find(a => Number(a.id) === Number(item.id))
                                            ) {
                                                item.text = item.name;
                                                items.push(item);
                                            }
                                        });

                                        return {
                                            results: items
                                        };
                                    },
                                    cache: true
                                },
                                minimumInputLength: 1,
                                // Custom template for displaying items
                                templateResult: function(item) {
                                    if (!item.type) {
                                        return item.name; // Display placeholder for empty results
                                    }
                                    const imageUrl = getIconForAccount(item.type, "circular");
                                    
                                    return $(
                                        `<div class="select2-item d-flex align-items-center">
                                            <img src="${imageUrl}" alt="${item.name}" class="rounded-circle position-relative network-icon mr-2" style="width: 22px; height: 22px;" />
                                            <span>${item.name}</span>
                                        </div>`
                                    );
                                },

                               
                            })
                            .off("select2:selecting")
                            .on("select2:selecting", function(d) {
                                const a = d.params.args.data;
                                _this.settings.accounts.push({
                                    id: Number(a.id),
                                    name: a.name,
                                    type: a.type
                                });
                                setTimeout(() => {
                                    $(this)
                                        .select2("close")
                                        .val(null);
                                }, 500);
                                return false;
                            });
                    })
                    .off("hidden.bs.modal")
                    .on("hidden.bs.modal", () => {
                        try {
                            if ($modal.hasClass("select2-hidden-accessible")) {
                                $modal.find(".input_team").select2("destroy");
                                $modal.find(".input_accounts").select2("destroy");
                            }
                        } catch (e) {}
                    })
                    .modal({ backdrop: "static", keyboard: false }, "show");
            }, 20);
            return false;
        },
        async saveFeed() {
            const data = {
                name: this.settings.name
            };
            if (this.settings.isTeamFeed) data.team = this.settings.teamId;
            else data.accounts = this.settings.accounts.map(a => a.id);
            if (this.settings.notify.length) {
                data.notify = this.settings.notify;
            }
            if (this.settings.notifyFrequency) {
                data.notifyFrequency = this.settings.notifyFrequency;
            }
            if (
                this.settings.twitterKeywords.length &&
                this.settings.accounts.some(a => a.type === "twitter.profile")
            ) {
                data.twitterKeywords = this.settings.twitterKeywords;
            }
            if (
                this.settings.instagramKeywords.length &&
                this.settings.accounts.some(a => a.type === "instagram.direct")
            ) {
                data.instagramKeywords = this.settings.instagramKeywords;
            }

            if(this.settings.catchItems && this.settings.catchItems.length){
                data.catchItems = this.settings.catchItems;
            }

            try {
                await axios.patch("/app/respond/feeds/" + this.id, data);
                document.location.reload();
            } catch (e) {
                (() => {
                    if (e.response && e.response.data) {
                        const errors = e.response.data.errors;
                        if (errors) {
                            Object.keys(errors).forEach(k => {
                                alertify.error(errors[k].join(" \n"));
                            });
                            return;
                        }
                        if (e.response.data && e.response.data.message) {
                            return alertify.error(e.response.data.message);
                        }
                    }
                    alertify.error(e.message || "An error occurred.");
                })();
            }
        },
        deleteFeed() {
            alertify.delete("Are you sure you want to delete this feed?", async () => {
                try {
                    await axios.delete("/app/respond/feeds/" + this.id);
                    document.location.href = "/app/respond/feeds";
                } catch (e) {
                    (() => {
                        if (e.response && e.response.data) {
                            const errors = e.response.data.errors;
                            if (errors) {
                                Object.keys(errors).forEach(k => {
                                    alertify.error(errors[k].join(" \n"));
                                });
                                return;
                            }
                            if (e.response.data && e.response.data.message) {
                                return alertify.error(e.response.data.message);
                            }
                        }
                        alertify.error(e.message || "An error occurred.");
                    })();
                }
            });
        },
        async initialize(id) {
            this.id = id;

            let postToSelect = null;
            const urlHashInfo = {};
            if (document.location.hash) {
                document.location.hash
                    .substring(1)
                    .split("&")
                    .forEach(v => {
                        const val = v.split("=");
                        urlHashInfo[val[0]] = val[1];
                    });
            }

            if (urlHashInfo.post) {
                this.selectedPostId = Number(urlHashInfo.post);
            }
            this.loadPosts();
            try {
                const { data } = await axios.get("/app/respond/feeds/" + this.id + "?json=true");

                this.accounts = data.accounts;
                this.users = data.users;
                _.extend(this.config, data.config);
                _.extend(this.team, data.team);

                this.name = data.name;
                this.loaded = true;
                $("#feed_configure_btn")
                    .off("click.configure")
                    .on("click.configure", this.openConfigureModal);
                if (this.accounts.length === 0) {
                    this.$nextTick(this.openConfigureModal);
                }

                this.connectToEcho();
            } catch (e) {
                this.error = axiosErrorHandler(e, true);
            }
        },
        async connectToEcho() {
            // connect to channel
            try {
                const _echo = await getEcho();
                channel = _echo.join("feed." + this.id);
            } catch (e) {
                throw e;
            }
            const userObj = u => {
                u.status = null;
                u.postId = null;
                return u;
            };
            channel
                .here(users => {
                    this.onlineUsers = users.filter(u => u.id !== appConfig.userId).map(userObj);
                    if (this.selectedPostId) {
                        channel.whisper("loadedPost", {
                            userId: appConfig.userId,
                            postId: this.selectedPostId
                        });
                    }
                    channel.listen("NewFeedPost", d => {
                        getEcho().then(Echo => {
                            if (d.socketId === Echo.socketId()) return;
                            console.log(d);
                            if (!this.posts) return;
                            if (!d.rootId) {
                                // is new thread probably
                                if (
                                    !this.posts.data.find(fp => {
                                        return Number(fp.id) === d.id;
                                    })
                                ) {
                                    this.hasNewData = true; // has new post, show button for reload
                                }
                            } else {
                                // add new indicator for that post
                                if (
                                    this.posts.data.find(fp => {
                                        return Number(fp.id) === d.rootId;
                                    })
                                ) {
                                    this.updatedPosts.push(d.rootId);
                                }
                            }
                        });
                    });
                    const typingTimers = {};
                    channel.listenForWhisper("typing", d => {
                        if (this.selectedPostId !== d.postId) return;
                        this.onlineUsers = this.onlineUsers.map(u => {
                            if (u.id === d.userId) {
                                u.status = "typing";
                                u.postId = d.postId;
                                this.$refs.rootFeedPost.typingIndicator = u.name + " is typing...";
                            }
                            return u;
                        });
                        if (typingTimers[d.userId]) {
                            clearTimeout(typingTimers[d.userId]);
                            typingTimers[d.userId] = null;
                        }
                        typingTimers[d.userId] = setTimeout(() => {
                            this.onlineUsers = this.onlineUsers.map(u => {
                                if (u.id === d.userId) {
                                    u.status = null;
                                    this.$refs.rootFeedPost.typingIndicator = null;
                                }
                                return u;
                            });
                        }, 5000);
                    });
                    channel.listenForWhisper("loadedPost", d => {
                        this.onlineUsers = this.onlineUsers.map(u => {
                            if (u.id === d.userId) {
                                u.postId = d.postId;
                            }
                            return u;
                        });
                    });
                })
                .joining(u => {
                    this.onlineUsers.push(userObj(u));
                    // inform newcomers of my post
                    channel.whisper("loadedPost", {
                        userId: appConfig.userId,
                        postId: this.selectedPostId
                    });
                })
                .leaving(leavingUser => {
                    this.onlineUsers = this.onlineUsers.filter(u => {
                        return u.id !== leavingUser.id && u.id !== appConfig.userId;
                    });
                });
        },
        reloadData() {
            this.search = "";
            this.hasNewData = false;
            this.loadPosts(1, true);
        },
        reloadPost() {
            const currPostId = this.selectedPostId;
            const index = this.updatedPosts.indexOf(currPostId);
            if (index === -1) return;
            this.selectedPostId = null;
            this.updatedPosts.splice(index, 1);
            this.$nextTick(() => {
                this.selectedPostId = currPostId;
            });
        },
        async loadPosts(page = 1, emptyPosts = false) {
            this.loadingPosts = true;
            if (emptyPosts && this.posts.data) this.posts.data = [];

            try {
                const res = await axios.get(
                    "/app/respond/feeds/" +
                        this.id +
                        "/posts?page=" +
                        page +
                        (this.selectedPostId ? "&include_post=" + this.selectedPostId : "") +
                        (this.search.length ? "&q=" + this.search : "")
                );
                const resData = res.data;
                const data = resData.posts;
                const posts = this.posts.data ? this.posts.data : []; // current posts

                data.data = [...posts, ...data.data]; // combine newly loaded posts
                if (this.selectedPostId) {
                    if (
                        !data.data.some(p => {
                            return p.id === this.selectedPostId;
                        }) &&
                        resData.include_post
                    ) {
                        data.data.unshift(resData.include_post);
                    }
                }
                this.posts = data;
                this.unread = resData.unread;
            } catch (e) {
                (() => {
                    if (e.response && e.response.data) {
                        const errors = e.response.data.errors;
                        if (errors) {
                            Object.keys(errors).forEach(k => {
                                alertify.error(errors[k].join(" \n"));
                            });
                            return;
                        }
                        if (e.response.data && e.response.data.message) {
                            return alertify.error(e.response.data.message);
                        }
                    }
                    alertify.error(e.message || "An error occurred.");
                })();
                alertify.error(e.message || "Unable to load posts");
            }
            this.loadingPosts = false;
        },
        markRead(postId) {
            // mark read (in UI), this method is called by FeedPost root post
            const oldPosts = [...this.posts.data];
            const unread = { ...this.unread };
            oldPosts.some((post, i) => {
                if (post.id === postId) {
                    // mark this as read
                    oldPosts[i].read = true;
                    unread[postId] = 0;
                    this.posts.data = oldPosts;
                    this.unread = unread;
                    return true;
                }
            });
        },
        deletePost(postId) {
            if (this.deletingPostId !== null) return;
            alertify.delete("Are you sure?", async () => {
                this.deletingPostId = postId;
                try {
                    const res = await axios.delete("/app/respond/feeds/" + this.id + "/posts/" + postId);
                    if (this.selectedPostId === postId) {
                        // un-select this post if this was selected
                        this.selectedPostId = null;
                    }
                    // remove post from state
                    this.posts.data = this.posts.data.filter(post => post.id !== postId);
                } catch (e) {
                    (() => {
                        if (e.response && e.response.data) {
                            const errors = e.response.data.errors;
                            if (errors) {
                                Object.keys(errors).forEach(k => {
                                    alertify.error(errors[k].join(" \n"));
                                });
                                return;
                            }
                            if (e.response.data && e.response.data.message) {
                                return alertify.error(e.response.data.message);
                            }
                        }
                        alertify.error(e.message || "An error occurred.");
                    })();
                }
                this.deletingPostId = null;
            });
        },
        handleFeedPostEvent(type, data) {
            if (!channel) return;
            if (type === "typing") {
                channel.whisper("typing", data);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
@import "../../../sass/variables";
.posts-container {
    min-height: 100%;
    max-height: calc(100vh - 270px);
    overflow-y: auto;
    .post {
        border-right: 3px solid transparent !important;
        .post-img {
            position: relative;
            img {
                height: auto;
            }
            .network-type {
                position: absolute;
                top: 30px;
                left: 30px;
            }
        }
        .post-text {
            max-height: 72px;
            overflow: hidden;
        }
        &.deleting {
            opacity: 0.5;
        }
        &.read {
            background-color: #e9ecef33;
        }
        &.active {
            border-right: 3px solid darken($blue, 10%) !important;
        }
        .post-options {
            float: right;
            opacity: 0;
            transition: all 0.2s linear;
        }
        &:hover {
            .post-options {
                opacity: 1;
            }
        }
    }
}
.live-user {
    i {
        position: relative;
        top: 12px;
        right: 12px;
    }
}
</style>
