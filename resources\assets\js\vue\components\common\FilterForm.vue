<template>
    <div>
        <div>
            <div class="form-group"
                 v-if="fields.includes('start')">
                <label for="startDate">Start Date</label>
                <input
                    title="Date"
                    type="date"
                    class="form-control"
                    placeholder="Select a date..."
                    v-model="filter.start"
                    @change="validateDate"
                />
                <small class="form-text text-muted">

                </small>
            </div>
            <div class="form-group"
                 v-if="fields.includes('end')">
                <label for="startDate">End Date</label>
                <input
                    title="Date"
                    type="date"
                    class="form-control"
                    placeholder="Select a date..."
                    v-model="filter.end"
                    @change="validateDate"
                />
                <small class="form-text text-muted">

                </small>
            </div>
            <div class="form-group"
                 v-if="fields.includes('network')">
                <label for="filterNetwork">Social Network</label>
                <select
                    class="w-100 form-control"
                    id="filterNetwork"
                    v-model="filter.network"
                    title="..."
                    v-selectpicker
                >
                    <option value="">
                        ...
                    </option>
                    <option value="facebook.page">
                        Facebook Page
                    </option>
                    <option value="twitter.profile">
                        Twitter Account
                    </option>
                    <option value="instagram.api">
                        Instagram Business
                    </option>
                    <option value="linkedin.org">
                        LinkedIn Organization
                    </option>
                    <option value="linkedin.brand">
                        LinkedIn Brand
                    </option>
                    <option value="google.location">
                        Google Business Profile
                    </option>
                    <option value="pinterest.profile">
                        Pinterest Profile
                    </option>
                </select>
                <small class="form-text text-muted"> Filter data by social network </small>
            </div>
            <div class="form-group"
                 v-if="teams.length && fields.includes('team')">
                <label for="filterTeam">Team</label>
                <select
                    class="w-100 form-control"
                    id="filterTeam"
                    v-model.number="filter.team"
                    title="..."
                    data-live-search="true"
                    v-selectpicker
                >
                    <option value="">
                        ...
                    </option>
                    <option
                        v-for="(team,i) in teams"
                        :value="team.id"
                        :key="i + 'team-field'">
                        {{ team.name }}
                    </option>
                </select>
                <small class="form-text text-muted"> Filter data by team </small>
            </div>
            <div class="form-group"
                 v-if="fields.includes('accounts')">
                <label for="filterAccount">Accounts</label>
                <select class="w-100" id="filterAccount" multiple v-model.number="filter.accounts" title="..." data-live-search="true" v-selectpicker>
                    <optgroup v-for="(network, i) in filterNetworks" :label='network.title' :key="network.title + '_' + i">
                        <option v-for="account in getAccountsByType(network.type)" :key="network.type + account.id" :data-content="`<img class='border rounded-circle border-white position-relative network-icon' src='${getIconForAccount(account.type, 'circular')}'><span class='p-2'>${account.name.length > 22 ? account.name.slice(0, 22) + '...' : account.name}</span>`" :value="account.id"></option>
                    </optgroup>
                </select>
                <small class="form-text text-muted"> Filter by social accounts </small>
            </div>
        </div>
        <div class="pt-4 mt-4 d-flex justify-content-between">
            <button
                class="btn btn-sm btn-light"
                @click="resetFilter"
            >
                Reset
            </button>
            <button class="btn btn-sm btn-primary" @click="onFilter">
                Apply
            </button>
        </div>
    </div>
</template>

<script>
import { getIconForAccount } from '../../../components'
import { getNetworks } from './../../../data'
export default {
    name: "FilterForm",
    components: {},
    props: {
        filterBy: Object, // filter by object; object just like this.filter
        fields: { // array of filter fields to show
            type: Array,
            default: () => [
                "start",
                "end",
                "team",
                "accounts"
            ]
        },
        filterAccounts: { // for filtering accounts based on any criteria - e.g. by network
            type: Function,
            default: () => true
        },
        accounts: Array, // all accounts
    },
    data() {
        return {
            filter: {
                start: null,
                end: null,
                team: null,
                accounts: [],
                network: null
            },
        networks: []
        };
    },
    computed: {
        teams() {
            const teamsById = {};
            this.accounts
                .filter((a) => a.team)
                .forEach((a) => {
                    teamsById[a.team.id] = a.team;
                });
            return Object.values(teamsById);
        },
        // filter
        filteredAccounts() {
            let accounts = null;
            if (this.filter.team) {
                accounts = this.accounts.filter((a) => a.team && a.team.id === this.filter.team);
            } else {
                accounts = this.accounts.filter((a) => !a.team);
            }
            if (this.filter.network) {
                accounts = accounts.filter((a) => a.type === this.filter.network);
            }
            if(typeof this.filterAccounts === "function"){
                accounts = accounts.filter(this.filterAccounts);
            }
            return accounts;
        },
        filterNetworks(){
            // only return networks that we have in accounts
            return this.networks.filter(n => {
                return this.filteredAccounts.find(a => a.type === n.type);
            });
        },
    },
    watch: {
        "filter.team": function (newVal) {
            // whenever team changes, reset accounts
            this.filter.accounts = [];
        },
        filterBy: {
            handler: function (newVal) {
                this.filter = newVal;
            },
            deep: true,
        },
    },
    methods: {
        getIconForAccount,
        getAccountsByType(type) {        
            return this.filteredAccounts.filter(account => account.type === type);
        },
        resetFilter() {
            this.$emit("reset");
        },
        onFilter() {
            this.$emit("filter", {...this.filter});
        },
        validateDate(){
           if (Date.parse(this.filter.end) < Date.parse(this.filter.start)) {
               alert("End date should be greater than Start date");
               this.filter.end = this.filterBy.end || "";
           }
           if(Date.parse(this.filter.start) > new Date()){
               alert("Start date cannot be greater than today")
               this.filter.start = this.filterBy.start || "";
           }
        },
        
       },
    async mounted() {
        if (this.filterBy) {
            Object.keys(this.filterBy).forEach((n) => {
                this.filter[n] = this.filterBy[n];
            });
        }
        this.networks = await getNetworks(true);
    }
};
</script>

<style></style>
