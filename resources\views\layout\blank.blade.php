
<!DOCTYPE html>
<html lang="en">
<head>
    @include('layout.includes.user.head')
</head>

<body>
@include('layout.includes.common_body')
<div id="fb-root"></div>
<script>
    (function(d, s, id) {
        var js, fjs = d.getElementsByTagName(s)[0];
        if (d.getElementById(id)) return;
        js = d.createElement(s); js.id = id;
        js.src = 'https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v3.1&appId={{ config('services.facebook.client_id') }}&autoLogAppEvents=1';
        fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));
</script>
<div id="app">

    <div class="container-fluid page-content">
        <div class="row">
            <div class="col">
                @yield('content')
            </div>
        </div>

    </div>

</div>
@include('layout.includes.user.footer_html')
@stack('footer_html')
</body>
</html>
