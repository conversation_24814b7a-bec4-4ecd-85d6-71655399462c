<?php

namespace App\Console\Commands;

use App\FeedPost;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;

class DeleteOldUnreadFeedPosts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'feed_posts:delete_unread';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete old unread feed posts';

    private $chunksProcessed = 0;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        // delete feed posts older than 1 months and still unread to save our db space
        FeedPost::with('feed')->where('created_at', '<', now()->subMonths(1))->where('read', false)->where('user_id', null)->chunk(100, function($feedPosts){
            /** @var $feedPosts FeedPost[]|Collection */

            foreach($feedPosts as $feedPost){
                $feed = $feedPost->feed;
                if($feed)
                    $feed->deletePost($feedPost);
            }

            $this->line('deleted ' . $feedPosts->count() . ' feed posts');

            ++$this->chunksProcessed;

            if($this->chunksProcessed >= 1000){
                // enough deletion for now
                return false;
            }

        });
    }
}
