const {generateAsync, generate} = require("stability-client");
require("fix-esm").register();
const Replicate = require("replicate-js").default;
const fetch = require("node-fetch").default;

const PORT = 3603;

const express = require("express");
const bodyParser = require("body-parser");
const log = require("./logger")("text2img api");

const app = express();

// parse application/x-www-form-urlencoded
app.use(bodyParser.urlencoded({extended: false}));

// parse application/json
app.use(bodyParser.json());

// log time
app.use((req, res, next) => {
    log(`${req.method} ${req.originalUrl} [STARTED]`);
    const start = process.hrtime();
    const getDurationInMilliseconds = start => {
        const NS_PER_SEC = 1e9;
        const NS_TO_MS = 1e6;
        const diff = process.hrtime(start);

        return (diff[0] * NS_PER_SEC + diff[1]) / NS_TO_MS;
    };

    res.on("finish", () => {
        const durationInMilliseconds = getDurationInMilliseconds(start);
        log(`${req.method} ${req.originalUrl} [FINISHED] ${durationInMilliseconds.toLocaleString()} ms`);
    });

    next();
});

// helper for errors
const errorHandler = err => {
    const response = {
        error: err.message
    };
    log(err);
    return response;
};

app.get("/ping", (req, res) => {
    res.send("pong");
});

app.post("/generate", async (req, res) => {
    let {prompt, width, height, upscaleFactor} = req.body;

    log(`Received payload: ${JSON.stringify(req.body)}`);

    if (!width) width = 512;
    if (!height) height = 512;
    if (!upscaleFactor) upscaleFactor = 0;

    if (!prompt) {
        return res.json(errorHandler(new Error("Missing prompt")));
    }

    log(`Prompt: ${prompt}`);

    try {
        const data = await generateAsync({
            prompt,
            width,
            height,
            steps: 70,
            cfgScale: 15,
            noStore: true,
            apiKey: process.env.DREAMSTUDIO_API_KEY,
            engine: "stable-diffusion-v1-6",
        });

        const {images} = data;

        const response = data.res;

        if (!response.isOk) {
            res.json(errorHandler(new Error(`Err #${response.code}: ${response.status} - ${response.message}`)))
            return;
        }

        if (!images.length) {
            res.json(errorHandler(new Error("No image returned")))
            return;
        }

        const img = images[0];


        let imgBuffer = img.buffer;
        if (width < 1000 && upscaleFactor > 0) {

            log("Width is less than 1000px, so we will upscale the image");

            // upscale row-res images
            const replicate = new Replicate({
                token: process.env.REPLICATE_API_KEY
            });

            const esrganModel = await replicate.models.get('nightmareai/real-esrgan');

            try {

                const newImageUrl = await Promise.race([
                    new Promise((resolve, reject) => {
                        setTimeout(() => resolve(null), 30000);
                    }),
                    esrganModel.predict({
                        image: `data:${img.mimeType};base64,${img.buffer.toString("base64")}`,
                        scale: upscaleFactor,
                    })
                ]);

                if(newImageUrl) {
                    let fImg = await fetch(newImageUrl);
                    imgBuffer = Buffer.from(await fImg.arrayBuffer());
                }
            } catch (e) {
                // not sure why
            }

        }

        res.type(img.mimeType).send(imgBuffer);

    } catch (err) {
        res.json(errorHandler(err));
    }
});

let server;
module.exports = {
    start() {
        if (server) return;
        server = app
            .listen(PORT, () => {
                log(`listening on port ${PORT}`);
            })
            .setTimeout(5 * 60 * 1000);
    },
    stop(cb) {
        if (!server) return;
        server.close();
        cb && cb();
    }
};

