<?php

namespace App\Helpers;

use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use LinkedIn\Client;
use App\Account;
use GuzzleHttp\Client as GuzzleClient;
use GuzzleHttp\Exception\RequestException;
use LinkedIn\Exception;
use LinkedIn\Http\Method;
use function GuzzleHttp\Psr7\build_query;
use Psr\Http\Message\ResponseInterface;

class LinkedInClient extends Client
{

    /**
     * @return GuzzleClient
     */
    public function getClient($opts = []){
        return new GuzzleClient(array_merge([
            'base_uri' => $this->getApiRoot()
        ], $opts));
    }

    public function getHeadersForRequest(){
        $headers = $this->getApiHeaders();
        unset($headers['Content-Type']);
        //$headers = [];
        if ($this->isUsingTokenParam()) {
            //
        } else {
            $headers['Authorization'] = 'Bearer ' . $this->accessToken->getToken();
        }
        return $headers;
    }


    /**
     * LinkedIn's new API requires different base path
     * @return LinkedInClient
     */
    public function useRest(){
        $this->setApiRoot('https://api.linkedin.com/rest/');
        $this->setApiHeaders([
            'x-li-format' => 'json',
            'X-Restli-Protocol-Version' => '2.0.0', // use protocol v2
            'Content-Type' => 'application/json',
            'Linkedin-Version' => '202503',
        ]);
        return $this;
    }

    /**
     * Upload media - we use overridden method because original method has differences from latest api spec of linkedin
     *
     * @param $path
     * @return array
     * @throws Exception
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @see \LinkedIn\Client
     */
    public function upload($path)
    {
        $headers = $this->getApiHeaders();
        unset($headers['Content-Type']);
        //$headers = [];
        if ($this->isUsingTokenParam()) {
            //
        } else {
            $headers['Authorization'] = 'Bearer ' . $this->accessToken->getToken();
        }
        $guzzle = new GuzzleClient([
            'base_uri' => $this->getApiRoot()
        ]);
        $fileinfo = pathinfo($path);
        $filename = preg_replace('/\W+/', '_', $fileinfo['filename']);
        if (isset($fileinfo['extension'])) {
            $filename .= '.' . $fileinfo['extension'];
        }
        $options = [
            'multipart' => [
                [
                    'name' => 'fileupload',
                    'filename' => $filename,
                    'contents' => fopen($path, 'r')
                ]
            ],
            'headers' => $headers,
        ];
        try {
            $response = $guzzle->request(Method::POST, 'https://api.linkedin.com/media/upload', $options);
        } catch (RequestException $requestException) {
            throw Exception::fromRequestException($requestException);
        }
        return self::responseToArray($response);
    }

    /**
     * @param $endpoint
     * @param array $params
     * @param string $method
     * @return ResponseInterface
     * @throws Exception
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function request($endpoint, array $params = [], $method = Method::GET)
    {
        $headers = $this->getApiHeaders();
        $options = $this->prepareOptions($params, $method);
        Method::isMethodSupported($method);
        if ($this->isUsingTokenParam()) {
            $params['oauth2_access_token'] = $this->accessToken->getToken();
        } else {
            $headers['Authorization'] = 'Bearer ' . $this->accessToken->getToken();
        }
        $guzzle = new GuzzleClient([
            'base_uri' => $this->getApiRoot(),
            'headers' => $headers,
        ]);
        if (!empty($params) && Method::GET === $method) {
            $endpoint .= '?' . build_query($params);
        }
        try {
            return $response = $guzzle->request($method, $endpoint, $options);/*
        } catch (ClientException $e) {
            $res = null;
            try {
                $res = \GuzzleHttp\json_decode($e->getResponse()->getBody());
            } catch (\Exception $ex){}
            if($res){
                if (isset($res['error_description'])) {
                    throw Exception::fromRequestException(new \Exception($res['error_description'], 0, $e));
                } elseif (isset($res['message'])) {
                    throw Exception::fromRequestException(new \Exception($res['message'], 0, $e));
                }
            }
            throw $e;*/
        } catch (RequestException $requestException) {
            if($requestException->getResponse()) {
                throw Exception::fromRequestException($requestException);
            } else {
                throw $requestException;
            }
        }
    }

    /**
     * A file splitter function [especially for video]
     * Can split a file to number of parts depending on the buffer size given
     *
     * @param string $filePath Path of the file to split
     * @param int $buffer The [maximum] size of the part of a file, default 4MB
     * 
     * @return array paths to the file parts
     */
    public function splitFile(string $filePath, int $buffer = 4194303){
        $file = fopen($filePath,'r');
        $file_size = filesize($filePath);
        
        $parts = $file_size / $buffer; //no of parts to split
    
        $file_parts = [];
    
        for($i=0 ; $i < $parts ; $i++){
            /**
             * read buffer sized amount from file
             * fread starts reading file from the point it stopped previously
             */
            $file_part = fread($file, $buffer); 

            $file_part_path =  'linkedin_uploading/part_' . Str::random() . '_' . $i . '_' . basename($filePath);  //the filename of the part
            
            $file_new = fopen(storage_path('app/') . $file_part_path,'w+');
            fwrite($file_new, $file_part);
            
            $file_parts[] = $file_part_path;
            fclose($file_new);
        }

        fclose($file);

        return $file_parts;
    }

    /**
     * @param string $filePath The path to the file on local disk
     * @param string $fileType File type i.e image, video
     * @param string $postAuthor User urn who created the post
     * @return string URN of the uploaded file
     * @throws \Exception|\GuzzleHttp\Exception\GuzzleException
     */
    public function uploadFile(string $filePath, string $fileType, string $postAuthor){
        if(!file_exists($filePath)){
            throw new \Exception('File not found');
        }

        // Get file size
        $fileSize = filesize($filePath);

        $guzzle = $this->getClient();

        $fileUploadHeaders = null;
        $eTags = [];
        $urn = null;
        $uploadUrl = null;

        $tries = 0;

        if($fileType === 'document'){
            while($tries < 5){
                try {
                    $resUpload = $this->post('documents?action=initializeUpload', [
                        'initializeUploadRequest' => [
                            'owner' => $postAuthor,
                        ],
                    ]);
                    $urn = $resUpload['value']['document'];
                    $uploadUrl = $resUpload['value']['uploadUrl'];
                    break;
                } catch(\Exception $e){
                    $tries++;
                    if($tries !== 5){
                        continue;
                    }
                    throw $e;
                }
            }
        }else if($fileType === 'video'){
            while($tries < 5){
                try {
                    $resUpload = $this->post('videos?action=initializeUpload', [
                        'initializeUploadRequest' => [
                            'owner' => $postAuthor,
                            "fileSizeBytes"=> $fileSize,
                            "uploadCaptions"=> false,
                            "uploadThumbnail"=> false
                        ],
                    ]);
                    $urn = $resUpload['value']['video'];
                    $uploadUrl = $resUpload['value']['uploadInstructions'][0]['uploadUrl'];
                    $fileUploadHeaders = [
                        'Content-Type' => 'application/octet-stream'
                    ];
                    break;
                } catch (\Exception $e) {
                    $tries++;
                    if($tries !== 5){
                        continue;
                    }
                    throw $e;
                }
            }
        } else {
            while($tries < 5){
                try {
                    $resUpload = $this->post('images?action=initializeUpload', [
                        'initializeUploadRequest' => [
                            'owner' => $postAuthor,
                        ],
                    ]);
                    $urn = $resUpload['value']['image'];
                    $uploadUrl = $resUpload['value']['uploadUrl'];
                    break;
                } catch(\Exception $e){
                    $tries++;
                    if($tries !== 5){
                        continue;
                    }
                    throw $e;
                }
            }
        }

        if($fileType === 'video'){
            try {
                // we need to split video into chunks of 4MB
                $parts = $this->splitFile($filePath, 4 * 1024 * 1024); //parts of 4MB
            } catch(\Exception $e){
                report($e);
                throw $e;
            }

            foreach($parts as $file_part){
                $tries = 0;
                $file_part_path = storage_path('app/') . $file_part;
                while($tries < 5){
                    $fileStream = \GuzzleHttp\Psr7\Utils::tryFopen($file_part_path, 'r');
                    try {
                        $r = $guzzle->request('PUT', $uploadUrl, array_filter([
                            'body' => $fileStream,
                            'headers' => $fileUploadHeaders,
                        ]));
                        $eTags[] = $r->getHeader('eTag')[0];
                        // cleanup
                        \Storage::disk('local')->delete($file_part);
                        break;
                    } catch (\Exception $e) {
                        $tries++;
                        if($tries !== 5){
                            // random sleep between 0 and 10 seconds
                            usleep(rand(0, 10) * 1000000);
                            continue;
                        }
                        report($e);

                        // in case of exception, we need to delete all parts here
                        \Storage::disk('local')->delete($parts);
                        if($e instanceof \GuzzleHttp\Exception\ServerException){
                            throw new \Exception('Temporary failure at LinkedIn server. Please try later.');
                        } else {
                            throw new \Exception('Unable to upload media to LinkedIn', 0, $e);
                        }
                    }
                }
            }
        } else {
            // upload headers also need authorization header
            $fileUploadHeaders = $this->getHeadersForRequest();
            $tries = 0;
            while($tries < 5){
                $fileStream = \GuzzleHttp\Psr7\Utils::tryFopen($filePath, 'r');
                try {
                    $guzzle->request('PUT', $uploadUrl, array_filter([
                        'body' => $fileStream,
                        'headers' => $fileUploadHeaders,
                    ]));
                    break;
                } catch (\Exception $e) {
                    $tries++;
                    if($tries !== 5){
                        // random sleep between 0 and 10 seconds
                        usleep(rand(0, 10) * 1000000);
                        continue;
                    }
                    report($e);
                    if($e instanceof \GuzzleHttp\Exception\ServerException){
                        throw new \Exception('Temporary failure at LinkedIn server. Please try later.');
                    } elseif($e instanceof \GuzzleHttp\Exception\ClientException) {
                        // log
                        \Log::error('Unable to upload media to LinkedIn', [
                            'response' => $e->getResponse()->getBody()->getContents(),
                        ]);
                        throw new \Exception('Unable to upload media to LinkedIn', 0, $e);
                    } else {
                        throw new \Exception('Unable to upload media to LinkedIn', 0, $e);
                    }
                }
            }
        }

        // if video, need to finalize upload
        if($fileType === 'video'){
            $tries = 0;
            while($tries < 5){
                try {
                    $headers = $this->getHeadersForRequest();
                    $headers['Content-Type'] = 'application/json';
                    $this->setApiHeaders($headers);
    
                    $this->request('https://api.linkedin.com/rest/videos?action=finalizeUpload', [
                        'finalizeUploadRequest' => [
                            'video' => $urn,
                            'uploadToken' => '',
                            'uploadedPartIds' => $eTags,
                        ]
                    ], Method::POST);
                    break;
                } catch (\Exception $e){
                    $tries++;
                    if($tries !== 5){
                        continue;
                    }
                    report($e);
                    throw new \Exception('Unable to finalize uploaded video');
                }
            }
        }

        return $urn;
    }

    /**
     * Process LinkedIn Options
     * @param Account $account
     * @param array $options
     * @param array $oldOptions
     * @param bool $validateOnly
     * @return array $linkedInOptions
     * @throws ValidationException
     * @throws \Exception
     */
    public static function processOptions(Account $account, array $options, array $oldOptions = [], bool $validateOnly = false){
        if(!$options){
            $options = [];
        }

        if(isset($options['customize_link'])){

            $thumbKey = 'thumbnail';
            if(isset($options['link_thumbnail'])){
                $thumbKey = 'link_thumbnail';
            }

            \Validator::make($options, [
                'link_title' => 'required|string|max:400'
            ])->validate();

            if(!isset($options[$thumbKey])){
                if(!$validateOnly && isset($oldOptions[$thumbKey])){ //if old options have thumbnail delete existing file;
                    \Storage::cloud()->delete($oldOptions[$thumbKey]['key']);
                }
            } else {
                \Validator::make($options[$thumbKey], [
                    'name' => 'string|required',
                    'extension' => 'string|required',
                    'key' => 'string|required',
                    'secureKey' => 'string|required',
                    'size' => 'required',
                ])->validate();

                // make sure the secure key is correct
                $decryptedKey = decrypt($options[$thumbKey]['secureKey']);
                if($decryptedKey !== $options[$thumbKey]['key']){
                    throw new \Exception('Article thumbnail: Invalid secure key');
                }

                if(!\Storage::cloud()->exists($options[$thumbKey]['key'])){
                    throw new \Exception('Article thumbnail: Media file not available: ' . $options[$thumbKey]['name']);
                }

                if(!$validateOnly){
                    $newKey = 'attachments/' . $account->id . '_' . time() . '_' . str_random(40) . sha1(time() . $options[$thumbKey]['name'] . $options[$thumbKey]['extension'] . user()->id) . '.' . $options[$thumbKey]['extension'];
                    if (isset($options[$thumbKey]['temporary']) && $options[$thumbKey]['temporary']) {
                        \Storage::cloud()->move($options[$thumbKey]['key'], $newKey);
                        unset($options[$thumbKey]['temporary']); // unset temporary flag
                        $options[$thumbKey]['key'] = $newKey;
                        $options[$thumbKey]['secureKey'] = encrypt($options[$thumbKey]['key']);
                    } else if($options[$thumbKey]['key'] === $oldOptions[$thumbKey]['key']){
                        \Storage::cloud()->copy($options[$thumbKey]['key'], $newKey);
                        $options[$thumbKey]['key'] = $newKey;
                        $options[$thumbKey]['secureKey'] = encrypt($options[$thumbKey]['key']);
                    }

                    // also delete temporary url
                    if(isset($options[$thumbKey]['url'])){
                        unset($options[$thumbKey]['url']);
                    }

                    // make sure size is integer
                    if(isset($options[$thumbKey]['size'])){
                        $options[$thumbKey]['size'] = (int) $options[$thumbKey]['size'];
                    }
                }

            }
        }

        return $options;
    }
}
