/* Style the scrollbar */
/* For Webkit browsers (Chrome, Safari) */
select::-webkit-scrollbar {
    width: 16px;
    background-color: $gray-100;
  }
  select::-webkit-scrollbar-thumb {
    background-color: $gray-300; /* Color of the thumb */
    border-radius: 6px;
  }
  /* For Firefox */
  select {
    scrollbar-color: #007bff #f5f5f5; /* thumb and track color */
    scrollbar-width: thin;
  }
  .form-control-file,
  .form-control-range {
    display: block;
    width: 100%;
  }
  ::-webkit-file-upload-button {
    border-radius: 6px;
    border: 1px solid $gray-700;
    background: $gray-100;
    padding: 8px 16px;
  }
  // .form-check-input {
  //   position: absolute;
  //   margin-top: $form-check-input-margin-y;
  //   margin-left: -$form-check-input-gutter;
  // }
  .form-check {
    padding-left: 0px;
    display: flex;
    align-items: center;
  }
  .form-check-label {
    margin-bottom: 0;
    font-weight: 400;
    color: $color-text;
  }
  .custom-file-label {
    color: $color-text;
    &::after {
      background-color: #E3E6EB;
    }
  }
  // switches
  //
  // Tweak a few things for switches
  .custom-switch {
    .custom-control-label {
      &::before {
        left: -($custom-switch-width + $custom-control-gutter);
        width: 52px;
        height: 32px;
        pointer-events: all;
        background-color: $color-primary;
        border-radius: 100px;
      }
      &::after {
        top: .25rem;
        width: 24px;
        height: 24px;
        left: 4px;
        background-color: #fff;
        border-radius: 100px;
        transform: scale(1);
        @include transition(transform .15s ease-in-out, $custom-forms-transition);
      }
    }
    .custom-control-input:checked ~ .custom-control-label {
      &::before{
        background-color:$color-primary;
        border: $color-primary;
      }
      &::after {
        background-color: #fff;
        transform: translateX(1.3rem)
      }
    }
    .custom-control-input:disabled {
      &:checked ~ .custom-control-label::before {
         background-color: #e0e0e0;
      }
    }
  }
  .form-control {
    font-weight: 400;
    border-color: #E4E7ED;
    option {
      font-weight: 400;
      padding: 8px 20px;
    }
  }
  input.form-control{
    &:hover {
      border: 1px solid $gray-400;
    }
    &:focus{
      border: 1px solid $color-primary;
      box-shadow: 0px 0px 0px 4px #D7ECFF;    
    }
    &:disabled{
        border: 1px solid $gray-200;
        background-color: #fff;
    }
    padding: 12px 16px;

  }
  textarea.form-control{
    &:hover {
      border: 1px solid $gray-700;
      box-shadow: 0px 8px 20px -4px rgba(29, 33, 41, 0.08), 0px 0px 0px 4px #D7ECFF;
    }
    &:focus{
      border: 1px solid $color-primary;
      box-shadow: 0px 8px 20px -4px rgba(29, 33, 41, 0.08), 0px 0px 0px 4px #D7ECFF;
    }
  }
  label {
    font-weight: 500;
    font-size: 1rem;
    margin-bottom: 4px;
    letter-spacing: 0px;
    color: $color-text-dark;
    &.require::after {
      content: '*';
      color: $color-danger;
      font-weight: 500;
      margin-left: 8px
    }
  }
