<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/*
 * Already have this table - so only updating it
 */
class CreateSubscriptionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('subscriptions', function (Blueprint $table) {

            $isSqlite = DB::connection()->getDriverName() === 'sqlite';

            if (!$isSqlite){
                // only need to add stripe_status
                $table->string('stripe_status');
                // add index
                $table->index(['user_id', 'stripe_status']);

            } else {
                // For SQLite, we cannot add a column with a default value directly
                // We will add the column without a default and then update it
                $table->string('stripe_status')->nullable();
                $table->index(['user_id', 'stripe_status']);
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('subscriptions', function (Blueprint $table) {
            $table->dropColumn('stripe_status');
            $table->dropIndex(['user_id', 'stripe_status']);
        });
    }
}
