 .table {
    border: none;
    th,
    td {
        font-size: 16px;
        font-weight: 400;
        border-top: $table-border-width solid $table-border-color;
        vertical-align: middle;
    }
    thead th {
        font-size: 1rem;
        font-weight: 500;
        border-bottom: none;
        border-top: none;
        &:first-child{
            padding-top: 0px;
        }
        span{
            font-size: 1.25rem;
            font-weight: 500;
            
        }
    }
    td.section-main{
        font-weight: 700;
    }
    tbody + tbody {
        border-top: (2 * $table-border-width) solid $table-border-color;
    }
    tbody tr:first-child td {
        border-top: none;
    }
}

.pricing-table{
    .table {
        td {
            text-align: center;
            &:first-child {
                text-align: left;
            }
        }
        td.section-main{
            font-weight: 700;
            font-size: 1.5rem;
            color: $color-text-dark;
            font-family: "Plus Jakarta Sans";
        }
    }
}
.datepicker {
    .table {
        border: 1px solid $color-divider-light;
    }
    th,
    td {
        font-size: 1rem;
    }
    thead th {
        font-weight: 700;
        font-size: 1rem;
    }
}
.table-border{
    border: 1px solid $gray-200;
}
