@extends('layout.full_width')
@section('title', $page['title']  . ' | ' . config('app.name'))
@section('html_tag_str') itemscope itemtype="https://schema.org/FAQPage"@endsection
@push('head_html')

    <meta name="description" content="{{ $page['description'] }}"/>

    <meta property="og:locale" content="en_US" />
    <meta property="og:type" content="website" />
    <meta property="og:title" content="{{ $page['title'] }}" />
    <meta property="og:description" content="{{ $page['description'] }}" />
    <meta property="og:url" content="https://socialbu.com/social-media-scheduling-tools/{{ $page['slug'] }}" />
    <meta property="og:site_name" content="SocialBu" />

    <meta property="og:image" content="{{ $page['image'] }}" />

    <meta property="fb:app_id" content="{{ config('services.facebook.client_id') }}" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content="{{ $page['image'] }}" />
    <meta name="twitter:title" content="{{ $page['title'] }}" />
    <meta name="twitter:description" content="{{ $page['description'] }}" />
    <meta name="twitter:site" content="@socialbuapp" />


    <link rel="canonical" href="https://socialbu.com/social-media-scheduling-tools/{{ $page['slug'] }}" />
    <style>
        .comparison-logo {
            max-width: 64px;
        }
    </style>
@endpush
@section('content')
    <header class="header header-main pt-9 pb-6">
        <div class="container text-center">
            <h1 class="display-1">{{ $page['title'] }}</h1>
            <p class="lead-2 mt-6">{{ $page['description'] }}</p>
            @include('common.internal.try-now-block')
        </div>
    </header>
    <main class="main-content">
        <!--
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/">Home</a></li>
                    <li class="breadcrumb-item"><a href="/social-media-scheduling-tools/">Social Media Scheduling Tools</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ $tool1['name'] }} vs. {{ $tool2['name'] }}</li>
                </ol>
            </nav>
        </div>
        -->
        <section class="section bg-light pt-6">
            <div class="container">
                <div class="row gap-y">
                    <div class="col-12">
                        <div class="card border bg-white">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead class="thead-inverse">
                                        <tr>
                                            <th class="w-25"></th>
                                            <th class="text-center">
                                                <img data-toggle="tooltip" class="img-responsive comparison-logo lozad" alt="{{ $tool1['name'] }}" title="{{ $tool1['name'] }}" src="/images/1x1.gif" data-src="{{ $tool1['logo'] }}" />
                                            </th>
                                            <th class="text-center">
                                                <img data-toggle="tooltip" class="img-responsive comparison-logo lozad" alt="{{ $tool2['name'] }}" title="{{ $tool2['name'] }}" src="/images/1x1.gif" data-src="{{ $tool2['logo'] }}" />
                                            </th>
                                            @if($tool3)
                                                <th class="text-center">
                                                    <img data-toggle="tooltip" class="img-responsive comparison-logo lozad" alt="{{ $tool3['name'] }}" title="{{ $tool3['name'] }}" src="/images/1x1.gif" data-src="{{ $tool3['logo'] }}" />
                                                </th>
                                            @endif
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <td class="w-25 option">Social Networks</td>
                                            <td class="text-center">
                                                {{ implode(', ', $tool1['networks']) }}
                                            </td>
                                            <td class="text-center">
                                                {{ implode(', ', $tool2['networks']) }}
                                            </td>
                                            @if($tool3)
                                                <td class="text-center">
                                                    {{ implode(', ', $tool3['networks']) }}
                                                </td>
                                            @endif
                                        </tr>
                                        <tr>
                                            <td class="option">Scheduling</td>
                                            <td class="text-center">
                                                <i class="ph ph-check text-success"></i>
                                            </td>
                                            <td class="text-center">
                                                <i class="ph ph-check text-success"></i>
                                            </td>
                                            @if($tool3)
                                                <td class="text-center">
                                                    <i class="ph ph-check text-success"></i>
                                                </td>
                                            @endif
                                        </tr>
                                        <tr>
                                            <td class="option">Teams/Brands</td>
                                            <td class="text-center">
                                                @if($tool1['slug'] === 'socialbu')
                                                    <i class="ph ph-check text-success"></i>
                                                @elseif($tool1['has_teams'])
                                                    <i class="ph ph-check text-success"></i>
                                                @else
                                                    <i class="ph ph-x pr-2 text-danger opacity-60"></i>
                                                @endif
                                            </td>
                                            <td class="text-center">
                                                @if($tool2['slug'] === 'socialbu')
                                                    <i class="ph ph-check text-success"></i>
                                                @elseif($tool2['has_teams'])
                                                    <i class="ph ph-check text-success"></i>
                                                @else
                                                    <i class="ph ph-x pr-2 text-danger opacity-60"></i>
                                                @endif
                                            </td>
                                            @if($tool3)
                                                <td class="text-center">
                                                    @if($tool3['slug'] === 'socialbu')
                                                        <i class="ph ph-check text-success"></i>
                                                    @elseif($tool3['has_teams'])
                                                        <i class="ph ph-check text-success"></i>
                                                    @else
                                                        <i class="ph ph-x pr-2 text-danger opacity-60"></i>
                                                    @endif
                                                </td>
                                            @endif
                                        </tr>
                                        <tr>
                                            <td class="option">Post Approvals</td>
                                            <td class="text-center">
                                                @if($tool1['slug'] === 'socialbu')
                                                    <i class="ph ph-check text-success"></i>
                                                @elseif($tool1['has_post_approvals'])
                                                    <i class="ph ph-check text-success"></i>
                                                @else
                                                    <i class="ph ph-x pr-2 text-danger opacity-60"></i>
                                                @endif
                                            </td>
                                            <td class="text-center">
                                                @if($tool2['slug'] === 'socialbu')
                                                    <i class="ph ph-check text-success"></i>
                                                @elseif($tool2['has_post_approvals'])
                                                    <i class="ph ph-check text-success"></i>
                                                @else
                                                    <i class="ph ph-x pr-2 text-danger opacity-60"></i>
                                                @endif
                                            </td>
                                            @if($tool3)
                                                <td class="text-center">
                                                    @if($tool3['slug'] === 'socialbu')
                                                        <i class="ph ph-check text-success"></i>
                                                    @elseif($tool3['has_post_approvals'])
                                                        <i class="ph ph-check text-success"></i>
                                                    @else
                                                        <i class="ph ph-x pr-2 text-danger opacity-60"></i>
                                                    @endif
                                                </td>
                                            @endif
                                        </tr>
                                        <tr>
                                            <td class="option">Auto Publish from RSS</td>
                                            <td class="text-center">
                                                @if($tool1['slug'] === 'socialbu')
                                                    <i class="ph ph-check text-success"></i>
                                                @elseif($tool1['has_publish_from_rss'])
                                                    <i class="ph ph-check text-success"></i>
                                                @else
                                                    <i class="ph ph-x pr-2 text-danger opacity-60"></i>
                                                @endif
                                            </td>
                                            <td class="text-center">
                                                @if($tool2['slug'] === 'socialbu')
                                                    <i class="ph ph-check text-success"></i>
                                                @elseif($tool2['has_publish_from_rss'])
                                                    <i class="ph ph-check text-success"></i>
                                                @else
                                                    <i class="ph ph-x pr-2 text-danger opacity-60"></i>
                                                @endif
                                            </td>
                                            @if($tool3)
                                                <td class="text-center">
                                                    @if($tool3['slug'] === 'socialbu')
                                                        <i class="ph ph-check text-success"></i>
                                                    @elseif($tool3['has_publish_from_rss'])
                                                        <i class="ph ph-check text-success"></i>
                                                    @else
                                                        <i class="ph ph-x pr-2 text-danger opacity-60"></i>
                                                    @endif
                                                </td>
                                            @endif
                                        </tr>
                                        <tr>
                                            <td class="option">True Automation</td>
                                            <td class="text-center">
                                                @if($tool1['slug'] === 'socialbu')
                                                    <i class="ph ph-check text-success"></i>
                                                @else
                                                    <i class="ph ph-x pr-2 text-danger opacity-60"></i>
                                                @endif
                                            </td>
                                            <td class="text-center">
                                                @if($tool2['slug'] === 'socialbu')
                                                    <i class="ph ph-check text-success"></i>
                                                @else
                                                    <i class="ph ph-x pr-2 text-danger opacity-60"></i>
                                                @endif
                                            </td>
                                            @if($tool3)
                                                <td class="text-center">
                                                    @if($tool3['slug'] === 'socialbu')
                                                        <i class="ph ph-check text-success"></i>
                                                    @else
                                                        <i class="ph ph-x pr-2 text-danger opacity-60"></i>
                                                    @endif
                                                </td>
                                            @endif
                                        </tr>
                                        <tr>
                                            <td class="option">Free plan</td>
                                            <td class="text-center">
                                                @if($tool1['has_free_plan'])
                                                    <i class="ph ph-check text-success"></i>
                                                @else
                                                    <i class="ph ph-x pr-2 text-danger opacity-60"></i>
                                                @endif
                                            </td>
                                            <td class="text-center">
                                                @if($tool2['has_free_plan'])
                                                    <i class="ph ph-check text-success"></i>
                                                @else
                                                    <i class="ph ph-x pr-2 text-danger opacity-60"></i>
                                                @endif
                                            </td>
                                            @if($tool3)
                                                <td class="text-center">
                                                    @if($tool2['has_free_plan'])
                                                        <i class="ph ph-check text-success"></i>
                                                    @else
                                                        <i class="ph ph-x pr-2 text-danger opacity-60"></i>
                                                    @endif
                                                </td>
                                            @endif
                                        </tr>
                                        <tr>
                                            <td class="w-25 option">Paid plans starting at</td>
                                            <td class="text-center">
                                                {{ $tool1['starting_price'] }}
                                            </td>
                                            <td class="text-center">
                                                {{ $tool2['starting_price'] }}
                                            </td>
                                            @if($tool3)
                                                <td class="text-center">
                                                    {{ $tool3['starting_price'] }}
                                                </td>
                                            @endif
                                        </tr>
                                        <tr>
                                            <td class="option">Free set-up on your behalf*</td>
                                            <td class="text-center">
                                                @if($tool1['slug'] === 'socialbu')
                                                    <i class="ph ph-check text-success"></i>
                                                @else
                                                    <i class="ph ph-x pr-2 text-danger opacity-60"></i>
                                                @endif
                                            </td>
                                            <td class="text-center">
                                                @if($tool2['slug'] === 'socialbu')
                                                    <i class="ph ph-check text-success"></i>
                                                @else
                                                    <i class="ph ph-x pr-2 text-danger opacity-60"></i>
                                                @endif
                                            </td>
                                            @if($tool3)
                                                <td class="text-center">
                                                    @if($tool3['slug'] === 'socialbu')
                                                        <i class="ph ph-check text-success"></i>
                                                    @else
                                                        <i class="ph ph-x pr-2 text-danger opacity-60"></i>
                                                    @endif
                                                </td>
                                            @endif
                                        </tr>
                                        <tr>
                                            <td class="option">Live Support</td>
                                            <td class="text-center">
                                                @if($tool1['live_chat'])
                                                    <i class="ph ph-check text-success"></i>
                                                @else
                                                    <i class="ph ph-x pr-2 text-danger opacity-60"></i>
                                                @endif
                                            </td>
                                            <td class="text-center">
                                                @if($tool2['live_chat'])
                                                    <i class="ph ph-check text-success"></i>
                                                @else
                                                    <i class="ph ph-x pr-2 text-danger opacity-60"></i>
                                                @endif
                                            </td>
                                            @if($tool3)
                                                <td class="text-center">
                                                    @if($tool3['live_chat'])
                                                        <i class="ph ph-check text-success"></i>
                                                    @else
                                                        <i class="ph ph-x pr-2 text-danger opacity-60"></i>
                                                    @endif
                                                </td>
                                            @endif
                                        </tr>
                                        </tbody>
                                        <tfoot class="thead-inverse">
                                        <tr>
                                            <th class="w-25"></th>
                                            <th class="text-center">
                                                @if($tool1['slug'] === 'socialbu')
                                                    <a class="btn btn-success" href="/auth/register">Try For Free</a>
                                                @else
                                                    <!--
                                                    {{ parse_url($tool1['link'], PHP_URL_HOST) }}
                                                    -->
                                                @endif
                                            </th>
                                            <th class="text-center">
                                                @if($tool2['slug'] === 'socialbu')
                                                    <a class="btn btn-success" href="/auth/register">Try For Free</a>
                                                @else
                                                    <!--
                                                    {{ parse_url($tool2['link'], PHP_URL_HOST) }}
                                                    -->
                                                @endif
                                            </th>
                                            @if($tool3)
                                                <th class="text-center">
                                                    @if($tool3['slug'] === 'socialbu')
                                                        <a class="btn btn-success" href="/auth/register">Try For Free</a>
                                                    @else
                                                        ---
                                                    @endif
                                                </th>
                                            @endif
                                        </tr>
                                        </tfoot>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>


                    <div class="col-12">
                        <div class="card border bg-white">
                            <div class="card-body">
                                @if(isset($page['heading1']))
                                    @foreach(range(1, 5) as $n)
                                        @if(isset($page['heading' . $n]))

                                                <section class="section py-7 overflow-hidden">
                                                    <div class="container">
                                                        <div class="row gap-y align-items-center">


                                                            @if($n % 2 == 0)

                                                                <div class="col-md-6 mx-auto">
                                                                    <h2 class="h4 font-weight-700">
                                                                        {{ $page['heading' . $n] }}
                                                                    </h2>
                                                                    <p class="lead">
                                                                        {{ $page['para' . $n] }}
                                                                    </p>
                                                                </div>

                                                                @if(isset($page['image' . $n]) && !empty($page['image' . $n]))
                                                                    <div class="col-md-6 order-md-first">
                                                                        <img src="/images/1x1.gif" data-src="{{ $page['image' . $n] }}" alt="{{ $page['heading' . $n] }}" data-aos="fade-left" class="aos-init aos-animate lozad" />
                                                                    </div>
                                                                @endif

                                                            @else

                                                                <div class="col-md-6 mx-auto">
                                                                    <h2 class="h4 font-weight-700">
                                                                        {{ $page['heading' . $n] }}
                                                                    </h2>
                                                                    <p class="lead">
                                                                        {{ $page['para' . $n] }}
                                                                    </p>
                                                                </div>

                                                                @if(isset($page['image' . $n]) && !empty($page['image' . $n]))
                                                                    <div class="col-md-6">
                                                                        <img src="/images/1x1.gif" data-src="{{ $page['image' . $n] }}" alt="{{ $page['heading' . $n] }}" data-aos="fade-left" class="aos-init aos-animate lozad" />
                                                                    </div>
                                                                @endif

                                                            @endif
                                                        </div>
                                                    </div>
                                                </section>


                                        @endif
                                    @endforeach
                                @else
                                    @foreach(['publish', 'respond', 'team_collaboration', 'analyze', 'monitor'] as $dataPoint)
                                        @php($hasData = false)
                                        @if(isset($tool1[$dataPoint]) && !empty($tool1[$dataPoint]))
                                            @php($hasData = true)
                                            <p class="lead">
                                                {{ $tool1[$dataPoint] }}
                                            </p>
                                        @endif
                                        @if(isset($tool2[$dataPoint]) && !empty($tool2[$dataPoint]))
                                            @php($hasData = true)
                                            <p class="lead">
                                                {{ $tool2[$dataPoint] }}
                                            </p>
                                        @endif
                                        @if($hasData)
                                            <hr/>
                                        @endif
                                    @endforeach
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <div class="pt-8 border-top pb-8">
            @include('common.internal.as-featured-on')
        </div>

        {{--
        <section class="section">
            <div class="container">
                <header class="section-header">
                    <h2>Frequently Asked Questions</h2>
                </header>


                <div class="row gap-y">
                    @foreach([$tool1, $tool2] as $tool)
                        <div class="col-md-6">

                            @if(!empty($tool['description']))
                                <div itemscope itemprop="mainEntity" itemtype="https://schema.org/Question">
                                    <h3 itemprop="name">What is {{ $tool['name'] }}?</h3>
                                    <div itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
                                        <p itemprop="text">
                                            {{ $tool['description'] }}
                                        </p>
                                    </div>
                                </div>
                            @endif

                            <div itemscope itemprop="mainEntity" itemtype="https://schema.org/Question">
                                <h3 itemprop="name">Does {{ $tool['name'] }} has a free plan?</h3>
                                <div itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
                                    <p itemprop="text">
                                        {{ $tool['has_free_plan'] ?
                                            ('Yes, ' . $tool['name'] . ' has a free plan. ' . (isset($tool['has_free_plan_notes']) ? implode('. ', $tool['has_free_plan_notes']) : ''))
                                            :
                                            ('No, ' . $tool['name'] . ' doesn\'t have a free plan.')
                                        }}
                                    </p>
                                </div>
                            </div>

                            @if(!empty($tool['starting_price']))
                                <div itemscope itemprop="mainEntity" itemtype="https://schema.org/Question">
                                    <h3 itemprop="name">What is the starting price of {{ $tool['name'] }}?</h3>
                                    <div itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
                                        <p itemprop="text">
                                            The starting price of {{ $tool['name'] }} is {{ $tool['starting_price'] }}.
                                            {{ isset($tool['starting_price_notes']) ? implode('. ', $tool['starting_price_notes']) : '' }}
                                        </p>
                                    </div>
                                </div>
                            @endif

                            @if(!empty($tool['live_chat']))
                                <div itemscope itemprop="mainEntity" itemtype="https://schema.org/Question">
                                    <h3 itemprop="name">Does {{ $tool['name'] }} offers support?</h3>
                                    <div itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
                                        <p itemprop="text">
                                            {{ 'Yes, ' . $tool['name'] . ' offers support via ' . implode(', ', $tool['live_chat']) }}
                                        </p>
                                    </div>
                                </div>
                            @endif

                            @if(!empty($tool['cheapest_order']))
                                <div itemscope itemprop="mainEntity" itemtype="https://schema.org/Question">
                                    <h3 itemprop="name">Is {{ $tool['name'] }} one of the cheapest social media scheduling tools?</h3>
                                    <div itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
                                        <p itemprop="text">
                                            Yes, {{ $tool['name'] }} is one of the cheapest social media scheduling tools.
                                        </p>
                                    </div>
                                </div>
                            @endif

                            <div itemscope itemprop="mainEntity" itemtype="https://schema.org/Question">
                                <h3 itemprop="name">How much time will it take to get up and running with {{ $tool['name'] }}?</h3>
                                <div itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
                                    <p itemprop="text">
                                        {{ $tool['slug'] === 'socialbu' ?
                                        'Getting up and running with ' . $tool['name'] . ' takes only a few minutes. ' . $tool['name'] . ' also provides free set-up service on your behalf.' :
                                        'Please visit their website to learn more about it.'
                                        }}
                                    </p>
                                </div>
                            </div>

                            @if($tool['slug'] !== 'socialbu')
                                <div itemscope itemprop="mainEntity" itemtype="https://schema.org/Question">
                                    <h3 itemprop="name">Is there any alternative for {{ $tool['name'] }}?</h3>
                                    <div itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
                                        <p itemprop="text">
                                            There are many alternatives for {{ $tool['name'] }}. You can check them all <a href="/social-media-scheduling-tools/{{ $tool['slug'] }}-alternatives">here</a>. We suggest trying SocialBu.
                                        </p>
                                    </div>
                                </div>
                            @endif

                        </div>
                    @endforeach
                        <div class="col-md-6">

                            <div itemscope itemprop="mainEntity" itemtype="https://schema.org/Question">
                                <h3 itemprop="name">Which one is best? {{ $tool1['name'] }} or {{ $tool2['name'] }}?</h3>
                                <div itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
                                    <p itemprop="text">
                                        The best is what works for you. We suggest learning more about the solution that you want to consider.
                                    </p>
                                </div>
                            </div>

                        </div>

                        <div class="col-md-6">

                            <div>
                                <h3>What is the source of this data?</h3>
                                <div>
                                    <p>
                                        This page only compares the scheduling or scheduling related features. The data has been obtained from multiple resources available on the internet. This data is not guaranteed to be accurate. Please check the related links to get accurate details.
                                    </p>
                                </div>
                            </div>

                        </div>
                </div>

            </div>
        </section>
              --}}

        @include('common.internal.testimonials-block')

        @include('common.internal.join-us-block')
    </main>
@endsection
