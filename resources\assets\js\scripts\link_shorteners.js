import $ from "jquery";
import slideoutModal from "../slideout_modal";

(async ()=>{

    // add
    const $addModal = $("#add_shortener");
    $addModal.on("click._modal_hide", ".hide_modal_onclick", function() {
        $addModal.modal("hide");
    });

    // edit
    const editModal = await slideoutModal.create("Link Shortener", {
        type: "right",
        class: "border-left border-secondary shadow",
        modalConfig: {}, // so it can be closed by clicking outside and esc key
        onAdded(el) {
            $(el)
                .find(".modal-body")
                .empty()
                .append(`<div id='edit_shortener' />`);
        }
    });

    let shortenerVue;
    $(".open_shortener").on("click", function(){
        const data = JSON.parse($(this).attr("data-json"));
        editModal.title(data.name + ' (' + data._type + ')');
        editModal.set({
            onOpen(el){
                $(el)
                    .find(".modal-body")
                    .empty()
                    .append(`<div id='edit_shortener' />`);
                __loadComponent("link-shortener", "#edit_shortener", c =>{
                    shortenerVue = c;
                    setTimeout(()=>{
                        c.initialize(data.id);
                    }, 300);
                });
            },
            onClose(){
                setTimeout(()=>{
                    shortenerVue.$destroy();
                }, 200);
            }
        });
        editModal.open();
    });

})();
