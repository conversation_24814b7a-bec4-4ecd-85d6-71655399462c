<template>
    <div class="row">
        <div class="col-12 mb-8">
            <h5 class="font-weight-500">Desktop vs. Mobile views</h5>
            <div class="card border" v-if="isLoading('account_metrics')" v-html="spinnerHtml"></div>
            <div class="row" v-else>
                <div class="d-flex col-12 overflow-auto hide-scrollbar">
                    <div class="analyze-card card border mr-4">
                        <div class="card-body p-20">
                            <div>
                                <h6 class="font-weight-500 mb-6">Desktop Map Views</h6>
                                <h3 class="mb-0">{{ totalDesktopMapViews }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="analyze-card card border">
                        <div class="card-body p-20">
                            <div>
                                <h6 class="font-weight-500 mb-6">Mobile Map Views</h6>
                                <h3 class="mb-0">{{ totalMobileMapViews }}</h3>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 mt-4">
                    <div class="card border pt-4 d-flex justify-content-center">

                        <Chart
                            :options="viewsChart"
                            ref="chart_views"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 mb-8">
            <h5 class="font-weight-500">Call to Actions</h5>
            <div class="card border" v-if="isLoading('account_metrics')" v-html="spinnerHtml"></div>
            <div class="row" v-else>
                <div class="d-flex col-12 overflow-auto hide-scrollbar">
                    <div class="analyze-card card border mr-4">
                        <div class="card-body p-20">
                            <div>
                                <h6 class="font-weight-500 mb-6">Phone Number Clicks</h6>
                                <h3 class="mb-0">{{ totalPhoneActions }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="analyze-card card border mr-4">
                        <div class="card-body p-20">
                            <div>
                                <h6 class="font-weight-500 mb-6">Website Clicks</h6>
                                <h3 class="mb-0">{{ totalWebsiteActions }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="analyze-card card border">
                        <div class="card-body p-20">
                            <div>
                                <h6 class="font-weight-500 mb-6">Driving Direction Requests</h6>
                                <h3 class="mb-0">{{ totalDrivingDirectionActions }}</h3>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 mt-4">
                    <div class="card border pt-4 d-flex justify-content-center">

                        <Chart
                            :options="actionsChart"
                            ref="chart_actions"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12">
            <h5 class="font-weight-500">Reviews</h5>
            <div class="card border" v-if="isLoading('account_metrics')" v-html="spinnerHtml"></div>
            <div class="row" v-else>
                <div class="col-6 col-md-6">
                    <div class="analyze-card card border">
                        <div class="card-body p-20">
                            <div>
                                <h6 class="font-weight-500 mb-6">Total Reviews</h6>
                                <h3 class="mb-0">{{ totalReviews }}</h3>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 mt-4">
                    <div class="card border pt-4 d-flex justify-content-center">

                        <Chart
                            :options="reviewsChart"
                            ref="chart_reviews"
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { axios, axiosErrorHandler, spinnerHtml } from '../../../../components';
import { Chart } from "highcharts-vue";

export default {
    name: 'GMBPerformance',
    components: {
        Chart
    },
    props: ['filter', 'accounts', 'filterFormOpen'],
    data() {
        return {
            loadingFlags: [],

            accountMetrics: [],
            accountMetricsDaily: [],
        }
    },
    async mounted() {
        await this.fetchData();
    },
    computed: {
        spinnerHtml: () => spinnerHtml,

        dataByTypeAndDate(){
            const dataByType = {
                'desktop_map_views' : {},
                'mobile_map_views' : {},
                'actions_phone': {},
                'actions_website': {},
                'actions_driving_directions': {},
                'reviews': {},
            }
            this.accountMetrics.forEach((a) => {
                const { metrics } = a;
                Object.keys(metrics).forEach(type => {
                    dataByType[type] = dataByType[type] || {};
                    metrics[type].forEach((m) => {
                        dataByType[type][m.date] = dataByType[type][m.date] || 0;
                        dataByType[type][m.date] += m.value;
                    });
                });
            });
            this.accountMetricsDaily.forEach((a) => {
                const { metrics } = a;
                Object.keys(metrics).forEach(type => {
                    dataByType[type] = dataByType[type] || {};
                    metrics[type].forEach((m) => {
                        dataByType[type][m.date] = dataByType[type][m.date] || 0;
                        dataByType[type][m.date] += m.value;
                    });
                });
            });
            return dataByType;
        },
        totalDesktopMapViews() {
            return Object.values(this.dataByTypeAndDate.desktop_map_views).reduce((a, b) => a + b, 0);
        },
        totalMobileMapViews() {
            return Object.values(this.dataByTypeAndDate.mobile_map_views).reduce((a, b) => a + b, 0);
        },
        totalPhoneActions() {
            return Object.values(this.dataByTypeAndDate.actions_phone).reduce((a, b) => a + b, 0);
        },
        totalWebsiteActions() {
            return Object.values(this.dataByTypeAndDate.actions_website).reduce((a, b) => a + b, 0);
        },
        totalDrivingDirectionActions() {
            return Object.values(this.dataByTypeAndDate.actions_driving_directions).reduce((a, b) => a + b, 0);
        },
        totalReviews(){
            return Object.values(this.dataByTypeAndDate.reviews).reduce((a, b) => a + b, 0);
        },

        // chart configs
        viewsChart(){
            const dataByType = this.dataByTypeAndDate;
            const series = [
                {
                    name: "Desktop Map Views",
                    marker: {
                        enabled: false
                    },
                    data: Object.keys(dataByType.desktop_map_views).map((key) => {
                        return [this.timestampForXAxis(key), dataByType.desktop_map_views[key]];
                    }),
                },
                {
                    name: "Mobile Map Views",
                    marker: {
                        enabled: false
                    },
                    data: Object.keys(dataByType.mobile_map_views).map((key) => {
                        return [this.timestampForXAxis(key), dataByType.mobile_map_views[key]];
                    }),
                },
            ];
            return {
                chart: {
                    type: "areaspline",
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: "",
                },
                xAxis: {
                    type: "datetime",
                },
                yAxis: {
                    title: {
                        text: "Views",
                    },
                    labels: {
                        format: '{value}',
                    }
                },
                plotOptions: {
                    series: {
                        colorByPoint: true,
                        dataLabels: {
                            enabled: true
                        }
                    }
                },
                series,
            };
        },
        actionsChart(){
            const dataByType = this.dataByTypeAndDate;
            const series = [
                {
                    name: "Phone Number Clicks",
                    marker: {
                        enabled: false
                    },
                    data: Object.keys(dataByType.actions_phone).map((key) => {
                        return [this.timestampForXAxis(key), dataByType.actions_phone[key]];
                    }),
                },
                {
                    name: "Website Clicks",
                    marker: {
                        enabled: false
                    },
                    data: Object.keys(dataByType.actions_website).map((key) => {
                        return [this.timestampForXAxis(key), dataByType.actions_website[key]];
                    }),
                },
                {
                    name: "Driving Direction Requests",
                    marker: {
                        enabled: false
                    },
                    data: Object.keys(dataByType.actions_driving_directions).map((key) => {
                        return [this.timestampForXAxis(key), dataByType.actions_driving_directions[key]];
                    }),
                },
            ];
            return {
                chart: {
                    type: "areaspline",
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: "",
                },
                xAxis: {
                    type: "datetime",
                },
                yAxis: {
                    title: {
                        text: "Actions",
                    },
                    labels: {
                        format: '{value}',
                    }
                },
                plotOptions: {
                    series: {
                        colorByPoint: true,
                        dataLabels: {
                            enabled: true
                        }
                    }
                },
                series,
            };
        },
        reviewsChart(){
            const dataByType = this.dataByTypeAndDate;
            const series = [
                {
                    name: "Reviews",
                    marker: {
                        enabled: false
                    },
                    data: Object.keys(dataByType.reviews).map((key) => {
                        return [this.timestampForXAxis(key), dataByType.reviews[key]];
                    }),
                },
            ];
            return {
                chart: {
                    type: "areaspline",
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: "",
                },
                xAxis: {
                    type: "datetime",
                },
                yAxis: {
                    title: {
                        text: "Reviews",
                    },
                    labels: {
                        format: '{value}',
                    }
                },
                plotOptions: {
                    series: {
                        colorByPoint: true,
                        dataLabels: {
                            enabled: true
                        }
                    }
                },
                series,
            };
        }
    },
    methods: {
        isLoading(flag) {
            return this.loadingFlags.includes(flag);
        },
        showLoader(flag) {
            if (this.isLoading(flag)) {
                return;
            }
            this.loadingFlags.push(flag);
        },
        hideLoader(flag) {
            this.loadingFlags = this.loadingFlags.filter(itm => itm !== flag);
        },
        fetchData() {
            this.fetchAccountMetrics();
            this.fetchDailyAccountMetrics();
        },
        async fetchAccountMetrics(){
            this.showLoader('account_metrics');
            try {
                const { data } = await axios.get("/api/v1/insights/accounts/metrics",
                    {
                        params: {
                            metrics: [
                                "reviews",
                            ].join(","),
                            start: this.filter.start,
                            end: this.filter.end,
                            team: this.filter.team,
                            accounts: this.filter.accounts,
                            network: this.filter.network,
                        },
                    });
                this.accountMetrics = data.data;
            } catch (e) {
                axiosErrorHandler(e);
            }
            this.hideLoader('account_metrics');
        },
        async fetchDailyAccountMetrics(){
            this.showLoader('account_metrics_daily');
            try {
                const { data } = await axios.get("/api/v1/insights/accounts/metrics",
                    {
                        params: {
                            metrics: [
                                "desktop_map_views",
                                "mobile_map_views",
                                "actions_website",
                                "actions_phone",
                                "actions_driving_directions",
                            ].join(","),
                            start: this.filter.start,
                            end: this.filter.end,
                            team: this.filter.team,
                            accounts: this.filter.accounts,
                            network: this.filter.network,
                            calculate_growth: false, // because these metrics are already daily data
                        },
                    });
                this.accountMetricsDaily = data.data;
            } catch (e) {
                axiosErrorHandler(e);
            }
            this.hideLoader('account_metrics_daily');
        },
        getAccount(id) {
            const all = this.accounts;
            let account = {};
            all.forEach((acc) => {
                if (acc.id === id) account = acc;
            })
            return account;
        },
        timestampForXAxis(timestamp) {
            return this.$momentUTC(timestamp, "YYYY-MM-DD").valueOf();
        },
    },
}
</script>
