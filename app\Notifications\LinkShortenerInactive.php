<?php

namespace App\Notifications;

use App\LinkShortener;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class LinkShortenerInactive extends Notification
{
    use Queueable;

    protected $shortener;

    /**
     * Create a new notification instance.
     *
     * @param LinkShortener $linkShortener
     */
    public function __construct(LinkShortener $linkShortener)
    {
        $this->shortener = $linkShortener;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->error()
            ->subject('Link Shortener Connection Broken')
            ->line('Link Shortener connection ' . e($this->shortener->name) . ' (' . $this->shortener->getType() . ') is marked as inactive because connection to the service was broken.')
            ->line('Please reconnect to make sure your links are shortened automatically.')
            ->action('Fix Connection', url('app/link_shorteners'))
            ->line('Please do not hesitate to contact us if you need any assistance or have any questions.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'link_shortener_id' => $this->shortener->id,
            'link_shortener_name' => $this->shortener->name,
            '_level' => 'danger',
        ];
    }
}
