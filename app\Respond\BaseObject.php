<?php

namespace App\Respond;

use App\Account;
use App\InboxConversation;
use App\InboxConversationItem;
use Illuminate\Support\Str;

abstract class BaseObject
{
    private $account;

    private $ourModel;

    public final function __construct($ourModel, Account $account)
    {

        if ($ourModel instanceof InboxConversationItem) {
            $this->ourModel = $ourModel;
        } elseif ($ourModel instanceof InboxConversation) {
            $this->ourModel = $ourModel;
        } else {
            throw new \InvalidArgumentException('Invalid model type: ' . get_class($ourModel));
        }

        $this->account = $account;
    }

    /**
     * @return InboxConversation|InboxConversationItem
     */
    public final function getModel()
    {
        return $this->ourModel;
    }

    public final function getExternalId(): string
    {
        // exists for both conversation and conversation item
        return $this->getModel()->external_id;
    }

    public final function getConversation(): ?InboxConversation
    {
        if ($this->getModel() instanceof InboxConversation) {
            return $this->getModel();
        }

        return $this->getModel()->conversation;
    }

    public final function getData(): array
    {
        // exists for both conversation and conversation item
        return $this->getModel()->data;
    }

    public final function getAccount(): Account
    {
        return $this->account;
    }

    /**
     * Validate an action based on the action type
     * @param string $actionType The action type to perform (e.g., 'like', 'reply', 'delete')
     * @return void
     * @throws \Exception
     */
    public final function validate(string $actionType): void {

        $methodToCall = 'validate' . Str::studly($actionType);

        if (!method_exists($this, $methodToCall)) {
            return;
        }

        $args = array_slice(func_get_args(), 1);

        $this->$methodToCall(...$args);
    }

    /**
     * Perform an action based on the action type. This method will call the appropriate method
     * based on the action type. That method should be defined in the class that extends this class.
     * That method can also make changes to the underlying Conversation or ConversationItem model.
     * @param string $actionType The action type to perform (e.g., 'like', 'reply', 'delete')
     * @return array|null Returns an array with the response data or null if no response is needed
     */
    public final function perform(string $actionType): ?array {

        $methodToCall = 'perform' . Str::studly($actionType);

        if (!method_exists($this, $methodToCall)) {
            throw new \BadMethodCallException('Method does not exist: ' . $methodToCall);
        }

        $args = array_slice(func_get_args(), 1);

        // Call the method dynamically
        // This allows us to call methods like performLike, performReply, etc.
        // based on the action type passed in.
        // This method can also return an array with the response data
        $response = $this->$methodToCall(...$args);

        if(is_array($response)){
            // if the response is an array, we should return it
            // this is useful, for example, when we want to return a response from a comment
            // or a like
            return $response;
        } else {
            // if the response is null, we should return null
            // this is useful, for example, when we don't want to return anything
            // or when we just want to perform an action without returning anything
            return null;
        }
    }
}
