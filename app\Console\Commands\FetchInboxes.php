<?php

namespace App\Console\Commands;

use App\Account;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;

class FetchInboxes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'inbox:fetch';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch inboxes for accounts that need it.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        Account::whereActive(true)
            // fetch inboxes which were last fetched 1 minute ago
            ->where('inbox_fetched_at', '<=', Carbon::now()->subMinutes(1))
            ->orWhereNull('inbox_fetched_at')
            ->orderBy('inbox_fetched_at')
            ->chunk(1000, function ($accounts) {

                \Log::info('Cron: Need to fetch inboxes of ' . count($accounts) . ' accounts');

                // publish these posts
                foreach ($accounts as $account) {
                    /** @var Account $account */

                    // dispatch job
                    dispatch((new \App\Jobs\FetchInboxData($account))->onQueue('inbox'));
                }

            });
    }
}
