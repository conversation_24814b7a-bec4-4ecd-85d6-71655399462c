<?php

namespace App\Console\Commands;

use App\Feed;
use App\User;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;

class DeleteUserPermanently extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:delete';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Permanently delete deleted users';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     */
    public function handle()
    {

        User::onlyTrashed()->chunk(200, function($users){
            /** @var User[]|Collection $users */
            foreach($users as $user){
                /** @var Feed[]|Collection $feeds */
                $feeds = Feed::ofUser($user->id);

                if($feeds->count() === 0){
                    if(Feed::withTrashed()->ofUser($user->id)->count() === 0){
                        // can delete the user now
                        // if user is 30 days old?
                        if(now()->diffInDays($user->deleted_at) >= 30){
                            // delete the user
                            // check if promocodes need to be detached first, else gives error on delete
                            if($user->promocodes()->exists()){
                                $user->promocodes()->detach();
                            }
                            $user->forceDelete(); // related resources are also deleted (ON DELETE CASCADE)
                        }
                    }
                } else {

                    foreach ($feeds as $feed){
                        $feed->delete();
                    }

                }

            }
        });

    }
}
