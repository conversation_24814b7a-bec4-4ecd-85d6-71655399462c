<?php

namespace App\Observers;

use App\Helpers\EmailListHelper;
use App\PublishQueue;

class PublishQueueObserver
{

    public function created(PublishQueue $queue){
        // send event
        try {
            EmailListHelper::getInstance()->sendEvent($queue->user, 'queue_created', $queue->name);
        } catch (\Exception $exception){
            report($exception);
        }
    }

    /**
     * Listen to the deleting event.
     *
     * @param  PublishQueue  $queue
     * @return void
     */
    public function deleting(PublishQueue $queue)
    {
        // items will be auto deleted by cron
        // and then this queue will also be deleted
        // send event
        try {
            EmailListHelper::getInstance()->sendEvent($queue->user, 'queue_deleted', $queue->name);
        } catch (\Exception $exception){
            report($exception);
        }
    }
}
