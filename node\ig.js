const fs = require("fs-extra");

const {
    AndroidIgpapi,
    UserInfoCommand,
    StoryPostCommand,
    MediaCommentCommand,
    StoryVideoUploader,
    StoryPhotoUploader,
    TrayStoryConfiguration, AccountLoginCommand,
} = require("@igpapi/android");
const {
    HashtagSticker,
    IgpapiStickersLayout,
    LocationSticker,
    MentionSticker,
    StoryLinkSticker,
} = require("@igpapi/sticker");

const log = require("./logger")("ig helper");
const axios = require("axios");

module.exports = {
    // login by state
    async getApifromState(state) {
        const igpapi = new AndroidIgpapi({ state });
        igpapi.http.setTimeout(60000 * 8);
        return igpapi;
    },

    // login by username and password to refresh state
    async refreshLogin(ig, username, password) {
        return await ig.execute(AccountLoginCommand, {
            username,
            password
        });
    },

    // get user info
    async getCurrentUser(/** @type {AndroidIgpapi} */ ig) {
        return await ig.account.currentUser();
    },

    // publish post
    async publishPost(
        /** @type {AndroidIgpapi} */ ig,
        data = {
            caption: "",
            media: [
                // if multiple, then its a carousel
                {
                    type: "image", // image or video
                    userTags: [], // only for user tags... { user_id: pk, position: [x y] }
                    mediaUrl: "",
                    mediaPath: null, // if media is local file
                    coverUrl: "", // only for video
                    coverPath: null // if cover is local file
                }
            ],
            location: null, // only for location
        }
    ) {

        if (data.media.length > 10) {
            throw new Error("Maximum of 10 media per post");
        }

        const mediaProcessed = [];
        for (let i in data.media){
            const m = data.media[i];
            let mediaBuffer = null;
            if (m.mediaUrl) {
                const res = await axios.get(m.mediaUrl, {
                    responseType: "arraybuffer"
                });
                mediaBuffer = Buffer.from(res.data, "binary");
            } else {
                mediaBuffer = await fs.readFile(m.mediaPath);
            }

            const ret = {
                ...m,
                mediaBuffer
            };

            // get cover image for video
            if (m.type === "video" && (m.coverUrl || m.coverPath)) {
                let coverBuffer = null;
                if (m.coverUrl) {
                    const res = await axios.get(m.coverUrl, {
                        responseType: "arraybuffer"
                    });
                    coverBuffer = Buffer.from(res.data, "binary");
                } else {
                    coverBuffer = await fs.readFile(m.coverPath);
                }
                ret.coverBuffer = coverBuffer;
            }

            mediaProcessed.push(ret);
        }
        data.media = mediaProcessed;

        const { media, caption, location } = data;

        let postOptions = {};

        postOptions.caption = "" + caption;

        if (location) {
            // we have lat, lng, title
            if(!location.address){
                // so find more data
                const locations = await ig.search.location(location.lat, location.lng, location.title);
                if(locations.length > 0){
                    location.address = locations[0].address;
                    location.name = locations[0].name;
                    location.external_id = locations[0].external_id;
                    location.external_id_source = locations[0].external_id_source

                    postOptions.location = {
                        name: location.name,
                        lat: location.lat,
                        lng: location.lng,
                        address: location.address,
                        external_id_source: location.external_id_source,
                        external_id: location.external_id
                    };
                }
            }
        }

        let publishResult;
        if (media.length === 1) {
            // one media = post
            if (media[0].type === "video") {

                if(media[0].usertags){
                    postOptions.usertags = media[0].userTags;
                }

                publishResult = await ig.publish.video({
                    video: media[0].mediaBuffer,
                    coverImage: media[0].coverBuffer,
                    ...postOptions
                });
            } else {
  
                if(media[0].alt && media[0].alt.length){
                    postOptions.customAccessibilityCaption = media[0].alt;
                }
                if(media[0].usertags){
                    postOptions.usertags = media[0].userTags;
                }
                publishResult = await ig.posting.send.photo({
                    file: media[0].mediaBuffer,
                    ...postOptions
                });
            }
        } else {
            // multi media = album
            publishResult = await ig.posting.send.album({
                items: media.map(m => {
                    const albumPostOption = {};
                    if(m.usertags){
                        albumPostOption.usertags = m.userTags;
                    }
                    if(m.type === "video"){
                        return ig.posting.album.video({
                            video: m.mediaBuffer,
                            cover: m.coverBuffer,
                            ...albumPostOption
                        });
                    } else {
                        return ig.posting.album.photo({
                            file: m.mediaBuffer,
                            ...albumPostOption
                        });
                    }
                }),
                ...postOptions
            });
        }

        return publishResult;
    },

    // publish story
    async publishStory(
        /** @type {AndroidIgpapi} */ ig,
        data = {
            type: "image",
            mediaUrl: null,
            mediaPath: null,
            stickers: []
        }
    ) {

        const { type, mediaUrl, mediaPath, stickers } = data;

        let mediaBuffer = null;
        if (mediaUrl) {
            const res = await axios.get(mediaUrl, {
                responseType: "arraybuffer"
            });
            mediaBuffer = Buffer.from(res.data, "binary");
        } else {
            mediaBuffer = await fs.readFile(mediaPath);
        }

        const mediaStickers = new IgpapiStickersLayout();

        // add all sticker configs
        (stickers || []).forEach(stickerData => {
            const stickerPosition = {
                width: stickerData.width,
                height: stickerData.height,
                x: stickerData.x,
                y: stickerData.y,
                rotation: stickerData.rotation
            };

            if (stickerData.type === "hashtag") {
                mediaStickers.add(
                    new HashtagSticker({
                        tag_name: stickerData.data.hashtag
                    }),
                    stickerPosition
                );
            } else if (stickerData.type === "location") {
                mediaStickers.add(
                    new LocationSticker({
                        location_id: stickerData.data.location_id
                    }),
                    stickerPosition
                );
            } else if (stickerData.type === "mention") {
                mediaStickers.add(
                    new MentionSticker({
                        user_id: stickerData.data.user_id
                    }),
                    stickerPosition
                );
            } else if (stickerData.type === "link") {
                mediaStickers.add(
                    new StoryLinkSticker({
                        url: stickerData.data.url
                    }),
                    stickerPosition
                );
            }
        });

        return await ig.execute(StoryPostCommand, {
            media:
                type === "video"
                    ? ig.instantiate(StoryVideoUploader, {
                        video: mediaBuffer
                    })
                    : ig.instantiate(StoryPhotoUploader, {
                        file: mediaBuffer
                    }),
            configuration: new TrayStoryConfiguration(),
            stickers: mediaStickers
        });
    },

    // get current user details
    async getUser(/** @type {AndroidIgpapi} */ ig, userId = null) {
        if(!userId) {
            userId = ig.state.extractUserId();
        }
        const { user } = await ig.execute(UserInfoCommand, { userId: ig.state.extractUserId() });
        return user;
    },

    // add comment to a media
    async addComment(/** @type {AndroidIgpapi} */ ig, data) {

        const { mediaId, text } = data;

        return await ig.execute(MediaCommentCommand, {
            mediaId,
            text,
        });
    },

    async getLocations(/** @type {AndroidIgpapi} */ ig, data) {

        const places = await ig.search.places(data.query);

        return places.map(p => {
            const { location } = p;
            return {
                title: location.name,
                subtitle: location.address,
                fb_id: location.facebook_places_id,
                lat: location.lat,
                lng: location.lng,
                pk: location.pk,
            }
        });

    },

    async getUsers(/** @type {AndroidIgpapi} */ ig, data) {

        const users = await ig.search.users(data.query);

        return users.map(u => {
            return {
                title: u.full_name,
                name: u.full_name,
                username: u.username,
                pk: u.pk,
                image: u.profile_pic_url,
            };
        });

    },

    shouldRetry(/** @type {Error} */ error) {
        return error.message.includes("429") || error.message.includes("socket hang up") || error.message.includes("ECONNRESET") || error.message.includes("ECONNREFUSED") || error.message.includes("network socket disconnected");
    }

};
