<?php

namespace App\Http\Controllers\Api;

use App\Tag;
use App\InboxConversation;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class TagController extends Controller
{
    public function getTags(Request $request)
    {
        $this->validate($request, [
            'team_id' => 'integer|exists:teams,id',
            'user_id' => 'integer|exists:users,id'
        ]);

        $user_id = $request->get('user_id') ?? user()->id;

        $tags = Tag::when($request->get('team_id'), function($query, $team_id){
            return $query->where('team_id', $team_id);
        })
        ->where('user_id', $user_id)
        ->get();

        return response()->json($tags);
    }

    /**
     * Create a new tag
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'name' => 'required|string|max:50|min:2',
            'team_id' => 'integer|exists:teams,id',
            'user_id' => 'integer|exists:users,id'
        ]);

        $user_id = $request->get('user_id') ?? user()->id;
        
        $tag = Tag::when($request->get('team_id'), function($query, $team_id){
                return $query->where('team_id', $team_id);
            })
            ->where('name', $request->get('name'))
            ->where('user_id', $user_id)
            ->first();
        
        if($tag){
            abort(400, 'Tag already exists');
        }

        $tag = new Tag([
            'name' => $request->get('name'),
            'user_id' => user()->id,
            'team_id' => $request->get('team_id') ?? null
        ]);

        $tag->save();

        return response()->json([
            'message' => 'Tag created successfully',
            'tag' => $tag->name
        ]);
    }

    /**
     * Add tags to a conversation
     *
     * @param Request $request
     * @return mixed
     */
    public function updateConversationTags(Request $request){
        
        $this->validate($request, [
            'convo_id' => 'required|integer|exists:inbox_conversations,id',
            'tags' => 'required|array',
            'tags.*' => 'integer|exists:tags,id',
        ]);

        $tags = $request->get('tags');
        \Log::info($tags);

        $conversation = InboxConversation::userConversations()->findOrFail($request->get('convo_id'));

        $conversation->tags()->sync($tags);
        $conversation->save();

        return response()->json([
            'message' => 'Tags added successfully'
        ]);
    }

    /**
     * 
     */
    public function destroy(Request $request, $id)
    {
        $tag = Tag::findOrFail($id);

        //rmv tag from all conversations
        $tag->conversations()->detach();

        $tag->delete();



        return response()->json(['message' => 'Tag deleted']);
    }
}
