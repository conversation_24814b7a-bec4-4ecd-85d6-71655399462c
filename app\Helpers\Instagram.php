<?php

namespace App\Helpers;

use Facebook\Exceptions\FacebookSDKException;
use Facebook\Facebook;
use Facebook\HttpClients\HttpClientsFactory;

class Instagram extends Facebook
{
    /**
     * @throws FacebookSDKException
     * @throws \Exception
     */
    public function __construct($config)
    {
        parent::__construct($config);
        // use our custom InstagramClient
        $this->client = new InstagramClient(
            HttpClientsFactory::createHttpClient($config['http_client_handler']),
            $config['enable_beta_mode'] ?? false
        );
    }
}
