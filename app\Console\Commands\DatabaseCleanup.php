<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class DatabaseCleanup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'socialbu:cleanup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'DB cleanup';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        // delete failed jobs related to ModelNotFound
        \DB::delete('DELETE FROM `failed_jobs` WHERE `exception` like \'%ModelNotFound%\'');

        // should not delete email jobs
//        \DB::delete('DELETE FROM `failed_jobs` WHERE `exception` like \'%EmailListHelper%\'');
//        \DB::delete('DELETE FROM `failed_jobs` WHERE `exception` like \'%mailgun.net%\'');

        // we should not delete failed jobs related to MaxAttemptsExceededException
//        \DB::delete('DELETE FROM `failed_jobs` WHERE `exception` like \'%MaxAttemptsExceededException%\'');

        \DB::delete('DELETE FROM `failed_jobs` WHERE `exception` like \'%AddCurationItem%\'');

        // happens when a failed job is related to a deleted class
        \DB::delete('DELETE FROM `failed_jobs` WHERE `exception` like \'%incomplete object%\'');
    }
}
