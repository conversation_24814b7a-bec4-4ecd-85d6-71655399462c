<?php

namespace App\Jobs;

use App\Account;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class RefreshAccountPic implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $account;

    /**
     * Delete the job if its models no longer exist.
     *
     * @var bool
     */
    public $deleteWhenMissingModels = true;

    /**
     * Create a new job instance.
     *
     * @param Account $account
     */
    public function __construct(Account $account)
    {
        $this->account = $account;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        \Redis::funnel('refresh_pic::' . $this->account->id)
            ->releaseAfter(60 * 5) // 5 minutes
            ->limit(1)
            ->then(function () {
                try {
                    $this->account->getProfilePic(true); // will refresh img if needed
                } catch (\Exception $exception){
                    // do nothing
                    \Log::error($exception->getMessage());
                }
            }, function () {
                // Could not obtain lock...
                $this->release(10);
            });
    }
}
