<?php

namespace App\Http\Controllers\Webhook;

use App\Helpers\EmailListHelper;
use App\User;
use Carbon\Carbon;
use GuzzleHttp\RequestOptions;
use <PERSON><PERSON>\Cashier\Http\Controllers\WebhookController;
use <PERSON><PERSON>\Cashier\Subscription;
use Symfony\Component\HttpFoundation\Response;

class StripeController extends WebhookController
{

    /**
     * Handle invoice payment success.
     *
     * @param  array $payload
     * @return \Symfony\Component\HttpFoundation\Response
     */
    protected function handleInvoicePaymentSucceeded(array $payload)
    {

        $cents_paid = $payload['data']['object']['total'];

        $user = null;
        if($cents_paid > 0) {

            /** @var User|null $user */
            if ($user = $this->getUserByStripeId($payload['data']['object']['customer'])) {

                // referral earnings if any
                if($user->referred_by && $user->referred_by > 0){

                    /** @var User $referredByUser */
                    $referredByUser = $user->referrer;

                    $total_payments_count = $user->getOption('total_payments_count', 0);

                    $user->setOption('total_payments_count', $total_payments_count + 1);

                    // credit the referrer
                    $referrerEarnings = $referredByUser->getOption('referrals_earning');
                    if(!$referrerEarnings)
                        $referrerEarnings = 0;

                    if($referredByUser->getOption('is_affiliate')){
                        $commission = $cents_paid * 0.2; // 20% flat on every payment for affiliate
                    }else {
                        if(($total_payments_count + 1) === 1){
                            $commission = $cents_paid * 0.5; // 50% on first payment
                        }else if(($total_payments_count + 1) > 1){
                            $commission = $cents_paid * 0.1; // 10%
                        }
                    }

                    $referrerEarnings = $referrerEarnings + $commission;

                    $referredByUser->setOption('referrals_earning', $referrerEarnings);
                }


                // subscribe user to email list
                try {
                    EmailListHelper::getInstance()->syncUserContact($user);
                } catch (\Exception $exception) {
                    report($exception);
                }

                try {
                    EmailListHelper::getInstance()->sendEvent($user, 'payment_succeeded', $cents_paid / 100);
                } catch (\Exception $exception){
                    report($exception);
                }

            }

        }

        return $this->successMethod();
    }

    /**
     * Handle a customer subscription created.
     *
     * @param  array $payload
     * @return \Symfony\Component\HttpFoundation\Response
     */
    protected function handleCustomerSubscriptionCreated(array $payload)
    {
        /** @var User|null $user */
        if ($user = $this->getUserByStripeId($payload['data']['object']['customer'])) {
            // subscribe user to email list
            try {
                EmailListHelper::getInstance()->syncUserContact($user);
            } catch (\Exception $exception){
                report($exception);
            }

            try {
                EmailListHelper::getInstance()->sendEvent($user, 'subscription_created', $user->getPlan(true));
            } catch (\Exception $exception){
                report($exception);
            }

            notify_chat('🟢 New subscription for user: ' . $user->email);
        }

        return $this->successMethod();
    }

    /**
     * Handle a cancelled customer from a Stripe subscription.
     *
     * @param  array  $payload
     * @return \Symfony\Component\HttpFoundation\Response
     */
    protected function handleCustomerSubscriptionDeleted(array $payload)
    {
        /** @var User|null $user */
        $user = $this->getUserByStripeId($payload['data']['object']['customer']);

        $res = parent::handleCustomerSubscriptionDeleted($payload);

        // after downgrade
        if($user){

            // update eml lists
            try {
                EmailListHelper::getInstance()->syncUserContact($user);
            } catch (\Exception $exception){
                report($exception);
            }

            if(!$user->canUsePlan($user->getPlan(true))){
                // user is in trouble because we just freeze everything if resources are too much for current plan,
                // so we behave good and gently notify the user via email
                // and downgrade their resources
                $user->downgradeExtraResources();
            }

            notify_chat('🟡 Subscription ended for user: ' . $user->email);

            try {
                EmailListHelper::getInstance()->sendEvent($user, 'subscription_ended');
            } catch (\Exception $exception){
                report($exception);
            }

        }

        return $res;
    }


    /**
     * Handle customer subscription updated.
     *
     * @param  array $payload
     * @return \Symfony\Component\HttpFoundation\Response
     */
    protected function handleCustomerSubscriptionUpdated(array $payload)
    {
        /** @var User|null $user */
        $user = $this->getUserByStripeId($payload['data']['object']['customer']);

        $res = parent::handleCustomerSubscriptionUpdated($payload);

        if ($user) {
            // sync user contact
            try {
                EmailListHelper::getInstance()->syncUserContact($user);
            } catch (\Exception $exception){
                report($exception);
            }

        }

        return $res;
    }

    /**
     * Handle customer updated.
     *
     * @param  array $payload
     * @return \Symfony\Component\HttpFoundation\Response
     */
    protected function handleCustomerUpdated(array $payload)
    {
        /** @var User $user */
        $user = $this->getUserByStripeId($payload['data']['object']['id']);

        $res = parent::handleCustomerUpdated($payload);

        if ($user) {
            // sync user contact
            try {
                EmailListHelper::getInstance()->syncUserContact($user);
            } catch (\Exception $exception){
                report($exception);
            }

            try {
                EmailListHelper::getInstance()->sendEvent($user, 'payment_profile_updated');
            } catch (\Exception $exception){
                report($exception);
            }

        }

        return $res;
    }

    /**
     * Handle deleted customer.
     *
     * @param  array $payload
     * @return \Symfony\Component\HttpFoundation\Response
     */
    protected function handleCustomerDeleted(array $payload)
    {
        /** @var User|null $user */
        $user = $this->getUserByStripeId($payload['data']['object']['id']);

        $res = parent::handleCustomerDeleted($payload);

        if ($user) {
            // sync user contact
            try {
                EmailListHelper::getInstance()->syncUserContact($user);
            } catch (\Exception $exception){
                report($exception);
            }
        }

        return $res;
    }

}
