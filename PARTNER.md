# SocialBu partners

## Partners showcase
We can show our partners at socialbu.com/about/partners

## API partners
We have dedicated endpoints for partners (/api/partners/*). 

### Access
- API access is granted by adding a partner oauth client to the database. 
- API authentication: Oauth2 -  client credentials grant

#### Creating client
```shell
php artisan passport:client
```

#### Getting access token (example PHP code)
```php
$guzzle = new GuzzleHttp\Client;
 
$response = $guzzle->post('https://socialbu.com/oauth/token', [
    'form_params' => [
        'grant_type' => 'client_credentials',
        'client_id' => 'client-id',
        'client_secret' => 'client-secret',
    ],
]);
 
$token = json_decode((string) $response->getBody(), true)['access_token'];

echo $token;
```

### API endpoints
#### Generate content by topic
- endpoint: /api/v1/partners/content_by_topic
- request type: POST
- request body: topic={your topic here}
- response json: { success: true, data: { text: "..." } }
#### Autocomplete content
- endpoint: /api/v1/partners/autocomplete
- request type: POST
- request body: content={content you want to autocomplete}
- response json: { success: true, data: { text: "..." } }
#### Generate text2img prompt
- endpoint: /api/v1/partners/text2img_prompt
- request type: POST
- request body: content={content you want the prompt for}
- response json: { success: true, data: { text: "..." } }
