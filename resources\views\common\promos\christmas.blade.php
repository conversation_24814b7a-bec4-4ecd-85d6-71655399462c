@extends('layout.full_width')
@php($title = 'Happy Holidays')
@php($description = 'SocialBu team wishes you a Merry Christmas and a happy new year. Accept our discount-gift of 25% off on all monthly plans and 50% off on all yearly plans.')
@php($image = 'https://socialbu.com/images/site/link-preview.jpg')
@php($url = 'https://socialbu.com/')
@section('title', $title . ' | ' . config('app.name'))
@push('head_html')
    <meta name="description" content="{{ $description }}"/>
    <link rel="canonical" href="{{ $url }}" />

    <meta property="og:locale" content="en_US" />
    <!--
    <meta property="og:type" content="website" />
    -->
    <meta property="og:title" content="{{ $title }}" />
    <meta property="og:description" content="{{ $description }}" />
    <meta property="og:url" content="{{ $url }}" />
    <meta property="og:site_name" content="SocialBu" />
    <meta property="og:image" content="{{ $image }}" />
    <meta property="fb:app_id" content="{{ config('services.facebook.client_id') }}" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content="{{ $image }}" />
    <meta name="twitter:title" content="{{ $title }}" />
    <meta name="twitter:description" content="{{ $description }}" />
    <meta name="twitter:site" content="@socialbuapp" />

    <link href="https://fonts.googleapis.com/css?family=Gaegu" rel="stylesheet" />
    <style>
        .font-gaegu {
            font-family: "Gaegu", sans-serif;
        }
        .red {
            color: #c33134 !important;
        }
        .red-btn {
            background-color: #c33134 !important;
            border-color: #c33134 !important;
        }
        .red-bg {
            background-color: #c33134 !important;
        }
        .red-border {
            border-color: #c33134 !important;

        }

        .plan_type_input label:not(.active) {
            opacity: 0.7;
            color: #fff !important;
        }


        /* snow effect */
        .svg-snowscene {
            width: 100%;
            bottom: -400px;
            min-height: 400px;
        }

        .snow {
            fill: #FFFFFF;
            animation-name: snowing;
            animation-duration: 3s;
            animation-iteration-count: infinite;
            animation-timing-function: ease-out;
        }
        .snow:nth-child(2n) {
            animation-delay: 1.5s;
        }
        .snow:nth-child(3n) {
            animation-delay: 2.3s;
            animation-duration: 3.3s;
        }
        .snow:nth-child(4n) {
            animation-delay: 0.8s;
            animation-duration: 3.2s;
        }
        .snow:nth-child(5n) {
            animation-delay: 2.8s;
        }

        @keyframes snowing {
            0% {
                fill-opacity: 1;
            }
            100% {
                fill-opacity: 0;
                transform: translateY(200px);
            }
        }

        .lightrope {
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            position: absolute;
            z-index: 1;
            margin: -15px 0 0 0;
            padding: 0;
            pointer-events: none;
            width: 100%;
            top: 90px;
        }
        .lightrope li {
            position: relative;
            -webkit-animation-fill-mode: both;
            animation-fill-mode: both;
            -webkit-animation-iteration-count: infinite;
            animation-iteration-count: infinite;
            list-style: none;
            margin: 0;
            padding: 0;
            display: block;
            width: 12px;
            height: 28px;
            border-radius: 50%;
            margin: 20px;
            display: inline-block;
            background: #00f7a5;
            box-shadow: 0px 4.6666666667px 24px 3px #00f7a5;
            -webkit-animation-name: flash-1;
            animation-name: flash-1;
            -webkit-animation-duration: 2s;
            animation-duration: 2s;
        }
        .lightrope li:nth-child(2n+1) {
            background: cyan;
            box-shadow: 0px 4.6666666667px 24px 3px rgba(0, 255, 255, 0.5);
            -webkit-animation-name: flash-2;
            animation-name: flash-2;
            -webkit-animation-duration: 0.4s;
            animation-duration: 0.4s;
        }
        .lightrope li:nth-child(4n+2) {
            background: #f70094;
            box-shadow: 0px 4.6666666667px 24px 3px #f70094;
            -webkit-animation-name: flash-3;
            animation-name: flash-3;
            -webkit-animation-duration: 1.1s;
            animation-duration: 1.1s;
        }
        .lightrope li:nth-child(odd) {
            -webkit-animation-duration: 1.8s;
            animation-duration: 1.8s;
        }
        .lightrope li:nth-child(3n+1) {
            -webkit-animation-duration: 1.4s;
            animation-duration: 1.4s;
        }
        .lightrope li:before {
            content: "";
            position: absolute;
            background: #222;
            width: 10px;
            height: 9.3333333333px;
            border-radius: 3px;
            top: -4.6666666667px;
            left: 1px;
        }
        .lightrope li:after {
            content: "";
            top: -14px;
            left: 9px;
            position: absolute;
            width: 52px;
            height: 18.6666666667px;
            border-bottom: solid #222 2px;
            border-radius: 50%;
        }
        .lightrope li:last-child:after {
            content: none;
        }
        .lightrope li:first-child {
            margin-left: -40px;
        }

        @-webkit-keyframes flash-1 {
            0%, 100% {
                background:  #F8B229;
                box-shadow: 0px 4.6666666667px 24px 3px  #F8B229;
            }
            50% {
                background: rgba(0, 247, 165, 0.4);
                box-shadow: 0px 4.6666666667px 24px 3px rgba(0, 247, 165, 0.2);
            }
        }

        @keyframes flash-1 {
            0%, 100% {
                background:  #F8B229;
                box-shadow: 0px 4.6666666667px 24px 3px  #F8B229;
            }
            50% {
                background: rgba(0, 247, 165, 0.4);
                box-shadow: 0px 4.6666666667px 24px 3px rgba(0, 247, 165, 0.2);
            }
        }
        @-webkit-keyframes flash-2 {
            0%, 100% {
                background: cyan;
                box-shadow: 0px 4.6666666667px 24px 3px cyan;
            }
            50% {
                background: rgba(0, 255, 255, 0.4);
                box-shadow: 0px 4.6666666667px 24px 3px rgba(0, 255, 255, 0.2);
            }
        }
        @keyframes flash-2 {
            0%, 100% {
                background: #146B3A;
                box-shadow: 0px 4.6666666667px 24px 3px #146B3A;
            }
            50% {
                background: #16C326;
                box-shadow: 0px 4.6666666667px 24px 3px #16C326;
            }
        }
        @-webkit-keyframes flash-3 {
            0%, 100% {
                background:  #f70094;
                box-shadow: 0px 4.6666666667px 24px 3px  #f70094;
            }
            50% {
                background: #C01B0E;
                box-shadow: 0px 4.6666666667px 24px 3px #C01B0E;
            }
        }
        @keyframes flash-3 {
            0%, 100% {
                background: #f70094;
                box-shadow: 0px 4.6666666667px 24px 3px #f70094;
            }
            50% {
                background: #C01B0E;
                box-shadow: 0px 4.6666666667px 24px 3px #C01B0E;
            }
        }
    </style>
@endpush
@push('footer_html')
    <script>
        // snow effect
        (function(){
            function getRandom(min, max) {
                return Math.random() * (max - min) + min;
            }

            (function letItSnow(){
                var snowflakes = document.querySelectorAll('.snow');
                for (var i = 0; i < snowflakes.length; i++) {
                    var snowflake = snowflakes[i];
                    snowflake.setAttribute('cx', getRandom(1,100) + '%');
                    snowflake.setAttribute('cy', '-' + getRandom(1,100));
                    snowflake.setAttribute('r', getRandom(1,8));
                }
            })();

        })();
        $(function(){
            $("#navbar_main a").attr("target", "_blank");
            $(".section-header h2").addClass("red font-gaegu");
            $("section h2 strong").addClass("red font-gaegu");
            $("#main .btn-primary").addClass("red-btn");
            $(".footer").addClass("bg-secondary").removeClass("bg-light");
        });
    </script>
@endpush
@section('content')
    <header class="header header-main pt-9 pt-md-10 pb-6 pb-md-8">
        <ul class="lightrope">
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
            <li></li>
        </ul>
        <div class="container pb-10" style="background-image: url('/images/site/promos/christmas.png'); background-size: contain; background-position:bottom; background-repeat: no-repeat;">

            <div class="row align-items-center h-100">

                <div class="col-12 text-center py-7" style="">
                    <h1 class="red font-weight-600 font-gaegu">
                        Happy Holidays
                    </h1>
                    <p class="lead mt-5 mb-6 lead-2" style="background-color: rgba(255,255,255,.7);">
                        SocialBu team wishes you a Merry Christmas and a happy new year.
                        <br/>
                        Accept our gift of
                        @if (url()->current() !== route('christmas-yearly-sale')) 
                            up to
                        @else
                            flat
                        @endif
                        50% discount for new subscriptions.
                    </p>
                    <a class="red-btn btn btn-xl btn-round btn-primary px-7" href="#discounts">
                        Get your discount today 
                    </a>
                </div>

            </div>
        </div>
    </header>


    <main class="main-content text-dark" id="main">

        <section class="section py-7 overflow-hidden bg-socialbu-light border-top border-bottom">
            <div class="container">
                <div class="row gap-y align-items-center">


                    <div class="col-md-4">
                        <img src="/images/site/promos/santa.png" alt="santa" data-aos="fade-left" class="aos-init aos-animate lozad" />
                    </div>

                    <div class="col-md-8">
                        <h2 class="h4 font-gaegu red">
                            The Only Discount You Need this Year
                        </h2>
                        <p>
                            Fill your holidays with cheer, joyful decoration, and an unforgettable experience. Enjoy your holidays with your loved ones without having to worry about your social media accounts.
                            <br/>
                            Let SocialBu take care of your social media management. Schedule content, manage your messages, and automate your social media.
                        </p>
                    </div>

                </div>
            </div>
        </section>

        <section class="section" id="discounts">
            <div class="container">
                <header class="section-header">
                    <h2 class="text-center font-gaegu red">Special Pricing</h2>
                    <p class="lead text-center text-muted">
                            50% off on one yearly payment
                        <br/>
                        @if (url()->current() !== route('christmas-yearly-sale'))
                            30% off on 4 monthly payments
                        @endif
                    </p>
                    @if (url()->current() !== route('christmas-yearly-sale'))
                    <div class="text-center py-6">
                        <div class="btn-group btn-group-toggle cursor-pointer plan_type_input bg-white" data-toggle="buttons">
                            <label class="btn btn-round red-bg btn-outline-danger cursor-pointer w-150">
                                    <input type="radio" name="pricing-1" value="monthly" autocomplete="off"> Monthly
                                </label>
                            <label class="btn btn-round red-bg btn-outline-danger cursor-pointer w-150 active">
                                <input type="radio" name="pricing-1" value="yearly" autocomplete="off" checked=""> Yearly
                            </label>
                        </div>
                    </div>
                    @endif
                </header>

                @foreach(['standard2', 'super', 'supreme'] as $planId)
                    <div class="row no-gutters border-red pricing-4 red-border">
                        <div class="col-md-9 plan-description border-right red-border">
                            <h2 class="red font-gaegu font-weight-bold">
                                {{ config('plans.' . $planId . '.name') }}
                                <span class="badge badge-light">
                                <span class="yearly">
                                    was ${{ config('plans.' . $planId . '.price_yearly') }} /yr
                                </span>
                                <span class="monthly" style="display: none">
                                    was ${{ config('plans.' . $planId . '.price') }} /mo
                                </span>
                            </span>
                            </h2>
                            <p class="mb-0">
                                {{ config('plans.' . $planId . '.limits.accounts') }} accounts, {{ config('plans.' . $planId . '.limits.teams') }} teams |
                                {{ [
                                    'starter' => 'for those looking to experiment',
                                    'standard2' => 'best for individuals',
                                    'super' => 'best for small businesses',
                                    'supreme' => 'best for agencies',
                                ][$planId] }}
                            </p>
                        </div>

                        <div class="col-md-3 plan-price">
                            <h3 class="h3 pt-0">
                                <div class="yearly">
                                <span class=" font-gaegu red">
                                    ${{ config('plans.' . $planId . '.price_yearly') * .5 }}
                                </span>
                                    <small class="red">/ yr</small>
                                </div>
                                <div class="monthly" style="display: none">
                                <span class=" font-gaegu red">
                                    ${{ config('plans.' . $planId . '.price') * .70 }}
                                </span>
                                    <small class="red">/ mo</small>
                                </div>
                            </h3>
                            <a class="btn btn-primary red-btn monthly" style="display: none" href="https://socialbu.com/app?promo=WgDObu94">
                                Get Now
                            </a>
                            <a class="btn btn-primary red-btn yearly" href="https://socialbu.com/app?promo=oNO08kO2">
                                Get Now
                            </a>
                            <br/>
                            <a class="btn btn-link text-muted" href="/pricing#features" target="_blank">Plan Details</a>
                        </div>
                    </div>
                @endforeach

            </div>
        </section>

        <section class="section testimonial-section">
            <div class="container">
                <div class="row gap-y justify-content-center align-items-center">
                    <div class="col-12 text-center py-0">
                        <h2 class="display-2 mb-0"> High-fives From Community</h2>
                        <p class="d-md-block d-none mt-4 pt-md-1 mb-0 lead-2">Testimonials and reviews from users</p>
                        <p class="d-md-none mt-3 mb-3">Testimonials and reviews from users</p>
                    </div>
                    @include('common.internal.testimonials-block')
                </div>
            </div>
        </section>
        @include('common.internal.join-us-block')

        <section class="section" style="z-index: -1">
            <div class="container">
                <div class="row">
                    <div class="col h-300 position-absolute" style="background-image: url('/images/site/promos/christmas.png');background-size: contain;background-position:top;background-repeat: no-repeat;top: -300px;">
                        &nbsp;
                    </div>
                </div>
            </div>
        </section>

    </main>

    <!-- snow for footer -->
    <section class="section p-0">
        <svg class="svg-snowscene position-absolute" xmlns="http://www.w3.org/2000/svg">
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
            <circle class="snow" />
        </svg>
    </section>

@endsection
