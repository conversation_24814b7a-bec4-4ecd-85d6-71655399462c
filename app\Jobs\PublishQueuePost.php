<?php

namespace App\Jobs;

use App\PublishQueue;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class PublishQueuePost implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $publishQueue;

    /**
     * Delete the job if its models no longer exist.
     *
     * @var bool
     */
    public $deleteWhenMissingModels = true;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(PublishQueue $publishQueue)
    {
        $this->publishQueue = $publishQueue;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Exception
     */
    public function handle()
    {
        $publishQueue = $this->publishQueue;

        if($publishQueue->getOption('is_publishing')){
            $ts_old = $publishQueue->getOption('is_publishing_since');

            $tsC = \Carbon\Carbon::createFromTimestamp($ts_old);
            $now = now();

            if($tsC->diffInSeconds($now) > 60 * 5){
                // if it has been more than 5 minutes, reset the flag
                $publishQueue->setOption('is_publishing', false);
            }

            // just queue the job again
            dispatch((new self($publishQueue))->onQueue('posts')->delay(now()->addSeconds( 30 ))); // retry after x seconds
            return;
        }

        $publishQueue->setMultipleOptions([
            'is_publishing' => true,
            'is_publishing_since' => time(),
        ]);

        try {
            // actually publish from queue
            $publishQueue->publish(true);
        } finally {
            $publishQueue->removeMultipleOptions([
                'is_publishing',
                'is_publishing_since',
            ]);
        }

    }
}
