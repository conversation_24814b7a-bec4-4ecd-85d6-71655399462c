<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>

    <title>Invoice #{{ $id ?? $invoice->id }}</title>

    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            background: #fff;
            background-image: none;
            font-size: 12px;
            font-family: DejaVu Sans, sans-serif;
        }
        address{
            margin-top:15px;
        }
        h2 {
            font-size:28px;
            color:#cccccc;
        }
        .container {
            padding-top:30px;
        }
        .invoice-head td {
            padding: 0 8px;
        }
        .invoice-body{
            background-color:transparent;
        }
        .logo {
            padding-bottom: 10px;
        }
        .table th {
            vertical-align: bottom;
            font-weight: bold;
            padding: 8px;
            line-height: 20px;
            text-align: left;
        }
        .table td {
            padding: 8px;
            line-height: 20px;
            text-align: left;
            vertical-align: top;
            border-top: 1px solid #dddddd;
        }
        .well {
            margin-top: 15px;
        }
    </style>
</head>

<body>
<div class="container">
    <table style="margin-left: auto; margin-right: auto" width="550">
        <tr>
            <td style="font-size:28px;color:#666666;font-weight: bold;">
                Receipt<!--<br/>
                <span style="font-size:12px;">
                     Invoice ID: {{ $id ?? $invoice->id }}
                </span> -->
            </td>

            <!-- Organization Name / Image -->
            <td align="right">
                <img alt="{{$vendor}}" width="160" height="auto" src="data:image/png;base64,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" />
                <!--
                <strong>SocialBu</strong>
                -->
            </td>
        </tr>
        <tr>
            <td>
                <strong>Invoice ID:</strong> {{ $id ?? $invoice->id }}
                <br/>
                <br/>
                <strong>To:</strong> {{ $owner->company && !empty($owner->company) ? $owner->company : ($owner->name && !empty($owner->name) ? $owner->name : $owner->email) }}

                @if (isset($lines_after_to) && !empty($lines_after_to))
                    @foreach($lines_after_to as $line)
                        {{ $line }}<br>
                    @endforeach
                @endif

                <br>
                <strong>Date:</strong> {{ $invoice->date()->toFormattedDateString() }}

            </td>

            <!-- Organization Name / Image -->
            <td align="right">
                @if (isset($lines_after_vendor) && !empty($lines_after_vendor))
                    @foreach($lines_after_vendor as $line)
                        {{ $line }}<br>
                    @endforeach
                @endif
                <br/>
                @if (isset($street))
                    {{ $street }}<br>
                @endif
                @if (isset($location))
                    {{ $location }}<br>
                @endif
                @if (isset($address) && !empty($address))
                    @foreach($address as $line)
                        {{ $line }}<br>
                    @endforeach
                @endif
                @if (isset($phone))
                    <strong>T</strong> {{ $phone }}<br>
                @endif
                @if (isset($vendorVat))
                    {{ $vendorVat }}<br>
                @endif

                @if (isset($url))
                    <a href="{{ $url }}">{{ $url }}</a>
                @endif
            </td>
        </tr>
    </table>
    <table style="margin-left: auto; margin-right: auto" width="550">
        <tr>
            <td>
                <!-- Invoice Info -->
                <!-- <p>
                 <strong>Product:</strong> {{ $product }}<br>
                    <strong>Invoice Number:</strong> {{ $id ?? $invoice->id }}<br
                </p> -->

                <!-- Extra / VAT Information -->
                @if (isset($vat) && !empty($vat))
                    <p>
                        <strong>VAT:</strong> {{ $vat }}
                    </p>
                @endif

                @if (isset($billing_address) && !empty($billing_address))
                    <p>
                        <strong>Billing Address:</strong><br>
                        @if(is_array($billing_address))
                            @foreach($billing_address as $line)
                                {{ $line }}<br>
                            @endforeach
                        @elseif(is_string($billing_address))
                            {{ $billing_address }}
                        @endif
                    </p>
                @endif

                @if (isset($tax_id) && !empty($tax_id))
                    <p>
                        <strong>Tax ID:</strong> {{ $tax_id }}
                    </p>
                @endif

                <br><br>

                <!-- Invoice Table -->
                <table width="100%" class="table" border="0">
                    <tr>
                        <th align="left">Description</th>
                        <th align="right">Date</th>
                        <th align="right">Amount (USD)</th>
                    </tr>

                    <!-- Existing Balance -->
                    <tr>
                        <td>Starting Balance</td>
                        <td>&nbsp;</td>
                        <td>{{ $invoice->startingBalance() }}</td>
                    </tr>

                    <!-- Display The Invoice Items -->
                    @foreach ($invoice->invoiceItems() as $item)
                        <tr>
                            <td colspan="2">{{ $item->description }}</td>
                            <td>{{ $item->total() }}</td>
                        </tr>
                    @endforeach

                <!-- Display The Subscriptions -->
                    @foreach ($invoice->subscriptions() as $subscription)
                        <tr>
                            <td>Subscription ({{ $subscription->quantity }})</td>
                            <td>
                                {{ $subscription->startDateAsCarbon()->formatLocalized('%B %e, %Y') }} -
                                {{ $subscription->endDateAsCarbon()->formatLocalized('%B %e, %Y') }}
                            </td>
                            <td>{{ $subscription->total() }}</td>
                        </tr>
                    @endforeach

                <!-- Display The Discount -->
                    @if ($invoice->hasDiscount())
                        <tr>
                            @if ($invoice->discountIsPercentage())
                                <td>{{ $invoice->coupon() }} ({{ $invoice->percentOff() }}% Off)</td>
                            @else
                                <td>{{ $invoice->coupon() }} ({{ $invoice->amountOff() }} Off)</td>
                            @endif
                            <td>&nbsp;</td>
                            <td>-{{ $invoice->discount() }}</td>
                        </tr>
                    @endif

                <!-- Display The Tax Amount -->
                    @if ($invoice->tax_percent)
                        <tr>
                            <td>Tax ({{ $invoice->tax_percent }}%)</td>
                            <td>&nbsp;</td>
                            <td>{{ Laravel\Cashier\Cashier::formatAmount($invoice->tax) }}</td>
                        </tr>
                @endif

                <!-- Display The Final Total -->
                    <tr style="border-top:2px solid #000;">
                        <td>&nbsp;</td>
                        <td style="text-align: right;"><strong>Total</strong></td>
                        <td><strong>{{ $invoice->total() }}</strong></td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td style="border-top: 1px solid #e5e5e5;font-size:8px;color:#999999;">
                <table width="100%" border="0">
                    <tr>
                        <td>
                            Thank you for using SocialBu
                        </td>
                        <td align="right">
                            socialbu.com
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</div>
</body>
</html>
