<?php

namespace App\Helpers;

use <PERSON>\TwitterOAuth\TwitterOAuthException;
use App\Account;
use App\LinkShortener;
use App\Socialite\LinkMngrProvider;
use App\Socialite\ThreadsProvider;
use App\Socialite\TikTokProvider;
use App\Socialite\PinterestProvider;
use Facebook\Exceptions\FacebookSDKException;
use Facebook\Facebook;
use Google_Client;
use Google_Service_MyBusiness;
use GuzzleHttp\Client;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Psr7\Uri;
use GuzzleRetry\GuzzleRetryMiddleware;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use League\OAuth2\Client\Token\AccessToken;
use LinkedIn\AccessToken as LinkedInAccessToken;
use PHPLicengine\Exception\CurlException;
use GuzzleHttp\RequestOptions;

class ApiHelper
{
    /**
     * Get the facebook api instance
     * @param mixed $token
     * @return Facebook
     * @throws \Facebook\Exceptions\FacebookSDKException
     */
    public static function getFacebook($token = null)
    {
        $fb = new Facebook([
            'app_id' => config('services.facebook.client_id'),
            'app_secret' => config('services.facebook.client_secret'),
            'default_graph_version' => config('services.facebook.default_graph_version'),
            'http_client_handler' => new FacebookHttpClient(),
        ]);
        if ($token) {
            $fb->setDefaultAccessToken($token);
        }
        return $fb;
    }

    /**
     * @param mixed $token
     * @param Account $account
     * @return Instagram
     * @throws FacebookSDKException
     */
    public static function getInstagram($token = null, Account $account = null)
    {
        $ig = new Instagram([
            'app_id' => config('services.instagram.client_id'),
            'app_secret' => config('services.instagram.client_secret'),
            'default_graph_version' => config('services.instagram.default_graph_version'),
            'http_client_handler' => new FacebookHttpClient(),
            ]
        );

        //if no expiresAt, it's short-lived token, get long-lived one
        if($account && $token && !isset($token['expiresAt'])){
            $response = guzzle_client()->get('https://graph.instagram.com/access_token', [
                'query' => [
                    'grant_type' => 'ig_exchange_token',
                    'client_secret' => config('services.instagram.client_secret'),
                    'access_token' => $token['token'],
                ]
            ]);
            $response = json_decode($response->getBody()->getContents(), true);
            $token['token'] = $response['access_token'];
            $token['expiresAt'] = time() + $response['expires_in'];
    
            $account->setToken($token);
            $account->refresh();
        }

        //refresh token a week before expiry when its still valid
        if($account && $token && isset($token['expiresAt']) && $token['expiresAt'] < (time() - 60 * 60 * 24 * 7)){

            $response = guzzle_client()->get('https://graph.instagram.com/refresh_access_token', [
                'query' => [
                    'grant_type' => 'ig_refresh_token',
                    'access_token' => $token['token'],
                ]
            ]);
            $response = json_decode($response->getBody()->getContents(), true);
            $token['token'] = $response['access_token'];
            $token['expiresAt'] = time() + $response['expires_in'];

            $account->setToken($token);
            $account->refresh();
        }
        
        if ($token) {
            $ig->setDefaultAccessToken($token['token']);
        }

        return $ig;
    }

    /**
     * Get the Twitter api instance
     * @param mixed $token
     * @return TwitterOauthCustom
     * @throws TwitterOAuthException
     * @throws \Exception
     */
    public static function getTwitter($token = null, Account $account = null)
    {
        $tw = TwitterOauthCustom::withoutToken();
        if($account && isset($token['refresh']) && isset($token['expires_at']) && $token['expires_at'] < time()){ // v2 oauth
            // need to refresh
            $client = guzzle_client([
                'headers' => [
                    'Content-Type' => 'application/x-www-form-urlencoded',
                    'Authorization' => 'Basic ' . base64_encode(config('services.twitter.client_id') . ':' . config('services.twitter.client_secret'))
                ]
            ]);

            try {
                $response = $client->post('https://api.x.com/2/oauth2/token', [
                    RequestOptions::FORM_PARAMS => [
                        'grant_type' => 'refresh_token',
                        'client_id' => config('services.twitter.client_id'),
                        'client_secret' => config('services.twitter.client_secret'),
                        'refresh_token' => $token['refresh'],
                    ]
                ]);

                $newToken = json_decode($response->getBody(), true);

                // important: don't override $token array we save username in it too
                $token['token'] = $newToken['access_token'];
                $token['expires_at'] = time() + $newToken['expires_in'];
                $token['refresh'] = $newToken['refresh_token'];

                $account->setToken($token);
                $account->refresh();
            } catch (\Exception $e) {
                \Log::error('Error refreshing Twitter token: ' . $e->getMessage());
                throw $e;
            }
        }

        if ($token) {
            $tw = $tw->withToken($token, $account->getOption('login_via_new_x_api'));
        }

        //  default api 1.1
        $tw->setApiVersion('1.1');

        return $tw;
    }

    /**
     * Get Mastodon api instance
     * @param string $domain
     * @param string $token
     * @return \Revolution\Mastodon\MastodonClient;
     */
    public static function getMastodon(string $domain, string $token)
    {
        return \Mastodon::domain($domain)
            ->token($token);
    }

    /**
     * @param mixed $token
     * @param Account $account
     * @return LinkedInClient
     */
    public static function getLinkedIn(array $token = null, Account $account = null)
    {
        // instantiate the LinkedIn client
        $linkedin = new LinkedInClient(
            config('services.linkedin.client_id'),
            config('services.linkedin.client_secret')
        );

        if ($token && $token['expiresAt'] < time()) {
            //need to refresh
            try {
                $linkedinClient = $linkedin->getClient([
                    'base_uri' => "https://www.linkedin.com/oauth/v2/",
                    'headers' => [
                        'Content-Type' => 'x-www-form-urlencoded'
                    ]
                ]);
                $res = $linkedinClient->request('POST', "accessToken", [
                    'form_params' => [
                        'grant_type' => 'refresh_token',
                        'refresh_token' => $token['refreshToken'],
                        'client_id' => config('services.linkedin.client_id'),
                        'client_secret' => config('services.linkedin.client_secret')
                    ]
                ]);
                $resToken = json_decode($res->getBody(), true);
                $token = [
                    'token' => $resToken['access_token'],
                    'expiresAt' => time() + $resToken['expires_in'],
                    'refreshToken' => $resToken['refresh_token']
                ];
                //set new token for account
                $account && $account->setToken($token);
            } catch (\Exception $e) {
                throw $e;
            }
        }
        $linkedin->setApiRoot('https://api.linkedin.com/v2/');
        $linkedin->setApiHeaders([
            'Content-Type' => 'application/json',
            'x-li-format' => 'json',
            'X-Restli-Protocol-Version' => '2.0.0', // use protocol v2
        ]);

        if ($token && isset($token['token'])) {
            // set token for client
            $linkedin->setAccessToken(new LinkedInAccessToken($token['token'], $token['expiresAt']));
        }
        return $linkedin;
    }

    /**
     * @param array|null $token
     * @return Google_Client
     */
    public static function getGoogle(array $token = null)
    {

        $client = new Google_Client();
        $client->setApplicationName(config('app.name'));
        $client->setClientId(config('services.google.client_id'));
        $client->setClientSecret(config('services.google.client_secret'));

        // because google expects a specific array format
        if (!isset($token['access_token']) && isset($token['token'])) {
            $token['access_token'] = $token['token'];
        }
        if (!isset($token['expires_at']) && isset($token['expiresAt'])) {
            $token['expires_at'] = $token['expiresAt'];
        }
        if (!isset($token['expires_in']) && isset($token['expiresIn'])) {
            $token['expires_in'] = $token['expiresIn'];
        }
        if (!isset($token['refresh_token']) && isset($token['refreshToken'])) {
            $token['refresh_token'] = $token['refreshToken'];
        }

        if ($token && isset($token['access_token'])) {
            // set token for client
            $client->setAccessToken($token);
        }

        return $client;
    }

    /**
     * @param array $token
     * @return Google_Service_MyBusiness
     */
    public static function getGoogleMyBusiness(array $token)
    {
        $client = self::getGoogle($token);
        $client->setApiFormatV2(2);
        return new Google_Service_Mybusiness($client);
    }

    /**
     * @param Google_Client|\Google\Client $client
     * @return \Google\Service\MyBusinessAccountManagement
     */
    public static function getGoogleMyBussinessAccountManagement($client)
    {
        return new \Google\Service\MyBusinessAccountManagement($client);
    }

    /**
     * @param Google_Client|\Google\Client $client
     * @return \Google\Service\MyBusinessBusinessInformation
     */
    public static function getGoogleMyBusinessBusinessInformation($client)
    {
        return new \Google\Service\MyBusinessBusinessInformation($client);
    }

    /**
     * @param Google_Client|\Google\Client $client
     * @return \Google\Service\BusinessProfilePerformance
     */
    public static function getGoogleBusinessProfilePerformance($client)
    {
        return new \Google\Service\BusinessProfilePerformance($client);
    }

    /**
     * @param Google_Client|\Google\Client $client
     * @return \Google\Service\MyBusinessVerifications
     */
    public static function getGoogleMyBusinessVerification($client)
    {
        return new \Google\Service\MyBusinessVerifications($client);
    }

    /**
     * @param array $token
     * @return \Google\Service\YouTube
     */
    public static function getYouTube(array $token)
    {
        $client = self::getGoogle($token);
        $client->setApiFormatV2(2);
        return new \Google\Service\YouTube($client);
    }

    /**
     * @param string $token
     * @return BitlyApiHelper
     */
    public static function getBitly(string $token)
    {
        try {
            $api = new \PHPLicengine\Api\Api($token);
        } catch (CurlException $e) {
            return null;
        }
        $obj = new BitlyApiHelper();
        $obj->api = $api;
        $obj->user = new \PHPLicengine\Service\User($api);
        $obj->group = new \PHPLicengine\Service\Group($api);
        $obj->bitlink = new \PHPLicengine\Service\Bitlink($api);
        return $obj;
    }

    /**
     * @param LinkShortener $shortener
     * @return \GuzzleHttp\Client
     * @throws \League\OAuth2\Client\Provider\Exception\IdentityProviderException
     * @throws \Exception
     */
    public static function getLinkMngr(LinkShortener $shortener)
    {
        if ($shortener->type !== 'linkmngr') {
            throw new \Exception('Invalid type passed: ' . $shortener->type);
        }
        $provider = new \League\OAuth2\Client\Provider\GenericProvider([
            'clientId' => config('services.linkmngr.client_id'),    // The client ID assigned to you by the provider
            'clientSecret' => config('services.linkmngr.client_secret'),    // The client password assigned to you by the provider
            'redirectUri' => config('services.linkmngr.redirect'),
            'urlAuthorize' => LinkMngrProvider::getBaseUrl('oauth/authorize'),
            'urlAccessToken' => LinkMngrProvider::getBaseUrl('oauth/token'),
            'urlResourceOwnerDetails' => LinkMngrProvider::getBaseUrl('v1/auth/user', 'api'),
        ]);

        $token = $shortener->getToken();
        $existingAccessToken = new AccessToken([
            'access_token' => $token['access_token'],
            'refresh_token' => $token['refresh_token'],
            'expires' => $token['timestamp'] + $token['expires_in'],
        ]);

        if ($existingAccessToken->hasExpired()) {
            $newAccessToken = $provider->getAccessToken('refresh_token', [
                'refresh_token' => $existingAccessToken->getRefreshToken()
            ]);

            // Purge old access token and store new access token to your data store.
            $shortener->setToken([
                'access_token' => $newAccessToken->getToken(),
                'refresh_token' => $newAccessToken->getRefreshToken(),
                'expires_in' => $newAccessToken->getExpires() - time(),
                'timestamp' => time(),
            ]);
            $shortener->refresh();
            $token = $shortener->getToken();
        }

        return guzzle_client([
            'base_uri' => LinkMngrProvider::getBaseUrl(null, 'api'),
            'headers' => [
                'Authorization' => 'Bearer ' . $token['access_token'],
                'Accept' => 'application/json',
            ]
        ]);
    }

    /**
     * @param Account $account
     * @return \GuzzleHttp\Client
     * @throws \League\OAuth2\Client\Provider\Exception\IdentityProviderException
     * @throws \Exception
     */
    public static function getTikTok(Account $account)
    {
        if ($account->type !== 'tiktok.profile') {
            throw new \Exception('Invalid type passed: ' . $account->type);
        }

        $token = json_decode($account->token, true);
        $existingAccessToken = new AccessToken([
            'access_token' => $token['token'],
            'refresh_token' => $token['refreshToken'],
            'expires' => $token['expiresAt'],
        ]);

        if ($existingAccessToken->hasExpired()) {

            // refresh token using guzzle because tiktok's api uses non-standard response format (for example: using `data.access_token` instead of `access_token`)
            $client = guzzle_client();
            $refreshRes = $client->post(TikTokProvider::getBaseUrl('v2/oauth/token/', 'open.tiktokapis.com'), [
                'form_params' => array_filter([
                    'client_key' => config('services.tiktok.client_id'),
                    'client_secret' => config('services.tiktok.client_secret'),
                    'grant_type' => 'refresh_token',
                    'refresh_token' => $existingAccessToken->getRefreshToken(),
                ])
            ]);

            $refreshRes = json_decode($refreshRes->getBody()->getContents(), true);

            if (isset($refreshRes['data'])) {
                // v1 api has 'data' key
                $refreshRes = $refreshRes['data'];
            }

            if(isset($refreshRes['error'])){
                throw new \Exception($refreshRes['error'] . ': ' . ($refreshRes['error_description'] ?? 'Re-connect') );
            }

            $newAccessToken = new AccessToken([
                'access_token' => $refreshRes['access_token'],
                'refresh_token' => $refreshRes['refresh_token'],
                'expires' => $refreshRes['expires_in'] + time(),
            ]);

            // Purge old access token and store new access token to your data store.
            $account->setToken([
                'token' => $newAccessToken->getToken(),
                'expiresAt' => $newAccessToken->getExpires(),
                'expiresIn' => $newAccessToken->getExpires() - time(),
                'refreshToken' => $newAccessToken->getRefreshToken(),
                'timestamp' => time(),
            ]);
            $account->refresh();
            $token = json_decode($account->token, true);
        }

        return guzzle_client([
            'base_uri' => TikTokProvider::getBaseUrl(null, 'open.tiktokapis.com'),
            'headers' => [
                'Authorization' => 'Bearer ' . $token['token'],
                'Content-Type' => 'application/json; charset=UTF-8',
            ],
            'timeout' => 60,
        ]);
    }

    /**
     * @param array $token
     * @param Account $account
     * @return Client
     */
    public static function getPinterest(array $token, Account $account = null){

        $existingAccessToken = new AccessToken([
            'access_token' => $token['token'],
            'refresh_token' => $token['refreshToken'],
            'expires' => $token['expiresAt'],
        ]);

        if ($account && $existingAccessToken->hasExpired()) {
            //https://developers.pinterest.com/docs/getting-started/authentication/#Refreshing%20and%20storing%20tokens

            $client = guzzle_client();

            $refreshRes = $client->post('https://api.pinterest.com/v5/oauth/token', [
                'headers' => [
                    'Authorization' => 'Basic ' . base64_encode(config('services.pinterest.client_id') .':' .config('services.pinterest.client_secret')), //Basic base64 encoded string made of client_id:client_secret
                    'Content-Type' => 'application/x-www-form-urlencoded'
                ],
                'form_params' => [
                    'scopes' => config('services.pinterest.required_scopes'), //optional
                    'grant_type' => 'refresh_token',
                    'refresh_token' => $existingAccessToken->getRefreshToken(),
                ]
            ]);

            $refreshRes = json_decode($refreshRes->getBody()->getContents(), true);

            $newAccessToken = new AccessToken([
                'access_token' => $refreshRes['access_token'],
                'refresh_token' => $existingAccessToken->getRefreshToken(),
                'expires' => $refreshRes['expires_in'] + time(),
            ]);

            // Purge old access token and store new access token to your data store.
            $account->setToken([
                'token' => $newAccessToken->getToken(),
                'expiresAt' => $newAccessToken->getExpires(),
                'expiresIn' => $newAccessToken->getExpires() - time(),
                'refreshToken' => $newAccessToken->getRefreshToken(),
                'timestamp' => time(),
            ]);
            $account->refresh();
            $token = json_decode($account->token, true);
        }

        return guzzle_client([
            'base_uri' => PinterestProvider::getBaseUrl(null, 'api'),
            'headers' => [
                'Authorization' => 'Bearer ' . $token['token'],
                'Accept' => 'application/json',
            ]
        ]);
    }

    /**
     * @param array $token
     * @param Account $account
     * @return Client
     */
    public static function getBluesky(array $token, Account $account = null){

        $jwt = $token['token'];
        $refreshToken = $token['refreshToken'];
        $service = $token['service'];

        $client = guzzle_client([
            'base_uri' => 'https://' . $service . '/xrpc/',
            'headers' => [
                'Authorization' => 'Bearer ' . $jwt,
                'Accept' => 'application/json',
            ],
            'timeout' => 60,
        ]);

        $shouldRenew = false;
        try {
            $getSessionRes = $client->get('com.atproto.server.getSession');
            $json = json_decode($getSessionRes->getBody()->getContents(), true);
            if($json['active']){
                $shouldRenew = true;
            }
        } catch (\Exception $exception){
            $shouldRenew = true;
        }


        if ($account && $shouldRenew) {
            $refreshSessionRes = $client->post('com.atproto.server.refreshSession', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $refreshToken,
                    'Accept' => 'application/json',
                ],
            ]);

            $json = json_decode($refreshSessionRes->getBody()->getContents(), true);

            $jwt = $json['accessJwt'];
            $refreshToken = $json['refreshJwt'];

            // Purge old access token and store new access token to your data store.
            $account->setToken(array_merge($token, [
                'token' => $jwt,
                'refreshToken' => $refreshToken,
                'timestamp' => time(),
            ]));
            $account->refresh();
            $token = json_decode($account->token, true);

            $client = guzzle_client([
                'base_uri' => 'https://' . $service . '/xrpc/',
                'headers' => [
                    'Authorization' => 'Bearer ' . $token['token'],
                    'Accept' => 'application/json',
                ],
                'timeout' => 60,
            ]);
        }

        return $client;
    }

    /**
     * @param array|Account $tokenOrAccount
     * @return Client
     */
    public static function getReddit($tokenOrAccount)
    {

        $token = null;
        $account = null;
        if ($tokenOrAccount instanceof Account) {
            $account = $tokenOrAccount;
            $token = json_decode($account->token, true);
        } else {
            $token = $tokenOrAccount;
        }

        $existingAccessToken = new AccessToken([
            'access_token' => $token['token'],
            'refresh_token' => $token['refreshToken'],
            'expires' => $token['expiresAt'],
        ]);

        $stack = HandlerStack::create();
        $stack->push(GuzzleRetryMiddleware::factory([
            'max_retry_attempts' => 5,
            'retry_on_status' => [429],
            'give_up_after_secs' => 180,
            'retry_only_if_retry_after_header' => true,
        ]));

        // user agent should be: <platform>:<app ID>:<version string> (by /u/<reddit username>)
        // example: android:com.example.myredditapp:v1.2.3 (by /u/kemitche)
        $userAgent = 'web:socialbu.com:v1.0 (by /u/usamaejazch)';

        // we choose a random proxy; because reddit can rate-limit us if we use the same IP
        $proxy = null;
        if(app()->environment('production')) {
            $proxy = array_random(array_merge(config('app.proxies'), [null]));
        }

        if ($account && $existingAccessToken->hasExpired()) {

            $client = guzzle_client(array_merge([
                'handler' => $stack,
                'headers' => [
                    'User-Agent' => $userAgent,
                ],
            ], array_filter([
                'proxy' => $proxy,
            ])));

            // refresh token
            $res = $client->post('https://www.reddit.com/api/v1/access_token', [
                'form_params' => [
                    'grant_type' => 'refresh_token',
                    'refresh_token' => $existingAccessToken->getRefreshToken()
                ],
                'headers' => [
                    'Authorization' => 'Basic ' . base64_encode(config('services.reddit.client_id') . ':' . config('services.reddit.client_secret')),
                ]
            ]);

            $res = json_decode($res->getBody()->getContents(), true);

            $newAccessToken = new AccessToken([
                'access_token' => $res['access_token'],
                'refresh_token' => $res['refresh_token'],
                'expires' => $res['expires_in'] + time(),
            ]);

            // Purge old access token and store new access token to your data store.
            $token = array_merge($token, [
                'token' => $newAccessToken->getToken(),
                'expiresAt' => $newAccessToken->getExpires(),
                'expiresIn' => $newAccessToken->getExpires() - time(),
                'refreshToken' => $newAccessToken->getRefreshToken(),
                'timestamp' => time(),
            ]);
            $account->setToken($token);
            $account->refresh();
        }

        return guzzle_client(array_merge([
            'handler' => $stack,
            'base_uri' => 'https://oauth.reddit.com/api/',
            'headers' => [
                'Authorization' => 'Bearer ' . $token['token'],
                'Accept' => 'application/json',
                'User-Agent' => $userAgent,
            ]
        ], array_filter([
            'proxy' => $proxy,
        ])));

    }

    /**
     * @param string $domain
     * @return array
     * @throws \Exception
     */
    public static function getMastodonCredentials(string $domain)
    {

        // make sure $domain is a valid domain
        if (empty($domain)) {
            throw new \Exception('Invalid domain for mastodon');
        }

        // make sure the $domain is an url (domain with https)
        if (!filter_var($domain, FILTER_VALIDATE_URL)) {
            throw new \Exception('Invalid domain for mastodon: not a valid url');
        }

        // create slug from domain
        $slug = Str::slug($domain);

        // now we need client id and secret; if we don't have, we have to create it
        $credentialsPath = 'mastodon/' . $slug . '.json';

        $storage = \Storage::cloud();
        // use local storage on non production
        if (!app()->environment('production')) {
            $storage = \Storage::disk('local');
        }

        // check in cloud storage
        if ($storage->exists($credentialsPath)) {
            $cContents = $storage->get($credentialsPath);
            $credentials = json_decode($cContents, true);
        } else {
            // create mastodon app first
            $credentials = \Mastodon::domain($domain)
                ->createApp(
                    config('app.name'),
                    config('services.mastodon.redirect'),
                    implode(' ', config('services.mastodon.required_scopes')),
                    config('app.url')
                );
            // save in cloud storage
            $storage->put($credentialsPath, json_encode($credentials));
        }

        return $credentials;
    }

    /**
     * @param array $token
     * @param Account $account
     * @return Client
     */
    public static function getThreads(array $token, Account $account = null){

        $existingAccessToken = new AccessToken([
            'access_token' => $token['token'],
            'expires' => $token['expiresAt'] ?? null,
        ]);

        $shouldRenew = empty($existingAccessToken->getExpires());
        if($existingAccessToken->getExpires()){
            if(!$existingAccessToken->hasExpired()){
                // get days remaining
                $daysRemaining = Carbon::createFromTimestamp($existingAccessToken->getExpires())->diffInDays();
                $shouldRenew = $daysRemaining <= 30;
            }
        }

        if ($account && $shouldRenew) {

            $client = guzzle_client();

            if(empty($existingAccessToken->getExpires())){
                // is short-lived token probably, so we exchange token and get long-lived token
                $query = Arr::query([
                    'grant_type' => 'th_exchange_token',
                    'client_secret' => config('services.threads.client_secret'),
                    'access_token' => $existingAccessToken->getToken(),
                ]);

                $refreshRes = $client->get('https://graph.threads.net/access_token?' . $query);
            } else {
                // should renew long-lived token
                // is short-lived token probably, so we exchange token and get long-lived token
                $query = Arr::query([
                    'grant_type' => 'th_refresh_token',
                    'access_token' => $existingAccessToken->getToken(),
                ]);

                $refreshRes = $client->get('https://graph.threads.net/refresh_access_token?' . $query);
            }


            $refreshRes = json_decode($refreshRes->getBody()->getContents(), true);

            $newAccessToken = new AccessToken([
                'access_token' => $refreshRes['access_token'],
                'expires' => $refreshRes['expires_in'] + time(),
            ]);

            // Purge old access token and store new access token to your data store.
            $account->setToken([
                'token' => $newAccessToken->getToken(),
                'expiresAt' => $newAccessToken->getExpires(),
                'expiresIn' => $newAccessToken->getExpires() - time(),
                'timestamp' => time(),
            ]);
            $account->refresh();
            $token = json_decode($account->token, true);
        }

        $stack = new HandlerStack();
        $stack->setHandler(\GuzzleHttp\choose_handler());
        $stack->push(\GuzzleHttp\Middleware::mapRequest(function (\Psr\Http\Message\RequestInterface $request) use($token) {
            // push access token to query for every url
            return $request->withUri(Uri::withQueryValue($request->getUri(), 'access_token', $token['token']));
        }));
        return guzzle_client([
            'handler' => $stack,
            'base_uri' => ThreadsProvider::getBaseUrl('v1.0/', 'graph.threads.net'),
        ]);
    }

    /**
     * For using any of the RapidAPI api that we have
     * @param string $host
     * @return Client
     */
    public static function getRapidAPI(string $host = 'reddapi.p.rapidapi.com')
    {
        return guzzle_client([
            'base_uri' => 'https://' . $host . '/',
            'headers' => [
                'x-rapidapi-key' => config('services.rapidapi.key'),
                'x-rapidapi-host' => $host,
            ],
            'timeout' => 60,
        ]);
    }

}

/**
 * @property \PHPLicengine\Api\Api $api
 * @property \PHPLicengine\Service\User $user
 * @property \PHPLicengine\Service\Bitlink $bitlink
 * @property \PHPLicengine\Service\Group $group
 */
class BitlyApiHelper extends Struct
{
}
