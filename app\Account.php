<?php

namespace App;

use <PERSON>\TwitterOAuth\TwitterOAuth;
use App\Helpers\ApiHelper;
use App\Helpers\LinkedInClient;
use App\Helpers\TwitterOauthCustom;
use App\Jobs\RefreshAccountPic;
use App\Jobs\SendHTTPRequest;
use App\Notifications\AccountInactive;
use App\Traits\HasOptions;
use Carbon\Carbon;
use Facebook\Exceptions\FacebookSDKException;
use Facebook\Facebook;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\File;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use LinkedIn\Client;
use Malahierba\PublicId\PublicId;
use Mimey\MimeTypes;
use Revolution\Mastodon\MastodonClient;
use Twitter\Text\Parser as TweetParser;

/**
 * App\Account
 *
 * @property int $id
 * @property string $account_id
 * @property string $name
 * @property int $user_id
 * @property string $type
 * @property int $private
 * @property int $active
 * @property mixed|null $token
 * @property array|null $options
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $public_id
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Post[] $posts
 * @property-read int|null $posts_count
 * @property-read \App\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Account newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Account newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Account ofUser($id = null)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Account private()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Account query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Account whereAccountId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Account whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Account whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Account whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Account whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Account whereOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Account wherePrivate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Account whereToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Account whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Account whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Account whereUserId($value)
 * @mixin \Eloquent
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\LinkShortener[] $linkShorteners
 * @property-read int|null $link_shorteners_count
 * @property \Illuminate\Support\Carbon|null $insights_fetched_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\SocialContact[] $contacts
 * @property-read int|null $contacts_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\InboxConversation[] $conversations
 * @property-read int|null $conversations_count
 * @method static Builder|Account whereInsightsFetchedAt($value)
 * @property \Illuminate\Support\Carbon|null $inbox_fetched_at
 * @method static Builder|Account whereInboxFetchedAt($value)
 */
class Account extends Model
{

    use HasOptions, PublicId;

    static protected $public_id_salt = 'account-glxsftsocial--@';

    static protected $public_id_min_length = 32; // min length for your generated short ids.

    static protected $public_id_alphabet = 'abcdefghijklmnopqrstuvwxyz0123456789';

    public $team_id = null; // is set by `accountsFromTeams` method of User

    static private $transformCache = [];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id', 'account_id', 'name', 'type', 'private', 'insights_fetched_at', 'inbox_fetched_at',
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'created_at',
        'updated_at',
        'insights_fetched_at',
        'inbox_fetched_at',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'token',
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'options' => 'array',
        'active' => 'boolean',
        'private' => 'boolean',
    ];

    /**
     * The "booting" method of the model.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('non_private', function (Builder $builder) {
            $builder->where('private', false);
        });
        $deleteCache = function (/** @type Account $account */ $account) {
            \Cache::forget('model_cache/account/' . $account->id);
        };
        static::updated($deleteCache);
        static::deleted($deleteCache);
    }

    /**
     * Returns the next auto increment `id`
     * @return int
     */
    public static function getNextId()
    {

        $statement = \DB::select("show table status like 'accounts'");

        return (int) $statement[0]->Auto_increment;
    }

    /**
     * @param Account $account
     * @param Team $team
     * @return Collection
     */
    public static function transform(Account $account, Team $team = null){
        // need prevent duplicate processing
        $key = $account->id . '_';
        $key .= $team ? $team->id . '_' : '';
        $key .= user() ? user()->id : '';

        if(isset(self::$transformCache[$key])){
            // already processed
            return self::$transformCache[$key];
        }

        $data = \Cache::remember('model_cache/account/' . $account->id, now()->addDays(1), function() use($account){
            return [
                'id' => (int) $account->id,
                'name' => $account->name,
                'type' => $account->type,
                '_type' => $account->getType(),
                'active' => $account->active,
                'image' => $account->getProfilePic(),
                'post_maxlength' => $account->getPostLength(),
                'attachment_types' => $account->getAttachmentTypes(),
                'max_attachments' => $account->getMaxAttachments(),
                'post_media_required' => $account->requiresMedia(),
                'video_dimensions' => $account->getVideoLimit()['dimensions'],
                'video_duration' => $account->getVideoLimit()['duration'],
                'user_id' => (int) $account->user_id,
                'account_id' => $account->account_id,
                'public_id' => $account->public_id,
                'extra_data' => $account->getExtraData(), // any extra data we need in json
            ];
        });

        if(!$team && $account->user_id !== \Auth::id()){
            // set team if needed and if not set already
            $team = $account->getTeam();
        }
        if( $team ){
            // current user probably has access through teams
            $data['team_id'] = (int) $team->id;
            $data['approvers'] = $team->getApprovers()->map(function ($u){
                return User::transform($u);
            });
            $data['team'] = Team::transform($team, true);
        }
        $ret = collect($data);
        self::$transformCache[$key] = $ret;

        return $ret;
    }

    /**
     * Scope a query to only include private user accounts (which are bound to User auth).
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePrivate($query)
    {
        return $query->withoutGlobalScopes()->where('private', true);
    }

    /**
     * Scope a query to fetch resources of a specified user.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $id
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfUser($query, $id = null)
    {
        if (!$id)
            $id = \Auth::id();
        return $query->where('user_id', $id);
    }

    /**
     * Get the posts added by this account.
     */
    public function posts()
    {
        return $this->hasMany(Post::class);
    }

    /**
     * Get the inbox conversations.
    */
    public function conversations()
    {
        return $this->hasMany(InboxConversation::class);
    }

    public function contacts()
    {
        return $this->hasMany(SocialContact::class);
    }

    public function linkShorteners(){
        return $this->belongsToMany(LinkShortener::class);
    }

    /**
     * @var array
     */
    private static $getTeamCache = [];

    /**
     * @return Team
     */
    public function getTeam(){
        // set when account is fetched from user->accountsFromTeams method
        $teamId = $this->team_id;

        if(!$teamId) {
            $authUser = user();

            if($authUser) {
                // get team for the related account if needed
                /** @var Team[]|Collection $teamsHavingAccount */

                if(isset(self::$getTeamCache[$authUser->id . '-' . $this->id . '-teams'])){
                    $teamsHavingAccount = self::$getTeamCache[$authUser->id . '-' . $this->id . '-teams'];
                } else {
                    $teamsHavingAccount = $authUser->joinedTeams()->whereHas('accounts', function ($q) {
                        return $q->where('id', $this->id);
                    })->get();
                    self::$getTeamCache[$authUser->id . '-' . $this->id . '-teams'] = $teamsHavingAccount;
                }

                // if not set, find a team
                if ($teamsHavingAccount->count() > 0)
                    $teamId = $teamsHavingAccount->first()->id;
            }
        }

        if(!$teamId) return null;

        if(isset(self::$getTeamCache[$teamId])){
            return self::$getTeamCache[$teamId];
        }

        // if the user has a team through which he has access to accounts
        $team = Team::find($teamId);
        if($team){
            // max 100 in cache
            if(count(self::$getTeamCache) > 100){
                // remove extra items
                self::$getTeamCache = array_slice(self::$getTeamCache, 0, 100);
            }

            self::$getTeamCache[$teamId] = $team;
            return $team;
        }

        return null;
    }

    /**
     * @param User $user
     * @return bool
     */
    public function userCanCreatePost(User $user){
        $team = $this->getTeam();
        if($team){
            return $team->hasPermission($user, 'posts.create');
        }
        return true;
    }

    /**
     * Get network type - facebook.page gives facebook and so on
     * @deprecated Don't use it anymore for new code
     * @return string
     */
    public function getNetwork(){
        return explode('.', $this->type)[0];
    }

    /**
     * Get type
     * @param Account $account
     * @return string
     */
    public static function getAccountType(self $account)
    {
        $t = $account->type;
        $MAP = [
            'facebook.page' => 'Facebook Page',
            'facebook.group' => 'Facebook Group',
            'twitter.profile' => 'X (Twitter) Account',
            'instagram.direct' => 'Instagram Account (direct)',
            'linkedin.profile' => 'LinkedIn Profile',
            'linkedin.org' => 'LinkedIn Organization',
            'linkedin.brand' => 'LinkedIn Brand',
            'google.location' => 'Google Business Profile',
            'instagram.api' => 'Instagram Business',
            'mastodon.profile' => 'Mastodon Account',
            'tiktok.profile' => 'TikTok Account',
            'pinterest.profile' => 'Pinterest Profile',
            'google.youtube' => 'YouTube Channel',
            'reddit.profile' => 'Reddit Profile',
            'reddit.subreddit' => 'Reddit Subreddit',
            'threads.profile' => 'Threads Account',
            'bluesky.profile' => 'Bluesky Account',
        ];
        return (isset($MAP[$t]) ? $MAP[$t] : $t);
    }
    public function getType()
    {
        return self::getAccountType($this);
    }

     /**
     * Get maximum comment content length
     */
    public function getCommentLength()
    {
        $lengths = [
            // instagram
            'instagram.direct' => 2000,
            'instagram.api' => 2000,

            // facebook
             'facebook.page' => 8000,
            
            // linkedin
            'linkedin.profile' => 1249,
            'linkedin.org' => 1249,
            'linkedin.brand' => 1249,
            
            //reddit 
            'reddit.profile' => 7500,
            'reddit.subreddit' => 7500
        ];
        if (isset($lengths[$this->type]))
            return $lengths[$this->type];
        else
            return 1000;
    }

    /**
     * Get maximum post content length
     */
    public function getPostLength()
    {
        $lengths = [
            // facebook
            'facebook.page' => 5000,
            'facebook.group' => 5000,

            // instagram
            'instagram.direct' => 2000,
            'instagram.api' => 2000,

            // twitter
            'twitter.profile' => 280, // was 140

            // mastodon
            'mastodon.profile' => 500, // default is 500

            // tiktok
            'tiktok.profile' => 2200,

            // youtube
            'google.youtube' => 5000,

            // linkedin
            'linkedin.profile' => 3000,
            'linkedin.org' => 3000,
            'linkedin.brand' => 3000,

            // google
            'google.location' => 1500,

            //pinterest
            'pinterest.profile' => 500,

            // reddit
            'reddit.profile' => 10000,
            'reddit.subreddit' => 10000,

            // threads
            'threads.profile' => 500,

            // bluesky
            'bluesky.profile' => 300,
        ];
        if (isset($lengths[$this->type])) {

            if($this->type === 'twitter.profile'){
                // check if user is premium
                $subType = $this->getOption('extra_data.subscription_type', 'None');
                if($subType !== 'None'){
                    // premium user limit is 25000
                    return 25000;
                }
            }


            return $lengths[$this->type];
        } else {
            return 5000;
        }
    }

    /**
     * Get supported attachment types (array)
     */
    public function getAttachmentTypes()
    {

        $t = $this->type;

        /*
         * Note: Apple tries really hard to think different: m4v is for apple-encoded mp4... qt is also for .mov file sometimes
         */

        if (strpos($t, 'twitter.') !== false) {
            return [
                'jpg', 'png', 'gif', 'jpeg', 'mp4', 'm4v', 'mov', 'qt', 'avi', 'webp',
            ];
        } elseif (strpos($t, 'facebook.') !== false) {
            return [
                'jpg', 'png', 'bmp', 'tiff', 'jpeg', 'mp4', 'm4v', 'mov', 'qt', 'avi', 'webp',
            ];
        } elseif(strpos($t, 'instagram') !== false){
            return [
                'jpg', 'png', 'jpeg', 'mp4', 'm4v', 'mov', 'qt', 'avi', 'webp',
            ];
        } elseif(strpos($t, 'linkedin') !== false){
            return [
                'jpg', 'png', 'jpeg', 'gif', 'mp4', 'qt', 'm4v', 'mov', 'webp', 'ppt', 'pptx', 'pdf' ,'doc', 'docx'
            ];
        } elseif($t === 'google.location'){
            return [
                'jpg', 'png', 'jpeg', 'webp', // 'gif', 'mp4', 'mov', 'qt', 'avi'
            ];
        } elseif($t === 'mastodon.profile'){
            return [
                'jpg', 'png', 'jpeg', 'gif', 'mp4', 'qt', 'm4v', 'mov', 'webp',
            ];
        } elseif($t === 'tiktok.profile'){
            return [
                'jpg', 'png', 'jpeg', 'mp4', 'm4v', 'mov', 'qt', 'avi', 'webp',
            ];
        } elseif($t === 'pinterest.profile'){
            return [
                'jpg', 'png', 'jpeg', 'mp4', 'qt', 'm4v', 'mov', 'webp',
            ];
        } elseif($t === 'google.youtube'){
            return [
                'mp4', 'm4v', 'mov', 'qt', 'avi', 'webp',
            ];
        } elseif($t === 'threads.profile'){
            return [
                'jpg', 'png', 'jpeg', 'mp4', 'm4v', 'mov', 'qt', 'avi', 'webp',
            ];
        } elseif(strpos($t, 'reddit') !== false){
            return [
                'jpg', 'png', 'jpeg', 'gif', 'mp4', 'm4v', 'mov', 'qt', 'webp',
            ];
        } elseif ($t === 'bluesky.profile') {
            return [
                'jpg', 'png', 'gif', 'jpeg', 'webp', 'mp4', 'm4v', 'mov', 'qt', 'avi'
            ];
        }

        return [
            'jpg', 'png', 'jpeg',
        ];
    }

    /**
     * Get video extensions allowed for attachments
     * @return array
     */
    public function getVideoAttachmentTypes(){
        $VIDEO_EXTS = [
            'mp4',
            'm4v',
            'mov',
            'qt',
            'avi',
        ];
        $allMediaTypes = $this->getAttachmentTypes();
        // only return video types
        return collect($allMediaTypes)->filter(function($ext)use($VIDEO_EXTS){
            return in_array($ext, $VIDEO_EXTS);
        })->toArray();
    }

    /**
     * Get max allowed attachments for a post
     *
     * @param string $type
     * @return int
     */
    public function getMaxAttachments($type = 'image'){
        $t = $this->type;
        if (strpos($t, 'twitter.') !== false) {
            return $type === 'image' ? 4 : 1;
        } elseif (strpos($t, 'facebook.') !== false) {
            return $type === 'image' ? 80 : 1;
        } elseif(strpos($t, 'instagram') !== false){
            return $type === 'image' ? 10 : 1;
        } elseif(strpos($t, 'linkedin') !== false){
            return $type === 'image' ? 9 : 1;
        } elseif($t === 'google.location'){
            // only 1 supported
            return 1;
        } elseif($t === 'mastodon.profile'){
            return $type === 'image' ? 4 : 1;
        } elseif($t === 'tiktok.profile'){
            return $type === 'image' ? 35 : 1; 
        } elseif($t === 'pinterest.profile'){
            return 1;
        } elseif($t === 'google.youtube'){
            return 1;
        } elseif(strpos($t, 'reddit') !== false){
            return 1;
        } elseif($t === 'threads.profile'){
            return $type === 'image' ? 10 : 1;
        } elseif($t === 'bluesky.profile'){
            return $type === 'image' ? 4 : 1;
        }
        return 1;
    }


    public function getVideoLimit($postType = 'post'){
        $t = $this->type;

        $lim = [
            'duration' => [ // in seconds
                'min' => 1,
                'max' => 140,
            ],
            'dimensions' => [
                'min' => [600, null], // width, height
                'max' => [null, null],
            ],
            'max_size' => 500, // in mbs
        ];

        if (strpos($t, 'twitter.') !== false) {
            $lim['dimensions']['min'] = [32, 32];
            $lim['duration'] = [
                'min' => 1,
                'max' => 140
            ];

            // check if user is premium
            // I don't think api allows video longer than 140 s
           $subType = $this->getOption('extra_data.subscription_type', 'None');
           if($subType !== 'None'){
               // premium user limit is ~1hr
               $lim['duration']['max'] = 60 * 60;
           }

        } elseif (strpos($t, 'pinterest') !== false) {
            $lim['dimensions']['min'] = [100, 100];
            $lim['duration'] = [
                'min' => 1,
                'max' => 60 * 5
            ];
        } elseif (strpos($t, 'facebook.') !== false) {
            $lim['dimensions']['min'] = [120, null];
            $lim['duration'] = [
                'min' => 1,
                'max' => $postType === 'reel' ? 90 : 60 * 60 * 2,
            ];
        } elseif(strpos($t, 'instagram') !== false){
            $lim['dimensions']['min'] = [480, null];
            $lim['duration'] = [
                'min' => 3,
                'max' => $postType === 'reel' ? 15 * 60 : 60
            ];
        } elseif(strpos($t, 'linkedin') !== false){
            $lim['dimensions']['min'] = [256, 144];
            $lim['dimensions']['max'] = [4096, 2304];
            $lim['duration'] = [
                'min' => 3,
                'max' => 60 * 10 // 10 min
            ];
            $lim['max_size'] = 5000; // 5 gb
        } elseif($t === 'google.location'){
            $lim['dimensions']['min'] = [1280, 720];
            $lim['dimensions']['max'] = [10000, 10000];
            $lim['duration'] = [
                'min' => 3,
                'max' => 30
            ];
            $lim['max_size'] = 25;
        } elseif(strpos($t, 'tiktok') !== false){

            if($this->getExtraData('creator_info') && isset($this->getExtraData('creator_info')['max_video_post_duration_sec'])){
                $maxLimit =  $this->getExtraData('creator_info')['max_video_post_duration_sec'];
            }else {
                $maxLimit = 10 * 60; 
            }

            $lim['dimensions']['min'] = [360, 360];
            $lim['dimensions']['max'] = [4096, 4096];
            $lim['duration'] = [
                'min' => 3,
                'max' => $maxLimit
            ];
        } elseif(strpos($t, 'youtube') !== false){
            $lim['dimensions']['min'] = [360, 360];
            $lim['dimensions']['max'] = [4096, 4096];
            $lim['duration'] = [
                'min' => 3,
                'max' => 43200 // 12 hrs
            ];
        } elseif(strpos($t, 'reddit') !== false){
            $lim['dimensions']['min'] = [360, 360];
            $lim['dimensions']['max'] = [4096, 4096];
            $lim['duration'] = [
                'min' => 3,
                'max' => 15 * 60, // 15 min
            ];
        } elseif ($t === 'threads.profile') {
            $lim['dimensions']['min'] = [32, 32];
            $lim['duration'] = [
                'min' => 1,
                'max' => 5 * 60, // 5 min
            ];
        } elseif ($t === 'bluesky.profile') {
            $lim['dimensions']['min'] = [32, 32];
            $lim['duration'] = [
                'min' => 1,
                'max' => 180, // 3 min
            ];
        }
        return $lim;
    }

    public function getExtraData($key = null, $default = null){
        dispatch(new \App\Jobs\SetupAccount($this)); // so we refresh the data if needed

        if(!$key) {
            return (object)$this->getOption('extra_data', new \stdClass());
        } else {
            return $this->getOption('extra_data.'.$key, $default);
        }
    }

    /**
     * Get api object for interacting with external account
     * @return TwitterOAuth|LinkedInClient|Facebook|\Google_Service_MyBusiness|Client|MastodonClient|\GuzzleHttp\Client|\Google\Service\YouTube|null
     * @throws \Exception
     */
    public function getApi()
    {
        $access = json_decode($this->token, true);
        if (in_array($this->type, ['facebook.page', 'facebook.group'])){
            try {
                return ApiHelper::getFacebook($access['token']);
            } catch (FacebookSDKException $e) {
                report($e);
                return null;
            }
        } else if ($this->type === 'twitter.profile'){
            return ApiHelper::getTwitter($access, $this);
        } else if(in_array($this->type, ['linkedin.profile', 'linkedin.org', 'linkedin.brand',])) {
            return ApiHelper::getLinkedIn($access, $this);
        } else if($this->type === 'instagram.api') {
            try {
                if($this->getOption('via_instagram_login', false)){
                    return ApiHelper::getInstagram($access, $this);
                } else {
                    return ApiHelper::getFacebook($access['token']);
                }
            } catch (FacebookSDKException $e) {
                report($e);
                return null;
            }
        } else if($this->type === 'google.location'){
            return ApiHelper::getGoogleMyBusiness($access);
        } else if ($this->type === 'mastodon.profile'){
            return ApiHelper::getMastodon($access['domain'], $access['token']);
        } else if ($this->type === 'tiktok.profile'){
            return ApiHelper::getTikTok($this);
        } else if ($this->type === 'pinterest.profile'){
            return ApiHelper::getPinterest($access, $this);
        } else if ($this->type === 'google.youtube'){
            return ApiHelper::getYouTube($access);
        } else if(in_array($this->type, ['reddit.profile', 'reddit.subreddit'])){
            return ApiHelper::getReddit($this);
        } else if ($this->type === 'threads.profile'){
            return ApiHelper::getThreads($access, $this);
        } else if ($this->type === 'bluesky.profile'){
            return ApiHelper::getBluesky($access, $this);
        }

        return null;

    }

    /**
     * Get profile pic
     * @param bool $fetch
     * @return string
     * @throws FacebookSDKException
     * @throws \Exception
     */
    public function getProfilePic(bool $fetch = false)
    {
        if(!$this->active) return url('/images/no-image.png'); // no need to return actual image for disconnected accounts

        $getDirectUrl = function(){
            $response = null;
            if ($this->type === 'facebook.page'){
                try {
                    /** @var Facebook $fb */
                    $fb = $this->getApi();

                    $res = $fb->get('/' . $this->account_id . '?fields=picture{url}')->getGraphNode()->asArray();

                    return $res['picture']['url'];

                } catch (\Exception $e) {
                    // $this->testConnection(true);
                    return 'https://graph.facebook.com/' . $this->account_id . '/picture?type=normal';
                }
            } else if($this->type === 'facebook.group'){
                try{
                    /** @var Facebook $fb */
                    $fb = $this->getApi();

                    $res = $fb->get('/' . $this->account_id . '?fields=cover{source}' )->getGraphNode()->asArray();
                    return $res['cover']['source'];
                }catch(\Exception $e){
                    // $this->testConnection(true);
                }
            } else if ($this->type == 'twitter.profile') {
                try {
                    /** @var TwitterOAuth $tw */
                    $tw = $this->getApi();

                    if($this->getOption('login_via_new_x_api')){
                        $tw->setApiVersion('2');
                        $response = $tw->get('users/me', [
                            'user.fields' => 'profile_image_url',
                        ]);
                        if ($tw->getLastHttpCode() === 200 && isset($response->data->profile_image_url)) {
                            return $response->data->profile_image_url;
                        }
                    } else {
                        $response = $tw->get("account/verify_credentials");
                        if ($response && $tw->getLastHttpCode() == 200) {
                            return $response->profile_image_url_https;
                        }
                    }
                } catch (\Exception $e) {
                    // $this->testConnection(true);
                }
            } else if ($this->type === 'instagram.api'){
                try {
                    /** @var Facebook $fb */
                    $fb = $this->getApi();
                    return $fb->get('/' . $this->account_id . '?fields=profile_picture_url')->getGraphNode()->getField('profile_picture_url');
                } catch (\Exception $e) {
                    // $this->testConnection(true);
                }
            } else if(strpos($this->type, 'linkedin.') !== false){
                try {
                    /** @var \Linkedin\Client $linkedin */
                    $linkedin = $this->getApi();
                    $liImg = null;
                    if($this->type === 'linkedin.profile') {
                        $res = $linkedin->get('me', [
                            'projection' => '(id,profilePicture(displayImage~:playableStreams))',
                        ]);
                        try {
                            $liImg = $res['profilePicture']['displayImage~']['elements'][0]['identifiers'][0]['identifier'];
                        } catch (\Exception $exception){
                            // some profiles don't return image
                            $liImg = '/images/no-image.png';
                        }
                    } else if($this->type === 'linkedin.org') {
                        $res = $linkedin->get('organizations/' . $this->account_id, [
                            'projection' => '(id,logoV2(original~:playableStreams))',
                        ]);
                        $liImg = $res['logoV2']['original~']['elements'][0]['identifiers'][0]['identifier'];
                    } else if($this->type === 'linkedin.brand') {
                        $res = $linkedin->get('organizationBrands/' . $this->account_id, [
                            'projection' => '(id,logoV2(original~:playableStreams))',
                        ]);
                        $liImg = $res['logoV2']['original~']['elements'][0]['identifiers'][0]['identifier'];
                    }
                    if($liImg) {
                        return $liImg;
                    }
                } catch (\Exception $e) {
                    // $this->testConnection(true);
                }
            } else if ($this->type == 'google.location') {
                try {
                    /** @var \Google_Service_MyBusiness $service */
                    $service = $this->getApi();
                    $res = $service->accounts_locations_media->listAccountsLocationsMedia($this->account_id, [
                        'pageSize' => 2500
                    ]);
                    $image = null;
                    foreach($res->getMediaItems() as $mediaItem){
                        /** @var \Google_Service_MyBusiness_MediaItem $mediaItem */

                        /** @var \Google_Service_MyBusiness_LocationAssociation $locAsociation */
                        $locAsociation = $mediaItem->getLocationAssociation();

                        if ($locAsociation->getCategory() === 'PROFILE'){
                            // found the image
                            $image = $mediaItem->getGoogleUrl();
                            break;
                        }
                    }
                    if($image) {
                        return $image;
                    }
                    return url('/images/logo_google_business_profile.png');
                } catch (\Exception $e) {
                    // $this->testConnection(true);
                }
            } else if ($this->type == 'mastodon.profile') {
                try {
                    /** @var MastodonClient $tw */
                    $m = $this->getApi();
                    // verify credentials
                    $response = $m->get('/accounts/verify_credentials');
                    if ($response) {
                        return $response['avatar'];
                    }
                } catch (\Exception $e) { }
            } else if ($this->type == 'tiktok.profile') {
                try {
                    /** @var \GuzzleHttp\Client $client */
                    $client = $this->getApi();
                    $response = $client->get('https://open.tiktokapis.com/v2/user/info/?fields=open_id,union_id,avatar_url');
                    $response = json_decode($response->getBody()->getContents(), true);
                    if ($response && isset($response['data']['user']['avatar_url'])) {
                        return $response['data']['user']['avatar_url'];
                    }
                } catch (\Exception $e) { }
            } else if ($this->type == 'google.youtube') {
                try {
                    /** @var \Google\Service\YouTube $client */
                    $youtube = $this->getApi();
                    $res = $youtube->channels->listChannels('snippet', [
                        'id' => $this->account_id,
                    ]);
                    $itm = $res->getItems()[0];

                    if(!$itm){
                        return null;
                    }

                    return $itm->getSnippet()->getThumbnails()->getDefault()->getUrl();
                } catch (\Exception $e) { }
            } else if ($this->type === 'pinterest.profile'){
                try {
                    $pinterest = $this->getApi();

                    $res = $pinterest->get('v5/user_account');
                    $json_res = json_decode($res->getBody()->getContents(), true);
                    return $json_res['profile_image'];
                }catch (\Exception $e) { }
            } else if (in_array($this->type, ['reddit.subreddit', 'reddit.profile'])) {

                $token = json_decode($this->token, true);

                try {
                    /** @var \GuzzleHttp\Client $reddit */
                    $reddit = $this->getApi();

                    $url = null;
                    if($this->type === 'reddit.subreddit'){
                        // subreddit
                        $res = $reddit->get('/r/' . $token['subreddit'] . '/about?raw_json=1');
                        $data = json_decode($res->getBody()->getContents(), true);
                        $url = $data['data']['community_icon'];
                    } else {
                        // profile
                        $res = $reddit->get('/api/v1/me?raw_json=1');
                        $data = json_decode($res->getBody()->getContents(), true);
                        $url = $data['icon_img'];
                    }
                    return $url;
                } catch (\Exception $e) { }
            } else if ($this->type == 'threads.profile') {
                try {
                    /** @var \GuzzleHttp\Client $threads */
                    $threads = $this->getApi();
                    $response = $threads->get("me?fields=id,username,threads_profile_picture_url,threads_biography");

                    $json = json_decode($response->getBody()->getContents(), true);

                    return $json['threads_profile_picture_url'] ?? null;

                } catch (\Exception $e) {
                    \Log::info('Error fetching threads image: ' . $e->getMessage());
                    // $this->testConnection(true);
                }
            } else if ($this->type == 'bluesky.profile') {
                try {
                    /** @var \GuzzleHttp\Client $bluesky */
                    $bluesky = $this->getApi();
                    $response = $bluesky->get("app.bsky.actor.getProfile?actor=" . urlencode($this->account_id));

                    $json = json_decode($response->getBody()->getContents(), true);

                    return $json['avatar'] ?? null;

                } catch (\Exception $e) {
                    \Log::info('Error fetching bluesky image: ' . $e->getMessage());
                    // $this->testConnection(true);
                }
            }
            return null;
        };
        $saveImage = function($url){

            $randomProxy = null;
            if(app()->environment('production')) {
                // set random proxy for Google Drive links
                // needed because a lot of requests can result in getting temporarily rate-limited
                $randomProxy = array_random(config('app.proxies'));
            } else {
                \Log::info('Bulk Import: skipping using a random proxy because its not production env');
            }

            // options to pass to guzzle
            // we also set header to mimic a normal browser
            $guzzleOpts = [
                'headers' => [
                    'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Safari/537.36', // chrome
                ],
                'proxy' => $randomProxy,
            ];

            $client = guzzle_client($guzzleOpts);

            $temp = tempnam(sys_get_temp_dir(), config('app.name'));

            // normal links
            try {
                $client->get($url, [
                    'sink' => $temp,
                ]);
            } catch (\Exception $exception) {
                @unlink($temp);
                return null;
            }

            try {
                $path = \Storage::putFileAs('avatars', new File($temp), $this->id . '.jpg');
            } catch (\Exception $exception){
                $path = null;
            }
            @unlink($temp);

            return $path;
        };
        $createPublicLinkFromPath = function($path){
            // expiry must be less than 1 week
            return app()->environment('production') ? \Storage::temporaryUrl($path, now()->addDays(6)) : \Storage::url($path);
        };

        $picData = $this->getOption('profile_pic', []);

        $needsFetch = true;
        if(!empty($picData) && isset($picData['timestamp'])){
            $cbPicTimestamp = Carbon::createFromTimestamp($picData['timestamp']);
            if($cbPicTimestamp->diffInDays(now()) < 4){
                // pic is less than x days old, so simple return the cached url
                $needsFetch = false;
            }
        }

        if($needsFetch && !$fetch){
            $pendingJob = $this->getOption('refresh_pic_job_pending');
            $pJobTimestamp = is_array($pendingJob) && isset($pendingJob['timestamp']) ? Carbon::createFromTimestamp($pendingJob['timestamp']) : null;
            if($pJobTimestamp && $pJobTimestamp->diffInHours(now()) >= 4){
                // requeue job
                $pJobTimestamp = null;
            }
            if(!$pendingJob || !$pJobTimestamp){
                $this->setOption('refresh_pic_job_pending', [
                    'timestamp' => time(),
                ]);
                dispatch(new RefreshAccountPic($this));
            }
        } else if($needsFetch && $fetch) {
            $url = $getDirectUrl();
            if(is_string($url)) {
                $picData['url'] = $url;
                $picData['timestamp'] = time();
                if(!empty($url) && !Str::startsWith($url, url('/'))){
                    // we upload and save path
                    $path = $saveImage($url);
                    if($path){
                        // save path now
                        $picData['path'] = $path;
                        // now generate public link
                        $publicLink = $createPublicLinkFromPath($path);
                        $picData['public_link'] = $publicLink;
                    }

                }

                // save now
                $this->setOption('profile_pic', $picData);
            }
        }

        $ret = url('/images/no-image.png'); // default img

        if(isset($picData['public_link'])){
            if(!$needsFetch) {
                // we have our public link, so just return that
                $ret = $picData['public_link'];

                // start: temp fix - make sure the url is not expired
                parse_str(parse_url($ret, PHP_URL_QUERY), $query);
                if(isset($query['X-Amz-Date'])){
                    $cbGenerateTimestamp = Carbon::parse($query['X-Amz-Date']);
                    // if the timestamp is > 4 days, we return fresh public link
                    if($cbGenerateTimestamp->diffInDays(now()) > 4){
                        $ret = $createPublicLinkFromPath($picData['path']);
                    }
                }
                // end: temp fix
            } else if(isset($picData['path'])) {
                $ret = $createPublicLinkFromPath($picData['path']);
            }
        }

        return $ret;
    }

    public function requiresImage(){
        return $this->requiresMedia();
    }

    /** Returns true if publishing to this account always requires a media
     * @return bool
     */
    public function requiresMedia(){
        return in_array($this->type, ['instagram.direct', 'instagram.api', 'tiktok.profile', 'google.youtube', 'pinterest.profile',]);
    }


    /** Returns true if publishing to this account always requires text
     * @return bool
     */
    public function requiresText(){
        return in_array($this->type, [

        ]);
    }

    /**
     * @param array $data
     * @param Carbon $publishAt
     * @param null $hash
     * @param array $attachments
     * @param bool $validateOnly
     * @return Post|bool
     * @throws \Exception|\Illuminate\Validation\ValidationException
     */
    public function publishPost(array $data, Carbon $publishAt, bool $validateOnly = false, $hash = null, array $attachments = []){
        if($this->type === 'instagram.direct'){
            throw new \Exception('Post scheduling/publishing is not available for Instagram Direct Accounts anymore. It will be available for only those accounts that are connected via Facebook.');
        }
        $mimes = new MimeTypes;

        $user = user();

        if(!$user) throw new \Exception('Unable to authorize request');

        // check if user can create content
        if(!$this->userCanCreatePost($user)){
            throw new \Exception('User cannot create posts on this account. Insufficient permissions.');
        }

        $user_id = $user->id;

        // we fetch account from user method, it will have team_id if the account is available through a team to current user
        /** @var Account $account */
        $account = $user->getAvailableAccounts()->firstWhere('id', $this->id);
        if(!$account) {
            throw new \Exception('Account not available');
        }

        $post_type = 'text';
        $data['content'] = isset($data['content']) ? $data['content'] : null;
        $data['attachments'] = isset($data['attachments']) ? $data['attachments'] : [];
        $data['options'] = isset($data['options']) ? $data['options'] : [];
        $data['source'] = isset($data['source']) ? $data['source'] : null;
        $data['is_draft'] = isset($data['is_draft']) ? $data['is_draft'] === true : false;

        // normalize newlines in content
        if($data['content'] && is_string($data['content'])){
            // $output = preg_replace('~\r\n?~', "\n", $input);
            // $output = preg_replace('/\r\n|\r|\n/', "\n", $input);
            $data['content'] = preg_replace('/\r\n|\r|\n/', "\n", $data['content']);
        }

        // check if post length and content ok
        if($this->type === 'twitter.profile'){
            Validator::make($data, [
                'content' => ($this->requiresText() || empty($data['attachments']) ? 'required|' : 'nullable|') . 'string',
                'options.media_alt_text.*' => 'max:1000'
            ])->validate();
            if($data['content']){
                // validate tweet length
                $parser = new TweetParser();
                $result = $parser->parseTweet($data['content']);
                if($result->weightedLength > $this->getPostLength()){
                    // exceeds maximum length of the tweet
                    throw new \Exception('Tweet length should be less than ' . $this->getPostLength() . ' characters');
                }

                if(substr_count($data['content'], '@') > $this->user->getOption('max_tweet_mentions', 6)){
                    throw new \Exception('Post should have less than ' . $this->user->getOption('max_tweet_mentions', 6) . ' @ characters. Contact us if you think this should be increased.');
                }

                // validate threaded replies if present
                if(isset($data['options']['threaded_replies'])){

                    if(!is_array($data['options']['threaded_replies'])) // shouldn't happen
                        $threaded_replies = (array) @json_decode($data['options']['threaded_replies'], true);
                    else
                        $threaded_replies = $data['options']['threaded_replies'];

                    $threaded_replies = TwitterOauthCustom::processThreadedReplies($account, $threaded_replies, [], $validateOnly);

                    $data['options']['threaded_replies'] = $threaded_replies;
                }

            }
        } else if( in_array($this->type, ['mastodon.profile', 'bluesky.profile', 'threads.profile',]) ){
            Validator::make($data, [
                'content' => ($this->requiresText() || empty($data['attachments']) ? 'required|' : 'nullable|') . 'string',
            ])->validate();
            if($data['content']){
                // validate post length
                $parser = new TweetParser();
                $result = $parser->parseTweet($data['content']);
                if($result->weightedLength > $this->getPostLength()){
                    // exceeds maximum length of the tweet
                    throw new \Exception('Content length should be less than ' . $this->getPostLength() . ' characters');
                }
            }
            if ($this->type === 'bluesky.profile' && !empty($data['attachments'])) {
                foreach ($data['attachments'] as $index => $attachment) {
                    $altText = $data['options']['media_alt_text'][$index] ?? '';
                    if (strlen($altText) > 2000) {
                        throw new \Exception("Bluesky alt text cannot exceed 2000 characters.");
                    }
                }
            }
            if(isset($data['options']['threaded_replies']) && count($data['options']['threaded_replies']) > 10){
                throw new \Exception('Thread replies cannot be more than 10');
            } else if (isset($data['options']['threaded_replies']) && count($data['options']['threaded_replies'])) {
                $threaded_replies = $data['options']['threaded_replies'];

                foreach ($threaded_replies as $index => $reply) {

                    $content = is_string($reply) ? $reply : $reply['text'];
                    
                    // normalize newlines in content
                    $content = preg_replace('/\r\n|\r|\n/', "\n", $content);

                    $parser = new TweetParser();
                    $result = $parser->parseTweet($content);

                    // check content max length
                    if($result->weightedLength > $this->getPostLength()){
                        // exceeds maximum length of the tweet
                        throw new \Exception('Threaded reply ' . ($index + 1) . ' length should be less than ' . $this->getPostLength() . ' characters');
                    }

                }
            }
        } else if($this->type === 'tiktok.profile'){

            $privacyOptions = $this->getExtraData('creator_info.privacy_level_options', []);

            if(!isset($data['options']['privacy_status'])){
                // if privacy_status is not set, set default
                $privacy = 'PUBLIC_TO_EVERYONE';
                if(!in_array('PUBLIC_TO_EVERYONE', $privacyOptions)){
                    $privacy = 'FOLLOWER_OF_CREATOR';
                }
                $data['options']['privacy_status'] = $privacy;
            }

            // validate privacy status
            if(!in_array($data['options']['privacy_status'], $privacyOptions)){
                throw new \Exception('Invalid privacy status: ' . $data['options']['privacy_status']);
            }

            Validator::make($data, [
                'options.privacy_status' => 'string|required',
                'options.title' => 'nullable|string|max:90',
            ])->validate();

        } else if(Str::contains($this->type, 'reddit')){
            \Validator::make(['title' => $data['options']['title']?? null, 'content' => $data['content'], 'attachments' => $data['attachments']], [
                'title' => 'nullable|required_without:content|string|max:300',
                'content' => 'nullable|required_without:title|string|max:10000',
                'attachments' => 'nullable|required_without_all:title,content',
                'attachments.*' => 'file|mimes:jpeg,png,jpg,gif,mp4,mov|max:' . config('app.max_file_size'), 
            ])->validate();
        } else {
            Validator::make($data, [
                'content' => ($this->requiresText() || (empty($data['attachments']) && empty($attachments)) ? 'required|' : 'nullable|') . 'string|max:' . (int)$this->getPostLength(),
            ])->validate();
        }
        
        // if it needs media but media is not present
        if( $this->requiresMedia() && empty($data['attachments']) && empty($attachments) ){
            throw new \Exception('Media attachment required');
        }

        // options validation
        if($this->type === 'google.location'){
            // general
            \Validator::make($data['options'], [
                'call_to_action' => 'nullable|string|in:BOOK,ORDER,SHOP,LEARN_MORE,SIGN_UP,CALL',
                'topic_type' => 'nullable|string|in:EVENT,OFFER'
            ])->validate();

            if(isset($data['options']['call_to_action'])){
                if(!empty($data['options']['call_to_action']) && $data['options']['call_to_action'] !== 'CALL'){
                    if(!(Str::startsWith($data['options']['call_to_action_url'], "http://") || Str::startsWith($data['options']['call_to_action_url'], "https://"))) {
                        $data['options']['call_to_action_url'] =  'https://' . $data['options']['call_to_action_url'];
                    }
                    \Validator::make($data['options'], [
                        'call_to_action_url' => 'required|string|url',
                    ])->validate();
                }
            }

            if(isset($data['options']['topic_type']) && !empty($data['options']['topic_type'])){
                // event details
                \Validator::make($data['options'], [
                    'event_title' => 'required|string|max:58',
                    'event_start' => 'required|date_format:d/m/Y h:i a',
                    'event_end' => 'required|date_format:d/m/Y h:i a|after:event_start',
                ])->validate();
            }
        } else if($this->type === 'google.youtube'){
            $data['options']['post_as_short'] = $data['options']['post_as_short'] ?? false;

            // validate privacy_status
            $data['options']['privacy_status'] = $data['options']['privacy_status'] ?? 'public';

            if(!in_array($data['options']['privacy_status'], ['public', 'private', 'unlisted'])){
                $data['options']['privacy_status'] = 'public';
            }

        }else if($this->type === 'pinterest.profile'){

            \Validator::make($data['options'], [
                'board_name' => 'string|required',
            ])->validate();

        } else if(strpos($this->type, 'linkedin.') !== false){
            $options = LinkedInClient::processOptions($account, $data['options'], [], $validateOnly);
            $data['options'] = $options;
        } else if($this->type === 'threads.profile'){
            \Validator::make($data['options'], [
                'reply_control' => 'nullable|string|in:everyone,accounts_you_follow,mentioned_only',
            ])->validate();
        }

        if(count($data['attachments']) > 1 && isset($data['options']['post_as_story']) && $data['options']['post_as_story']){
            throw new \Exception('Only 1 media attachment is allowed for a story');
        }

        // TODO: optimize media in size and so on
        // check if valid attachments are passed
        if(count($data['attachments']) > 0 && empty($attachments)){
            foreach ($data['attachments'] as $index => $file) {
                /** @var UploadedFile $file */
                $attMime = $file->getMimeType();
                $attType =  strtolower($mimes->getExtension($attMime));

                $post_type = in_array($attType, ['mp4', 'm4v', 'mov', 'qt', 'avi',]) ? 'video' : 'image';

                if(in_array($attType, ['mp4', 'm4v', 'mov', 'qt', 'avi',])){
                    $post_type = 'video';
                } else if(in_array($attType, ['pdf', 'doc', 'docx', 'ppt', 'pptx'])){
                    $post_type = 'document';
            
                    if (in_array($this->type, ['linkedin.profile', 'linkedin.org', 'linkedin.brand'])) {
                        if(!isset($data['options']['document_title'])){
                            throw new \Exception('Document title is required for LinkedIn posts');
                        }
                    }
                } else {
                    $post_type = 'image';
                }
                // check image dimensions if needed
                if($post_type === 'image' && $this->type === 'google.location'){
                    // make sure img width n height are >= 250px
                    $size = @getimagesize($file->getRealPath());
                    if ($size) {
                        list($width, $height) = $size;
                        if ($width < 250 || $height < 250) {
                            throw new \Exception('Media width and height should be at-least 250px. Current dimensions: ' . $width . 'x' . $height);
                        } else if ($width > 10000 || $height > 10000) {
                            throw new \Exception('Media width and height should be maximum 10000px. Current dimensions: ' . $width . 'x' . $height);
                        }
                    }
                }

                if($post_type === 'video' && $this->type === 'pinterest.profile'){
                    if(isset($data['options']['thumbnail'])){
                        $decryptedKey = decrypt($data['options']['thumbnail']['secureKey']);
                        if($decryptedKey !== $data['options']['thumbnail']['key']){
                            throw new \Exception('Pin thumbnail: Invalid secure key');
                        }
            
                        if(!\Storage::cloud()->exists($data['options']['thumbnail']['key'])){
                            throw new \Exception('Pin thumbnail: Media file not available: ' . $data['options']['thumbnail']['name']);
                        }
                    }else {
                        throw new \Exception('For video pins, thumbnail is required!');
                    }
                }

            }
            

            foreach ($data['attachments'] as $file) {
                $attMime = $file->getMimeType();
                $attType =  strtolower($mimes->getExtension($attMime));

                /** @var UploadedFile $file */
                if(!$validateOnly) { // only store file when not validating

                    // DO NOT use putFile as it results in all attachments in one request going to the same path for multiple accounts
                    // and so attachment is ended up shared which we dont want
                    // so we manually set the file name here
                    $path = \Storage::putFileAs('attachments', $file,
                        $user->id . '_' . $this->id . '_' . time() . '_' . str_random(40) . sha1(time() . $this->id . $file->getFilename() . $file->getMimeType() . $user->id . $data['content']) . '.' .$attType
                    );

                    if (!$path) {
                        // del uploaded attachments and give error
                        foreach ((array) $attachments as $attachment) {
                            if(isset($attachment['path']) && $attachment['path']) {
                                if (\Storage::exists($attachment['path']))
                                    \Storage::delete($attachment['path']);
                            }
                        }
                        throw new \Exception('Unable to store attachment. Please try again.');
                    }

                } else {
                    $path = null;
                }
                $attachments[] = [
                    'path' => $path,
                    'type' => $attType,
                    'ext' => $attType,
                    'size' => $file->getSize(), // req for some places
                    'mime' => $file->getMimeType(),
                    'name' => str_replace('.tmp', '.' . $attType, $file->getClientOriginalName())
                ];
            }

        }

        // check max. attachments allowed
        $max_allowed_attachments = $this->getMaxAttachments($post_type);
        if(count($attachments) > $max_allowed_attachments){
            throw new \Exception('Maximum attachments you can post are ' . $max_allowed_attachments);
        }

        // set post_type if default (from attachments)
        // this is handling of an old bug
        if(isset($post_type) && $post_type === 'text'){
            foreach((array) $attachments as $attachment){
                $post_type = in_array($attachment['type'], ['mp4', 'm4v', 'mov', 'qt', 'avi',]) ? 'video' : 'image';
                break;
            }
        }

        // if video or gif, it should only be 1 file
        $has_gif = collect($attachments)->where('type', 'gif')->count() > 0;
        if(($post_type === 'video' || $has_gif) && count($attachments) > 1){
            throw new \Exception('Only 1 attachment is allowed if you are posting a video or a gif.');
        }

        if($validateOnly) return true; // if we are here, then all validation is good

        if($has_gif && $post_type === 'video') $post_type = 'image'; // dont know why we need this

        // calculate unique post hash; this needs to be unique for this request
        // update: should be unique for every post
        if(!$hash || 1)
            $hash = hash('sha256', $user_id . str_random(40) . $this->id . $data['content'] . $post_type . time(), false);

        $now = now();
        if($publishAt <= $now)
            $publishAt = $now;

        $teamId = null;
        if(isset($data['options']['team_id'])){
            // team id is set, so we save to options
            $teamId = $data['options']['team_id'];
        }
        $options = Post::processPostOptions($this, $data['options']);

        if($teamId !== null){
            // add team id to options again because processPostOptions will trim any non-user-facing options
            $options['team_id'] = $teamId;
        }
        // create post
        $post = new Post([
            'user_id' => $user_id,
            'account_id' => $this->id,
            'content' => $data['content'],
            'type' => $post_type,
            'post_hash' => $hash,
            'publish_at' => $publishAt,
            'draft' => $data['is_draft'],
            'approved' => true, // default
        ]);

        if(isset($data['source']))
            $post->source = $data['source'];

        // if it needs approval
        $team = null;
        if($teamId > 0){
            // we have team id so just find the team
            $team = $user->joinedTeams()->find($teamId);
        }
        if(!$team && $teamId === null) {
            // get team from account if possible
            $team = $account->getTeam();
        }

        if($team && !$post->draft){
            // only set for approval if its not a draft post
            if($team->requiresContentApproval() && !$team->hasPermission($user, 'approvals.approve')){
                // the approvers are set, so set approved = false if user is not an approver
                $post->approved = false;
            }
        }

        // add attachments if set
        if(!empty($attachments)) {
            $options['attachments'] = $attachments;
        }

        $post->options = $options;
        // post is saved
        $post->save();

        $post->shortenLinksIfNeeded();

        // cron will take care of it
        return $post;
    }

    /**
     * Test API connection
     * @param bool $notify To notify the user if connection fails
     * @param string|null $reason Optional reason for when connection failure happens
     * @return bool
     */
    public function testConnection(bool $notify = false, string $reason = null)
    {
        $connect_status = false;
        if (in_array($this->type, ['facebook.page', 'facebook.group', 'instagram.api'])) {

            try {
                /** @var Facebook $fb */
                $fb = $this->getApi();

                $info = $fb->get('/' . $this->account_id)->getDecodedBody();

                if (isset($info['id'])) {
                    $connect_status = true;
                }

            } catch (\Exception $e) {
                if( Str::contains($e->getMessage(), ['request limit reached', 'unexpected error']) || $e->getMessage() === null || $e->getMessage() === '' || $e->getMessage() === 'null'){
                    // probably connected
                    $connect_status = true;
                } else if(!Str::contains(strtolower($e->getMessage()), [
                    'session has been invalidated',
                    'session has expired',
                    'permission must be granted',
                    'not authorized',
                    'not a confirmed user',
                    'not accessible',
                    'logged-in checkpoint',
                    'user logged out',
                    'deleted',
                    'must be an administrator',
                    'two factor authentication',
                    'cannot call api on behalf of this user',
                    'till you log in',
                    'this endpoint requires the', // user token expired or user removed permissions
                    'must be granted before impersonating', // permissions not given
                    'missing permission',
                    'access is denied', // Access is denied because the instagram account type is not business or creator. The instagram account was switched to a personal account. Please change back to business or creator to access.
                ])){
                    // some error
                    report($e);
                }
            }
        } elseif ($this->type == 'twitter.profile') {
            try {
                /** @var TwitterOAuth $tw */
                $tw = $this->getApi();

                if($this->getOption('login_via_new_x_api')){
                    $tw->setApiVersion('2');
                    $tw->get("users/me");
                } else {
                    $tw->get("account/verify_credentials");
                }
                $connect_status = $tw->getLastHttpCode() === 200;

            } catch (\Exception $e) {}
        } elseif (in_array($this->type, ['linkedin.profile', 'linkedin.org', 'linkedin.brand'])) {
            try {
                /** @var \LinkedIn\Client $linkedin */
                $linkedin = $this->getApi();
                $paths = [
                    'linkedin.profile' => 'me',
                    'linkedin.org' => 'organizations/' . $this->account_id,
                    'linkedin.brand' => 'organizationBrands/' . $this->account_id,
                ];
                $res = $linkedin->get($paths[$this->type], [
                    'projection' => '(id)',
                ]);

                if(isset($res['id']))
                    $connect_status = true;
            } catch (\Exception $e) {

            }
        } elseif ($this->type === 'google.location') {
            try {
                /** @var \Google_Service_MyBusiness $service */
                $service = $this->getApi();
                $informationService = ApiHelper::getGoogleMyBusinessBusinessInformation($service->getClient());
                $informationService->locations->get('locations/' . explode('/locations/', $this->account_id)[1], [
                    'readMask' => 'title',
                ]);
                $connect_status = true;
            } catch (\Exception $e) {

            }
        } elseif ($this->type == 'mastodon.profile') {
            try {
                /** @var MastodonClient $m */
                $m = $this->getApi();
                $response = $m->get('/accounts/verify_credentials');
                $connect_status = !!$response;
            } catch (\Exception $e) { }
        } elseif ($this->type == 'tiktok.profile') {
            try {
                /** @var \GuzzleHttp\Client $client */
                $client = $this->getApi();
                $response = $client->get('https://open.tiktokapis.com/v2/user/info/?fields=open_id,union_id,avatar_url');
                $response = json_decode($response->getBody()->getContents(), true);
                $connect_status = !!$response;
            } catch (\Exception $e) { }
        } elseif ($this->type === 'google.youtube') {
            try {
                /** @var \Google\Service\YouTube $service */
                $service = $this->getApi();
                $service->channels->listChannels('snippet', [
                    'id' => $this->account_id,
                ]);
                $connect_status = true;
            } catch (\Exception $e) {

            }
        } elseif(in_array($this->type, ['reddit.profile', 'reddit.subreddit'])){
            try {
                $token = json_decode($this->token, true);
                /** @var \GuzzleHttp\Client $reddit */
                $reddit = $this->getApi();
                if($this->type === 'reddit.subreddit'){
                    // subreddit
                    $reddit->get('/r/' . $token['subreddit'] . '/about?raw_json=1');
                } else {
                    // profile
                    $reddit->get('/api/v1/me?raw_json=1');
                }
                $connect_status = true;
            } catch (\Exception $e) {

            }
        } elseif($this->type == 'pinterest.profile'){
            try {
                $pinterest = $this->getApi();
                $res = $pinterest->get('v5/user_account');
                $connect_status = !!$res;
            } catch (\Exception $e) {

            }
        } elseif ($this->type == 'threads.profile') {
            try {
                /** @var \GuzzleHttp\Client $threads */
                $threads = $this->getApi();
                $res = $threads->get("me");
                $connect_status = !!$res;
            } catch (\Exception $e) {

            }
        } elseif ($this->type == 'bluesky.profile') {
            try {
                /** @var \GuzzleHttp\Client $client */
                $client = $this->getApi();
                $res = $client->get("com.atproto.server.getSession");
                $connect_status = !!$res;
            } catch (\Exception $e) {

            }
        }

        $notifyUser = $connect_status != $this->active;

        if (!$connect_status && $this->active) { // mark as inactive if connection fails
            $this->setActive(false);

            $postbackUrl = $this->getOption('postback_url');
            if($postbackUrl){
                // send postback to the url
                dispatch(new SendHTTPRequest('POST', $postbackUrl, [
                    // guzzle options
                    'json' => [
                        'account_action' => 'disconnected',
                        'account_id' => $this->id,
                        'account_type' => $this->type,
                        'account_name' => $this->name,
                    ],
                ]));
            }

        } elseif ($connect_status && !$this->active) {
            $this->setActive(true);

            $postbackUrl = $this->getOption('postback_url');
            if($postbackUrl){
                // send postback to the url
                dispatch(new SendHTTPRequest('POST', $postbackUrl, [
                    // guzzle options
                    'json' => [
                        'account_action' => 'connected',
                        'account_id' => $this->id,
                        'account_type' => $this->type,
                        'account_name' => $this->name,
                    ],
                ]));
            }
        }

        if ($notify && $notifyUser && !$connect_status) {
            // notify the user
            if($this->user) {
                $this->user->notify(new AccountInactive($this, $reason));
            }
        }

        return $connect_status === true;
    }

    public function deactivate($notify = false, $reason = null){
        $this->setActive(false);
        if($notify){
            if(!$reason) {
                $trace = debug_backtrace();
                if (count($trace) > 1) {
                    $reason = '';
                    if(isset($trace[1]['class']))
                        $reason .= $trace[1]['class'] . '::';
                    if(isset($trace[1]['function']))
                        $reason .= $trace[1]['function'] . '()';
                    if(isset($trace[1]['line']))
                        $reason .= ' at line ' . $trace[1]['line'];
                } else {
                    $reason = 'Unknown';
                }
            }
            // notify the user
            if($this->user) {
                $this->user->notify(new AccountInactive($this, $reason));
            }
        }
    }

    /**
     * Get the user that added the account.
     */
    public function user()
    {
        return $this->belongsTo('App\User');
    }

    /**
     * Setup webhooks (if any)
     * @param bool $setup
     * @throws \Exception
     */
    public function setupWebhook(bool $setup = true)
    {
        $response = null;

        $FB_PAGE_FIELDS = 'feed,ratings,messages,message_echoes,message_reactions';

        // only for fb page
        if ($this->type === 'facebook.page') {
            /** @var Facebook $fb */
            $fb = $this->getApi();
            // check if automations exist for this page
            if ($setup) {
                // subscribe
                // Returns a `Facebook\FacebookResponse` object
                $graphNode = $fb->post(
                    '/' . $this->account_id . '/subscribed_apps',
                    ['subscribed_fields' => $FB_PAGE_FIELDS]
                )->getGraphNode()->asArray();

                if(!isset($graphNode['success'])){
                    // unexpected
                    throw new \Exception('Unfamiliar response received from Facebook. Please try again and accept all the required permissions');
                }
                if (!$graphNode['success']) {
                    throw new \Exception('Unfamiliar response received from Facebook.');
                }

                // we need to subscribe to messenger events too
                // seems like this is only needed one time, still we do every 1 day
                \Cache::remember('_fb_app_messenger_subscribed', now()->addDays(1), function () use($fb, $FB_PAGE_FIELDS) {

                    $messengerSubResponse = $fb->post('/' . config('services.facebook.client_id') . '/subscriptions',
                        [
                            'object' => 'page',
                            'fields' => $FB_PAGE_FIELDS
                        ],
                        config('services.facebook.client_id') . '|' . config('services.facebook.client_secret')
                    )->getGraphNode()->asArray();

                    if (!$messengerSubResponse['success']) {
                        throw new \Exception('Unfamiliar response received from Facebook.');
                    }

                    return true;
                });
            } else {
                if(Account::where('account_id', $this->account_id)->where('id', '<>', $this->id)->count() === 0){
                    // unsubscribe
                    try {
                        // Returns a `Facebook\FacebookResponse` object
                        $response = $fb->delete(
                            '/' . $this->account_id . '/subscribed_apps'
                        );
                        $graphNode = $response->getGraphNode()->asArray();
                        if (!isset($graphNode['success'])) {
                            throw new \Exception('Unfamiliar response received from Facebook.');
                        }
                    } catch (FacebookSDKException $e) {
                        if(Str::contains(strtolower($e->getMessage()), ['deleted', 'permission', 'invalidated', 'checkpoint', 'not authorized', 'error validating', 'user must be an administrator', 'not installed',])){
                            return;
                        }
                        throw $e;
                    }
                }
            }
        } else if($this->type === 'instagram.api'){

            $hasNewApi = $this->getOption('via_instagram_login', false);

            $INSTA_FIELDS = $hasNewApi ? 'comments,message_reactions,messages,messaging_seen' : 'feed,ratings,messages,message_echoes,message_reactions';
            
            $access = json_decode($this->token, true);

            try {
                $insta = $hasNewApi ? ApiHelper::getInstagram($access, $this) : ApiHelper::getFacebook($access['page_token']);
            } catch (\Exception $exception){
                if ($setup){
                    throw $exception;
                }

                // if we are here, then the access token is invalid or expired,
                // so we just return
                return;
            }

            if($hasNewApi){
                $page_id = $this->account_id;
            } else {
                $page_id = $access['page_id'];
            }

            // check if automations exist for this page
            if ($setup) {
                // subscribe
                // Returns a `Facebook\FacebookResponse` object
                $graphNode = $insta->post(
                    '/' . $page_id . '/subscribed_apps',
                    ['subscribed_fields' => $INSTA_FIELDS,] // as of v3.2 of api, it requires subscribed_fields
                )->getGraphNode()->asArray();

                if (!$graphNode['success']) {
                    throw new \Exception('Unfamiliar response received from Instagram.');
                }
            } else {
                if(Account::where('account_id', $page_id)->where('id', '<>', $this->id)->count() === 0){
                    // unsubscribe
                    try {
                        // Returns a `Facebook\FacebookResponse` object
                        $response = $insta->delete(
                            '/' . $page_id . '/subscribed_apps'
                        );
                        $graphNode = $response->getGraphNode()->asArray();
                        if (!isset($graphNode['success'])) {
                            throw new \Exception('Unfamiliar response received from Facebook.');
                        }
                    } catch (FacebookSDKException $e) {
                        // do nothing
                    }
                }
            }
        }
    }

    /**
     * Set token
     * @param array $token
     * @return Account
     */
    public function setToken(Array $token)
    {
        $this->token = json_encode($token);
        $this->save();
        return $this;
    }

    /**
     * Update active / inactive status
     * @param boolean $status
     * @return Account
     */
    public function setActive($status)
    {
        $this->active = $status == true;
        $this->save();
        return $this;
    }

    private function getOptions(){
        return (array) $this->options;
    }

    private function setOptions($options)
    {
        $this->options = $options;
        $this->save();
        return $this;
    }

}
