<?php

namespace App\Http\Controllers\Api\Partners;

use App\Helpers\ContentGenerator;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class AiContentController extends Controller
{
    public function index(){
        return response()->json([
            'success' => false,
            'message' => 'Invalid request',
        ], 400);
    }

    public function ping(){
        return response()->json([
            'success' => true,
            'message' => 'pong',
        ], 200);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|void
     * @throws \Illuminate\Validation\ValidationException
     */
    public function generateContentByTopic(Request $request)
    {
        $this->validate($request, [
            'topic' => 'required|string|min:3|max:150',
        ]);

        $modelToUse = 'gpt-3';
        $topic = trim($request->input('topic'));
        try {
            $data = ContentGenerator::getInstance()->generatePost($topic, null, $modelToUse, 'generic');

            $text = trim( $data['post'] );

            // trim special non-break-space
            $text = trim($text, ' ');

            return response()->json([
                'success' => true,
                'data' => [
                    'text' => $text,
                ],
            ]);

        } catch (\Exception $exception){
            abort(400, $exception->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|void
     * @throws \Illuminate\Validation\ValidationException
     */
    public function autocomplete(Request $request)
    {
        $this->validate($request, [
            'content' => 'required|string|max:1000',
        ]);

        $modelToUse = 'gpt-3';
        $content = trim($request->input('content'));
        try {
            $data = ContentGenerator::getInstance()->autocomplete($content, [], $modelToUse);

            $text = trim( $data['content'] );

            // trim special non-break-space
            $text = trim($text, ' ');

            return response()->json([
                'success' => true,
                'data' => [
                    'text' => $text,
                ],
            ]);

        } catch (\Exception $exception){
            abort(400, $exception->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|void
     * @throws \Illuminate\Validation\ValidationException
     */
    public function generateImagePrompt(Request $request)
    {
        $this->validate($request, [
            'content' => 'required|string|min:3|max:500',
        ]);

        try {
            $data = ContentGenerator::getInstance()->generateImagePrompt($request->input('content'), 'content body');

            $prompt = trim( $data['prompt'] );

            return response()->json([
                'success' => true,
                'data' => [
                    'text' => $prompt,
                ],
            ]);

        } catch (\Exception $exception){
            abort(400, $exception->getMessage());
        }
    }

}
