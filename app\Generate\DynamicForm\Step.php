<?php

namespace App\Generate\DynamicForm;
use Liquid\Template;

abstract class Step
{
    protected $input = [];
    private $subStepNodes = [];
    private $availableData = [];
    private $id;

    // for saving result of step execution
    private $continueSubNodes = [];
    private $output = [];

    public function __construct(string $id, array $stepData = [], array $availableData = [])
    {
        $this->id = $id;
        $this->input = $stepData['input'] ?? [];
        $this->subStepNodes = $stepData['sub_steps'] ?? [];
        $this->availableData = $availableData;
    }

    /**
     * @return void
     * @throws \Exception
     */
    abstract protected function validate();

    /**
     * @return void
     */
    abstract protected function execute();

    protected function setOutput(array $output)
    {
        $this->output = $output;
    }

    protected function setSubSteps(array $subSteps)
    {
        $this->continueSubNodes = $subSteps;
    }

    public function getOutput(): array
    {
        return $this->output;
    }

    protected function getData()
    {
        return transform_data_placeholders($this->input, $this->availableData);
    }

    /**
     * @throws \Exception
     */
    public function process(): array
    {
        $this->validate();
        $this->execute();

        $outputData = [
            $this->id => $this->getOutput(),
        ];
        // now, we may need to process sub steps
        foreach ($this->continueSubNodes as $nodeKey) {

            $subSteps = $this->subStepNodes[$nodeKey] ?? [];

            foreach ($subSteps as $index => $subStep) {
                $subId = $this->id . '-' . $nodeKey . ($index + 1);
                $subStep = new static($subId, $subStep);
                $subStep->process();

                $outputData = array_merge($outputData, [
                    $subId => $subStep->getOutput()
                ]);
            }

        }

        return $outputData;
    }
}
