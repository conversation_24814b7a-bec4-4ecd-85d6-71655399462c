<?php

namespace App\Console\Commands;

use App\CurationFeed;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class CurationFetchFeeds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'curation:fetch_feeds';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch curation RSS feeds';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $fetchedFeeds = 0;

        // we only fetch 100 feeds at a time
        CurationFeed::where('fetched_at', '<', now()->subDays(1))
            ->orWhereNull('fetched_at')
            ->chunk(500, function ($feeds) use(&$fetchedFeeds) {
                /** @var CurationFeed[]|Collection $feeds */

                /**
                 * @param $url
                 * @return CurationFeed|null
                 */
                $findFeedByUrl = function($url) use ($feeds) {
                    return $feeds->first(function($feed) use ($url) {
                        return $feed->url == $url;
                    });
                };

                $client = guzzle_client([
                    'headers' => [
                        'User-Agent' => config('app.name') . ' HTTP Agent/1.0',
                        'Accept' => 'application/atom+xml, application/rss+xml, application/rdf+xml;q=0.9, application/xml;q=0.8, text/xml;q=0.8, text/html;q=0.7, unknown/unknown;q=0.1, application/unknown;q=0.1, */*;q=0.1',
                    ],
                ]);

                $requestGenerator = function() use ($client, $feeds) {
                    foreach ($feeds as $feed) {
                        if($feed->shouldFetch()) {
                            yield $feed->url => new Request('GET', $feed->url);
                        }
                    }
                };

                $pool = new Pool($client, $requestGenerator(), [
                    'concurrency' => 100,
                    'fulfilled' => function(Response $response, $url) use ($findFeedByUrl, &$fetchedFeeds) {

                        $feed = $findFeedByUrl($url);
                        if(!$feed) {
                            // cant happen
                            return;
                        }
                        $body = $response->getBody()->getContents();
                        $this->info('Fetched feed: ' . $feed->url);

                        $fetchedItems = $feed->fetch($body);
                        if ($fetchedItems > 0) {
                            $fetchedFeeds++;
                            $this->info('Fetched ' . $fetchedItems . ' items from feed: ' . $feed->url);
                        }
                    },
                    'rejected' => function(\Exception $reason, $url) use ($findFeedByUrl) {
                        $feed = $findFeedByUrl($url);
                        if(!$feed) {
                            return;
                        }
                        $feed->fetch(null, $reason);
                        $this->info('Failed to fetch feed: ' . $feed->url);
                    },
                ]);

                // Initiate the transfers and create a promise
                $promise = $pool->promise();

                // Force the pool of requests to complete
                try {
                    $promise->wait();
                } catch (\Throwable $e) {
                    if(!Str::contains($e->getMessage(), ['Cannot change a rejected promise to fulfilled', 'not valid header name'])) {
                        report($e);
                    }
                    $this->error('Failed to fetch some feeds: ' . $e->getMessage());
                }

                // stop if 100 feeds are done
                if ($fetchedFeeds >= 500) {
                    return false;
                }

                return true;
        });
    }
}
