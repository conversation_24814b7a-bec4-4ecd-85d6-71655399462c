<template>
    <div class="row">
        <div class="col-12 mt-4">
            <h2>Welcome, {{user && user.name}}!</h2>
            <div class="row">
                <div class="col-12 col-md-4 mb-2">
                    <div class="card border">
                        <div v-if="loadingElement.totalFeed" v-html="spinnerHtml"></div>
                        <a class="text-default card-body dashboard-card p-2" href="/app/respond/feeds" v-else>
                            <div>
                                <span>Unread Feeds</span>
                            </div>
                            <div>
                                <h4 class="mb-0">{{ totalFeed }}</h4>
                            </div>
                        </a>
                    </div>
                </div>
                <div class="col-12 col-md-4">
                    <div class="card border">
                        <div v-if="loadingElement.totalSchedulePost" v-html="spinnerHtml"></div>
                        <a class="text-default dashboard-card card-body p-2" href="/app/publish/posts" v-else>
                            <div>
                                <span>Scheduled Posts</span>
                            </div>
                            <div>
                                <h4 class="mb-0">{{ totalSchedulePost }}</h4>
                            </div>
                        </a>
                    </div>
                </div>
                <div class="col-12 col-md-4">
                    <div class="card border">
                        <div v-if="loadingElement.totalAutomation" v-html="spinnerHtml"></div>
                        <a class="text-default card-body dashboard-card p-2" href="/app/automations" v-else>
                            <div>
                                <span>Total Automations</span>
                            </div>
                            <div>
                                <h4 class="mb-0">{{ totalAutomation }}</h4>
                            </div>
                        </a>
                    </div>
                </div>
                <div class="col-12 mt-4" v-if="failedPost || inactiveAccountCount">
                    <h5>Action required</h5>
                    <div class="card border p-4">
                        <div class="row">
                            <div class="col-12 col-md-6" v-if="failedPost">
                                <div class="card border" :style="{ 'border-color': 'red !important' }">
                                    <a class="text-default dashboard-card card-body p-2" href="/app/publish/posts">
                                        <div>
                                            <span>Failed Post</span>
                                        </div>
                                        <div>
                                            <h4 class="mb-0">{{ failedPost }}</h4>
                                        </div>
                                    </a>
                                </div>
                            </div>
                            <div class="col-12 col-md-6" v-if="inactiveAccountCount">
                                <div class="card border" :style="{ 'border-color': 'red !important' }">
                                    <a class="text-default dashboard-card card-body p-2" href="/app/accounts">
                                        <div>
                                            <span>Inactive Accounts</span>
                                        </div>
                                        <div>
                                            <h4 class="mb-0">{{ inactiveAccountCount }}</h4>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 mt-4">
        <div class="row">
        <div class="col-12 col-md-6 col-sm-12">
            <div class="card border">
                <h5 class = "text-center" >Account Followers</h5>
                <div v-if="loadingElement.totalAccountGrowth" v-html="spinnerHtml"></div>
                <div class="w-100" ref="growthParent" v-else>                
                <LineChart :chartData="accountGrowth" :chartWidth="chart.accountGrowthWidth" ref="accountGrowth"  />
                </div>
            </div>
            </div>
            <div class="col-12 col-md-6 col-sm-12">
                <div class="card border">
                <h5 class="text-center" >Publishing Behavior</h5>
                <div v-if="loadingElement.publishingBehaviorChart" v-html="spinnerHtml"></div>
                <div class="w-100" ref="publishingParent" v-else >
                <PieChart :chartData="publishingBehavior" :chartWidth="chart.publishingBehaviorWidth" ref="publishingBehavior"/>

                </div>
            </div>
            </div>
            </div>
        </div>

        <div class="col-12 mt-4">
            <h5>Posts Published Vs Engagements</h5>
            <div class="col-12 card border d-flex justify-content-center">
                <div v-if="loadingElement.publishingVsEngagementChart" v-html="spinnerHtml"></div>
                <LineChart :chartData="postsAndEngagements" :chartWidth="1070" ref="postsAndEngagements" v-else />
            </div>
        </div>
        <div class="col-12 mt-4">
            <div class="d-flex justify-content-between mb-1">
                <h5>Top Posts <span class="text-muted h6" v-if="topPosts.length" style="font-size: 12px">(Top 5 posts)</span></h5>
                <div class="form-group mb-0">
                    <select class="w-100" @change="getTopPosts" v-model="topPostsMetric" v-selectpicker>
                        <option v-for="(metric, i) in ['comments', 'likes', 'impressions']" :key="i" :value="metric">
                            {{ metric.toUpperCase() }}
                        </option>
                    </select>
                </div>
            </div>
            <div class="card border" v-if="loadingElement.topPosts">
                <div class="card-body d-flex justify-content-center">
                    <div v-html="spinnerHtml"></div>
                </div>
            </div>
            <div v-else>
                <div v-if="!topPosts.length">
                    <div class="card border">
                        <div class="card-body d-flex justify-content-center">
                            <span>No data to show</span>
                        </div>
                    </div>
                </div>
                <div v-else v-for="(data, index) in topPosts.slice(0,5)" :key="index">
                    <Post :post="data.post" />
                </div>
            </div>
        </div>
        <div class="col-12 mt-4">
        <h5>Top Blogs  <span class="text-muted h6" v-if="topBlogs.length" style="font-size: 12px">(Top 5 blogs)</span></h5>  
        <div class="row">    
            <div class="card col-md-4 mb-6"  v-for="(blog, index) in topBlogs.slice(0,5)" :key="index">
                <a :href="blog.link" target="_blank" class="blog-card">
                    <div class="card-top">                    
                        <img class="rounded"  width="100%" height="100%" :src="blog.img" :alt="blog.title" />   
                    </div>    
                    <div class="card-body">
                        <div class="text-center">
                           <b>                             
                                {{ blog.title }}                                
                            </b>
                        </div>
                        <div class="text-right text-muted">
                            <small>{{ new Date(blog.pubDate).toDateString() }}  </small>
                        </div>
                    </div>  
                </a>                                          
            </div>
        </div>    
        </div>
    </div>
</template>

<script>
import Post from './Post.vue';
import { getAccounts } from "../../data";
import { axios, axiosErrorHandler, spinnerHtml } from "../../components";

export default {
    name: "Dashboard",

    components: {
        Post
    },
    mounted() {
        this.getFeedsCount();
        this.getScheduledPostsCount();
        this.getAutomationsCount();
        this.getInactiveAccountCount();
        this.accountFollowersChart();
        this.publishingBehaviorChart();
        this.publishingVsEngagementChart();
        this.getTopPosts();
        this.getUser();
        this.getBlogs();
      
        if(this.$refs.growthParent){
            this.chart.accountGrowthWidth = this.$refs.growthParent.offsetWidth;
        }
        if(this.$refs.publishingParent){
            this.chart.publishingBehaviorWidth = this.$refs.publishingParent.offsetWidth;
        }

    },
    data() {
        return {
            totalAccountGrowth: 0,
            accountGrowth: [],
            publishingBehavior: [],
            engagementsData: [],
            totalEngagements: 0,
            totalPostsPublished: 0,
            totalFeed: 0,
            totalSchedulePost: 0,
            totalAutomation: 0,
            currentPlan: 0,
            postsAndEngagements: [],
            topPosts: [],
            topBlogs: [],
            user:null,
            failedPost: 0,
            inactiveAccountCount: 0,
            topPostsMetric: 'comments',
            chart:{
                accountGrowthWidth: 520,
                publishingBehaviorWidth: 520
            },
            loadingElement: {
                totalAccountGrowth: false,
                publishingBehaviorChart: false,
                publishingVsEngagementChart: false,
                totalEngagements: false,
                totalPostsPublished: false,
                topPosts: false,
                totalFeed: false,
                totalSchedulePost: false,
                totalAutomation: false,
                currentPlan: false,
            },
        };
    },
    computed: {
        spinnerHtml: () => spinnerHtml,
    },
    methods: {
        async accountFollowersChart() {
            this.loadingElement.totalAccountGrowth = true;
            let data = null;
            try {
                const res = await axios.get("/api/v1/reports/account-metrics/metrics",{
                    params: {
                        metric: 'followers'
                    }
                });
                data = res.data;
                this.accountGrowth = [];
                this.totalAccountGrowth = 0;
                for (const [accountName, accData] of Object.entries(data)) {                
                    for (let i = 0; i < accData["followers"].length; i++) {
                        this.totalAccountGrowth += accData["followers"][i][1];
                    }
                    this.accountGrowth.push({
                        name: accountName,
                        type: 'spline',
                        data: accData["followers"],
                    });
                }
                this.loadingElement.accountGrowthGraph = false;
                this.loadingElement.totalAccountGrowth = false;
            } catch (e) {
                axiosErrorHandler(e);
            }
        },
        async publishingBehaviorChart() {
            this.loadingElement.publishingBehaviorChart = true;
            try {
                let res = await axios.get("/api/v1/reports/post-metrics/publishing_behavior");
                this.publishingBehavior = res.data ? res.data.data : [];
                this.imagePostCount = res.data.data[0] ? res.data.data[0].y.toFixed(2) : 0;
                this.videoPostCount = res.data.data[1] ? res.data.data[1].y.toFixed(2) : 0;
                this.textPostCount = res.data.data[2] ? res.data.data[2].y.toFixed(2) : 0;
            } catch (e) {
                axiosErrorHandler(e);
            }
            this.loadingElement.publishingBehaviorChart = false;
        },
        async publishingVsEngagementChart() {
            this.loadingElement.totalEngagements = true;
            this.loadingElement.totalPostsPublished = true;
            this.loadingElement.publishingVsEngagementChart = true;
            let data = null;
            let postsPublished = null;
            try {
                const res = await axios.get("/api/v1/reports/post-metrics/metrics", {
                    params: {
                        metric_type: "comments,shares,likes",
                    },
                });
                let data = res.data
                this.totalEngagements = 0
                let engagementsData = [];
                if( data.likes && data.likes.length) {
                    for (let i = 0; i < data.likes.length; i++) {
                        engagementsData.push([
                            data["likes"][i][0],
                            data["likes"][i][1] + data["comments"][i][1] + data["shares"][i][1],
                        ]);
                        this.totalEngagements += data["likes"][i][1] + data["comments"][i][1]  + data["shares"][i][1];
                    }  
                }
                this.postsAndEngagements = []
                this.postsAndEngagements.push({
                    type: 'spline',
                    name: 'Engagements',
                    data: engagementsData,
                })
            } catch (e) {
                axiosErrorHandler(e);
            }

            try {
                const res = await axios.get("/api/v1/reports/post-metrics/posts_count");
                postsPublished = res.data.data;
                this.totalPostsPublished = 0;
                postsPublished.forEach((post) => (this.totalPostsPublished += post[1]));
                this.postsAndEngagements.push({
                    type: "spline",
                    name: "Posts Published",
                    data: postsPublished,
                });
            } catch (e) {
                axiosErrorHandler(e);
            }
            this.loadingElement.totalPostsPublished = false;
            this.loadingElement.totalEngagements = false;
            this.loadingElement.publishingVsEngagementChart = false;
        },
        async getTopPosts() {
            this.loadingElement.topPosts = true;
            try {
                const res = await axios.get("/api/v1/reports/post-metrics/top_posts", {
                    params: {
                        metric: this.topPostsMetric
                    }
                });
                let posts = res.data ? res.data : [];
                let accounts = await getAccounts();
                posts.forEach((item, index) => {
                    let acc = [];
                    acc = [this.getAccount(accounts, item.post.account_id)];
                    item.post.accounts = acc;
                    item.post.insights = [
                        { type: "likes", value: item.post.insights.likes ? item.post.insights.likes : 0 },
                        { type: "comments", value: item.post.insights.comments ? item.post.insights.comments : 0 },
                        {
                            type: "impressions",
                            value: item.post.insights.impressions ? item.post.insights.impressions : 0,
                        },
                        {
                            type: "engagements",
                            value:
                                (item.post.insights.likes ? item.post.insights.likes : 0) +
                                (item.post.insights.comments ? item.post.insights.comments : 0),
                        },
                    ];
                });
                this.topPosts = posts;
                this.loadingElement.topPosts = false;
            } catch (e) {
                axiosErrorHandler(e);
                this.loadingElement.topPosts = false;
            }
        },
        getAccount(all, id) {
            let account = {};
            all.forEach((acc) => {
                if (acc.id === id) account = acc;
            });
            return account;
        },
        async getFeedsCount() {
            this.loadingElement.totalFeed = true;
            this.totalFeed = 0;
            try {
                const res = await axios.get("/api/v1/reports/unread-feeds-count");
                this.totalFeed = res.data.count;
                this.loadingElement.totalFeed = false;
            } catch (e) {
                axiosErrorHandler(e);
                this.loadingElement.totalFeed = false;
            }
        },
        async getScheduledPostsCount() {
            this.loadingElement.totalSchedulePost = true;
            this.totalSchedulePost = 0;
            try {
                const res = await axios.get("/api/v1/reports/pending-posts-count");
                this.totalSchedulePost = res.data.count;
                this.loadingElement.totalSchedulePost = false;
            } catch (e) {
                axiosErrorHandler(e);
                this.loadingElement.totalSchedulePost = false;
            }
        },
        async getAutomationsCount() {
            this.loadingElement.totalAutomation = true;
            this.totalAutomation = 0;
            try {
                const res = await axios.get("/api/v1/reports/automations-count");
                this.totalAutomation = res.data.count;
                this.loadingElement.totalAutomation = false;
            } catch (e) {
              axiosErrorHandler(e);
              this.loadingElement.totalAutomation = false;
            }
        },
        async getInactiveAccountCount() {
            this.loadingElement.inactiveAccountCount = true;
            this.inactiveAccountCount = 0;
            try {
                const res = await axios.get("/api/v1/reports/inactive-accounts-count");
                this.inactiveAccountCount = res.data.count;
            } catch (e) {
                axiosErrorHandler(e);
            }
            this.loadingElement.inactiveAccountCount = false;
        },
        async getUser() {
            try {
                const res = await axios.get("/api/v1/user");
                this.user = res.data
            } catch (e) {
                axiosErrorHandler(e);
            }
        },
        async getBlogs() {
            try {
                const res = await axios.get("/api/v1/recent_blogs");
                this.topBlogs = res.data
                console.log(this.topBlogs);
            } catch (e) {
                axiosErrorHandler(e);
            }
        },
         async getFailedPost() {
            this.loadingElement.failedPost = true;
            this.failedPost = 0;
            try {
                const res = await axios.get("/api/v1/reports/failed-posts-count");
                this.failedPost = res.data.count;
            } catch (e) {
                axiosErrorHandler(e);
            }
            this.loadingElement.failedPost = false;
        },

    },
};
</script>
<style scoped lang="scss">
.cardHeight {
    height: 250px;
}
.blog-card{
    box-shadow: none;
    &:hover{
        box-shadow: 0px 6px 11px rgb(0 0 0 / 6%);
    }
}
</style>
