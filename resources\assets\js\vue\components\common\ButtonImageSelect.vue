<template>
    <button class="btn btn-light" type="button" :disabled="disabled">
        <label class="cursor-pointer"
                @click="removeImage" v-if="image.name" v-tooltip="$lang.get('generic.remove')">
            <i class="ph ph-x ph-md text-danger" aria-hidden="true"></i>
            <span>{{ truncate(image.name, 40) }}</span>
        </label>
        <label
                :class="{'disabled': disabled, 'cursor-pointer': !disabled}" :for="id + 'image'" v-else>
            <i class="ph ph-image" aria-hidden="true"></i>
            <span v-text="$lang.get('publish.attach_image')"></span>
        </label>
        <input class="d-none" ref="input" :name="inputName" type="file" :id="id + 'image'" accept="image/*"
               @change="imageUploaded" :disabled="disabled"/>
    </button>
</template>

<script>
import size from "filesize";
import _ from "lodash";
import Compressor from "compressorjs";
import { alertify } from "../../../components";
export default {
    name: "ButtonImageSelect",
    data() {
        return {
            image: {
                name: null
            },
            id: null
        };
    },
    props: ["disabled", "inputName", "validator", "name", "onImageUpdate"],
    methods: {
        truncate: _.truncate,
        imageUploaded(e) {
            let imgElem = e.target;
            const _this = this;
            let file = imgElem.files[0];

            const processFile = file => {
                if ("FileReader" in window && file) {
                    // validate extension and file size
                    if (file.size > process.env.MIX_MAX_IMAGE_SIZE) {
                        alertify.error("The selected file is too large. Please try a smaller file.");
                        this.resetFileInput(); // reset file input
                        return;
                    }
                    let reader = new FileReader();
                    reader.onload = () => {
                        // validate file type / ext
                        if (_this.validator(file.type)) {
                            //_this.image.name = imgElem.value.split(/[\\/]/).pop();
                            _this.image.name = file.name;
                            _this.onImageUpdate({
                                file: file
                            });
                        } else {
                            _this.removeImage();
                        }
                    };
                    reader.readAsDataURL(file);
                }
            };

            // file size check
            if (file.size > process.env.MIX_MAX_IMAGE_SIZE) {
                // use compressorjs
                new Compressor(imgElem.files[0], {
                    strict: true,
                    quality: 0.8,
                    checkOrientation: false,
                    maxWidth: 4096,
                    maxHeight: 4096,
                    convertSize: 5000000, // min-size for png to convert to jpg
                    success(result) {
                        let file = new File([result], result.name, {
                            type: result.type || ""
                        });
                        processFile(file);
                    },
                    error(err) {
                        alertify.error(err.message);
                        this.resetFileInput(); // reset file input
                    },
                });
            } else {
                processFile(file); // use original file
            }

        },
        resetFileInput() {
            $(this.$refs.input)
                .attr("type", "")
                .val("")
                .attr("type", "file");
        },
        removeImage(e) {
            this.resetFileInput();
            this.image.name = null;
            this.onImageUpdate({
                file: null
            });
            if (e) e.preventDefault();
        }
    },
    mounted() {
        this.id = this.name;
        this.$bus.$on(this.id + ".removeImage", this.removeImage);
    },
    updated() {
        if (this.disabled) this.removeImage();
    }
};
</script>
