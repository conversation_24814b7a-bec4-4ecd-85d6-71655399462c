<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class AddAccount extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->greeting('Hey ' . explode(' ', ($notifiable->name . ''))[0])
            ->subject('Get Started: Add Social Media Accounts to SocialBu')
            ->line('Have you not started using SocialBu yet?')
            ->line('Add your social media accounts to SocialBu to get started. We hope SocialBu will make your life easier 😊')
            ->line('For any help, we are here whenever you need us. Just reply to this email, or contact us via live chat.')
            ->action('Add Account', url("app/accounts"))
            ->line('Please do not hesitate to contact us if you need any assistance or have any questions.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [

        ];
    }
}
