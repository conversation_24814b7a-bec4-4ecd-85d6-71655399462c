<?php

namespace App\Socialite;

use <PERSON><PERSON>\Socialite\Two\AbstractProvider;
use <PERSON><PERSON>\Socialite\Two\ProviderInterface;
use GuzzleHttp\ClientInterface;
use <PERSON><PERSON>\Socialite\Two\User;

class PinterestProvider extends AbstractProvider implements ProviderInterface
{
    public static function getBaseUrl($url = null, $subdomain = 'www', $domain = 'pinterest.com')
    {
        $baseUrl = 'https://' . $subdomain . '.' . $domain .'/';
        return $baseUrl . ( $url ? $url : '' );
    }
    /**
     * {@inheritdoc}
     */
    protected function getAuthUrl($state)
    {
        return $this->buildAuthUrlFromBase($this::getbaseUrl('oauth/'), $state);
    }

    /**
     * {@inheritdoc}
     */
    protected function getTokenUrl()
    {
        return $this::getbaseUrl('v5/oauth/token', 'api');
    }

    /**
     * {@inheritdoc}
     */
    public function getAccessTokenResponse($code)
    {
        $postKey = (version_compare(ClientInterface::VERSION, '6') === 1) ? 'form_params' : 'body';

        $response = $this->getHttpClient()->post($this->getTokenUrl(), [
            'headers' => [ 
                'Authorization' => 'Basic '.base64_encode($this->clientId.':'.$this->clientSecret),
                'Content-Type'  => 'application/x-www-form-urlencoded'
            ],
            $postKey => $this->getTokenFields($code),
        ]);

        return json_decode($response->getBody(), true);
    }

    /**
     * {@inheritdoc}
     */
    protected function getTokenFields($code)
    {
        return [
            'code' => $code,
            'redirect_uri' => $this->redirectUrl,
            'grant_type' => 'authorization_code',
        ];
    }
    

    /**
     * {@inheritdoc}
     */
    protected function getUserByToken($token)
    {
        $response = $this->getHttpClient()->get(
            $this::getBaseUrl('v5/user_account', 'api'),
            [
                'headers' => [
                    'Authorization' => 'Bearer '.$token,
                ],
            ]
        );

        return json_decode((string) $response->getBody(), true);
    }
    

    /**
     * {@inheritdoc}
     */
    protected function mapUserToObject(array $user)
    {
        return (new User())->setRaw($user)->map(
            [
                'id'       => $user['username'],
                'nickname' => $user['username'],
                'name'     => null,
                'email'    => null,
                'avatar'   => $user['profile_image'],
            ]
        );
    }
}