<?php
namespace App\Helpers;

use <PERSON>\TwitterOAuth\TwitterOAuth;
use App\Account;
use Illuminate\Support\Str;
use GuzzleHttp\Client;
use GuzzleHttp\Subscriber\Oauth\Oauth1;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\RequestOptions;
use FFMpeg\FFProbe;
use Request;

// TwitterOauthCustom
class TwitterOauthCustom extends TwitterOAuth {
    private static $instance;
    public function withToken($token, $login_via_new_x_api = false){
        if($login_via_new_x_api){
            $connection = new self(config('services.twitter.client_id'), config('services.twitter.client_secret'), null, $token['token']);
        }else {
            $connection = new self(config('services.twitter.consumer_key'), config('services.twitter.consumer_secret'), $token['token'], $token['secret']);
        }
        $connection->setTimeouts(60, 180);
        return $connection;
    }
    public static function withoutToken(){
        if(self::$instance == null) {
            self::$instance = new self(config('services.twitter.consumer_key'), config('services.twitter.consumer_secret'));
            self::$instance->setTimeouts(60, 180);
        }
        return self::$instance;
    }

    /**
     * Upload a local file to Twitter and return media ID.
     * @param $localPath
     * @return string Media ID
     * @throws \Exception
     */
    public function uploadMediaToTwitter($localPath, Account $account){
        $tw = $this;
        $mime = mime_content_type($localPath);
        $media_params = [
            'media' => $localPath,
            'media_type' => $mime,
            'additional_owners' => [$account->account_id]
        ];

        $newPath = $localPath;

        if(Str::contains($mime, 'video')){
            $ffprobe = FFProbe::create();
            $durationSecs = (float) $ffprobe->format($localPath)->get('duration');
    
            $amplifyVideo = $durationSecs > 140;
            
            if($amplifyVideo){
                $media_params['media_category'] = 'amplify_video'; // required for videos longer than 30s and proper video upload
                $media_params['total_bytes'] = filesize($localPath); // required for amplify video
            } else {
                $media_params['media_category'] = 'tweet_video'; // required for videos longer than 30s and proper video upload
            }

        } else if(Str::contains($mime, 'gif')){
            $media_params['media_category'] = 'tweet_gif'; // required for gifs
        } else {
            $media_params['media_category'] = 'tweet_image'; // required for images

            // get mimetype ($localPath is absolute path)
            $mimeType = mime_content_type($localPath);

            // if it's an image and type is png, convert to jpg
            $shouldConvertToJPG = Str::contains($mimeType, ['png', 'webp']);

            if($shouldConvertToJPG){
                $img = \Intervention\Image\Facades\Image::make($localPath);

                $newPath = $localPath. '.jpg';

                $img->save($newPath, 90);
            }

            reduce_image_size_if_needed($localPath, 5242880); // 5MB
        }

        $login_via_new_x_api = $account->getOption('login_via_new_x_api');

        if($login_via_new_x_api){

            try {
                $media_id = $tw->customMediaUpload($media_params);
            } catch(\Exception $exception){
                \Log::info($exception->getMessage());
                throw $exception;
            }

            if($newPath !== $localPath){
                unlink($newPath);
            }

            return $media_id;

        } else {

            $uploadTries = 0;
            do {

                try {
                    $uploadRes = $tw->upload("media/upload", $media_params, true); // chunked upload
                } catch(\Exception $exception){
                    $uploadTries++;

                    $shouldRetry = Str::contains($exception->getMessage(), ['SSL protocol error', 'Unable to get media_id']);

                    if(!$shouldRetry || $uploadTries >= 3){
                        throw $exception; // rethrow exception if we don't want to retry or we already retried 3 times
                    }
                    sleep(2); // wait before retrying
                    continue; // retry
                }

                break; // if we got here, we successfully executed the request

            } while(true);

            if($newPath !== $localPath){
                unlink($newPath);
            }

            if(!isset($uploadRes->media_id_string)){
                $lastBody = $tw->getLastBody();
                if(!empty($lastBody->errors)){
                    throw new \Exception($lastBody->errors[0]->message);
                }

                $lastBody = $tw->getLastBody();
                if(!empty($lastBody->error)){
                    throw new \Exception((string) $lastBody->error);
                }

                report(new \Exception('media_id_string not set: ' . json_encode($uploadRes)));
                throw new \Exception('Twitter response was incomplete');
            }

            if(isset($uploadRes->processing_info)){
                // we need to wait for media to be ready
                $limit = 0;
                $status = null;
                do {
                    $statusNew = $tw->mediaStatus($uploadRes->media_id_string);

                    if(!$statusNew){
                        // for some reason, we didn't get a response
                        sleep(5);
                        $limit++;
                        continue;
                    }

                    $status = $statusNew;

                    if($status->processing_info->state === 'failed'){
                        \Log::info(json_encode($status));
                        throw new \Exception('Twitter returned error: ' . $status->processing_info->error->name);
                    }

                    if(isset($status->processing_info->check_after_secs))
                        sleep($status->processing_info->check_after_secs);
                    $limit++;
                } while( (!$status || $status->processing_info->state !== 'succeeded') && $limit <= 10 );
            }

            if ($tw->getLastHttpCode() == 200 || $tw->getLastHttpCode() == 201) {
                return $uploadRes->media_id_string;
            } else {
                \Log::info(json_encode($tw->getLastBody()));
                throw new \Exception('HTTP error (' . $tw->getLastHttpCode() . ') while uploading an attachment to twitter');
            }
        }
    }

    /**
     * @throws \Exception
     */
    public function customMediaUpload($params = []){
        if (!is_readable($params['media']) || !file_exists($params['media'])) {
            throw new \InvalidArgumentException(
                'You must supply a readable file',
            );
        }

        $client = guzzle_client([
            'headers' => [
                'Authorization' => 'Bearer ' . $this->getBearer(),
                'Content-Type' => 'application/json'
            ],
        ]);

        // initialize the upload
        try {
            $init = $client->post('https://api.twitter.com/2/media/upload/initialize', [
                RequestOptions::JSON => array_filter([
                    'media_type' => $params['media_type'],
                    'total_bytes' => $params['total_bytes'] ?? filesize($params['media']),
                    'media_category' => $params['media_category'] ?? null,
                    'additional_owners' => $params['additional_owners'] ?? null,
                ]),
            ]);
        } catch(\Exception $exception){
            report($exception);
            throw new \Exception('Error in media upload: ' . $exception->getMessage());
        }

        $response = json_decode($init->getBody()->getContents(), true);

        if(isset($response['errors'])){
            throw new \Exception('Error in response from twitter: ' . json_encode($response['errors']));
        }

        $media_id = $response['data']['id'] ?? null;

        // append the media in chunks
        $segmentIndex = 0;
        $media = fopen($params['media'], 'rb');
        while (!feof($media)) {
            try {
                $append = $client->post('https://api.twitter.com/2/media/upload/' . $media_id . '/append', [
                    RequestOptions::MULTIPART => [
                        [
                            'name' => 'media',
                            'contents' => fread($media, 1048576), //1MB
                        ],
                        [
                            'name' => 'segment_index',
                            'contents' => $segmentIndex++,
                        ],
                    ],
                ]);
            }catch(\Exception $exception){

                // log if we have a response body
                if ($exception instanceof \GuzzleHttp\Exception\ClientException) {
                    $responseBody = $exception->getResponse()->getBody()->getContents();
                    \Log::error('Twitter media append error: ' . $responseBody);
                }

                report($exception);
                throw new \Exception('Error in chunk upload: ' . $exception->getMessage());
            }

            $responseBody = json_decode($append->getBody()->getContents(), true);

            if (isset($responseBody['errors'])) {
                throw new \Exception('Error in chunk upload: ' . json_encode($responseBody['errors']));
            }
        }
        fclose($media);

        // finalize the upload
        $response = $client->post('https://api.twitter.com/2/media/upload/' . $media_id . '/finalize');

        $response = json_decode($response->getBody()->getContents(), true);

        if(isset($response['errors'])){
            \Log::info(json_encode($response));
            throw new \Exception('Twitter returned error: ' . json_encode($response['errors']));
        }

        if(isset($response['data']['processing_info'])){
            if($response['data']['processing_info']['state'] === 'failed'){
                \Log::info(json_encode($response));
                throw new \Exception('Twitter media upload failed.' );
            }
            // we need to wait for the media to be ready
            $tries = 0;
            $status = null;

            // check the status of the media upload
            do {
                try {
                    $status = $client->get('https://api.twitter.com/2/media/upload/', [
                        'query' => [
                            'media_id' => $media_id,
                            'command' => 'STATUS',
                        ],
                    ]);
                } catch(\Exception $exception){
                    report($exception);
                    throw $exception;
                }
                
                $status = json_decode($status->getBody()->getContents(), true);

                if(isset($status['data']['processing_info']['check_after_secs'])){
                    sleep($status['data']['processing_info']['check_after_secs']);
                }
                $tries++;
            } while( ($status['data']['processing_info']['state'] !== 'succeeded') && $tries <= 20 );
        }

        if(isset($status) && $status['data']['processing_info']['state'] !== 'succeeded'){
            throw new \Exception('Twitter media upload failed: ' . json_encode($status) );
        }

        return $media_id;
    }

    /**
     * Process threaded replies (add/remove files where needed, validate, etc)
     * @param Account $account
     * @param array $threadedReplies
     * @param array $oldThreadedReplies
     * @param false $validateOnly
     * @return array Threaded replies after processing, ready to save
     * @throws \Illuminate\Validation\ValidationException|\Exception
     */
    public static function processThreadedReplies(Account $account, array $threadedReplies, array $oldThreadedReplies = [], $validateOnly = false){

        if(!$threadedReplies){
            $threadedReplies = [];
        }

        $existingAttachmentsForThread = [];
        $newAttachmentsForThread = [];
        foreach($oldThreadedReplies as $tweet){
            if(isset($tweet['media']) && !empty($tweet['media'])){
                foreach($tweet['media'] as $media){
                    $existingAttachmentsForThread[] = $media['key'];
                }
            }
        }
        foreach($threadedReplies as $tweet){
            if(isset($tweet['media']) && !empty($tweet['media'])){
                foreach($tweet['media'] as $media){
                    $newAttachmentsForThread[] = $media['key'];
                }
            }
        }
        // check if thread attachments need to be destroyed; we will delete them later
        $filesToDestroy = collect($existingAttachmentsForThread)->filter(function($file) use($newAttachmentsForThread) {
            return !in_array($file, $newAttachmentsForThread);
        })->toArray();

        $filesNotToCopy = collect($existingAttachmentsForThread)->filter(function($file) use($newAttachmentsForThread) {
            return in_array($file, $newAttachmentsForThread);
        })->toArray();

        if(count($threadedReplies) > 50){
            throw new \Exception('Thread replies cannot be more than 50');
        }

        foreach ($threadedReplies as $tweetIndex => $tweet){

            if(is_string($tweet)){ // old format
                $tweet = ['text' => $tweet];
                $threadedReplies[$tweetIndex] = $tweet;
            }

            if(isset($tweet['tweet'])){
                $tweet['text'] = $tweet['tweet'];
                unset($tweet['tweet']);
            }

            if(!isset($tweet['media'])){
                $tweet['media'] = [];
            }
            if(!isset($tweet['text'])){
                $tweet['text'] = '';
            }

            // either text or media is required
            if(strlen(trim($tweet['text'])) == 0 && count($tweet['media']) == 0){
                throw new \Exception('[Thread Reply ' . ($tweetIndex + 1) . '] Tweet must have either text or media');
            }

            $media = $tweet['media'];

            // check if valid attachment types
            foreach( $media as $file){
                if(!in_array($file['extension'], $account->getAttachmentTypes())){
                    throw new \Exception('[Thread Reply ' . ($tweetIndex + 1) . '] Invalid media type: ' . $file['extension']);
                }
            }

            $hasVideo = collect($media)->filter(function ($f) {
                return in_array($f['extension'], ['mp4', 'm4v', 'mov', 'qt', 'avi',]);
            })->count() > 0;

            $post_type = $hasVideo ? 'video' : 'image';

            \Validator::make(['attachments' => $media], [
                'attachments' => 'max:' . $account->getMaxAttachments($post_type),
                'attachments.*.name' => 'string|required',
                'attachments.*.extension' => 'string|required',
                'attachments.*.key' => 'string|required',
                'attachments.*.secureKey' => 'string|required',
                'attachments.*.size' => 'required',
            ])->validate();

            // move attachment or copy if it's not temporary, we only do this when not validating (actual data saving)
            $media = collect($media)->map(function ($file) use($account, $tweetIndex, $filesNotToCopy, $validateOnly) {

                // make sure the secure key is correct
                $decryptedKey = decrypt($file['secureKey']);
                if($decryptedKey != $file['key']){
                    throw new \Exception('[Thread Reply ' . ($tweetIndex + 1) . '] Invalid secure key');
                }

                // make sure it exists
                if(!\Storage::cloud()->exists($file['key'])){
                    throw new \Exception('[Thread Reply ' . ($tweetIndex + 1) . '] Media file not available: ' . $file['name']);
                }

                if(!$validateOnly){
                    // move the file to the correct location
                    $newKey = 'attachments/' . $account->id . '_' . time() . '_' . str_random(40) . sha1(time() . $file['name'] . $file['extension'] . user()->id) . '.' . $file['extension'];
                    if (isset($file['temporary']) && $file['temporary']) {
                        \Storage::cloud()->move($file['key'], $newKey);
                        unset($file['temporary']); // unset temporary flag
                        $file['key'] = $newKey;
                        $file['secureKey'] = encrypt($file['key']);
                    } else if(!in_array($file['key'], $filesNotToCopy)) {
                        \Storage::cloud()->copy($file['key'], $newKey);
                        $file['key'] = $newKey;
                        $file['secureKey'] = encrypt($file['key']);
                    }

                    // also delete temporary url
                    if(isset($file['url'])){
                        unset($file['url']);
                    }

                    // make sure size is integer
                    if(isset($file['size'])){
                        $file['size'] = (int) $file['size'];
                    }

                }

                return $file;
            })->toArray();
            $tweet['media'] = $media;

            // update attachments if moved
            $threadedReplies[$tweetIndex] = $tweet;
        }

        if(!$validateOnly) {
            // we delete files that are not in the new attachments for threaded replies
            \Storage::cloud()->delete($filesToDestroy);
        }

        // make sure threaded replies is an array and not an object
        return array_values($threadedReplies);
    }


    /**
     * Adds alt text to media on Twitter
     * @param string $media_id
     * @param string $alt_text
     * @param Account $account
     * @throws \Exception
     */
    public function addMediaAltText(string $media_id, string $alt_text, Account $account){

        if($account->type !== 'twitter.profile'){
            throw new \Exception('Account is not a Twitter profile');
        }

        $login_via_new_x_api = $account->getOption('login_via_new_x_api');

        if($login_via_new_x_api){
            $client = guzzle_client([
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->getBearer(),
                    'Content-Type' => 'application/json'
                ],
            ]);

            try {
                $res = $client->post("https://api.twitter.com/2/media/metadata", [
                    RequestOptions::JSON => array_filter([
                        'id' => $media_id,
                        'metadata' => [
                            "alt_text" =>  [
                                "text" => $alt_text
                            ]
                        ]
                    ]),
                ]);
            } catch (\Exception $exception){
                report($exception);
                throw new \Exception('Unable to upload alt text');
            }
        } else {
            $token = json_decode($account->token, true);
            $middleware = new Oauth1([
                'consumer_key'    => config('services.twitter.consumer_key'),
                'consumer_secret' => config('services.twitter.consumer_secret'),
                'token'           => $token['token'],
                'token_secret'    => $token['secret'],
            ]);

            $stack = HandlerStack::create();
            $stack->push($middleware);

            $client = new Client([
                'handler' => $stack,
                'auth' => 'oauth',
                'connect_timeout' => 60,
                'read_timeout' => 120,
                'timeout' => 120,
                'headers'=>[
                    'Content-Type'=> 'application/json; charset=UTF-8',
                ]
            ]);

            try {
                $client->post("https://upload.twitter.com/1.1/media/metadata/create.json", [
                    RequestOptions::JSON => array_filter([
                        'media_id' => $media_id,
                        "alt_text" =>  [
                            "text" => $alt_text // Must be <= 1000 chars
                        ],
                    ]),
                ]);
            } catch (\Exception $exception){
                report($exception);
                throw new \Exception('Unable to upload alt text');
            }
        }

    }


}
