<template>
    <div class="cursor-default">
        <picker set="native" @select="selectEmoji" :native="true" :emoji-tooltip="true" :infinite-scroll="false" :show-preview="false" title="Select an emoji" :emoji-size="16" />
    </div>
</template>

<script>
    import { Picker } from 'emoji-mart-vue'
    export default {
        name: "EmojiPicker",
        components: {
            Picker
        },
        props: [
            "onSelect"
        ],
        data() {
            return {


            }
        },
        methods: {
            selectEmoji(emoji) {
                //console.log(emoji);
                this.onSelect && this.onSelect(emoji);
            }
        }
    }
</script>

<style lang="scss" scoped>
    .emoji-mart {
        max-height: 260px !important;
        border: none !important;
    }
</style>