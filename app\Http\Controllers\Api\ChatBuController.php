<?php

namespace App\Http\Controllers\Api;

use App\Helpers\ContentGenerator;
use GuzzleHttp\Exception\ClientException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Validation\ValidationException;

class ChatBuController extends Controller
{
    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function messages(Request $request){
        return response()->json($this->loadMessages());
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * @throws \Exception
     */
    public function sendMessage(Request $request){
        $this->validate($request, [
            'message' => 'required|string|max:20000',
        ]);

        $user = user();

        $userPlan = $user->getPlan(true);
        if(!in_array($userPlan, ['super', 'supreme', 'custom',])){
            $teams =$user->joinedTeams;
            foreach ($teams as $team){
                $teamUser = $team->user;
                if(in_array($teamUser->getPlan(true), ['super', 'supreme', 'custom',])){
                    $userPlan = $teamUser->getPlan(true);
                    break;
                }
            }
            if(!in_array($userPlan, ['super', 'supreme'])){
                abort(400, 'Upgrade required: Only available in Super and Supreme plans. You need to upgrade your plan or be in a team that has access to Bu AI');
            }
        }

        $messages = $this->loadMessages();
        try {
            $reply = ContentGenerator::getInstance()->chatWithBu(
                trim($request->input('message')),
                $messages
            );
        } catch (ClientException $clientException){

            \Log::info($clientException->getResponse()->getBody() . '');

            throw $clientException;
        }

        $reply = trim($reply);

        $messages[] = [
            'sender' => 'me',
            'content' => trim($request->input('message')),
            'timestamp' => time(),
        ];

        $messages[] = [
            'sender' => 'you',
            'content' => $reply,
            'timestamp' => time(),
        ];

        $this->saveMessages($messages);

        return response()->json($reply);
    }

    /**
     * @param Request $request
     * @return void
     */
    public function deleteMessages(Request $request){
        $this->saveMessages([]);
    }

    private function loadMessages(){
        // for now, we just store the messages in user's options
        return user()->getOption('chatbu_messages', []);
    }

    private function saveMessages($messages){

        // only store last 50 messages
        if(count($messages) > 50){
            $messages = array_slice($messages, -50);
        }

        if(empty($messages)) {
            user()->removeOption('chatbu_messages');
        } else {
            // for now, we just store the messages in user's options
            user()->setOption('chatbu_messages', $messages);
        }
    }
}
