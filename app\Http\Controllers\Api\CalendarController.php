<?php

namespace App\Http\Controllers\Api;

use App\Account;
use App\Http\Controllers\Json\JsonCollectionController;
use App\Post;
use Exception;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Collection;
use Illuminate\Validation\ValidationException;

class CalendarController extends Controller
{
    /**
     * @param Request $request
     * @return Collection
     * @throws Exception
     */
    public function posts(Request $request){

        $this->_validate($request);

        // get all accounts
        $accounts = $this->_getAccounts();

        // get the posts for the date range
        $scheduledPosts = (new JsonCollectionController())->posts($request, false, 'scheduled_or_awaiting_approval', true);
        $publishedPosts = (new JsonCollectionController())->posts($request, false, 'published', true);
        $draftPosts = (new JsonCollectionController())->posts($request, false, 'draft', true);

        $posts = array_merge(
            $scheduledPosts->all(),
            $publishedPosts->all(),
            $draftPosts->all()
        );

        // free memory
        unset($scheduledPosts, $publishedPosts, $draftPosts);

        return $this->_getData($request, $posts, $accounts);

    }

    /**
     * @param Request $request
     * @throws ValidationException
     */
    private function _validate(Request $request){

        $this->validate($request, [
            'start' => 'required|date',
            'end' => 'required|date',
        ]);

    }

    /**
     * @param Request $request
     * @param array $items
     * @param Collection $accounts
     * @return Collection|\Tightenco\Collect\Support\Collection
     * @throws Exception
     */
    private function _getData(Request $request, array $items, $accounts){

        // get all post ids
        $postIds = collect($items)->pluck('id')->toArray();

        // get all insights for the posts
        $metricsData = Post::getMetricsForPosts($postIds);

        $data = [];
        foreach($items as $_item){

            $item = Post::transform($_item, $metricsData);

            /** @var Account $account */
            $account = $accounts->firstWhere('id', $_item->account_id);
            $accountArr = Account::transform($account);

            $event = [];

            $event['id'] = $_item->id;

            $event['start'] = $_item->publish_at->toIso8601String();
            $event['end'] = $_item->publish_at->addSeconds(1)->toIso8601String();

            $event['title'] = isset($_item->content) && !empty($_item->content) ? e($_item->content) : 'Media';

            $event['description'] = '';

            if(count($item['attachments'])> 0 && $_item instanceof Post){
                $event['description'] = '<small><i class="ph ph-link"></i> ' . count($item['attachments']) . ' attachment' . (count($item['attachments']) > 1 ? 's': '') . '</small>';
            }

            $event['classNames'] = [];
            $event['titleHtml'] = $event['title'];

            if($_item->published_at){
                // is already published post
                $event['classNames'][] = 'opacity-60';
                $event['titleHtml'] = '<i class="ph ph-check"></i> ' . $event['titleHtml'];
                $event['description'] = '<small><i class="ph ph-check"></i> Published</small> <br/>' . $event['description'];
            }

            $event['account'] = e($account->name);

            $event['account_type'] = $account->type;

            if(isset($item['approved']) && !$item['approved']){
                $event['classNames'][] = 'border border-warning';
                $event['description'] = '<small><i class="ph ph-info"></i> Awaiting Approval</small> <br/>' . $event['description'];
            } else if($item['error']){
                $event['classNames'][] = 'border border-danger';
                $event['description'] = '<small><i class="ph ph-info"></i> Error</small> <br/>' . $event['description'];
            }

            $event['description'] = (strlen($event['description']) > 0 ? $event['description'] . '<hr class="my-2"/>' : '')  . str_limit( isset($item['content']) && !empty($item['content']) ? e($item['content']) : "" , 240);

            $item['accounts'] = [
                $accountArr
            ];

            $event['item'] = $item;

            // used only in mobile app
            $event['accounts'] = [
                $accountArr
            ];

            $data[] = $event;
        }

        return collect($data);
    }

    private function _getAccounts(){
        return user()->getAvailableAccounts(['*'], true);
    }

}
