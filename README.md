# SocialBu
socialbu.com

## Backend

* Language: PHP
* Framework: <PERSON><PERSON>
* More: NodeJS (where required)

## Frontend

* CSS Framework: Bootstrap
* Framework: VueJS (used specifically)
* Backend Templates: Lara<PERSON>'s Blade Template Engine

## Development

* Requirements
  * PHP (recommended. 7.1.3, min: 7.1.3, can use: 7.1.x)
  * MySQL (min. 5.7.14)
  * Webserver software (Apache / NGinx etc)
  * GIT
* More requirements
  * Composer (for managing php dependecies)
  * NodeJS (for automation, and managing other things - also used in app)
* Installing dependencies
  * Run `composer install` for php dependencies.
  * Run `npm install` for nodejs dependencies.
* Environment variables (CONFIGURATION)
  * Copy file .env.example as .env
  * Update / add all variables in .env file. Like `APP_URL`, hostname, social api keys etc. Add / update mysql details also.
  * Run `php artisan key:generate` to generate your private secure `APP_KEY`. (used by laravel)
* Compiling assets
  * Run `npm run dev` to compile css and js assets
* Setting up database
  * To setup database tables, run the command
    `php artisan migrate:refresh` (This will flush all data and re-create all the structure)
* Things to remember
  * After making changes to .env (if any) or other config files, make sure to delete cached files and regenerate autoloader:
    * `php artisan cache:clear`
    * `composer dump-autoload` (needed for regenerating autoload file)
  * Use your own 'API credentials' where possible. (set them in your private .env file). See .env.example for all environment variables.
  * Use `ngrok` (see http://www.ngrok.com) or localtunnel (see http://localtunnel.me) for developing and testing locally.
* Mailtrap
  * Use `mailtrap.io` for testing emails. Signup for mailtrap (https://mailtrap.io) and update SMTP details in your .env file.
  * And then use mailtrap to test / see / track all emails which are sent through the app.
* Localtunnel
  * The basic script to get a hostname from localtunnel.me is also included.
  * `npm start tunnel` (will assign a random subdomain and forward to local port 8000)
  * `npm start tunnel yourcustomsubdomain123 9000` (will give you yourcustomsubdomain123.localtunnel.me and forward to local port 9000)
  * ... or use local tunnel directly (https://github.com/localtunnel/localtunnel)
* Storage folder public access
  * Link storage folder to public folder using `php artisan storage:link`

### To start laravel queue worker

```
php artisan queue:work redis --queue=posts,automations,default --sleep=3 --tries=3 --timeout=180
```

#### To restart all workers

```
php artisan queue:restart
```

### Laravel scheduler cron

```
* * * * * php /path/to/artisan schedule:run >> /dev/null 2>&1
```

### Static Code Analysis
```
composer run analyse
```

## Deployment
First, we need to build the assets for production. Run the following command:
```
vendor/bin/dep deploy:build_assets
```
This will build assets for production (and also uses production .env file).


Then, deploy the code to the server (you need a linux or linux compatible terminal):
```
vendor/bin/dep deploy
```

### Tests
```
composer test
```

## Infrastructure
- DigitalOcean Droplet
- DigitalOcean Spaces
- Mailgun
- ...
