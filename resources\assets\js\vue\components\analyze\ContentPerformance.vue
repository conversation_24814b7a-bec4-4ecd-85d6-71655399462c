<template>
    <div class="row">
        <div class="col-12 mb-8">
            <h5 class="mb-4">Posts vs Engagements</h5>
            <div class="card border" v-if="isLoading('metrics_all') || isLoading('counts_all')" v-html="spinnerHtml"></div>
            <div class="row" v-else>
                <div class="col-12 d-flex overflow-auto hide-scrollbar">
                    <div class="analyze-card card border mr-4">
                        <div class="card-body p-20">
                            <div>
                                <h6 class="font-weight-500 mb-6">Posts Published</h6>
                                <h3 class="mb-0">{{ totalPostsPublished }}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="analyze-card card border">
                        <div class="card-body p-20">
                            <h6 class="font-weight-500 mb-6">Engagements Received</h6>
                            <h3 class="mb-0">{{ totalEngagements }}</h3>
    
                        </div>
                    </div>
                </div>
                <div class="col-12 mt-4">
                    <div class="card border pt-4 pl-2 pb-4 d-flex justify-content-center">
                        <Chart
                            ref="chart_posts_vs_engagements"
                            :options="allPostsAndEngagementsChart" />
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 mb-8">
            <h5 class="mb-4">Publishing Behavior</h5>
            <div class="row">
                <div class="col-md-12">
                    <div class="card border">
                        <div class="card-body card-body p-20">
                            <ul class="d-flex justify-content-md-center justify-content-start flex-nowrap nav border-md-bottom-0 border-bottom-1 navbar-reports overflow-auto hide-scrollbar" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link cursor-pointer"
                                       @click="publishingBehaviorTab = 'overview'"
                                       :class="{'active': publishingBehaviorTab === 'overview'}">Overview</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link cursor-pointer"
                                       @click="publishingBehaviorTab = 'text_posts'"
                                       :class="{'active': publishingBehaviorTab === 'text_posts'}">Text Posts</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link cursor-pointer"
                                       @click="publishingBehaviorTab = 'image_posts'"
                                       :class="{'active': publishingBehaviorTab === 'image_posts'}">Image Posts</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link cursor-pointer"
                                       @click="publishingBehaviorTab = 'video_posts'"
                                       :class="{'active': publishingBehaviorTab === 'video_posts'}">Video Posts</a>
                                </li>
                            </ul>
                            <div class="tab-content pt-5 px-4 pb-4">
                                <div class="tab-pane active show">
                                    <template v-if="publishingBehaviorTab === 'overview'">
                                        <div v-if="isLoading('post_types_chart')" v-html="spinnerHtml"></div>
                                        <Chart
                                            key="pie_chart1"
                                            ref="chart_post_types"
                                            :options="postTypesChart"
                                            v-else
                                        />
                                    </template>
                                    <template v-else-if="publishingBehaviorTab === 'text_posts'">
                                        <div
                                            v-if="isLoading('metrics_text') || isLoading('counts_text')"
                                            v-html="spinnerHtml"></div>
                                        <Chart
                                            ref="chart_text_posts"
                                            :options="textPostsAndEngagementsChart"
                                            v-else
                                        />
                                    </template>
                                    <template v-else-if="publishingBehaviorTab === 'image_posts'">
                                        <div
                                            v-if="isLoading('metrics_image') || isLoading('counts_image')"
                                            v-html="spinnerHtml"></div>
                                        <Chart
                                            ref="chart_image_posts"
                                            :options="imagePostsAndEngagementsChart"
                                            v-else
                                        />
                                    </template>
                                    <template v-else-if="publishingBehaviorTab === 'video_posts'">
                                        <div
                                            v-if="isLoading('metrics_video') || isLoading('counts_video')"
                                            v-html="spinnerHtml"></div>
                                        <Chart
                                            ref="chart_video_posts"
                                            :options="videoPostsAndEngagementsChart"
                                            v-else
                                        />
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 mb-8">
            <h5 class="mb-4">Hashtag Analysis</h5>
            <div class="col-12 card border d-flex justify-content-center">
                <div v-if="isLoading('hashtags')"
                     v-html="spinnerHtml"></div>
                <Chart
                    ref="chart_hashtags"
                    :options="hashtagsByWeightChart" v-else/>
            </div>
        </div>
        <div class="col-12">
            <h5 class="mb-4">
                Top Posts
                <span v-if="topPosts.length">
                    (10)
                </span>
            </h5>
            <div class="card border" v-if="isLoading('top_posts')">
                <div class="card-body d-flex justify-content-center">
                    <div v-html="spinnerHtml"></div>
                </div>
            </div>
            <div v-else>
                <div v-if="!topPosts.length">
                    <div class="card border">
                        <div class="card-body d-flex justify-content-center">
                            <span>No data to show</span>
                        </div>
                    </div>
                </div>
                <div
                    v-for="(data, index) in topPosts" :key="index + 'post-container'" v-else>
                    <Post :post="data"/>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { axios, axiosErrorHandler, spinnerHtml } from '../../../components'
import { Chart } from "highcharts-vue";
import Post from '../Post.vue';

export default {
    name: 'ContentPerformance',
    props: ['filter', 'accounts'],
    components: {
        Post,
        Chart
    },
    data() {
        return {
            loadingFlags: [],

            postMetrics: {
                all: [],
                image: [],
                video: [],
                text: []
            },
            postCounts: {
                all: [],
                image: [],
                video: [],
                text: []
            },

            hashtagsByScore: [],
            topPosts: [],

            publishingBehaviorTab: 'overview',
        }
    },
    computed: {
        spinnerHtml: () => spinnerHtml,
        totalEngagements(){
            let total = 0;
            Object.values(this.postMetrics.all).map(d => {
                d.forEach(e => {
                    total += e.value;
                });
            });
            return total;
        },
        totalPostsPublished(){
            return this.postCounts.all.reduce((a, b) => {
                return a + b.count;
            }, 0);
        },

        // chart configs
        allPostsAndEngagementsChart(){
            const newDataByDate = {};
            Object.values(this.postMetrics.all).forEach((metricData) => {
                metricData.forEach(item =>{
                    if (newDataByDate[item.date]) {
                        newDataByDate[item.date] += item.value;
                    } else {
                        newDataByDate[item.date] = item.value;
                    }
                })
            });
            const engagementsData = Object.keys(newDataByDate).map((date) => {
                return [
                    this.timestampForXAxis(date),
                    newDataByDate[date]
                ];
            });
            const postsPublished = this.postCounts.all.map(itm => {
                return [this.timestampForXAxis(itm.date), itm.count];
            });

            return {
                chart: {
                    type: 'areaspline', // Changed to area chart type
                    zoomType: 'x'
                },
                colors:[ "#FDC512", "#E8271A"],

                accessibility: {
                    description: 'This area chart compares engagement levels and posts published over time.'
                },
                title: {
                    text: ''
                },
                subtitle: {
                    text: ''
                },
                xAxis: {
                    type: 'datetime', // Ensure proper time-based x-axis
                    
                },
                yAxis: [
                    {
                        title: {
                            text: 'Engagements',
                            margin: 16
                        },
                        labels: {
                            format: '{value}',
                            
                        }
                    },
                    {//secondary yAxis
                        title: {
                            text: 'Posts Published',
                            
                        },
                        labels: {
                            format: '{value}',
                            
                        },
                        opposite: true
                    }
                ],
                plotOptions: {
                    series: {
                        fillOpacity: 0.7
                    },
                    area: {
                        marker: {
                            enabled: false, // Simplified markers for area charts
                            symbol: 'circle',
                            radius: 2,
                            states: {
                                hover: {
                                    enabled: true
                                }
                            }
                        }
                    }
                },
                series: [
                    {
                        name: 'Engagements',
                        marker: {
                            enabled: false
                        },
                        data: engagementsData
                    },
                    {
                        name: 'Posts Published',
                        yAxis: 1,
                        marker: {
                            enabled: false
                        },
                        data: postsPublished
                    }
                ],
                
            };
        },
        textPostsAndEngagementsChart(){
            const newDataByDate = {};
            Object.values(this.postMetrics.text).forEach((metricData) => {
                metricData.forEach(item =>{
                    if (newDataByDate[item.date]) {
                        newDataByDate[item.date] += item.value;
                    } else {
                        newDataByDate[item.date] = item.value;
                    }
                })
            });
            const engagementsData = Object.keys(newDataByDate).map((date) => {
                return [
                    this.timestampForXAxis(date),
                    newDataByDate[date]
                ];
            });
            const postsPublished = this.postCounts.text.map(itm => {
                return [this.timestampForXAxis(itm.date), itm.count];
            });

            return {
                chart: {
                    type: "areaspline",
                    zoomType: "x"
                },
                colors:[ "#FDC512", "#E8271A"],
                title: {
                    text: "",
                },
                xAxis: {
                    type: "datetime",
                },
                yAxis: [
                    { // Primary yAxis
                        title: {
                            text: "Engagements",
                        },
                        labels: {
                            format: '{value}',
                        }
                    },
                    { //secondary yAxis
                        title: {
                            text: "Posts Published",
                        },
                        labels: {
                            format: '{value}',
                        },
                        opposite: true
                    }
                ],
                plotOptions: {
                    series: {
                        fillOpacity: 0.7
                    },
                    area: {
                        marker: {
                            enabled: false, // Simplified markers for area charts
                            symbol: 'circle',
                            radius: 2,
                            states: {
                                hover: {
                                    enabled: true
                                }
                            }
                        }
                    }
                },
                series: [
                    {
                        name: 'Engagements',
                        marker: {
                            enabled: false
                        },
                        data: engagementsData,
                    },
                    {
                        name: 'Posts Published',
                        yAxis: 1,
                        marker: {
                            enabled: false
                        },
                        data: postsPublished,
                    }
                ],
            };
        },
        imagePostsAndEngagementsChart(){
            const newDataByDate = {};
            Object.values(this.postMetrics.image).forEach((metricData) => {
                metricData.forEach(item =>{
                    if (newDataByDate[item.date]) {
                        newDataByDate[item.date] += item.value;
                    } else {
                        newDataByDate[item.date] = item.value;
                    }
                })
            });
            const engagementsData = Object.keys(newDataByDate).map((date) => {
                return [
                    this.timestampForXAxis(date),
                    newDataByDate[date]
                ];
            });
            const postsPublished = this.postCounts.image.map(itm => {
                return [this.timestampForXAxis(itm.date), itm.count];
            });

            return {
                chart: {
                    type: "areaspline",
                    zoomType: "x"
                },
                colors:[ "#FDC512", "#E8271A"],
                title: {
                    text: "",
                },
                xAxis: {
                    type: "datetime",
                },
                yAxis: [
                    { // Primary yAxis
                        title: {
                            text: "Engagements",
                        },
                        labels: {
                            format: '{value}',
                        }
                    },
                    { //secondary yAxis
                        title: {
                            text: "Posts Published",
                        },
                        labels: {
                            format: '{value}',
                        },
                        opposite: true
                    }
                ],
                plotOptions: {
                    series: {
                        fillOpacity: 0.7
                    },
                    area: {
                        marker: {
                            enabled: false, // Simplified markers for area charts
                            symbol: 'circle',
                            radius: 2,
                            states: {
                                hover: {
                                    enabled: true
                                }
                            }
                        }
                    }
                },
                series: [
                    {
                        name: 'Engagements',
                        marker: {
                            enabled: false
                        },
                        data: engagementsData,
                    },
                    {
                        name: 'Posts Published',
                        yAxis: 1,
                        marker: {
                            enabled: false
                        },
                        data: postsPublished,
                    }
                ],
            };
        },
        videoPostsAndEngagementsChart(){
            const newDataByDate = {};
            Object.values(this.postMetrics.video).forEach((metricData) => {
                metricData.forEach(item =>{
                    if (newDataByDate[item.date]) {
                        newDataByDate[item.date] += item.value;
                    } else {
                        newDataByDate[item.date] = item.value;
                    }
                })
            });
            const engagementsData = Object.keys(newDataByDate).map((date) => {
                return [
                    this.timestampForXAxis(date),
                    newDataByDate[date]
                ];
            });
            const postsPublished = this.postCounts.video.map(itm => {
                return [this.timestampForXAxis(itm.date), itm.count];
            });

            return {
                chart: {
                    type: "areaspline",
                    zoomType: "x"
                },
                colors:[ "#FDC512", "#E8271A"],
                title: {
                    text: "",
                },
                xAxis: {
                    type: "datetime",
                },
                yAxis: [
                    { // Primary yAxis
                        title: {
                            text: "Engagements",
                        },
                        labels: {
                            format: '{value}',
                        }
                    },
                    { //secondary yAxis
                        title: {
                            text: "Posts Published",
                        },
                        labels: {
                            format: '{value}',
                        },
                        opposite: true
                    }
                ],
                plotOptions: {
                    series: {
                        fillOpacity: 0.7
                    },
                    area: {
                        marker: {
                            enabled: false, // Simplified markers for area charts
                            symbol: 'circle',
                            radius: 2,
                            states: {
                                hover: {
                                    enabled: true
                                }
                            }
                        }
                    }
                },
                series: [
                    {
                        name: 'Engagements',
                        marker: {
                            enabled: false
                        },
                        data: engagementsData,
                    },
                    {
                        name: 'Posts Published',
                        yAxis: 1,
                        marker: {
                            enabled: false
                        },
                        data: postsPublished,
                    }
                ],
            };
        },
        postTypesChart(){
            const data = [
                {
                    name: "Text Posts",
                    y: (this.postCounts.text.reduce((a, b) => {
                        return a + b.count;
                    }, 0) / this.totalPostsPublished) * 100,
                },
                {
                    name: "Image Posts",
                    y: (this.postCounts.image.reduce((a, b) => {
                        return a + b.count;
                    }, 0) / this.totalPostsPublished) * 100,
                },
                {
                    name: "Video Posts",
                    y: (this.postCounts.video.reduce((a, b) => {
                        return a + b.count;
                    }, 0) / this.totalPostsPublished) * 100,
                }
            ];
            return {
                chart: {
                    type: "pie",
                    plotBackgroundColor: null,
                    plotBorderWidth: null,
                    plotShadow: false,
                },
                colors:['#FA4B3F','#05D3F0','#FFDC4D'],
                title: {
                    text: '',
                },
                tooltip: {
                    pointFormat: "{series.name}: <b>{point.percentage:.1f}%</b>",
                },
                accessibility: {
                    point: {
                        valueSuffix: "%",
                    },
                },
                series: [{
                    name: '',
                    colorByPoint: true,
                    data
                }]
            }
        },
        hashtagsByWeightChart(){
            const data = this.hashtagsByScore.map((item) => {
                return {
                    name: item.hashtag,
                    weight: item.score,
                }
            });
            return {
                title: {
                    text: '',
                },
                colors:['#FDC512','#05D3F0','#FA3F6C','#0C6FFF','#29CC7A'],
                accessibility: {
                    screenReaderSection: {
                        beforeChartFormat:
                            '<h5>{chartTitle}</h5>' +
                            '<div>{chartSubtitle}</div>' +
                            '<div>{chartLongdesc}</div>' +
                            '<div>{viewTableButton}</div>',
                    },
                },
                chart: {
                    plotBackgroundColor: null,
                    plotBorderWidth: null,
                    plotShadow: false,
                },
                series: [
                    {
                        type: 'wordcloud',
                        data,
                        name: 'Engagements',
                    },
                ],
            };
        }
    },
    methods: {
        isLoading(flag){
            return this.loadingFlags.includes(flag);
        },
        showLoader(flag) {
            if(this.isLoading(flag)){
                return;
            }
            this.loadingFlags.push(flag);
        },
        hideLoader(flag) {
            this.loadingFlags = this.loadingFlags.filter(itm => itm !== flag);
        },
        fetchData() {
            this.fetchMetrics();
            this.fetchCounts();
            this.fetchMetrics('text');
            this.fetchCounts('text');
            this.fetchMetrics('image')
            this.fetchCounts('image');
            this.fetchMetrics('video')
            this.fetchCounts('video');
            this.fetchHashtagsAndScore();
            this.fetchTopPosts();
        },
        async fetchMetrics(type = null) {
            this.showLoader('metrics_' + (type ? type : ''));
            try {
                const { data } = await axios.get(
                    '/api/v1/insights/posts/metrics',
                    {
                        params: {
                            post_type: type,
                            metrics: 'comments,likes,shares,pin_clicks,reactions,saved,video_views,plays,retweets,post_views,post_cta_clicks,views_search',
                            start: this.filter.start,
                            end: this.filter.end,
                            team: this.filter.team,
                            accounts: this.filter.accounts,
                        },
                    },
                );
                this.postMetrics[type ? type : "all"] = data.data;
            } catch (e) {
                axiosErrorHandler(e)
            }
            this.hideLoader('metrics_' + (type ? type : ''));
        },
        async fetchCounts(type = null){
            this.showLoader('counts_' + (type ? type : ''));
            try {
                const { data } = await axios.get('/api/v1/insights/posts/counts', {
                    params: {
                        post_type: type,
                        start: this.filter.start,
                        end: this.filter.end,
                        team: this.filter.team,
                        accounts: this.filter.accounts,
                    },
                });
                this.postCounts[type ? type : "all"] = data.data;
            } catch (e) {
                axiosErrorHandler(e)
            }
            this.hideLoader('counts_' + (type ? type : ''));
        },
        async fetchHashtagsAndScore() {
            this.showLoader('hashtags');
            try {
                const { data } = await axios.get(
                    '/api/v1/insights/posts/performance_by_hashtags',
                    {
                        params: {
                            start: this.filter.start,
                            end: this.filter.end,
                            team: this.filter.team,
                            accounts: this.filter.accounts,
                        },
                    },
                );
                this.hashtagsByScore = data.data;
            } catch (e) {
                axiosErrorHandler(e)
            }
            this.hideLoader('hashtags');
        },
        async fetchTopPosts() {
            this.showLoader('top_posts');
            try {
                const { data } = await axios.get('/api/v1/insights/posts/top_posts', {
                    params: {
                        start: this.filter.start,
                        end: this.filter.end,
                        team: this.filter.team,
                        accounts: this.filter.accounts,
                        metrics: 'comments,likes,shares,reactions,saved,video_views,plays,retweets,post_views,post_cta_clicks,views_search', // metrics to use for scoring posts
                    }
                });
                const posts = data.data;
                if (posts && posts.length) {
                    posts.forEach((item) => {
                        item.accounts = [this.getAccount(item.account_id)];
                    });
                }
                this.topPosts = posts;
            } catch (e) {
                axiosErrorHandler(e);
            }
            this.hideLoader('top_posts');
        },
        getAccount(id) {
            const all = this.accounts;
            let account = {};
            all.forEach((acc) => {
                if (acc.id === id) account = acc;
            })
            return account;
        },
        timestampForXAxis(timestamp) {
            return this.$momentUTC(timestamp, "YYYY-MM-DD").valueOf();
        },
    },
    async mounted() {
        await this.fetchData();
    }
}
</script>
