<template>
    <div class="post post-padding">
        <div class="d-flex align-items-start mt-3"
             v-for="(tweet, i) in tweets" :key="i + '-' + tweet.media.length + 'tw_preview'">
            <img :src="account.image" class="avatar rounded-circle" alt="avatar" />
            <div class="ml-3" style="min-width: 80%;">
                <div class="d-flex">
                    <div class="fullname font-weight-700">{{ account.name }}</div>
                    <span class="username ml-1">@username</span>
                    <span class="ml-1 timestamp">· now</span>
                </div>
                <RichText :value="tweet.content" :readonly="true" class="text" style="min-height: 12px;"/>
                
                <div class="attachment-container overflow-hidden" :style="text ? 'margin-top:6px': 'margin-top:10px'" v-if="tweet.media && tweet.media.length">
                    <div class="gallery gallery-4-type-2" v-if="tweet.media.length > 1">
                        <div v-for="(attachment, index) in tweet.media" class="gallery-item p-0" :key="index + attachment.url" 
                            :class="{
                                'pr-1': index % 2 === 0, // Apply margin-right if it's an even index
                                'mb-1': index < 2
                                
                            }"
                        >

                            <video class="rounded object-fit-cover border border-light" controls :title="attachment.name" v-if="attachment && (attachment.type.includes('video'))">
                                <source :src="attachment.url" />
                            </video>
                            <img  
                                class="object-fit-cover w-100" 
                                :src="attachment.url" 
                                :alt="attachment.name" 
                                :title="attachment.name"
                                :style="{
                                    borderTopLeftRadius: index === 0 ? '14px' : '',
                                    borderTopRightRadius: index === 1 ? '14px' : '',
                                    borderBottomLeftRadius: index === 2 ? '14px' : '',
                                    borderBottomRightRadius: index === 3 ? '14px' : ''
                                }"
                            v-else/>
                        </div>
                    </div>
                    <div class="w-100 text-center" v-else-if="tweet.media.length === 1">
                        <video controls :title="tweet.media[0].name"  v-if="tweet.media[0].type.includes('video')">
                            <source :src="tweet.media[0].url" />
                        </video>
                        <img class="rounded w-100" :src="tweet.media[0].url" :alt="tweet.media[0].name" :title="tweet.media[0].name"
                            v-else/>
                    </div>
                </div>

                <LinkPreview
                    v-if="link && attachments.length === 0"
                    :url="link"
                    type="twitter" />

                <div class="post-footer my-3">
                    <div class="post-actions">
                        <div class="pb-2 d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <i class="ph ph-chat-circle ph-md mr-1"></i><span class="small-2">36</span>
                            </div>
                            <div class="d-flex align-items-center">
                                <i class="ph ph-repeat ph-md mr-1"></i><span class="small-2">40</span> 
                            </div>
                            <div class="d-flex align-items-center">
                                <i class="ph ph-heart ph-md mr-1"></i><span class="small-2">2.8k</span>
                            </div>
                            <div class="d-flex align-items-center">
                                <i class="ph ph-chart-bar ph-md mr-1"></i><span class="small-2">24.6K</span> 
                            </div>
                            <i class="ph ph-bookmark-simple ph-md"></i>
                            <i class="ph ph-share-network ph-md"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import LinkPreview from "../LinkPreview.vue";
import RichText from "../../common/RichText.vue";
export default {
    name: "TwitterTweet",
    props: ["account", "text", "attachments", "link", "options"],
    components: {
        LinkPreview,
        RichText
    },
    computed: {
        content() {
            // normalize linebreaks
            return (this.text || "").replace(/(\r\n|\r|\n){2,}/g, "$1\n");
        },
        tweets() {
            const tweets = [];
            tweets.push({ content: this.content, media: this.attachments });
            if (this.options.threaded_replies) {
                this.options.threaded_replies.forEach(reply => {
                    let tweetText = "";
                    if (typeof reply === "object") {
                        tweetText = reply.tweet;
                    } else {
                        tweetText = reply;
                    }
                    let normalizedText = (tweetText || "").replace(/(\r\n|\r|\n){2,}/g, "$1\n"); // normalize linebreaks
                    let attachments = [];
                    if (typeof reply === "object" && reply.media) {
                        let replyMedia = [];
                        if (typeof reply.media === "string") {
                            replyMedia = JSON.parse(reply.media);
                        } else {
                            replyMedia = reply.media;
                        }
                        attachments = replyMedia.map(a => {
                            let attachment = {
                                _metaData: a._metaData,
                                type: a.extension || a.mimeType,
                                mime: a.mimeType,
                                name: a.name,
                                url: a.url ? a.url : ""
                            };
                            // if `type` is not mime type already, set `type` from `mime`
                            // this is also for existing uploads
                            if (attachment.mime) {
                                attachment.type = attachment.mime;
                            }
                            return attachment;
                        });
                    }
                    tweets.push({ content: normalizedText, media: attachments });
                });
            }
            return tweets;
        }
    },
};
</script>

<style lang="scss" scoped>
.fullname,
.username,
.timestamp,
.post {
    font-family: Roboto, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Ubuntu, "Helvetica Neue", sans-serif;
}
.post-padding{
    padding: 0px 14px;
}
.text {
    overflow-wrap: break-word;
    word-break: break-word;
    color:#101318 !important;
    line-height: 23.2px;

    a {
        color: #1DA1F2 !important;
    }
}
.fullname {
    color: #101318 !important;
    font-size: 18px !important;
    line-height: 18px;
}
.username,
.timestamp {
    color: #53646E;
    font-size: 18px !important;
    line-height: 18px;
}
.attachment-container {
   border-radius: 4px;
}
.gallery-item img{
   border-radius: 4px;
}
.post-footer{
    color: #526470 !important;
}

.rounded{
    border-radius: 14px !important;
}
</style>