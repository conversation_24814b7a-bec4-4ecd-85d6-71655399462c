import { axios } from "./components";
import { merge } from "lodash";
let api = null;

/**
 *
 * @return {Promise<Object>} Canva API instance
 */
export function getApi() {
    return new Promise((resolve, reject) => {
        if (api) {
            resolve(api);
        } else {
            ((document, url) => {
                const script = document.createElement("script");
                script.src = url;
                script.onload = async () => {
                    api = await window.Canva.DesignButton.initialize({
                        apiKey: process.env.MIX_APP_ENV === "production" ? "Xk55N8RKbyUf-m6E4XpduO37" : "WEkpS1y4Ttd4oUfCzM_W8TEu"
                    });
                    resolve(api);
                };
                document.body.appendChild(script);
            })(document, "https://sdk.canva.com/designbutton/v2/api.js");
        }
    });
}

/**
 *
 * @param opts
 * @return {Promise<string>} URL to the design
 */
export function createDesign(opts) {
    const defaultOpts = {
        design: {
            type: "SocialMedia" // Poster
        },
        editor: {
            fileType: "jpg",
            publishLabel: "Use this design",
        }
    };
    opts = merge(defaultOpts, opts);
    return new Promise((resolve, reject) => {
        getApi().then(api => {
            opts.onDesignPublish = opts => {
                resolve(opts.exportUrl);
            };
            opts.onDesignClose = opts => {
                resolve(null); // user canceled
            };
            api.createDesign(opts);
        });
    });
}

/**
 *
 * @param url
 * @return {Promise<File>}
 */
export function getFileFromUrl(url) {
    return new Promise(async (resolve, reject) => {
        try {
            const { data, headers } = await axios.get(
                "/app/publish/extension_editor/get_media?media=" + encodeURIComponent(url),
                {
                    responseType: "blob",
                    timeout: 60000
                }
            );

            let headerLine = headers["content-disposition"];
            let filename = headerLine.split("filename=")[1];

            const file = new File([data], filename, {
                type: headers["content-type"]
            });

            resolve(file);
        } catch (e) {
            reject(e);
        }
    });
}

export default {
    getApi,
    createDesign,
    getFileFromUrl
};
