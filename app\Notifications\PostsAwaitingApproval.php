<?php

namespace App\Notifications;

use App\Team;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class PostsAwaitingApproval extends Notification implements ShouldQueue
{
    use Queueable;

    /** @var Team|null $team */
    private $team = null;
    private $count = 0;

    /**
     * Create a new notification instance.
     *
     * @param Team $team
     * @param int $count
     */
    public function __construct(Team $team, int $count)
    {
        $this->team = $team;
        $this->count = $count;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->greeting('Hey ' . explode(' ', ($notifiable->name . ''))[0])
            ->subject($this->count . ' post' . ($this->count > 1 ? 's': '') . ' for ' . $this->team->name . ' need' . ($this->count > 1 ? '': 's') . ' approval')
            ->line('There ' . ($this->count > 1 ? 'are': 'is') . ' ' . $this->count . ' post' . ($this->count > 1 ? 's': '') . ' awaiting approval.')
            ->line('Please approve the posts before their scheduled time so they can be published.')
            ->action('Review Posts', url('app/publish/awaiting_approval'))
            ->line('Posts which are not approved before their scheduled time will be published once they are approved.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'count' => $this->count,
        ];
    }
}
