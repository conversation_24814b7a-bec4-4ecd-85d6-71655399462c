<?php

namespace App\Console;

use App\Console\Commands\BuildSitemap;
use App\Console\Commands\CurationDeleteItems;
use App\Console\Commands\CurationFetchFeeds;
use App\Console\Commands\CurationScoreItems;
use App\Console\Commands\DatabaseCleanup;
use App\Console\Commands\DeleteFeedPermanently;
use App\Console\Commands\DeleteFeedsThatShouldBeDeleted;
use App\Console\Commands\DeleteNotifications;
use App\Console\Commands\DeleteOldPosts;
use App\Console\Commands\DeleteOldUnreadFeedPosts;
use App\Console\Commands\DeletePublishQueuePermanently;
use App\Console\Commands\DeleteTemporaryFiles;
use App\Console\Commands\DeleteUserPermanently;
use App\Console\Commands\FeedPostsNotify;
use App\Console\Commands\FetchInboxes;
use App\Console\Commands\GetPostMetrics;
use App\Console\Commands\GetAccountMetrics;
use App\Console\Commands\MonitorOurLiveChat;
use App\Console\Commands\NotifyPostsAwaitingApproval;
use App\Console\Commands\NotifyUsForStats;
use App\Console\Commands\PollRSS;
use App\Console\Commands\PublishFromQueues;
use App\Console\Commands\PublishScheduledPosts;
use App\Console\Commands\UserEmailVerifyReminder;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        PollRSS::class,
        BuildSitemap::class,
        DeleteFeedPermanently::class,
        DeletePublishQueuePermanently::class,
        DeleteFeedsThatShouldBeDeleted::class,
        DeleteUserPermanently::class,
        PublishFromQueues::class,
        DeleteOldUnreadFeedPosts::class,
        NotifyPostsAwaitingApproval::class,
        NotifyUsForStats::class,
        UserEmailVerifyReminder::class,
        PublishScheduledPosts::class,
        FeedPostsNotify::class,
        DatabaseCleanup::class,
        MonitorOurLiveChat::class,
        DeleteNotifications::class,
        GetPostMetrics::class,
        GetAccountMetrics::class,
        CurationFetchFeeds::class,
        CurationScoreItems::class,
        CurationDeleteItems::class,
        DeleteTemporaryFiles::class,
        DeleteOldPosts::class,
        FetchInboxes::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        /**
         * Important: If you use withoutOverlapping(), remember that if for some reason (fatal error, crash, etc.), laravel fails to remove the lock file/key, the command will never run again until it expires or is manually cleaned up
         */

        // clean up
        $schedule->command('activitylog:clean')
            ->daily()
            ->runInBackground()
            ->sentryMonitor(null, 10);

        $schedule->command(DatabaseCleanup::class)
            ->daily()
            ->name('database_cleanup')
            ->withoutOverlapping()
            ->onOneServer()
            ->runInBackground()
            ->sentryMonitor(null, 10);

        $schedule->command(DeleteOldPosts::class)
            ->everyThirtyMinutes()
            ->name("del_old_posts")
            ->withoutOverlapping()
            ->onOneServer()
            ->runInBackground()
            ->sentryMonitor();

        $schedule->command(DeleteOldUnreadFeedPosts::class)
            ->cron('0 */3 * * *') // every 3 hrs
            ->name("del_old_unread_feedposts")
            ->withoutOverlapping()
            ->onOneServer()
            ->runInBackground();

        $schedule->command(DeleteFeedPermanently::class)
            ->everyThirtyMinutes()
            ->name("permanently_delete_feeds")
            ->withoutOverlapping()
            ->onOneServer()
            ->runInBackground();

        $schedule->command(DeleteFeedsThatShouldBeDeleted::class)
            ->twiceDaily()
            ->name("delete_unwanted_feeds")
            ->withoutOverlapping()
            ->onOneServer()
            ->runInBackground();

        $schedule->command(DeletePublishQueuePermanently::class)
            ->twiceDaily()
            ->name("permanently_delete_publish_queues")
            ->withoutOverlapping()
            ->onOneServer()
            ->runInBackground()
            ->sentryMonitor();

        $schedule->command(DeleteUserPermanently::class)
            ->daily()
            ->name("permanently_delete_users")
            ->withoutOverlapping()
            ->onOneServer()
            ->runInBackground()
            ->sentryMonitor();

        $schedule->command(DeleteNotifications::class)
            ->daily()
            ->name("cleanup_read_notifications")
            ->withoutOverlapping()
            ->onOneServer()
            ->runInBackground()
            ->sentryMonitor();

        // generate sitemap
        $schedule->command('sitemap:generate')
            ->daily()
            ->runInBackground();


        // user email verify reminder
        $schedule->command(UserEmailVerifyReminder::class)
            ->daily()
            ->name("user_email_verify_reminder")
            ->withoutOverlapping()
            ->onOneServer()
            ->runInBackground()
            ->sentryMonitor();

        // publish scheduled posts
        $schedule->command(PublishScheduledPosts::class)
            ->everyMinute()
            ->name("publish_pending_posts")
            ->withoutOverlapping(5)
            ->onOneServer()
            ->runInBackground()
            ->sentryMonitor(null, 2);

        $schedule->command(PublishFromQueues::class)
            ->everyMinute()
            ->name("publish_from_queues")
            ->withoutOverlapping(5)
            ->onOneServer()
            ->runInBackground()
            ->sentryMonitor(null, 3);

        $schedule->command(FetchInboxes::class)
            ->everyMinute()
            ->name("fetch_inboxes")
            ->withoutOverlapping(5)
            ->onOneServer()
            ->runInBackground()
            ->sentryMonitor(null, 3);

        // generate email to the users who should be notified
        $schedule->command(FeedPostsNotify::class)
            ->everyFiveMinutes()
            ->name("new_feedposts_notifications")
            ->withoutOverlapping()
            ->onOneServer()
            ->runInBackground();

        $schedule->command(NotifyPostsAwaitingApproval::class)
            ->everyMinute()
            ->name("notify_for_posts_awaiting_approval")
            ->withoutOverlapping(5)
            ->onOneServer()
            ->runInBackground()
            ->sentryMonitor(null, 5);

        $schedule->command(PollRSS::class)
            ->everyFiveMinutes()
            ->name("poll_rss")
            ->withoutOverlapping(25)
            ->onOneServer()
            ->runInBackground()
            ->sentryMonitor(null, 5);

        // an account only updates once every 24 hrs
        $schedule->command(GetAccountMetrics::class)
            ->everyThirtyMinutes()
            ->name("insights_followers")
            ->withoutOverlapping(40)
            ->onOneServer()
            ->runInBackground()
            ->sentryMonitor(null, 20);

        $schedule->command(GetPostMetrics::class)
            ->everyFiveMinutes()
            ->name("insights_posts")
            ->withoutOverlapping(10)
            ->onOneServer()
            ->runInBackground()
            ->sentryMonitor(null, 5);

        $schedule->command(CurationFetchFeeds::class)
            ->everyThirtyMinutes()
            ->name("curation_fetch_feeds")
            ->withoutOverlapping()
            ->onOneServer()
            ->runInBackground()
            ->sentryMonitor();

        $schedule->command(CurationScoreItems::class)
            ->everyMinute()
            ->name("curation_score_items")
            ->withoutOverlapping(25)
            ->onOneServer()
            ->runInBackground()
            ->sentryMonitor(null, 15);

        $schedule->command(CurationDeleteItems::class)
            ->daily()
            ->name("curation_delete_items")
            ->withoutOverlapping()
            ->onOneServer()
            ->runInBackground()
            ->sentryMonitor(null, 10);

        $schedule->command(NotifyUsForStats::class)
            ->daily()
            ->name("notify_us_for_stats")
            ->withoutOverlapping()
            ->onOneServer()
            ->runInBackground();

        $schedule->command(MonitorOurLiveChat::class)
            ->everyFiveMinutes()
            ->name('notify_us_for_live_chat')
            ->withoutOverlapping()
            ->onOneServer()
            ->runInBackground();

        $schedule->command(DeleteTemporaryFiles::class)
            ->daily()
            ->name('delete_temporary_files')
            ->withoutOverlapping()
            ->onOneServer()
            ->runInBackground()
            ->sentryMonitor(null, 10);

        // Horizon snapshot
        $schedule->command('horizon:snapshot')->everyFiveMinutes();

        // auto retry failed jobs
        $schedule->command('queue:retry all')
            ->daily()
            ->runInBackground();
    }

    /**
     * Register the Closure based commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        require base_path('routes/console.php');
    }
}
