<?php

namespace Tests\Unit;

use App\Account;
use App\Http\Controllers\Json\JsonCollectionController;
use App\Team;
use App\User;
use Illuminate\Http\Request;
use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;

class TeamTest extends TestCase
{
    use RefreshDatabase;
    /**
     * Test adding member to team
     */
    public function testMembersAdd()
    {

        $team = $this->createTeam();

        $this->assertEquals(1, $team->members()->count()); // including creator

        $this->assertEquals(0, $team->pendingMembers()->count());
        $this->assertEquals(0, $team->membersWithoutAdmin()->count());

        $member = factory(User::class)->create();
        // invite a member
        $team->updateMembers([
            [
                'id' => $member->id,
                'permissions' => [
                    'posts.create',
                ],
            ]
        ]);

        $this->assertEquals(1, $team->pendingMembers()->count());

        $this->actingAs($member);
        $team->approveInvite();

        $this->assertEquals(1, $team->membersWithoutAdmin()->count());
        $this->assertEquals(2, $team->members()->count());

        // remove old member, and invite new one
        $member2 = factory(User::class)->create();
        $team->updateMembers([
            [
                'id' => $member2->id,
                'permissions' => [
                    'posts.create',
                ],
            ]
        ]);

        $this->actingAs($member2);
        $team->approveInvite(false); // reject invite acting as the new member

        $this->assertEquals(0, $team->membersWithoutAdmin()->count());

        $this->assertEquals(1, $team->members()->count()); // admin should be alone

    }

    public function testPermissions(){
        $team = $this->createTeam();
        $member = factory(User::class)->create();
        $team->updateMembers([
            [
                'id' => $member->id,
                'permissions' => [
                    'posts.create',
                ]
            ]
        ]);
        $this->assertEquals(1, $team->pendingMembers()->count());
        $this->assertEquals(0, $team->membersWithoutAdmin()->count());
        $this->assertFalse($team->hasPermission($member, 'posts.create'));

        $this->actingAs($member);
        $team->approveInvite(true);

        $this->assertEquals(2, $team->members()->count());

        $this->assertTrue($team->hasPermission($member, 'posts.create'));
        $this->assertFalse($team->hasPermission($member, 'approvals.approve'));

        $team->updateMembers([
            [
                'id' => $member->id,
                'permissions' => [
                    'approvals.approve',
                ]
            ]
        ]);

        $this->assertFalse($team->hasPermission($member, 'posts.create'));
        $this->assertTrue($team->hasPermission($member, 'approvals.approve'));

    }

    /**
     * @throws \Exception
     */
    public function testPostApprovals(){
        $team = $this->createTeam();

        $this->assertFalse($team->requiresContentApproval());
        $this->assertTrue($team->requiresContentApproval(true));
        $this->assertTrue($team->requiresContentApproval());

        $this->assertEquals(1, $team->accounts()->count());

        /** @var Account $account */
        $account = $team->accounts()->first();
        $account->setActive(true);

        $this->assertTrue($account->active);

        /** @var User $member */
        $member = factory(User::class)->create();
        $team->updateMembers([
            [
                'id' => $member->id,
                'permissions' => [
                    'posts.create',
                ]
            ]
        ]);
        $this->actingAs($member);
        $team->approveInvite(true);

        $approver = factory(User::class)->create();
        $team->updateMembers([
            [
                'id' => $member->id,
                'permissions' => [
                    'posts.create',
                ]
            ],
            [
                'id' => $approver->id,
                'permissions' => [
                    'approvals.approve',
                ]
            ],
        ]);
        $this->actingAs($approver);
        $team->approveInvite(true);

        $this->assertFalse($team->hasPermission($approver, 'posts.create'));
        $this->assertTrue($team->hasPermission($approver, 'approvals.approve'));

        $this->assertTrue($team->hasPermission($member, 'posts.create'));
        $this->assertFalse($team->hasPermission($member, 'approvals.approve'));

        $this->actingAs($member);

        $post = $account->publishPost([
            'content' => 'test'
        ], now()->addYears(1));

        $this->assertFalse($post->approved);
        $this->assertFalse($post->canBeApprovedBy($member));
        $this->assertTrue($post->canBeApprovedBy($approver));

        // make sure there are posts pending approval for $member
        $this->assertEquals(
            1,
            count(
                (new JsonCollectionController())
                    ->posts(new Request([
                        'type' => 'awaiting_approval',
                    ]), true)->items()
            )
        );


        $rejectedPost = $account->publishPost([
            'content' => 'test'
        ], now()->addYears(1));

        $this->assertEquals(
            2,
            count(
                (new JsonCollectionController())
                    ->posts(new Request([
                        'type' => 'awaiting_approval',
                    ]), true)->items()
            )
        );

        $this->actingAs($approver);

        $this->assertEquals(
            2,
            count(
                (new JsonCollectionController())
                    ->posts(new Request([
                        'type' => 'awaiting_approval',
                    ]), true)->items()
            )
        );

        $post->approvePost(true);
        $this->assertTrue($post->approved);

        $this->assertFalse($rejectedPost->approved);
        $rejectedPost->approvePost(false, 'test');
        $this->assertTrue($rejectedPost->approved);
        $this->assertTrue($rejectedPost->draft);
        $this->assertIsString($rejectedPost->getOption('reject_reason'));

        $this->actingAs($member);
        $this->assertEquals(
            1,
            count(
                (new JsonCollectionController())
                    ->posts(new Request([
                        'type' => 'draft',
                    ]), true)->items()
            )
        );


        $team->updateMembers([
            [
                'id' => $member->id,
                'permissions' => [
                    'posts.create',
                ]
            ],
            [
                'id' => $approver->id,
                'permissions' => [
                    'approvals.approve',
                    'posts.create',
                ]
            ],
        ]);
        $this->actingAs($approver);

        $approvedPost  = $account->publishPost([
            'content' => 'test'
        ], now()->addYears(1));
        $this->assertTrue($approvedPost->approved);

        $this->assertEquals(2, $team->getApprovers()->count());

        $this->actingAs($team->user);

        // admin always has all permissions
        $this->assertTrue($team->hasPermission($team->user, 'posts.create'));
        $this->assertTrue($team->hasPermission($team->user, 'approvals.approve'));
        $this->assertEquals(count(Team::$allPermissions), count($team->getPermissionsForUser($team->user)));

        $approvedPost  = $account->publishPost([
            'content' => 'test'
        ], now()->addYears(1));
        $this->assertTrue($approvedPost->approved);

        $this->assertEquals(
            3,
            count(
                (new JsonCollectionController())
                    ->posts(new Request([
                        'type' => 'scheduled',
                    ]), true)->items()
            )
        );

        $this->assertEquals(
            3,
            count(
                (new JsonCollectionController())
                    ->posts(new Request([
                        'type' => 'scheduled_or_awaiting_approval',
                    ]), true)->items()
            )
        );

        $this->actingAs($approver);

        $this->assertEquals(
            1,
            count(
                (new JsonCollectionController())
                    ->posts(new Request([
                        'type' => 'scheduled',
                    ]), true)->items()
            )
        );

        $this->assertEquals(
            1,
            count(
                (new JsonCollectionController())
                    ->posts(new Request([
                        'type' => 'scheduled_or_awaiting_approval',
                    ]), true)->items()
            )
        );

        $this->actingAs($member);


        $this->assertEquals(
            1,
            count(
                (new JsonCollectionController())
                    ->posts(new Request([
                        'type' => 'scheduled',
                    ]), true)->items()
            )
        );

        $this->assertEquals(
            1,
            count(
                (new JsonCollectionController())
                    ->posts(new Request([
                        'type' => 'scheduled_or_awaiting_approval',
                    ]), true)->items()
            )
        );

        $postsReviewer = factory(User::class)->create();
        $team->updateMembers([
            [
                'id' => $member->id,
                'permissions' => [
                    'posts.create',
                ]
            ],
            [
                'id' => $approver->id,
                'permissions' => [
                    'approvals.approve',
                    'posts.create',
                ]
            ],
            [
                'id' => $postsReviewer->id,
                'permissions' => [
                    'posts.view',
                    'posts.view_drafts'
                ]
            ],
        ]);

        $this->actingAs($postsReviewer);
        $team->approveInvite(true);

        $this->assertEquals(
            1,
            count(
                (new JsonCollectionController())
                    ->posts(new Request([
                        'type' => 'draft',
                    ]), true)->items()
            )
        );

        $this->assertEquals(
            3,
            count(
                (new JsonCollectionController())
                    ->posts(new Request([
                        'type' => 'scheduled',
                    ]), true)->items()
            )
        );

    }

    private function createTeam(){

        /** @var Team $team */
        $team = factory(Team::class)->create();

        // attach dummy account
        /** @var Account $account */
        $account = factory(Account::class)->create([
            'user_id' => $team->user_id,
        ]);

        $team->updateAccounts([$account->id]);

        $account->setActive(true); // important because it was set inactive because of an internal Account::transform call

        return $team;
    }
}
