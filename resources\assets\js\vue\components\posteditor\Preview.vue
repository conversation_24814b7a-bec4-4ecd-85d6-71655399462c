<template>
    <div v-if="loaded">
        <FacebookPost
            :account="account"
            :text="text"
            :attachments="attachmentsWithUrls"
            :link="link"
            :options="options"
            v-if="account.type.includes('facebook.')" />
        <TwitterTweet
            :account="account"
            :text="text"
            :attachments="attachmentsToShow"
            :link="link"
            :options="options"
            v-else-if="account.type === 'twitter.profile'" />
        <MastodonToot
            :account="account"
            :text="text"
            :attachments="attachmentsToShow"
            :link="link"
            :options="options"
            v-else-if="account.type === 'mastodon.profile'" />
        <InstagramPost
            :account="account"
            :text="text"
            :attachments="attachmentsWithUrls"
            :options="options"
            v-else-if="account.type.includes('instagram.')" />
        <LinkedinPost
            :account="account"
            :text="text"
            :attachments="attachmentsWithUrls"
            :link="link"
            :options="options"
            v-else-if="account.type.includes('linkedin.')" />
        <GBPPost
            :account="account"
            :text="text"
            :attachments="attachmentsWithUrls"
            :link="link"
            :options="options"
            v-else-if="account.type === 'google.location'" />
        <YoutubeVideo
            :account="account"
            :text="text"
            :attachments="attachmentsWithUrls"
            :link="link"
            :options="options"
            v-else-if="account.type === 'google.youtube'" />
        <TikTokPost
            :account="account"
            :text="text"
            :attachments="attachmentsWithUrls"
            :options="options"
            v-else-if="account.type.includes('tiktok.')" />
        
        <RedditPost
            :account="account"
            :text="text"
            :attachments="attachmentsWithUrls"
            :options="options"
            v-else-if="account.type.includes('reddit.')" />
        <PinterestPost
            :account="account"
            :text="text"
            :attachments="attachmentsWithUrls"
            :options="options"
            v-else-if="account.type.includes('pinterest.')" />
        <ThreadsPost
            :account="account"
            :text="text"
            :link="link"
            :attachments="attachmentsWithUrls"
            :options="options"
            v-else-if="account.type.includes('threads.')" />
        <BlueskyPost
            :account="account"
            :text="text"
            :link="link"
            :attachments="attachmentsWithUrls"
            :options="options"
            v-else-if="account.type.includes('bluesky.')" />
            
        <div class="p-4" v-else>
            Preview not available
        </div>
    </div>
</template>

<script>
import FacebookPost from "./preview/FacebookPost.vue";
import TwitterTweet from "./preview/TwitterTweet.vue";
import InstagramPost from "./preview/InstagramPost.vue";
import LinkedinPost from "./preview/LinkedinPost.vue";
import GBPPost from './preview/GBPPost';
import MastodonToot from "./preview/MastodonToot.vue";
import TikTokPost from "./preview/TikTokPost.vue";
import YoutubeVideo from './preview/YoutubeVideo.vue';
import PinterestPost from './preview/PinterestPost.vue';
import RedditPost from "./preview/RedditPost.vue";
import ThreadsPost from "./preview/ThreadsPost.vue";
import BlueskyPost from "./preview/BlueskyPost.vue";
const URL = window.URL || window.webkitURL;
export default {
    name: "Preview",
    components: {RedditPost, LinkedinPost, InstagramPost, TwitterTweet, FacebookPost, GBPPost, MastodonToot, TikTokPost, YoutubeVideo, PinterestPost, ThreadsPost, BlueskyPost},
    props: ["account", "text", "attachments", "link", "options"],
    data() {
        return {
            attachmentUrlsToRevoke: [],
            loaded: false
        };
    },
    methods: {
        getPreviewMedia(attachment) {
            const url = URL.createObjectURL(attachment);
            this.attachmentUrlsToRevoke.push(url);
            return url;
        }
    },
    computed:{
        attachmentsToShow(){
            if(this.attachments){
                return this.attachments.map(a => {
                    let attachment = {
                        _metaData: a._metaData,
                        type: a.type,
                        mime: a.mime,
                        name: a.name,
                        url: a.url ? a.url : ''
                    };
                    // generate link if needed for new uploads
                    if(!attachment.url) {
                        console.log('atttachments computed')
                        attachment.url = this.getPreviewMedia(a);
                    }
                    // if `type` is not mime type already, set `type` from `mime`
                    // this is also for existing uploads
                    if(attachment.mime){
                        attachment.type = attachment.mime;
                    }

                    return attachment;
                });
            }
        },
        attachmentsWithUrls(){
            return this.attachments.map(a => {
                let attachment = {
                    _metaData: a._metaData,
                    type: a.type,
                    mime: a.mime,
                    name: a.name,
                    url: a.url ? a.url : ''
                };
                // generate link if needed for new uploads
                if(!attachment.url) {
                    attachment.url = this.getPreviewMedia(a);
                }
                // if `type` is not mime type already, set `type` from `mime`
                // this is also for existing uploads
                if(attachment.mime){
                    attachment.type = attachment.mime;
                }

                return attachment;
            });
        }
    },
    mounted(){
        this.$nextTick(()=> {
            this.loaded = true;
        });
    },
    beforeDestroy() {
        this.attachmentUrlsToRevoke.forEach(url => {
            URL.revokeObjectURL(url);
        });
    }
};
</script>

<style lang="scss" scoped>
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');

</style>
