<?php

namespace App\Observers;

use App\Account;
use App\Helpers\EmailListHelper;

class AccountObserver
{
    /**
     * Handle to the account "created" event.
     *
     * @param  \App\Account  $account
     * @return void
     */
    public function created(Account $account)
    {
        // used to greet new users who signed up with twitter; no more now as its against twitter TOS

        // send event
        try {
            EmailListHelper::getInstance()->sendEvent($account->user, 'account_added', $account->type);
        } catch (\Exception $exception){
            report($exception);
        }

    }

    /**
     * Handle the account "updated" event.
     *
     * @param  \App\Account  $account
     * @return void
     */
    public function updated(Account $account)
    {
        //
    }

    /**
     * Handle the account "deleting" event.
     *
     * @param  \App\Account $account
     * @return bool
     */
    public function deleting(Account $account)
    {
        // fb webhook
        try {
            $account->setupWebhook(false);
        } catch (\Exception $e) {
            if(app()->environment() !== 'local') {
                report($e);
            }
        }

        // send event
        try {
            EmailListHelper::getInstance()->sendEvent($account->user, 'account_deleted', $account->name . ' (' . $account->type . ')');
        } catch (\Exception $exception){
            report($exception);
        }

        $accountId = $account->id;
        // delete insights data for this account
        dispatch(function() use ($accountId){
            get_insights_db()->table('post_metrics')->where('account_id', $accountId)->delete();
            get_insights_db()->table('account_metrics')->where('account_id', $accountId)->delete();
            get_insights_db()->table('post_hashtags')->where('account_id', $accountId)->delete();
            get_insights_db()->table('misc_metrics')->where('account_id', $accountId)->delete();
        });

        return true;
    }

    public function deleted(Account $account){
        //
    }
}
