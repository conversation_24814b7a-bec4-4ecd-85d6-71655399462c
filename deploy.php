<?php
namespace Deployer;

// https://github.com/deployphp/deployer/blob/master/recipe/laravel.php
require 'recipe/laravel.php';

set('keep_releases', 30);

set('ssh_type', 'native');
set('ssh_multiplexing', false);

// Project name
set('application', 'SocialBu');

// Project repository
set('repository', '*****************:usamaejaz/socialtool.git');

// [Optional] Allocate tty for git clone. Default value is false.
set('git_tty', false);

// increase timeout from 300 which is default
set('default_timeout', 12000);

// Shared files/dirs between deploys 
add('shared_files', []);
add('shared_dirs', []);

// fix for older ssh servers
set('git_ssh_command', 'ssh');

// Hosts
host('**************')
    ->setPort(22)
    ->setRemoteUser('root')
    ->setIdentityFile('~/.ssh/id_rsa')
    ->setForwardAgent(true)
    ->setSshArguments(['-o StrictHostKeyChecking=no'])
    ->set('deploy_path', '/socialbu');
    
// Tasks
task('test', function(){
    writeln('Hello world');
});


task('deploy:upload_assets', function(){


    // before upload, confirm that the build is ready or done already
    $assetsBuilt = askConfirmation('Have you built the assets before deploying?');

    if(!$assetsBuilt){
        // fail
        throw new \Exception('Please build the assets before deploying');
    }

    $fixedPath = __DIR__;

    upload($fixedPath . '/public/js', '{{release_path}}/public');
    upload($fixedPath . '/public/css', '{{release_path}}/public');
    upload($fixedPath . '/public/fonts', '{{release_path}}/public');
    upload($fixedPath . '/public/images', '{{release_path}}/public');
    upload($fixedPath . '/public/images', '{{release_path}}/public');
    upload($fixedPath . '/public/mix-manifest.json', '{{release_path}}/public');
});

task('deploy:node_install', function(){
    cd('{{release_path}}');
    run('GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no" npm install');
});

task('deploy:build_assets', function(){

    // if .env.bak exists, restore it
    if(file_exists(__DIR__ . '/.env.bak')){
        rename(__DIR__ . '/.env.bak', __DIR__ . '/.env');
    }

    // first get the server's .env because we need to set it in our local environment before building assets
    $env = run('cat {{deploy_path}}/shared/.env');

    // backup the local .env file
    rename(__DIR__ . '/.env', __DIR__ . '/.env.bak');

    // save the .env file
    file_put_contents(__DIR__ . '/.env', $env);

    // first, clean local folders
    runLocally('rm -rf public/js');
    runLocally('rm -rf public/css');
    runLocally('rm -rf public/fonts');
    runLocally('rm -rf public/images');

    runLocally('GIT_SSH_COMMAND="ssh -o StrictHostKeyChecking=no" npm install');
    runLocally('npm run prod', [
        'timeout' => 1200,
    ]);

    // restore the local .env file
    rename(__DIR__ . '/.env.bak', __DIR__ . '/.env');
});

task('deploy:passport', function(){
    cd('{{release_path}}');
    run('php artisan passport:keys');
});

task('deploy:reload_service', function(){

    cd('{{release_path}}');

    run('php artisan clear_comparison_pages_cache');

    // reload php/nginx
    run('service php7.3-fpm reload'); // needed for clearing cache
    run('service nginx reload');

    run('php artisan horizon:terminate'); // restart horizon queues

    run('supervisorctl stop text2img-server:*');
    run('kill $(lsof -t -i:3603) || echo "Text2Img server not running"');
    run('supervisorctl start text2img-server:*');

    run('php artisan sitemap:generate');
});

// [Optional] if deploy fails automatically unlock.
after('deploy:failed', 'deploy:unlock');

// Migrate database before symlink new release.

before('deploy:symlink', 'artisan:migrate');
before('deploy:symlink', 'deploy:node_install');
before('deploy:symlink', 'deploy:upload_assets');

// build before deploying separately
//before('deploy:upload_assets', 'deploy:build_assets');

after('deploy', 'deploy:passport');
after('deploy', 'deploy:reload_service');
after('deploy', 'deploy:notify_release');

task('deploy:notify_release', function(){
    $commit = trim(runLocally('git rev-parse HEAD'));

    // get last 5 commit messages
    $lastCommitMessages = trim(runLocally('git rev-list --format=%B --max-count=5 ' . $commit));
    $lastCommitMessagesArr = array_filter(explode("\n", $lastCommitMessages));
    foreach($lastCommitMessagesArr as &$line){
        $line = trim($line);
        if(strpos($line, 'commit') > -1){
            $line = ''; // don't want commit hash
            continue;
        }
        if(strlen($line) > 0){
            $line = '- ' . $line;
        }
    }
    $lastCommitMessages = implode("<br/>\n", array_filter($lastCommitMessagesArr));

    // notify sentry
    try {
        runLocally('curl "https://sentry.io/api/hooks/release/builtin/1235589/****************************************************************/" -X "POST" -H "Content-Type: application/json" -d "{\"version\": \"' . $commit . '\"}"');
        runLocally('curl "https://sentry.io/api/hooks/release/builtin/5306065/****************************************************************/" -X "POST" -H "Content-Type: application/json" -d "{\"version\": \"' . $commit . '\"}"');
    } catch (\Exception $e) {
        writeln('Ignored Error: ' . $e->getMessage());
    }

    // notify basecamp
    try {
        runLocally('curl "https://3.basecamp.com/4644022/integrations/bGdZCfTt1K7vFHXYzLMvJoLC/buckets/17678791/chats/2791260950/lines" -X "POST" -d content="<b>New deployment (' . $commit . ')</b><br/>Last 5 commits are listed below: <br/>' . $lastCommitMessages . '"');
    } catch (\Exception $e) {
        writeln('Ignored Error: ' . $e->getMessage());
    }
});


task('clear', [
    'artisan:cache:clear',
    'artisan:view:clear',
    'artisan:config:cache',
    //'artisan:route:cache',
]);
