/**
 * Code here is lazy loaded on all website (marketing) pages
 */
/**
 * jQuery and other first-level dependencies
 */
global.jQuery = require("jquery");
require("bootstrap"); // bootstrap framework js

import axios from "axios";
import copy from "copy-to-clipboard";
import { spinnerHtml, axiosErrorHandler } from "../components";
import lozad from "lozad";

require("floatthead"); // required for pricing table sticky header
const { throttle } = require("lodash");

// jQuery document.ready dependent code
jQuery(function($) {
    // initialize tooltip
    $('[data-toggle="tooltip"]').tooltip();

    // lazy load images
    const observer = lozad(); // lazy loads elements with default selector as '.lozad'
    observer.observe();

    // scroll to element
    $("a[data-scroll]").on("click", function() {
        const id = $(this).attr("href");
        if (!id.startsWith("#")) return;
        $([document.documentElement, document.body]).animate(
            {
                scrollTop: $(id).offset().top
            },
            100
        );
        return false;
    });

    // pricing page
    $(".plan_type_input input")
        .on("change", () => {
            const type = $(".plan_type_input input:checked").val();
            if (type === "monthly") {
                $(".yearly").hide();
                $(".yearly_plan").removeClass("btn-outline-primary").addClass('btn-outline-secondary');
                $(".monthly_plan").removeClass('btn-outline-secondary').addClass('btn-outline-primary');
                $(".monthly").show();
            } else if (type === "yearly") {
                $(".monthly").hide();
                $(".monthly_plan").removeClass("btn-outline-primary").addClass('btn-outline-secondary');
                $(".yearly_plan").removeClass('btn-outline-secondary').addClass('btn-outline-primary');
                $(".yearly").show();
            }
        })
        .trigger("change");

    // $("#features td:nth-of-type(3), #features th:nth-of-type(3)").remove(); // remove starter plan columns

    $("table#features").floatThead({
        responsiveContainer: function(){
            return $("table#features").closest('.table-responsive');
        }
    });

    // frequent questions accordion 
    (() => {
    
        $('#frequent-questions .collapse').on('show.bs.collapse', function () {
            const $header = $(this).prev('.card-header');

            $header.removeClass('pb-5').addClass('pb-2');
            $header.removeClass('border-bottom');
            $header.find('i').removeClass('ph-caret-down').addClass('ph-caret-up');
        }).on('hide.bs.collapse', function () {
            const $header = $(this).prev('.card-header');
            
            $header.removeClass('pb-2').addClass('pb-5');
            $header.find('i').removeClass('ph-caret-up').addClass('ph-caret-down');
            $header.addClass('border-bottom');
        });
    })();
    
    // startup queries accordion 
    (() => {
    
        $('#startup-queries .collapse').on('show.bs.collapse', function () {
            const $header = $(this).prev('.card-header');

            $header.find('i').removeClass('ph-caret-down').addClass('ph-caret-up');

        }).on('hide.bs.collapse', function () {
            const $header = $(this).prev('.card-header');
            
            $header.find('i').removeClass('ph-caret-up').addClass('ph-caret-down');
        });
    })();
    
    
    // SB features accordion
    (() => {
        const $accordion = $('#publish_mobile_accordion');

        $accordion.find('i').addClass('text-primary');
        $accordion.find('span').addClass('text-primary');

        $('.collapse.landing-page-accordion').on('show.bs.collapse', function () {
            const $element = $(this).parent().find('div[data-type]');

            $element.removeClass('border-bottom');
            console.log($element.find('i'));
            $element.find('i').removeClass('ph-arrow-right').addClass('text-primary ph-arrow-down');

            $element.find('i').addClass('text-primary');
            $element.find('span').addClass('text-primary');
        }).on('hide.bs.collapse', function () {
            const $element = $(this).parent().find('div[data-type]');

            $element.addClass('border-bottom');
            $element.find('i').removeClass('text-primary ph-arrow-down').addClass('ph-arrow-right');
            $element.find('span').removeClass('text-primary');
        });

    })();

    (() => {
        $('.everything-section-button').on("click", function() {
            // Remove 'active' and 'text-primary' from all buttons
            $('button').removeClass('active text-primary rounded-xl').addClass('border-none');
        
            // Add 'active' and 'text-primary' to the clicked button
            
            $(this).removeClass('border-none').addClass('active text-primary  rounded-xl');
        
            // Get the target section from the button's data-target attribute
            var targetSection = $(this).data('target');
            // Hide all sections first with the class
            $('.section-content').addClass('d-none').removeClass('d-block');
        
            // Show the selected section by removing 'd-none' and adding 'd-block'
            $(targetSection).removeClass('d-none').addClass('d-block');
        })
    })();

    // copy email
    (() => {
        const btn = $('#copy_email_button');
        btn.on("click", function() {
            copy('<EMAIL>');
            btn.text('Copied!');
            btn.tooltip("update");
            setTimeout(() => {
                btn.text('copy e-mail address');
            }, 1000);
        });
    })();

    // show team
    (function () {
        window.showTeamContent = function (id) {
            const cards = $('.row [data-member-team]');

            if (id === 'all') {
                cards.show();
            } else {
                cards.hide();
                cards.filter('[data-member-team="' + id + '"]').show();
            }

            $('.btn.team-button').removeClass('active');
            $('#' + id).addClass('active');
        };
    })();

    // email subscribe on blog
    (() => {
        let emlSubPending = false;
        $("form.email_subscribe").on("submit", function() {
            (async () => {
                if (emlSubPending) return;
                emlSubPending = true;
                const $submitBtn = $(this).find("button[type='submit']");
                const btnOrigHtml = $submitBtn.html();
                $submitBtn.empty().append(`<i class="ph ph-circle-notch ph-spin"></i>`);
                try {
                    const { data } = await axios.post($(this).attr("action"), $(this).serialize());
                    if (data.success) {
                        // good
                        window.alert(data.message);
                        $(this)[0].reset();
                    } else {
                        window.alert(data.message);
                    }
                } catch (e) {
                    (() => {
                        if (e.response && e.response.data) {
                            const errors = e.response.data.errors;
                            if (errors) {
                                Object.keys(errors).forEach(k => {
                                    window.alert(errors[k].join(" \n"));
                                });
                                return;
                            }
                            if (e.response.data && e.response.data.message) {
                                return window.alert(e.response.data.message);
                            }
                        }
                        window.alert(e.message);
                    })();
                }
                emlSubPending = false;
                $submitBtn.empty().append(btnOrigHtml);
            })();
            return false;
        });
    })();

    // menu
    (() => {
        const $overlayMenu = $("#overlay");
        const $navbar = $("#navbar_main");
        const stickyClassName = "sticky-navbar";

        // fixing navbar when scrolling
        (() => {
            let lastScrollTop = 0;
            const onScroll = () => {
                const st = $(this).scrollTop();
                if (!$overlayMenu.is(":hidden")) {
                    return $navbar.addClass(stickyClassName);
                }
                if (st > lastScrollTop) {
                    // downscroll code
                    $navbar.removeClass(stickyClassName);
                } else {
                    if (window.scrollY > 20) {
                        $navbar.addClass(stickyClassName);
                    } else {
                        $navbar.removeClass(stickyClassName);
                    }
                }
                lastScrollTop = st;
            };
            $(window).on("scroll", throttle(onScroll, 200));
        })();

        // hover effect over dropdown menu
        (() => {
            let toggleOverlay = false;
            let toggleStickyMenu = false;
            $(".dropdown-fullwidth")
                .on("show.bs.dropdown", function() {
                    if ($overlayMenu.is(":hidden")) {
                        toggleOverlay = true;
                        $overlayMenu.fadeIn(200);
                        if (!$navbar.hasClass(stickyClassName)) {
                            $navbar.addClass(stickyClassName);
                            toggleStickyMenu = true;
                        }
                    }
                })
                .on("hide.bs.dropdown", function() {
                    if (toggleOverlay) {
                        toggleOverlay = false;
                        $overlayMenu.hide();
                        if (toggleStickyMenu) {
                            $navbar.removeClass(stickyClassName);
                            toggleStickyMenu = false;
                        }
                    }
                });
        })();

        // overlay for menu open on mobile
        (() => {
            const $navbarBrand = $(".navbar-brand");
            const $menuSignupBtn = $("#nav_signup_btn");
            $("#menu_content")
                .on("hide.bs.collapse", function() {
                    $overlayMenu.fadeOut(200);
                    $menuSignupBtn.show();
                    $navbarBrand.css("opacity", "1");
                })
                .on("show.bs.collapse", function() {
                    $overlayMenu.fadeIn(200);
                    $navbarBrand.css("opacity", "0");
                    $menuSignupBtn.hide();
                });
            $overlayMenu.on("click", function() {
                $(this).fadeOut(200);
            });
        })();
    })();

    // tools: generate content
    (() => {

        window.schedulePost = function(){
            // open socialbu.com/app for now
            window.open("https://socialbu.com/app", "_blank");
        };

        const $form = $("#generate_content_form");
        const $result = $("#generated_content");
        const $text = $("#post_text");
        const $action = $("#post_action")
        const $submitBtn = $form.find("button[type=submit]");
        const $copyBtn = $("#copy_btn");
        const $scheduleBtn = $("#schedule_btn");
        const $downloadBtn = $("#download_btn");
        let downloadFile;
        if($copyBtn.length){

            $copyBtn.on("click", function() {
                $copyBtn.find("i").removeClass("ph-copy").addClass("ph-check");
                $copyBtn.find(".copy-text").text("Copied!");
                $copyBtn.tooltip("update");
                setTimeout(() => {
                    $copyBtn.find("i").removeClass("ph-check").addClass("ph-copy");
                    $copyBtn.find(".copy-text").text("Copy");
                }, 1000);
                copy($result.text());
            }).hide();
        } else if($downloadBtn.length){
            $downloadBtn.on("click", function() {
                if(!downloadFile) return;
                const filename = $downloadBtn.attr("data-name") || "Blog";
                const link = document.createElement('a');
                link.href = window.URL.createObjectURL(downloadFile);
                link.download = "SocialBu " + filename + " Image - " + (new Date()).toString();
                link.click();
            }).hide();
        }
        $form.on("submit", ()=>{
            (async ()=>{
                const type = $form.find("input[name='type']").val();
                $submitBtn.prop("disabled", true);
                $copyBtn.hide();
                $downloadBtn.hide();
                $scheduleBtn.hide();
                $text.hide();
                const formData = $form.serialize();
                const originalResult = $result.html();
                $result.empty().append(spinnerHtml);
                try {
                    if(type.includes("text2img") && type !== "prompt_text2img"){
                        const { data, headers } = await axios.post("/tools/_content_generator", formData, {
                            responseType: "blob",
                            timeout: 120000
                        });

                        let headerLine = headers["content-disposition"];
                        let filename = headerLine.split("filename=")[1];

                        const file = new File([data], filename, {
                            type: headers["content-type"]
                        });
                        $downloadBtn.prop("disabled",false);
                        downloadFile = file;
                        const imgUri = window.URL.createObjectURL(file);

                        $result.empty().append(`<img src="${imgUri}" alt="generated image" class="img-responsive" />`);
                        $action.removeClass('position-absolute action-container justify-content-end').addClass('position-relative');
                        $downloadBtn.show();

                        setTimeout(()=>{
                            window.URL.revokeObjectURL(imgUri);
                        }, 5000);

                    } else {
                        const { data } = await axios.post("/tools/_content_generator", formData);
                        if(data && data.length){
                            $copyBtn.prop("disabled",false);
                            $result.empty().append(data);
                            $result.css("white-space", "pre-wrap");
                            $result.addClass("text-dark");
                            $copyBtn.show();
                            $scheduleBtn.show();
                        } else {
                            $result.html(`<p>No content generated. Please try later. This can also happen to prevent service abuse.</p>`);
                        }
                    }

                } catch (e) {
                    window.alert(axiosErrorHandler(e, true));
                    $result.empty().append(originalResult);
                } finally {
                    $submitBtn.prop("disabled", false);
                }
            })();
            return false;
        });
    })();

    // append product menu to more_features div (used on platform pages)
    (() => {
        const $container = $("#more_features");
        const $targetDiv = $container.find("._placeholder");
        $targetDiv.append($("#product_menu_contents").html());
        $targetDiv.find(".col-lg-2").addClass("border-top border-bottom");
        $targetDiv.find(".col-lg-2:first").addClass("border-left");
        $targetDiv.find(".col-lg-2:last").addClass("border-right");
        $container.fadeIn();
    })();

    //demo form
    (() => {
        const $form = $("#demo_form");
        const $widget = $("#calendly-inline-widget")
        let calendlyLoaded = false;
        $form.on("submit", (event) => {
            event.preventDefault(); // Prevent default form submission
            (async ()=>{
                const formData = $form.serialize();
                try {
                    const response = await axios.post("/demo/save_form", formData);
                    if (response.status === 200) {
                        $form.addClass("d-none");
                        $widget.removeClass("d-none");

                        if(!calendlyLoaded) {
                            // load js from `https://assets.calendly.com/assets/external/widget.js`
                            $("head").append(`<script src="https://assets.calendly.com/assets/external/widget.js" type="text/javascript"></script>`);
                            calendlyLoaded = true;
                        }
                    }
                } catch (e) {
                    window.alert(axiosErrorHandler(e, true));
                }
            })()
        })
    })();
    //glossary items
    (() => {

        $(".glossary-alphabet-nav a").on("click", function(e) {
            const $element = $(this);
            $element.addClass("active").siblings().removeClass("active");
        });

        // search glossary items
        const $form = $("#search_glossary_item");

        $form.on("submit", function(event){
            event.preventDefault();
        });

        $form.on("input", function(event){
            $('.glossary-alphabet-section').hide();
            const searchValue = event.target.value;
            const $glossaryItems = $('.glossary');
            if(searchValue.length > 0){
                $glossaryItems.each(function(){
                    const $item = $(this);
                    const itemText = $item.find('.card-title').text().toLowerCase();
                    if(itemText.includes(searchValue.toLowerCase())){
                        $item.show();
                    } else {
                        $item.hide();
                    }
                });
            } else {
                $glossaryItems.show();
                $('.glossary-alphabet-section').show();

            }
        });


    })();


    // ask anything socialbu
    (() => {
        const askQuestionBtn = $("#askQuestion");
        const askQuestionModal = $("#askQuestionModal");
        const askQuestionForm = $("#questionForm");
        const closeModalBtn = $('#closeQuestionModal');
        let sendingQuestion = false;
        //show the modal        
        askQuestionBtn.on("click", function(){
            askQuestionModal.modal("show");
        });

        //close the modal
        closeModalBtn.on("click", function(){
            askQuestionModal.modal("hide");
        });

        askQuestionForm.on("submit", function(event) {
            event.preventDefault();
            if (sendingQuestion) return;
            sendingQuestion = true;
            const questionInputValue = $("#askQuestionInput").val(); // Get the input value
            (async () => {
                try {
                    const res = await axios.post("/ask", {
                        message: questionInputValue,
                    })
                    $("#askQuestionInput").val(" "); 
                    askQuestionModal.modal("hide");           
                } catch (error) {
                    window.alert(axiosErrorHandler(e, true));
                } finally{
                    sendingQuestion = false;
                }
            })();

        });
    })();
});
