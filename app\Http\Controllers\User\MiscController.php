<?php

namespace App\Http\Controllers\User;

use App\Helpers\EmailListHelper;
use GuzzleHttp\Exception\BadResponseException;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class MiscController extends Controller
{
    /**
     * @param Request $request
     * @throws \Illuminate\Validation\ValidationException
     */
    public function saveOnBoardingTourProgress(Request $request){
        $this->validate($request, [
            'completed' => 'required|string',
        ]);
        $completedTourIdsCSV = $request->input('completed');
        $completedTourIds = explode(',', $completedTourIdsCSV);

        if(empty($completedTourIds)){
            return;
        }

        // save the csv
        user()->setOption('completed_tour_steps', $completedTourIdsCSV);
    }


    /**
     * @param Request $request
     * @param $slug
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector|\Symfony\Component\HttpFoundation\StreamedResponse|void
     * @throws \Illuminate\Validation\ValidationException
     */
    public function proxyRequestToUrl(Request $request, $slug){

        // $slug is is account id in case of account image
        $this->validate($request, [
            'hash' =>  'string|required',
        ]);

        $imgUrlHash = $request->input('hash');

        try {
            $url = decrypt($imgUrlHash);
        } catch (\Exception $exception){
            if(\Validator::make($request->input(), [
                'hash' =>  'string|url',
            ])->fails()){
                return abort(404);
            } else {
                $url = $request->input('hash');
            }
        }

        if(\Validator::make(['url' => $url], [
            'url' =>  'string|url',
        ])->fails()){
//
//            if(Str::endsWith($slug, ['jpg', 'png', 'jpeg',])){
//                // bad response so show dummy pic
//                return redirect(url('/images/no-image.png'));
//            }
//
            return redirect(url('/images/no-image.png'));
        }

        $randomProxy = null;
        if(app()->environment('production')) {
            $randomProxy = Arr::random(config('app.proxies'));
        }

        // options to pass to guzzle
        // we also set header to mimic a normal browser
        $guzzleOpts = [
            'headers' => [
                'User-Agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/69.0.3497.100 Safari/537.36', // chrome
            ],
            'proxy' => $randomProxy,
            'connect_timeout' => 100,
            'read_timeout' => 60,
            'timeout' => 120,
        ];

        $client = guzzle_client($guzzleOpts);

        try {

            $res = $client->get($url);
            $lowerCaseHeaders = collect($res->getHeaders())->mapWithKeys(function($val, $k){
                return [
                    strtolower($k) => $val,
                ];
            });

            $secondsToCache = 31536000;

            return response()->streamDownload(function() use($res){
                $body = $res->getBody();
                while (! $body->eof()) {
                    echo $body->read(2048);
                }
            }, null, $lowerCaseHeaders->only([
                'content-length',
                'content-type',
                'content-disposition',
            ])->merge([
                'cache-control' => 'public, max-age=' . $secondsToCache . ', immutable',
                'etag' => md5($url),
                'expires' => now()->addSeconds($secondsToCache)->format('D, d M Y H:i:s \G\M\T'),
            ])->toArray());

        } catch (BadResponseException $exception) {

            $res = $exception->getResponse();

            if(Str::endsWith($slug, ['jpg', 'png', 'jpeg',]) && $res){
                // bad response so show dummy pic
                return redirect(url('/images/no-image.png'));
            }

            if(!Str::contains($exception->getMessage(), [403])){ // ignore url expired
                report($exception);
            }


            return redirect($url);
        }
    }

    public function facebookProfilePic(Request $request, $accountId, $objectId){

        $url = session('facebook/picture/' . $accountId . '/' . $objectId, null);

        if(!$url) {
            /** @var \App\Account $account */
            $account = \App\Account::findByPublicId($accountId);
            if (!$account) abort(404);
            if ($account->type !== 'facebook.page') abort(404);
            try {
                $url = $account->getApi()->get("/" . $objectId . "/picture?redirect=0")->getGraphNode()->asArray()['url'];
                session([
                    'facebook/picture/' . $accountId . '/' . $objectId => $url,
                ]);
            } catch (\Exception $exception) {
                abort(404);
            }
        }

        return redirect($url);
    }

    /**
     * Forward event from frontend and record it in our CRM
     * @param Request $request
     * @param $event
     * @return void
     */
    public function recordEventInCrm(Request $request){

        // re-flash the session messages
        $request->session()->reflash();

        $this->validate($request, [
            'event' => 'required|string',
            'data' => 'nullable',
        ]);
        try {
            EmailListHelper::getInstance()->sendEvent(user(), $request->input('event'), $request->input('data'));
        } catch (\Exception $exception){
            report($exception);
        }
    }

    /**
     * @throws ValidationException
     */
    public function flashAndRedirect(Request $request)
    {
        $this->validate($request, [
            'message' => 'required|string',
            'type' => 'required|string|in:success,info,warning,danger',
            'to' => 'required|string',
        ]);
        $request->session()->flash($request->input('type'), $request->input('message'));

        // the redirect param should not be an external url
        if(Str::startsWith($request->input('to'), ['http://', 'https://', '//'])){
            abort(404);
        }

        return redirect($request->input('to'));
    }
}
