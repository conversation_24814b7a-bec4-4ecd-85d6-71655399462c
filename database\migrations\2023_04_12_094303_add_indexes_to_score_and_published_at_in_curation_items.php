<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddIndexesToScoreAndPublishedAtInCurationItems extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('curation_items', function (Blueprint $table) {
            $table->index('score');
            $table->index('published_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('curation_items', function (Blueprint $table) {
            $table->dropIndex('curation_items_score_index');
            $table->dropIndex('curation_items_published_at_index');
        });
    }
}
