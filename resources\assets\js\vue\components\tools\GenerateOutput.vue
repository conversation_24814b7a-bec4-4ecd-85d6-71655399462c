<template>
    <div>
        <div class="mt-2 pb-3">
            <div v-if="loading" v-html="overlayLoaderHtml"></div>
            <DynamicForm :fields="fields" :output-components="outputComponents" @submit="submit"/>
        </div>
        <div class="card bg-light bg-none-mobile mt-7 mb-4" v-if="outputData.length || loading">
            <div class="card-body p-20">
                <div v-if="loading"> 
                    Generating...
                </div>
                <div v-else>
                    <h6 class="display-5" v-if="title">{{ title }}</h6> 
                    <div class="pt-4 d-flex flex-wrap" v-if="outputData.length">
                        <div v-for="(data, i ) in outputData" :key="data + i" class="d-flex justify-content-between align-items-center mb-4 mr-4 px-4 py-3 bg-white rounded">
                            <span class="post">{{ data }} </span>
                            <span v-if="data == copiedData"> <i class="badge ml-4 badge-success">copied to clipboard!</i> </span>
                            <i class="cursor-pointer ph ph-copy-simple ph-md ml-4" v-else @click="copyPost(data)" ></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import DynamicForm from "./DynamicForm.vue";
import { axiosErrorHandler, overlayLoaderHtml, axios } from '../../../components';
export default {
    name: 'GenerateOutput',
    components: {
        DynamicForm,
    },
    props: {
    },
    data() {
        return {
            type:null,
            fields: [],
            title:'',
            outputComponents: [],
            copiedData: null,
            outputData:[],
            splitDataBy: null,
            parameters: {},
            loading: false,
        }
    },
    computed:{
        overlayLoaderHtml: () => overlayLoaderHtml,
    },
    methods: {
        initialize(data) {
            this.title = data.title;
            this.fields = data.form.fields;
            this.outputComponents = data.form.outputComponents || [];
            this.type = data.type ? data.type : null;
            this.splitDataBy = data.splitDataBy ? data.splitDataBy : null;
            this.parameters = data.inputData ? data.inputData : {};
        },
        async submit(inputData) {
            try{
                this.loading = true;
                let combineData = {...inputData, ...this.parameters};
                let response = await axios.post("/api/v1/generate/forms/"+this.type, combineData);
                if(response.data && response.data.text){
                    if(this.splitDataBy){
                        this.outputData = response.data.text.split(this.splitDataBy);
                    }
                    else{
                        this.outputData = [response.data.text];
                    }
                } else {
                    console.error("invalid response format:");
                }
                this.loading = false;
            }catch(e){
                axiosErrorHandler(e);
                this.loading = false;
            }
            

        },
        copyPost(data) {
            navigator.clipboard.writeText(data).then(() => {
                this.copiedData = data;
                setTimeout(() => {
                    this.copiedData = null; // Clear the copied post after 1 second
                }, 1000);
            }).catch(err => {
                console.error("Error copying text:", err);
            });
        },
    }
    
}
</script>