<?php

namespace App\Notifications;

use App\Post;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class PostFailure extends Notification implements ShouldQueue
{
    use Queueable;

    private $post;

    private $reason;

    /**
     * Create a new notification instance.
     *
     * @param Post $post
     */
    public function __construct(Post $post)
    {
        $this->post = $post;
        $this->reason = isset($this->post->result['_errorMsg']) ? $this->post->result['_errorMsg'] : 'Unknown error (no details available)';
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->subject('Couldn\'t publish a post to your ' . $this->post->account->getType())
            ->error()
            ->line('One of your posts could not be published to ' . e($this->post->account->name) . ' (' . $this->post->account->getType() . ')')
            ->line(e($this->reason))
            ->action('See more', url('app/publish/posts'))
            ->line('Please do not hesitate to contact us if you need any assistance or have any questions.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'post_id' => $this->post->id,
            'account_name' => $this->post->account->name,
            'account_type' => $this->post->account->getType(),
            'reason' => $this->reason,
            '_level' => 'danger',
        ];
    }
}
