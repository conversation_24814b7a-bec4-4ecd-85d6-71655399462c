img {
    max-width: 100%;
    height: auto;
}
.img-thumbnail {
    padding: 0.25rem;
    border-color: lighten($color-divider, 3%);
    border-radius: 3px;
}
.img-outside-right {
    overflow: hidden;
    img {
        width: 100%;
        transform: translateX(15%);
    }
}
  
  
  
// Avatar
//
.avatar {
    border-radius: 10rem;
    width: 48px;
    height: 48px;
}
.avatar-xxs {
    width: 24px;
    height: 24px;
}
.avatar-xs {
    width: 32px;
    height: 32px;
}
.avatar-sm {
    width: 40px;
    height: 40px;
}
.avatar-lg {
    width: 48px;
    height: 48px;
}
.avatar-xl {
    width: 80px;
    height: 80px;
}
  
.img-fadein {
    opacity: .75;
    transition: .5s;
    &:hover {
        opacity: 1;
    }
}
