// common operators
export const ID_OPERATORS = [
    {
        type: "IS",
        description: "is"
    },
    {
        type: "IS_NOT",
        description: "is not"
    }
];
export const NUMBER_OPERATORS = [
    {
        type: "IS",
        description: "is"
    },
    {
        type: "IS_NOT",
        description: "is not"
    },
    {
        type: "IS_GT",
        description: "is greater than"
    },
    {
        type: "IS_LT",
        description: "is less than"
    }
];
export const STRING_OPERATORS = [
    {
        type: "IS",
        description: "is"
    },
    {
        type: "IS_NOT",
        description: "is not"
    },
    {
        type: "CONTAINS",
        description: "contains"
    },
    {
        type: "NOT_CONTAINS",
        description: "doesn't contain"
    },
    {
        type: "STARTS_WITH",
        description: "starts with"
    },
    {
        type: "ENDS_WITH",
        description: "ends with"
    }
];
export const ARRAY_OPERATORS = [
    {
        type: "CONTAINS",
        description: "contains"
    },
    {
        type: "NOT_CONTAINS",
        description: "doesn't contain"
    }
];
export const YES_NO_OPERATORS = [
    {
        type: "YES",
        description: "yes"
    },
    {
        type: "NO",
        description: "no"
    }
];
// conditions
const TWITTER_CONDITIONS = [
    {
        key: "twitter.name",
        description: "Name",
        operator: STRING_OPERATORS,
        value: {
            type: "text"
        }
    },
    {
        key: "twitter.first_name",
        description: "First Name",
        operator: STRING_OPERATORS,
        value: {
            type: "text"
        }
    },
    {
        key: "twitter.username",
        description: "Username",
        operator: STRING_OPERATORS,
        value: {
            type: "text"
        }
    },
    {
        key: "twitter.description",
        description: "Bio / Description",
        operator: STRING_OPERATORS,
        value: {
            type: "textarea"
        }
    },
    {
        key: "twitter.website",
        description: "Website",
        operator: STRING_OPERATORS,
        value: {
            type: "text"
        }
    },
    {
        key: "twitter.location",
        description: "Location",
        operator: STRING_OPERATORS,
        value: {
            type: "text"
        }
    },
    {
        key: "twitter.followers",
        description: "Followers count",
        operator: NUMBER_OPERATORS,
        value: {
            type: "number"
        }
    },
    {
        key: "twitter.friends",
        description: "Friends count",
        operator: NUMBER_OPERATORS,
        value: {
            type: "number"
        }
    },
    {
        key: "twitter.is_protected",
        description: "Has tweets protected",
        operator: YES_NO_OPERATORS
    },
    {
        key: "twitter.is_verified",
        description: "Has verified account",
        operator: YES_NO_OPERATORS
    }
];
const FACEBOOK_CONDITIONS = [
    {
        key: "facebook.name",
        description: "Name",
        operator: STRING_OPERATORS,
        value: {
            type: "text"
        }
    },/*
    {
        key: "facebook.location",
        description: "Location",
        operator: STRING_OPERATORS,
        value: {
            type: "text"
        }
    },
    {
        key: "facebook.is_fan",
        description: "Is a fan",
        operator: YES_NO_OPERATORS
    }*/
];
const INSTAGRAM_CONDITIONS = [
    {
        key: "instagram.username",
        description: "Username",
        operator: STRING_OPERATORS,
        value: {
            type: "text"
        }
    }
];
// actions
export default {
    // array or fn which returns array
    events: [
        {
            type: "incoming_message",
            description: "Message is received",
            options: ["account"]
        },
        {
            type: "account_post",
            description: "New post is published by account",
            options: ["account"]
        },/*
        {
            type: "facebook.user_post",
            description: "Someone posts on page",
            options: ["account"]
        },*/
        {
            type: "facebook.post_reply",
            description: "Someone replies to a post",
            options: ["account"]
        },
        {
            type: "facebook.review",
            description: "Someone posts a review",
            options: ["account"]
        },/*
        {
            type: "twitter.mention",
            description: "Someone mentions me",
            options: ["account"]
        },*/
        // {
        //     type: "twitter.friend_follows_me",
        //     description: "Friend follows me",
        //     options: ["account"]
        // },
        {
            type: "instagram.new_comment",
            description: "Someone leaves a comment",
            options: ["account"]
        },
        {
            type: "rss_new_item",
            description: "RSS Feed has new item",
            options: ["url"]
        },
        {
            type: "webhook",
            description: "Webhook is triggered",
            options: ["webhook_url"]
        }
    ],
    actions: {
        // `type` is unique for each action
        /** specific actions based on events **/
        incoming_message: [
            {
                type: "send_reply",
                description: "Send reply",
                input: [
                    {
                        name: "content",
                        type: "textarea",
                        required: true,
                        width: 6,
                        newline: true // place in separate new line
                    },
                    {
                        name: "image",
                        type: "image",
                        newline: true
                    }
                ]
            }
        ],
        "facebook.user_post": [
            {
                type: "send_reply",
                description: "Send reply",
                input: [
                    {
                        name: "content",
                        type: "textarea",
                        required: true
                    },
                    {
                        name: "image",
                        type: "image"
                    }
                ]
            }
        ],
        "facebook.post_reply": [
            {
                type: "send_reply",
                description: "Send reply",
                input: [
                    {
                        name: "content",
                        type: "textarea",
                        required: true
                    },
                    {
                        name: "image",
                        type: "image"
                    }
                ]
            }
        ],
        "facebook.review": [
            {
                type: "send_reply",
                description: "Send reply",
                input: [
                    {
                        name: "content",
                        type: "textarea",
                        required: true
                    },
                    {
                        name: "image",
                        type: "image"
                    }
                ]
            }
        ],
        "twitter.mention": [
            {
                type: "send_reply",
                description: "Send reply",
                input: [
                    {
                        name: "content",
                        type: "textarea",
                        required: true
                    },
                    {
                        name: "image",
                        type: "image"
                    }
                ]
            },
            {
                type: "retweet",
                description: "Retweet",
                input: []
            }
        ],
        "twitter.friend_follows_me": [
            {
                type: "send_dm",
                description: "Send a DM",
                input: [
                    {
                        name: "content",
                        type: "textarea",
                        required: true
                    },
                    {
                        name: "image",
                        type: "image"
                    }
                ]
            }
        ],
        "instagram.new_comment": [
            {
                type: "send_reply",
                description: "Send reply",
                input: [
                    {
                        name: "content",
                        type: "textarea",
                        required: true
                    },
                    // {
                    //     name: "image",
                    //     type: "image"
                    // }
                ]
            }
        ],
        // generic actions are included for all events
        generic: [
            {
                type: "send_email",
                description: "Send an email",
                input: [
                    {
                        name: "recipients",
                        type: "email",
                        multiple: true,
                        required: true
                    },
                    {
                        name: "subject",
                        type: "text",
                        required: true,
                        width: 8,
                        newline: true
                    },
                    {
                        name: "body",
                        type: "html",
                        required: true,
                        width: 8,
                        newline: true
                    }
                ]
            },
            {
                type: "new_post",
                description: "Publish a post",
                input: [
                    {
                        name: "accounts",
                        type: "account",
                        required: true,
                        multiple: true
                    },
                    {
                        name: "content",
                        type: "textarea",
                        required: true,
                        width: 6,
                        newline: true
                    },
                    {
                        name: "attach_media",
                        type: "checkbox",
                        newline: true,
                        description: "Attach media if available",
                        media_dropdown: true
                    },
                    {
                        name: "trim_link_from_content",
                        type: "checkbox",
                        newline: true,
                        hide: true,
                        description: "Trim link from post text"
                    },
                    {
                        name: "publish_at",
                        type: "text",
                        newline: true,
                        timestamp: true,
                        width: 6,
                        description: "Publish at",
                        timestampName: "Schedule post"
                    }
                ]
            },
            {
                type: "add_post_to_queue",
                description: "Add post to queue",
                input: [
                    {
                        name: "queue",
                        type: "queue",
                        required: true
                    },
                    {
                        name: "content",
                        type: "textarea",
                        required: true,
                        width: 6,
                        newline: true
                    },
                    {
                        name: "attach_media",
                        type: "checkbox",
                        newline: true,
                        description: "Attach media if available",
                        media_dropdown: true
                    }
                ]
            },
            {
                type: "webhook_request",
                description: "HTTP Request",
                input: [
                    {
                        name: "url",
                        type: "url",
                        required: true
                    },
                    {
                        name: "method",
                        type: "dropdown",
                        options: [
                            //"GET",
                            "POST"
                            //"PUT",
                            //"PATCH",
                            //"DELETE",
                        ],
                        required: true,
                        width: 2
                    },
                    {
                        name: "type",
                        type: "dropdown",
                        options: [
                            "JSON"
                            //"URLENCODED",
                        ],
                        required: true,
                        width: 2
                    },
                    {
                        name: "body",
                        type: "request_body",
                        width: 6,
                        newline: true
                    }
                ],
                exports: {
                    is_successful: {
                        description: "Request is successful",
                        type: [
                            "boolean"
                        ],
                        operator: YES_NO_OPERATORS
                    },
                    response: {
                        description: "HTTP Response",
                        type: [ // possible types
                            "string",
                            "json"
                        ],
                        operator: STRING_OPERATORS,
                        value: {
                            type: "textarea"
                        }
                    }
                }
            }
        ]
    },
    conditions: {
        // specific conditions (array or a fn which returns array)
        webhook: [
            {
                key: "request_body",
                description: "Request body",
                operator: STRING_OPERATORS,
                value: {
                    type: "textarea"
                }
            },
        ],
        incoming_message: [
            {
                key: "content",
                description: "Message Content",
                operator: STRING_OPERATORS,
                value: {
                    type: "textarea"
                }
            },
        ],
        "incoming_message.facebook": [
            {
                key: "has_media",
                description: "Has media",
                operator: YES_NO_OPERATORS
            },
            {
                key: "has_sticker",
                description: "Has sticker",
                operator: YES_NO_OPERATORS
            }
        ],
        "incoming_message.twitter": [
            {
                key: "has_media",
                description: "Has media",
                operator: YES_NO_OPERATORS
            }
        ],
        account_post: [
            {
                key: "content",
                description: "Post Content",
                operator: STRING_OPERATORS,
                value: {
                    type: "textarea"
                }
            },
            {
                key: "has_media",
                description: "Has media",
                operator: YES_NO_OPERATORS
            },
            {
                key: "media_url",
                description: "Media url (if available)",
                operator: STRING_OPERATORS,
                value: {
                    type: "text"
                }
            },
            {
                key: "permalink",
                description: "Post permalink",
                operator: STRING_OPERATORS,
                value: {
                    type: "text"
                }
            }
        ],
        rss_new_item: [
            {
                key: "title",
                description: "Item title",
                operator: STRING_OPERATORS,
                value: {
                    type: "text"
                }
            },
            {
                key: "link",
                description: "Item link",
                operator: STRING_OPERATORS,
                value: {
                    type: "text"
                }
            },
            {
                key: "image",
                description: "Item image",
                operator: STRING_OPERATORS,
                value: {
                    type: "text"
                }
            },
            {
                key: "description",
                description: "Item description",
                operator: STRING_OPERATORS,
                value: {
                    type: "text"
                }
            },
            {
                key: "author",
                description: "Item author",
                operator: STRING_OPERATORS,
                value: {
                    type: "text"
                }
            },
            {
                key: "category",
                description: "Item category",
                operator: ARRAY_OPERATORS,
                value: {
                    type: "text"
                }
            },
            {
                key: "has_image",
                description: "Has image",
                operator: YES_NO_OPERATORS
            }
        ],
        "twitter.*": TWITTER_CONDITIONS,
        "twitter.mention": [
            {
                key: "content",
                description: "Content",
                operator: STRING_OPERATORS,
                value: {
                    type: "textarea"
                }
            },
            {
                key: "has_media",
                description: "Has image",
                operator: YES_NO_OPERATORS
            },
            {
                key: "is_reply",
                description: "Is reply to a tweet",
                operator: YES_NO_OPERATORS
            }
        ],
        "facebook.*": FACEBOOK_CONDITIONS,
        "facebook.user_post": [
            {
                key: "content",
                description: "Content",
                operator: STRING_OPERATORS,
                value: {
                    type: "textarea"
                }
            },
            {
                key: "has_media",
                description: "Has media",
                operator: YES_NO_OPERATORS
            }
        ],
        "facebook.review": [
            {
                key: "content",
                description: "Content",
                operator: STRING_OPERATORS,
                value: {
                    type: "textarea"
                }
            },
            {
                key: "is_positive",
                description: "Is Positive",
                operator: YES_NO_OPERATORS
            }
        ],
        "facebook.post_reply": [
            {
                key: "content",
                description: "Content",
                operator: STRING_OPERATORS,
                value: {
                    type: "textarea"
                }
            },
            {
                key: "has_media",
                description: "Has media",
                operator: YES_NO_OPERATORS
            },
            {
                key: "is_account_post",
                description: "Post is published by account",
                operator: YES_NO_OPERATORS
            },
            {
                key: "post_id",
                description: "Post ID",
                operator: ID_OPERATORS,
                value: {
                    type: "text"
                }
            }
        ],
        "instagram.*": INSTAGRAM_CONDITIONS,
        "instagram.new_comment": [
            {
                key: "content",
                description: "Content",
                operator: STRING_OPERATORS,
                value: {
                    type: "textarea"
                }
            }
        ],
        generic: []
    }
};
