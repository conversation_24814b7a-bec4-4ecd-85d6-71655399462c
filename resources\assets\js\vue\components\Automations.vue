<template>
    <div class="col-12">
        <div class="w-100" v-if="loaded">
            <div id="new_atm" class="modal text-left" role="dialog">
                <div class="modal-dialog">
                    <div class="modal-content rounded-xl">
                        <div class="modal-header p-6">
                            <h5 class="modal-title">New Automation</h5>
                            <button type="button" class="close" data-dismiss="modal"><i class="ph ph-x ph-md"></i></button>
                        </div>
                        <div class="modal-body">
                            <form @submit.prevent="saveAutomation">

                                <div class="form-group">
                                    <input v-model="description" name="description" class="form-control input-lg" title="Description" placeholder="Enter some title to identify this automation" />
                                </div>
                                <div v-if="userTeams.length" class="form-group mb-6" data-tour="id=createautomation_team;text=If you are creating ths Automation for a team, select the team.">
                                    <label>
                                        Assign to a team (optional)
                                    </label>
                                    <select v-model="team_id" class="form-control" name="team_id" title="Choose a team if you want to assign this Automation to a team">
                                        <option value="">---</option>
                                        <option :value="team.id" v-for="team in userTeams" :key="team.id">
                                            {{ team.name }}
                                        </option>
                                    </select>
                                </div>

                                <div class="form-group text-right overflow-hidden pt-2 mb-6">
                                    <button type="button" class="btn btn-light float-left" data-dismiss="modal">Cancel</button>
                                    <button type="submit" class="btn btn-primary float-right">Next</button>
                                </div>
                            </form>
                        </div>
                    </div>

                </div>
            </div>
            <h3 class="d-flex justify-content-between mb-4">
                Automations
                <button class="btn btn-light btn-sm float-right" type="button"
                        data-toggle="modal" data-target="#new_atm">
                    <div class="d-flex align-items-center">
                        <i class="ph ph-plus mr-2" title="New Automation"></i> <span class="d-md-inline-block d-none">New</span> Automation
                    </div>
                </button>
            </h3>
            <div class="card pt-6" v-if="automation.items && automation.items.length === 0">
                <div class="card-body p-md-6 p-0">
                    <div class="text-center">
                        <h4 class="d-md-block d-none font-weight-400">Looks like you have no automation setup yet.</h4>
                        <h5 class="d-md-none mb-4">Looks like you have no automation setup yet.</h5>
                        <p class="d-md-block d-none mb-5 container w-60">
                            You can automate your accounts by setting up automations here. Try creating a new automation. <a href="https://help.socialbu.com/article/29-creating-a-socialbu-automation" data-beacon-article-modal="5e37af222c7d3a7e9ae71f35">Learn more</a>
                        </p>
                        <p class="d-md-none mb-5">
                            You can automate your accounts by setting up automations here. Try creating a new automation. <a href="https://help.socialbu.com/article/29-creating-a-socialbu-automation" data-beacon-article-modal="5e37af222c7d3a7e9ae71f35">Learn more</a>
                        </p>
                        <button class="btn btn-primary btn-sm" type="button" data-toggle="modal" data-target="#new_atm">
                            <div class="d-flex align-items-center">
                                <i class="ph ph-plus mr-2"></i> Create Automation
                            </div>
                        </button>
                    </div>
                </div>
            </div>
            <div v-else>
                <div class="card border rounded-md-xl rounded-lg mb-3" v-for="atm in automation.items" :key="atm.id">
                    <div class="card-body p-md-20 p-4">
                        <div v-html="overlayLoaderHtml" v-if="automation.loading"></div>
                        <div class="d-flex justify-content-between align-items-center mb-md-3 mb-2">
                            <a :href="'/app/automations/'+atm.id+'/edit'" class="w-100 text-secondary">
                                <div class="d-flex justify-content-between">
                                    <h5 class="font-weight-500 mb-0">
                                        {{ atm.description.substring(0,255) }}
                                        <span class="ml-2" v-if="atm.team_id">
                                            <i class="fa fa-users" data-toggle="tooltip" title="Assigned to team  and created by "></i>
                                        </span>
                                    </h5>
                                </div>
                            </a>
                            <div class="status p-1 mr-4" :class="atm.active  ? 'bg-success' : 'bg-danger'"></div>
                            <div class="dropdown show">
                                <button title="Select actions for the selected posts" class="btn btn-xs post-action p-2" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <div class="d-flex align-items-center">
                                        <i class="ph ph-dots-three ph-md ph-bold"></i>
                                    </div>
                                </button>
                                <div class="dropdown-menu dropdown-menu-right">
                                    <a class="dropdown-item cursor-pointer" @click.prevent="duplicateAutomation(atm.id)">
                                        <i class="ph ph-copy ph-lg mr-2"></i> Duplicate
                                    </a>
                                    <a class="dropdown-item cursor-pointer" @click.prevent="deleteAutomation(atm.id)">
                                        <i class="ph ph-trash ph-lg mr-2"></i> Delete
                                    </a>
                                </div>
                            </div>
                        </div>
                        <p class="text-muted mb-3">
                            Trigger: {{ atm.event_name}}
                        </p>
                        <p class="text-muted mb-md-0 mb-1">
                            {{ atm.executed_at ? 'Last executed ' + $momentUTC(atm.executed_at).format('MMM DD, YYYY - h:mm a') : 'Never executed' }}
                        </p>
                    </div>
                </div>
                <div class="mt-2" v-if="automation.lastPage && automation.lastPage > 1 && !automation.loading">
                    <!-- pagination -->
                    <ul class="pagination justify-content-end">
                        <li class="page-item" :class="{active: automation.currentPage === n}" v-for="n in $pagination(automation.currentPage, automation.lastPage)">
                            <a class="page-link" v-if="n==='...'">{{ n }}</a>
                            <a class="page-link" href="#" @click.prevent="navigateToPage(n)" v-else>{{ n }}</a>
                        </li>
                    </ul>                                
                </div>
            </div>
        </div>
        <div class="card border-0" v-else>
            <div class="card-body text-center" v-html="spinnerHtml"></div>
        </div>
    </div>
</template>
<script>
import $ from 'jquery';
import {  axios, axiosErrorHandler,spinnerHtml,overlayLoaderHtml, alertify } from "../../components";
import { getUserTeams } from "../../data";
export default {
    name: "Automations",
    data() {
        return {
            loaded: false, // initial load
            description: '',
            team_id:null,
            activeAutomation:[], //to highlight newly added automation
            automation:{
                items: [], // holds all automations
                currentPage: null,
                lastPage: null,
                nextPage: null,
                loading:true
            },
            userTeams: [],
            saving: false, // saving
            type:null,
        };
    },
    watch:{
        activeAutomation(Ids) {
            if (Ids.length) {
                this.$nextTick(() => {
                    // let offset = $("#list-item-" + Ids).offset();
                    let offset = $("#list-item-" + Ids[0]).offset();
                    if (offset)
                        $("html, body").animate(
                            {
                                scrollTop: offset.top - 100
                            },
                            300
                        );
                    setTimeout(() => {
                        this.activeAutomation = []; // reset active post
                    }, 1000);
                });
            }
        },
    },
    computed:{
        spinnerHtml: () => spinnerHtml,
        overlayLoaderHtml: () => overlayLoaderHtml,
    },
    methods: {
        initialize() {
            this.navigateToPage(1);
            this.getUserTeams();
        },
        async getUserTeams(){
            try{
                const res = await getUserTeams('all');
                this.userTeams = res;
            }catch(e){
                axiosErrorHandler(e);
            }
        },
        async navigateToPage(page, cb){
            if(!page) page = 1;
                this.automation.loading = true;
                try {
                    const res = await axios.get("/app/automations/list?page=" + page);
                    this.automation.items = res.data.data;
                    this.automation.currentPage = res.data.currentPage;
                    this.automation.lastPage = res.data.lastPage;
                    this.loaded = true
                    cb && cb();
                } catch (e) {
                    axiosErrorHandler(e);
                }
            this.automation.loading = false;
        },
        async saveAutomation(){
            try{
                this.saving = true;
                const formData = new FormData();
                formData.append('description',this.description);
                if(this.team_id){
                    formData.append('team_id',this.team_id);
                }
                const res = await axios.post("/app/automations",formData); 
                if(res.data && res.data.id){
                    window.location.href = `/app/automations/${res.data.id}/edit`;
                }
            }catch(e){
                axiosErrorHandler(e);
            }
            this.saving = false;
        },
        async duplicateAutomation(id){
            this.automation.loading = true;
            try{
                const res = await axios.patch("/app/automations/"+id+"/duplicate");
                if(res.data){
                    this.activeAutomation.push(res.data.id);
                }
                this.navigateToPage(this.automation.currentPage);
            }catch(e){
                axiosErrorHandler(e);
            }
            this.automation.loading = false;
        },
        async deleteAutomation(id){
            alertify.delete("Are you sure you want to delete this automation?", async () => {
                this.automation.loading = true;
                try{
                    await axios.delete("/app/automations/"+id);
                    this.navigateToPage(this.automation.currentPage);
                }catch(e){
                    axiosErrorHandler(e);
                }
                this.automation.loading = false;
            });
        }
    },
}
</script>
<style scoped>
.status{
    width: 12px;
    height: 12px;
    border-radius: 50%;
}
.post-action:hover{
    background: #F2F4F7;
    border-radius: 8px;
}
</style>
