
<div id="add_mastodon_modal" class="modal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="ph ph-mastodon-logo social-icon active mastodon fa-lg"></i> Add Mastodon Account
                </h5>
                <button type="button" class="close-button" data-dismiss="modal" aria-hidden="true"><i class="ph ph-x"></i></button>
            </div>
            <!-- form with one input for domain -->
            <form method="get" action="{{ route('accounts.auth', ['provider' => 'mastodon']) }}">
                <div class="modal-body">
                    <div class="form-row">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="mastodon_domain">Mastodon domain</label>
                                <div class="input-group">
                                    <input type="text" name="domain" class="form-control" id="mastodon_domain" aria-describedby="mastodonHelp" placeholder="Enter Mastodon domain" required />
                                    <div class="input-group-append">
                                    <span class="input-group-text">
                                        <!-- dropdown -->
                                        <div class="dropdown">
                                            <button type="button" class="btn btn-sm btn-link text-muted" data-toggle="dropdown">
                                                <i class="fa fa-angle-down"></i>
                                            </button>
                                            <div class="dropdown-menu">
                                                <h6 class="dropdown-header">Example Mastodon domains</h6>
                                                <a class="dropdown-item set_mastodon_domain" href="#" data-value="mastodon.social">mastodon.social</a>
                                                <a class="dropdown-item set_mastodon_domain" href="#" data-value="mastodon.world">mastodon.world</a>
                                                <a class="dropdown-item set_mastodon_domain" href="#" data-value="mstdn.social">mstdn.social</a>
                                                <a class="dropdown-item set_mastodon_domain" href="#" data-value="mastodon.art">mastodon.art</a>
                                            </div>
                                        </div>
                                    </span>
                                    </div>
                                </div>
                                <small id="mastodonHelp" class="form-text text-muted">
                                    For example, if your Mastodon account is <pre>@<EMAIL></pre>, then enter <strong>mastodon.social</strong> in the field above.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer text-right">
                    <button type="submit" class="btn btn-primary">Continue</button>
                </div>
            </form>
        </div>
    </div>
</div>

<div id="add_youtube_modal" class="modal" tabindex="-1">
    <div class="modal-dialog modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header border-bottom">
                <h5 class="modal-title">
                    <i class="fa fa-youtube-play social-icon active youtube fa-lg"></i> Add YouTube Channel
                </h5>
                <button type="button" class="close-button" data-dismiss="modal" aria-hidden="true"><i class="ph ph-x"></i></button>
            </div>
            <div class="modal-body p-4">
                <div class="row">
                    <div class="col">
                        <p class="lead small mb-4">
                            You will be redirected to Google to authorize {{ config('app.name') }} to access your YouTube account.
                        </p>
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <p class="alert alert-primary mb-4">
                            {{ config('app.name') }}'s use and transfer to any other app of information received from Google APIs will adhere to <a target="_blank" href="https://developers.google.com/terms/api-services-user-data-policy#additional_requirements_for_specific_api_scopes" class="alert-link">Google API Services User Data Policy</a>, including the Limited Use requirements.
                        </p>
                        <div class="text-center">
                            <a href="{{ route('accounts.auth', ['provider' => 'google', 'service' => 'youtube']) }}" class="btn btn-primary hide_modal_onclick ">
                                    <span class="d-flex align-items-center">
                                        <i class="fa fa-youtube-play fa-2x mr-2"></i> Continue
                                    </span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="add_bluesky_modal" class="modal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="si si-bluesky social-icon active bluesky fa-lg"></i> Add Bluesky Account
                </h5>
                <button type="button" class="close-button" data-dismiss="modal" aria-hidden="true"><i class="ph ph-x"></i></button>
            </div>
            <form method="post" action="{{ route('accounts.add_from_req', ['provider' => 'bluesky']) }}">
                {{ csrf_field() }}
                <div class="modal-body">
                    <div class="form-row">
                        <div class="col-12">
                            <div class="form-group">
                                <label for="bsky_handle">Bluesky Handle</label>
                                <input type="text" name="handle" class="form-control" id="bsky_handle" aria-describedby="bskyHelp" placeholder="you.bsky.social" required />
                                <small id="bskyHelp" class="form-text text-muted"></small>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group">
                                <label for="bsky_pass">Bluesky App Password</label>
                                <input type="password" name="password" class="form-control" id="bsky_pass" aria-describedby="bskyPassHelp" required />
                                <small id="bskyPassHelp" class="form-text text-muted">
                                    Generate an App Password via <b>Settings > Privacy and security > App Passwords</b> in your Bluesky account.
                                </small>
                            </div>
                        </div>
                        <div class="col-12 collapse" id="bsky_service_input">
                            <div class="form-group">
                                <label for="bsky_service">Bluesky Service</label>
                                <input type="text" name="service" class="form-control" value="bsky.social" id="bsky_service" aria-describedby="bskyServiceHelp" required />
                                <small id="bskyServiceHelp" class="form-text text-muted">
                                    The service domain of your Bluesky account. Leave as <b>bsky.social</b> if you are unsure what it is.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer text-right">
                    <!-- toggle btn for service input -->
                    <button type="button" class="btn btn-link btn-sm" data-toggle="collapse" href="#bsky_service_input" role="button" aria-expanded="false" aria-controls="bsky_service_input">
                        Advanced
                    </button>
                    <button type="submit" class="btn btn-primary">Continue</button>
                </div>
            </form>
        </div>
    </div>
</div>

@if(!empty($auth_facebook))
    <div id="fb_auth_facebook_accounts" class="modal fade" data-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">@lang('accounts.select')</h5>
                    <button type="button" class="close-button" data-dismiss="modal" aria-hidden="true"><i class="ph ph-x"></i></button>
                </div>
                <div class="modal-body">
                    <form action="{{ route('accounts.add_from_req', ['provider' => 'facebook']) }}" method="POST">
                        {{ csrf_field() }}
                        <div class="form-group d-flex justify-content-end">
                            <select class="selectpicker col-10 form-control" name="account_ids[]" multiple title="Select account" data-actions-box="true" data-live-search="true">
                                <optgroup label="Facebook Pages">
                                    @foreach($auth_facebook as $acc)
                                        @if($acc['_type'] == 'page')
                                            <option value="{{ $acc['id'] }}">{{ $acc['name'] }}</option>
                                        @endif
                                    @endforeach
                                </optgroup>
                                <optgroup label="Group">
                                    @foreach($auth_facebook as $acc)
                                        @if($acc['_type'] == 'group')
                                            <option value="{{ $acc['id'] }}">{{ $acc['name'] }}</option>
                                        @endif
                                    @endforeach
                                </optgroup>
                                <optgroup label="Instagram Accounts">
                                    @foreach($auth_facebook as $acc)
                                        @if($acc['_type'] == 'instagram')
                                            <option value="{{ $acc['id'] }}">{{ '@' . (isset($acc['username']) ? $acc['username'] : $acc['id']) }}</option>
                                        @endif
                                    @endforeach
                                </optgroup>
                            </select>
                            <button type="submit" class="btn btn-primary">@lang('generic.add')</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <form style="display:none" id="cancel_acc_add_form" method="POST"
          action="{{ route('accounts.add_from_req', ['provider' => 'facebook']) }}">
        {{ csrf_field() }}
        <input type="hidden" name="cancel" value="1"/>
    </form>

@elseif(!empty($auth_linkedin))

    <div id="linkedin_auth_accounts" class="modal fade" data-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">@lang('accounts.select')</h5>
                    <button type="button" class="close-button" data-dismiss="modal" aria-hidden="true"><i class="ph ph-x"></i></button>
                </div>
                <div class="modal-body">
                    <form action="{{ route('accounts.add_from_req', ['provider' => 'linkedin']) }}" method="POST">
                        {{ csrf_field() }}
                        <div class="form-group d-flex justify-content-end">
                            <select class="selectpicker col-10 form-control" name="account_ids[]" multiple title="Select account" data-actions-box="true" data-live-search="true">
                                <optgroup label="@lang('generic.profile')">
                                    @foreach($auth_linkedin as $acc)
                                        @if($acc['_type'] == 'profile')
                                            <option value="{{ $acc['id'] }}">{{ $acc['name'] }}</option>
                                        @endif
                                    @endforeach
                                </optgroup>
                                <optgroup label="Organization">
                                    @foreach($auth_linkedin as $acc)
                                        @if($acc['_type'] == 'org')
                                            <option value="{{ $acc['id'] }}">{{ $acc['name'] }}</option>
                                        @endif
                                    @endforeach
                                </optgroup>
                                <optgroup label="Brand">
                                    @foreach($auth_linkedin as $acc)
                                        @if($acc['_type'] == 'brand')
                                            <option value="{{ $acc['id'] }}">{{ $acc['name'] }}</option>
                                        @endif
                                    @endforeach
                                </optgroup>
                            </select>
                            <button type="submit" class="btn btn-primary">@lang('generic.add')</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <form style="display:none" id="cancel_acc_add_form" method="POST"
          action="{{ route('accounts.add_from_req', ['provider' => 'linkedin']) }}">
        {{ csrf_field() }}
        <input type="hidden" name="cancel" value="1"/>
    </form>

@elseif(!empty($auth_google))

    <div id="google_auth_accounts" class="modal fade" data-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">@lang('accounts.select')</h5>
                    <button type="button" class="close-button" data-dismiss="modal" aria-hidden="true"><i class="ph ph-x"></i></button>
                </div>
                <div class="modal-body">
                    <form action="{{ route('accounts.add_from_req', ['provider' => 'google']) }}" method="POST">
                        {{ csrf_field() }}
                        <div class="form-group d-flex justify-content-end">
                            <select class="selectpicker col-10 form-control" name="account_ids[]" multiple title="Select accounts" data-actions-box="true" data-live-search="true">
                                @if(isset($auth_google['gmb']))
                                    @foreach($auth_google['gmb'] as $accIndex => $acc)
                                        <optgroup label="GMB: {{ $acc['account_name'] }}">
                                            @if(!empty($acc['locations']))
                                                @foreach($acc['locations'] as $locIndex => $location)
                                                    <option value="gmb.{{ $accIndex . '.locations.' . $locIndex}}">
                                                        {{ $location['name'] }}
                                                    </option>
                                                @endforeach
                                            @endif
                                        </optgroup>
                                    @endforeach
                                @endif
                                @if(isset($auth_google['youtube']))
                                    <optgroup label="YouTube">
                                        @foreach($auth_google['youtube'] as $channel)
                                            <option value="youtube.{{ $channel['id'] }}">{{ $channel['name'] }}</option>
                                        @endforeach
                                    </optgroup>
                                @endif
                            </select>
                            <button type="submit" class="btn btn-primary">@lang('generic.add')</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <form style="display:none" id="cancel_acc_add_form" method="POST"
          action="{{ route('accounts.add_from_req', ['provider' => 'google']) }}">
        {{ csrf_field() }}
        <input type="hidden" name="cancel" value="1"/>
    </form>

@elseif(!empty($auth_reddit))

    <div id="reddit_auth_accounts" class="modal fade" data-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">@lang('accounts.select')</h5>
                    <button type="button" class="close-button" data-dismiss="modal" aria-hidden="true"><i class="ph ph-x "></i></button>
                </div>
                <div class="modal-body">
                    <form action="{{ route('accounts.add_from_req', ['provider' => 'reddit']) }}" method="POST">
                        {{ csrf_field() }}
                        <div class="form-group d-flex justify-content-end">
                            <select class="selectpicker col-10 form-control" name="account_ids[]" multiple title="Select accounts" data-actions-box="true" data-live-search="true">
                                @foreach($auth_reddit as $id => $acc)
                                    <option value="{{ $id }}">{{ $acc['name'] }}</option>
                                @endforeach
                            </select>
                            <button type="submit" class="btn btn-primary">@lang('generic.add')</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <form style="display:none" id="cancel_acc_add_form" method="POST"
          action="{{ route('accounts.add_from_req', ['provider' => 'reddit']) }}">
        {{ csrf_field() }}
        <input type="hidden" name="cancel" value="1"/>
    </form>

@endif

@push('footer_html')
    <script>

        // redirect to return_url if needed
        @if(session()->get('accounts.connect.return_url'))
        document.location.replace('{{ session()->get('accounts.connect.return_url') }}');
        @endif

        // postMessage if needed
        @if(session()->get('accounts.connect.send_window_message'))
        if(window.opener) {
            window.opener.postMessage('account_connected', '*');
        }
        @endif

        __loadScript("accounts", function (s) {
            @php
                if(isset($open_add_account_modal) && $open_add_account_modal){
                    echo 's.showAddModal();';
                }
                if(isset($open_add_mastodon_modal) && $open_add_mastodon_modal){
                    echo 's.showAddMastodonModal();';
                }
                if(isset($open_add_bluesky_modal) && $open_add_bluesky_modal){
                    echo 's.showAddBlueskyModal();';
                }
                if($auth_facebook){
                    echo 's.onAuthFacebook();';
                } else if($auth_linkedin){
                    echo 's.onAuthLinkedin();';
                } else if($auth_google){
                    echo 's.onAuthGoogle();';
                } else if($auth_reddit){
                    echo 's.onAuthReddit();';
                }
            @endphp
        });
    </script>
@endpush
