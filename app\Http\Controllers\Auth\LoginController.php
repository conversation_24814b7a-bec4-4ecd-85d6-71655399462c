<?php

namespace App\Http\Controllers\Auth;

use App\User;
use Auth;
use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = '/app';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout', 'confirmEmail', 'appLogin');
    }

    /**
     * Validate the user login request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return void
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    protected function validateLogin(Request $request)
    {
        $request->validate([
            $this->username() => 'required|string|max:1000',
            'password' => 'required|string|max:1000',
        ]);
    }

    /**
     * The user has been authenticated.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  mixed $user
     * @return mixed
     */
    protected function authenticated(Request $request, $user)
    {
        if($request->input('extension') === 'true'){
            // return msg
            return 'You have been logged in. You can safely close this window now. <script>window.close()</script>';
        }
    }

    public function showLoginForm(Request $request){
        return view('guest.login', [
            'extension' => $request->input('extension') === 'true',
        ]);
    }

    public function confirmEmail($token = null){
        if($token == null) abort(404);
        /** @var User $user */
        $user = User::where('token', $token)->firstOrFail();
        $user->confirmEmail();
        if(!\Auth::check()){
            \Auth::login($user);
        }
        flash(trans('settings.email_verified_msg'));
        return redirect()->route('settings');
    }

    /**
     * @param Request $request
     * @return mixed|void
     * @throws \Illuminate\Validation\ValidationException
     * @throws \Exception
     */
    public function getToken(Request $request)
    {
        $this->validate($request, [
            'email' => 'string|required|email|max:1000',
            'password' => 'string|required|max:1000',
        ]);

        $credentials = $request->only('email', 'password');

        if (\Auth::once($credentials)) {
            $user = user();

            try {
                $token = $user->getApiToken(true);
            } catch (\Exception $e) {
                abort(400, $e->getMessage());
                return;
            }

            return response()->json([
                'authToken' => $token,
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'verified' => (bool) $user->verified,
            ]);
        } else {
            abort(422, 'Email or password not correct.');
        }

    }

    /**
     * @param Request $request
     * @return mixed|void
     * @throws \Illuminate\Validation\ValidationException
     */
    public function appLogin(Request $request)
    {

        $this->validate($request, [
            'email' => 'string|required|email|max:1000',
            'password' => 'string|required|max:1000',
        ]);

        $credentials = $request->only('email', 'password');

        if (\Auth::once($credentials)) {
            /** @var User $user */
            $user = Auth::user();

            if(!$user->verified){
                abort(400, 'Please complete your email address verification first.');
            }

            $token = $user->createToken('Mobile App')->accessToken;

            return response()->json([
                'authToken' => $token,
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'verified' => $user->verified,
            ]);
        } else {
            abort(422, 'Email or password not correct.');
        }
    }

    /**
     * Quick login link
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector|null
     * @throws ValidationException
     */
    public function quickLogin(Request $request)
    {
        // check if the key exists
        $this->validate($request, [
            'k' => 'string|required|max:1000',
        ]);

        $key = $request->input('k');

        // decrypt
        try {
            $decrypted = decrypt($key);

            $exploded = explode('::', $decrypted);

            if(count($exploded) != 2){
                abort(400, 'Invalid key.');
            }

            $email = $exploded[0];
            $password = $exploded[1];

            $credentials = [
                'email' => $email,
                'password' => $password,
            ];

            if (\Auth::once($credentials)) {
                /** @var User $user */
                $user = Auth::user();

                if(!$user->verified){
                    abort(400, 'Please complete your email address verification first.');
                }

                // now, we need to create session and redirect to home
                Auth::login($user);

                // redirect to home
                return redirect('/app');
            } else {
                abort(422, 'Email or password not correct.');
            }

        } catch (\Exception $e) {
            abort(400, 'Invalid key.');
        }

        return null;
    }
}
