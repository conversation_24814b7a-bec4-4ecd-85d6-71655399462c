<?php

namespace App;

use <PERSON>\TwitterOAuth\TwitterOAuth;
use App\Helpers\Instagram;
use App\Traits\HasOptions;
use Facebook\Facebook;
use Illuminate\Database\Eloquent\Model;

/**
 * App\FeedPost
 *
 * @property int $id
 * @property int $feed_id
 * @property string|null $external_id
 * @property string $keywords
 * @property string $type
 * @property int|null $parent_id
 * @property int|null $root_id
 * @property int $read
 * @property object $data
 * @property int|null $account_id
 * @property int|null $user_id
 * @property array|null $options
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Account|null $account
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\FeedPost[] $children
 * @property-read int|null $children_count
 * @property-read \App\Feed $feed
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\FeedPost[] $nested
 * @property-read int|null $nested_count
 * @property-read \App\FeedPost|null $parent
 * @property-read \App\FeedPost|null $root
 * @method static \Illuminate\Database\Eloquent\Builder|\App\FeedPost newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\FeedPost newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\FeedPost query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\FeedPost whereAccountId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\FeedPost whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\FeedPost whereData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\FeedPost whereExternalId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\FeedPost whereFeedId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\FeedPost whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\FeedPost whereKeywords($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\FeedPost whereOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\FeedPost whereParentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\FeedPost whereRead($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\FeedPost whereRootId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\FeedPost whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\FeedPost whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\FeedPost whereUserId($value)
 * @mixin \Eloquent
 */
class FeedPost extends Model
{
    use HasOptions;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'feed_id', 'external_id', 'account_id', 'parent_id', 'keywords', 'type',
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'options' => 'array',
        'data' => 'object',
    ];

    /**
     * Get the account associated with this object.
     */
    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    /**
     * Get the feed associated with this object.
     */
    public function feed()
    {
        return $this->belongsTo(Feed::class);
    }

    /**
     * Get the  parent feed post.
     */
    public function parent()
    {
        return $this->belongsTo(self::class);
    }

    /**
     * Get the root feed post.
     */
    public function root()
    {
        return $this->belongsTo(self::class);
    }

    /**
     * Get child feedposts
     */
    public function children(){
        return $this->hasMany(FeedPost::class, 'parent_id');
    }


    /**
     * Get all nested posts
     */
    public function nested(){
        return $this->hasMany(FeedPost::class, 'root_id');
    }

    /**
     * Reply to the feedPost - not used currently
     */
    public function reply($text){
        $this->feed->reply($text, $this->id);
    }

    /**
     * Like post
     * @throws \Exception
     */
    public function like(){

        $feedPost = $this;

        /** @var Account $account */
        $account = $feedPost->account;

        if($account->type === 'twitter.profile' && $feedPost->type === 'tweet'){
            /** @var TwitterOAuth $tw */
            $tw = $account->getApi();
            $tweetId = $feedPost->external_id;
            try {
                $tw->post('favorites/' . ($feedPost->getOption('liked') ? 'destroy' : 'create'), [
                    'id' => $tweetId,
                ]);
                // toggle the like value locally
                $feedPost->getOption('liked') ? $feedPost->removeOption('liked') : $feedPost->setOption('liked', true);
            } catch (\Exception $e){
                throw new \Exception('Unable to complete the action');
            }
            if($tw->getLastHttpCode() != 200){
                throw new \Exception('Unable to complete the action: Error ' . $tw->getLastHttpCode());
            }
        } else if($account->type === 'facebook.page' && ($feedPost->type === 'post'||$feedPost->type === 'comment')) {
            /** @var Facebook $fb */
            $fb = $account->getApi();
            if ($feedPost->getOption('reaction')) {
                try {
                    $fb->delete('/' . $feedPost->external_id . '/likes');
                    $feedPost->removeOption('reaction');
                } catch (\Exception $e) {
                    throw new \Exception('Unable to complete the action: ' . $e->getMessage());
                }
            } else {
                try {
                    $fb->post('/' . $feedPost->external_id . '/likes');
                    $feedPost->setOption('reaction', 'like');
                } catch (\Exception $e) {
                    throw new \Exception('Unable to complete the action: ' . $e->getMessage());
                }
            }
        } else if($account->type === 'instagram.direct' && ($feedPost->type === 'post'||$feedPost->type === 'comment')){

            throw new \Exception('Not supported.');

            /** @var Instagram $ig */
            $ig = $account->getApi();
            if($feedPost->getOption('liked')){
                try {
                    if($feedPost->type === 'post')
                        $ig->media->unlike($feedPost->external_id);
                    else if($feedPost->type === 'comment')
                        $ig->media->unlikeComment($feedPost->external_id, 1);

                    $feedPost->removeOption('liked');
                } catch (\Exception $e){
                    throw new \Exception('Unable to complete the action: ' . $e->getMessage());
                }
            } else {
                try {
                    if($feedPost->type === 'post')
                        $ig->media->like($feedPost->external_id, 1);
                    else if($feedPost->type === 'comment')
                        $ig->media->likeComment($feedPost->external_id, 1);

                    $feedPost->setOption('liked', true);
                } catch (\Exception $e){
                    throw new \Exception('Unable to complete the action: ' . $e->getMessage());
                }
            }
        } else {
            throw new \Exception('Invalid action');
        }
    }

    /**
     * Mark as read/unread
     * @param bool $read
     */
    public function markRead($read = true){
        $this->read = $read !== false;
        $this->save();
    }

    /**
     * Get attachments
     */
    public function getAttachments(){
        $mimes = new \Mimey\MimeTypes;
        if(!isset($this->data->attachments))
            $this->data->attachments = [];
        return collect($this->data->attachments)->map(function ($att) use($mimes){
            // set url
            $att->url = app()->environment('production') ? \Storage::temporaryUrl($att->path, now()->addDays(1)) : \Storage::url($att->path);
            $att->mime = $mimes->getMimeType($att->type);
            if(!$att->mime) $att->mime = \Storage::getMimetype($att->path);
            return $att;
        });
    }

    private function getOptions(){
        return (array) $this->options;
    }

    private function setOptions($options)
    {
        $this->options = $options;
        $this->save();
        return $this;
    }

}
