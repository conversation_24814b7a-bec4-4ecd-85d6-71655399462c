<template>
    <div class="post p-3">
        <small class="post-author">
            Posted by u/username 1 min ago
        </small>
        <div class=" mt-2 mb-4">
            <h3 class="post-title">
                {{ title }}
            </h3>
            <div>
                <span class="small badge text-danger border-danger border" v-if="options.is_nsfw">
                    nsfw
                </span>
                    <span class="small badge text-muted border-secondary border" v-if="options.is_spoiler">
                    spoiler
                </span>
            </div>
        </div>
        <div class="mb-4"
             v-if="attachments.length">
            <div class="mt-4" v-for="attachment in attachments">
                <img alt="media" :src="attachment.url" class="img-fluid" />
            </div>
        </div>
        <div class="post-content" v-html="content"></div>
        <div class="post-footer mt-2">
            <div class="post-actions">
                <div class="mt-2">
                    <i class="ph ph-arrow-fat-up ph-lg"></i>
                    <span class="mx-2">141</span>
                    <i class="ph ph-arrow-fat-down ph-lg mr-4"></i>
                    <i class="ph ph-chat-centered  ph-lg mr-4"></i>
                    <i class="ph ph-medal ph-lg mr-4"></i>
                    <i class="ph ph-share-fat ph-lg"></i>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { marked } from 'marked';
import DOMPurify from "dompurify";
import { truncate } from "lodash";

export default {
    name: "RedditPost",
    props: ["account", "text", "attachments", "link", "options"],
    components: {
    },
    computed: {
        title(){
            let title = this.options.title;
            if(!title){
                title = truncate(this.text, { length: 300});
            }
            return title;
        },
        content() {

            // parse markdown
            let html = marked.parse(this.text);

            // sanitize html
            html = DOMPurify.sanitize(html, {});

            return html;
        }
    }
};
</script>
<style lang="scss" scoped>
.post-title {
    color: #252A32;
    font-size: 16px;
    font-weight: 500;
}
.post-author {
    color: #787C7E;
}
.post-content *, .post-content {
    font-size: 16px !important;
    font-weight: 400;
}
.post-actions .action {
    color: #878A8C;
    cursor: pointer;
    background: transparent;
    &:hover {
        background: rgba(26,26,27,0.1);
    }
}
</style>
