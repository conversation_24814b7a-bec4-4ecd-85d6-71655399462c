<?php

namespace App\Notifications;

use App\Post;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class PostApprovalRejected extends Notification implements ShouldQueue
{
    use Queueable;

    /** @var User|null $team */
    private $approver = null;
    /** @var Post|null $team */
    private $post = null;

    /**
     * Create a new notification instance.
     *
     * @param Post $post
     * @param User $approver
     */
    public function __construct(Post $post, User $approver)
    {
        $this->post = $post;
        $this->approver = $approver;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->replyTo($this->approver->email, $this->approver->name) // some users may reply to the email
            ->subject('Post Not Approved')
            ->greeting('Hey ' . explode(' ', ($notifiable->name . ''))[0])
            ->line(e($this->approver->name) . ' rejected your post for the following reason.')
            ->line('<blockquote style="border-left: 5px solid #F2F4F6;padding: 20px;">' . e($this->post->getOption('reject_reason')) . '</blockquote>')
            ->action('Review Post', url('app/publish/drafts'))
            ->line('You can find the post in your drafts.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'approver_id' => $this->approver->id,
            'approver_name' => $this->approver->name,
            'reason' => $this->post->getOption('reject_reason'),
            '_level' => 'warning',
        ];
    }
}
