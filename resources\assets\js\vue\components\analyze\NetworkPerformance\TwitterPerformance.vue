<template>
    <div class="row">
        <div class="col-12 mb-8">
            <h5 class="mb-4">Followers Growth</h5>
            <div class="card border" v-if="isLoading('account_metrics')" v-html="spinnerHtml"></div>
            <div class="row" v-else>
                <div class="col-12 col-md-6">
                    <div class="analyze-card card border">
                        <div class="card-body p-20">
                            <div>
                                <h6 class="font-weight-500 mb-6">New Followers</h6>
                                <h3 class="mb-0">{{ totalFollowers }}</h3>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 mt-4">
                    <div class="card border pt-4 d-flex justify-content-center">
                        <Chart
                            :options="growthChart"
                            ref="chart_followers"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12">
            <h5 class="mb-4">Content at a Glance</h5>
            <div class="row">
                <div class="col-md-12">
                    <div class="card border">
                        <div class="card-body card-body py-3 px-20">
                            <ul class="d-flex justify-content-md-center justify-content-start flex-nowrap nav border-md-bottom-0 border-bottom-1 navbar-reports overflow-auto hide-scrollbar" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link cursor-pointer"
                                       @click="publishingBehaviorTab = 'overview'"
                                       :class="{'active': publishingBehaviorTab === 'overview'}">Overview</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link cursor-pointer"
                                       @click="publishingBehaviorTab = 'text_posts'"
                                       :class="{'active': publishingBehaviorTab === 'text_posts'}">Text Posts</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link cursor-pointer"
                                       @click="publishingBehaviorTab = 'image_posts'"
                                       :class="{'active': publishingBehaviorTab === 'image_posts'}">Image Posts</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link cursor-pointer"
                                       @click="publishingBehaviorTab = 'video_posts'"
                                       :class="{'active': publishingBehaviorTab === 'video_posts'}">Video Posts</a>
                                </li>
                            </ul>
                            <div class="tab-content pt-5 px-4 pb-4">
                                <div class="tab-pane active show">
                                    <template v-if="publishingBehaviorTab === 'overview'">
                                        <div v-if="isLoading('post_types_chart')" v-html="spinnerHtml"></div>
                                        <Chart
                                            key="pie_chart1"
                                            ref="chart_post_types"
                                            :options="postTypesChart"
                                            v-else
                                        />
                                    </template>
                                    <template v-else-if="publishingBehaviorTab === 'text_posts'">
                                        <div
                                            v-if="isLoading('metrics_text') || isLoading('counts_text')"
                                            v-html="spinnerHtml"></div>
                                        <Chart
                                            ref="chart_text_posts"
                                            :options="textPostsAndEngagementsChart"
                                            v-else
                                        />
                                    </template>
                                    <template v-else-if="publishingBehaviorTab === 'image_posts'">
                                        <div
                                            v-if="isLoading('metrics_image') || isLoading('counts_image')"
                                            v-html="spinnerHtml"></div>
                                        <Chart
                                            ref="chart_image_posts"
                                            :options="imagePostsAndEngagementsChart"
                                            v-else
                                        />
                                    </template>
                                    <template v-else-if="publishingBehaviorTab === 'video_posts'">
                                        <div
                                            v-if="isLoading('metrics_video') || isLoading('counts_video')"
                                            v-html="spinnerHtml"></div>
                                        <Chart
                                            ref="chart_video_posts"
                                            :options="videoPostsAndEngagementsChart"
                                            v-else
                                        />
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { axios, axiosErrorHandler, spinnerHtml } from '../../../../components'
import { Chart } from "highcharts-vue";

export default {
    name: 'TwitterPerformance',
    components: {
        Chart
    },
    props: ['filter', 'accounts', 'filterFormOpen'],
    data() {
        return {
            loadingFlags: [],

            accountMetrics: [],

            postMetrics: {
                image: [],
                video: [],
                text: []
            },
            postCounts: {
                image: [],
                video: [],
                text: []
            },

            publishingBehaviorTab: 'overview',
        }
    },
    computed: {
        spinnerHtml: () => spinnerHtml,
        totalFollowers() {
            let followers = 0;
            this.accountMetrics.forEach((a) => {
                const { metrics } = a;
                (metrics.followers || []).forEach((d) => {
                    followers += d.value;
                });
            });
            return followers;
        },

        // chart configs
        growthChart() {
            const followersData = this.accountMetrics.map((item) => {
                const { metrics } = item;
                const acc = this.getAccount(item.account_id);
                return {
                    name: acc.name + " (" + acc._type + ")",
                    marker: {
                        enabled: false
                    },
                    data: (metrics.followers || []).map(a => [this.timestampForXAxis(a.date), a.value]),
                };
            });

            return {
                chart: {
                    type: "areaspline",
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: "",
                },
                xAxis: {
                    type: "datetime",
                },
                yAxis: {
                    title: {
                        text: "Followers",
                    },
                },
                plotOptions: {
                    series: {
                        fillOpacity: 0.7
                    },
                    area: {
                        marker: {
                            enabled: false, // Simplified markers for area charts
                            symbol: 'circle',
                            radius: 2,
                            states: {
                                hover: {
                                    enabled: true
                                }
                            }
                        }
                    }
                },
                series: followersData,
            };
        },
        textPostsAndEngagementsChart() {
            const newDataByDate = {};
            Object.values(this.postMetrics.text).forEach((item) => {
                item.forEach(d => {
                    newDataByDate[d.date] = newDataByDate[d.date] || 0;
                    newDataByDate[d.date] += d.value;
                });
            });
            const engagementsData = Object.keys(newDataByDate).map((key) => {
                return [
                    this.timestampForXAxis(key),
                    newDataByDate[key]
                ];
            });
            const postsPublished = this.postCounts.text.map(itm => {
                return [this.timestampForXAxis(itm.date), itm.count];
            });

            return {
                chart: {
                    type: "areaspline",
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: "",
                },
                xAxis: {
                    type: "datetime",
                },
                yAxis: [
                    { // Primary yAxis
                        title: {
                            text: "Engagements",
                        },
                        labels: {
                            format: '{value}',
                        }
                    },
                    { //secondary yAxis
                        title: {
                            text: "Posts Published",
                        },
                        labels: {
                            format: '{value}',
                        },
                        opposite: true
                    }
                ],
                plotOptions: {
                    series: {
                        fillOpacity: 0.7
                    },
                    area: {
                        marker: {
                            enabled: false, // Simplified markers for area charts
                            symbol: 'circle',
                            radius: 2,
                            states: {
                                hover: {
                                    enabled: true
                                }
                            }
                        }
                    }
                },
                series: [
                    {
                        name: 'Engagements',
                        marker: {
                            enabled: false
                        },
                        data: engagementsData,
                    },
                    {
                        name: 'Posts Published',
                        yAxis:1,
                        marker: {
                            enabled: false
                        },
                        data: postsPublished,
                    }
                ],
            };
        },
        imagePostsAndEngagementsChart() {
            const newDataByDate = {};
            Object.values(this.postMetrics.image).forEach((item) => {
                item.forEach(d => {
                    newDataByDate[d.date] = newDataByDate[d.date] || 0;
                    newDataByDate[d.date] += d.value;
                });
            });
            const engagementsData = Object.keys(newDataByDate).map((key) => {
                return [
                    this.timestampForXAxis(key),
                    newDataByDate[key]
                ];
            });
            const postsPublished = this.postCounts.image.map(itm => {
                return [this.timestampForXAxis(itm.date), itm.count];
            });

            return {
                chart: {
                    type: "areaspline",
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: "",
                },
                xAxis: {
                    type: "datetime",
                },
                yAxis: [
                    { // Primary yAxis
                        title: {
                            text: "Engagements",
                        },
                        labels: {
                            format: '{value}',
                        }
                    },
                    { //secondary yAxis
                        title: {
                            text: "Posts Published",
                        },
                        labels: {
                            format: '{value}',
                        },
                        opposite: true
                    }
                ],
                plotOptions: {
                    series: {
                        fillOpacity: 0.7
                    },
                    area: {
                        marker: {
                            enabled: false, // Simplified markers for area charts
                            symbol: 'circle',
                            radius: 2,
                            states: {
                                hover: {
                                    enabled: true
                                }
                            }
                        }
                    }
                },
                series: [
                    {
                        name: 'Engagements',
                        marker: {
                            enabled: false
                        },
                        data: engagementsData,
                    },
                    {
                        name: 'Posts Published',
                        yAxis:1,
                        marker: {
                            enabled: false
                        },
                        data: postsPublished,
                    }
                ],
            };
        },
        videoPostsAndEngagementsChart() {
            const newDataByDate = {};
            Object.values(this.postMetrics.video).forEach((item) => {
                item.forEach(d => {
                    newDataByDate[d.date] = newDataByDate[d.date] || 0;
                    newDataByDate[d.date] += d.value;
                });
            });
            const engagementsData = Object.keys(newDataByDate).map((key) => {
                return [
                    this.timestampForXAxis(key),
                    newDataByDate[key]
                ];
            });
            const postsPublished = this.postCounts.video.map(itm => {
                return [this.timestampForXAxis(itm.date), itm.count];
            });

            return {
                chart: {
                    type: "areaspline",
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: "",
                },
                xAxis: {
                    type: "datetime",
                },
                yAxis: [
                    { // Primary yAxis
                        title: {
                            text: "Engagements",
                        },
                        labels: {
                            format: '{value}',
                        }
                    },
                    { //secondary yAxis
                        title: {
                            text: "Posts Published",
                        },
                        labels: {
                            format: '{value}',
                        },
                        opposite: true
                    }
                ],
                plotOptions: {
                    series: {
                        fillOpacity: 0.7
                    },
                    area: {
                        marker: {
                            enabled: false, // Simplified markers for area charts
                            symbol: 'circle',
                            radius: 2,
                            states: {
                                hover: {
                                    enabled: true
                                }
                            }
                        }
                    }
                },
                series: [
                    {
                        name: 'Engagements',
                        marker: {
                            enabled: false
                        },
                        data: engagementsData,
                    },
                    {
                        name: 'Posts Published',
                        yAxis:1,
                        marker: {
                            enabled: false
                        },
                        data: postsPublished,
                    }
                ],
            };
        },
        postTypesChart() {
            const textPosts = this.postCounts.text.reduce((a, b) => {
                return a + b.count;
            }, 0);
            const imagePosts = this.postCounts.image.reduce((a, b) => {
                return a + b.count;
            }, 0);
            const videoPosts = this.postCounts.video.reduce((a, b) => {
                return a + b.count;
            }, 0);
            const totalPosts = textPosts + imagePosts + videoPosts;
            const data = [
                {
                    name: "Text Posts",
                    y: (textPosts / totalPosts) * 100,
                },
                {
                    name: "Image Posts",
                    y: (imagePosts / totalPosts) * 100,
                },
                {
                    name: "Video Posts",
                    y: (videoPosts / totalPosts) * 100,
                }
            ];
            return {
                chart: {
                    type: "pie",
                    plotBackgroundColor: null,
                    plotBorderWidth: null,
                    plotShadow: false,
                },
                colors:['#FA4B3F','#05D3F0','#FFDC4D'],
                title: {
                    text: '',
                },
                tooltip: {
                    pointFormat: "{series.name}: <b>{point.percentage:.1f}%</b>",
                },
                accessibility: {
                    point: {
                        valueSuffix: "%",
                    },
                },
                series: [{
                    name: '',
                    colorByPoint: true,
                    data
                }]
            }
        },
    },
    async mounted() {
        await this.fetchData();
    },
    methods: {
        isLoading(flag) {
            return this.loadingFlags.includes(flag);
        },
        showLoader(flag) {
            if (this.isLoading(flag)) {
                return;
            }
            this.loadingFlags.push(flag);
        },
        hideLoader(flag) {
            this.loadingFlags = this.loadingFlags.filter(itm => itm !== flag);
        },
        fetchData() {
            this.fetchAccountMetrics();
            this.fetchMetrics('text');
            this.fetchCounts('text');
            this.fetchMetrics('image')
            this.fetchCounts('image');
            this.fetchMetrics('video')
            this.fetchCounts('video');
        },
        async fetchMetrics(type = null) {
            this.showLoader('metrics_' + (type ? type : ''));
            try {
                const {data} = await axios.get(
                    '/api/v1/insights/posts/metrics',
                    {
                        params: {
                            post_type: type,
                            metrics: 'comments,likes,shares,reactions',
                            start: this.filter.start,
                            end: this.filter.end,
                            team: this.filter.team,
                            accounts: this.filter.accounts,
                            network: this.filter.network,
                        },
                    },
                );
                this.postMetrics[type ? type : "all"] = data.data;
            } catch (e) {
                axiosErrorHandler(e)
            }
            this.hideLoader('metrics_' + (type ? type : ''));
        },
        async fetchCounts(type = null) {
            this.showLoader('counts_' + (type ? type : ''));
            try {
                const {data} = await axios.get('/api/v1/insights/posts/counts', {
                    params: {
                        post_type: type,
                        start: this.filter.start,
                        end: this.filter.end,
                        team: this.filter.team,
                        accounts: this.filter.accounts,
                        network: this.filter.network,
                    },
                });
                this.postCounts[type ? type : "all"] = data.data;
            } catch (e) {
                axiosErrorHandler(e)
            }
            this.hideLoader('counts_' + (type ? type : ''));
        },
        async fetchAccountMetrics() {
            this.showLoader('account_metrics');
            try {
                const { data } = await axios.get("/api/v1/insights/accounts/metrics",
                    {
                        params: {
                            metrics: ["followers",].join(","),
                            start: this.filter.start,
                            end: this.filter.end,
                            team: this.filter.team,
                            accounts: this.filter.accounts,
                            network: this.filter.network,
                        },
                    });
                this.accountMetrics = data.data;
            } catch (e) {
                axiosErrorHandler(e);
            }
            this.hideLoader('account_metrics');
        },
        getAccount(id) {
            const all = this.accounts;
            let account = {};
            all.forEach((acc) => {
                if (acc.id === id) account = acc;
            })
            return account;
        },
        timestampForXAxis(timestamp) {
            return this.$momentUTC(timestamp, "YYYY-MM-DD").valueOf();
        },
    },
}
</script>
