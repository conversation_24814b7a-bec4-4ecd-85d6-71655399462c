<?php

namespace App\Http\Controllers;

use Bannerbear\BannerbearClient;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use function Clue\StreamFilter\fun;

class ToolCompareController extends Controller
{

    private static $socialbu_networks = ['Facebook', 'Twitter', 'LinkedIn', 'Instagram', 'Pinterest', 'TikTok', 'Reddit', 'Mastodon', 'Threads', 'BlueSky', 'Google Business Profile', 'YouTube'];

    private function loadAllTools(){
        // load the data from csv and cache it
        $csv = null;
        if(app()->environment() !== 'production' || !\Cache::has('social_media_tools_compare')){
            $csv = [];
            $file = fopen(resource_path('new_social_tools_data.csv'), 'r');
            // Read the CSV file line by line
            while (($row = fgetcsv($file)) !== false) {
                $csv[] = $row;
            }

            array_walk($csv, function(&$a) use ($csv) {
                // make sure both arrays are same length
                $a = array_pad($a, count($csv[0]), "");

                // associate array with keys and values
                $a = array_combine($csv[0], $a);
            });
            array_shift($csv); # remove column header

            // boolean keys
            $boolKeys = [
                "has_free_plan",
                "post_recycling",
                "social_calendar",
                "twitter_thread_scheduling",
                "instagram_story_scheduling",
                "instagram_first_comment",
                "bulk_import",
                "post_preview",
                "ai_content_generator",
                "social_inbox",
                "dynamic_automations",
                "has_publish_from_rss",
                "top_performing_posts",
                "has_teams",
                "has_post_approvals",
                "live_chat",
            ];

            // modify
            foreach ($csv as &$row){
                $row['slug'] = str_slug($row['name']);
                foreach ($boolKeys as $k) {
                    if(empty($row[$k])) $row[$k] = 'No';
                    $row[$k] = in_array($row[$k], ['Y', 'Yes', 'YES']) || Str::contains($row[$k], ['Y', 'Yes', 'YES']);
                }
            }

            \Cache::put('social_media_tools_compare', $csv, now()->addDays(7));
        } else {
            $csv = \Cache::get('social_media_tools_compare');
        }
        return $csv;
    }
    private function loadComparisonsText(){
        // load the data from csv and cache it
        $csv = null;
        if(app()->environment() !== 'production' || !\Cache::has('tools_comparison_content_csv')){
            $fp = fopen(resource_path('comparison_content.csv'), 'r');

            $csv = array();

            while ($row = fgetcsv($fp)) {
                $csv[] = $row;
            }

            fclose($fp);

            array_walk($csv, function(&$a) use ($csv) {
                // make sure both arrays are same length
                $a = array_pad($a, count($csv[0]), "");

                // associate array with keys and values
                try {
                    $a = array_combine($csv[0], $a);
                } catch (\Exception $e) {
                    dd($a);
                }
            });
            array_shift($csv); # remove column header

            \Cache::put('tools_comparison_content_csv', $csv, now()->addDays(7));
        } else {
            $csv = \Cache::get('tools_comparison_content_csv');
        }
        return $csv;
    }
    public function toolAlternative($name){
        $data = $this->loadAllTools();
        $tool1 = array_first($data, function($tool) use($name){
            return $tool['slug'] === $name;
        });
        $tool2 = array_first($data, function($tool) use($name){
            return $tool['slug'] === 'socialbu';
        });
        if(!$tool1 || $name === 'socialbu') abort(404);

        $tool2['sb_white_logo'] = '/images/redesign/logo-white-bg.svg'; //SocialBu white logo

        $getOther = function($tool) use($tool1, $tool2){
            return $tool['name'] === $tool1['name'] ? $tool2 : $tool1;
        };

        $getNetworks = function($tool){
            $str = $tool['networks'];
            return array_filter(explode(',', $str), function($n){
                return !empty($n);
            });
        };

        $implodeWithAnd = function($sep, $arr){
            if(count($arr) === 1) return implode($sep, $arr);
            return implode($sep, array_slice($arr, 0, -1)) . ' and ' . end($arr);
        };

        $getFeatures = function($tool){
            $featureKeys = [
                "has_free_plan",
                "post_recycling",
                "social_calendar",
                "twitter_thread_scheduling",
                "instagram_story_scheduling",
                "instagram_first_comment",
                "bulk_import",
                "post_preview",
                "ai_content_generator",
                "social_inbox",
                "dynamic_automations",
                "has_publish_from_rss",
                "top_performing_posts",
                "has_teams",
                "has_post_approvals",
                // "live_chat",
            ];
            $features = [];
            foreach($featureKeys as $k){
                if(isset($tool[$k]) && $tool[$k]){
                    $title = Str::title(str_replace('_', ' ', $k));
                    $features[] = $title;
                }
            }
            return $features;
        };

        $tool2UniqueNetworks = array_values(array_diff($getNetworks($tool2), $getNetworks($tool1)));
        $tool2UniqueFeatures = array_values(array_diff($getFeatures($tool2), $getFeatures($tool1)));

        $meta_description = 'Compare ' . $tool1['name'] . ' with the best alternative offering better benefits, pricing, and customer support. Choose a better alternative for your ' . $implodeWithAnd(', ', $getNetworks($tool1)) . ' accounts. ';

        $detailed_description = $meta_description;

        $detailed_description .= '<br/><br/>' . $tool1['name'] . ' starts at ' . $tool1['starting_price'] . ' while ' . $tool2['name'] . ' starts at ' . $tool2['starting_price'] . '. ';
        $detailed_description .= 'Features like ' . $implodeWithAnd(', ', $tool2UniqueFeatures) . ' are also compared on this page. ';


        $page = [
            'title' => Arr::random([
                'The best alternative to ' . $tool1['name'] . ': ' . $tool2['name'],
                'The best ' . $tool1['name'] . ' alternative: ' . $tool2['name'],
                'A better alternative to ' . $tool1['name'] . ': ' . $tool2['name'],
                'A better ' . $tool1['name'] . ' alternative: ' . $tool2['name'],
                'The best ' . $tool1['name'] . ' alternative: ' . $tool2['name'],
            ]),
            'description' => $meta_description,
            'detailed_description' => $detailed_description,
            'slug' => $tool1['slug'] . '-alternative',
            'header_image' => $this->getImageUrl($tool1, $tool2,'cat_fight'),
        ];
        $page['image'] = $this->getImageUrl($tool1, $tool2, 'alternative');

        $dynamic_data = [];

        if(!$tool1['social_calendar']){
            $dynamic_data[] = [
                'title' => $tool2['name'] . ' has an easy way to plan content',
                'subtitle' => $tool1['name'] . ' does not.',
                'content' => 'Visualize all your published and scheduled content with ' . $tool2['name'] . ' so you can plan, manage, and streamline your social media content. This is not available with ' . $tool1['name'] . '.',
                'image' => [
                    'url' => '/images/site/social-calendar-1.jpg',
                    'alt' => 'social calendar in ' . $tool1['name'],
                ],
                'link' => '/social-calendar',
            ];
        }

        if(!$tool1['has_post_approvals'] || !$tool1['has_teams']){
            $dynamic_data[] = [
                'title' => $tool1['name'] . ' doesn\'t have a content approval workflow',
                'subtitle' => $tool2['name'] . ' does.',
                'content' => $tool2['name'] . ' streamlines your social media approval process, so you can collaborate as a team and review your posts before they get published. You\'ll never have to worry about embarrassing mistakes that can happen while using ' . $tool1['name'] . '.',
                'image' => [
                    'url' => '/images/site/post-approval-1.jpg',
                    'alt' => 'post approval in ' . $tool1['name'],
                ],
                'link' => '/collaborate',
            ];
        }

        if(!$tool1['twitter_thread_scheduling']){
            $dynamic_data[] = [
                'title' => 'Schedule Twitter threads with ' . $tool2['name'],
                'subtitle' => $tool1['name'] . '  can\'t compete with us.',
                'content' => 'Are you one of those people who have a lot to say on Twitter? ' . $tool2['name'] . ' supports scheduling Twitter threads which is not possible with ' . $tool1['name'] . '. So go ahead and share your thoughts with the world using ' . $tool2['name'] . '.',
                'image' => [
                    'url' => '/images/site/twitter-thread-scheduling.jpg',
                    'alt' => 'Twitter thread scheduling in ' . $tool1['name'],
                ],
                'link' => '/twitter',
            ];
        }

        if(!$tool1['instagram_story_scheduling']){
            $dynamic_data[] = [
                'title' => $tool2['name'] . ' supports Instagram story scheduling',
                'subtitle' => $tool1['name'] . ' cannot schedule Instagram stories.',
                'content' => 'With ' . $tool2['name'] . ', you can easily schedule your Instagram stories in advance. This powerful tool makes it easy to keep your account active and engaging, without having to worry about last-minute stress. So why be stuck with ' . $tool1['name'] . '?',
                'image' => [
                    'url' => '/images/site/schedule-story-2.jpg',
                    'alt' => 'Instagram story scheduling in ' . $tool1['name'],
                ],
                'link' => '/schedule-stories',
            ];
        }

        if(!$tool1['instagram_first_comment']){
            $dynamic_data[] = [
                'title' => $tool2['name'] . ' supports scheduling Instagram posts with first-comment',
                'subtitle' => 'and ' . $tool1['name'] . ' does not support that.',
                'content' => 'Automatically add a first comment after your media is published to Instagram. You can add hashtags in your posts without crowding the caption. ' . $tool2['name'] . ' supports scheduling Instagram posts with first-comment which is not possible with ' . $tool1['name'] . '.',
                'image' => [
                    'url' => '/images/site/instagram-leave-comment-1.jpg',
                    'alt' => 'Instagram post scheduling with first comment in ' . $tool1['name'],
                ],
                'link' => '/instagram',
            ];
        }

        if(!$tool1['post_recycling']){
            $dynamic_data[] = [
                'title' => $tool1['name'] . ' doesn\'t have post recycling',
                'subtitle' => 'but ' . $tool2['name'] . ' does.',
                'content' => 'Schedule your evergreen content according to the rules you set. You can control when it\'s published, and even have custom time slots for publishing! It\'s too much to ask for with ' . $tool1['name'] . '.',
                'image' => [
                    'url' => '/images/site/11_2020/post-recycling.jpg',
                    'alt' => 'Post recycling in ' . $tool1['name'],
                ],
                'link' => '/post-recycling',
            ];
        }

        if(!$tool1['bulk_import']){
            $dynamic_data[] = [
                'title' => $tool1['name'] . ' doesn\'t support importing posts in bulk',
                'subtitle' => 'but ' . $tool2['name'] . ' does.',
                'content' => 'The process of uploading content to ' . $tool2['name'] . ' is simple and quick. You can bulk upload your CSV files and all your content will be scheduled in advance so you don\'t have to worry about it taking all your time! Good luck saving time with ' . $tool1['name'] . '.',
                'image' => [
                    'url' => '/images/site/bulk-upload.jpg',
                    'alt' => 'bulk scheduling in ' . $tool1['name'],
                ],
                'link' => '/bulk-scheduling',
            ];
        }

        if(!$tool1['ai_content_generator']){
            $dynamic_data[] = [
                'title' => $tool2['name'] . ' can write content for you using AI',
                'subtitle' => $tool1['name'] . ' is no way near us.',
                'content' => 'Let AI write content for your social media posts. It\'s almost like a social media content writer in your team. Generate content and ideas for your social media posts with a few clicks. Can you save this much time with ' . $tool1['name'] . '?',
                'image' => [
                    'url' => '/images/site/tweet-generator.jpg',
                    'alt' => 'AI post writer in ' . $tool1['name'],
                ],
            ];
        }

        if(!$tool1['social_inbox']){
            $dynamic_data[] = [
                'title' => $tool1['name'] . ' doesn\'t have a Social Inbox',
                'subtitle' => 'but ' . $tool2['name'] . ' does.',
                'content' => $tool2['name'] . ' offers an efficient unified inbox that is time-saving and easy to use. See all your messages, comments, and replies at one place. Not sure if this is possible with ' . $tool1['name'] . '.',
                'image' => [
                    'url' => '/images/site/11_2020/respond-review.jpg',
                    'alt' => 'social inbox in ' . $tool1['name'],
                ],
                'link' => '/respond',
            ];
        }

        if(!$tool1['dynamic_automations']){
            $dynamic_data[] = [
                'title' => $tool1['name'] . ' doesn\'t have fully dynamic automation workflow',
                'subtitle' => 'but ' . $tool2['name'] . ' does.',
                'content' => 'With ' . $tool2['name'] . ', you can automate every social media scenario like posting content when you publish a new blog post, sending an automated reply to comments, notifying an external service whenever a new post is published, alerting a specific user if a comment or message has a particular keyword in it, and a lot more. We do not call post scheduling "automation" like ' . $tool1['name'] . '!',
                'image' => [
                    'url' => '/images/site/automation-event-1.jpg',
                    'alt' => 'fully dynamic automation in ' . $tool1['name'],
                ],
                'link' => '/automate',
            ];
        }

        if(!$tool1['has_publish_from_rss']){
            $dynamic_data[] = [
                'title' => 'Automatically post content from your blog (or RSS feeds) using ' . $tool2['name'],
                'subtitle' => 'No luck with ' . $tool1['name'] . '.',
                'content' => 'Are you tired of manually posting to your social media accounts every time there\'s something new on your blog or website? ' . $tool2['name'] . ' is here to help! That means you\'ll never have to worry about forgetting to share something important again (as with ' . $tool1['name'] . ').',
                'image' => [
                    'url' => '/images/site/post-from-rss-automation-1.jpg',
                    'alt' => 'auto post form rss in ' . $tool1['name'],
                ],
                'link' => '/post-from-rss',
            ];
        }

        return view('common.social_media_tools.tool_alternative_v2', [
            'tool1' => $tool1,
            'tool2' => $tool2,
            'page' => $page,
            'socialbu_networks' => self::$socialbu_networks,
            'tool2UniqueNetworks' => $tool2UniqueNetworks,
            'tool2UniqueFeatures' => $tool2UniqueFeatures,
            'dynamic_data' => $dynamic_data
        ]);
    }
    public function toolVsTool($name1, $name2){
        if($name1 === $name2) abort(404);

        if($name2 === 'socialbu') abort(404);

        if($name1 !== 'socialbu') {
            $slugs = ([$name1, $name2]);
            sort($slugs);

            if($slugs[0] !== $name1 || $slugs[1] !== $name2) abort(404);
        }

        if($name1 === 'socialbu'){
            // redirect to alternative page
            return redirect('/compare/' . $name2 . '-alternative', 301);
        }

        $data = $this->loadAllTools();
        $tool1 = array_first($data, function($tool) use($name1){
            return $tool['slug'] === $name1;
        });
        $tool2 = array_first($data, function($tool) use($name2){
            return $tool['slug'] === $name2;
        });
        if(!$tool1 || !$tool2) abort(404);

        $getOther = function($tool) use($tool1, $tool2){
            return $tool['name'] === $tool1['name'] ? $tool2 : $tool1;
        };

        $getNetworks = function($tool){
            $str = $tool['networks'];
            return array_filter(explode(',', $str), function($n){
                return !empty($n);
            });
        };

        $implodeWithAnd = function($sep, $arr){
            if(count($arr) === 1) return implode($sep, $arr);
            return implode($sep, array_slice($arr, 0, -1)) . ' and ' . end($arr);
        };

        $getFeatures = function($tool){
            $featureKeys = [
                "has_free_plan",
                "post_recycling",
                "social_calendar",
                "twitter_thread_scheduling",
                "instagram_story_scheduling",
                "instagram_first_comment",
                "bulk_import",
                "post_preview",
                "ai_content_generator",
                "social_inbox",
                "dynamic_automations",
                "has_publish_from_rss",
                "top_performing_posts",
                "has_teams",
                "has_post_approvals",
                "live_chat",
            ];
            $features = [];
            foreach($featureKeys as $k){
                if( (isset($tool[$k]) && $tool[$k]) || $tool['name'] === 'SocialBu'){
                    $title = Str::title(str_replace('_', ' ', $k));
                    $features[] = $title;
                }
            }
            return array_filter($features);
        };

        $toolWithMoreNetworks = count($getNetworks($tool1)) > count($getNetworks($tool2)) ? $tool1 : $tool2;
        $toolWithMoreFeatures = count($getFeatures($tool1)) > count($getFeatures($tool2)) ? $tool1 : $tool2;

        $toolWithLessNetworks = $getOther($toolWithMoreNetworks);
        $toolWithLessFeatures = $getOther($toolWithMoreFeatures);

        $toolsWithLiveChat = [];
        if(in_array('live_chat', $tool1))
            $toolsWithLiveChat[] = $tool1;
        if(in_array('live_chat', $tool2))
            $toolsWithLiveChat[] = $tool2;

        $meta_description = $toolWithMoreNetworks['name'] . ' has more social networks while ' . $toolWithLessNetworks['name'] . ' is focused on ' . $implodeWithAnd(', ', $getNetworks($toolWithLessNetworks))  . '. ';

        $detailed_description = $meta_description;

        $meta_description .= 'Find out the complete comparison of ' . $tool1['name'] . ' with ' . $tool2['name'] . ' and see which one works better for you.';

        $detailed_description .= $tool1['name'] . ' starts at ' . $tool1['starting_price'] . ' and ' . $tool2['name'] . ' starts at ' . $tool2['starting_price'] . '. ';

        if(count($toolsWithLiveChat) > 0){
            $liveChatNames = collect($toolsWithLiveChat)->map(function($tool){
                return $tool['name'];
            })->toArray();

            $liveChatStr = implode(' and ', $liveChatNames) . ' also ' . ( count($toolsWithLiveChat) > 1 ? 'have' : 'has' ) . ' live chat support. ';

            $detailed_description .= $liveChatStr;
        }

        $detailed_description .= '<br/>Features like ' . strtolower($implodeWithAnd(', ', $getFeatures($toolWithMoreFeatures))) . ' are also compared on this page.';

        $detailed_description .= '<br/><br/> We also suggest SocialBu as an excellent alternative for your social media management and automation needs. It stands out with a wide range of powerful features and even offers a free plan. Plus, its pricing is more affordable, providing feature-rich packages at budget-friendly rates.';

        $tool1UniqueNetworks = array_values(array_diff($getNetworks($tool1), $getNetworks($tool2)));
        $tool2UniqueNetworks = array_values(array_diff($getNetworks($tool2), $getNetworks($tool1)));

        $tool1UniqueFeatures = array_values(array_diff($getFeatures($tool1), $getFeatures($tool2)));
        $tool2UniqueFeatures = array_values(array_diff($getFeatures($tool2), $getFeatures($tool1)));


        $page = [
            'title' => $tool1['name'] . ' vs. ' . $tool2['name'],
            'description' => $meta_description,
            'detailed_description' => $detailed_description,
            'slug' => $tool1['slug'] . '-vs-' . $tool2['slug'],
            'header_image' => $this->getImageUrl($tool1, $tool2,'cat_fight'),
        ];
        $page['image'] = $this->getImageUrl($tool1, $tool2, 'default');

        $tool3 = null;
        if($name1 !== 'socialbu' && $name2 !== 'socialbu'){
            // 3rd one should be socialbu
            $tool3 = array_first($data, function($tool) use($name1){
                return $tool['slug'] === 'socialbu';
            });
            // $page['title'] = $tool1['name'] . ' vs. ' . $tool2['name'] . ' vs. ' . $tool3['name'];
        }

        $dynamic_content = [];

        // more networks
        $dynamic_content[] = [
            'title' => $tool1['name'] . ' works with ' . ($tool1 == $toolWithMoreNetworks ? 'more' : 'less') . ' social networks',
            'content' => 'In comparison to ' . $toolWithLessNetworks['name'] . ', ' . $toolWithMoreNetworks['name'] . ' works with ' . $toolWithMoreNetworks['networks'] . '.',
        ];

        // more features
        $dynamic_content[] = [
            'title' => $tool1['name'] . ' has ' . ($tool1 == $toolWithMoreFeatures ? 'more' : 'less') . ' features',
            'content' => 'In comparison to ' . $toolWithLessFeatures['name'] . ', ' . $toolWithMoreFeatures['name'] . ' has more features.',
        ];

        // unique features
        $toolHavingMoreUniqueFeatures = count($tool1UniqueFeatures) > count($tool2UniqueFeatures) ? $tool1 : $tool2;
        if(count($toolHavingMoreUniqueFeatures) > 0){
            $dynamic_content[] = [
                'title' => [
                    $toolHavingMoreUniqueFeatures['name'] . ' has ' . ( count($toolHavingMoreUniqueFeatures) > 4 ? 'many' : 'some' ) . ' unique features',
                ],
                'content' => $toolHavingMoreUniqueFeatures['name'] . ' has unique features like ' . $implodeWithAnd(', ', $toolHavingMoreUniqueFeatures == $tool1 ? $tool1UniqueFeatures : $tool2UniqueFeatures ) . ' that are not available in ' . $getOther($toolHavingMoreUniqueFeatures)['name'] . '.',
            ];
        }

        $toolHavingLessUniqueFeatures = $getOther($toolHavingMoreUniqueFeatures);
        if(count($toolHavingLessUniqueFeatures == $tool1 ? $tool1UniqueFeatures : $tool2UniqueFeatures) > 0){
            $dynamic_content[] = [
                'title' => $toolHavingLessUniqueFeatures['name'] . ' also shines',
                'content' => $toolHavingLessUniqueFeatures['name'] . ' has these features that are not available in ' . $toolHavingMoreUniqueFeatures['name'] . ': '. $implodeWithAnd(', ', $toolHavingLessUniqueFeatures == $tool1 ? $tool1UniqueFeatures : $tool2UniqueFeatures ) . '.',
            ];
        }

        // trial and free plan
        $dynamic_content[] = [
            'title' => 'Free plan and free trial',
            'content' => with(1, function() use($tool1, $tool2){

                if($tool1['has_free_plan'] && !$tool2['has_free_plan']){
                    $str = $tool1['name'] . ' has a free plan.';
                } elseif($tool2['has_free_plan'] && !$tool1['has_free_plan']) {
                    $str = $tool2['name'] . ' has a free plan.';
                } elseif($tool1['has_free_plan'] && $tool2['has_free_plan']){
                    $str = 'Both ' . $tool1['name'] . ' and ' . $tool2['name'] . ' have a free plan.';
                } else {
                    $str = 'Neither ' . $tool1['name'] . ' nor ' . $tool2['name'] . ' have a free plan.';
                }


                $str2 = '';
                if($tool1['has_free_trial'] && !$tool2['has_free_trial']){
                    $str2 = $tool1['name'] . ' has a free trial on their paid plans.';
                } elseif($tool2['has_free_trial'] && !$tool1['has_free_trial']) {
                    $str2 = $tool2['name'] . ' has a free trial on their paid plans.';
                } elseif($tool1['has_free_trial'] && $tool2['has_free_trial']){
                    $str2 = 'Both ' . $tool1['name'] . ' and ' . $tool2['name'] . ' offer free trial on their paid plans.';
                } else {
                    $str2 = 'Neither ' . $tool1['name'] . ' nor ' . $tool2['name'] . ' offer free trial.';
                }

                return $str . ' ' . $str2;
            }),
        ];

        // social calendar
        $dynamic_content[] = [
            'title' => 'Content planning with calendar',
            'content' => with(1, function() use($tool1, $tool2){
                if($tool1['social_calendar'] && !$tool2['social_calendar']){
                    return $tool1['name'] . ' has an easy to use Social Calendar which is not available with ' . $tool2['name'] . '.';
                } elseif($tool2['social_calendar'] && !$tool1['social_calendar']) {
                    return $tool2['name'] . ' has an easy to use Social Calendar which is not available with ' . $tool1['name'] . '.';
                } elseif($tool1['social_calendar'] && $tool2['social_calendar']){
                    return 'Both ' . $tool1['name'] . ' and ' . $tool2['name'] . ' have Social Calendar for easy content planning.';
                } else {
                    return 'Neither ' . $tool1['name'] . ' nor ' . $tool2['name'] . ' have calendar for easy content planning.';
                }
            }),
        ];

        // content approvals
        $dynamic_content[] = [
            'title' => 'Content approval workflow',
            'content' => with(1, function() use($tool1, $tool2){
                if($tool1['has_post_approvals'] && !$tool2['has_post_approvals']){
                    return $tool1['name'] . ' has content approval workflow which is not available with ' . $tool2['name'] . '.';
                } elseif($tool2['has_post_approvals'] && !$tool1['has_post_approvals']) {
                    return $tool2['name'] . ' has content approval workflow which is not available with ' . $tool1['name'] . '.';
                } elseif($tool1['has_post_approvals'] && $tool2['has_post_approvals']){
                    return 'Both ' . $tool1['name'] . ' and ' . $tool2['name'] . ' have content approval workflow for easy content review within teams.';
                } else {
                    return 'Neither ' . $tool1['name'] . ' nor ' . $tool2['name'] . ' have content approval workflow.';
                }
            }),
        ];

        // ai content generator
        $dynamic_content[] = [
            'title' => 'Generate AI content',
            'content' => with(1, function() use($tool1, $tool2){
                if($tool1['ai_content_generator'] && !$tool2['ai_content_generator']){
                    return $tool1['name'] . ' has AI based content generator which is not available with ' . $tool2['name'] . '.';
                } elseif($tool2['ai_content_generator'] && !$tool1['ai_content_generator']) {
                    return $tool2['name'] . ' has AI based content generator which is not available with ' . $tool1['name'] . '.';
                } elseif($tool1['ai_content_generator'] && $tool2['ai_content_generator']){
                    return 'Both ' . $tool1['name'] . ' and ' . $tool2['name'] . ' have AI based content generator.';
                } else {
                    return 'Neither ' . $tool1['name'] . ' nor ' . $tool2['name'] . ' have AI based content generator.';
                }
            }),
        ];

        // instagram_story_scheduling
        $dynamic_content[] = [
            'title' => 'Schedule Instagram stories',
            'content' => with(1, function() use($tool1, $tool2){
                if($tool1['instagram_story_scheduling'] && !$tool2['instagram_story_scheduling']){
                    return $tool1['name'] . ' lets you schedule stories on Instagram which is not possible with ' . $tool2['name'] . '.';
                } elseif($tool2['instagram_story_scheduling'] && !$tool1['instagram_story_scheduling']) {
                    return $tool2['name'] . ' lets you schedule stories on Instagram which is not possible with ' . $tool1['name'] . '.';
                } elseif($tool1['instagram_story_scheduling'] && $tool2['instagram_story_scheduling']){
                    return 'Both ' . $tool1['name'] . ' and ' . $tool2['name'] . ' have Instagram story scheduling.';
                } else {
                    return 'Neither ' . $tool1['name'] . ' nor ' . $tool2['name'] . ' lets you schedule stories on Instagram.';
                }
            }),
        ];

        // instagram_first_comment
        $dynamic_content[] = [
            'title' => 'Schedule Instagram posts with first comment',
            'content' => with(1, function() use($tool1, $tool2){
                if($tool1['instagram_first_comment'] && !$tool2['instagram_first_comment']){
                    return $tool1['name'] . ' lets you schedule Instagram posts with first comment which is not possible with ' . $tool2['name'] . '.';
                } elseif($tool2['instagram_first_comment'] && !$tool1['instagram_first_comment']) {
                    return $tool2['name'] . ' lets you schedule Instagram posts with first comment which is not possible with ' . $tool1['name'] . '.';
                } elseif($tool1['instagram_first_comment'] && $tool2['instagram_first_comment']){
                    return 'Both ' . $tool1['name'] . ' and ' . $tool2['name'] . ' offer scheduling Instagram posts with first comment.';
                } else {
                    return 'Neither ' . $tool1['name'] . ' nor ' . $tool2['name'] . ' offer scheduling Instagram posts with first comment.';
                }
            }),
        ];

        // twitter_thread_scheduling
        $dynamic_content[] = [
            'title' => 'Schedule Twitter threads',
            'content' => with(1, function() use($tool1, $tool2){
                if($tool1['twitter_thread_scheduling'] && !$tool2['twitter_thread_scheduling']){
                    return $tool1['name'] . ' lets you schedule threads on Twitter which is not possible with ' . $tool2['name'] . '.';
                } elseif($tool2['twitter_thread_scheduling'] && !$tool1['twitter_thread_scheduling']) {
                    return $tool2['name'] . ' lets you schedule threads on Twitter which is not possible with ' . $tool1['name'] . '.';
                } elseif($tool1['twitter_thread_scheduling'] && $tool2['twitter_thread_scheduling']){
                    return 'Both ' . $tool1['name'] . ' and ' . $tool2['name'] . ' have Twitter thread scheduling.';
                } else {
                    return 'Neither ' . $tool1['name'] . ' nor ' . $tool2['name'] . ' lets you schedule threads on Twitter.';
                }
            }),
        ];

        // post_recycling
        $dynamic_content[] = [
            'title' => 'Recycle your content - evergreen posting',
            'content' => with(1, function() use($tool1, $tool2){
                if($tool1['post_recycling'] && !$tool2['post_recycling']){
                    return $tool1['name'] . ' has post recycling which is not possible with ' . $tool2['name'] . '.';
                } elseif($tool2['post_recycling'] && !$tool1['post_recycling']) {
                    return $tool2['name'] . ' has post recycling which is not possible with ' . $tool1['name'] . '.';
                } elseif($tool1['post_recycling'] && $tool2['post_recycling']){
                    return 'Both ' . $tool1['name'] . ' and ' . $tool2['name'] . ' have post recycling.';
                } else {
                    return 'Neither ' . $tool1['name'] . ' nor ' . $tool2['name'] . ' have post recycling.';
                }
            }),
        ];

        // bulk_import
        $dynamic_content[] = [
            'title' => 'Import your social media content in bulk',
            'content' => with(1, function() use($tool1, $tool2){
                if($tool1['bulk_import'] && !$tool2['bulk_import']){
                    return $tool1['name'] . ' supports importing content in bulk which is not possible with ' . $tool2['name'] . '.';
                } elseif($tool2['bulk_import'] && !$tool1['bulk_import']) {
                    return $tool2['name'] . ' supports importing content in bulk which is not possible with ' . $tool1['name'] . '.';
                } elseif($tool1['bulk_import'] && $tool2['bulk_import']){
                    return 'Both ' . $tool1['name'] . ' and ' . $tool2['name'] . ' supports importing content in bulk.';
                } else {
                    return 'Neither ' . $tool1['name'] . ' nor ' . $tool2['name'] . ' supports importing content in bulk.';
                }
            }),
        ];

        // auto_post_from_rss_feeds
        $dynamic_content[] = [
            'title' => 'Automatically post from RSS feeds',
            'content' => with(1, function() use($tool1, $tool2){
                if($tool1['has_publish_from_rss'] && !$tool2['has_publish_from_rss']){
                    return $tool1['name'] . ' supports automatically posting content from RSS feeds which is not possible with ' . $tool2['name'] . '.';
                } elseif($tool2['has_publish_from_rss'] && !$tool1['has_publish_from_rss']) {
                    return $tool2['name'] . ' supports automatically posting content from RSS feeds which is not possible with ' . $tool1['name'] . '.';
                } elseif($tool1['has_publish_from_rss'] && $tool2['has_publish_from_rss']){
                    return 'Both ' . $tool1['name'] . ' and ' . $tool2['name'] . ' supports automatically posting content from RSS feeds.';
                } else {
                    return 'Neither ' . $tool1['name'] . ' nor ' . $tool2['name'] . ' supports automatically posting content from RSS feeds.';
                }
            }),
        ];

        // teams
        $dynamic_content[] = [
            'title' => 'Team collaboration for your social media',
            'content' => with(1, function() use($tool1, $tool2){
                if($tool1['has_teams'] && !$tool2['has_teams']){
                    return $tool1['name'] . ' has team collaboration feature which is not possible with ' . $tool2['name'] . '.';
                } elseif($tool2['has_teams'] && !$tool1['has_teams']) {
                    return $tool2['name'] . ' has team collaboration feature which is not possible with ' . $tool1['name'] . '.';
                } elseif($tool1['has_teams'] && $tool2['has_teams']){
                    return 'Both ' . $tool1['name'] . ' and ' . $tool2['name'] . ' have team collaboration feature.';
                } else {
                    return 'Neither ' . $tool1['name'] . ' nor ' . $tool2['name'] . ' have team collaboration feature.';
                }
            }),
        ];

        // live_chat
        $dynamic_content[] = [
            'title' => 'Highly available live chat support',
            'content' => with(1, function() use($tool1, $tool2){
                if($tool1['live_chat'] && !$tool2['live_chat']){
                    return $tool1['name'] . ' has live chat support which is not available with ' . $tool2['name'] . '.';
                } elseif($tool2['live_chat'] && !$tool1['live_chat']) {
                    return $tool2['name'] . ' has live chat support which is not available with ' . $tool1['name'] . '.';
                } elseif($tool1['live_chat'] && $tool2['live_chat']){
                    return 'Both ' . $tool1['name'] . ' and ' . $tool2['name'] . ' have live chat support.';
                } else {
                    return 'Neither ' . $tool1['name'] . ' nor ' . $tool2['name'] . ' have live chat support.';
                }
            }),
        ];
//
//        $allComparisonsText = $this->loadComparisonsText();
//
//        $comparisonText = collect($allComparisonsText)
//            ->where('tool1', $tool1['name'])
//            ->where('tool2', $tool2['name'])
//            ->first();

        return view('common.social_media_tools.tool_vs_tool_v2', [
            'tool1' => $tool1,
            'tool2' => $tool2,
            'tool3' => $tool3,
            'page' => $page,
            'socialbu_networks' => self::$socialbu_networks,
            'toolWithMoreNetworks' => $toolWithMoreNetworks,
            'toolWithMoreFeatures' => $toolWithMoreFeatures,
            'toolWithLessNetworks' => $toolWithLessNetworks,
            'toolWithLessFeatures' => $toolWithLessFeatures,
            'toolsWithLiveChat' => $toolsWithLiveChat,
            'tool1UniqueNetworks' => $tool1UniqueNetworks,
            'tool2UniqueNetworks' => $tool2UniqueNetworks,
            'tool1UniqueFeatures' => $tool1UniqueFeatures,
            'tool2UniqueFeatures' => $tool2UniqueFeatures,
            'dynamic_content' => $dynamic_content,
        ]);
    }
    public function toolCompareList($name){
        $data = $this->loadAllTools();
        $tool = Arr::first($data, function($tool) use($name){
            return $tool['slug'] === $name;
        });
        if(!$tool || $name === 'socialbu') abort(404);

        return view('common.social_media_tools.tool_compare_list', [
            'tool' => $tool,
            'list' => $data,
        ]);
    }
    public function index(){
        return redirect('/social-media-scheduling-tools/');
    }

    private function getImageUrl($tool1, $tool2 = null, $type = 'default'){

        //create slug and store file and reuse it
        $slug = $tool1['slug'] . '-' . $type . ($tool2 ? '-' . $tool2['slug'] : '');

        $filePath = 'images/social_media_tools/' . Str::slug($slug) . '.jpg';

        if(\Storage::disk('public')->exists($filePath)){
            return \Storage::disk('public')->url($filePath);
        }

        return url('images/comparison/thisorthat.png');  // still return the path

        /*
        $apiKey = '************************************';

        if($type === 'cat_fight') {
            $templateId = 'kY4Qv7D8K8X5B0qmP1';
            $data = [
                "modifications" => [
                    [
                        "name" => "player_2",
                        "image_url" => $tool1['logo'],
                    ],
                    [
                        "name" => "player_1",
                        "image_url" => $tool2['logo'],
                    ]
                ],
                'metadata' => $tool1['name'] . ' vs. ' . $tool2['name'],
            ];
        } else if($type === 'alternative'){
            $templateId = 'wXmzGBDa6MYbLN7gjn';
            $apiKey = '************************************';
            $data = [
                "modifications" => [
                    [
                        "name" => "message",
                        "text" => Arr::random([
                            'The best alternative to ' . $tool1['name'],
                            'The best ' . $tool1['name'] . ' alternative',
                            $tool1['name'] . ' alternative that you can trust',
                            'The best ' . $tool1['name'] . ' alternative compared',
                            'A better alternative to ' . $tool1['name'],
                            'A better ' . $tool1['name'] . ' alternative',
                        ]),
                    ],
                ],
                'metadata' => $tool1['name'] . ' alternative',
            ];
        } else {
            $templateId = 'wXmzGBDadOq5LN7gjn';
            $data = [
                "modifications" => [
                    [
                        "name" => "player_2",
                        "image_url" => $tool1['logo'],
                    ],
                    [
                        "name" => "player_1",
                        "image_url" => $tool2['logo'],
                    ]
                ],
                'metadata' => $tool1['name'] . ' vs. ' . $tool2['name'],
            ];
        }

        $bb = new BannerbearClient($apiKey);
        try {
            $bbImg = $bb->create_image(
                $templateId,
                $data,
                TRUE
            );
            $url = $bbImg['image_url'];

            $img = @file_get_contents($url);
            if($img){
                \Storage::disk('public')->put($filePath, $img);
            }

            return \Storage::disk('public')->url($filePath);
        } catch (\Exception $exception){
            if(!Str::contains(strtolower($exception->getMessage()), ['402', '400'])){
                report($exception);
             }
            return url('images/comparison/thisorthat.png');  // still return the path
        }
        */
    }
}
