.alertify {

    .ajs-dialog {
        box-shadow: none !important;
        padding: 32px; 
        border-radius: 1rem;
    }
    
    .ajs-header {
        font-size: 20px;
        font-weight: 700;
        line-height: 120%; /* 28.8px */
        font-family: "DM Sans";
        color: #252A32;    
        border: none;  
        padding: 24px 24px 8px 24px; 
    }
    .ajs-body{
        font-family: "DM Sans";
        min-height: 32px !important;
    }
    .ajs-content{
        font-size: 16px;
        font-weight: 400;
        color: #4A5465;
        font-family: "DM Sans";
        border: none;
        padding: 0px !important;
    }
    .ajs-button {
        min-height: unset !important;
        font-size: 16px;
        padding: 8px 16px;
    }

    .ajs-dimmer, .ajs-modal {
        transition: none !important;
    }
    .ajs-footer{
        padding: 24px 24px 0px 24px; 
        border: none;
    }
    @media (max-width: 600px) {
        .ajs-footer .ajs-buttons{
            display: flex !important;
            flex-direction: column !important;
        }
    }
}
.alertify-notifier{
    .ajs-message{
        text-shadow: none !important;
        font-family: "DM Sans" !important;
        font-size: 16px;
        border-radius: 8px;
        line-height: 150%;
        padding: 20px !important;
    }
    .ajs-success{
        background-color: #DEFAD4 !important;
        color:#147830 !important;
        box-shadow: 0px 8px 20px -4px rgba(29, 33, 41, 0.08);

    }
    .ajs-error{
        background-color: #FEE3D4 !important;
        color: #B51632 !important;
        box-shadow: 0px 8px 20px -4px rgba(29, 33, 41, 0.08);

    }
    .ajs-warning{
        background-color: #FEF3CC !important;
        color: #B56202 !important;
        box-shadow: 0px 8px 20px -4px rgba(29, 33, 41, 0.08);

    }

}
