<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class VerifyEmailReminder extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage)
            ->greeting('Hey ' . explode(' ', ($notifiable->name . ''))[0])
            ->subject('Reminder: Verify your SocialBu account')
            ->line('Your email address is not verified. While your email is not verified, you cannot use SocialBu. Please complete your email verification to activate your account.')
            ->line('If you have any thoughts or if you think you should not receive this email, please contact us.')
            ->action('Verify Now', url("auth/confirm/{$notifiable->token}"))
            ->line('Note: Unverified accounts will eventually be deleted.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            //
        ];
    }
}
