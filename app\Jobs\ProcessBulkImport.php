<?php

namespace App\Jobs;

use App\Imports\BulkImport;
use App\User;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Validation\ValidationException;

class ProcessBulkImport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /** @var User */
    private $user;

    private $file;

    /**
     * Create a new job instance.
     *
     * @param $user
     * @param $file
     */
    public function __construct($user, $file)
    {
        $this->user = $user;
        $this->file = $file;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Exception
     */
    public function handle()
    {

        $currentInfo = $this->user->getOption('bulk_import');

        if($this->user->getOption('bulk_import.job_processing')){

            // handle race conditions
            $lastTimestamp = $this->user->getOption('bulk_import.job_processing_started_at');

            if(!$lastTimestamp) {
                // set current timestamp
                $currentInfo['job_processing_started_at'] = time();
                $this->user->setOption('bulk_import', $currentInfo);
                return;
            }

            // check if job is running for more than 30 minutes
            if(time() - $lastTimestamp > 30 * 60){
                $this->user->setOption('bulk_import', [
                    'status' => 'error',
                    'error' => 'Import was running for more than 30 minutes. Please try again.',
                ]);
                return;
            }

            return;
        } else {

            // start processing
            $currentInfo['job_processing'] = true;
            $currentInfo['job_processing_started_at'] = time();
            // update info
            $this->user->setOption('bulk_import', $currentInfo);

        }

        // set current user for auth - required
        \Auth::setUser($this->user);

        // get file, do bulk import
        $importer = new BulkImport($this->user);
        $error = null;
        try {
            $importer->import($this->file);
        } catch (ValidationException $exception){
            $error = array_first( array_flatten($exception->errors()) );
        } catch (\Exception $exception){
            $error = $exception->getMessage();
        }

        // delete file now
        try {
            \Storage::delete($this->file);
        } catch (\Exception $exception){
            report($exception);
        }

        if($error){
            $this->user->setOption('bulk_import', [
                'status' => 'error',
                'error' => $error,
            ]);
            return;
        }


        // successfully imported
        $this->user->setOption('bulk_import', [
            'status' => 'done',
            'count' => $importer->getCount(),
        ]);

        // clear current user
        \Auth::logout();
    }
}
