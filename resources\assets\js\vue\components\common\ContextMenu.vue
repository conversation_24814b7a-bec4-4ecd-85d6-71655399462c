<template>
	<div class="container border border-2" id="context_menu_container" ref="container" :style="{'display': isOpen ? 'block':'none', 'left': `${position.x}px`,'top': `${position.y}px`}">
        <ul class="item_list" >
            <li  v-for="(option, i) in options" :key="i" @click="handleOptionClicked(option.action)" class="item">{{option.label}}</li>  
        </ul>
	</div>
</template>

<script>
	export default {
        props: ["options", "optionClicked"],
		data() {
			return {
				isOpen: false,
				position: {
					x: 0,
					y: 0,
				},
				data: null,
			};
		},
		methods: {
            handleOptionClicked(action) {
                this.optionClicked(action, this.data);
                this.close();
            },
			open(event, data) {
                //here we need to find dynamic width and height of menu element, right now not working
                //so hard coding the values.
                let menu = document.getElementById('context_menu_container');
                let menuWidth = this.$refs.container.offsetWidth || 200;
                let menuHeight = this.$refs.container.offsetHeight || 132;

				if (event) {
                    //horizontal position
                    if((menuWidth + event.pageX) >= window.innerWidth) {
                        this.position.x = (event.pageX - (menuWidth + 238))
                    } else {
                        this.position.x = event.pageX - 238
                    }

                    //vertical position
                    if ((menuHeight + event.pageY) >= window.innerHeight) {
                        this.position.y = (event.pageY - (menuHeight + 74))
                    } else {
                        this.position.y = event.pageY - 74
                    }
				} 
				this.data = data;
				this.isOpen = true;
			},
			close() {
				this.isOpen = false;
			},
		},
		mounted() {
			document.addEventListener('click', e => {
				if (!this.$refs.container || !this.isOpen)
					return;
				const insideMenu = this.$refs.container.contains(e.target);
				if (!insideMenu)
					this.isOpen = false;
			});
		}
	};
</script>

<style scoped lang="scss">
	.container {
        position: absolute;
        z-index: 1000000;
		padding: 0px;
        max-width: 200px;
        box-shadow: 0 3px 6px 0 rgba(#333, 0.2);
        border-radius: 6px;
        background-color: #ecf0f1;
        .item_list {
            list-style: none;
            padding: 0px;
            .item {
                display: flex;
                color: #333;
                cursor: pointer;
                padding: 5px 15px;
                align-items: center;

                &:hover {
                    font-weight: 600;
                    background-color: #c3c7c7;
                    // color: #fff;
                }
            }
        }
	}
</style>