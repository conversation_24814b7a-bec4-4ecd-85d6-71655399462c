<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Str;

class SocialMediaTools extends Controller
{
    public function loadAllTools(){
        // load the data from csv and cache it
        if(app()->environment() !== 'production' || !\Cache::has('social_media_tools_csv')){
            $data = [];
            $file = fopen(resource_path('new_social_tools_data.csv'), 'r');

            // Read the CSV file line by line
            while (($row = fgetcsv($file)) !== false) {
                $data[] = $row;
            }

            array_walk($data, function(&$a) use ($data) {
                // make sure both arrays are same length
                $a = array_pad($a, count($data[0]), "");

                // associate array with keys and values
                $a = array_combine($data[0], $a);
            });

            array_shift($data); # remove column header

            // boolean keys
            $boolKeys = ['has_free_plan', 'has_free_trial', 'logo_is_white', 'has_teams', 'has_post_approvals', 'has_publish_from_rss', 'live_chat'];
            // array of text elems by comma
            $arrayText = ['networks', 'best_for_industry'];

            // modify
            foreach ($data as &$row){
                $row['slug'] = str_slug($row['name']);
                foreach ($boolKeys as $k) {
                    if(empty($row[$k])) $row[$k] = 'No';
                    if(in_array($row[$k], ['Yes','No']))
                        $row[$k] = $row[$k] === 'Yes';
                    if(strlen($row[$k]) > 1){
                        $exploded = explode(',', $row[$k]);
                        $bool = in_array($exploded[0], ['Y', 'Yes']);
                        $row[$k . '_notes'] = explode('.', $exploded[1]);
                        $row[$k . '_notes'] = array_map(function($v){
                            return ucfirst(trim($v));
                        }, $row[$k . '_notes']);
                        $row[$k] = $bool;
                    }
                }

                foreach($arrayText as $k){
                    $row[$k] = array_filter(array_map(function($v){
                        $v = trim($v);
                        if(strlen($v) < 2) return null;
                        return $v;
                    }, explode(',', $row[$k])));
                }
                
                $row['starting_price'] = (function($price) use(&$row){
                    $exploded = explode(',', $price);
                    if(count($exploded) > 1) {
                        $row['starting_price_notes'] = array_map(function ($v) {
                            return ucfirst(trim($v));
                        }, explode('.', $exploded[1]));
                    }
                    return $exploded[0];
                })($row['starting_price']);
            }

            \Cache::put('social_media_tools_csv', $data, now()->addDays(7));
        } else {
            $data = \Cache::get('social_media_tools_csv');
        }
        return $data;
    }
    public function loadToolsDataByCategory(){
        $csv = null;

        if(app()->environment() !== 'production' || !\Cache::has('tools_content_by_category_csv')){
            $markdownData = [];

            $markdownFile = fopen(resource_path('tools_content_by_category.csv'), 'r');

            while (($row = fgetcsv($markdownFile)) !== false) {
                $markdownData[] = $row;
            }


            array_walk($markdownData, function(&$a) use ($markdownData) {
                // make sure both arrays are same length
                $a = array_pad($a, count($markdownData[0]), "");
                $a = array_combine($markdownData[0], $a);
            }); 

            array_shift($markdownData); # remove column header

            $categorized_tools = [];
            foreach($markdownData as $item){
                $categorized_tools[strtolower($item['name'])] = $item;
            }

            \Cache::put('tools_content_by_category_csv', $categorized_tools, now()->addDays(7));

            return $categorized_tools;
        } else {
            $csv = \Cache::get('tools_content_by_category_csv');
            return $csv;
        }
    }
    public function loadComparisons(){
        // load the data from csv and cache it
        $csv = null;
        if(app()->environment() !== 'production' || !\Cache::has('res/social_media_comparisons_csv')){
            $csv = array_map(function ($csvLine){
                return str_getcsv($csvLine, ',', '"');
            }, file(resource_path('social_media_comparisons.csv')));
            array_walk($csv, function(&$a) use ($csv) {
                // make sure both arrays are same length
                $a = array_pad($a, count($csv[0]), "");

                // associate array with keys and values
                $a = array_combine($csv[0], $a);
            });
            array_shift($csv); # remove column header

            $dataBySlug = [];
            foreach ($csv as $row){
                $dataBySlug[$row['slug']] = $row;
            }

            \Cache::put('res/social_media_comparisons_csv', $dataBySlug, now()->addDays(7));

            return $dataBySlug;
        } else {
            $csv = \Cache::get('res/social_media_comparisons_csv');
            return $csv;
        }
    }
    public function list(){
        $list = $this->loadAllTools();
        return view('common.social_media_tools.list', [
            'list' => $list,     
        ]);
    }
    public function facebook(){
        return $this->networkSpecific('facebook');
    }
    public function twitter(){
        return $this->networkSpecific('twitter');
    }
    public function linkedin(){
        return $this->networkSpecific('linkedin');
    }
    public function instagram(){
        return $this->networkSpecific('instagram');
    }
    public function networkSpecific($network){
        $network = strtolower($network);
        if(!in_array($network, ['facebook', 'twitter', 'linkedin', 'instagram', 'google-my-business', 'google-plus', 'telegram', 'youtube', 'yelp', 'vk', 'pinterest'])){
            abort(404);
        }
        $network = str_slug($network, ' ');
        $data = collect($this->loadAllTools())->filter(function($tool) use($network){ return Str::contains(strtolower(implode(',', $tool['networks'])), $network); });
        if($data->count() === 0) abort(404);
        return view('common.social_media_tools.network_specific',[
            'list' => $data,
            'network' => $network,
        ]);
    }
    public function cheapest(){
        $data = collect($this->loadAllTools())
        ->filter(function($tool) {
            return !empty($tool['cheapest_order']);
        })
        ->sortBy(function($tool) {
            if($tool['name'] === 'SocialBu') return 0; // socialbu should always be on top

            return (float)str_replace('$', '', $tool['cheapest_order']); //price is like $5.99, we need to sort by 5.99
        });
        return view('common.social_media_tools.cheapest', [
            'list' => $data,
        ]);
    }
    public function free(){
        $all_tools = collect($this->loadAllTools())->filter(function($tool){
            return $tool['has_free_plan'];
        });
        
        $categorized_tools = $this->loadToolsDataByCategory();

        $data = [];
        foreach($all_tools as &$item){
            if(isset($categorized_tools[strtolower($item['name'])]) && strtolower($categorized_tools[strtolower($item['name'])]['category']) === 'free'){
                $item['markdown'] = $categorized_tools[strtolower($item['name'])]['markdown']; // get markdown from the dedicated file
                array_push($data, $item);
            } else {
                array_push($data, $item); // use the existing data
            }
        }

        return view('common.social_media_tools.free',[
            'list' => $data,
        ]);
    }
    public function offeringLiveSupport(){
        $data = collect($this->loadAllTools())->filter(function($tool){ return $tool['live_chat']; });
        return view('common.social_media_tools.offering_live_support',[
            'list' => $data,
        ]);
    }
    public function forIndustry($industry){
        $industry = str_slug($industry, ' ');
        if(!in_array($industry, ['real estate', 'agencies'])) abort(404);
        $data = collect($this->loadAllTools())->filter(function($tool) use($industry){ return in_array($industry, $tool['best_for_industry']) || in_array('all', $tool['best_for_industry']); });
        return view('common.social_media_tools.for_industry',[
            'list' => $data,
            'industry' => $industry,
        ]);
    }
    public function toolAlternatives($name){

        // redirect to our new page
        return redirect('/compare/' . $name . '-alternative');

        $data = $this->loadAllTools();
        $tool = array_first($data, function($tool) use($name){
            return $tool['slug'] === $name;
        });
        if(!$tool || $name === 'socialbu') abort(404);
        return view('common.social_media_tools.tool_alternatives', [
            'list' => array_filter($data, function($tool) use($name){
                return $tool['slug'] !== $name;
            }),
            'tool' => $tool,
        ]);
    }
    public function toolVsTool($name1, $name2){

        if($name1 === $name2) abort(404);

        if($name2 === 'socialbu') abort(404);

        if($name1 !== 'socialbu') {
            $slugs = ([$name1, $name2]);
            sort($slugs);

            if($slugs[0] !== $name1 || $slugs[1] !== $name2) abort(404);
        }

        // redirect to our new page
        if($name1 === 'socialbu') {
            return redirect('/compare/' . $name2 . '-alternative');
        } else {
            return redirect('/compare/' . $name1 . '-vs-' . $name2);
        }

        $data = $this->loadAllTools();
        $tool1 = array_first($data, function($tool) use($name1){
            return $tool['slug'] === $name1;
        });
        $tool2 = array_first($data, function($tool) use($name2){
            return $tool['slug'] === $name2;
        });
        if(!$tool1 || !$tool2) abort(404);

        $page = [
            'title' => $tool1['name'] . ' vs. ' . $tool2['name'],
            'description' => 'A quick overview and the comparison of ' . $tool1['name'] . ' and ' . $tool2['name'] . '. Find out what features are offered by each of them and which one is better.',
            'slug' => $tool1['slug'] . '-vs-' . $tool2['slug'],
        ];
        $page['image'] = 'https://og-image.osamaejaz1.now.sh/%3Cdiv%20style%3D%22color%3A%236699fd%3B%22%3E' . rawurlencode($page['title']) . '%3C%2Fdiv%3E.png?theme=light&md=1&fontSize=100px&images=https://socialbu.com/images/logo-icon.png';

        $tool3 = null;
        if($name1 !== 'socialbu' && $name2 !== 'socialbu'){
            // 3rd one should be socialbu
            $tool3 = array_first($data, function($tool) use($name1){
                return $tool['slug'] === 'socialbu';
            });
            // $page['title'] = $tool1['name'] . ' vs. ' . $tool2['name'] . ' vs. ' . $tool3['name'];
        }

        // find comparison data if found
        // we usually have it for top comparisons
        $comparisons = $this->loadComparisons();

        if(isset($comparisons[$page['slug']])){
            foreach($comparisons[$page['slug']] as $key => $val){
                $page[$key] = $val;
            }
        }

        return view('common.social_media_tools.tool_vs_tool', [
            'tool1' => $tool1,
            'tool2' => $tool2,
            'tool3' => $tool3,
            'page' => $page,
        ]);
    }
}
