<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;

class RequireEmailVerified
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $guard
     * @return mixed
     */
    public function handle($request, Closure $next, $guard = null)
    {
        if (Auth::guard($guard)->check()) {
            // check if user email is verified
            $user = Auth::user();
            if(!$user->verified){
                // redirect to settings page with message
                flash(trans('auth.verify_email_to_continue'), 'error');
                return redirect()->route('settings');
            }
        }
        return $next($request);
    }
}
