<?php

use App\Account;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UsersTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // insert test users
        $test_user_id = DB::table('users')->insertGetId([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('test'),
            'company' => 'Test Company',
            'options' => json_encode([
                'test_key' => 'test_value'
            ])
        ]);
        if($acc = Account::find(5)){
            $acc->user_id = $test_user_id;
            $acc->save();
        }
    }
}
