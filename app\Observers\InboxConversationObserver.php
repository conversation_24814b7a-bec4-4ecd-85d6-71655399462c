<?php

namespace App\Observers;

use App\InboxConversation;

class InboxConversationObserver
{
    public function deleting(InboxConversation $conversation){
        // delete attachments if any
        $attachments = $conversation->getOption('attachments', []);

        if(is_array($attachments) && count($attachments) > 0){
            foreach ($attachments as $attachment) {
                if(\Storage::exists($attachment['path']))
                    \Storage::delete($attachment['path']);
            }
        }

        return true;
    }
}
