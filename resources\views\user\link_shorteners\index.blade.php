@extends('layout.user')
@section('title', 'Link Shorteners | ' . config('app.name'))
@section('content')

    @push('footer_html')
        <script>
            __loadScript("link_shorteners", function (s) {});
        </script>
    @endpush 

    <div class="row">
        <div class="col-12">

            <h3 class="pb-md-4 pb-20">
                Link Shorteners
                <button class="btn btn-light float-right" type="button" data-toggle="modal" data-target="#add_shortener">
                    <div class="d-flex align-items-center">
                        <i class="ph-bold ph-plus mr-2"></i> Connect
                    </div>
                </button>
            </h3>

            @if(empty($link_shorteners) || $link_shorteners->count() == 0)
                <div class="card pt-8" data-tour="id=no_link_shortener_yet;title=Link Shorteners;text=You can connect a link shortener to your social accounts so all the shared links will be shortened using the link shortener;position=top">
                    <div class="card-body p-md-6 p-20">
                        <div class="text-center">
                            <h4 class="d-md-block d-none">Looks like you have not connected a link shortener yet</h4>
                            <h4 class="d-md-none font-weight-600 mb-4">Looks like you have not connected a link shortener yet</h4>
                            <p class="mb-5">
                                Connect a link shortener to automatically shorten all links in your posts. <a href="https://help.socialbu.com/article/258-connecting-your-link-shortener-in-socialbu" data-beacon-article-modal="62de762c86b3a9744247062c">Learn more</a>
                            </p>
                            <button class="btn btn-primary" type="button" data-toggle="modal" data-target="#add_shortener">
                                <i class="ph-bold ph-plus"></i>
                                Connect
                            </button>
                        </div>
                    </div>
                </div>
            @else
                <div class="card border">
                    <div class="card-body p-0">
                        <div class="table-responsive rounded-lg">
                            <table class="table table-hover mb-0">
                                <thead class="card-header">
                                <tr>
                                    <th class="py-3 pl-md-5 pl-4" scope="col">@lang('generic.name')</th>
                                    <th class="py-3" scope="col">@lang('generic.type')</th>
                                    <th class="py-3" scope="col">Accounts</th>
                                    <th class="py-3" scope="col"><div class="d-md-inline-block d-none">Query</div>  Parameters</th>
                                    <th class="py-3 pr-md-5 pr-4" scope="col">@lang('generic.status')</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach($link_shorteners->items() as $shortener)
                                    <tr class="cursor-pointer open_shortener" role="button"
                                        data-json="{{ \App\LinkShortener::transform($shortener) }}">
                                        <td class="pl-5">
                                            {{ $shortener->getName() }}
                                        </td>
                                        <td>{{ $shortener->getType() }}</td>
                                        <td>{{ with($shortener->accounts()->count(), function($v){ return $v > 0 ? $v : '---'; }) }}</td>
                                        <td>{{ with(count($shortener->getQueryParameters()), function($v){ return $v > 0 ? $v : '---'; }) }}</td>
                                        <td class="pr-5" >
                                            <i class="ph ph-md {{ $shortener->active ? "ph-check text-success" : "ph-x text-danger" }}"></i>
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                @if($link_shorteners->hasPages())
                    <div class="d-flex justify-content-end pt-2 pb-0">
                        {{ $link_shorteners->links() }}
                    </div>
                @endif
            @endif
        </div>
    </div>

    <div id="add_shortener" class="modal fade" tabindex="-1">
        <div class="modal-dialog modal-dialog-slideout custom-modal-width right">
            <div class="modal-content p-md-5 px-4 py-20">
                <div class="modal-header p-0 mb-4">
                    <h5 class="modal-title">Connect Link Shortner</h5>
                    <button class="close-button" type="button"  data-dismiss="modal" aria-hidden="true">
                        <i class="ph ph-x ph-md"></i>
                    </button>
                </div>
                <div class="modal-body p-0">
                    <ul class="list-group list-group-flush">

                        <li class="list-group-item py-4 px-0">

                            <a href="{{ route('link_shorteners.auth', ['provider' => 'linkmngr']) }}"
                               class="hide_modal_onclick">
                                <h6 class="font-weight-400 mb-0">
                                    LinkMngr <span class="small-2 badge badge-success font-weight-500 ml-2">recommended</span>
                                </h6>
                            </a>

                        </li>

                        <li class="list-group-item py-4 px-0">

                            <a href="{{ route('link_shorteners.auth', ['provider' => 'bitly']) }}"
                               class="hide_modal_onclick">
                                <h6 class="font-weight-400 mb-0">
                                    Bitly
                                </h6>
                            </a>

                        </li>

                    </ul>
                </div>
                <div class="modal-footer">
                        <p>Need assistance? Take a look at this <a href="https://help.socialbu.com/article/258-connecting-your-link-shortener-in-socialbu" data-beacon-article-modal="62de762c86b3a9744247062c">helpful guide</a>.</p>
                </div>
            </div>
        </div>
    </div>

@endsection
