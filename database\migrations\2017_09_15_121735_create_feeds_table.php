<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateFeedsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('feeds', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->increments('id');

            // feed name e.g. XYZ company // only needed if this is a non-team feed
            $table->string('name');

            // user who created this feed
            $table->integer('user_id')->unsigned()->index();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade')->onUpdate('cascade');

            // this feed will process whatever accounts are set for that team, only one feed for a specific team should exist at a time. no duplicates by team id
            $table->integer('team_id')->nullable()->unsigned()->unique()->index();
            $table->foreign('team_id')->references('id')->on('teams')->onDelete('cascade')->onUpdate('cascade');

            // to store misc. data for the feed (if needed)
            $table->json('options')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('feeds');
    }
}
