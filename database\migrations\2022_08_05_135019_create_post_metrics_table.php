<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreatePostMetricsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.insights'))->create('post_metrics', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->bigIncrements('id');

            $table->integer('post_id')->unsigned()->index(); // internal socialbu post id
            $table->integer('account_id')->unsigned()->index(); // internal socialbu account id

            $table->string('metric_type')->index(); // click, reaction, reach, comments, likes, etc
            $table->unsignedBigInteger('metric_value');

            $table->timestamp('timestamp')->useCurrent()->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(config('database.insights'))->dropIfExists('post_metrics');
    }
}
