<?php

namespace App\Helpers;


use App\Automation;
use Illuminate\Support\Str;

class AutomationCondition
{

    protected $matches = null;

    /**
     * AutomationCondition constructor.
     * @param array $conditions
     * @param array $data
     * @param string $operator
     */
    function __construct(array $conditions, array $data, $operator = 'AND')
    {
        if(count($conditions) === 0) {
            // no conditions, so pass by default
            $this->matches = true;
            return;
        }
        foreach ($conditions as $condition) {

            if (isset($condition['conditions']) && isset($condition['operator'])) {
                if (!(new AutomationCondition($condition['conditions'], $data, $condition['operator']))->matches()) {
                    // if any one of it fails to match, set as false and stop further checking
                    $this->matches = false;
                    break;
                } else {
                    $this->matches = true;
                }
            } else {
                // check individual condition
                if (!isset($condition['value'])) $condition['value'] = null;
                $subject = array_get($data, $condition['key'], null);
                if ($subject === null) {
                    // nothing to compare
                    if(!array_has($data, $condition['key'])) {
                        // data point is not set
                        \Log::info('[AutomationCondition] No data point for `' . $condition['key'] . '`: ' . json_encode(['conditions' => $conditions, 'data' => $data]));
                    }
                    $this->matches = false;
                    break;
                }
                $method = camel_case($condition['operator']);
                $ret = false;
                if(method_exists($this, $method))
                    $ret = $this->{$method}($subject, $condition['value']);

                //\Log::info($subject . ' ' . $condition['operator'] . ' ' . $condition['value'] .' => ' . $ret);

                if(!$ret){
                    if($operator === 'AND'){
                        $this->matches = false;
                        break;
                    } else if($operator === 'OR'){
                        if($this->matches === null) // if its not already set
                            $this->matches = false;
                    }
                } else {
                    if($operator === 'AND'){
                        if($this->matches === null)
                            $this->matches = true;
                    } else if($operator === 'OR'){
                        // only need one to be true, so return
                        $this->matches = true;
                        break;
                    }
                }

            }
        }
    }

    /**
     * Returns if conditions matched or not
     * @return bool
     */
    public function matches()
    {
        return $this->matches;
    }

    private function is($subject, $value)
    {
        // if $subject is array, compare as json string
        if(is_array($subject)){

            try {
                $json = json_encode($subject);
            } catch (\Exception $e) {
                $json = null;
            }

            if($json !== null){
                return $this->is($json, $value);
            }

            return false;
        }

        return mb_strtolower($subject) == mb_strtolower($value);
    }

    private function isNot($subject, $value)
    {
        return !$this->is($subject, $value);
    }

    private function isGt($subject, $value)
    {
        return $subject > $value;
    }

    private function isLt($subject, $value)
    {
        return $subject < $value;
    }

    private function contains($subject, $value)
    {
        if(is_array($subject)){

            // also match assuming $subject is json string
            try {
                $json = json_encode($subject);
            } catch (\Exception $e) {
                $json = null;
            }

            if($json !== null){
                $matches = $this->contains($json, $value);

                if($matches){
                    return true;
                }
            }

            return in_array(mb_strtolower($value), array_map('mb_strtolower', $subject));
        } else {
            return Str::contains(mb_strtolower($subject), mb_strtolower($value));
        }
    }

    private function notContains($subject, $value)
    {
        return !$this->contains($subject, $value);
    }

    private function yes($subject, $value = null)
    {
        return $subject == true;
    }

    private function no($subject, $value = null)
    {
        return !$this->yes($subject);
    }

    private function startsWith($subject, $value){
        return starts_with(mb_strtolower($subject), mb_strtolower($value));
    }

    private function endsWith($subject, $value){
        return ends_with(mb_strtolower($subject), mb_strtolower($value));
    }
}
