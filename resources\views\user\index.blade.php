@extends('layout.user')
@section('title', config('app.name'))
@section('content')

    <div class="row">
        <div class="col-12">
            <h3 class="mb-md-4 mb-3">Welcome back, {{ explode(' ', \Auth::user()->name)[0] }}!</h3>
        </div>
    </div>
    @if($failedPosts + $inactiveAccounts > 0)
        <div class="row">
            <div class="col">
                <div>
                    @if($failedPosts > 0)
                        <a href="/app/publish/posts"><span class="alert alert-danger d-block mb-1">Some posts were not published</span></a>
                    @endif

                    @if($inactiveAccounts > 0)
                        <a href="/app/accounts?show=inactive"><span class="alert alert-danger d-block mb-1">Some accounts are disconnected</span></a>
                    @endif
                </div>
            </div>
        </div>
    @endif
    <div class="row mt-md-4 mt-3">
        <div class="col pr-0 mb-4">
            <a href="{{ route('feeds.index') }}">
                <div class="card border">
                    <div class="card-body p-20">
                        <h4>
                            {{ $unreadFeeds }}
                        </h4>
                        <p class="mb-0">Unread Feeds</p>
                        <br class="d-md-none">
                    </div>
                </div>
            </a>
        </div>
        <div class="col pr-md-0 pl-md-4 pl-3 mb-4">
            <a href="{{ route('publish.posts') }}">
                <div class="card border">
                    <div class="card-body p-20">
                        <h4>
                            {{ $pendingPosts }}
                        </h4>
                        <p class="mb-0">Scheduled Posts</p>
                        <br class="d-md-none">
                    </div>   
                </div>
            </a>
        </div>
        <div class="col pr-0 mb-4">
            <a href="{{ route('automations.index') }}">
                <div class="card border">
                    <div class="card-body p-20">
                        <h4>
                            {{ $automations }}
                        </h4>
                        <p class="mb-0">Total Automations</p>
                    </div>
                </div>
            </a>
        </div>
        <div class="col mb-4">
            <a href="{{route('settings')}}">
                <div class="card border">
                    <div class="card-body p-20">
                        <h4>{{ \Auth::user()->getPlan()['name'] }}</h4>
                        <p class="mb-0">Current Plan</p>
                        <br class="d-md-none">
                    </div>
                </div>
            </a>
        </div>
    </div>
    <div class="row mt-md-4 mt-3">
        <!-- show tip of the day -->
        <div class="col-12 col-md-6 pr-md-2 mb-md-4 mb-3">
            <div class="card border">
                <div class="card-body p-20">
                    <div>
                        <h4>A tip for you</h4>
                        <p class="mb-0">
                            @php(
                                    $tips = [
                                        'You can use data variables in automation actions. For example, you can use <code>@{{ content }}</code> which will be replaced with content of the message or post (depending on the event).',
                                        'Automations which are triggered via webhook can access "request data". For example, if you post "body" to the webhook endpoint for your automation you can access it using <code>@{{ body }}</code>',
                                        'You can create automations for replies on a specific facebook post by adding a condition to match the Post ID.',
                                        'Don\'t make your audience feel like they are interacting with a bot. You were born a human so be one.',
                                        'Use Feeds to engage with your audience. The feed is updated with messages and posts from your audience so you can respond effectively.',
                                        'SocialBu was initially started by a single developer.',
                                        'SocialBu is the first product of Glaxosoft, the company behind SocialBu.',
                                        'An apple a day keeps the doctor away.',
                                        'Earth is not flat.',
                                        'You can suggest us some good tips to show here.',
                                        'You can enable or disable an Automation but you cannot do the same with a Feed.',
                                        'We will be very happy to receive your feedback. So what are you waiting for?',
                                        'Criticism is an opportunity.',
                                        'If you are a blogger, write a review about SocialBu and get credits to use on SocialBu plans',
                                        'Team Feeds are available to every member of a Team. You can create a team Feed by selecting your Team instead of Accounts for the Feed.',
                                        'Automations created by team members involving accounts shared by you will also appear in your SocialBu account and you have full access over them.',
                                        'You can schedule and publish posts to multiple social media accounts using SocialBu. More features and social media integrations are coming soon.',
                                        'If you want to programmatically post status updates, Automation triggered by <code>Webhook</code> can help.',
                                    ]
                                )
                            {!! array_random($tips) !!}
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 pl-2 mb-4">
            <div class="card border" id="quote_container">
                <div class="card-body p-20">
                    @push('footer_html')
                        <script>
                            $(document).ready(function(){
                                try {
                                    var url = 'https://api.forismatic.com/api/1.0/?method=getQuote&lang=en&format=jsonp&jsonp=?';
                                    $.getJSON(url, function (data) {
                                        var quoteX = data.quoteText;
                                        var authorX = data.quoteAuthor;
                                        if (quoteX && authorX) {
                                            $("#quote_body").html(quoteX);
                                            $("#quote_author").html(authorX);
                                            $("#quote_container").fadeIn(400);
                                        }
                                    });
                                } catch (e) {
                                    // do nothing
                                }
                            });
                        </script>
                    @endpush
                    <div>
                        <h4 class="mb-2">Quote of the day</h4>
                        <p class="mb-0" id="quote_body">
                            “When you see a man of worth, think of how you may emulate him. When you see one who is unworthy, examine yourself.”
                        </p>
                        <span id="quote_author"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
