<?php

namespace App\Notifications;

use App\User;
use App\InboxConversation;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class ConversationAssigned extends Notification
{
    use Queueable;

    protected $conversation;
    protected $assignedBy;

    /**
     * Create a new notification instance.
     *
     * @param InboxConversation $conversation
     * @param User|null $assignedBy null if the system assigned the conversation
     */
    public function __construct(InboxConversation $conversation, User $assignedBy = null)
    {
        $this->conversation = $conversation;
        $this->assignedBy = $assignedBy;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $contactName = $this->conversation->contact ? $this->conversation->contact->name : 'name unavailable';
        $accountName = $this->conversation->account ? $this->conversation->account->name : 'name unavailable';
        $conversationUrl = url('/app/inbox/' . $this->conversation->id);

        $assignedByName = $this->assignedBy ? $this->assignedBy->name : 'SocialBu';

        return (new MailMessage)
            ->subject('A conversation has been assigned to you!')
            ->greeting('Hello ' . explode(' ', trim($notifiable->name))[0] . ',')
            ->line($assignedByName . ' has assigned you a conversation.')
            ->line('Contact: ' . $contactName)
            ->line('Account: ' . $accountName)
            ->line('Status: ' . ucfirst($this->conversation->status))
            ->action('View Conversation', $conversationUrl)
            ->line('Please respond to the conversation as soon as possible. Thank you!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        $assignedByName = $this->assignedBy ? $this->assignedBy->name : 'SocialBu';

        return [
            'assigned_by_id' => $this->assignedBy ? $this->assignedBy->id : null,
            'assigned_by_name' => $assignedByName,
            'conversation_title' => $this->conversation->getTitle(),
            'conversation_id' => $this->conversation->id,
            'contact_name' => $this->conversation->contact ? $this->conversation->contact->name : null,
            'account_name' => $this->conversation->account ? $this->conversation->account->name : null,
            'status' => $this->conversation->status,
        ];
    }
}
