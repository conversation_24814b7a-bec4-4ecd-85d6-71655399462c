<?php

namespace App\Helpers;


use App\Account;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Twitter\Text\Extractor;
use DonatelloZa\RakePlus\RakePlus;


class Hashtags
{
    /**
     * @param Collection|array|Account[] $accounts
     * @param string $text
     * @return array
     */
    static public function getSuggested($accounts, string $text){
        $guzzle = ContentGenerator::getInstance()->getGuzzle('openai');

        $promptAccounts = '';

        if(count($accounts) > 0){
            $promptAccounts .= 'Social media accounts: ' .  $accounts->map(function($a){
                return $a->name . ' (' . $a->getType() . ')';
            })->join(',') . '\n\n';
        }

        $res = $guzzle->post('https://api.openai.com/v1/chat/completions', [
            'json' => [
                'model' => 'gpt-4o-mini',
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => implode("\n", [
                            $promptAccounts,
                            'Text: ' . $text,
                            '',
                            'Generate 10 highly relevant hashtags for the above text. Write one hashtag per line without anything else on the line.',
                            '',
                            'Hashtags:'
                        ])
                    ]
                ],
                'temperature' => 1.0,
                'max_tokens' => 150,
                'top_p' => 1,
            ]
        ]);

        $body = json_decode($res->getBody()->getContents());

        return collect(explode("\n", $body->choices[0]->message->content))->map(function($line){
            return trim(
                Str::replaceFirst('#', '', $line)
            );
        })->filter(function($line){
            return strlen($line) > 0;
        })->filter(function($line) use($text){
            return !Str::contains(strtolower($text), [
                '#' . strtolower($line),
            ]);
        })->values()->slice(0, 10)->toArray();
    }

    /**
     * Extract hashtags from text
     * @param $text
     * @return array
     */
    static private function extract($text){
        $extractor = new Extractor();
        return $extractor->extractHashtags($text);
    }

    /**
     * Extract keywords from text which can be used as hashtags
     * @param $text
     * @param string $lang
     * @return Collection
     */
    static private function rake($text, $lang = 'en_US'){

        $origText = $text;

        // filter current hashtags from text
        $textParts = explode(' ', $text);
        $currentHashtags = self::extract($text);
        foreach ($textParts as $index => $part){
            if( Str::contains($part, $currentHashtags) ){
                unset($textParts[$index]);
            }
        }
        $text = trim(implode(' ', $textParts));

        // Note: en_US is the default language.
        $rake = RakePlus::create($text, $lang, 3);
        return collect( $rake->sortByScore('desc')->get() )->map(function($text){
            $text = preg_replace('/[^A-Za-z0-9\-]/', '', $text); // Removes special chars.
            return trim(studly_case($text));
        })->filter(function($phrase) use($origText){
            // eliminate hashtags which are already present in $text
            return !Str::contains(strtolower($origText), [
                '#' . strtolower($phrase),
            ]) && strlen($phrase) <= 30;
        })->unique()->values()->slice(0, 5);  // get first 5  items only
    }
}
