<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreatePostsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('posts', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->increments('id'); // post id
            $table->text('content')->nullable(); // actual post content

            $table->string('post_hash')->index(); // hash of this post. will be built using post_contents and user_id and timestamp (created_at)

            $table->string('external_id')->nullable()->default(null); // external id (fb post id or twitter tweet id etc) of this post (will be only set after the post is published)

            $table->integer('account_id')->unsigned()->index(); // account on which this post will be published
            $table->foreign('account_id')->references('id')->on('accounts')->onDelete('cascade')->onUpdate('cascade');

            $table->integer('user_id')->unsigned()->index(); // user who added this post
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade')->onUpdate('cascade');

            $table->string('type', 16)->default('text'); // post type, can be 'image' or something, or 'text' (default)

            $table->string('source', 16)->nullable()->default(null); // source of the post. can be 'null' which means (manual / user) or 'monitor' if it was added by our monitor etc.

            $table->json('options')->nullable(); // a general json column for storing any data if needed

            $table->timestamp('publish_at')->nullable(); // scheduled time on which this post should be sent

            $table->timestamp('published_at')->nullable(); // the time at which the post was sent

            $table->json('result')->nullable(); // to store success or error response of api while publishing this post. this can help to see reason. for pending posts, it will be null otherwise api response for success/failed posts. will always contain _success (true or false) and _errorMsg (in case of success: false). _errorMsg is user friendly error msg

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('posts');
    }
}
