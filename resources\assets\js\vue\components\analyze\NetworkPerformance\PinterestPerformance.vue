<template>
    <div class="row">
        <div class="col-12 mb-8">
            <h5 class="mb-4">Followers Growth</h5>
            <div class="card border" v-if="isLoading('account_metrics')" v-html="spinnerHtml"></div>
            <div class="row" v-else>
                <div class="col-12 col-md-6">
                    <div class="analyze-card card border">
                        <div class="card-body p-20">
                            <div>
                                <h6 class="font-weight-500 mb-6">New Followers</h6>
                                <h3 class="mb-0">{{ totalFollowers }}</h3>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 mt-4">
                    <div class="card border pt-4">
                        <Chart
                            :options="growthChart"
                            ref="chart_followers"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 mb-8">
            <h5 class="mb-4">Pin Clicks</h5>
            <div class="card border" v-if="isLoading('account_metrics_daily')" v-html="spinnerHtml"></div>
            <div class="row" v-else>
                <div class="col-6 col-md-6">
                    <div class="analyze-card card border">
                        <div class="card-body p-20">
                            <div>
                                <h6 class="font-weight-500 mb-6">Total Pin Clicks</h6>
                                <h3 class="mb-0">{{ totalPinClicks }}</h3>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 mt-4">
                    <div class="card border pt-4">
                        <Chart
                            :options="pinClicksChart"
                            ref="chart_pin_clicks"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 mb-8">
            <h5 class="mb-4">Profile Visits</h5>
            <div class="card border" v-if="isLoading('account_metrics_daily')" v-html="spinnerHtml"></div>
            <div class="row" v-else>
                <div class="col-6 col-md-6">
                    <div class="analyze-card card border">
                        <div class="card-body p-20">
                            <div>
                                <h6 class="font-weight-500 mb-6">Total Profile Visits</h6>
                                <h3 class="mb-0">{{ totalProfileVisits }}</h3>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 mt-4">
                    <div class="card border pt-4">
                        <Chart
                            :options="profileVisitsChart"
                            ref="chart_profile_visits"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12">
            <h5 class="mb-4">Content at a Glance</h5>
            <div class="row">
                <div class="col-md-12">
                    <div class="card border">
                        <div class="card-body card-body py-3 px-20">
                            <ul class="d-flex justify-content-md-center justify-content-start flex-nowrap nav border-md-bottom-0 border-bottom-1 navbar-reports overflow-auto hide-scrollbar" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link cursor-pointer"
                                       @click="publishingBehaviorTab = 'overview'"
                                       :class="{'active': publishingBehaviorTab === 'overview'}">Overview</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link cursor-pointer"
                                       @click="publishingBehaviorTab = 'image_posts'"
                                       :class="{'active': publishingBehaviorTab === 'image_posts'}">Image Posts</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link cursor-pointer"
                                       @click="publishingBehaviorTab = 'video_posts'"
                                       :class="{'active': publishingBehaviorTab === 'video_posts'}">Video Posts</a>
                                </li>
                            </ul>
                            <div class="tab-content pt-5 px-4 pb-4">
                                <div class="tab-pane active show">
                                    <template v-if="publishingBehaviorTab === 'overview'">
                                        <div v-if="isLoading('post_types_chart')" v-html="spinnerHtml"></div>
                                        <Chart
                                            key="pie_chart1"
                                            ref="chart_post_types"
                                            :options="postTypesChart"
                                            v-else
                                        />
                                    </template>
                                    <template v-else-if="publishingBehaviorTab === 'image_posts'">
                                        <div
                                            v-if="isLoading('metrics_image') || isLoading('counts_image')"
                                            v-html="spinnerHtml"></div>
                                        <Chart
                                            ref="chart_image_posts"
                                            :options="imagePostsAndEngagementsChart"
                                            v-else
                                        />
                                    </template>
                                    <template v-else-if="publishingBehaviorTab === 'video_posts'">
                                        <div
                                            v-if="isLoading('metrics_video') || isLoading('counts_video')"
                                            v-html="spinnerHtml"></div>
                                        <Chart
                                            ref="chart_video_posts"
                                            :options="videoPostsAndEngagementsChart"
                                            v-else
                                        />
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { axios, axiosErrorHandler, spinnerHtml } from '../../../../components'
import { Chart } from "highcharts-vue";

export default {
    name: 'PinterestPerformance',
    components: {
        Chart
    },
    props: ['filter', 'accounts', 'filterFormOpen'],
    data() {
        return {
            loadingFlags: [],

            accountMetrics: [],
            accountMetricsDaily: [],

            postMetrics: {
                image: [],
                video: [],
            },
            postCounts: {
                image: [],
                video: [],
            },

            publishingBehaviorTab: 'overview',
        }
    },
    computed: {
        spinnerHtml: () => spinnerHtml,
        totalFollowers() {
            let followers = 0;
            this.accountMetrics.forEach((a) => {
                const { metrics } = a;
                (metrics.followers || []).forEach((d) => {
                    followers += d.value;
                });
            });
            return followers;
        },

        totalPinClicks() {
            let total = 0;
            this.accountMetricsDaily.forEach((a) => {
                const {metrics} = a;
                (metrics.pin_click || []).forEach((d) => {
                    total += d.value;
                });
            });
            return total;
        },
        totalImpressions() {
            let total = 0;
            this.accountMetricsDaily.forEach((a) => {
                const {metrics} = a;
                (metrics.impressions || []).forEach((d) => {
                    total += d.value;
                });
            });
            return total;
        },
        totalProfileVisits() {
            let total = 0;
            this.accountMetricsDaily.forEach((a) => {
                const {metrics} = a;
                (metrics.profile_visit || []).forEach((d) => {
                    total += d.value;
                });
            });
            return total;
        },

        // chart configs
        growthChart() {
            const followersData = this.accountMetrics.map((item) => {
                const { metrics } = item;
                const acc = this.getAccount(item.account_id);
                return {
                    name: acc.name + " (" + acc._type + ")",
                    marker: {
                        enabled: false
                    },
                    data: (metrics.followers || []).map(a => [this.timestampForXAxis(a.date), a.value]),
                };
            });

            return {
                chart: {
                    type: "areaspline",
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: "",
                },
                xAxis: {
                    type: "datetime",
                },
                yAxis: {
                    title: {
                        text: "Followers",
                    },
                    labels: {
                        format: '{value}',
                    }
                },
                plotOptions: {
                    series: {
                        fillOpacity: 0.7
                    },
                    area: {
                        marker: {
                            enabled: false, // Simplified markers for area charts
                            symbol: 'circle',
                            radius: 2,
                            states: {
                                hover: {
                                    enabled: true
                                }
                            }
                        }
                    }
                },
                series: followersData,
            };
        },
        pinClicksChart() {
            const seriesData = this.accountMetricsDaily.map((item) => {
                const {metrics} = item;
                const acc = this.getAccount(item.account_id);
                return {
                    name: acc.name + " (" + acc._type + ")",
                    marker: {
                        enabled: false
                    },
                    data: (metrics.pin_click || []).map(a => [this.timestampForXAxis(a.date), a.value]),
                };
            });

            return {
                chart: {
                    type: "areaspline",
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: "",
                },
                xAxis: {
                    type: "datetime",
                },
                yAxis: {
                    title: {
                        text: "Views",
                    },
                    labels: {
                        format: '{value}',
                    }
                },
                plotOptions: {
                    series: {
                        fillOpacity: 0.7
                    },
                    area: {
                        marker: {
                            enabled: false, // Simplified markers for area charts
                            symbol: 'circle',
                            radius: 2,
                            states: {
                                hover: {
                                    enabled: true
                                }
                            }
                        }
                    }
                },
                series: seriesData,
            };
        },
        profileVisitsChart() {
            const seriesData = this.accountMetricsDaily.map((item) => {
                const {metrics} = item;
                const acc = this.getAccount(item.account_id);
                return {
                    name: acc.name + " (" + acc._type + ")",
                    marker: {
                        enabled: false
                    },
                    data: (metrics.profile_visit || []).map(a => [this.timestampForXAxis(a.date), a.value]),
                };
            });

            return {
                chart: {
                    type: "areaspline",
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: "",
                },
                xAxis: {
                    type: "datetime",
                },
                yAxis: {
                    title: {
                        text: "Views",
                    },
                    labels: {
                        format: '{value}',
                    }
                },
                plotOptions: {
                    series: {
                        fillOpacity: 0.7
                    },
                    area: {
                        marker: {
                            enabled: false, // Simplified markers for area charts
                            symbol: 'circle',
                            radius: 2,
                            states: {
                                hover: {
                                    enabled: true
                                }
                            }
                        }
                    }
                },
                series: seriesData,
            };
        },
        imagePostsAndEngagementsChart() {
            const newDataByDate = {};
            Object.values(this.postMetrics.image).forEach((item) => {
                item.forEach(d => {
                    newDataByDate[d.date] = newDataByDate[d.date] || 0;
                    newDataByDate[d.date] += d.value;
                });
            });
            const engagementsData = Object.keys(newDataByDate).map((key) => {
                return [
                    this.timestampForXAxis(key),
                    newDataByDate[key]
                ];
            });
            const postsPublished = this.postCounts.image.map(itm => {
                return [this.timestampForXAxis(itm.date), itm.count];
            });

            return {
                chart: {
                    type: "areaspline",
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: "",
                },
                xAxis: {
                    type: "datetime",
                },
                yAxis: [
                    { // Primary yAxis
                        title: {
                            text: "Engagements",
                        },
                        labels: {
                            format: '{value}',
                        }
                    },
                    { //secondary yAxis
                        title: {
                            text: "Posts Published",
                        },
                        labels: {
                            format: '{value}',
                        },
                        opposite: true
                    }
                ],
                plotOptions: {
                    series: {
                        fillOpacity: 0.7
                    },
                    area: {
                        marker: {
                            enabled: false, // Simplified markers for area charts
                            symbol: 'circle',
                            radius: 2,
                            states: {
                                hover: {
                                    enabled: true
                                }
                            }
                        }
                    }
                },
                series: [
                    {
                        name: 'Engagements',
                        marker: {
                            enabled: false
                        },
                        data: engagementsData,
                    },
                    {
                        name: 'Posts Published',
                        yAxis:1,
                        marker: {
                            enabled: false
                        },
                        data: postsPublished,
                    }
                ],
            };
        },
        videoPostsAndEngagementsChart() {
            const newDataByDate = {};
            Object.values(this.postMetrics.video).forEach((item) => {
                item.forEach(d => {
                    newDataByDate[d.date] = newDataByDate[d.date] || 0;
                    newDataByDate[d.date] += d.value;
                });
            });
            const engagementsData = Object.keys(newDataByDate).map((key) => {
                return [
                    this.timestampForXAxis(key),
                    newDataByDate[key]
                ];
            });
            const postsPublished = this.postCounts.video.map(itm => {
                return [this.timestampForXAxis(itm.date), itm.count];
            });

            return {
                chart: {
                    type: "areaspline",
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: "",
                },
                xAxis: {
                    type: "datetime",
                },
                yAxis: [
                    { // Primary yAxis
                        title: {
                            text: "Engagements",
                        },
                        labels: {
                            format: '{value}',
                        }
                    },
                    { //secondary yAxis
                        title: {
                            text: "Posts Published",
                        },
                        labels: {
                            format: '{value}',
                        },
                        opposite: true
                    }
                ],
                plotOptions: {
                    series: {
                        fillOpacity: 0.7
                    },
                    area: {
                        marker: {
                            enabled: false, // Simplified markers for area charts
                            symbol: 'circle',
                            radius: 2,
                            states: {
                                hover: {
                                    enabled: true
                                }
                            }
                        }
                    }
                },
                series: [
                    {
                        name: 'Engagements',
                        marker: {
                            enabled: false
                        },
                        data: engagementsData,
                    },
                    {
                        name: 'Posts Published',
                        yAxis:1,
                        marker: {
                            enabled: false
                        },
                        data: postsPublished,
                    }
                ],
            };
        },
        postTypesChart() {
            const imagePosts = this.postCounts.image.reduce((a, b) => {
                return a + b.count;
            }, 0);
            const videoPosts = this.postCounts.video.reduce((a, b) => {
                return a + b.count;
            }, 0);
            const totalPosts = imagePosts + videoPosts;
            const data = [
                {
                    name: "Image Posts",
                    y: (imagePosts / totalPosts) * 100,
                },
                {
                    name: "Video Posts",
                    y: (videoPosts / totalPosts) * 100,
                }
            ];
            return {
                chart: {
                    type: "pie",
                    plotBackgroundColor: null,
                    plotBorderWidth: null,
                    plotShadow: false,
                },
                colors:['#FA4B3F','#05D3F0','#FFDC4D'],
                title: {
                    text: '',
                },
                tooltip: {
                    pointFormat: "{series.name}: <b>{point.percentage:.1f}%</b>",
                },
                accessibility: {
                    point: {
                        valueSuffix: "%",
                    },
                },
                series: [{
                    name: '',
                    colorByPoint: true,
                    data
                }]
            }
        },
    },
    async mounted() {
        await this.fetchData();
    },
    methods: {
        isLoading(flag) {
            return this.loadingFlags.includes(flag);
        },
        showLoader(flag) {
            if (this.isLoading(flag)) {
                return;
            }
            this.loadingFlags.push(flag);
        },
        hideLoader(flag) {
            this.loadingFlags = this.loadingFlags.filter(itm => itm !== flag);
        },
        fetchData() {
            this.fetchAccountMetrics();
            this.fetchDailyAccountMetrics();
            this.fetchMetrics('image');
            this.fetchCounts('image');
            this.fetchMetrics('video')
            this.fetchCounts('video');
        },
        async fetchMetrics(type = null) {
            this.showLoader('metrics_' + (type ? type : ''));
            try {
                const {data} = await axios.get(
                    '/api/v1/insights/posts/metrics',
                    {
                        params: {
                            post_type: type,
                            metrics: 'comments,saved,pin_clicks,reactions,video_views',
                            start: this.filter.start,
                            end: this.filter.end,
                            team: this.filter.team,
                            accounts: this.filter.accounts,
                            network: this.filter.network,
                        },
                    },
                );
                this.postMetrics[type ? type : "all"] = data.data;
            } catch (e) {
                axiosErrorHandler(e)
            }
            this.hideLoader('metrics_' + (type ? type : ''));
        },
        async fetchCounts(type = null) {
            this.showLoader('counts_' + (type ? type : ''));
            try {
                const {data} = await axios.get('/api/v1/insights/posts/counts', {
                    params: {
                        post_type: type,
                        start: this.filter.start,
                        end: this.filter.end,
                        team: this.filter.team,
                        accounts: this.filter.accounts,
                        network: this.filter.network,
                    },
                });
                this.postCounts[type ? type : "all"] = data.data;
            } catch (e) {
                axiosErrorHandler(e)
            }
            this.hideLoader('counts_' + (type ? type : ''));
        },
        async fetchAccountMetrics() {
            this.showLoader('account_metrics');
            try {
                const { data } = await axios.get("/api/v1/insights/accounts/metrics",
                    {
                        params: {
                            metrics: ["followers"].join(","),
                            start: this.filter.start,
                            end: this.filter.end,
                            team: this.filter.team,
                            accounts: this.filter.accounts,
                            network: this.filter.network,
                        },
                    });
                this.accountMetrics = data.data;
            } catch (e) {
                axiosErrorHandler(e);
            }
            this.hideLoader('account_metrics');
        },
        async fetchDailyAccountMetrics() {
            this.showLoader('account_metrics_daily');
            try {
                const { data } = await axios.get("/api/v1/insights/accounts/metrics",
                    {
                        params: {
                            metrics: ["pin_click", "impression", "engagement", "profile_visit"].join(","),
                            calculate_growth: false,
                            start: this.filter.start,
                            end: this.filter.end,
                            team: this.filter.team,
                            accounts: this.filter.accounts,
                            network: this.filter.network,
                        }
                    }
                );
                this.accountMetricsDaily = data.data;
            } catch (e) {
                axiosErrorHandler(e);
            }
            this.hideLoader('account_metrics_daily');
        },
        getAccount(id) {
            const all = this.accounts;
            let account = {};
            all.forEach((acc) => {
                if (acc.id === id) account = acc;
            })
            return account;
        },
        timestampForXAxis(timestamp) {
            return this.$momentUTC(timestamp, "YYYY-MM-DD").valueOf();
        },
    },
}
</script>
