<?php

namespace App\Http\Controllers\User;

use App\Account;
use App\Automation;
use App\Http\Controllers\Json\JsonCollectionController;
use App\PublishQueue;
use App\Team;
use App\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Http\Response;
use Illuminate\Support\Str;

class AutomationsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\Factory|Response|\Illuminate\View\View
     */
    public function index()
    {
        return view('user.automations.index');
    }
    public function getAutomations(Request $request){
        
        $per_page = $request->input('per_page', 20);
        $page = $request->input('page', 1);

        $automations = Automation::userAutomations()->paginate($per_page, ['*'], 'page', $page);
        return collect([
            'data' => with($automations, function($itms){
                $_itms = [];
                foreach($itms as $itm){
                    $_itms[] = Automation::transform($itm);
                }
                return $_itms;
            }),
            'currentPage' => $automations->currentPage(),
            'lastPage' => $automations->lastPage(),
            'nextPage' => $automations->hasMorePages() ? $automations->currentPage() + 1 : null,
            'total' => $automations->total(), 
        ]);
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\RedirectResponse
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'description' => 'bail|string|required|max:255',
            'team_id' => 'nullable|integer|exists:teams,id',
        ]);

        $user = user();

        $_user = $user;

        $team = null;
        if($request->input('team_id')){
            // check the limit for the team admin
            /** @var Team $team */
            $team = $user->joinedTeams()->findOrFail($request->input('team_id'));

            if(!$team->hasPermission($user, 'automation.create')){
                abort(403, 'You do not have permission to create an automation for this team');
            }

            $_user = getResourceOwner($team);
        }

        if(getUsage('automations', $team) + 1 > getPlanUsage('automations', $team)){
            // show error
            if($user->id !== $_user->id){
                abort(400, 'Please upgrade the account associated with the team for adding an automation.');
            } else {
                abort(400, 'Please upgrade your account for adding an automation.');
            }
        }

        $atm = new Automation([
            'user_id' => \Auth::id(),
            'description' => $request->input('description'),
        ]);

        if($request->input('team_id')){
            $atm->team_id = $request->input('team_id');
        }

        $atm->event_data = $atm->rules = $atm->options = [];
        $atm->save();

        $atm->log('created', [], user());

        return response()->json(['id' => $atm->id]); 
    }

    /**
     * Show the automation - url is used in some places so just redirect to edit page.
     *
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     * @throws \Illuminate\Validation\ValidationException
     */
    public function show($id)
    {
        return redirect(route('automations.edit', $id));
    }

    /**
     * Edit the automation.
     *
     * @param int $id
     * @return \Illuminate\Contracts\View\Factory|Response|\Illuminate\View\View
     * @throws \Illuminate\Validation\ValidationException
     */
    public function edit($id)
    {
        $automation = Automation::userAutomations()->findOrFail($id);
        $jsonCollectionController = new JsonCollectionController();
        return view('user.automations.edit', [
            'automation' => $automation,
            'accounts' => $jsonCollectionController->accounts(new Request([
                'type' => 'all',
            ])),
            'queues' => $jsonCollectionController->queues(new Request()),
        ]);
    }
    public function duplicate($id){
        
        $automation = Automation::userAutomations()->findOrFail($id);
        
        $duplicate = $automation->replicate(['executed_at']);
        if($duplicate){
            $duplicate->active = false;
            $duplicate ->description = 'Copy of '.$duplicate ->description;
            $duplicate ->save();
            return response()->json(['id' => $duplicate->id]);
        }
        
    }
    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int $id
     * @throws \Exception
     */
    public function update(Request $request, $id)
    {
        $automation = Automation::userAutomations()->findOrFail($id);
        $this->validate($request, [
            'description' => 'required|string',
            'event' => 'required|string|in:incoming_message,account_post,facebook.user_post,facebook.post_reply,facebook.review,twitter.mention,twitter.friend_follows_me,instagram.new_follower,instagram.new_comment,rss_new_item,webhook', // should be valid event type
            'event_data' => 'array',
            'ignore_failure' => 'boolean',
            'rules' => 'required|array|min:1|max:10',
            'rules.*.actions' => 'required|array|min:1',
            'rules.*.actions.*.type' => 'required|string|in:send_reply,send_email,new_post,webhook_request,like_tweet,retweet,send_dm,add_post_to_queue',
        ]);

        // validate event and event_data
        $event = $request->input('event');
        $event_data = $request->input('event_data');
        $EVENT_REQUIRED_OPTIONS = [
            'account' => 'incoming_message,account_post,facebook.user_post,facebook.post_reply,facebook.review,twitter.mention,twitter.friend_follows_me,instagram.new_follower,instagram.new_comment',
            'url' => 'rss_new_item',
        ];
        $evValidator = \Validator::make($event_data, [
            'account' => [
                function($attribute, $value, $fail) {
                    if (!user()->getAvailableAccounts()->firstWhere('id', $value)) { // if account is not valid and existing
                        return $fail($attribute.' is invalid.');
                    }
                },
            ],
            'url' => 'url|active_url', // make sure url is active and valid
        ]);
        foreach ($EVENT_REQUIRED_OPTIONS as $ev_data_key => $ev_types){ // check if required data is set
            if(in_array($event, explode(',', $ev_types))){
                $evValidator->addRules([
                    $ev_data_key => 'required'
                ]);
            }
        }
        $evValidator->validate();

        $user = user();

        $availableAccounts = $user->getAvailableAccounts();

        // validate rules
        $rules = $request->input('rules');
        $ACTION_VALIDATION_RULES = [
            'send_reply' => [
                'content' => 'string|required'
            ],
            'send_email' => [
                'recipients' => 'array|required|min:1',
                'recipients.*' => 'email',
                'subject' => 'string|required',
                'body' => 'string|required',
            ],
            'new_post' => [
                'content' => 'string|required',
                'accounts' => 'array|required|min:1',
                'accounts.*' => [
                    function($attribute, $value, $fail) {
                        if (!user()->getAvailableAccounts()->firstWhere('id', $value)) { // if account is not valid and existing
                            return $fail($attribute.' is invalid.');
                        }
                    }
                ],
                'publish_at' => [
                    'required_if:publish_at_show,true',
                    function($attribute, $value, $fail){
                        if(!$value) return;
                        if( count(placeholders_extract($value)) === 1){
                            // has a placeholder
                        } else if(Str::contains($value, '+')) {
                            // is a relative timestamp string
                            try {
                                modify_relative_time(now(), $value);
                            } catch (\Exception $exception){
                                $fail($exception->getMessage());
                            }
                        } else {
                            $fail($attribute.' (schedule timestamp) is invalid.');
                        }
                    }
                ]
            ],
            'add_post_to_queue' => [
                'content' => 'string|required',
                'queue' => [
                    'required',
                    'integer',
                    function($attribute, $value, $fail) {
                        if (!$value || !PublishQueue::available()->firstWhere('id', $value)) {
                            return $fail($attribute.' is invalid.');
                        }
                    }
                ],
            ],
            'webhook_request' => [
                'url' => 'required|url',
                'method' => 'string|required|in:POST',
                'type' => 'string|required|in:JSON',
                'body' => 'string|required|json', // allow custom body later
            ],
            'like_tweet' => [],
            'retweet' => [],
            'send_dm' => [
                'content' => 'string|required'
            ],

        ];
        $validateConds = function ($conditions) use(&$validateConds){
            foreach($conditions as $condition){
                if(isset($condition['conditions'])){
                    \Validator::make($condition, [
                        'operator' => 'required|string|in:AND,OR',
                        'conditions' => 'required|array|min:1',
                    ])->validate();
                    $validateConds($condition['conditions']);
                } else {
                    // just basic validation for now
                    \Validator::make($condition, [
                        'key' => 'required|string',
                        'operator' => 'required|string',
                    ])->validate();
                }
            }
        };
        foreach($rules as $rule){
            // validation actions
            foreach($rule['actions'] as $action){
                // check if action is correct and has required data
                if($action['type'] === 'send_email') // convert comma-separated emails to email array so that validator can validate
                    $action['data']['recipients'] = array_filter(explode(',', $action['data']['recipients']));

                // maxlength
                if($action['type'] === 'send_reply' || $action['type'] === 'send_dm'){

                    if(!isset($action['data']['content'])){
                        abort(400, 'Content is required for this action');
                    }

                    /** @var Account $account */
                    $account = Account::find($event_data['account']);

                    if($account->type === 'twitter.profile'){
                        // for Twitter, make sure they don't start the tweet with @
                        if(Str::contains($action['data']['content'], '@')){
                            abort(400, 'Content cannot contain @');
                        }
                    }

                    $ACTION_VALIDATION_RULES[$action['type']]['content'] .= '|max:' . (int) $account->getPostLength();

                } else if($action['type'] === 'new_post'){

                    $max_length = null;

                    foreach($action['data']['accounts'] as $id){
                        /** @var Account $account */
                        $account = Account::find($id);

                        if(!$account){
                            abort(400, 'Invalid account state. Please refresh the page and try again.');
                        }

                        if($max_length === null || $max_length > $account->getPostLength())
                            $max_length = $account->getPostLength();

                        if($account->type === 'twitter.profile'){
                            // for twitter, make sure they don't start the tweet with @
                            // $parser = new TweetExtractor();
                            // $result = $parser->extractMentionedScreennames($action['data']['content']);
                            if(starts_with($action['data']['content'], '@')){
                                abort(400, 'Tweet content cannot start with a @');
                            } else if(substr_count($action['data']['content'], '@') > 1){
                                abort(400, 'Tweet content cannot contain multiple @');
                            }
                        }

                    }

                    $ACTION_VALIDATION_RULES[$action['type']]['content'] .= '|max:' . (int) $max_length;
                }

                // validate now
                \Validator::make((array) $action['data'], $ACTION_VALIDATION_RULES[$action['type']], [
                    'publish_at.required_if' => 'Please enter the timestamp for publishing the post',
                ])->validate();
            }
            // validate conditions
            $validateConds($rule['conditions']);
        }

        if(isset($event_data['account'])){ // setup new account
            /** @var Account $acc */
            $acc = Account::find($event_data['account']);
            // add `tag` as it makes it easy to get automations related to specific objects/actions
            $automation->tag = explode('.', $acc->type)[0] . ':' . $event_data['account']; // e.g facebook:ID_HERE
        } else {
            $automation->tag = null;
            if(isset($event_data['url'], $automation->event_data['url'])){
                if($event_data['url'] !== $automation->event_data['url']){
                    // url changed, reset polling data
                    $options = $automation->options;
                    if(isset($options['rss_polling'])){
                        unset($options['rss_polling']);
                        $automation->options = $options;
                    }
                }
            }
        }

        // if we are here, all things good
        $automation->description = $request->input('description');
        if($automation->event != $event && $automation->event != ''){
            // reset options if automation event is changed
            $automation->options = [];
        }
        $automation->event = $event;
        $automation->event_data = $event_data;
        $automation->rules = $rules;

        $automation->save();

        $automation->setOption('ignore_failure', $request->input('ignore_failure', false));

        $automation->log('updated', [], user());
    }


    /**
     * Update the status (active/inactive).
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     */
    public function setStatus(Request $request, $id){
        $automation = Automation::userAutomations()->findOrFail($id);

        if($request->input('active', false) && getUsage('automations', $automation) > getPlanUsage('automations', $automation)){
            // show error
            abort(400, 'Your resource usage is exceeding your current plan limits. Please resolve this issue before enabling the automation.');
        }

        $automation->active = $request->input('active', false);
        $automation->save();
    }

    /**
     * Get automation log
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int $id
     * @return mixed
     */
    public function getLog(Request $request, $id){
        $automation = Automation::userAutomations()->findOrFail($id);
        return $automation->getLogs(15);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse|Response
     * @throws \Exception
     */
    public function destroy(Request $request, $id)
    {
        Automation::userAutomations()->findOrFail($id)->delete();
        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'The automation has been deleted!'
            ]);
        }
        return redirect()->route('automations.index');
        
    }
}
