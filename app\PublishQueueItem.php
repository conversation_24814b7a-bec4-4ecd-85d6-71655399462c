<?php

namespace App;

use App\Traits\HasOptions;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\UploadedFile;
use Mimey\MimeTypes;
use Symfony\Component\HttpFoundation\File\File;

/**
 * App\PublishQueueItem
 *
 * @property int $id
 * @property int $user_id
 * @property string|null $content
 * @property array|null $options
 * @property int $publish_queue_id
 * @property int $times_published
 * @property \Illuminate\Support\Carbon|null $last_published_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\PublishQueue $queue
 * @property-read \App\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueueItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueueItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueueItem query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueueItem whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueueItem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueueItem whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueueItem whereLastPublishedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueueItem whereOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueueItem wherePublishQueueId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueueItem whereTimesPublished($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueueItem whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueueItem whereUserId($value)
 * @mixin \Eloquent
 * @property int|null $order
 * @method static \Illuminate\Database\Eloquent\Builder|PublishQueueItem whereOrder($value)
 */
class PublishQueueItem extends Model
{
    use HasOptions;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id', 'content',
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'options' => 'array',
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'created_at',
        'updated_at',
        'last_published_at',
    ];

    /**
     * Get the user.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the queue
     */
    public function queue(){
        return $this->belongsTo(PublishQueue::class);
    }

    /**
     * @param array|UploadedFile[] $files
     */
    public function addAttachments(array $files){
        $mimes = new MimeTypes;
        $attachments = [];
        foreach ($files as $file) {
            $path = \Storage::putFile('attachments', $file);
            $attMime = $file->getMimeType();
            $attType =  strtolower($mimes->getExtension($attMime));
            $attachments[] = [
                'path' => $path,
                'type' => $attType,
                'name' => str_replace('.tmp', '.' . $attType, $file->getFilename()),
                'mimeType' => $attMime,
            ];
        }
        $this->setOption('attachments', $attachments);
    }





















    protected function getOptions()
    {
        return (array) $this->options;
    }

    protected function setOptions($options)
    {
        $this->options = $options;
        $this->save();
        return $this;
    }
}
