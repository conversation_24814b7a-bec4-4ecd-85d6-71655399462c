<template>
    <div class="row">
        <div class="col-12">
            <div class="row mb-2">
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-end flex-wrap">
                        <div>
                            <div class="d-flex align-items-center">
                                <div>
                                    <h3 class="mb-md-3 mb-2">
                                        Generate
                                        <span v-if="contentType === 'tweet'">
                                            Tweets
                                        </span>
                                        <span v-else-if="contentType === 'linkedin_post'">
                                            LinkedIn Posts
                                        </span>
                                        <span v-else>
                                            Content
                                        </span>
                                    </h3>
                                    <p class="d-md-block d-none mb-4">
                                        Let AI write your next {{ contentTerm }}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="mb-4 pb-md-0 pb-1" :title="'Remaining attempts: ' + (limits.total - limits.used)"
                            v-if="accountId && limits.total > 0">
                            Current Usage {{ teamId ? 'For Selected Team' : '' }}: {{ limits.used }} / {{ limits.total }}
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-body p-0">
                            <div class="row">
                                <div class="col-md-6 col-12">
                                    <form @submit.prevent="generateContent">
                                        <div class="form-group mb-md-6 mb-4 pb-md-0 pb-1">
                                            <label for="content_topic">
                                                Account
                                            </label>
                                            <select v-model="generateFor" class="form-control" data-live-search="true" v-selectpicker>
                                                <optgroup v-for="team in teams" :label="team.name">
                                                    <option v-for="account in accounts.filter(a => a.team_id === team.id)" :value="team.id + '_' + account.id">
                                                        {{ account.name }} ({{ account._type }})
                                                    </option>
                                                </optgroup>
                                                <optgroup label="My Accounts">
                                                    <option v-for="account in accounts.filter(a=>!a.team_id)" :value="'_' + account.id">
                                                        {{ account.name }} ({{ account._type }})
                                                    </option>
                                                </optgroup>
                                            </select>
                                            <small class="form-text small-2">
                                                Choose your
                                                <span v-if="contentType === 'tweet'">Twitter</span>
                                                <span v-else-if="contentType === 'linkedin_post'">LinkedIn</span>
                                                account so that AI can write specifically for your account
                                            </small>
                                        </div>
                                        <div class="form-group mb-md-6 mb-5" v-if="accountId && !userHasAccess">
                                            <div class="alert alert-warning border border-warning" role="alert">
                                                <h4 class="alert-heading">
                                                    <i class="ph ph-warning"></i>
                                                    Upgrade Required
                                                </h4>
                                                <p class="lead">
                                                    AI powered content generation is not available for the selected {{ teamId ? 'team' : 'account'}}.
                                                    <template v-if="teamId">
                                                        Please ask your team administrator to upgrade the account.
                                                    </template>
                                                    <template v-else>
                                                        Please upgrade your SocialBu account.
                                                    </template>
                                                </p>
                                                <a class="alert-link text-uppercase" target="_blank" href="/app/settings#billing"
                                                 v-if="!teamId">
                                                    Click Here To Upgrade Your Subscription
                                                </a>
                                            </div>
                                        </div>
                                        <div class="form-group mb-md-6 mb-5">
                                            <label for="content_topic">Topic</label>
                                            <input required type="text" class="form-control" id="content_topic" placeholder="Topic to write the content for" v-model="topic" />
                                            <small class="form-text">
                                                Try to write niche topics. A general topic may give you a generic {{ contentTerm }}.
                                            </small>
                                        </div>
                                        <div class="form-group text-center mb-6 pb-2 pt-md-2">
                                            <button class="btn btn-primary btn-sm" type="submit"
                                            :disabled="generating">
                                                <span v-if="generating">
                                                    <i class="ph ph-circle-notch ph-spin text-white"></i>
                                                </span>
                                                <span v-else>
                                                    {{ contentType === 'tweet' ? 'Write X Post' : contentType === 'linkedin_post' ? 'Write LinkedIn Post' : contentType === 'instagram_caption' ? 'Write IG Caption' : contentType === 'generic' ? 'Write Short Post' : 'write' }}
                                                </span>
                                            </button>
                                        </div>
                                    </form>
                                </div>
                                <div class="col-md-6 col-12 border-secondary rounded-md bg-light p-4">
                                    <template v-if="suggestions.length">

                                        <h5 class="font-weight-500">Suggested Topics</h5>
                                        <div class="list-group list-group-flush">
                                            <div class="list-group-item cursor-pointer py-3 px-0 bg-light"
                                                v-for="t in suggestions">
                                                <a class="text-secondary" href="#"
                                                    v-text="t"
                                                    @click.prevent="topic = t">
                                                </a>
                                            </div>
                                        </div>

                                    </template>
                                    <div class="h-100 d-flex align-items-center justify-content-center" v-else>
                                        <div>
                                            Topic suggestions will show here when available
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-12">
                                    <h6>Generated Content</h6>
                                    <div class="card border border-secondary content-card">
                                        <div class="card-body p-4">
                                            <div class="position-absolute d-md-block d-none" style="top: 10px; right: 10px;"
                                                v-if="output && !generating">
                                                <button class="btn btn-sm"
                                                        title="Create post"
                                                        v-tooltip
                                                        @click="createPost(output)">
                                                    <i class="ph ph-note-pencil ph-md"></i>
                                                </button>
                                            </div>
                                            <div
                                            v-if="generating" :key="'robot_icon_generating'">
                                                
                                                <div class="ml-4" data-title="dot-flashing" title="AI is thinking">
                                                    <div class="dot-flashing"></div>
                                                </div>

                                            </div>
                                            <div v-else class="text-light w-90" :class="{'text-dark': output }">
                                                <nl2br :text="output || 'The content written by AI will show here...'" tag="div"></nl2br>
                                            </div>
                                            <div class="d-md-none mt-2"
                                                v-if="output && !generating">
                                                <i class="ph ph-note-pencil ph-md" @click="createPost(output)"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row mt-6 pt-2" v-if="data.total || loading">
                <div class="col-12">
                    <div v-if="loading" v-html="spinnerHtml"></div>
                    <div class="card" v-else>
                        <div class="card-body p-0">
                            <h3>
                                History
                            </h3>
                            <p class="mb-5">
                                <span v-if="contentType === 'tweet'">
                                    Tweets
                                </span>
                                <span v-else-if="contentType === 'linkedin_post'">
                                    LinkedIn posts
                                </span>
                                <span v-else>
                                    Posts
                                </span>
                                that were previously generated
                            </p>
                            <div class="row" v-for="item in data.items">
                                <div class="col-md-4">
                                    <div class="d-flex">
                                        <i class="ph ph-user ph-fill ph-md mr-3 user-icon"></i>
                                        <p class="mb-4 pb-md-0 pb-1">{{ item.keyword }}</p>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div class="d-flex">
                                        <img class="w-20px h-20px mr-2" src="/images/logo-icon.png">
                                        <nl2br :text="item.content" tag="div" />
                                    </div>
                                    <div class="text-md-right text-left mb-md-5 mb-6 mt-md-0 mt-2 ml-md-0 ml-6">
                                        <i class="d-md-none ph ph-copy ph-md mr-4 pr-1" @click="copyPost(item.content)"></i>
                                        <i class="cursor-pointer ph ph-pencil-simple-line ph-md mr-4 pr-1" title="Create a post"
                                            :class="{'invisible': item.is_bad }"
                                            @click="createPost(item.content)"
                                            v-tooltip></i>
                                        <i class="cursor-pointer ph ph-thumbs-down ph-md" title="Flag content"
                                            :class="{'text-danger': item.is_bad, 'text-muted': !item.is_bad }"
                                            @click="flagContent(item.id)"
                                            v-tooltip></i>
                                    </div>

                                </div>
                            </div>

                            <div v-if="data.lastPage > 1">
                                <!-- pagination -->
                                <ul class="pagination justify-content-end">
                                    <li class="page-item" :class="{active: data.currentPage === n}" v-for="n in $pagination(data.currentPage, data.lastPage)">
                                        <a class="page-link" v-if="n==='...'">{{ n }}</a>
                                        <a class="page-link" href="#" @click.prevent="navigateToPage(n)" v-else>{{ n }}</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { alertify, axios, axiosErrorHandler, spinnerHtml } from "../../components";
import { getAccounts } from "../../data";
import { truncate, debounce, shuffle, uniq } from "lodash";
import create_content from "../../scripts/create_content";
import copy from 'copy-to-clipboard';

export default {
    name: "GeneratedContent",
    data() {
        return {
            loading: false,
            generating: false,

            type: null, // tweet, linkedin_post, generic

            availability: {
                allowedTeams: [], // array of team ids that have this feature
                userAllowed: false, // whether user is allowed to use this feature on their own
                limits: {
                    teams: {
                        // id: {used, total}
                    },
                    user: {
                        // id: {used, total}
                    }
                }
            },

            generateFor: null, // teamId_accountId or _accountId
            topic: null,
            output: "",

            suggestedUserTopics: [], // topics extracted from user content
            suggestedKeywords: [], // topics from search keywords
            suggestedHistoryKeywords: [], // topics from history

            accounts: [],

            data: {
                items: [],
                currentPage: 1,
                lastPage: 1,
                total: 0
            }
        };
    },
    computed: {
        spinnerHtml: () => spinnerHtml,
        contentType() {
            return this.type || "generic";
        },
        contentTerm(){
            return this.contentType === 'generic' ? 'post' : this.contentType.replace(/_/g, ' ');
        },
        teams() {
            const teamsById = {};
            this.accounts.filter(a => a.team).forEach(a => {
                teamsById[a.team.id] = a.team;
            });
            return Object.values(teamsById);
        },
        accountId(){
            const parts = (this.generateFor || "").split("_");
            if(parts.length > 1){
                return Number(parts[1]);
            }
            return null;
        },
        teamId(){
            const parts = (this.generateFor || "").split("_");
            if(parts.length > 1){
                return parts[0] ? Number(parts[0]) : null;
            }
            return null;
        },
        suggestions(){
            return uniq(shuffle([
                ...this.suggestedUserTopics.slice(0, 2),
                ...this.suggestedKeywords,
                ...this.suggestedHistoryKeywords.slice(0, 2),
            ])).slice(0, 5);
        },
        userHasAccess(){
            if(this.teamId){
                return this.availability.allowedTeams.includes(this.teamId);
            } else if(this.accountId) {
                return this.availability.userAllowed;
            }
            return true;
        },
        limits(){
            if(this.teamId){
                return this.availability.limits.teams[this.teamId];
            } else {
                return this.availability.limits.user;
            }
        },
    },
    watch: {
        generateFor(v){
            if(v){
                this.loadSuggestedUserTopics();
                this.loadSuggestedHistoryKeywords();
            } else {
                this.suggestedUserTopics = [];
                this.suggestedHistoryKeywords = [];
            }
        },
        topic(v){
            if(v){
                this.loadSuggestedKeywords();
            } else {
                this.suggestedKeywords = [];
            }
        }
    },
    methods: {
        truncate,
        navigateToPage(page) {
            this.load(page);
        },
        async initialize(type = "generic", availability){
            this.type = type;
            this.availability = availability;
            await this.load();
            this.accounts = (await getAccounts()).filter(a => {
                if(this.contentType === "tweet"){
                    return a.type === "twitter.profile";
                } else if(this.contentType === "linkedin_post"){
                    return a.type.includes("linkedin");
                } else if(this.contentType === "instagram_caption"){
                    return a.type.includes("instagram");
                }else if(this.contentType === "tiktok_caption"){
                    return a.type.includes("tiktok")
                }else if(this.contentType === "facebook_post"){
                    return a.type.includes("facebook")
                }else if(this.contentType === "youtube_video_description"){
                    return a.type.includes("google.youtube")
                }else if(this.contentType === "reddit_post"){
                    return a.type.includes("reddit")
                }else if(this.contentType === "pinterest_pin"){
                    return a.type.includes("pinterest")
                }else if(this.contentType === "google_business_profile_post"){
                    return a.type.includes("google.location")
                }else if(this.contentType === "mastodon_post"){
                    return a.type.includes("mastodon")
                }else {
                    return true;
                }
            });
        },
        async load(page = 1) {
            if (this.loading) return;
            this.loading = true;
            try {
                const { data } = await axios.get(
                    "/api/v1/generated_content?page=" + page + "&type=" + this.contentType
                );
                this.data = data;
            } catch (e) {
                axiosErrorHandler(e);
            }
            this.loading = false;
        },
        async loadSuggestedUserTopics() {
            if(this.accountId){
                try {
                    const { data } = await axios.get("/api/v1/generated_content/suggested_topics_by_account?account_id=" + this.accountId );
                    if(data.length) {
                        this.suggestedUserTopics = Array.from(data).slice(0, 10);
                    } else if(this.suggestedUserTopics.length > 0) {
                        this.suggestedUserTopics = [];
                    }
                } catch (e) {
                    console.error(e);
                }
            }
        },
        /** @type {Function} */
        loadSuggestedKeywords: debounce(async function(){
            if(this.topic && this.topic.length > 3){
                try {
                    const { data } = await axios.get("/api/v1/generated_content/suggested_topics_by_search?keywords[]=" + encodeURIComponent(this.topic) + (this.accountId ? "&account_id=" + this.accountId : ""));
                    if(data.length){
                        this.suggestedKeywords = Array.from(data).slice(0, 10);
                    } else if(this.suggestedKeywords.length > 0) {
                        this.suggestedKeywords = [];
                    }
                } catch (e) {
                    console.error(e);
                }
            }

        }, 1500),
        async loadSuggestedHistoryKeywords(){

            if(this.accountId){
                try {
                    const { data } = await axios.get("/api/v1/generated_content/suggested_topics_by_history?account_id=" + this.accountId);
                    if(data.length){
                        this.suggestedHistoryKeywords = Array.from(data).slice(0, 10);
                    } else if(this.suggestedHistoryKeywords.length > 0) {
                        this.suggestedHistoryKeywords = [];
                    }
                } catch (e) {
                    console.error(e);
                }
            }

        },
        async generateContent(){
            if(this.generating){
                return;
            }

            this.generating = true;
            this.output = "";
            try {
                if(!this.accountId){
                    throw new Error("Please select an account");
                }
                const { data } = await axios.post("/api/v1/generated_content", {
                    type: this.contentType,
                    topic: this.topic,
                    account_id: this.accountId,
                    team_id: this.teamId,
                });

                // append limit
                try {
                    if (this.teamId) {
                        ++this.availability.limits.teams[this.teamId].used;
                    } else {
                        ++this.availability.limits.user.used;
                    }
                } catch (e) {
                    console.error(e);
                }

                this.output = data.content;

                this.data.items.unshift(data);
                ++this.data.total;

            } catch (e) {
                axiosErrorHandler(e);
            }
            this.generating = false;
        },
        copyPost(post){
            copy(post);
        },
        async createPost(text){
            (await create_content.getPostComposer()).content = text;
            await create_content.newPost();
        },
        async flagContent(id){
            const data = this.data.items.find(i => i.id === id);
            try {
                await axios.patch("/api/v1/generated_content/" + id, {
                    is_bad: !data.is_bad
                });
                data.is_bad = !data.is_bad;
                this.data = {... this.data};
            } catch (e) {
                axiosErrorHandler(e);
            }
        }
    }
};
</script>

<style lang="scss" scoped>

.content-card{
    min-height: 140px;
}

.dot-flashing {
    position: relative;
    width: 12px;
    height: 12px;
    border-radius: 8px;
    background-color: #0557F0;
    animation: dot-flashing 1s infinite linear alternate;
    animation-delay: 1s;
  }
  .dot-flashing::before, .dot-flashing::after {
    content: "";
    display: inline-block;
    position: absolute;
    top: 0;
  }
  .dot-flashing::before {
    left: -15px;
    width: 12px;
    height: 12px;
    border-radius: 8px;
    background-color: #0557F0;
    animation: dot-flashing 1.5s infinite alternate;
    animation-delay: 0s;
  }
  .dot-flashing::after {
    left: 15px;
    width: 12px;
    height: 12px;
    border-radius: 8px;
    background-color: #0557F0;
    animation: dot-flashing 1.5s infinite alternate;
    animation-delay: .5s;
  }
  
  @keyframes dot-flashing {
    0%, 100% {
      background-color: #ffffff;
    }
    50% {
      background-color: #0557F0;
    }
  }
  .user-icon{
    color: #98A1B2;
  }
</style>
