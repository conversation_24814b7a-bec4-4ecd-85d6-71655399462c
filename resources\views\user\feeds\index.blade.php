@extends('layout.user')
@section('title', 'Feeds | ' . config('app.name'))
@section('content')

    <div class="row">
        <div class="col-12 mb-4">
            <h3 class="mb-0">
                @lang('generic.feeds')
                <button class="btn btn-light btn-sm float-right" type="button"
                        data-toggle="modal" data-target="#add_feed_modal">
                    <div class="d-flex align-items-center">
                        <i class="ph-bold ph-plus mr-2" title="New Feed"></i> New Feed
                    </div>
                </button>
            </h3>
        </div>
        <div class="col-12">
            <div id="add_feed_modal" class="modal text-left" role="dialog">
                <div class="modal-dialog">

                    <div class="modal-content rounded-xl">
                        <div class="modal-header p-6">
                            <h5 class="modal-title">New Feed</h5> 
                            <button type="button" class="close" data-dismiss="modal"><i class="ph ph-x ph-md"></i></button>
                        </div>
                        <div class="modal-body">
                            <form id="new_feed_form" action="" method="POST">
                                {{ csrf_field() }}

                                <div class="form-group mb-5">
                                    <input name="name" class="form-control input-lg" title="Name" placeholder="Enter your Feed name" />
                                </div>

                                @if(count(user()->joinedTeams) > 0)
                                    <div class="form-group mb-6" data-tour="id=createfeed_team;text=If you are creating ths Feed for a team, select the team.">
                                        <label>
                                            Assign to a team (optional)
                                        </label>
                                        <select class="form-control" name="team_id" title="Choose a team if you want to assign this Feed to a team">
                                            <option value="">---</option>
                                            @foreach(user()->joinedTeams as $team)
                                                <option value="{{ $team->id }}">
                                                    {{ $team->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                @endif

                                <div class="form-group text-right overflow-hidden pt-2">
                                    <button type="button" class="btn btn-light btn-lg float-left" data-dismiss="modal">Cancel</button>
                                    <button type="submit" class="btn btn-primary btn-lg float-right">Next</button>
                                </div>
                            </form>
                        </div>
                    </div>

                </div>
            </div>
            @if(empty($feeds) || $feeds->count() == 0)
                <div class="card">
                    <div class="card-body">
                        <div class="text-center">
                            <h3>Looks like you have no feed set up yet.</h3>
                            <p>
                                Feeds help you stay on top of messages and posts, so you can reply to your fans. <a href="https://help.socialbu.com/article/45-creating-a-respond-feed" data-beacon-article-modal="5e37c2c704286364bc94c186">Learn more</a>
                            </p>
                            <button class="btn btn-primary btn-sm" type="button" data-toggle="modal" data-target="#add_feed_modal">
                                <div class="d-flex align-items-center">
                                    <i class="ph ph-plus mr-2"></i>Create a Feed
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            @else

            @foreach($feeds->items() as $feed)
                <div class="card border mb-3">
                    <div class="card-body p-20">
                        <a class="text-secondary" href="{{ route('feeds.show', $feed->id) }}">
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1">
                                    {{ str_limit($feed->getName(), 255) }}
                                </h5>
                                @if($feed->team_id > 0)
                                    <small class="text-muted">
                                        <i class="ph ph-users" data-toggle="tooltip" title="Team Feed"></i>
                                    </small>
                                @endif
                            </div>
                            <p class="mb-1 text-muted">
                                @php( $latest = $feed->posts()->latest('id')->first() )
                                {{ $latest ? 'Last updated ' . $latest->updated_at->diffForHumans() : 'Created ' . $feed->updated_at->diffForHumans() }}
                            </p>

                            @php($unread = $feed->posts()->where('read', false)->count())
                            @if($unread > 0)
                                <div class="badge badge-danger">
                                    {{ $unread > 1 ? $unread : $unread }} unread items
                                </div>
                            @endif

                        </a>
                    </div>
                </div>
            @endforeach

                @if($feeds->hasPages())
                    <div class="d-flex justify-content-end pt-2 pb-0">
                        {{ $feeds->links() }}
                    </div>
                @endif

            @endif

        </div>
    </div>
@endsection
