<template>
    <div v-if="load">
        <div :class="{'loading': !initialized || fetchingMedia || posted }">

            <div class="z-index-fix position-fixed w-100 h-100 d-flex align-items-center justify-content-center bg-white text-muted status_overlay"
                 v-if="!initialized || fetchingMedia">
                <i class="ph ph-circle-notch ph-spin mr-2 text-sb" aria-hidden="true"></i>
                <span v-if="fetchingMedia">Attaching media</span>
                <span v-else-if="!initialized">Loading...</span>
            </div>
            <div class="z-index-fix position-fixed w-100 h-100 d-flex align-items-start justify-content-center bg-white status_overlay"
                 v-else-if="posted">
                <div class="card border-0 mt-6 success-card">
                    <div class="card-img-top text-center">
                        <i class="ph ph-fill ph-check-circle-o text-success"></i>
                    </div>

                    <div class="card-body text-center">
                        <h5 class="card-title">
                            Success
                        </h5>
                        <p class="mb-0" v-if="!redirectTo">
                            This popup will now close. Keep it up!
                        </p>
                        <p class="mb-0" v-else>
                            Redirecting you to <i>{{ redirectTo }}...</i>
                        </p>
                    </div>

                    <div class="card-footer text-center">
                        You can see all your posts at <a target="_blank" class="text-sb" href="https://socialbu.com/app/publish/posts">SocialBu</a>
                    </div>
                </div>
            </div>
            <post-editor ref="editor" :rendered-in-popup="true" :rendered-in-extension="true" @postsAdded="handleNewPosts" />
        </div>
    </div>
</template>

<script>
/**
 * This component is used for extension and also our /share endpoint (for quick "share" links)
 * */

import {isURL} from "validator";
import _ from "lodash";
import PostEditor from "./PostEditor.vue";
import {axios} from "../../components";

let height = null;
export default {
    components: {
        PostEditor
    },
    name: "ExtensionEditor",
    data() {
        return {
            load: false,
            posted: false,
            initialized: false,
            fetchingMedia: false,
            fetchingMediaPercentage: 0
        };
    },
    computed: {
        data() {
            if (!document.location.search.includes("?")) return {};
            return document.location.search
                .split("?")[1]
                .split("&")
                .reduce(function(prev, curr, i, arr) {
                    const p = curr.split("=");
                    prev[decodeURIComponent(p[0])] = decodeURIComponent(p[1]);
                    return prev;
                }, {});
        },
        redirectTo() {
            return this.data.redirectTo ? this.data.redirectTo : null;
        }
    },
    methods: {
        handleNewPosts() {
            this.posted = true;
            this.$nextTick(() => {
                if (!this.redirectTo) {
                    this.updateHeight();
                    setTimeout(() => {
                        window.top.postMessage(
                            {
                                SocialBu_status: "posted"
                            },
                            "*"
                        );
                    }, 3000);
                } else {
                    setTimeout(() => {
                        document.location.href = this.redirectTo;
                    }, 3000);
                }
            });
        },
        updateHeight: _.debounce(function() {
            if (!height || document.querySelector("#app").scrollHeight !== height) {
                height = document.querySelector("#app").scrollHeight;
                window.top.postMessage(
                    {
                        SocialBu_status: "updateHeight",
                        height
                    },
                    "*"
                );
            }
        }, 500)
    },
    mounted() {
        this.load = true;

        const eventMethod = window.addEventListener ? "addEventListener" : "attachEvent";
        const eventer = window[eventMethod];
        const messageEvent = eventMethod === "attachEvent" ? "onmessage" : "message";

        eventer(messageEvent, function(e) {
            const data = e.data || e.message;
            if(!data) return;
            if (data.SocialBu_status === "posted") {
                // close the popup
                window.close();
            } else if (data.SocialBu_status === "updateHeight") {
                // do nothing
            }
        });

        this.$nextTick(() => {

            window.top.postMessage(
                {
                    SocialBu_status: "loaded"
                },
                "*"
            );

            let isLoadedTimer,
                isLoadedFn,
                onLoaded = async () => {
                    // pass title, url, text
                    const userData = this.data;

                    if (userData.media && userData.media.trim()) {
                        this.fetchingMedia = true;

                        // csv separated for multi attachments
                        const medias = userData.media.split(",").map(l => l && l.trim()).filter(l => l);

                        const promises = [];
                        let i = 0;
                        const allFiles = [];
                        for(const media of medias){
                            if(!isURL(media)) continue;

                            const attachMedia = async media => {
                                // fetch media from url
                                try {
                                    const { data, headers } = await axios.get(
                                        "/app/publish/extension_editor/get_media?media=" + encodeURIComponent(media),
                                        {
                                            responseType: "blob",
                                            timeout: 60000,
                                            onUploadProgress: progressEvent => {
                                                const totalLength = progressEvent.lengthComputable
                                                    ? progressEvent.total
                                                    : progressEvent.target.getResponseHeader("content-length") ||
                                                    progressEvent.target.getResponseHeader("x-decompressed-content-length");
                                                // console.log("onUploadProgress", totalLength);
                                                if (totalLength !== null) {
                                                    this.fetchingMediaPercentage = Math.round(
                                                        progressEvent.loaded * 100 / totalLength
                                                    );
                                                }
                                            }
                                        }
                                    );

                                    let headerLine = headers["content-disposition"];
                                    let filename = headerLine.split("filename=")[1];

                                    // add file in the same order
                                    allFiles[i] = new File([data], filename, {
                                        type: headers["content-type"]
                                    });
                                    i++;

                                } catch (e) {
                                    (() => {
                                        if (e.response && e.response.data) {
                                            const errors = e.response.data.errors;
                                            if (errors) {
                                                Object.keys(errors).forEach(k => {
                                                    alert("Unable to attach media: " + errors[k].join(" \n"));
                                                });
                                                return;
                                            }
                                            if (e.response.data && e.response.data.message) {
                                                return alert("Unable to attach media: " + e.response.data.message);
                                            }
                                        }
                                        alert("Unable to attach media: " + (e.message || "An error occurred."));
                                    })();
                                    console.error(e);
                                }
                            };

                            promises.push(attachMedia(media));

                            await new Promise(resolve => setTimeout(resolve, 500));
                        }

                        await Promise.all(promises);

                        this.$refs.editor.$refs.mediaSelectBtn.onFilesSelected(allFiles);

                        this.fetchingMedia = false;
                    }

                    this.$refs.editor.content = (() => {
                        let content = "";
                        if (userData.text) {
                            content += userData.text;
                        } else if (userData.title) {
                            content += _.truncate(userData.title, { length: 200 });
                        }
                        if (userData.url) {
                            content += " " + userData.url;
                        }
                        return content;
                    })();
                };

            isLoadedFn = () => {
                if (this.$refs.editor && this.$refs.editor.initialized) {
                    onLoaded();
                    this.initialized = true;
                } else {
                    isLoadedTimer = setTimeout(isLoadedFn, 500);
                }
            };

            isLoadedFn();
        });
    }
};
</script>

<style lang="scss">
.status_overlay {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 3;
    overflow: hidden;
}
.success-card {
    margin: 0 auto;
    max-width: 400px;
}
.alertify-notifier {
    display: none !important;
}
.loading {
    min-height: 320px;
}
.z-index-fix {
    z-index: 9999;
}

/*
Schedule popup is too big for popup, so zoom out and make it a bit smaller
 */
#schedule_popup {
    zoom: 0.8;
    .modal-header {
        padding: 0;
        border-bottom: 0;
    }
    .modal-header .close {
        z-index: 2;
    }
    .modal-title {
        display: none !important;
    }
    .modal-body {
        margin-top: -40px;
        z-index: 1;
    }
    .row .timepicker {
        height: 200px;
    }
    .modal-footer {
        margin-top: -80px;
        border-top: 0;
    }
    .modal-footer button {
        z-index: 2;
    }
}
</style>
