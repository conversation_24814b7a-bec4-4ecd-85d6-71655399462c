<template>
    <div class="post mt-4">
        <template v-if="options.post_as_reel || options.post_as_story">
            <div class="attachment-container position-relative d-flex align-items-center justify-content-center mt-4 overflow-hidden" v-if="attachment">
                <div class="w-100">
                    <img class="w-100" :src="attachment.url" :title="attachment.name" v-if="attachment.type.includes('image') && !options.post_as_reel">
                    <video class="w-100" :title="attachment.name" v-else>
                        <source :src="attachment.url" />
                    </video>
                    <div class="position-absolute text-white w-100" style="top:0px; left: 0px; z-index: 1">
                        
                        <div class="range-element mx-3 border-bottom"></div>
                        <div class="position-absolute overlay-element bg-white mx-3 w-50"></div>
                    </div>
                    <div v-if="options.post_as_reel" class="position-absolute" style="right: 20px;bottom: 32px;z-index: 1;">
                        <div class="reel-post-actions">
                            <div class="text-white text-center small">
                                <div class="group">
                                    <i class="ph ph-heart"></i>
                                    <div>292</div>
                                </div>
                                <div class="group">
                                    <i class="ph ph-chat-circle"></i>
                                    <div>40</div>
                                </div>
                                <div class="group">
                                    <i class="ph ph-paper-plane-tilt"></i>
                                    
                                </div>
                                <div class="group">
                                    <i class="ph ph-dots-three-vertical"></i>
                                </div>
                                <div>
                                    <img class="reel-avatar"  alt="avatar" :src="account.image"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="position-absolute" style="bottom: 0px;z-index: 1;" v-if="options.post_as_reel">
                        <div class="reel-info text-white">
                            <div class="d-flex align-items-center user-info">
                                <img class="rounded-circle"  alt="avatar" :src="account.image"/>
                                <div class="mb-0 user-name font-weight-600">{{ account.name.length > 22 ? account.name.substring(0,22).concat("...") : account.name }} </div>
                            </div>
                            <div class="w-90 mb-3">
                                <RichText style="min-height: 8px;" placeholder="Write something..."
                                    :value="content"
                                    :readonly="true" :marks="richTextMarksSchema"/>
                            </div>
                            <div class="d-flex align-items-center text-white audio-card">
                                <i class="ph ph-music-notes mr-1"></i>
                                <span class="small-2">{{ account.name.length > 22 ? account.name.substring(0,22).concat("...") : account.name }} • Original audio</span>
                            </div>
                        </div>
                    </div>
                    <div class="position-absolute w-100" style="top: 22px;" v-else>
                        <div class="story-info text-white d-flex align-items-center justify-content-between">
                            <div class="user-info d-flex align-items-center">
                                <img class="rounded-circle"  alt="avatar" :src="account.image"/>
                                <div class="mb-0">
                                    <span class="font-weight-600 user-name">
                                        {{ account.name.length > 22 ? account.name.substring(0,22).concat("...") : account.name }} 
                                    </span>
                                    <span class="time">
                                        17h
                                    </span>
                                </div>
                            </div>
                            <i class="ph ph-dots-three-vertical ph-bold ph-lg"></i>
                        </div>
                    </div>
                </div>
            </div>
        </template>
        
        <template v-else>
            <div class="d-flex align-items-center justify-content-between profile">
                <div class="d-flex align-items-center">
                    <img :src="account.image" class="instagram-avatar rounded-circle" alt="avatar" />
                    <div class="fullname font-weight-500 ml-3">
                        {{ account.name.length > 22 ? account.name.substring(0,22).concat("...") : account.name }}
                    </div>
                </div>
                <i class="ph ph-dots-three-vertical ph-bold ph-lg"></i>
            </div>
            <div v-if="!attachments.length" class="add-media mt-3 border-top border-bottom bg-light">
                Please attach media for Instagram.
            </div>
            <div v-else>
                <vue-carousel class="text-center mt-3"
                    v-if="attachments.length > 1" :data="attachments.map(attachment=> (createElement, content, context) => {
                    if(attachment.type.includes('video')){
                        return createElement('video', {
                            attrs: {
                                controls: true,
                                preload: 'none'
                            },
                            style: 'width: 100%;'
                        }, [
                            createElement('source', {
                                attrs: {
                                    src: attachment.url
                                }
                            }, [])
                        ]);
                    } else {
                        return createElement('img', {
                            attrs: {
                                src: attachment.url
                            },
                            style: 'width: 100%;'
                        }, []);
                    }
                })" :controls="true" :autoplay="false" />
                <div v-else>
                    <video class="w-100 video mt-3" controls :title="attachments[0].name" v-if="attachments[0].type.includes('video')">
                        <source :src="attachments[0].url" />
                    </video>
                    <img class="w-100 mt-3" :src="attachments[0].url" :alt="attachments[0].name" :title="attachments[0].name"
                            v-else/>
                </div>
                <div class="post-footer font-weight-500">
                    <div class="post-footer-option d-flex justify-content-between px-4 py-3">
                        <div class="d-flex align-items-center">
                            <div class="d-flex align-items-center mr-3">
                                <i class="ph ph-heart footer-icon mr-1"></i>
                                <span>1,436</span>
                            </div>
                            <div class="d-flex align-items-center mr-3">
                                <i class="ph ph-chat-circle footer-icon mr-1"></i>
                                <span>40</span>
                            </div>
                            <i class="ph ph-paper-plane-tilt footer-icon"></i>
                        </div>
                        <i class="ph ph-bookmark-simple footer-icon"></i>                    
                    </div>
                </div>
                <div class="px-4 pb-5">
                    <p v-if="text" class="text-dark mb-0 text"> 
                        <span class="fullname font-weight-500">{{ account.name.length > 22 ? account.name.substring(0,22).concat("...") : account.name }}</span> 
                        <RichText style="min-height: 8px;" placeholder="Write something..."
                            :value="content"
                            :readonly="true" :marks="richTextMarksSchema"/>
                    </p>
                    <p v-if="comment" class="text-dark mb-0 mt-2 text"> 
                        <span class="fullname">{{ account.name.length > 22 ? account.name.substring(0,22).concat("...") : account.name }}</span> 
                        <span v-html="comment"></span>
                    </p>
                </div>
            </div>
        </template>
    </div>
</template>

<script>
import _ from "lodash";
import VueCarousel from "@chenfengyuan/vue-carousel";
import AutoLinker from "autolinker";
import RichText from "../../common/RichText.vue";
import { sanitizeHtml} from "../../../../components";

export default {
    name: "InstagramPost",
    props: ["account", "text", "attachments", "options"],
    data() {
        return {
            showFull: false
        };
    },
    components: {
        VueCarousel,
        RichText
    },
    computed: {
        attachment(){
            return this.attachments && this.attachments[0];
        },
        content() {

            let text = this.text.replace(/(\r\n|\r|\n){2,}/g, "$1\n"); // normalize linebreaks

            if (this.options.post_as_reel || this.options.post_as_story) {
                return this.showFull ? text : text.length >= 40 ? (`${text.substring(0, 40)}<span class="_see_all cursor-pointer" style="color:#465166;">...more</span>`) : text;
            } else {
                return this.showFull ? text : text.length >= 110 ? (`${text.substring(0, 110)}<span class="_see_all cursor-pointer" style="color:#465166;">...more</span>`) : text;
            }   

        },
        richTextMarksSchema() {
            return {
                span: {
                    attrs: {
                        className: {}, // Define class attribute for the span
                        styles:{default:null}
                    },
                    inclusive: false,
                    parseDOM: [
                        {
                            tag: "span[class]", // Match span elements with a class attribute
                            getAttrs: dom => {
                                const className = dom.getAttribute('class');
                                const styles = dom.getAttribute('style');
                                return { className, styles };
                            }
                        }
                    ],
                    toDOM(node) {
                        const { className, styles } = node.attrs;
                        return ["span", { class: className, style: styles }, 0];
                    }
                }
            }
        },
        comment() {
            if (!this.options.comment) return null;
            return sanitizeHtml(this.getHtmlText(
                this.showFull
                    ? this.options.comment
                    : _.truncate(this.options.comment, {
                          length: 120,
                          omission: "... [_SB_]more[_SB_]"
                      }).replace(
                          "... [_SB_]more[_SB_]",
                          `... <a href='#' class="_see_all" style="color: #8e8e8e;">more</a>`
                      )
            ));
        },
        location() {
            try {
                return JSON.parse(this.options.location).title;
            } catch (e) {}
            return null;
        },
    },
    methods: {
        truncate: _.truncate,
        getHtmlText(text) {
            text = text.replace(/(\r\n|\r|\n){2,}/g, "$1\n"); // normalize linebreaks
            return AutoLinker.link(text, {
                className: "external-link",
                urls: false,
                hashtag: "instagram",
                mention: "instagram",
                email: false,
                phone: false,
                replaceFn: match => {
                    const tag = match.buildTag();
                    tag.setAttr(this.$options._scopeId, ""); // so our scoped css works
                    return tag;
                }
            });
        },
        showMore() {
            this.showFull = true;
        }
    },
    mounted() {
        $(document).on("click.see_all", "._see_all", this.showMore);
    },
    beforeDestroy() {
        $(document).off("click.see_all", "._see_all", this.showMore);
    }
};
</script>

<style lang="scss" scoped>
.post {
    font-family: Roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;

}
.instagram-avatar{
    width: 36px;
    height: 36px;
}
.profile{
    padding-left: 18px;
    padding-right: 14px;
}
.fullname {
    line-height: 22.4px;
    color: #262626;
    &:hover {
        text-decoration: none;
    }
}
.timestamp {
    color: #bfbfbf;
}
.text {
    overflow-wrap: break-word;
    word-break: break-word;
    font-weight: 400;
    a {
        color: #106DCC !important;
    }
}
.add-media{
    color: #353F54;
    height: 424px;
    padding-top: 212px;
    padding-left: 85px;
    border: 1px solid #F2F4F7 !important;
    font-family: 'DM Sans';
}
.video{
    object-fit: cover;
}
.post-footer {
    color: #262626;
    line-height: 22.4px;
    .footer-icon{
        font-size: 32px !important;
    }
}
.attachment-container{
    height: 754px;
    background: #000000;
    border-bottom-left-radius: 7px;
    border-bottom-right-radius: 7px;

    .range-element{
        border-color: gray !important;
        height: 2px;
        margin-top: 10px;
        background: gray;
        border-radius: 20px;
    }
    .overlay-element{
        top: 10px;
        height: 2px;
        border-radius: 20px;
    }
    .reel-info{
        margin: 0px 0px 24px 20px !important;
        .user-info{
            margin-bottom: 14px;
            img{
                width: 44px;
                height: 44px;
                margin: 5px;
            }
            
        }
        .audio-card{
            width: fit-content;
            max-width: 350px;
            padding: 5px 10px;
            background-color: #2626264D;
            border: 1px solid #FFFFFF33;
            border-radius: 40px;

        }
    }
    .user-name{
        margin-left: 14px;
        line-height: 23.8px;
        font-size: 17px;
                
    }
    .story-info{
        margin: 0px 14px;
        .user-info{
            img{
                width: 38px;
                height: 38px;
            }
            .time{
                line-height: 23.8px;
                font-size: 17px !important;
                margin-left: 10px;
                opacity: 0.7;
            }
        }
    }
    .reel-post-actions{
        .group{
            margin-bottom: 17px;
            i{
                font-size: 36px;
                margin-bottom: 9px;
            }
        }
        .reel-avatar{
            width: 29px;
            height: 29px;
            border-radius: 5px;
            border: 2px solid #FFF;
        }

    }
}
</style>
