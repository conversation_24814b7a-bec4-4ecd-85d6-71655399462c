<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateMiscMetrics extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection(config('database.insights'))->create('misc_metrics', function (Blueprint $table) {
            $table->bigIncrements('id');

            // user and account ids to assign if needed
            $table->integer('user_id')->unsigned()->index()->nullable(); // internal socialbu user id; relevant user
            $table->integer('account_id')->unsigned()->index()->nullable(); // internal socialbu account id; relevant account

            $table->string('action')->index()->nullable(); // action name

            $table->string('object')->index()->nullable(); // relevant object name and id if any; e.g. post:1234

            $table->timestamp('timestamp')->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(config('database.insights'))->dropIfExists('misc_metrics');
    }
}
