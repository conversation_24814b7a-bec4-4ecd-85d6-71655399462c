<?php

namespace App\Http\Controllers\Api;

use App\Account;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Arr;

class AccountsController extends Controller
{
    public function getAuthUrl(Request $request){

        $provider = $request->input('provider');

        if (!in_array($provider, \App\Http\Controllers\User\AccountsController::$PROVIDERS)){
            return response()->json(
                [
                    'success' => false,
                    'error' => 'The provider '. $provider . ' is not valid.'
                ], 400
            );
        }

        $user = user();

        // make sure postback_url is not of localhost (check using dns maybe, so it covers all loop back domains)
        $postback_url = $request->input('postback_url');

        if($postback_url){

            $host = parse_url($postback_url, PHP_URL_HOST);

            if (filter_var($postback_url, FILTER_VALIDATE_URL) === false) {
                return response()->json(
                    [
                        'success' => false,
                        'error' => 'The postback_url is not a valid URL.'
                    ], 400
                );
            }

            if ($host === 'localhost' || $host === '127.0.0.1') {
                return response()->json(
                    [
                        'success' => false,
                        'error' => 'The postback_url cannot be localhost.'
                    ], 400
                );
            }

            // check ip of the postback_url domain
            try {
                $ip = @gethostbyname($host);
                if ($ip === '127.0.0.1') {
                    return response()->json(
                        [
                            'success' => false,
                            'error' => 'The postback_url cannot be localhost.'
                        ], 400
                    );
                }
            } catch (\Exception $e) { }

        }

        // validate return_url if needed
        $return_url = $request->input('return_url');
        if($return_url){
            if (filter_var($return_url, FILTER_VALIDATE_URL) === false) {
                return response()->json(
                    [
                        'success' => false,
                        'error' => 'The return_url is not a valid URL.'
                    ], 400
                );
            }
        }

        return response()->json(
            [
                'connect_url' => url("/auth/accounts/connect?" . Arr::query(array_filter([
                    'connect_token' => encrypt($user->id . ':' . $provider),
                    'postback_url' => $request->input('postback_url'),
                    'account_id' => $request->input('account_id'),
                    'return_url' => $request->input('return_url'),
                ]))),
            ]
        );
    }

    public function getAccount(Request $request, $id){
        $acc = Account::ofUser()->find($id);
        if(!$acc){
            return response()->json([
                'message' =>  'Account not found'
            ], 404);
        }
        return response()->json(Account::transform($acc));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int $id
     * @return \Illuminate\Http\JsonResponse
     * @throws \Illuminate\Validation\ValidationException
     */
    public function update(Request $request, $id)
    {
        $acc = Account::ofUser()->find($id);
        if(!$acc){
            return response()->json([
                'message' =>  'Account not found'
            ], 404);
        }
        $this->validate($request, [
            'name' => 'required|max:255',
        ]);
        $acc->name = $request->input('name');
        $acc->save();
        return response()->json([
            'success' => true,
            'message' =>  trans('accounts.updated')
        ]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        /** @var Account $acc */
        $acc = Account::ofUser()->find($id);
        if(!$acc){
            return response()->json([
                'message' =>  'Account not found'
            ], 404);
        }

        try {
            if (!$acc->delete()) {
                return response()->json([
                    'message' =>  'Account was not deleted. Please try again.'
                ], 500);
            }
        } catch (\Exception $e) {
            report($e);
            return response()->json([
                'message' =>  'Account was not deleted. Please try again.'
            ], 500);
        }

        return response()->json([
            'success' => true,
            'message' =>  trans('accounts.deleted')
        ]);

    }
}
