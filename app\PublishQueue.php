<?php

namespace App;

use App\Jobs\PublishQueuePost;
use App\Notifications\PublishQueueFailure; 
use App\Notifications\QueueDisableOnFailure;
use App\Traits\HasLogs;
use App\Traits\HasOptions;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Http\File;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Str;

/**
 * App\PublishQueue
 *
 * @property int $id
 * @property string $name
 * @property int $user_id
 * @property int|null $team_id
 * @property array|null $options
 * @property int $active
 * @property int|null $times_to_publish
 * @property \Illuminate\Support\Carbon|null $next_publish_at
 * @property \Illuminate\Support\Carbon|null $started_at
 * @property \Illuminate\Support\Carbon|null $ended_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\PublishQueueItem[] $items
 * @property-read int|null $items_count
 * @property-read \App\Team|null $team
 * @property-read \App\User $user
 * @method static bool|null forceDelete()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueue newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueue newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueue ofUser($id = null)
 * @method static \Illuminate\Database\Query\Builder|\App\PublishQueue onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueue query()
 * @method static bool|null restore()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueue whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueue whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueue whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueue whereEndedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueue whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueue whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueue whereNextPublishAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueue whereOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueue whereStartedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueue whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueue whereTimesToPublish($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueue whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\PublishQueue whereUserId($value)
 * @method static \Illuminate\Database\Query\Builder|\App\PublishQueue withTrashed()
 * @method static \Illuminate\Database\Query\Builder|\App\PublishQueue withoutTrashed()
 * @mixin \Eloquent
 * @property-read Collection|\App\PublishQueueItem[] $unorderedItems
 * @property-read int|null $unordered_items_count
 */
class PublishQueue extends Model
{
    use HasOptions, SoftDeletes, HasLogs;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name', 'user_id', 'team_id', 'active', 'times_to_publish',
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'options' => 'array',
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'created_at',
        'updated_at',
        'started_at',
        'ended_at',
        'next_publish_at',
        'deleted_at',
    ];

    /**
     * @param PublishQueue $queue
     * @return array
     */
    public static function transform(PublishQueue $queue)
    {
        return with($queue, function($_queue){
            /** @var PublishQueue $_queue */
            $queue = $_queue->toArray();
            $queue = (object) $queue;
            return [
                'id' => $queue->id,
                'name' => $queue->name,
                'user_id' => $queue->user_id,
                'user_name' => $_queue->user->name,
                'options' => $queue->options,
                'team_id' => $queue->team_id,
                'active' => $queue->active,
                'times_to_publish' => $queue->times_to_publish,
                'next_publish_at' => $queue->next_publish_at,
                'started_at' => $queue->started_at,
                'ended_at' => $queue->ended_at,
                'created_at' => $queue->created_at,
                'updated_at' => $queue->updated_at,
                'need_cleanup' => $_queue->times_to_publish > 0 && $_queue->items()->where('times_published', '>=', $queue->times_to_publish)->exists(),
            ];
        });
    }


    /**
     * @param null $user_id
     * @return Collection|PublishQueue[]
     */
    static public function available($user_id = null){
        /** @var User $user */
        if(!$user_id)
            $user = \Auth::user();
        else
            $user = User::find($user_id);
        return self::with('user')
            ->where('user_id', $user->id)
            ->orWhereIn('team_id', $user->joinedTeams()->select('id')->get()->map(function($t){
                return $t->id;
            }))
            ->get();
    }

    /**
     * Scope a query to fetch resources of a specified user.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $id
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfUser($query, $id = null)
    {
        if (!$id)
            $id = \Auth::id();
        return $query->where('user_id', $id);
    }

    /**
     * Get the user.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the team if set.
     */
    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Get  accounts this queue will publish to.
     * @return Collection
     */
    public function accounts(){

        if(!$this->user){
            // user probably deleted
            return new Collection();
        }

        $accountIds = $this->getOption('publish_to');
        if(!$accountIds) $accountIds = [];

        if($this->team_id && $this->team){
            /** @var Team $team */
            $team = $this->team;
            $allAccounts = $team->accounts;
        } else {
            $allAccounts = $this->user->getAvailableAccounts();
        }
        /** @var Account[]|Collection $allAccounts */

        return $allAccounts->whereIn('id', $accountIds);
    }

    /**
     * Get queue items (unordered)
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function unorderedItems(){
        return $this->hasMany(PublishQueueItem::class);
    }

    /**
     * Get queue items (ordered)
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function items(){
        return $this->unorderedItems()->orderBy('order', 'ASC')->orderBy('id','ASC');
    }

    /**
     * @param array $data
     * @return PublishQueueItem
     */
    public function addItem(array $data){
        // all validation should already be done

        /** @var PublishQueueItem $item */
        $item = $this->items()->create([
            'user_id' => \Auth::id(),
            'content' => $data['content'] ?? null,
        ]);

        if(isset($data['options']) && is_array($data['options'])){
            $item->options = $data['options'];
        }

        // find next $order
        $maxOrder = $this->items()->selectRaw('MAX(`order`) as `order`')->first()->order ?? 0;

        $item->order = $maxOrder + 1;
        
        $item->save();

        if(!empty($data['attachments']))
            $item->addAttachments($data['attachments']);

        // we should also update next publish time (in case the queue had no post in the past and now has 1 but next publish time is a timestamp from past)
        $this->findNextPublishTime(true);

        return $item;
    }

    /**
     * @param bool $save
     * @return Carbon|null
     */
    public function findNextPublishTime(bool $save = false){

        if($this->started_at && $this->started_at > now()){
            // if not started yet
            if($this->next_publish_at) {
                $this->next_publish_at = null;
                $this->save();
            }
            return null;
        }

        if($this->ended_at && $this->ended_at < now()){
            // if already ended
            if($this->next_publish_at) {
                $this->next_publish_at = null;
                $this->save();
            }
            return null;
        }

        if(!$save && $this->next_publish_at){
            return $this->next_publish_at;
        }

        // build a map with weekdays => days => [time slots]
        // build a map with month-days => days => [time slots]
        $week_days = [];
        $month_days = [];

        $schedules = $this->getOption('publish_schedule');
        if(!$schedules) return null;

        $availableTimeSlots = [];

        foreach($schedules as $schedule){
            $every = $schedule['publish_every']; // week/month
            $on = $schedule['publish_on']; // array weekdays/month-days
            $at = $schedule['publish_at']; // string hh:mm (24hr)

            if($every === 'week'){
                foreach ($on as $day){
                    if(!isset($week_days[$day])) $week_days[$day] = [];
                    $week_days[$day][] = $at;
                }
            } else if($every === 'month'){

                $months = $schedule['publish_months'];
                if(empty($months)){
                    // if no month is set, assume all
                    $months = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
                }
                foreach ($months as $month){
                    if(!isset($month_days[$month]))
                        $month_days[$month] = [];
                    foreach ($on as $day){
                        if(!isset($month_days[$month][$day])) $month_days[$month][$day] = [];
                        $month_days[$month][$day][] = $at;
                    }

                }

            }

        }


        // find the nearest timestamp from weekdays
        // find the nearest timestamp from month-days

        $now = now(timezone($this->user));
        $curMonthName = $now->format('F');
        $curMonthDay = $now->format('j');
        $curWeekDay = $now->format('l');
        $curTimestamp = $now->getTimestamp();


        if(!isset($week_days[$curWeekDay])){
            $week_days[$curWeekDay] = [];
        }
        if(!isset($month_days[$curMonthName][$curMonthDay])){
            $month_days[$curMonthName] = $month_days[$curMonthName] ?? [];
            $month_days[$curMonthName][$curMonthDay] = [];
        }
        foreach (array_merge($week_days[$curWeekDay], $month_days[$curMonthName][$curMonthDay]) as $slot){
            $slotTime = $now->copy()->setTimeFromTimeString($slot)->getTimestamp();
            if($slotTime > $curTimestamp){
                // is in future today, so it's good
                $availableTimeSlots[] = $slotTime;
            }
        }

        if(empty($availableTimeSlots)){
            // find next time slots
            // first loop days from today to next 7 weekdays and check nearest day slot
            $addDays = 1;
            $slotFound = false;
            while(!$slotFound && $addDays < 32){
                $newTime = $now->copy()->addDays($addDays);
                $newWeekday = $newTime->format('l');
                $newMonthDay = $newTime->format('j');
                if(isset($week_days[$newWeekday])){
                    // found
                    foreach ($week_days[$newWeekday] as $slot){
                        $slotTime = $newTime->setTimeFromTimeString($slot)->getTimestamp(); // timestamp of that day + time
                        $availableTimeSlots[] = $slotTime;
                        $slotFound = true;
                    }
                }
                if(isset($month_days[$curMonthName][$newMonthDay])){
                    // found
                    foreach ($month_days[$curMonthName][$newMonthDay] as $slot){
                        $slotTime = $newTime->setTimeFromTimeString($slot)->getTimestamp(); // timestamp of that day + time
                        $availableTimeSlots[] = $slotTime;
                        $slotFound = true;
                    }
                }
                ++$addDays;
            }

            if(!$slotFound){
                // no slot has been found, probably loop months to find it now
                $addMonths = 1;
                while(!$slotFound && $addMonths < 13){
                    $newMonthTime = $now->copy()->addMonths($addMonths);
                    $newMonth = $newMonthTime->format('j');
                    if(isset($month_days[$newMonth])){
                        // found, now loop month days from 1 to 31
                        for($i=1; $i<=$newMonthTime->daysInMonth; ++$i){
                            $newTime = $now->copy()->addMonths($addMonths)->day($i);
                            if(isset($month_days[$newMonth][$i])){
                                // found slots
                                foreach($month_days[$newMonth][$i] as $slot) {
                                    $slotTime = $newTime->setTimeFromTimeString($slot)->getTimestamp(); // timestamp of that day + time
                                    $availableTimeSlots[] = $slotTime;
                                    $slotFound = true;
                                }

                                if($slotFound){
                                    break;
                                }
                            }
                        }
                    }
                    ++$addMonths;
                }
            }

        }

        $ts = null;
        if(!empty($availableTimeSlots)){
            // find the nearest slot (smallest)
            $ts = Carbon::createFromTimestamp(collect($availableTimeSlots)->sort()->first());
        }

        if($save) {
            // save to db
            $this->next_publish_at = $ts;
            $this->save();
        }

        \Log::info('PublishQueue: Next publish time for queue ' . $this->id . ' is ' . ($ts ? $ts->format('Y-m-d H:i:s') : 'null'), [
            'queue_id' => $this->id,
            'queue_name' => $this->name,
        ]);

        return $ts;
    }

    public function getLastQueueItemId(){
        return $this->getOption('last_published_item');
    }

    /**
     * @return null|integer
     * @throws \Exception
     */
    public function findNextQueueItemId(){
        $lastId = $this->getLastQueueItemId();
        /** @var PublishQueueItem $lastPublishedItem */
        $lastPublishedItem = $lastId ? $this->items()->find($lastId) : null;

        $lastOffset = $this->getOption('last_offset'); // last published offset/position in the queue

        // important, we have to order by times_published first, then by other columns else it will find the same posts with same order value and publish them again
        $query = $this->items();

        $itm = null; // next item to publish

        // find the next post to publish
        $lastOrder = $lastPublishedItem ? $lastPublishedItem->order : null;
        $itemsWithoutOrder = (clone $query)->whereNull('order')->count();

        // get items with same order
        $itemsWithSameOrder = $lastOrder ? (clone $query)->where('order', $lastOrder)->count() : 0;

        if(!$lastOrder || $itemsWithoutOrder > 0 || $itemsWithSameOrder > 1){
            // set order for all items
            \DB::statement(\DB::raw('SET @rownum = 1')); // set row number to 1

            // update all items with order = row number
            (clone $query)->update([
                'order' => \DB::raw('@rownum := @rownum + 1'),
            ]);
        }

        // if we are here, we assume order is set for all items
        if($lastPublishedItem){
            $lastPublishedItem->refresh();
        }

        if($lastOffset){
            // find the next item to publish
            $itm = (clone $query)->where('order', '>', $lastOffset)->first();
        }

        // if no item found after last offset, use $lastPublishedItem->order as offset
        if(!$itm && $lastPublishedItem){
            $itm = (clone $query)->where('order', '>', $lastPublishedItem->order)->first();
        }

        if(!$itm){
            // start or re-start the queue
            // also, if no item found till here, possibly all items are published once, and we may need to re-start the queue?
            $itm = (clone $query)->first();
        }

        if(!$itm){
            // no items at all
            return null;
        }

        /** @var PublishQueueItem $itm */
        return $itm->id;
    }

    /**
     * Shuffle all posts in queue
     * @return void
     */
    public function shuffle(){
        // we need to set the `order` column of each item randomly in an efficient way because items can be in thousands
        // we need to do this in a single query
        $query = $this->unorderedItems();

        // order by random
        $query = $query->inRandomOrder();

        // now update all items with order = row number
        \DB::statement(\DB::raw('SET @rownum = 1')); // set row number to 1

        // update all items with order = row number
        $query->update([
            'order' => \DB::raw('@rownum := @rownum + 1'),
        ]);
    }

    /**
     * @param bool $actuallyPublish
     * @return void
     * @throws \Exception
     */
    public function publish(bool $actuallyPublish = false){

        if(!$this->active){
            // should not publish
            return;
        }

        if(!$this->user){
            // user probably deleted
            return;
        }

        if(!$actuallyPublish){
            // schedule the job to run in background
            dispatch((new PublishQueuePost($this))->onQueue('posts'));
            return;
        }

        // do a check to see if it is really the time to publish the post
        $nextItemId = $this->findNextQueueItemId();
        if(!$nextItemId){
            return;
        }

        $nextPublishTime = $this->findNextPublishTime();

        if(!$nextPublishTime) return; // should not happen

        if(now()->getTimestamp() < $nextPublishTime->getTimestamp()){
            // should not post
            return;
        }

        \Log::info('PublishQueue: Publishing from queue: ' . $this->id, [
            'queue_id' => $this->id,
            'queue_name' => $this->name,
            'item_id' => $nextItemId,
            'next_publish_time' => $nextPublishTime->format('Y-m-d H:i:s'),
        ]);

        /** @var PublishQueueItem $item */
        $item = PublishQueueItem::find($nextItemId);

        $user = $item->user;

        // if we are here, things are good
        $accounts = $this->accounts();

        // set auth user from item's user
        \Auth::setUser($user);

        // get attachments
        $attachments = $item->getOption('attachments');
        if(!$attachments) $attachments = [];

        // clone attachments, because we cannot affect original attachments
        $attachmentFiles = [];
        foreach($attachments as $attachment){
            if(!$attachment['path']) continue; // should not happen tho
            $stream = \Storage::readStream($attachment['path']);
            $localPath = 'converting_videos/' . $this->id . str_random() . '_' . basename($attachment['path']);

            // save the file locally
            \Storage::disk('local')->writeStream($localPath, $stream);

            $file =  new UploadedFile(storage_path('app/' . $localPath), basename($attachment['path']));

            $attachmentFiles[] = $file;
        }
        unset($attachments);

        $cleanup = function() use (&$attachmentFiles){
            try {
                // delete local files
                foreach ($attachmentFiles as $file){
                    @unlink($file->getRealPath());
                }
            } catch (\Exception $exception){ }

            // clear auth user
            \Auth::logout();
        };

        $errorsByAccount = [];
        foreach ($accounts as $account){
            // validate for each account
            try {
                /** @var Account $account */
                $account->publishPost([
                    'content' => $item->content,
                    'source' => 'queue',
                    'options' => array_merge([
                        'queue_id' => $this->id,
                        'queue_name' => $this->name,
                    ], isset($item->options[$account->id]) ? $item->options[$account->id] : ($item->options ? $item->options : [])),
                    'attachments' => $attachmentFiles,
                ], $nextPublishTime, true);

            } catch (\Exception $exception){

                $errorsByAccount[$account->id] = $exception->getMessage();

                if(!$this->onQueueFailure($item, $exception, $account)){
                    // should not continue
                    $cleanup();
                    return;
                }

                // we should log this
                $this->log('publish_error', [
                    'account' => Account::transform($account),
                    'error' => $exception->getMessage(),
                ]);
            }
        }

        // post to each account
        foreach ($accounts as $account){

            $options = array_merge([
                'queue_id' => $this->id,
            ], isset($item->options[$account->id]) ? $item->options[$account->id] : ($item->options ? $item->options : []));

            $options['post_source_name'] = $this->name; //so we can show the name on post card

            try {
                $account->publishPost([
                    'content' => $item->content,
                    'source' => 'queue',
                    'options' => $options,
                    'attachments' => $attachmentFiles,
                ], $nextPublishTime, false);
            } catch (\Exception $exception){

                if(!isset($errorsByAccount[$account->id])){
                    // report to user because it didn't happen while validation
                    $errorsByAccount[$account->id] = $exception->getMessage();

                    if(!$this->onQueueFailure($item, $exception, $account)){
                        // should not continue
                        break;
                    }

                    // continuing but we should log this
                    $this->log('publish_error', [
                        'account' => Account::transform($account),
                        'error' => $exception->getMessage(),
                    ]);
                }
            }
        }

        $cleanup();

        // set the id in options
        $this->setMultipleOptions([
            'last_published_item' => $item->id,
            'last_offset' => $item->order,
        ]);

        // increment publish times
        $item->times_published = $item->times_published + 1;
        $item->last_published_at = now();
        $item->save();

        $this->refresh();

        // update next timestamp
        $this->findNextPublishTime(true);

        // now, if times_published is equal to times_to_publish, delete the item
        if($this->times_to_publish >= 1 && $item->times_published >= $this->times_to_publish){
            $item->delete();
        }
    }

    /**
     * Alert user and/or disable queue if needed
     * @param PublishQueueItem $item
     * @param \Exception $exception
     * @param Account $account
     * @return bool
     * @throws \Exception
     */
    public function onQueueFailure(PublishQueueItem $item, \Exception $exception, Account $account)
    {

        $this->refresh();

        $accountInfo = $account->name . ' (' . $account->getType() . ')';

        $this->setOption('last_error', $exception->getTraceAsString());

        $disableQueue = $this->getOption("disable_on_failure");

        if($disableQueue){
            $this->user->notify(new QueueDisableOnFailure($this, $exception->getMessage(), $accountInfo));
        } else {
            $this->user->notify(new PublishQueueFailure($this, $exception->getMessage(), $accountInfo));
        }

        if($this->user_id !== $item->user_id){
            $item->user->notify(new PublishQueueFailure($this, $exception->getMessage(), $accountInfo));
        }

        if($disableQueue){
            $this->active = false;
            $this->save();

            return false;
        }

        return true;
    }

    /**
     * @return $this
     * @throws \Exception
     */
    public function toggleStatus(){
        $this->active = !$this->active;
        $this->save();
        // update next timestamp
        $this->findNextPublishTime(true);
        return $this;
    }

    private function logMessages(){
        return [
            'publish_error' => 'Publishing failed for account: {{ props.account.name }}. {{ props.error }}',
        ];
    }

    protected function getOptions()
    {
        return (array) $this->options;
    }

    protected function setOptions($options)
    {
        $this->options = $options;
        $this->save();
        return $this;
    }

}
