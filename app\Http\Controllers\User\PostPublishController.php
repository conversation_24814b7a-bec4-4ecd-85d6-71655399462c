<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Api\PostsController;
use App\Http\Controllers\Json\JsonCollectionController;
use App\Post;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Validation\ValidationException;

class PostPublishController extends Controller
{
    /**
     * Display a listing of the posts.
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function index()
    {
        return view('user.publish.posts');
    }

    /**
     * Display a listing of the draft posts.
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getDrafts()
    {
        return view('user.publish.drafts');
    }


    /**
     * Display published posts.
     *
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Http\RedirectResponse|\Illuminate\Http\Response|\Illuminate\View\View
     * @throws ValidationException
     */
    public function getHistory(Request $request)
    {
        $type = $request->input('type', 'posts');
        if($type != 'posts'){
            return redirect()->route('publish.history');
        }
        $data = [
            'type' => $type,
        ];
        if($type === 'posts'){

            /** @var LengthAwarePaginator $posts */
            $posts = (new JsonCollectionController())->posts(new Request(), true, 'published');
            $data['posts'] = $posts;
            $data['postsVue'] = (new Collection($posts->items()))->map(function(/** @var Post $itm */ $itm){
                return Post::transform($itm);
            });
        }
        return view('user.publish.history', $data);
    }



    /**
     * Display calendar.
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getCalendar()
    {
        return view('user.publish.calendar');
    }


    /**
     * Display a listing of the draft posts.
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getPostsAwaitingApproval()
    {
        return view('user.publish.awaiting_approval');
    }


    /**
     * Create a new post.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse|\Illuminate\Http\Response|void
     * @throws ValidationException
     * @throws \Exception
     */
    public function post(Request $request)
    {
        $queueIds = [];
        if($request->has('queue_ids')){
            $queueIds = $request->input('queue_ids');
        }
        else if($request->has('queue_id')){
            $queueIds = [$request->input('queue_id')];
        }
        if(!empty($queueIds)){
            // saving to queue
            (new QueueController())->addPost($queueIds, $request);
            $successMsg = 'Successfully saved to queue';
            return response()->json([
                'message' => $successMsg,
            ]);
        }

        // use api to create posts
        $request = $this->transformRequestForApi($request);
        try {
            $postsApiRes = (new PostsController())->post($request);
        } catch (\Exception $exception){
            if ($exception instanceof ValidationException) {
                throw $exception;
            }
            abort(400, $exception->getMessage());
            return;
        }

        // if we are here, that means we good
        $postsCreated = $postsApiRes->get('posts');

        
        // if we are here, all done
        return response()->json([
            'posts' => $postsCreated,
            'message' => 'Success'
        ]);

    }

    /**
     * Update the specified post.
     *
     * @param \Illuminate\Http\Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse|void
     * @throws \Exception
     */
    public function update(Request $request, $id)
    {
        $request = $this->transformRequestForApi($request);

        // send to api, can also throw validation exception
        try {
            $res = (new PostsController())->patch($request, $id);
            return response()->json($res);
        } catch (\Exception $exception){
            if ($exception instanceof ValidationException) {
                throw $exception;
            }
            if(app()->environment('production')){
                throw $exception;
            } else {
                abort(400, $exception->getMessage());
            }
        }
    }

    /**
     * Remove the specified post if it exists.
     *
     * @param Request $request
     * @param $id
     * @return void
     */
    public function destroy(Request $request, $id)
    {
        $request = $this->transformRequestForApi($request);
        (new PostsController())->destroy($request, $id); // deletes and also uses abort() calls for validation
    }

    /**
     * Remove the posts in bulk
     *
     * @param Request $request
     * @return void
     */
    public function bulkDelete(Request $request)
    {
        $request = $this->transformRequestForApi($request);
        (new PostsController())->bulkDelete($request); // deletes and also uses abort() calls for validation
    }

    /**
     * Edit posts in bulk
     *
     * @param Request $request
     * @return void
     */
    public function bulkEdit(Request $request)
    {
        $request = $this->transformRequestForApi($request);
        (new PostsController())->bulkEdit($request);
    }

    private function transformRequestForApi(Request $request){

        // set publish_at to be utc timestamp as api expects utc
        if($request->has('publish_at')){
            $publishAt = $request->input('publish_at');

            // If it's a JSON string, decode it to array
            if (is_string($publishAt)) {
                $publishAt = json_decode($publishAt, true);
            }

            // Always normalize to array format
            if (!is_array($publishAt)) {
                $publishAt = [$publishAt];
            }

            // Convert each date to UTC
            $now = Carbon::now('UTC');
            $convertedDates = array_map(function($item) use ($now) {
                $carbonDate = Carbon::createFromFormat('Y-m-d H:i:s', $item, timezone())
                                    ->timezone('UTC');

                // Ensure date is not in the past
                if($carbonDate < $now) {
                    $carbonDate = $now;
                }

                return $carbonDate->format('Y-m-d H:i:s');
            }, $publishAt);

            $request = $request->merge([
                'publish_at' => $convertedDates
            ]);
        }

        return $request;

    }
}
