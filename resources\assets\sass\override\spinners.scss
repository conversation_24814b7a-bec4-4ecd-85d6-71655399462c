//
// Rotating border
//
@keyframes spinner-border {
    to { transform: rotate(360deg); }
}
.spinner-border {
    display: inline-block;
    width: $spinner-width;
    height: $spinner-height;
    vertical-align: $spinner-vertical-align;
    border: $spinner-border-width solid $gray-200;
    border-top-color: currentColor;
    border-right-color: currentColor;
    border-radius: 50%;
    animation: 1s linear infinite spinner-border;
}
.spinner-border-sm {
    width: $spinner-width-sm;
    height: $spinner-height-sm;
    border-width: $spinner-border-width-sm;
}