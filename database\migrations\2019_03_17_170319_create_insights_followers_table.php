<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateInsightsFollowersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // relations cannot work, this is in a separate db
        Schema::connection(config('database.insights'))->create('followers', function (Blueprint $table) {
            $table->increments('id');
            $table->string('account_type')->index(); // facebook.page, instagram.direct etc
            $table->string('account_id')->index(); // external id
            $table->unsignedInteger('followers');
            $table->timestamp('timestamp')->useCurrent()->index();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection(config('database.insights'))->dropIfExists('followers');
    }
}
