<?php

use GuzzleHttp\Psr7\Request;
use Illuminate\Database\Seeder;

class CurationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // load csv file from resources
        $csv = array_map('str_getcsv', file(resource_path('rss_categories.csv')));
        // remove first row
        array_shift($csv);
        // insert into database
        foreach ($csv as $row) {
            $t = trim($row[0]);
            $url = trim($row[1]);
            if(\Illuminate\Support\Str::contains($url, '.feedspot.com')){
                continue; // dummy url
            }

            if(\App\CurationTopic::where('name', $t)->count() == 0) {
                $topic = new \App\CurationTopic();
                $topic->name = $t;
                $topic->save();
            }
        }

        $processedUrls = [];

        $client = guzzle_client([
            'headers' => [
                'User-Agent' => config('app.name') . ' HTTP Agent/1.0',
                'Accept' => 'application/atom+xml, application/rss+xml, application/rdf+xml;q=0.9, application/xml;q=0.8, text/xml;q=0.8, text/html;q=0.7, unknown/unknown;q=0.1, application/unknown;q=0.1, */*;q=0.1',
            ],
        ]);

        $requestGenerator = function() use ($client, $csv, &$processedUrls) {
            foreach ($csv as $row) {
                $topic = trim($row[0]);
                $url = trim($row[1]);
                if(\Illuminate\Support\Str::contains($url, '.feedspot.com')){
                    continue; // dummy url
                }

                // check if valid url
                if(!filter_var($url, FILTER_VALIDATE_URL)){
                    continue;
                }

                $feed = \App\CurationFeed::where('url', $url)->first();

                if($feed && !in_array($url, $processedUrls)){
                    $processedUrls[] = $url;
                }

                if(!in_array($url, $processedUrls)){
                    // The magic happens here, with yield key => value
                    yield $url . '::' . $topic => new Request('GET', $url);
                }
            }
        };

        $pool = new GuzzleHttp\Pool($client, $requestGenerator(), [
            'concurrency' => 100,
            'fulfilled' => function(GuzzleHttp\Psr7\Response $response, $index) {
                $parts = explode('::', $index);
                $url = $parts[0];
                $topic = $parts[1];

                $body = $response->getBody()->getContents();

                // check url is alive
                $sp = new \SimplePie();

                echo "Checking $url ... ";
                echo "\n";

                $sp->set_raw_data($body);
                try {
                    $sp->init();
                } catch (\Exception $e) {
                    return;
                }

                if ($sp->error()) { // check if it's a valid feed
                    return;
                }

                $topic = \App\CurationTopic::where('name', $topic)->first();
                $feed = \App\CurationFeed::where('url', $url)->first();
                if(!$feed && $topic) {
                    $feed = new \App\CurationFeed();
                    $feed->url = $url;
                    $feed->save();
                    $topic->feeds()->attach($feed->id);
                    echo $index, " ok\n";
                }
            },
            'rejected' => function(Exception $reason, $index) {
                // This callback is delivered each failed request
                echo $index, "\n";
                echo $reason->getMessage(), "\n\n";
            },
        ]);

        // Initiate the transfers and create a promise
        $promise = $pool->promise();

        // Force the pool of requests to complete
        $promise->wait();
    }
}
