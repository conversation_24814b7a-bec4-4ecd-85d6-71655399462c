<?php

namespace App\Http\Controllers\Api;

use App\SocialContact;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class SocialContactController extends Controller
{
    /**
     * Get social contacts.
     *
     * @param \Illuminate\Http\Request $request
     * @return mixed
     */
    public function getSocialContacts(Request $request){
        
        $this->validate($request, [
            'account_id' => 'integer|exists:accounts,id',
            'status' => 'string|in:active,spam,blocked',
            'per_page' => 'integer'
        ]);

        $perPage = $request->input('per_page', 10);

        $contacts = SocialContact::ofUser()
                    ->when($request->input('account_id'), function($query, $account_id){
                        return $query->where('account_id', $account_id);
                    })
                    ->when($request->input('status'), function($query, $status){
                        return $query->where('status', $status);
                    })
                    ->paginate($perPage);

        return response()->json([
            'contacts' => $contacts
        ]);
    }

    /**
     * Update the status of an account.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return mixed
    */
    public function updateSocialContactStatus(Request $request)
    {
        $this->validate($request, [
            'contact_ids' => 'required|array',
            'status' => 'string|required|in:active,spam,blocked',
            
        ]);

        foreach($request->get('contact_ids') as $id){
            $contact = SocialContact::ofUser()->findOrFail($id);
            $contact->status = $request->get('status');
            $contact->save();
        }

        return response()->json([
            'message' => 'Status updated successfully'
        ]);
    }

    /**
     * Delete a social contact.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return mixed
    */
    public function deleteSocialContact(Request $request){
        $this->validate($request, [
            'contact_ids' => 'required|array'
        ]);
       
        SocialContact::ofUser()->whereIn('id', $request->get('contact_ids'))->delete();

        return response()->json([
            'message' => 'Action performed successfully'
        ]);
    }
}
