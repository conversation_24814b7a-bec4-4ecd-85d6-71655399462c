<?php

namespace App\Jobs;

use App\CurationFeed;
use App\CurationItem;
use Carbon\Carbon;
use GuzzleHttp\Psr7\UriResolver;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class AddCurationItem implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /** @var CurationFeed */
    protected $feed;

    /** @var \SimplePie_Item */
    protected $item;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($feed, $item)
    {
        $this->feed = $feed;
        $this->item = $item;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {

        $item = $this->item;
        $feed = $this->feed;

        if(!$feed || !$item) {
            return;
        }

        // make sure it's a valid url
        $url = $item->get_permalink();
        if(!$url || !filter_var($url, FILTER_VALIDATE_URL)){
            return;
        }

        // first, find possible guids
        $guids = array_values(array_filter([
            CurationItem::transformGUID($item->get_id()),
            CurationItem::transformGUID($url),
        ]));

        // we don't have an existing item, so we can add it
        $curationItem = new CurationItem();
        $curationItem->guid = $guids[0];
        $curationItem->title = Str::limit(ensure_utf8($item->get_title()), 180);
        $curationItem->link = $url;
        $curationItem->description = Str::limit(ensure_utf8($item->get_description()), 2000);
        $curationItem->published_at = Carbon::createFromTimestamp($item->get_gmdate('U') ?: time());

        // timestamp is future, skip
        if($curationItem->published_at->isFuture()){
            return;
        }

        // attach the feed to the item
        $curationItem->feed_id = $feed->id;

        // start: get the media
        $media = null;

        // first, get from rss feed item
        try {
            if ($item->get_enclosure()) {
                if (Str::contains($item->get_enclosure()->get_type(), 'image') && $item->get_enclosure()->get_link()) {
                    $media = $item->get_enclosure()->get_link();
                }
                if (!$media && ($item->get_enclosure()->get_medium() === 'video' || Str::contains($item->get_enclosure()->get_type(), 'video'))) {
                    // check if we have a video, if so, set $media to video url
                    $media = $item->get_enclosure()->get_link();
                }
            }
        } catch (\Exception $exception){}

        // load image from link's meta
        if(!$media && $item->get_link()) {
            try {
                $meta_tags = extract_meta_tags($item->get_link());
                $getTag = function($tag) use($meta_tags, $item){
                    if(isset($meta_tags[$tag]) && !empty($meta_tags[$tag])){
                        /** @var string $image */
                        $image = $meta_tags[$tag];
                        // convert to abs url if it is relative
                        if(!Str::startsWith($image, 'http')){
                            $image = (string) UriResolver::resolve( \GuzzleHttp\Psr7\Utils::uriFor($item->get_link()), \GuzzleHttp\Psr7\Utils::uriFor($image) );
                        }

                        $image = trim($image);

                        if(empty($image)){
                            return null;
                        }

                        return $image;
                    }
                    return null;
                };

                // first we try the fb tag
                $media = $getTag('og:image');

                if(!$media){
                    // then we try the twitter tag
                    $media = $getTag('twitter:image');
                }

            } catch (\Exception $exception){}
        }

        // load img from content
        if( !$media && $item->get_content() && strlen($item->get_content()) > 0 ){
            // load image from content of item if possible
            try {
                $htmlDOM = new \DOMDocument();
                $htmlDOM->loadHTML($item->get_content());

                foreach ($htmlDOM->getElementsByTagName('img') as $tag) {
                    /** @var \DOMElement $tag */
                    $media = trim($tag->getAttribute('src'));
                    if(!empty($media)){
                        break;
                    }
                }
            } catch (\Exception $exception){}
        }

        // load image from item thumbnail if needed
        try {
            if (!$media && $item->get_thumbnail()) {
                $media = is_array($item->get_thumbnail()) ? Arr::first($item->get_thumbnail()) : $item->get_thumbnail();
            }
        } catch (\Exception $exception){}

        // decode html entities if needed
        if($media && strlen($media) > 0){
            $decodedImg = htmlspecialchars_decode($media);
            if( $decodedImg !== $media ){
                $media = $decodedImg;
            }
        }

        if($media && is_string($media)){
            // make sure its a valid url
            if(filter_var($media, FILTER_VALIDATE_URL) !== false){
                $curationItem->media = $media;
            }
        }
        // end: get the media

        // comma separated authors
        $csvAuthors = collect($item->get_authors() ? $item->get_authors() : [])->map(function($author){
            return trim($author->get_name());
        })->filter()->implode(',');
        if(!empty($csvAuthors)){
            $curationItem->authors = Str::limit(ensure_utf8($csvAuthors), 180);
        }

        // comma separated categories
        $csvTags = collect($item->get_categories() ? $item->get_categories() : [])->map(function($category){
            return trim($category->get_label());
        })->filter()->implode(',');
        if(!$csvTags){
            $curationItem->tags = Str::limit(ensure_utf8($csvTags), 180);
        }

        $curationItem->save();

    }
}
