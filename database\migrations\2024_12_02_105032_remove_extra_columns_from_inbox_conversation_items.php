<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class RemoveExtraColumnsFromInboxConversationItems extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('inbox_conversation_items', function (Blueprint $table) {
            // first drop index
            $table->dropForeign(['account_id']);
            $table->dropColumn([
                'status', // not needed (just like how helpscout works) - only conversation will have status
                'account_id', // because we already have this in the conversation
            ]);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('inbox_conversation_items', function (Blueprint $table) {
            $table->string('status')->nullable();
            $table->integer('account_id')->unsigned()->index();
            $table->foreign('account_id')->references('id')->on('accounts')->onDelete('cascade')->onUpdate('cascade');
        });
    }
}
