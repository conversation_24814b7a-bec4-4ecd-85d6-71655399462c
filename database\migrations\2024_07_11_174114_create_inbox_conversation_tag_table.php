<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateInboxConversationTagTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('inbox_conversation_tag', function (Blueprint $table) {
            $table->bigIncrements('id');

            $table->unsignedBigInteger('inbox_conversation_id');
            $table->foreign('inbox_conversation_id')->references('id')->on('inbox_conversations')->onDelete('cascade');
            
            $table->unsignedBigInteger('tag_id');
            
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('inbox_conversation_tag');
    }
}
