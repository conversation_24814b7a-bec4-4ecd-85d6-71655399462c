<?php

namespace App\Generate\DynamicForm\Forms;

use App\Generate\DynamicForm\FormInterface;

class UsernameGenerator implements FormInterface
{
    public function fields(): array
    {
        return [
            [
                'id' => 'topic',
                'label' => 'Topic',
                'type' => 'text',
                'rules' => 'required|string|max:60',
                'class' => 'col-md-6',
            ],
            [
                'id' => 'short_description',
                'label' => 'Short Description',
                'type' => 'textarea',
                'rules' => 'required|string|max:200',
                'class' => 'col-md-6',
            ],
            [
                'id' => 'tone',
                'label' => 'Writing Tone',
                'type' => 'select',
                'options' => [
                    ['value' => 'funny', 'label' => 'Funny','selected' => false],
                    ['value' => 'aesthetic', 'label' => 'Aesthetic', 'selected' => false],
                    ['value' => 'cool', 'label' => 'Cool', 'selected' => false],
                    ['value' => 'professional', 'label' => 'Professional', 'selected' => false],
                ],
                'rules' => 'required|in:funny,aesthetic,cool,professional',
                'class' => 'col-md-6',
            ],
            [
                'id' => 'category',
                'label' => 'Category',
                'type' => 'select',
                'options' => [
                    ['value' => 'general', 'label' => 'General', 'selected' => false],
                    ['value' => 'marketing', 'label' => 'Marketing', 'selected' => false],
                    ['value' => 'tech', 'label' => 'Tech', 'selected' => false],
                    ['value' => 'health', 'label' => 'Health', 'selected' => false],
                    ['value' => 'lifestyle', 'label' => 'Lifestyle', 'selected' => false],
                ],
                'rules' => 'required|in:general,marketing,tech,health,lifestyle',
                'class' => 'col-md-6',
            ],
            [
                'id' => 'variant_count',
                'label' => 'Number of Variants',
                'type' => 'select',
                'options' => [
                    ['value' => '5', 'label' => '5 Usernames', 'selected' => false],
                    ['value' => '10', 'label' => '10 Usernames', 'selected' => false],
                    ['value' => '15', 'label' => '15 Usernames', 'selected' => false],
                    ['value' => '20', 'label' => '20 Usernames', 'selected' => false],
                ],
                'rules' => 'required|in:5,10,15,20',
                'class' => 'col-md-6',
            ],
            [
                'id' => 'network',
                'label' => 'Network',
                'type' => 'select',
                'options' => [
                    ['value' => 'instagram', 'label' => 'Instagram', 'selected' => false],
                    ['value' => 'snapchat', 'label' => 'Snapchat', 'selected' => false],
                    ['value' => 'tiktok', 'label' => 'TikTok', 'selected' => false],
                    ['value' => 'facebook', 'label' => 'Facebook', 'selected' => false],
                    ['value' => 'youtube', 'label' => 'YouTube', 'selected' => false],
                    // ['value' => 'twitter', 'label' => 'X (Twitter)', 'selected' => false],
                    // ['value' => 'linkedin', 'label' => 'LinkedIn', 'selected' => false],
                    // ['value' => 'reddit', 'label' => 'Reddit', 'selected' => false],
                    // ['value' => 'pinterest', 'label' => 'Pinterest', 'selected' => false],
                    // ['value' => 'bluesky', 'label' => 'Bluesky', 'selected' => false],
                    // ['value' => 'google_business', 'label' => 'Google Business Profile', 'selected' => false],
                    // ['value' => 'threads', 'label' => 'Threads', 'selected' => false],
                    // ['value' => 'mastodon', 'label' => 'Mastodon', 'selected' => false]
                ],
                'rules' => 'required|in:instagram,snapchat,tiktok,facebook,youtube',
                'class' => 'col-md-6',
            ],
//            [
//                'id' => 'allow_numbers',
//                'label' => 'Allow Numbers',
//                'type' => 'checkbox',
//                'rules' => 'boolean', //boolean
//                'class' => 'col-12',
//                'default' => false,
//            ],
//            [
//                'id' => 'allow_special_characters',
//                'label' => 'Allow Special Characters',
//                'type' => 'checkbox',
//                'rules' => 'boolean', //boolean
//                'class' => 'col-12',
//                'default' => false,
//            ]
        ];
    }


    public function steps(): array
    {
        return [
            [
                'step' => 'http',
                'input' => [
                    'method' => 'Post',
                    'url' => 'https://api.openai.com/v1/chat/completions',
                    'type' => 'json', // can be json, form, or multipart
                    'response_type' => 'json',
                    'headers' => [
                        'Authorization' => 'Bearer ' . config('services.openai.secret'),
                        'Content-Type' => 'application/json',
                    ],
                    'data' => array_filter([
                        'model' => 'gpt-4o-mini',
                        'messages' => [
                            [
                                'role' => 'user',
                                'content' => trim(implode("\n", [
                                    "Generate a list of {{form.variant_count}} creative and unique usernames for a {{form.network}} account in the {{ form.category }} niche with a {{form.tone}} tone.",
                                    "The usernames should be short, catchy, and relevant to the category.",
                                    "Additional details: {{form.topic}}. Description: {{form.short_description}}.",
                                    "Return the usernames as a comma-separated string. Do not include any additional text.",
                                ]))
                            ],
                        ],
                        'temperature' => 0.7,
                        'max_tokens' => 500,
                        'top_p' => 1,
                        'frequency_penalty' => 1,
                        'presence_penalty' => 1,
                        'stop' => [
                            'Usernames:',
                        ],
                        'user' => user() ? (string) user()->id : null, // required by openai
                    ])
                ],
            ],

        ];
    }


    public function outputData(): array
    {
        return [
            'text' => '{{step1.data.choices.0.message.content}}',
        ];

    }

    public function outputComponents(): array
    {
        return [];
    }
}
