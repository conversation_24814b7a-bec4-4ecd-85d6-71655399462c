openapi: 3.0.0
info:
    description: |
        This is documentation for SocialBu API
        [http://socialbu.com](http://socialbu.com).
    version: "1.0.0"
    title: SocialBu API
    termsOfService: 'https://socialbu.com/about/terms'
    contact:
        email: <EMAIL>
servers:
    - description: SocialBu API
      url: https://socialbu.com/api/v1
tags:
    - name: authentication
      description: Authenticate api requests
    - name: posts
      description: Manage your posts
    - name: media upload
      description: upload your media files
    - name: accounts
      description: Manage your accounts
    - name: team
      description: Manage teams
        # externalDocs:
        #   description: Find out more about our store
        #   url: 'http://socialbu.com'
    - name: content_generation
      description: Generate content using AI

security:
    - bearerAuth: []

components:
    securitySchemes:
        bearerAuth:
            type: http
            scheme: bearer
            bearerFormat: JWT
    schemas:
        GenericPostRequest:
          type: object
          required:
            - accounts
            - publish_at
          properties:
            accounts:
              type: array
              items:
                type: integer
            team_id:
              type: integer
            publish_at:
              type: string
              example: "2025-04-14 15:30:00"
              description: Format should be Y-m-d H:i:s (UTC)
            content:
              type: string
            draft:
                type: boolean
                description: Save the post as a draft
            existing_attachments:
              type: array
              items:
                type: object
                properties:
                  upload_token:
                    type: string
                    description: The value received from /upload_media/status endpoint.
            postback_url:
              type: string
              description: Receive postback at this URL if needed. Sends a JSON POST request with parameters `post_id`, `account_id`, and `status` (created, published, failed).
              
        FacebookPostRequest:
          allOf:
            - $ref: '#/components/schemas/GenericPostRequest'
            - type: object
              properties:
                options:
                  type: object
                  properties:
                    comment:
                      type: string
                    post_as_story:
                      type: boolean
                      description: Supported for posts having media attachment(s)
            

        InstagramPostRequest:
          allOf:
            - $ref: '#/components/schemas/GenericPostRequest'
            - type: object
              properties:
                options:
                  type: object
                  properties:
                    post_as_reel:
                      type: boolean
                      description: Supported for video posts only
                    post_as_story:
                      type: boolean
                      description: Supported for posts having media attachment(s)
                    share_reel_to_feed:
                      type: boolean
                      description: Supported when post_as_reel option is true
                    comment:
                      type: string
                    thumbnail:
                      type: object
                      description: Supported for video posts only
                      properties:
                        name:
                          type: string
                        mimeType:
                          type: string
                        extension:
                          type: string
                        key:
                          type: string
                        url:
                          type: string
                        secureKey:
                          type: string
                        temporary:
                            type: boolean
                        _metaData: 
                            type: object
                            properties:
                              width:
                                  type: integer
                              height:
                                  type: integer

        
        ThreadsPostRequest:
          allOf:
            - $ref: '#/components/schemas/GenericPostRequest'
            - type: object
              properties:
                options:
                  type: object
                  properties:
                      reply_control:
                          type: string
                          description: Who can reply to the post (everyone, accounts_you_follow, mentioned_only)


        XPostRequest:
          allOf:
            - $ref: '#/components/schemas/GenericPostRequest'
            - type: object
              properties:
                options: 
                  type: object
                  properties:
                      media_alt_text: 
                        type: array
                        items:
                            type: string
                      threaded_replies:
                          type: array
                          items: 
                            type: object
                            properties:
                                tweet:
                                  type: string
                                media:
                                  type: array
                                  items: 
                                    type: object
                                    properties:
                                        name:
                                          type: string
                                        size:
                                          type: integer
                                        mimeType:
                                          type: string
                                        extension:
                                          type: string
                                        key:
                                              type: string
                                        url:
                                          type: string
                                        secureKey:
                                          type: string
                                        temporary:
                                              type: boolean
                                        _metaData: 
                                          type: object
                                          properties:
                                            width:
                                              type: integer
                                            height:
                                              type: integer
                                        type: 
                                          type: string
                                        ext:
                                          type: string
                                        mime:
                                          type: string
                                      

        LinkedinPostRequest:
          allOf:
            - $ref: '#/components/schemas/GenericPostRequest'
            - type: object
              properties:
                options:
                  type: object
                  properties:
                      link:
                          type: string
                      trim_link_from_content:
                          type: boolean
                          description: Trim the link from the content
                      customize_link:
                          type: boolean
                          description: Customize the link title, link description, and link preview thumbnail
                      link_description:
                            type: string
                            description: Set custom link description when customize_link is set to true
                      link_title: 
                            type: string
                            description: Set custom link title. Required when customize_link is set to true.
                      thumbnail:
                          type: object
                          description: Supported for video posts only
                          properties:
                            name:
                              type: string
                            mimeType:
                              type: string
                            extension:
                              type: string
                            key:
                              type: string
                            url:
                              type: string
                            secureKey:
                              type: string
                            temporary:
                                type: boolean
                            _metaData: 
                                type: object
                                properties:
                                  width:
                                      type: integer
                                  height:
                                      type: integer
                      comment:
                          type: string
                      document_title:
                          type: string
                          description: Supported and required for posts having PDF, PPT, or Doc as an attachment



        RedditPostRequest:
          allOf:
            - $ref: '#/components/schemas/GenericPostRequest'
            - type: object
              properties:
                options: 
                    type: object
                    required: ['title']
                    properties:
                        title:
                            type: string
                        is_spoiler:
                            type: boolean
                            description: Mark the post as spoiler
                        is_nsfw:
                            type: boolean
                            description: Mark the post as NSFW
                        flair_id:
                            type: integer
                            description: Flair ID can be obtained from accounts' extra_data
                        comment:
                            type: string



        MastodonPostRequest:
          allOf:
            - $ref: '#/components/schemas/GenericPostRequest'
            - type: object
              properties:
                options:
                  type: object
                  properties:
                      mark_sensitive:
                          type: boolean
                          description: Mark status and attached media as sensitive
                      spoiler:
                          type: string
                          description: Text to be shown as a warning or subject before the actual content
                      media_alt_text:
                          type: array
                          items: 
                            type: string



        PinterestPostRequest:
            allOf:
                - $ref: '#/components/schemas/GenericPostRequest'
                - type: object
                  properties:
                    options:
                      type: object
                      required: ['thumbnail', 'board_name']
                      properties:
                          thumbnail:
                              type: object
                              description: Supported for video posts only
                              properties:
                                name:
                                  type: string
                                mimeType:
                                  type: string
                                extension:
                                  type: string
                                key:
                                  type: string
                                url:
                                  type: string
                                secureKey:
                                  type: string
                                temporary:
                                    type: boolean
                                _metaData: 
                                    type: object
                                    properties:
                                      width:
                                          type: integer
                                      height:
                                          type: integer
                          pin_title:
                              type: string
                          board_name:
                              type: string
                          media_alt_text:
                              type: array
                              items: 
                                type: string
                          pin_link:
                              type: string
                          pin_note:
                              type: string
                              description: Add a private note


                              
        GoogleBusinessProfilePostRequest:
          allOf:
            - $ref: '#/components/schemas/GenericPostRequest'
            - type: object
              properties:
                options:
                    type: object
                    properties:
                        topic_type:
                            type: string
                            description: Set the post type `EVENT` or  `OFFER`. By default the post type is standard.
                        event_title:
                            type: boolean
                            description: Set the event title. Required when topic_type is EVENT or OFFER
                        event_start:
                          type: string
                          format: date-time
                          description: Format should be Y-m-d H:i:s (UTC), Required when topic_type is EVENT or OFFER
                        event_end:
                          type: string
                          format: date-time
                          description: Format should be Y-m-d H:i:s (UTC), Required when topic_type is EVENT or OFFER
                        offer_coupon:
                            type: string
                            description: Coupon code for offer
                        offer_link:
                            type: string
                            description: Link where the offer can be redeemed
                        offer_terms:
                            type: string
                        call_to_action:
                            type: string
                            description: a message that encourages users to take a specific action, could be `BOOK`, `ORDER`, `SHOP`, `LEARN_MORE`, `SIGN_UP`, or `CALL`
                        call_to_action_url:
                            type: string
                            description: Link to where you want the call-to-action button to take the user.
                        save_media_to_gallery:
                            type: boolean
                            description: Save the media in the gallery.



        YoutubePostRequest:
          allOf:
            - $ref: '#/components/schemas/GenericPostRequest'
            - type: object
              properties:
                options:
                    type: object
                    properties:
                        video_title:
                            type: string
                        video_tags:
                            type: string
                            description: Add tags, separated by comma
                        category_id:
                            type: integer
                            description: You can get the categories list from account's extra_data.
                        privacy_status:
                            type: string
                            description: Set the video privacy (public, private, or unlisted)
                        post_as_short:
                            type: boolean
                        made_for_kids:
                            type: boolean,
                        thumbnail:
                            type: object
                            description: Add thumbnail to the video
                            properties:
                                name:
                                    type: string
                                mimeType:
                                    type: string
                                extension:
                                    type: string
                                key:
                                    type: string
                                url:
                                    type: string
                                secureKey:
                                    type: string
                                temporary:
                                    type: boolean
                                _metaData: 
                                    type: object
                                    properties:
                                        width:
                                            type: integer
                                        height:
                                            type: integer



        TiktokPostRequest:
            allOf:
            - $ref: '#/components/schemas/GenericPostRequest'
            - type: object
              properties:
                options: 
                    type: object
                    required: ['privacy_status']
                    properties:
                        title:
                            type: string
                        privacy_status:
                            type: string
                            description: Set the post privacy to one of extra_data.creator_info.privacy_level_options (found in the Account object). For example, `PUBLIC_TO_EVERYONE`.
                        allow_stitch:
                            type: boolean
                        allow_duet:
                            type: boolean
                        allow_comment:
                            type: boolean
                        disclose_content:
                              type: boolean
                              description: Indicate whether this content promotes yourself, a brand, product or service
                        branded_content:
                              type: boolean
                              description: You are in a paid partnership with a brand.
                        own_brand:
                              type: boolean
                              description: You are promoting yourself or your own business.
                        auto_add_music:
                              type: boolean
                              description: For photo posts only. Automatically adds recommended music to posts.


                              
        BlueskyPostRequest:
          allOf:
            - $ref: '#/components/schemas/GenericPostRequest'
            - type: object
              properties:
                options: 
                    type: object
                    properties:
                        media_alt_text:
                            type: string

paths:
    /auth/get_token:
        post:
            security:
                - {}
            tags:
                - authentication
            summary: Get access token for authentication of requests
            operationId: getToken
            responses:
                '200':
                    description: Access token
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    authToken:
                                        type: string
                                        description: New access token. Any old token will be invalidated.
                                    id:
                                        type: integer
                                    name:
                                        type: string
                                    email:
                                        type: string
                                    verified:
                                        type: boolean
                '400':
                    description: Bad Request
                '422':
                    description: The given data was incorrect.
            requestBody:
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - email
                                - password
                            properties:
                                email:
                                    type: string
                                password:
                                    type: string
    /auth/logout:
        post:
            tags:
                - authentication
            summary: Destroy current access token
            operationId: logout
            responses:
                '200':
                    description: Logout
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    success:
                                        type: boolean
                '400':
                    description: Bad Request

    /posts:
        get:
            tags:
                - posts
            summary: Get Scheduled or published posts
            operationId: getPosts
            responses:
                '200':
                    description: Posts
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    items:
                                        type: array
                                        items:
                                            type: object
                                    currentPage:
                                        type: integer
                                    lastPage:
                                        type: integer
                                    nextPage:
                                        type: integer
                                    total:
                                        type: integer
                '400':
                    description: Bad Request
                '422':
                    description: The given data was invalid.
            parameters:
                - name: type
                  in: query
                  description: The type of posts you want to fetch
                  schema:
                      type: array
                      items:
                          type: string
                          enum:
                              - scheduled
                              - awaiting_approval
                              - scheduled_or_awaiting_approval
                              - draft
                              - published
                          default: scheduled
        post:
            tags:
                - posts
            summary: Create a new post.
            operationId: createPost
            responses:
                '200':
                    description: successful operation
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    success:
                                        type: boolean
                                    posts:
                                        type: array
                                        items:
                                            type: object
                '422':
                    description: The given data was invalid.
            requestBody:
                content:
                    application/json:
                        schema:
                            anyOf:
                              - $ref: '#/components/schemas/FacebookPostRequest'
                              - $ref: '#/components/schemas/InstagramPostRequest'
                              - $ref: '#/components/schemas/XPostRequest'
                              - $ref: '#/components/schemas/ThreadsPostRequest'
                              - $ref: '#/components/schemas/LinkedinPostRequest'
                              - $ref: '#/components/schemas/RedditPostRequest'
                              - $ref: '#/components/schemas/MastodonPostRequest'
                              - $ref: '#/components/schemas/PinterestPostRequest'
                              - $ref: '#/components/schemas/GoogleBusinessProfilePostRequest'
                              - $ref: '#/components/schemas/YoutubePostRequest'
                              - $ref: '#/components/schemas/TiktokPostRequest'
                              - $ref: '#/components/schemas/BlueskyPostRequest'
                        
                          
    '/posts/{postId}':
        get:
            tags:
                - posts
            summary: Find post by ID
            description: Returns a single post
            operationId: getPost
            parameters:
                - name: postId
                  in: path
                  description: ID of post to return
                  required: true
                  schema:
                      type: integer
            responses:
                '400':
                    description: Bad Request
                '404':
                    description: Resource not found
                '405':
                    description: Validation exception
                '200':
                    description: successful operation
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    success:
                                        type: boolean
                                    message:
                                        type: string
        delete:
            tags:
                - posts
            summary: Deletes the post
            operationId: deletePost
            parameters:
                - name: postId
                  in: path
                  description: Post id to delete
                  required: true
                  schema:
                      type: integer
            responses:
                '400':
                    description: Bad Request
                '404':
                    description: Resource not found
                '405':
                    description: Validation exception
                '200':
                    description: successful operation
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    success:
                                        type: boolean
                                    message:
                                        type: string
        patch:
            tags:
                - posts
            summary: Update an existing post
            operationId: updatePost
            parameters:
                - name: postId
                  in: path
                  description: Post id to delete
                  required: true
                  schema:
                      type: integer
            responses:
                '404':
                    description: Resource not found
                '422':
                    description: The given data was invalid.
                '200':
                    description: successful operation
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    success:
                                        type: boolean
                                    message:
                                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                accounts:
                                    type: array
                                    items:
                                        type: integer
                                team_id:
                                    type: integer
                                publish_at:
                                    type: string
                                    example: "2025-04-14 15:30:00"
                                    description: Format should be Y-m-d H:i:s (UTC)
                                content:
                                    type: string
                                existing_attachments:
                                    type: array
                                    items:
                                        oneOf:
                                            - type: object
                                              properties:
                                                upload_token:
                                                    type: string
                                                    description: The value received from /upload_media/status endpoint.
                                            - type: object
                                options:
                                    type: object
                                    properties:
                                        video_title:
                                            type: string
                                        trim_link_from_content:
                                            type: boolean
                                        link:
                                            type: string
                                        media_alt_text:
                                            type: array
                                            items: 
                                                type: string
                                        comment:
                                            type: string
    
    /upload_media:
      post:
        tags: 
          - media upload
        summary: Upload media file
        operationId: uploadMediaFile
        responses:
          '200':
              description: file uploaded
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      name: 
                        type: string
                        description: file name
                      mime_type:
                        type: string
                        description: file mime type
                      signed_url:
                        type: string
                        description: |
                          This parameter provides a pre-signed URL to which you should upload your media file using the HTTP PUT method. 

                          To ensure a successful upload, you must set the following headers in your request:
                          - `Content-Type`: Set this header to the MIME type of the file you are uploading (e.g., `image/jpeg`, `video/mp4`). This header informs the server about the type of the media content being uploaded.
                          - `Content-Length`: Set this header to be the size of the file being uploaded (bytes).
                          - `x-amz-acl`: Set this to `private`. 
                          
                      key:
                        type: string
                        description: file key
                      secure_key:
                        type: string
                        description: file secure key
                      url:
                        type: string
                        description: file public url
          '422':
            description: The given data was invalid.
        requestBody:
          content:
            application/json:
              schema:
                type: object
                required:
                  - name
                  - mime_type
                properties:
                  name: 
                    type: string
                  mime_type: 
                    type: string
    
    /upload_media/status:
        get:
            tags:
                - media upload
            summary: Check media upload status
            operationId: checkMediaStatus
            parameters:
                - name: key
                  in: query
                  description: The key value returned in /upload_media endpoint
                  schema:
                        type: string
            responses: 
                '200':
                  description: File status
                  content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                success:
                                    type: boolean
                                    description: If true, the file has been uploaded
                                message:
                                    type: string
                                upload_token:
                                    type: string
                                    nullable: true
                                    description: Pass this value in existing_attachments when creating a post
                '400':
                    description: Bad Request
                '422':
                    description: The given data was invalid.
            

    /accounts:
        get:
            tags:
                - accounts
            summary: Manage your accounts
            operationId: getAccounts
            responses:
                '200':
                    description: Accounts
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    items:
                                        type: array
                                        items:
                                            type: object
                                    currentPage:
                                        type: integer
                                    lastPage:
                                        type: integer
                                    nextPage:
                                        type: integer
                                    total:
                                        type: integer
                '422':
                    description: The given data was invalid.
            parameters:
                - name: type
                  in: query
                  description: The type of accounts you want to fetch
                  schema:
                      type: array
                      items:
                          type: string
                          enum:
                              - all
                              - user
                              - shared
                          default: all
        post:
            tags:
                - accounts
            summary: Connect a new social account
            operationId: connectAccount
            requestBody:
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - provider
                            properties:
                                provider:
                                    type: string
                                postback_url:
                                    type: string
                                    description: Receive postback at this URL. Sends a JSON POST request with parameters `account_action` (added, updated, connected, disconnected), `account_id`, `account_type`, and `account_name`. The `account_action` is `disconnected` if an account is found disconnected by the system, `connected` if a previously disconnected account is reconnected by the system automatically, `added` when a new account is added/connected for the first time, and `updated` when an existing account is reconnected.
                                account_id:
                                    type: string
            responses:
                '400':
                    description: The provider is not supported
                '200':
                    description: You get the URL for connecting social accounts. Recommended to open it in a popup.
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    connect_url:
                                        type: string
                                        description: URL that you can visit to add your accounts
    '/accounts/{accountId}':
        get:
            tags:
                - accounts
            summary: Find account by ID
            description: Returns a single account
            operationId: getAccount
            parameters:
                - name: accountId
                  in: path
                  description: ID of account to return
                  required: true
                  schema:
                      type: integer
            responses:
                '404':
                    description: Resource not found
                '200':
                    description: successful operation
                    content:
                        application/json:
                            schema:
                                type: object
        patch:
            tags:
                - accounts
            summary: Update account
            operationId: updateAccount
            parameters:
                - name: accountId
                  in: path
                  description: Id for the account that you want to update
                  required: true
                  schema:
                      type: integer
            responses:
                '404':
                    description: The resource not found
                '422':
                    description: The given data was invalid.

                '200':
                    description: resource updated
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    message:
                                        type: string

            requestBody:
                content:
                    application/json:
                        schema:
                            type: object
                            properties:
                                name:
                                    type: string
        delete:
            tags:
                - accounts
            summary: Deletes the account
            operationId: deleteAccount
            parameters:
                - name: accountId
                  in: path
                  description: Account id to delete
                  required: true
                  schema:
                      type: integer
            responses:
                '404':
                    description: Resource not found
                '200':
                    description: Account deleted
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    message:
                                        type: string

    /teams:
        get:
            tags:
                - team
            summary: Get your teams
            operationId: getTeams
            responses:
                '400':
                    description: Bad Request
                '200':
                    description: Teams
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    items:
                                        type: array
                                        items:
                                            type: object
                                    currentPage:
                                        type: integer
                                    lastPage:
                                        type: integer
                                    nextPage:
                                        type: integer
                                    total:
                                        type: integer
            parameters:
                - name: type
                  in: query
                  description: The type of posts you want to fetch
                  schema:
                      type: array
                      items:
                          type: string
                          enum:
                              - joined
                              - created
                          default: created

        post:
            tags:
                - team
            summary: Create a new team.
            operationId: createTeam
            responses:
                '405':
                    description: Invalid input
                '200':
                    description: successful operation
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    success:
                                        type: boolean
                                    message:
                                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - name
                                - accounts
                            properties:
                                name:
                                    type: string
                                accounts:
                                    type: array
                                    items:
                                        type: object
                                        properties:
                                            id:
                                                type: integer
                                members:
                                    type: object
                                    properties:
                                        id:
                                            type: integer
                                        permissions:
                                            type: array
                                            items:
                                                type: string

                                requires_content_approval:
                                    type: boolean

    '/teams/{teamId}':
        put:
            tags:
                - team
            summary: Update team
            operationId: updateTeam
            parameters:
                - name: teamId
                  in: path
                  description: team id
                  required: true
                  schema:
                      type: integer
            responses:
                '200':
                    description: resource updated
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    success:
                                        type: boolean
                                    message:
                                        type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - name
                                - accounts
                            properties:
                                name:
                                    type: string
                                accounts:
                                    type: array
                                    items:
                                        type: object
                                        properties:
                                            id:
                                                type: integer
                                members:
                                    type: object
                                    properties:
                                        id:
                                            type: integer
                                        permissions:
                                            type: array
                                            items:
                                                type: string

                                requires_content_approval:
                                    type: boolean
        delete:
            tags:
                - team
            summary: Deletes the item
            operationId: deleteTeam
            parameters:
                - name: teamId
                  in: path
                  description: team id
                  required: true
                  schema:
                      type: integer
            responses:
                '200':
                    description: Resource deleted
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    success:
                                        type: boolean
                                    message:
                                        type: string
    /generated_content:
        post:
            tags:
                - content_generation
            summary: Generate AI content
            operationId: generateContent
            requestBody:
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - type
                                - topic
                                - acccount_id
                            properties:
                                type:
                                    type: string
                                    enum:
                                        - tweet
                                        - linkedin_post
                                        - instagram_caption
                                        - generic
                                    default: generic
                                topic:
                                    type: string
                                team_id:
                                    nullable: true
                                    type: integer
                                acccount_id:
                                    type: integer
            responses:
                '200':
                    description: Content generated
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    id:
                                        type: integer
                                    type:
                                        type: string
                                    content:
                                        type: string
                                    keyword:
                                        type: string
                                    user_id:
                                        type: integer
                                    team_id:
                                        type: integer
                                    account_id:
                                        type: integer
                                    is_bad:
                                        type: boolean
                                    created_at:
                                        type: string
                                        format: date-time
                                    model:
                                        type: string
                '403':
                    description: Don't have permission.
                '422':
                    description: The given data was invalid.

    /generated_content/generate_by_post:
        post:
            tags:
                - content_generation
            summary: Generate content by post
            operationId: generateContentByPost
            requestBody:
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - post_id
                            properties:
                                post_id:
                                    type: integer
            responses:
                '200':
                    description: Content generated
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    id:
                                        type: integer
                                    type:
                                        type: string
                                    content:
                                        type: string
                                    keyword:
                                        type: string
                                    user_id:
                                        type: integer
                                    team_id:
                                        type: integer
                                    account_id:
                                        type: integer
                                    is_bad:
                                        type: boolean
                                    created_at:
                                        type: string
                                        example: "2025-04-14 15:30:00"
                                    model:
                                        type: string
                '403':
                    description: Don't have permission.
                '422':
                    description: The given data was invalid.

    /generated_content/autocomplete_post:
        post:
            tags:
                - content_generation
            summary: Autocomplete post content
            operationId: autocompletePost
            requestBody:
                content:
                    application/json:
                        schema:
                            type: object
                            required:
                                - account_id
                            properties:
                                content:
                                    type: string
                                    nullable: true
                                account_id:
                                    type: integer
                                team_id:
                                    type: integer
                                    nullable: true
            responses:
                '200':
                    description: Content generated
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    content:
                                        type: string
                '403':
                    description: Don't have permission.
                '422':
                    description: The given data was invalid.


    /insights/posts/counts:
        get:
            tags:
                - insights
            summary: Get post counts published within a specified time frame
            description: Returns the number of posts published on each date within a specified time frame
            operationId: getPostsCounts
            responses:
                '404':
                    description: Resource not found
                '200':
                    description: successful operation
                    content:
                      application/json:
                        schema:
                          type: object
                          properties:
                            data:
                              type: array
                              items:
                                type: object
                                properties:
                                  date:
                                    type: string
                                    format: date
                                  count:
                                    type: integer   
            parameters:
              - name: post_type
                in: query
                description: The post type
                required: true
                schema:
                  type: string
                  enum:
                    - image
                    - video
                    - text
              - name: accounts
                in: query
                description: An array of account IDs.
                schema: 
                  type: array
                  items:
                      type: string
              - name: team
                in: query
                description: The team id
                schema:
                  type: integer
              - name: start
                in: query
                description: The start date
                required: true
                schema:
                  type: string
                  format: date-time
                  description: Format should be Y-m-d
              - name: end
                in: query
                description: The end date
                required: true
                schema:
                  type: string
                  format: date-time
                  description: Format should be Y-m-d


    /insights/posts/metrics:
       get:
            tags:
                - insights
            summary: Get various metrics for posts within a specified time frame.
            description: Returns a count of specified metrics for posts on a date-wise basis within a given time frame
            operationId: getPostsMetrics
            responses:
                '404':
                    description: Resource not found
                '200':
                    description: successful operation
                    content:
                        application/json:
                          schema:
                            type: object
                            properties:
                              success:
                                type: boolean
                              data:
                                type: object
                                properties:
                                  items: 
                                    type: object
                                    properties:
                                      comments:
                                        type: array
                                        items: 
                                          type: object
                                          properties:
                                            date:
                                              type: string
                                              format: date
                                            value: 
                                              type: integer
                                      likes:
                                        type: array
                                        items: 
                                          type: object
                                          properties:
                                            date:
                                              type: string
                                              format: date
                                            value: 
                                              type: integer
                                        
            parameters:
              - name: post_type
                in: query
                description: The post type
                required: true
                schema:
                  type: string
                  enum:
                    - image
                    - video
                    - text
              - name: start
                in: query
                description: The start date
                required: true
                schema:
                  type: string
                  format: date-time
                  description: Format should be Y-m-d
              - name: end
                in: query
                description: The end date
                required: true
                schema:
                  type: string
                  format: date-time
                  description: Format should be Y-m-d
              - name: metrics
                in: query
                description: |
                  The comma separated metrics type you want to fetch.
                  Supported metrics for each network:
        
                  - **Instagram**: `like_count`, `comments_count`, `impressions`, `reach`, `saved`, `plays`, `shares`, `total_interactions`
                  - **Facebook**: `reactions`, `comments`, `shares`, `impressions`, `post_clicks`, `video_views`, `video_impressions`
                  - **Twitter**: `retweets`, `likes`
                  - **Mastodon**: `replies`, `reblogs`, `favourites`
                  - **LinkedIn**: `clicks`, `comments`, `engagement`, `impressions`, `likes`, `shares`, `unique_impressions`, `video_views`
                  - **YouTube**: `comments`, `likes`, `dislikes`, `views`
                  - **Pinterest**: `saved`, `impressions`, `pin_clicks`, `reactions`, `comments`, `video_views`, `video_avg_watch_time`
                  - **Reddit**: `comments`, `score`
                  - **Threads**: `views`, `likes`, `replies`, `reposts`, `quotes`
                  - **Bluesky**: `likes`, `replies`, `reposts`
                required: true
                schema:
                  type: string
              - name: accounts
                in: query
                description: An array of account IDs.
                schema: 
                  type: array
                  items:
                      type: string
              - name: team
                in: query
                description: The team id
                schema:
                  type: integer
              
                                    
    /insights/posts/top_posts:
       get:
            tags:
                - insights
            summary: Get the top posts
            description: Returns a list of the top posts based on specified metrics
            operationId: getTopPosts
            responses:
                '404':
                    description: Resource not found
                '200':
                    description: successful operation
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    data:
                                        type: array
                                        items:
                                            type: object 
            parameters:
              - name: start
                in: query
                description: The start date
                required: true
                schema:
                  type: string
                  format: date-time
                  description: Format should be Y-m-d
              - name: end
                in: query
                description: The end date
                required: true
                schema:
                  type: string
                  format: date-time
                  description: Format should be Y-m-d
              - name: metrics
                in: query
                description: The comma separated metrics type you want to rank posts by
                required: true
                schema:
                  type: string
              - name: accounts
                in: query
                description: An array of account IDs.
                schema: 
                  type: array
                  items:
                      type: string
              - name: team
                in: query
                description: The team id
                schema:
                  type: integer

    /insights/posts/performance_by_hashtags:
       get:
            tags:
                - insights
            summary: Get hashtags performance
            description: Returns performance data for hashtags used in posts.
            operationId: getPostsPerformanceByHashtags
            responses:
                '404':
                    description: Resource not found
                '200':
                    description: successful operation
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    data:
                                        type: array
                                        items:
                                            type: object
                                            properties:
                                              hashtag: 
                                                type: string
                                              score: 
                                                type: integer
                                        
            parameters:
              - name: start
                in: query
                description: The start date
                required: true
                schema:
                  type: string
                  format: date-time
                  description: Format should be Y-m-d
              - name: end
                in: query
                description: The end date
                required: true
                schema:
                  type: string
                  format: date-time
                  description: Format should be Y-m-d
              - name: metric
                in: query
                description: The metric value
                schema:
                  type: string
              - name: accounts
                in: query
                description: An array of account IDs.
                schema: 
                  type: array
                  items:
                      type: string
              - name: team
                in: query
                description: The team id
                schema:
                  type: integer                           

    /insights/accounts/metrics:
       get:
            tags:
                - insights
            summary: Get various metrics for accounts within a specified time frame.
            description: Returns a count of specified metrics for accounts on a date-wise basis within a given time frame
            operationId: getAccountsMetrics
            responses:
                '404':
                    description: Resource not found
                '200':
                    description: successful operation
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                  data:
                                    type: array
                                    items:
                                      type: object
                                      properties:
                                        account_id:
                                          type: integer
                                        metrics: 
                                          type: object
                                          properties:
                                            followers:  
                                              type: array
                                              items: 
                                                type: object
                                                properties:
                                                  date:
                                                    type: string
                                                    format: date
                                                  value: 
                                                    type: string
                                            total_views:
                                              type: array
                                              items: 
                                                type: object
                                                properties:
                                                  date:
                                                    type: string
                                                    format: date
                                                  value: 
                                                    type: integer
                                        latest:
                                              type: array
                                              items: 
                                                type: object
                                                properties:
                                                  type:
                                                    type: string
                                                  value: 
                                                    type: integer
                                                  timestamp:
                                                    type: string
                                                    format: date-time
            parameters:
              - name: start
                in: query
                description: The start date
                required: true
                schema:
                  type: string
                  format: date-time
                  description: Format should be Y-m-d
              - name: end
                in: query
                description: The end date
                required: true
                schema:
                  type: string
                  format: date-time
                  description: Format should be Y-m-d
              - name: metrics
                in: query
                description: |
                  The comma separated metrics type you want to fetch.         
                  Supported metrics for each network:
              
                  - **Instagram**: `followers`, `impressions`, `reach`, `followers_by_country/*`, `followers_by_gender_age/*`
                  - **Facebook**: `page_total_actions`, `page_post_engagements`, `page_impressions`, `page_fans`, `page_views_total`, `fans_by_country/*`, `fans_by_gender_age/*`, `fan_adds_by_paid_non_paid_unique/*`
                  - **Twitter**: `followers`
                  - **Mastodon**: `followers`
                  - **LinkedIn**: `followers`, `followers_by_staff_count_range/*`, `followers_by_industry/*`, `followers_by_function/*`, `followers_by_seniority/*`, `views_by_country/*`, `total_views`
                  - **Google Location**: `desktop_map_views`, `mobile_map_views`, `actions_website`, `actions_phone`, `actions_driving_directions`, `reviews`
                  - **YouTube**: `views`, `subscribers`, `videos`
                  - **Reddit Subreddit**: `subscribers`, `active_users`
                  - **Reddit Profile**: `karma`
                  - **Pinterest**: `followers`, `monthly_views`, `impressions`, `engagements`, `saves`, `clicks`
                  - **Threads**: `views`, `likes`, `replies`, `reposts`, `quotes`, `followers_count`
                  - **Bluesky**: `followers`
                required: true
                schema:
                  type: string
              - name: calculate_growth
                in: query
                description: A boolean value indicating whether to calculate the growth of the metrics.
                schema:
                  type: boolean
                  default: true
              - name: accounts
                in: query
                description: An array of account IDs.
                schema: 
                  type: array
                  items:
                      type: string
              - name: team
                in: query
                description: The team id
                schema:
                  type: integer                                     

    /insights/stats:
        get:
            tags:
                - insights
            description: Get stats for the authenticated user
            operationId: getUserStats
            responses:
                '400':
                    description: Bad Request
                '404':
                    description: Resource not found
                '405':
                    description: Validation exception
                '200':
                    description: successful operation
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    unreadFeeds:
                                        type: integer
                                    userAutomations:
                                        type: integer
                                    userPendingPosts:
                                        type: integer
                                    userFailedPosts:
                                        type: integer
                                    inactiveAccounts:
                                        type: integer
                                                                        

    /insights/teams/metrics:
       get:
            tags:
                - insights
            summary: Get the team metrics
            description: Returns a team's members performance metrics
            operationId: getTeamsMetrics
            responses:
              '404':
                description: Resource not found
              '200':
                description: successful operation
                content:
                  application/json:
                    schema:
                      type: object
                      properties:
                        data:
                          type: array
                          items:
                            type: object
                            properties:
                              member_id:
                                type: integer
                              member_name:
                                type: string
                              member_photo:
                                type: string
                              total_engagements:
                                type: integer
                              scheduled_posts:
                                type: array
                                items:
                                  type: object
                                  properties:
                                    date:
                                      type: string
                                      format: date
                                    count: 
                                      type: integer
                                        
                              published_posts:
                                type: array
                                items:
                                  type: object
                                  properties:
                                    date:
                                      type: string
                                      format: date
                                    count: 
                                      type: integer
                              rejected_posts:
                                type: array
                                items:
                                  type: object
                                  properties:
                                    date:
                                      type: string
                                      format: date
                                    count: 
                                      type: integer
                        success:
                          type: boolean
            parameters:
              - name: start
                in: query
                description: The start date
                required: true
                schema:
                  type: string
                  format: date-time
                  description: Format should be Y-m-d
              - name: end
                in: query
                description: The end date
                required: true
                schema:
                  type: string
                  format: date-time
                  description: Format should be Y-m-d
              - name: metrics
                in: query
                description: The comma separated metrics type you want to fetch
                required: true
                schema:
                  type: string
              - name: accounts
                in: query
                description: An array of account IDs.
                schema: 
                  type: array
                  items:
                      type: string
              - name: team
                in: query
                description: The team id
                schema:
                  type: integer   


    /curation/topics:
         
         get:
            tags:
                - curation
            summary: Get the curation topics
            description: Returns a list of curation topics
            operationId: getTopics
            responses:
                '400':
                    description: Bad Request
                '404':
                    description: Resource not found
                '405':
                    description: Validation exception
                '200':
                    description: successful operation
                    content:
                        application/json:
                            schema:
                                type: array
                                items: 
                                  type: object
                                  properties:
                                      id:
                                         type: integer
                                      name:
                                         type: string
            parameters:
              - name: q
                in: query
                description: The topic name
                schema:
                  type: string

    /curation/items:
        get:
            tags:
                - curation
            summary: Get curation items
            description: Returns a list curation items
            operationId: getItems
            responses:
                '400':
                    description: Bad Request
                '404':
                    description: Resource not found
                '405':
                    description: Validation exception
                '200':
                    description: successful operation id, feed_id, title, link, description, media, published_at, authors, tags, score
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    items:
                                        type: array
                                        items: 
                                            type: object
                                            properties:
                                              id:
                                                type: integer
                                              feed_id: 
                                                type: integer
                                              title: 
                                                type: string
                                              link: 
                                                type: string
                                              description:
                                                type: string
                                              media: 
                                                type: string
                                              published_at:
                                                type: string
                                                example: "2025-04-14 15:30:00"
                                              authors:
                                                type: string
                                              tage: 
                                                type: string
                                              score: 
                                                type: integer
                                    currentPage:
                                        type: integer
                                    lastPage:
                                        type: integer
                                    nextPage:
                                        type: integer
                                    total:
                                        type: integer
            parameters:
                - name: page
                  in: query
                  description: The page number
                  schema:
                    type: integer
                - name: per_page
                  in: query
                  description: The number of items per page
                  schema:
                    type: integer
                - name: authors
                  in: query
                  description: The list of authors
                  schema: 
                    type: array
                    items:
                      type: string
                - name: feed_id
                  in: query
                  description: The feed id
                  schema:
                    type: integer
                - name: topics
                  in: query
                  description: The list of topics
                  schema: 
                    type: array
                    items:
                      type: string
                - name: search
                  in: query
                  description: The search string
                  schema:
                    type: string
                - name: from
                  in: query
                  description: The start date
                  schema:
                    type: string
                    format: date-time
                    description: Format should be Y-m-d
                - name: to
                  in: query
                  description: The end date
                  schema:
                    type: string
                    format: date-time
                    description: Format should be Y-m-d
                                        
    /curation/items/{id}:
        get:
            tags:
                - curation
            summary: Find curation item by ID
            description: Returns a curation item
            operationId: getItem
            parameters:
                - name: id
                  in: path
                  description: ID of item to return
                  required: true
                  schema:
                      type: integer
            responses:
                '404':
                    description: Resource not found
                '200':
                    description: successful operation
                    content:
                      application/json:
                        schema:
                          type: object
                          properties:
                            id:
                              type: integer
                            feed_id: 
                              type: integer
                            title: 
                              type: string
                            link: 
                              type: string
                            description:
                              type: string
                            media: 
                              type: string
                            published_at:
                              type: string
                              example: "2025-04-14 15:30:00"
                            authors:
                              type: string
                            tage: 
                              type: string
                            score: 
                              type: integer

    /notifications:
        get:
            tags:
                - notifications
            summary: Get notifications
            description: Returns a list of notifications
            operationId: getNotifications
            responses:
                '400':
                    description: Bad Request
                '404':
                    description: Resource not found
                '405':
                    description: Validation exception
                '200':
                    description: successful operation
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    items:
                                        type: array
                                        items:
                                          type: object
                                          properties:
                                            created_at: 
                                              type: string
                                              example: "2025-04-14 15:30:00"
                                            id:
                                              type: string
                                            read_at: 
                                              type: string
                                            is_unread:
                                              type: boolean
                                            title:
                                              type: string
                                            body: 
                                              type: string
                                            url:
                                              type: string
                                            level:
                                              type: string
                                    currentPage:
                                        type: integer
                                    lastPage:
                                        type: integer
                                    nextPage:
                                        type: integer
                                    total:
                                        type: integer

    /notifications/unread:
        get:
            tags:
                - notifications
            summary: Get unread notifications
            description: Returns a list unread notifications
            operationId: getUnreadNotifications
            responses:
                '400':
                    description: Bad Request
                '404':
                    description: Resource not found
                '405':
                    description: Validation exception
                '200':
                    description: successful operation
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    items:
                                        type: array
                                        items:
                                          type: object
                                          properties:
                                            created_at: 
                                              type: string
                                              example: "2025-04-14 15:30:00"
                                            id:
                                              type: string
                                            read_at: 
                                              type: string
                                            is_unread:
                                              type: boolean
                                            title:
                                              type: string
                                            body: 
                                              type: string
                                            url:
                                              type: string
                                            level:
                                              type: string
                                    currentPage:
                                        type: integer
                                    lastPage:
                                        type: integer
                                    nextPage:
                                        type: integer
                                    total:
                                        type: integer
    /notifications/{id}:
        get:
            tags:
                - notifications
            summary: Find notification by ID
            description: Returns a notification
            operationId: getNotification
            parameters:
                - name: id
                  in: path
                  description: ID of notification to return
                  required: true
                  schema:
                      type: integer
            responses:
                '404':
                    description: Resource not found
                '200':
                    description: successful operation
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                  created_at: 
                                    type: string
                                    example: "2025-04-14 15:30:00"
                                  id:
                                    type: string
                                  read_at: 
                                    type: string
                                  is_unread:
                                    type: boolean
                                  title:
                                    type: string
                                  body: 
                                    type: string
                                  url:
                                    type: string
                                  level:
                                    type: string

    /notifications/{id}/mark_read:  
        post:
            tags:
                - notifications
            summary: Mark a notification as read
            operationId: markAsRead
            parameters:
                - name: id
                  in: path
                  description: ID of notifications to mark read
                  required: true
                  schema:
                      type: integer
            responses:
                '404':
                    description: Resource not found
                '200':
                    description: successful operation
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                  created_at: 
                                    type: string
                                    example: "2025-04-14 15:30:00"
                                  id:
                                    type: string
                                  read_at: 
                                    type: string
                                  is_unread:
                                    type: boolean
                                  title:
                                    type: string
                                  body: 
                                    type: string
                                  url:
                                    type: string
                                  level:
                                    type: string                       

    /notifications/{id}/mark_unread:
        post:
            tags:
                - notifications
            summary: Mark a notification as unread
            operationId: markAsUnread
            parameters:
                - name: id
                  in: path
                  description: ID of notification to mark as unread
                  required: true
                  schema:
                      type: integer
            responses:
                '404':
                    description: Resource not found
                '200':
                    description: successful operation
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                  created_at: 
                                    type: string
                                    example: "2025-04-14 15:30:00"
                                  id:
                                    type: string
                                  read_at: 
                                    type: string
                                  is_unread:
                                    type: boolean
                                  title:
                                    type: string
                                  body: 
                                    type: string
                                  url:
                                    type: string
                                  level:
                                    type: string

    /notifications/mark_all_read:
        post:
          tags:
             - notifications
          summary: Mark all notifications as read
          operationId: markAllAsRead

          responses:
            '200':
                description: successful operation
                
            '400':
                description: Bad request.
