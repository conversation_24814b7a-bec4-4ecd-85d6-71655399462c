<?php

namespace App\Http\Controllers\User;

use App\Helpers\EmailListHelper;
use App\Http\Middleware\RequireEmailVerified;
use App\User;
use Gabievi\Promocodes\Facades\Promocodes;
use Gabievi\Promocodes\Models\Promocode;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Mail;
use Laravel\Cashier\Exceptions\IncompletePayment;
use Swift_Message;

class SettingsController extends Controller
{
    /**
     * Instantiate a new controller instance.
     */
    public function __construct()
    {
        // require user email to be verified to access these methods
        $this->middleware(RequireEmailVerified::class)->only('updateSubscription', 'updatePayment', 'updatePassword', 'regenerateApiToken');
    }

    /**
     * Show settings page.
     * @param Request $request
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function showPage(Request $request){
        /** @var User $user */
        $user = \Auth::user();
        $invoices = null;
        $credit = 0;
        if($user->hasPaymentMethod()){
            if($request->has('invoices')){
                $invoices = $user->invoices();
            }
            $balance = $user->asStripeCustomer()->account_balance;
            if($balance < 0)
                $credit =  number_format(abs($balance)/100, 2);
        }
        return view('user.settings.index', [
            'user' => $user,
            'invoices' => $invoices,
            'credit' => $credit,
            'timezones' => get_timezones(),
        ]);
    }

    /**
     * Resend email confirmation link
     */
    public function resendVerificationEmail(){

        /** @var User $user */
        $user = \Auth::user();

        if($user->verified)
            abort(400);

        $user->sendEmailVerification(true);

    }

    /**
     * Update settings. This calls internal function depending on $type
     * @param Request $request
     * @param string $type
     * @return mixed
     */
    public function update(Request $request, $type = null){

        if(!in_array($type, ['account', 'subscription', 'payment', 'password', 'promo']))
            abort(404);

        /** @var Response|boolean $res */
        $res = $this->{'update' . ucfirst($type)}($request);

        // sync with our email list too
        try {
            EmailListHelper::getInstance()->syncUserContact(user());
        } catch (\Exception $exception){
            report($exception);
        }

        if(method_exists($res, 'getStatusCode')) {
            return $res;
        } else if($res === true) {
            flash(trans('settings.updated'));
            return redirect()->route('settings');
        }
        return redirect()->route('settings');
    }

    /**
     * Set promo code (redeem)
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updatePromo(Request $request){
        /** @var User $user */
        $user = \Auth::user();

        try {

            if($user->getActiveDeal()){
                throw new \Exception('A code is already active. Please deactivate it first.');
            }

            /** @var Promocode|null|false $promo */
            $promo = Promocodes::check($request->input('code', 'not_code'));
            if(!$promo){
                flash('Invalid promo code', 'error');
            } else {

                // check if user has active stripe subscription
                $subscription = $user->subscription('default');

                if($subscription && $subscription->active()){
                    // end the subscription right away
                    $subscription->cancelNow();
                }

                // if we are here, the promo is valid and use-able
                $user->redeemCode($request->input('code'));

                $codeData = $promo->data;

                $plan = $codeData['subscription_plan'];

                $planConfig = $codeData['plan_config'] ?? [];

                // store promo data in user settings
                $user->setOption('subscription_plan', $plan);

                // store plan config
                if($planConfig){
                    $user->setOption('plan_config', $planConfig);
                }

                // here, user has successfully redeemed the promo code
                flash('Promo code activated', 'success');

                try {
                    EmailListHelper::getInstance()->sendEvent($user, 'deal_activated', $request->input('code'));
                } catch (\Exception $exception){
                    report($exception);
                }
                record_usage_event('deal_activated', $request->input('code'));

            }
        } catch (\Exception $exception){
            flash($exception->getMessage(), 'error');
        }

        return redirect(route('settings') . '#billing');
    }

    /**
     * Update user details.
     * @param Request $request
     * @return bool
     * @throws \Illuminate\Validation\ValidationException
     */
    private function updateAccount(Request $request){
        $this->validate($request, [
            'name' => 'required|max:191',
            'email' => 'required|max:191|email|unique:users,email,' . \Auth::id(),
            'phone' => 'nullable|max:191',
            'company' => 'nullable|max:191',
        ]);

        /** @var User $user */
        $user = \Auth::user();

        try {
            $user->setEmail($request->input('email'));
        } catch (\Exception $e) {
            abort(400, $e->getMessage());
        }

        if($user->getOption('timezone') !== $request->input('timezone')){
            if(is_valid_timezone($request->input('timezone'))) {
                $user->setTimezone($request->input('timezone'));
            } else {
                flash('This timezone is not supported.', 'error');
            }
        }

        $user->company = $request->input('company');
        $user->name = $request->input('name');
        $user->save();

        if($user->getPhone() !== $request->input('phone')) {
            try {
                $user->setPhone($request->input('phone'));
            } catch (\Exception $e) {
                flash($e->getMessage(), 'error');
            }
        }

        try {
            EmailListHelper::getInstance()->sendEvent($user, 'profile_updated');
        } catch (\Exception $exception){
            report($exception);
        }
        record_usage_event('profile_updated');

        return true;

    }

    /**
     * Update user password.
     * @param Request $request
     * @return bool
     * @throws \Illuminate\Validation\ValidationException
     */
    private function updatePassword(Request $request){
        $this->validate($request, [
            'new_password' => 'bail|required|min:8|confirmed',
        ]);

        try {
            user()->setPassword($request->input('new_password'), $request->input('current_password'));

            // update pass hash in session
            $request->session()->put([
                'password_hash' => $request->user()->getAuthPassword(),
            ]);

            return true;
        } catch (\Exception $e) {
            flash($e->getMessage(), 'error');
            return false;
        }
    }

    /**
     * Update subscription
     * @param Request $request
     * @return mixed
     * @throws \Illuminate\Validation\ValidationException
     */
    public function updateSubscription(Request $request){
        $action = $request->input('action');
        if(!$action) abort(400);

        /** @var User $user */
        $user = \Auth::user();

        if($user->getActiveDeal()){

            if($action === 'deactivate_deal'){
                // remove the deal
                $user->removeOption('subscription_plan');

                try {
                    EmailListHelper::getInstance()->sendEvent($user, 'deal_removed');
                } catch (\Exception $exception){
                    report($exception);
                }
                record_usage_event('deal_removed');

                return response()->noContent();
            } else {
                // give error
                abort(400, 'Please deactivate your deal code first to start a subscription plan');
            }

        }

        $subscription = $user->subscription('default');

        if($action === 'free'){

            // check if user can use free
            if(!$user->canUsePlan('free')){
                abort(400, $user->canUsePlan('free', true));
            }

            // downgrade to free plan, also cancel trial if any
            if($subscription->active()){
                $subscription
                    ->skipTrial()
                    ->swap('free');
                // cancel now as free sub is not needed to be kept active
                $subscription->cancelNow();

                try {
                    EmailListHelper::getInstance()->sendEvent($user, 'select_plan', 'free');
                } catch (\Exception $exception){
                    report($exception);
                }

                return response()->noContent();
            } else {
                // it should already be 'free'
                if($user->getPlan(true) !== 'free'){
                    // something wrong
                    report(new \Exception('Subscription inactive but getPlan() returned something else than `free`'));
                    return response()->noContent();
                }
            }
        } else if($action === 'resume'){
            if(!$subscription || !$subscription->onGracePeriod()){
                abort(400, 'Your subscription is not valid');
            }
            // resume the cancelled subscription
            $subscription->resume();

            try {
                EmailListHelper::getInstance()->sendEvent($user, 'subscription_resumed');
            } catch (\Exception $exception){
                report($exception);
            }

        } else if($action === 'cancel'){
            if(!$subscription || $subscription->cancelled()){
                abort(400, 'Cannot cancel an already-cancelled subscription');
            }

            $this->validate($request, [
                'reason' => 'required|string|max:500'
            ]);

            $user->setOption('subscription_cancel_reason', $request->input('reason'));


            // create a ticket in help desk
            Mail::send([], [], function ($message) use($request, $user) {
                /** @var Swift_Message $message */
                $message->to('<EMAIL>')
                    ->subject('Subscription cancelled for ' . $user->name)
                    ->setBody($request->input('reason'))
                    ->setReplyTo($user->email, $user->name)
                    ->setFrom('<EMAIL>');
            });

            // cancel the subscription
            $subscription->cancel();

            try {
                EmailListHelper::getInstance()->sendEvent($user, 'subscription_cancelled');
            } catch (\Exception $exception){
                report($exception);
            }

            // notify our chat
            notify_chat('🔴 Subscription cancelled for user: ' . $user->email . '. Reason: ' . $request->input('reason'));

        }  else if($action === 'end'){

            if(!$subscription || $subscription->ended()){
                abort(400, 'Cannot end an already-ended or no subscription');
            }

            // cancel the subscription
            $subscription->cancelNow();

        } else if($action === 'start'){

            $this->validate($request, [
                'payment_method' => 'required|string',
                'card_name' => 'required|string',
                'country' => 'required|string',
                'plan' => 'required|string|in:' . implode(',', array_keys(config('plans')) ),
                'type' => 'required|string|in:monthly,yearly',
            ]);

            if($subscription && $subscription->active()){
                abort(400, 'Subscription is already active.');
            }

            $origPlan = $plan = $request->input('plan', 'free');

            if(!$user->canUsePlan($plan)) {
                abort(400, $user->canUsePlan($plan, true));
            }

            if($request->input('type') === 'yearly')
                $plan .= '-yearly';

            try {

                if(!$subscription){
                    // first time, so give trial
                    $subBuilder = $user->newSubscription('default', $plan)
                        ->trialDays(7)
                        ->quantity($user->getOption('subscription_quantity.' . $origPlan) ? $user->getOption('subscription_quantity.' . $origPlan) : 1);
                } else {
                    // user had a subscription before, so no trial
                    $subBuilder = $user->newSubscription('default', $plan)
                        ->skipTrial()
                        ->quantity($user->getOption('subscription_quantity.' . $origPlan) ? $user->getOption('subscription_quantity.' . $origPlan) : 1);
                }

                try {
                    $user->applyCouponIfNeeded();
                } catch (\Exception $exception) {
                    report($exception);
                }

                $subscription = $subBuilder->create($request->input('payment_method'), [
                    'email' => $user->email,
                ]);

                $user->setOption('payment.name', $request->input('card_name'));
                $user->setOption('payment.country', $request->input('country'));

            } catch (IncompletePayment $exception){

                $user->setOption('payment.name', $request->input('card_name'));
                $user->setOption('payment.country', $request->input('country'));

                return response()->json([
                    'redirect' => route('cashier.payment', [$exception->payment->id, 'redirect' => route('settings', ['#billing'])])
                ]);
            } catch(\Exception $exception){
                abort(400, $exception->getMessage());
            }

            flash('Your subscription has been started.', 'success');

            try {
                EmailListHelper::getInstance()->sendEvent($user, 'select_plan', $origPlan);
            } catch (\Exception $exception){
                report($exception);
            }

            return response()->noContent();

        } else if($action === 'select'){
            // change an active subscription plan
            if(!$user->hasPaymentMethod()){
                abort(400, 'Please update your payment method first.');
            }

            $this->validate($request, [
                'plan' => 'required|string|in:' . implode(',', array_keys(config('plans')) ),
                'type' => 'required|string|in:monthly,yearly',
            ]);

            $origPlan = $plan = $request->input('plan', 'free');


            if(!$user->canUsePlan($plan)) {
                abort(400, $user->canUsePlan($plan, true));
            }

            if($request->input('type', 'monthly') === 'yearly')
                $plan .= '-yearly';

            if($subscription){

                if($subscription->hasIncompletePayment()){
                    return response()->json([
                        'redirect' => route('cashier.payment', [$subscription->latestPayment()->id, 'redirect' => route('settings', ['#billing'])]),
                        'message' => 'Please complete the payment to continue.'
                    ])->setStatusCode(402);
                }

                if($subscription->onGracePeriod()){
                    // first we resume subs
                    $subscription->resume();
                }

                if ($subscription->active()) {

                    // apply coupon if needed
                    try {
                        $user->applyCouponIfNeeded();
                    } catch (\Exception $exception){
                        report($exception);
                    }

                    try {
                        if ($user->getOption('subscription_quantity.' . $origPlan)) {
                            // custom quantity
                            $subscription->updateQuantity($user->getOption('subscription_quantity.' . $origPlan))->swapAndInvoice($plan);
                        } else {
                            $subscription->updateQuantity(1)->swapAndInvoice($plan);
                        }
                    } catch (IncompletePayment $exception){
                        return response()->json([
                            'redirect' => route('cashier.payment', [$exception->payment->id, 'redirect' => route('settings', ['#billing'])]),
                        ]);
                    } catch (\Exception $exception){
                        report($exception);
                        abort(400, $exception->getMessage());
                    }

                    return response()->noContent();
                }
            }

            try {
                // if we are here, it means sub is not active, so create new
                // don't give trial anymore
                $subBuilder = $user->newSubscription('default', $plan)
                    ->skipTrial()
                    ->quantity( $user->getOption('subscription_quantity.' . $origPlan) ? $user->getOption('subscription_quantity.' . $origPlan)  : 1);

                try {
                    $user->applyCouponIfNeeded();
                } catch (\Exception $exception){
                    report($exception);
                }

                $subscription = $subBuilder->create(null);

                try {
                    EmailListHelper::getInstance()->sendEvent($user, 'select_plan', $origPlan);
                } catch (\Exception $exception){
                    report($exception);
                }

            } catch (IncompletePayment $exception){

                try {
                    EmailListHelper::getInstance()->sendEvent($user, 'select_plan', $origPlan);
                } catch (\Exception $exception){
                    report($exception);
                }

                return response()->json([
                    'redirect' => route('cashier.payment', [$exception->payment->id, 'redirect' => route('settings', ['#billing'])])
                ]);
            } catch(\Exception $exception){
                abort(400, $exception->getMessage());
            }

        }

        return response()->noContent();
    }

    /**
     * Update payment card for user
     * @param Request $request
     * @return Response
     * @throws \Illuminate\Validation\ValidationException
     */
    public function updatePayment(Request $request){
        /** @var User $user */
        $user = \Auth::user();

        $this->validate($request, [
            'payment_method' => 'required|string',
            'card_name' => 'required|string',
            'country' => 'required|string',
        ]);
        try {
            $user->updateDefaultPaymentMethod($request->input('payment_method'));
        } catch(\Exception $exception){
            abort(400, $exception->getMessage());
        }
        $user->setOption('payment.name', $request->input('card_name'));
        $user->setOption('payment.country', $request->input('country'));
        flash('Payment method has been updated.', 'success');

        return response()->noContent();
    }

    public function createPaymentIntent(Request $request)
    {
        $user = user();

        $intent = $user->createSetupIntent();

        $secret = $intent->client_secret;

        return response()->json(['client_secret' => $secret]);
    }

    public function regenerateApiToken()
    {
        $user = user();

        try {
            $user->getApiToken(true);
            flash('API token has been regenerated.', 'success');
        } catch (\Exception $exception){
            flash($exception->getMessage(), 'error');
        }

        return redirect()->route('settings', ['#developers']);
    }

    /**
     * @param Request $request
     * @throws \Exception
     */
    public function deleteAccount(Request $request){
        /** @var \App\User $user */
        $user = \Auth::user();

        $subscription = $user->subscription('default');

        if($subscription && $subscription->active()){
            if($subscription->onGracePeriod()){
                $subscription->cancelNow();
            } else {
                abort(400, 'Please cancel your subscription first.');
                return;
            }
        }

        \Auth::logout();

        $user->delete();
    }

    public function downloadInvoice(Request $request, $invoiceId) {

        try {
            EmailListHelper::getInstance()->sendEvent(user(), 'download_invoice');
        } catch (\Exception $exception){
            report($exception);
        }

        return user()->downloadInvoice($invoiceId, [
            'vendor'  => 'SocialBu',
            'vat' => $request->user()->getOption('company_vat'),
            'billing_address' => $request->user()->getOption('company_billing_address'),
            'tax_id' => $request->user()->getOption('company_tax_id'),
            'lines_after_vendor' => [/*'Company ID : 7020545',*/ 'Tax ID : 61-1899368'],
            'address' => ['Glaxosoft LLC', '16192 Coastal Highway', 'Lewes Delaware 19958', 'U.S.A'],
            'product' => config('app.name'),
        ]);
    }
}
