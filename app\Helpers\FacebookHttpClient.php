<?php
namespace App\Helpers;

use Facebook\HttpClients\FacebookCurlHttpClient;

/**
 * Class FacebookCurlHttpClient
 *
 * @package Facebook
 */
class FacebookHttpClient extends FacebookCurlHttpClient
{
    /**
     * Opens a new curl connection.
     *
     * @param string $url     The endpoint to send the request to.
     * @param string $method  The request method.
     * @param string $body    The body of the request.
     * @param array  $headers The request headers.
     * @param int    $timeOut The timeout in seconds for the request.
     */
    public function openConnection($url, $method, $body, array $headers, $timeOut)
    {
        parent::openConnection($url, $method, $body, $headers, $timeOut);
        $this->facebookCurl->setopt(CURLOPT_CONNECTTIMEOUT, 60); // was 10 which is too low
    }
}
