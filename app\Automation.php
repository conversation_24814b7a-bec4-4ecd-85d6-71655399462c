<?php

namespace App;

use App\Helpers\AutomationAction;
use App\Helpers\AutomationCondition;
use App\Jobs\ProcessAutomation;
use App\Notifications\AutomationFailure;
use App\Traits\HasLogs;
use App\Traits\HasOptions;
use Illuminate\Database\Eloquent\Model;
use Malahierba\PublicId\PublicId;

/**
 * App\Automation
 *
 * @property int $id
 * @property string $description
 * @property string|null $tag
 * @property string|null $event
 * @property array $event_data
 * @property int $user_id
 * @property array $rules
 * @property array $options
 * @property int $active
 * @property \Illuminate\Support\Carbon|null $executed_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $public_id
 * @property-read \App\User $user
 * @property-read \App\Team|null $team
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Automation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Automation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Automation ofUser($id = null)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Automation query()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Automation userAutomations($id = null)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Automation whereActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Automation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Automation whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Automation whereEvent($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Automation whereEventData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Automation whereExecutedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Automation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Automation whereOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Automation whereRules($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Automation whereTag($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Automation whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Automation whereUserId($value)
 * @mixin \Eloquent
 * @property int|null $team_id
 * @method static \Illuminate\Database\Eloquent\Builder|Automation whereTeamId($value)
 */
class Automation extends Model
{
    use PublicId, HasOptions, HasLogs;

    static protected $public_id_salt = 'automation-glxsftsocial--@';

    static protected $public_id_min_length = 32; // min length for your generated short ids.

    static protected $public_id_alphabet = 'abcdefghijklmnopqrstuvwxyz0123456789';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id', 'description', 'team_id',
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'options' => 'array',
        'rules' => 'array',
        'event_data' => 'array',
        'active' => 'boolean',
    ];

    /**
     * The attributes that are timestamps
     *
     * @var array
     */
    protected $dates = [
        'created_at',
        'updated_at',
        'executed_at',
    ];

    static public function transform(Automation $automation){
        return collect([
            'id' => $automation->id,
            'description' => $automation->description,
            'user_id' => $automation->user_id,
            'team_id' => $automation->team ? $automation->team->id : null,
            'team' => $automation->team ? Team::transform($automation->team) : null,
            'event' => $automation->event,
            'event_data' => array_to_object($automation->event_data),
            'rules' => $automation->rules,
            'active' => $automation->active,
            'webhook_url' => url('webhook/automation/' .$automation->public_id),
            'ignore_failure' => (bool) $automation->getOption('ignore_failure', false),
            'event_name' => $automation->getPrettyEventName(),
            'executed_at' => $automation->executed_at,
            'created_by' => \Auth::id() !== $automation->user_id ? $automation->user->name : 'you'
        ]);
    }

    /**
     * Scope a query to fetch resources of a specified user.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $id
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfUser($query, $id = null)
    {
        if (!$id)
            $id = \Auth::id();
        return $query->where('user_id', $id);
    }

    /**
     * Scope a query to fetch available resources belonging to specified user.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $id
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Query\Builder
     */
    public function scopeUserAutomations($query, $id = null)
    {
        /** @var User $user */
        if($id){
            $user = User::find($id);
        } else {
            $user = \Auth::user();
        }

        if(!$user){
            // happens in case of deleted user
            return $query->where('tag', 'non_existent');
        }

        return $query->where('user_id', $user->id)->orWhereIn('team_id', $user->joinedTeams()->get()->filter(function(Team $t) use($user){
            return $t->hasPermission($user, 'automation.edit');
        })->map(function (Team $team){
            return $team->id;
        }));
    }

    /**
     * Get the user that added this object.
     */
    public function user()
    {
        return $this->belongsTo('App\User');
    }

    /**
     * Get the team for this object.
     */
    public function team()
    {
        return $this->belongsTo('App\Team');
    }

    /**
     * Get event name prettified (for showing to user)
     *
     * @return string
     */
    public function getPrettyEventName(){

        if(empty($this->event)) return 'N/A';

        $evParts = explode('.', $this->event);
        $name = '';
        if(isset($evParts[1])){
            $name = ucfirst(array_shift($evParts)) . ' ';
        }
        $name .= title_case(str_replace('_', ' ', $evParts[0]));
        return $name;
    }

    /**
     * @param array $data dynamic data / placeholder data
     * @param mixed $eventData event-specific data e.g. message id
     * @param bool $really
     * @return array|null return data = input[], output[], errors[], executed? = true
     */
    public function execute($data, $eventData = [], $really = false)
    {
        if (!$really) {
            dispatch((new ProcessAutomation($this, $data, $eventData))->onQueue('automations'));
            return null;
        }

        // input and output is returned in case we want to know the execution data/steps; currently used to return response if needed on webhook trigger
        $input = $data;
        $output = [];
        if (!isset($data['__input'])) {
            // to be used as a placeholder if needed
            $data['__input'] = $input;
        }

        /** @var \Exception[]|array $errors */
        $errors = [];
        $executed = false;
        foreach ($this->rules as $actionGrpIndex => $actionGroup) {
            if ($this->event === 'webhook' || (new AutomationCondition($actionGroup['conditions'], $data))->matches()) { // if conditions evaluated to true then execute actions
                $executed = true;
                foreach ($actionGroup['actions'] as $actionIndex => $action) {
                    // process action
                    try {
                        $actionExec = new AutomationAction($this, $action, $data, $eventData);
                        if($actionExec->getData()){
                            // this action exports data
                            $data['action_' . ($actionGrpIndex + 1) . '_' . ($actionIndex + 1)] = $actionExec->getData(); // make the result use-able by actions executing further
                            $output['action_' . ($actionGrpIndex + 1) . '_' . ($actionIndex + 1)] = $actionExec->getData(); // data for output array
                        }
                        //\Log::info($data);
                    } catch (\Exception $e) {

                        \Log::error($e->getTraceAsString(), [
                            'automation_id' => $this->id,
                        ]);

                        // log error
                        $errors[] = $e;
                    }
                }
            }
        }

        // log automation execution
        if($executed) {
            activity('automations')
                ->performedOn($this)
                ->withProperties([
                    'type' => 'info',
                ])
                ->log('Automation executed');
            $this->executed_at = now();
            $this->save();
        }

        if (!empty($errors)) {
            // get readable error message
            $reason = implode('. ', collect($errors)->map(function ($e) {
                /** @var \Exception $e */
                return $e->getMessage();
            })->toArray());

            // disable automation notifying user of errors
            if(!$this->getOption('ignore_failure')) {
                $this->deactivate($reason);
            } else {
                // automation has ignore flag set, so only log
                activity('automations')
                    ->performedOn($this)
                    ->withProperties([
                        'type' => 'error',
                    ])
                    ->log('Error: ' . $reason);
            }
            foreach($errors as $error){
                if($error->getPrevious()){
                    report($error->getPrevious());
                }
            }
        }

        // return data
        return [
            'input' => $input,
            'output' => $output,
            'errors' => collect($errors)->map(function ($e /** @var $e \Exception */ ){ return $e->getMessage();})->toArray(),
            'executed' => $executed,
        ];
    }

    /** Deactivate automation
     * @param string|\Exception $reason
     */
    public function deactivate($reason = null)
    {
        $this->active = false;
        $this->save();
        if ($reason) {
            if ($reason instanceof \Exception) {
                $reason = $reason->getMessage();
            }
            if($this->user)
                $this->user->notify(new AutomationFailure($this, $reason));
            activity('automations')
                ->performedOn($this)
                ->withProperties([
                    'type' => 'error',
                ])
                ->log('Automation deactivated because: ' . $reason);
        } else {
            activity('automations')
                ->performedOn($this)
                ->withProperties([
                    'type' => 'info',
                ])
                ->log('Automation deactivated');
        }
    }

    private function getOptions()
    {
        return (array)$this->options;
    }

    private function setOptions($options)
    {
        $this->options = $options;
        $this->save();
        return $this;
    }

    private function logMessages(){
        return [
            'updated' => '{{ user.name }} updated the automation',
            'created' => '{{ user.name }} created the automation',
        ];
    }

}
