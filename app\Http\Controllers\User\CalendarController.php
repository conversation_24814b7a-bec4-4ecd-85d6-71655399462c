<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Api\CalendarController as CalendarControllerApi;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class CalendarController extends Controller
{
    public function index(){
        return view('user.publish.calendar');
    }

    /**
     * @param Request $request
     * @return array
     * @throws \Exception
     */
    public function posts(Request $request){
        return (new CalendarControllerApi())->posts($request)->toArray();
    }
}
