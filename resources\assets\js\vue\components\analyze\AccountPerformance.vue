<template>
    <div class="row">
        <div class="col-12 mb-8">
            <h5 class="mb-4">
                Growth
            </h5>
            <div
                class="card border"
                v-if="isLoading('account_metrics')"
                v-html="spinnerHtml"
            ></div>
            <div v-else>
                <div class="mb-4 row">
                    <div class="col-12 col-md-6 pr-2">
                        <div class="analyze-card card border">
                            <div class="card-body p-20">
                                <h6 class="font-weight-500 mb-6">New Followers</h6>
                                <h3 class="mb-0">{{ totalFollowers }}</h3>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card border mt-4">
                    <div class="card-body">
                        <Chart
                            :options="growthChart"
                            ref="chart_growth"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 mb-8">
            <h5 class="mb-4">
                Engagements
            </h5>
            <div
                class="card border"
                v-if="isLoading('post_metrics')"
                v-html="spinnerHtml"
            ></div>
            <div v-else>

                <div class="mb-4 row">
                    <div class="col-12 col-md-6 pr-2">
                        <div class="analyze-card card border">
                            <div class="card-body p-20">
                                <div>
                                    <h6 class="font-weight-500 mb-6">Engagements Received</h6>
                                    <h3 class="mb-0">{{ totalEngagements }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card border">
                    <div class="card-body">
                        <Chart
                            :options="engagementsChart"
                            ref="chart_engagements"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12">
            <h5 class="mb-4">
                Accounts Overview
            </h5>
            <div>
                <div v-if="isLoading('account_metrics')" v-html="spinnerHtml">
                </div>
                <div
                    v-else>
                    <div class="row">
                        <div class="col-12"
                             v-if="!accountsOverview.length">
                            <div class="card border">
                                <div class="card-body p-20">
                                    No data to display
                                </div>
                            </div>
                        </div>
                        <div class="col-12 mb-4" v-if="accountsOverview.length">
                            <div class="card">
                                <div class="card-body p-0">
                                    <div class="table-responsive table-border rounded-md">
                                        <table class="table mb-0">
                                            <thead class="card-header">
                                                <tr>
                                                    <th class="py-3 pl-md-5 pl-0" scope="col">Name</th>
                                                    <th class="d-md-table-cell d-none py-3" scope="col">Type</th>
                                                    <th class="py-3" scope="col"><div class="d-md-none">Follo...</div><div class="d-md-block d-none">Followers</div></th>
                                                    <th class="py-3 pr-0" scope="col"> <div class="d-md-inline-block d-none">Total</div> Views</th>

                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr v-for="(account, index) in accountsOverview" v-if="account.metrics && account.metrics.length" :key="index + 'accounts-overview'">
                                                    <td class="pl-md-5 pl-0 pr-0">
                                                        <div class="profile-component d-flex align-items-center">
                                                            <div class="position-relative">
                                                                <img class="d-md-block d-none avatar avatar-xs"
                                                                    :src="account.image">
                                                                <img class="d-md-none avatar avatar-sm"
                                                                    :src="account.image">
                                                                <img class="d-md-none position-absolute border rounded-circle border-white network-icon" style="left: 20px;top: 20px;"
                                                                    :src="getIconForAccount(account.type, 'circular')"
                                                                    :title="account.type" v-tooltip>
                                                            </div>
                                                            <div class="ml-md-2 ml-3">
                                                                <div>{{account.name }}</div>
                                                                <p class="mb-0 d-md-none">{{ account._type }}</p>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="d-md-table-cell d-none">
                                                        <div class="d-flex align-items-center">
                                                                <img class="network-icon mr-2"
                                                                    :src="getIconForAccount(account.type,'original')"
                                                                    :title="account.type" v-tooltip>
                                                                <span class="mb-0 text-muted">{{ account._type }}</span>
                                                            </div>
                                                    </td>
                                                    <td>
                                                        <div v-for="metric in account.metrics" :key="'metric' + account.account_id + account.type">
                                                            <div v-if="metric.type.toLowerCase().includes('followers') || metric.type.toLowerCase().includes('fans')">
                                                                {{ metric.value }}
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div v-for="metric in account.metrics" :key="'metric' + account.account_id + account.value">
                                                            <div v-if="metric.type.toLowerCase().includes('view')">
                                                                {{ metric.value }}
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import {
    axios,
    axiosErrorHandler,
    spinnerHtml,
    getIconForAccount
} from '../../../components'
import { Chart } from "highcharts-vue";
import { capitalize } from "lodash";

export default {
    name: 'AccountPerformance',
    props: ['filter', 'accounts'],
    components: {
        Chart
    },
    data() {
        return {
            loadingFlags: [],

            accountMetrics: [],
            postMetrics: [],
            postCounts: [],

            accountEngagementsByDate: [],
        }
    },
    computed: {
        spinnerHtml: () => spinnerHtml,

        totalFollowers(){
            let followers = 0;
            this.accountMetrics.forEach((item) => {
                Object.keys(item.metrics).forEach((key) => {
                    if(["followers", "fans",].includes(key)){
                        item.metrics[key].forEach(e => {
                            followers += e.value;
                        });
                    }
                });
            });
            return followers;
        },
        totalEngagements(){
            let engagements = 0;
            this.postMetrics.forEach(a => {
                Object.values(a.metrics).forEach(d =>{
                    d.forEach(e => {
                        engagements += e.value;
                    });
                })
            });
            return engagements;
        },

        // chart configs
        growthChart(){
            const followersData = this.accountMetrics.map((item) => {
                const { metrics } = item;
                const acc = this.getAccount(item.account_id);
                return {
                    name: acc.name + " (" + acc._type + ")",
                    marker: {
                        enabled: false
                    },
                    data: (metrics.followers || metrics.fans || []).map(a => [this.timestampForXAxis(a.date), a.value]),
                };
            }).filter(a => a.data.length);

            return {
                chart: {
                    type: "areaspline",
                    zoomType: "x"
                },
                colors:[ "#FFDC4D", "#FA4B3F",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: "",
                },
                xAxis: {
                    type: "datetime",
                },
                yAxis: [
                    { // Primary yAxis
                        title: {
                            text: "Followers / Fans",
                        },
                        labels: {
                            format: '{value}',
                        }
                    },
                    
                ],
                plotOptions: {
                    series: {
                        fillOpacity: 0.7
                    },
                    area: {
                        marker: {
                            enabled: false, // Simplified markers for area charts
                            symbol: 'circle',
                            radius: 2,
                            states: {
                                hover: {
                                    enabled: true
                                }
                            }
                        }
                    }
                },
                series: followersData
            };
        },
        engagementsChart(){
            const engagementsData = this.postMetrics.map((item) => {
                const accountId = item.account_id;
                const acc = this.getAccount(accountId);

                const metrics = item.metrics;

                const dataByDate = {};

                Object.keys(metrics).forEach((key) => {
                    const data = metrics[key];
                    if(data){
                        data.forEach(d => {
                            const date = d.date;
                            dataByDate[date] = dataByDate[date] || 0;
                            dataByDate[date] += d.value;
                        });
                    }
                });

                return {
                    name: acc.name + " (" + acc._type + ")",
                    marker: {
                        enabled: false
                    },
                    data: Object.keys(dataByDate).map(date => {
                        return [
                            this.timestampForXAxis(date),
                            dataByDate[date]
                        ];
                    }),
                };
            });

            return {
                chart: {
                    type: "areaspline",
                    zoomType: "x"
                },
                colors:[ "#FFDC4D", "#FA4B3F",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: "",
                },
                xAxis: {
                    type: "datetime",
                },
                yAxis: [
                    { // Primary yAxis
                        title: {
                            text: "Engagements",
                        },
                        labels: {
                            format: '{value}',
                        }
                    },
                    
                ],
                plotOptions: {
                    series: {
                        fillOpacity: 0.7
                    },
                    area: {
                        marker: {
                            enabled: false, // Simplified markers for area charts
                            symbol: 'circle',
                            radius: 2,
                            states: {
                                hover: {
                                    enabled: true
                                }
                            }
                        }
                    }
                },
                series: engagementsData,
            };
        },

        // overview
        accountsOverview(){
            return this.accountMetrics.map(a => {
                const acc = this.getAccount(a.account_id);
                return {
                    account_id: a.account_id,
                    name: acc.name,
                    type: acc.type,
                    _type: acc._type,
                    image: acc.image,
                    metrics: a.latest.filter(d => d.value !== 0), // array of {type, value, timestamp}
                }
            });
        }
    },
    async mounted() {

        await this.fetchData();
    },
    methods: {
        getIconForAccount,
        capitalize,
        isLoading(flag){
            return this.loadingFlags.includes(flag);
        },
        showLoader(flag) {
            if(this.isLoading(flag)){
                return;
            }
            this.loadingFlags.push(flag);
        },
        hideLoader(flag) {
            this.loadingFlags = this.loadingFlags.filter(itm => itm !== flag);
        },
        getAccount(id) {
            const all = this.accounts;
            let account = {};
            all.forEach((acc) => {
                if (acc.id === id) account = acc;
            })
            return account;
        },
        fetchData() {
            this.fetchAccountMetrics();
            this.fetchPostCounts();
            this.fetchPostMetrics();
        },
        async fetchAccountMetrics() {
            this.showLoader('account_metrics');
            try {
                const { data } = await axios.get("/api/v1/insights/accounts/metrics",
                    {
                        params: {
                            // these metrics are not daily metrics, so we have to only get life-time metrics
                            // fans/followers is required for growth chart and other metrics are for overview
                            metrics: [
                                // common
                                "followers",

                                // fb specific
                                "fans",

                                // linkedin specific
                                "total_views",
                            ].join(","),
                            start: this.filter.start,
                            end: this.filter.end,
                            team: this.filter.team,
                            accounts: this.filter.accounts,
                        },
                    });
                this.accountMetrics = data.data;
            } catch (e) {
                axiosErrorHandler(e);
            }
            this.hideLoader('account_metrics');
        },
        async fetchPostCounts(){
            this.showLoader('post_counts');
            try {
                const { data } = await axios.get('/api/v1/insights/posts/counts', {
                    params: {
                        start: this.filter.start,
                        end: this.filter.end,
                        team: this.filter.team,
                        accounts: this.filter.accounts,
                    },
                });
                this.postCounts.data = data;
            } catch (e) {
                axiosErrorHandler(e);
            }
            this.hideLoader('post_counts');
        },
        async fetchPostMetrics() {
            this.showLoader('post_metrics');
            try {
                const { data } = await axios.get("/api/v1/insights/posts/metrics",
                    {
                        params: {
                            metrics: 'comments,likes,shares,pin_clicks,reactions,saved,video_views,plays,retweets,post_views,post_cta_clicks,views_search',
                            start: this.filter.start,
                            end: this.filter.end,
                            team: this.filter.team,
                            accounts: this.filter.accounts,
                            group_by_account: true,
                        },
                    });
                this.postMetrics = data.data;
            } catch (e) {
                axiosErrorHandler(e);
            }
            this.hideLoader('post_metrics');
        },
        timestampForXAxis(timestamp) {
            return this.$momentUTC(timestamp, "YYYY-MM-DD").valueOf();
        },
    }
}
</script>

<style lang="scss" scoped>
i.network-type {
    position: absolute;
    top: 30px;
    left: 30px;

    &.mdi {
        top: 24px;
    }
}
.profile-pic1 {
    display: inline-block;
    border-radius: 50%;
    height: 40px;
    width: 40px;
}
.network-icon {
    width: 20px;
    height: 20px;
}
@media (max-width: 600px){
    .table-responsive{
        border: none !important;
        scrollbar-width: none;
        .card-header{
            border-bottom: 1px solid #F2F4F7;
            background-color: white;
        }    
    }
}
.profile-component{
    min-width: 184px;
}
</style>
