# Things to consider when adding a new type of social network (may not be exhaustive)

- auth/account add process (php, view, js/css - accounts controller, services config, accounts view, custom.scss color classes, accounts.js)
  - add new network config to `config/services.php` if needed
  - add relevant .env variables
  - create socialite provider (if needed)
  - create ApiHelper method to get instance or install sdk if needed
  - add new provider + code in AccountsController
  - add link to connect account in accounts view
  - add icons/svgs
  - add color classes to scss
  - accounts.js, components.js
- Account
  - account type
  - post length
  - attachment types
  - max attachments
  - video limits
  - getApi() method
  - profile pic
  - publishPost method (if needed)
  - testConnection method
  - ... other places where needed
- Publishing / Post
  - post options, validation, and parsing (if needed)
  - publish method
  - permalink method
  - getMetrics method
- Post composer
  - aspect ratios
  - max attachment length
  - video limits etc
  - highlight config for rich text
  - custom options if any
- Preview component
- test automated, all post types
- GetPostMetrics command method
- GetAccountMetrics command method
- Feeds / FeedItem check
- Automations support if needed
- BulkImport php update
  - account types
  - any code for specific validation or mapping of columns
- mobile app update (accountlabel component, css)
- mobile app post editor update (+options)
