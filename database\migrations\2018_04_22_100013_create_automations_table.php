<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAutomationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('automations', function (Blueprint $table) {
            $table->engine = 'InnoDB';

            $table->increments('id');

            $table->string('description'); // what will this automation do

            $table->string('tag')->nullable()->index(); // to tag automation e.g. account:ID_HERE, rss:URL_HERE etc (max length should be considered)
            $table->string('event')->nullable()->index(); // event type
            $table->json('event_data')->nullabe(); // data specific to event

            $table->integer('user_id')->unsigned()->index();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade')->onUpdate('cascade');

            $table->json('rules'); // will store automation rules (action, conditions etc)

            $table->json('options')->nullabe(); // if any data needs to be stored

            $table->boolean('active')->default(true);

            $table->timestamp('executed_at')->nullable(); // last executed

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('automations');
    }
}
