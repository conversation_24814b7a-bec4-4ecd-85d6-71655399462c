@extends('layout.full_width')
@php($title = 'Social Media Glossary & Marketing Terms')
@php($description = 'Check out our A-Z social media glossary for the latest terms, slang, and marketing definitions to boost your digital marketing knowledge.')
@php($image = 'https://socialbu.com/images/redesign/homepage/main-section.webp')
@php($url = 'https://socialbu.com/glossary')
@section('title', $title . ' - ' . config('app.name'))
@push('head_html')
<meta name="description" content="{{ $description }}" />
<link rel="canonical" href="{{ $url }}" />

<meta property="og:locale" content="en_US" />
<meta property="og:title" content="{{ $title }}" />
<meta property="og:description" content="{{ $description }}" />
<meta property="og:url" content="{{ $url }}" />
<meta property="og:site_name" content="SocialBu" />
<meta property="og:image" content="{{ $image }}" />
<meta property="fb:app_id" content="{{ config('services.facebook.client_id') }}" />

<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:image" content="{{ $image }}" />
<meta name="twitter:title" content="{{ $title }}" />
<meta name="twitter:description" content="{{ $description }}" />
<meta name="twitter:site" content="@socialbuapp" />


<style>
    .glossary-alphabet-nav{
        a{
            color: #353F54 !important;
        }
        a:hover{
            background-color: #E4E7ED;
        }
        a.active{
            background-color: rgba(16, 63, 152, 0.08) !important;
            border: 1px solid rgba(228, 231, 237, 1) !important;
        }
    }
    .glossary{
        .card-text{
            word-break: break-all;
        }
        .glossary-item-card{
            height: 197px;
        }
    }

    .glossary-cta-img{
        min-height: 590px;
        object-fit: cover;
    }
    .glossary-cta-details{
        padding: 40px;
        bottom: 0px;
    }
    .glossary-search-bar{
        width: 370px;
        .form-control{
            padding-left: 40px;
        }
        .search-icon{
            color: #98A1B2;
            top: 16px;
            left: 16px;
        }
    }
</style>
@endpush

@section('content')
    <header class="header">
        <div class="container">
            <div class="row">
                <div class="col-12 col-md-6 pt-6">
                    <h1 class="display-1 text-center text-md-left mb-4">Social Media Glossary and Definitions</h1>
                    <p class="lead-2 text-center text-md-left pt-1 mb-6">
                        Do you want to understand the most popular social media terms? Check out our glossary below to discover everything about social media.
                    </p>
                    <form class="glossary-search-bar position-relative" method="POST" id="search_glossary_item">
                            <input class="form-control rounded-md shadow-none" type="search" placeholder="Search glossary" />
                            <i class="position-absolute search-icon ph ph-magnifying-glass ph-md"></i>
                    </form>
                </div>
                <div class="d-none d-md-block col-12 col-md-6">
                    <img class="img-responsive hover-move-up lozad position-relative" src="/images/1x1.gif" data-src="/images/wordpress/main-image.webp" alt="Social media glossary" />
                </div>
            </div>
        </div>
    </header>

    <main class="main-content" id="main">
        <section class="section glossary-sticky-section py-0 border-bottom bg-white">
            <div class="container px-0 py-3">
                <div class="row">
                    <div class="col-md-12">
                        <div class="d-flex align-items-center flex-md-nowrap flex-wrap justify-content-md-start justify-content-center glossary-alphabet-nav">
                            @foreach (range('A', 'Z') as $letter)
                                <a class="btn btn-sm" href="#{{ $letter }}">{{ $letter }}</a>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>

        </section>
        <section class="section py-6">
            <div class="container rounded-2xl pt-2">
                <div class="row">
            
                    @if(count($data) > 0)
                        @foreach($data as $alphabet => $items)
                            <div class="col-md-12 glossary-alphabet-section" id="{{ $alphabet }}">
                                <h3 class="font-weight-800 display-3 mb-4">{!! $alphabet !!}</h3>
                            </div>
                            @foreach($items as $item)
                                <div class="col-md-3 col-12 glossary pr-1 pb-20">
                                    <a href="{{ route('glossary.item', $item['slug']) }}">
                                        <div class="card bg-light glossary-item-card">
                                            <div class="card-body p-20">
                                                <h3 class="card-title font-weight-600">
                                                    {!! $item['title']['rendered'] !!}
                                                </h3>
                                                <div class="card-text">
                                                    {!! substr($item['excerpt']['rendered'],0, 80).'...' !!}
                                                </div>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            @endforeach
                        @endforeach
                    @else
                        <div class="alert alert-info w-100">No glossary terms found.</div>
                    @endif
                        
                    
                </div>
            </div>
        </section>
        <section class="section">
            <div class="container">
                <div class="position-relative">
                    <img class="d-md-block d-none img-responsive glossary-cta-img hover-move-up lozad rounded-2xl" src="/images/wordpress/glossary-cta.webp" alt="Run Your Social Media on Autopilot">
                    <img class="d-md-none img-responsive glossary-cta-img hover-move-up lozad rounded-2xl" src="/images/wordpress/gossary-cta-mobile.webp" alt="Run Your Social Media on Autopilot">
                    <div class="glossary-cta-details position-absolute container text-white">
                        <div class="row">
                            <div class="col-md-8 px-md-4 px-0">
                                <h2 class="display-1">Run Your Social <br> Media on Autopilot</h2>
                            </div>
                            <div class="col-md-4 px-md-4 px-0 mt-auto">
                                <a class="btn btn-primary btn-lg mb-4 w-100 d-flex align-items-center justify-content-center" href="/auth/register">
                                    Get Started Now
                                    <i class="ml-2 ph ph-arrow-right ph-lg"></i>
                                </a>
                                <p class="text-center mb-0">Cancel anytime. Free for 7 days. </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

@endsection
