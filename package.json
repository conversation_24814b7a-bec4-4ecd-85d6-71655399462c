{"private": true, "scripts": {"development": "mix", "dev": "npm run development", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "production": "mix --production", "prod": "npm run production", "echo-server": "laravel-echo-server", "api-docs": "api2html -o local/api.html -l shell,javascript--nodejs openapi.yaml -c resources/assets/images/redesign/logo-white-bg.svg"}, "devDependencies": {"@babel/plugin-proposal-object-rest-spread": "^7.17.3", "@chenfengyuan/vue-carousel": "^1.0.2", "@fullcalendar/bootstrap": "^5.7.0", "@fullcalendar/core": "^5.7.0", "@fullcalendar/daygrid": "^5.7.0", "@fullcalendar/interaction": "^5.7.0", "@fullcalendar/moment-timezone": "^5.7.0", "@fullcalendar/timegrid": "^5.7.0", "@fullcalendar/vue": "^5.7.0", "@sentry/browser": "^7.28.1", "@sentry/tracing": "^7.28.1", "@vue/compiler-sfc": "^3.2.31", "alertifyjs": "^1.8.0", "aos": "^2.3.3", "autolinker": "^1.8.1", "axios": "^0.26.1", "babel-plugin-syntax-dynamic-import": "^6.18.0", "bootstrap": "^4.3.1", "bootstrap-select": "^1.13.18", "compressorjs": "^1.0.1", "copy-to-clipboard": "^3.3.1", "cross-env": "^5.1.4", "dompurify": "^2.3.6", "emoji-mart": "^3.0.1", "emoji-mart-vue": "^2.6.6", "exponential-backoff": "^3.1.0", "fabric": "^3.6.3", "filesize": "^3.6.0", "floatthead": "^2.2.1", "fontfaceobserver": "^2.1.0", "highlight-within-textarea": "^2.0.5", "http-proxy": "^1.18.1", "humanize-plus": "^1.8.2", "jquery": "^3.5.1", "jquery-autosize": "^1.18.18", "jquery-serializejson": "^2.9.0", "js-cookie": "^2.2.0", "json-loader": "^0.5.7", "lang.js": "^1.1.12", "laravel-echo": "^1.5.1", "laravel-localization-loader": "^1.0.5", "laravel-mix": "^6.0.43", "laravel-mix-criticalcss": "^1.0.2", "laravel-vue-pagination": "^1.2.0", "liquidjs": "^10.20.2", "localtunnel": "^2.0.2", "lodash": "^4.17.20", "lozad": "^1.15.0", "marked": "^5.1.0", "moment": "^2.21.0", "moment-timezone": "^0.5.13", "node-cache": "^5.1.2", "papaparse": "^5.2.0", "portal-vue": "^1.5.0", "prosemirror-autocomplete": "^0.3.1", "prosemirror-commands": "^1.2.1", "prosemirror-history": "^1.2.0", "prosemirror-inputrules": "^1.1.3", "prosemirror-keymap": "^1.1.5", "prosemirror-model": "^1.16.1", "prosemirror-schema-basic": "^1.1.2", "prosemirror-state": "^1.3.4", "prosemirror-view": "^1.23.8", "resolve-url-loader": "^5.0.0", "sass": "^1.49.9", "sass-loader": "^12.1.0", "select2": "^4.0.13", "select2-bootstrap4-theme": "^1.0.0", "select2-theme-bootstrap4": "^1.0.1", "shepherd.js": "^7.1.5", "socket.io-client": "^2.2.0", "sortablejs": "^1.7.0", "spin.js": "^2.3.2", "tempusdominus-bootstrap-4": "^5.1.2", "twitter-text": "^3.1.0", "validator": "^13.7.0", "validatorjs": "^3.22.1", "vue": "^2.6.14", "vue-async-computed": "^3.3.1", "vue-loader": "^15.9.8", "vue-nl2br": "0.0.5", "vue-perfect-scrollbar": "^0.1.0", "vue-quill-editor": "^3.0.6", "vue-template-compiler": "^2.6.14", "zxcvbn": "^4.4.2"}, "engines": {"node": ">=8.5.0", "npm": ">=5.4.2"}, "dependencies": {"@igpapi/android": "bitbucket:usamaejaz/igpapi-android", "@igpapi/core": "bitbucket:usamaejaz/igpapi-core", "@igpapi/sticker": "bitbucket:usamaejaz/igpapi-sticker", "@sentry/node": "^4.1.1", "body-parser": "^1.19.1", "debug": "^4.1.0", "dotenv": "^10.0.0", "express": "^4.17.1", "fix-esm": "^1.0.1", "fs-extra": "^7.0.1", "heic2any": "^0.0.4", "highcharts": "^10.2.0", "highcharts-vue": "^1.4.0", "koa": "^2.5.3", "koa-bodyparser": "^4.2.1", "laravel-echo-server": "^1.6.2", "node-fetch": "^2.6.1", "puppeteer": "^1.17.0", "quill": "latest", "replicate-js": "github:nicholascelestin/replicate-js", "sqlite3": "^4.0.4", "stability-client": "^1.8.0", "tempy": "^0.2.1", "typedi": "^0.10.0", "vuedraggable": "^2.24.3"}}