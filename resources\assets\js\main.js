import Vue from "vue";
import * as Sentry from "@sentry/browser";
import { BrowserTracing } from "@sentry/tracing";
import {spinnerHtml } from "./components";

if (process.env.MIX_APP_ENV === "production") {
    Sentry.init({
        dsn: process.env.MIX_SENTRY_DSN,
        integrations: [new BrowserTracing()],
        // performance monitoring transactions to capture (% of all requests)
        tracesSampleRate: 0.0001,
        allowUrls: [/https?:\/\/((www)\.)?socialbu\.com/],
    });
}

// lazy load main/all.js using import on mousemove, scroll or click or after 3000ms
let allJsLoaded = false;
function loadAllJs (event = null) {

    if(event) {
        event.currentTarget.removeEventListener(event.type, loadAllJs); // remove the event listener that got triggered
    }

    if (allJsLoaded) return;
    allJsLoaded = true;

    import("./main/all.js");
}

setTimeout(()=> loadAllJs(), 5000);
document.addEventListener('scroll', loadAllJs);
document.addEventListener('mousemove', loadAllJs);
document.addEventListener('touchstart', loadAllJs);
document.addEventListener('click', loadAllJs);

window.__loadComponent = (name, selector, cb = null) => {
    const el = document.querySelector(selector);
    if (!el) return console.error("Selector " + selector + " not found in document.");
    el.innerHTML = spinnerHtml; // default loading spinner
    const mountCb = c => {
        if (c.default) {
            c = c.default; // fix after mix upgrade
        }
        const instance = new (Vue.extend(c))().$mount(el);
        cb && cb(instance);
    };
    if (name === "generate-tool-output") {
        import("./vue/components/tools/GenerateOutput.vue").then(mountCb);
    }
};
