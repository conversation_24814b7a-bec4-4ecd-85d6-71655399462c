<?php

namespace App\Observers;

use App\Feed;
use App\FeedPost;
use App\Helpers\EmailListHelper;
use Illuminate\Support\Collection;

class FeedObserver
{

    /**
     * Handle the feed "created" event.
     *
     * @param  \App\Feed  $feed
     * @return void
     */
    public function created(Feed $feed)
    {
        // send event
        try {
            EmailListHelper::getInstance()->sendEvent($feed->user, 'feed_created', $feed->getName());
            trigger_team_activity($feed->team, $feed->user);
        } catch (\Exception $exception){
            report($exception);
        }
    }

    /**
     * Handle the feed "deleting" event.
     *
     * @param  \App\Feed  $feed
     * @return void
     */
    public function deleting(Feed $feed)
    {
        //
    }

    /**
     * Handle the feed "deleted" event.
     *
     * @param  \App\Feed  $feed
     * @return void
     */
    public function deleted(Feed $feed)
    {
        // send event
        try {
            EmailListHelper::getInstance()->sendEvent($feed->user, 'feed_deleted', $feed->getName());
        } catch (\Exception $exception){
            report($exception);
        }
    }

}
