@extends('layout.full_width')
@php($title = 'Automatically Post From RSS Feeds')
@php($description = 'SocialBu\'s Automation can auto-post social media posts from your RSS feeds. The best RSS to Social Media solution.')
@php($image = 'https://socialbu.com/images/site/post-from-rss-800.png')
@php($url = 'https://socialbu.com/post-from-rss')
@section('title', $title . ' | ' . config('app.name'))
@push('head_html')
    <meta name="description" content="{{ $description }}"/>
    <link rel="canonical" href="{{ $url }}" />

    <meta property="og:locale" content="en_US" />
    <!--
    <meta property="og:type" content="website" />
    -->
    <meta property="og:title" content="{{ $title }}" />
    <meta property="og:description" content="{{ $description }}" />
    <meta property="og:url" content="{{ $url }}" />
    <meta property="og:site_name" content="SocialBu" />
    <meta property="og:image" content="{{ $image }}" />
    <meta property="fb:app_id" content="{{ config('services.facebook.client_id') }}" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content="{{ $image }}" />
    <meta name="twitter:title" content="{{ $title }}" />
    <meta name="twitter:description" content="{{ $description }}" />
    <meta name="twitter:site" content="@socialbuapp" />
@endpush
@section('content')

    <header class="header">
        <div class="container">
            <div class="row">
                <div class="col-12 col-md-6 py-6" style="">
                    <h1 class="font-weight-600">
                        Auto Post From RSS Feeds
                    </h1>
                    <p class="lead mt-5 mb-6 lead-2">
                        Automatically post social media posts from RSS feeds. Automate and publish content to your social media accounts with customizable post formats and schedule.
                    </p>
                    <a class="btn btn-lg btn-primary btn-round" href="/auth/register">
                        Try now
                    </a>
                </div>
                <div class="d-none d-md-block col-12 col-md-6">
                    <img src="/images/1x1.gif" data-src="/images/site/post-from-rss-800.png" alt="post from rss" class="img-responsive hover-move-up lozad" />
                </div>
            </div>
        </div>
    </header>

    <main class="main-content text-dark" id="main">
        <section class="section overflow-hidden bg-light border-top border-secondary pb-0">
            <div class="container">

                <div class="row gap-y align-items-center">

                    <div class="col-md-12 mb-8 text-center">
                        <img src="/images/1x1.gif" data-src="/images/site/post-from-rss-automation-1.jpg" alt="post from rss to social media" data-aos="fade-left" class="img-responsive shadow border border-secondary rounded aos-init aos-animate lozad">
                    </div>

                    <div class="col-md-6 h-200">
                        <h2 class="h4">
                            Automate content publishing to your social media
                        </h2>
                        <p>
                            Publish from RSS feeds to your social media without any hassle. Auto-post content to all your social media accounts with SocialBu Automation.
                        </p>
                    </div>

                    <div class="col-md-6 h-200">
                        <h2 class="h4">
                            Share your blog posts and drive traffic
                        </h2>
                        <p>
                            With SocialBu, keep your social media audience updated with your blog updates and new blog posts. Drive traffic from your social media to your blog.
                        </p>
                    </div>

                    <div class="col-md-6 h-200">
                        <h2 class="h4">
                            Fully customizable post format and schedule
                        </h2>
                        <p>
                            Customize social media posts to include any keyword, hashtag, or content format that you want. You set how your post will look and when it will be posted.
                        </p>
                    </div>

                    <div class="col-md-6 h-200">
                        <h2 class="h4">
                            Curate and share content from relevant sources
                        </h2>
                        <p>
                            You can publish from any RSS feed so that you never run out of engaging content to share with your social media audience.
                        </p>
                    </div>

                </div>
            </div>
        </section>
        <section class="section overflow-hidden bg-light border-bottom border-secondary pt-0">
            <div class="container">
                <div class="row gap-y align-items-center">

                    <div class="col-md-6">
                        <div class="card card-body border">
                            <h2 class="card-title">
                                Publish Instantly
                            </h2>
                            <p class="small-1">
                                Simply publish to your social media accounts whenever there is a new post or item available in an RSS feed.
                            </p>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card card-body border">
                            <h2 class="card-title">
                                Queue For Later
                            </h2>
                            <p class="small-1">
                                With SocialBu Queues, add the new post to a queue which is customizable and publish content according to your own schedule.
                            </p>
                        </div>
                    </div>

                </div>
            </div>
        </section>

        @include('common.internal.testimonials-block')

        @include('common.internal.join-us-block')

    </main>

@endsection
