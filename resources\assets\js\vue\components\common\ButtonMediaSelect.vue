<template>
    <div :style="{height: hidden ? 0 : 28 + 'px'}" class="mt-1 mt-md-0">
        <template v-if="!hidden">
            <label class="btn btn-sm custom-file-upload text-decoration-none h-100" :title="attachMediaText"
                   @click="selectFile"
                   :class="{'disabled': noUpload, 'cursor-pointer': !noUpload, 'border-right': files && files.length, 'btn-primary': renderedInLibrary}" v-tooltip>
                <i v-if="renderedInLibrary" class="ph ph-cloud-arrow-up" aria-hidden="true"></i>
                <i v-else class="ph ph-arrow-up-right" aria-hidden="true"></i>
                <span>{{ attachMediaText }}</span>
            </label>
            <!-- <div class="badge badge-light border m-1" v-if="!renderedInLibrary" :title="file.name" v-for="(file, index) in files">
                {{ file.name }} <i class="ph ph-x ph-md cursor-pointer" title="Remove" @click="removeFile(index)" v-tooltip></i>
            </div> -->
        </template>

        <input class="d-none" ref="inputEl" type="file" :multiple="multiple"
               @change="mediaUploaded" :disabled="noUpload"/>
    </div>
</template>

<script>
import size from "filesize";
import { truncate } from "lodash";
import Compressor from "compressorjs";
import { alertify, getImageMetadata, getVideoMetadata } from "../../../components";


export default {
    name: "ButtonMediaSelect",
    data() {
        return {
            files: []
        };
    },
    props: ["disabled", "validator", "max", "hidden", 'compressImage', 'onMediaUpdate', 'renderedInLibrary', "btnText"],
    computed: {
        noUpload() {
            if (this.maximumFiles <= this.files.length) {
                return true;
            }
            return !!this.disabled;
        },
        attachMediaText() {
            return this.btnText ? this.btnText : "Attach Media";
        },
        maximumFiles() {
            return this.max || 1;
        },
        multiple() {
            return this.maximumFiles > 1;
        }
    },
    methods: {
        truncate,
        selectFile() {
            // trigger input click
            this.$refs.inputEl.click();
        },
        addFiles(files) {
            return this.onFilesSelected(files);
        },
        setFiles(files) {
            this.files = [...files];
        },
        onFilesSelected(files) {
            const _this = this;
            if (!this.renderedInLibrary && files.length + this.files.length > this.maximumFiles) {
                alertify.error("Too many files. You can only select a maximum of " + this.maximumFiles + " files.");
                this.resetFileInput();
                return;
            }

            if (!this.renderedInLibrary && files.length > 1 && files.find(f => f.type.includes("video") || f.type.includes("gif"))) {
                alertify.error("To attach the video (or a GIF), please select only 1 file.");
                this.resetFileInput();
                return;
            }

            if (!this.renderedInLibrary && files.length && _this.files.find(f => f.type.includes("video") || f.type.includes("gif"))) {
                alertify.error(
                    "You have a video (or a GIF) selected. Please remove the currently selected files and retry"
                );
                this.resetFileInput();
                return;
            }
            if (!this.renderedInLibrary && _this.files.length && files.find(f => f.type.includes("video") || f.type.includes("gif"))) {
                alertify.error(
                    "For videos (and GIFs), only 1 file can be selected. Please remove the currently selected files and retry"
                );
                this.resetFileInput();
                return;
            }

            files.forEach(async file => {
                // validate file size
                if (file.size > process.env.MIX_MAX_FILE_SIZE) {
                    alertify.error(
                        "The file " +
                            file.name +
                            " is too large. Please try a file smaller than " +
                            size(process.env.MIX_MAX_FILE_SIZE)
                    );
                    this.resetFileInput(); // reset file input
                    return;
                }

                const processFile = (file, extraData = {}) => {
                    const maxSizeLimit = Number(
                        file.type.includes("image") ? process.env.MIX_MAX_IMAGE_SIZE : process.env.MIX_MAX_FILE_SIZE
                    );

                    // validate extension and file size
                    if (file.size > maxSizeLimit) {
                        alertify.error(
                            "The file " +
                                file.name +
                                " is too large. Please try a file smaller than " +
                                size(maxSizeLimit)
                        );
                        this.resetFileInput(); // reset file input
                        return;
                    }

                    file._metaData = extraData; // add data to File object

                    // validate file type / ext
                    if (_this.validator(file.type, true, extraData)) {
                        if (
                            _this.files.find(f => f.name === file.name && f.size === file.size && f.type === file.type)
                        ) {
                            alertify.error("The file " + file.name + " is already selected.");
                            _this.removeMedia();
                            return;
                        }
                        _this.files.push(file);
                        _this.$emit("mediaUpdate", {
                            file: file,
                            files: _this.files
                        });
                        _this.removeMedia();
                    } else {
                        _this.removeMedia();
                    }
                };

                if (file.type.includes("image")) {
                    // is an image

                    // file size/type check
                    if (!file.type.includes("gif") && (file.size > process.env.MIX_MAX_COMPRESSED_IMAGE_SIZE || this.compressImage)) {
                        // use compressorjs
                        new Compressor(file, {
                            strict: true,
                            quality: 0.8,
                            checkOrientation: false,
                            maxWidth: 4096,
                            maxHeight: 4096,
                            convertSize: 5000000, // min-size for png to convert to jpg
                            async success(result) {
                                let file = new File([result], result.name, {
                                    type: result.type || ""
                                });
                                const metadata = await getImageMetadata(file);
                                processFile(file, metadata);
                            },
                            error(err) {
                                alertify.error(err.message);
                                _this.resetFileInput(); // reset file input
                            }
                        });
                    } else {
                        const metadata = file._metaData ? file._metaData : await getImageMetadata(file);
                        processFile(file, metadata); // original file
                    }
                } else if (file.type.includes("video")) {
                    // is a video
                    if (["video/mp4", "video/quicktime", "video/x-msvideo"].includes(file.type)) {
                        const metadata = file._metaData ? file._metaData : await getVideoMetadata(file);

                        processFile(file, metadata);
                    } else {
                        alertify.error("We recommend .MP4 video format. Please use an mp4 file.");
                        this.resetFileInput();
                    }
                }
            });
        },
        mediaUploaded(e) {
            let imgElem = this.$refs.inputEl;
            const files = Array.from(imgElem.files);
            this.onFilesSelected(files);
        },
        resetFileInput(resetFiles = true) {
            try {
                // $(this.$refs.inputEl)
                //     .attr("type", "")
                //     .val("")
                //     .attr("type", "file");
                this.$refs.inputEl.value = null;
                if(this.renderedInLibrary && resetFiles) {
                    this.files = [];
                }
            } catch (e) {}
        },
        removeFile(file) {
            let index = this.files.findIndex(f => f.name === file.name && f.size === file.size && f.type === file.type);
            this.files.splice(index, 1);
            this.$emit("mediaUpdate", {
                file: null,
                files: this.files
            });
        },
        removeMedia() {
            this.resetFileInput();
        },
        removeAll() {
            this.removeMedia();
            this.files = [];
            this.$emit("mediaUpdate", {
                file: null,
                files: this.files
            });
        }
    },
    updated() {
        if (this.disabled) this.resetFileInput(false);
    }
};
</script>
<style lang="scss">
.text-decoration-none {
    text-decoration: none !important;
}
.custom-file-upload {
    display: inline-block;
    margin-bottom: 0px;
    cursor: pointer;
    margin-top: -4px;
    border-radius: 3px;
}
</style>
