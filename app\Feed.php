<?php

namespace App;

use <PERSON>\TwitterOAuth\TwitterOAuth;
use App\Helpers\Instagram;
use App\Traits\HasOptions;
use Facebook\Exceptions\FacebookResponseException;
use Facebook\Facebook;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use InstagramAPI\Exception\BadRequestException;
use App\Account;

/**
 * App\Feed
 *
 * @property int $id
 * @property string $name
 * @property int $user_id
 * @property int|null $team_id
 * @property array|null $options
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Account[] $feedAccounts
 * @property-read int|null $feed_accounts_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\FeedPost[] $posts
 * @property-read int|null $posts_count
 * @property-read \App\Team|null $team
 * @property-read \App\User $user
 * @method static bool|null forceDelete()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Feed newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Feed newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Feed ofUser($id = null)
 * @method static \Illuminate\Database\Query\Builder|\App\Feed onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Feed query()
 * @method static bool|null restore()
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Feed userFeeds($id = null)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Feed whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Feed whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Feed whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Feed whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Feed whereOptions($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Feed whereTeamId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Feed whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|\App\Feed whereUserId($value)
 * @method static \Illuminate\Database\Query\Builder|\App\Feed withTrashed()
 * @method static \Illuminate\Database\Query\Builder|\App\Feed withoutTrashed()
 * @mixin \Eloquent
 */
class Feed extends Model
{

    use HasOptions, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name', 'user_id', 'team_id',
    ];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'options' => 'array',
    ];

    /**
     * The attributes that should be mutated to dates.
     *
     * @var array
     */
    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public static function transform(Feed $feed)
    {
        return collect([
            'id' => $feed->id,
            'name' => $feed->getName(),
            'team' => [
                'id' => $feed->team_id > 0 ? $feed->team_id : null,
                'name' => $feed->team_id > 0 ? $feed->team->name : null,
            ],
            // we do array_values because for some reason, sometimes this is not an array but is an object
            'accounts' => array_values($feed->accounts->filter(function($account){
                return in_array($account->type, ['facebook.page', 'twitter.profile', 'instagram.api', ]);
            })->map(function($account){
                return Account::transform($account);
            })->toArray()),
            'users' => $feed->getUsers(),
            'config' => with($feed->getOption('config'), function ($config) use($feed){
                if(!$config){
                    $config = [];
                }
                foreach($config as $k => $c){
                    if($k === 'catchItems') continue; // we manually add this later below
                    if(!$c){
                        unset($config[$k]);
                    }
                }
                return array_merge([
                    'twitterKeywords' => [],
                    'instagramKeywords' => [],
                    'notify' => [],
                    'notifyFrequency' => 'daily',
                    'catchItems' => $feed->getItemsToCatch(),
                ], $config);
            }),
        ]);
    }

    /**
     * Scope a query to fetch resources of a specified user.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $id
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfUser($query, $id = null)
    {
        if(!$id)
            $id = \Auth::id();
        return $query->where('user_id', $id);
    }

    /**
     * Get all feeds available to user
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $id
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Query\Builder
     */
    public function scopeUserFeeds($query, $id = null){
        /** @var User $user */
        if($id){
            $user = User::find($id);
        } else {
            $user = \Auth::user();
        }
        $teamIds = $user->joinedTeams()->get(['id'])->map(function($t){
            return $t->id;
        });
        return $query->where('user_id', $user->id)->orWhereIn('team_id', $teamIds);
    }

    /**
     * Get the user that added the team.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the posts.
     */
    public function posts()
    {
        return $this->hasMany(FeedPost::class);
    }

    /**
     * Get the user that added the team.
     */
    public function team()
    {
        return $this->belongsTo(Team::class);
    }

    /**
     * Get feed name
     */
    public function getName(){
        return $this->name;
    }

    /**
     * Get the associated accounts (feed accounts).
     */
    public function feedAccounts()
    {
        return $this->belongsToMany(Account::class);
    }

    /**
     * Get the associated accounts (team or feed).
     */
    public function accounts()
    {
        if($this->team){
            return $this->team->accounts();
        } else {
            return $this->feedAccounts();
        }
    }

    /**
     * Get users who can use this feed
     * @return Collection
     */
    public function getUsers(){
        return $this->team_id > 0 ? $this->team->members->map(function($user){
            /** @var User $user */
            return User::transform($user);
        }) : ($this->user ?
            collect([User::transform($this->user)]) : collect());
    }

    public function getAvailableItemsToCatch(){
        $ITEMS = [
            'twitter.profile' => [
                'messages',
                'mentions',
            ],
            'facebook.page' => [
                'messages',
                'comments',
                'posts',
                'ratings',
            ],
            'instagram.api' => [
                'comments',
                // 'mentions',
                'messages'
            ],
        ];
        $allNetworks = $this->accounts->map(function($a){
            return $a->type;
        })->unique()->toArray();

        $data = [];
        foreach ($allNetworks as $network){
            if(isset($ITEMS[$network])) {
                $availableItems = $ITEMS[$network];
                foreach ($availableItems as $item) {
                    $data[$item] = $item;
                }
            }
        }

        return array_values($data);

    }

    public function getItemsToCatch(){
        $allItems = $this->getAvailableItemsToCatch();
        $itemsToCatch = $this->getOption('config.catchItems');
        if($itemsToCatch === null){
            $itemsToCatch = $allItems;
        } else {
            // make sure all are in available items
            foreach ( (array) $itemsToCatch as $index => $item){
                if(!in_array($item, $allItems)){
                    unset($itemsToCatch[$index]);
                }
            }
        }
        return $itemsToCatch;
    }

    /**
     * Add posts for this feed
     *
     * @param Account $account
     * @param string $type
     * @param array $posts
     * @throws \Exception
     */
    public function addPosts(Account $account, $type, array $posts)
    {
        foreach ($posts as $data){
            if(is_array($data)) $data = array_to_object($data);
            // \Log::info('finding post');
            if(!$this->findPost($account, $type, $data)) {
                // \Log::info('adding post');
                $this->addPost($account, $type, $data);
                //\Log::info('duplicate post');
            }
        }
    }

    /**
     * Add private note
     *
     * @param int $parent_id
     * @param int $root_id
     * @param $data
     * @param int $user_id
     * @return FeedPost
     */
    public function addNote(int $parent_id, int $root_id, \stdClass $data, int $user_id){

        $feedPost = new FeedPost([
            'feed_id' => $this->id,
            'type' => 'private_note',
        ]);

        $data->attachments = [];

        $feedPost->data = $data;

        $feedPost->user_id = $user_id;
        $feedPost->parent_id = $parent_id;
        $feedPost->read = true;
        $feedPost->root_id = $root_id;

        // update keywords
        $feedPost->keywords = str_limit(implode(',', array_filter([
            $data->content,
        ])), 140);

        /** @var FeedPost $newFeedPost */
        $newFeedPost = $this->posts()->save($feedPost);

        return $newFeedPost;

    }

    /**
     * Add post for this feed
     *
     * @param Account $account
     * @param string $type
     * @param mixed $data
     * @param int $user_id
     * @return false|FeedPost
     * @throws FacebookResponseException
     * @throws \Exception
     */
    public function addPost(Account $account, $type, $data, $user_id = null){

        if($account->type === 'instagram.direct'){
            // no longer supported here
            return false;
        }

        if(!isset($data->attachments)){
            $data->attachments = []; // so that we dont end up with an error about `attachments` not existing
        }

        // note
        if($type === 'private_note'){
            // this is a private note
        }

        if($account->type === 'twitter.profile'){
            if(!$data->user){
                $data->user = new \stdClass(); // so that code dont crash
                report(new \Exception('user object not set'));
            }
            if($type === 'tweet'){
                if(!isset($data->full_text) && isset($data->text)){
                    $data->full_text = $data->text; // work around when full_text is not present (like in the response returned after creating a tweet)
                }
            }
        } else if($account->type === 'facebook.page' || $account->type === 'instagram.api'){
            // sometimes, 'name' is not present on `from` object
            if(!isset($data->from)){
                $data->from = (object) [];
                $data->from->id = null;
            }
            if(!isset($data->from->name)){
                $data->from->name = '** unknown **';
            }
        } else if($account->type === 'instagram.direct'){
            if(isset($data->user)){
                if(!isset($data->user->username) && isset($data->user->name)){
                    $data->user->username = $data->user->name;
                }
                if(!isset($data->user->name) && isset($data->user->full_name)){
                    $data->user->name = $data->user->full_name;
                }
                if(isset($data->user->username) && (!isset($data->user->name) || empty($data->user->name))){
                    $data->user->name = $data->user->username;
                }
                if(!isset($data->user->id) && isset($data->user->pk)){
                    $data->user->id = $data->user->pk;
                }
                if(!isset($data->user->pk) && isset($data->user->id)){
                    $data->user->pk = $data->user->id;
                }
                if(!isset($data->user->image) && isset($data->user->profile_pic_url)){
                    $data->user->image = $data->user->profile_pic_url;
                }
                $data->from = $data->user;
            }
            if($type === 'message'){
                if(!isset($data->from->id)){
                    $data->from->id = null;
                }
                if(!isset($data->item_id) && $data->from->id === $account->account_id){
                    $data->item_id = null;
                }
            } else if($type === 'post'){
                if(!isset($data->media_id) && isset($data->pk)){
                    $data->media_id = $data->pk;
                }
            }
            $isTimestamp = function ($timestamp) {
                if (ctype_digit($timestamp) && strtotime(date('Y-m-d H:i:s', $timestamp)) === (int)$timestamp) {
                    return true;
                } else {
                    return false;
                }
            };

            if(!isset($data->timestamp) || !$isTimestamp($data->timestamp)){
                $data->timestamp = time();
            }
        }

        if(!in_array($type, ['message', 'tweet', 'post', 'comment', ])){
            report(new \Exception('Invalid type passed.'));
            return false;
        };

        // check if the same post is already in db
        // like when sending outgoing reply, we manually insert, but the same post can also come from webhook/cron
        $duplicateCheck = $this->posts()->where('account_id', $account->id)->where('type', $type);

        $feedPost = new FeedPost([
            'feed_id' => $this->id,
            'account_id' => $account->id,
            'type' => $type,
        ]);
        $feedPost->data = $data;

        if ($account->type === 'twitter.profile') {

            if(!isset($data->user->screen_name)){
                $data->user->screen_name = '#unknown';
            }

            if(!isset($data->user->name)){
                $data->user->name = 'A user';
            }

            // update keywords
            $feedPost->keywords = str_limit(implode(',', array_filter([
                $data->user->screen_name,
                $data->user->name,
                ($feedPost->type === 'message' ? $data->message_create->message_data->text : $data->full_text),
            ])), 140);

            // set parent, external id and root
            if($type === 'message'){

                if((clone $duplicateCheck)->where('external_id', (string) $data->id)->exists()) return (clone $duplicateCheck)->where('external_id', (string) $data->id)->first();

                $other_party = $data->message_create->sender_id == $account->account_id ? $data->message_create->target->recipient_id : $data->message_create->sender_id;

                $parent = $this->posts()
                    ->where('account_id', $account->id)
                    ->where('type', 'message')
                    ->where('external_id', (string) $other_party)
                    ->where('root_id', null)
                    ->first();
                if($parent) {
                    // for message, root id and parent id will be same because there is only one level of indentation (sender -> messages)
                    $feedPost->parent_id = $parent->id;
                    $feedPost->root_id = $parent->id;
                    $feedPost->external_id = $data->id; // external id will be just msg id

                    // also update latest data on root post
                    $pData = $parent->data;
                    $pData->latest = $data;
                    $parent->data = $pData;

                    // mark parent as unread too
                    // only if msg was sent by other party
                    if($data->message_create->sender_id != $account->account_id){
                        $parent->read = false;
                    }

                    $parent->save();
                } else {
                    // parent not found, dont process if sender is account itself
                    if($data->message_create->sender_id == $account->account_id) return false;
                    // the root post should have an id which is possible to match easily
                    $feedPost->external_id = $other_party;
                }

                if($data->message_create->sender_id == $account->account_id){
                    $feedPost->read = true;
                }

            } else if($type === 'tweet') {

                if((clone $duplicateCheck)->where('external_id', (string) $data->id_str)->exists()) return (clone $duplicateCheck)->where('external_id', $data->id_str)->first();

                if($data->in_reply_to_status_id_str){
                    $parent = $this->posts()
                        ->where('account_id', $account->id)
                        ->where('external_id', (string) $data->in_reply_to_status_id_str)
                        ->first();
                    if($parent){
                        // so the parent tweet exists in db
                        $feedPost->parent_id = $parent->id;

                        /** @var FeedPost $root */
                        $root = $parent;

                        unset($parent); // free memory, lol

                        // now find the root post
                        while($root->parent_id != null){
                            $root = $root->parent()->first();
                        }

                        // now set root id
                        $feedPost->root_id = $root->id;

                        // update latest
                        $pData = $root->data;

                        // set last from user
                        if($data->user->id_str != $account->account_id){
                            $pData->last_user = $data->user;
                        }

                        $pData->latest = $data;
                        $root->data = $pData;

                        // mark parent as unread too
                        // only if msg was sent by other party
                        if($data->user->id != $account->account_id){
                            $root->read = false;
                        }

                        $root->save();
                    } else {
                        // parent not found, but this tweet is a reply to an existing tweet
                        // so we fetch that existing tweet and insert it before inserting this one
                        // TODO: get and insert parent tweet (later)
                    }
                }

                // external id is tweet id
                $feedPost->external_id = $data->id_str;

                if($data->user->id == $account->account_id){
                    $feedPost->read = true;
                }

            }

        } else if($account->type === 'facebook.page') {

            // set external id for comment or post
            if ($type === 'comment') {
                $feedPost->external_id = $data->comment_id;
            } else if ($type === 'post') {
                $feedPost->external_id = $data->post_id;
            }

            // update keywords
            $feedPost->keywords = str_limit(implode(',', array_filter([
                $data->from->name,
                $data->message,
            ])), 140);

            if ($type === 'message') {

                if ((clone $duplicateCheck)->where('external_id', (string) $data->id)->exists()) return (clone $duplicateCheck)->where('external_id', (string) $data->id)->first();

                $parent = $this->posts()
                    ->where('account_id', $account->id)
                    ->where('type', 'message')
                    ->where('external_id', (string) $data->thread_id)
                    ->where('root_id', null)
                    ->first();

                if ($parent) {
                    $pData = $parent->data;

                    // sometimes fb may send duplicate
                    // so check if current msg is a duplicate for parent
                    if ($pData->id == $data->id) return false;

                    // for message, root id and parent id will be same because there is only one level of indentation (sender -> messages)
                    $feedPost->parent_id = $parent->id;
                    $feedPost->root_id = $parent->id;
                    $feedPost->external_id = $data->id;

                    // update latest
                    $pData->latest = $data;
                    $parent->data = $pData;

                    // mark parent as unread too
                    // only if msg was sent by other party

                    if(!isset($data->from->id)){
                        $data->from->id = null;
                    }
                    if ($data->from->id != $account->account_id) {
                        $parent->read = false;
                    }

                    $parent->save();
                } else {
                    // parent not found, dont process the msg if its from the page itself
                    if(!isset($data->from->id)){
                        $data->from->id = null;
                    }
                    if ($data->from->id == $account->account_id) return false;
                    // set external id so that we can match easily next time for this conversation
                    $feedPost->external_id = $data->thread_id;
                }


                if(!isset($data->from->id)){
                    $data->from->id = null;
                }
                if ($data->from->id == $account->account_id) {
                    $feedPost->read = true; // our own msg shouldn't be marked as new
                }

            } else if ($type === 'comment') {

                if ((clone $duplicateCheck)->where('external_id', (string) $data->comment_id)->exists()) return (clone $duplicateCheck)->where('external_id', (string) $data->comment_id)->first();

                // a second-level comment will have a parent comment
                // a first-level comment will have a parent post
                // so if the parent is not in our system, we will also insert that parent first

                /** @var Facebook $fb */
                $fb = $account->getApi();

                // make sure post is in db
                $parentPost = $this->posts()
                    ->where('account_id', $account->id)
                    ->where('type', 'post')
                    ->where(function ($query) use ($data) {
                        /** @var Builder $query */
                        $q = $query->where('external_id', (string) $data->post_id);
                        if (isset($data->open_graph_story_id)) {
                            $q = $q->orWhere('external_id', $data->open_graph_story_id);
                        }
                        return $q;
                    })
                    ->first();
                if (!$parentPost) {
                    // only continue if the current comment is not by page

                    if(!isset($data->from->id)){
                        $data->from->id = null;
                    }
                    if ($data->from->id == $account->account_id) return false;
                    // insert post in db
                    try {
                        $fbPost = $fb->get('/' . $data->post_id . '?fields=id,message,from,created_time,is_published,full_picture,story')->getGraphNode();
                        $postData = array_to_object($fbPost->asArray());

                        if (!isset($postData->from)) {
                            // strange fb api
                            $postData->from = (object)[
                                'id' => explode('_', $data->post_id)[0],
                            ];
                        }

                        if ($fbPost->getField('source')) {
                            // is a video post
                            $postData->media = $fbPost->getField('source');
                            $postData->video = $postData->media;
                        } else if ($fbPost->getField('full_picture')) {
                            $postData->media = $fbPost->getField('full_picture');
                            $postData->photo = $postData->media;
                        }

                        $postData->post_id = $fbPost->getField('id'); // because we use post_id (as it is set in webhook events)

                        // if there is no `message', may be there is `story`
                        if (!isset($postData->message) && isset($postData->story)) {
                            $postData->message = $postData->story;
                        }

                        if (!isset($postData->message)) {
                            $postData->message = '** not available **';
                        }

                        $parentPost = $this->addPost($account, 'post', $postData); // insert parent post

                    } catch (FacebookResponseException $e) {
                        if ($e->getHttpStatusCode() === 400 || $e->getHttpStatusCode() === 404) {
                            return false;
                        } else if( Str::contains($e->getMessage(), ['permission']) ) {
                            return false;
                        }
                        throw $e;
                    } catch (\Exception $e) {
                        throw $e;
                    }
                }

                if (isset($data->parent_comment_id) && $data->post_id == $data->parent_comment_id) {
                    // fb bug
                    unset($data->parent_comment_id);
                }

                if (isset($data->parent_comment_id)) {
                    // this is a 2nd-level comment
                    $parentComment = $this->posts()
                        ->where('account_id', $account->id)
                        ->where('type', 'comment')
                        ->where('external_id', (string) $data->parent_comment_id)
                        ->first();
                    if (!$parentComment && $parentPost) {
                        // insert parent comment first
                        try {
                            $fbComment = $fb->get('/' . $data->parent_comment_id . '?fields=id,created_time,from,message,application,attachment,can_comment')->getGraphNode();
                            $commentData = array_to_object($fbComment->asArray());

                            if ($att = $fbComment->getField('attachment')) {
                                $commentData->media = $att->getField('media')->getField('image')->getField('src');
                            }

                            $commentData->post_id = $parentPost->external_id; // because we use post_id (as it is set in webhook events)
                            $commentData->comment_id = $data->parent_comment_id; // and current comment id

                            $parentComment = $this->addPost($account, 'comment', $commentData); // insert parent comment

                        } catch (FacebookResponseException $e) {
                            if ($e->getHttpStatusCode() === 400 || $e->getHttpStatusCode() === 404) {
                                // treat as 1st level comment (fb bug for very old posts which dont have nested comments)
                                $parentComment = $parentPost;
                            } else {
                                throw $e;
                            }
                        } catch (\Exception $e) {
                            throw $e;
                        }
                    }

                    $feedPost->parent_id = $parentComment->id;

                    if (!$parentComment->root_id) {
                        $feedPost->root_id = $parentPost->id;
                    } else {

                        $feedPost->root_id = $parentComment->root_id;
                        if ($parentPost->id !== $parentComment->root_id) {
                            // problem
                            report(new \Exception('Parent Post ID' . $parentPost->id . ' != ' . $parentComment->root_id));
                            // fix
                            $parentPost = $parentComment->root;
                        }
                    }
                } else {
                    // 1st-level comment
                    $feedPost->parent_id = $parentPost->id;
                    $feedPost->root_id = $parentPost->id;
                }

                // set last_from and latest
                $parentData = $parentPost->data;
                if(!isset($data->from->id)){
                    $data->from->id = null;
                }
                if ($data->from->id != $account->account_id) {
                    // set last_from (original from)
                    $parentData->last_from = $data->from;
                }
                $parentData->latest = $data;
                $parentPost->data = $parentData;

                // mark parent as unread too
                // only if msg was sent by other party
                if ($data->from->id != $account->account_id) {
                    $parentPost->read = false;
                }

                $parentPost->save();

            }


            if(!isset($data->from->id)){
                $data->from->id = null;
            }
            if ($data->from->id == $account->account_id) {
                $feedPost->read = true;
            }
        } else if($account->type === 'instagram.direct') {

            // set external id for comment or post
            if ($type === 'comment') {
                $feedPost->external_id = $data->comment_id;
            } else if ($type === 'post') {
                $feedPost->external_id = $data->media_id;
                $data->text = $data->caption ? $data->caption->text : '';
            }

            $feedPost->keywords = str_limit(implode(',', array_filter([
                $data->user->username,
                $data->text,
            ])), 140);

            if ($type === 'message') {

                if(isset($data->item_id) && $data->item_id) {
                    if ((clone $duplicateCheck)->where('external_id', (string) $data->item_id)->exists()) return (clone $duplicateCheck)->where('external_id', (string) $data->item_id)->first();
                }

                $parent = $this->posts()
                    ->where('account_id', $account->id)
                    ->where('type', 'message')
                    ->where('external_id', (string) $data->thread_id)
                    ->where('root_id', null)
                    ->first();

                if ($parent) {
                    $pData = $parent->data;

                    if($pData->item_id === $data->item_id){
                        // no need to re add this msg, the parent external id is set to thread_id so we explicitly check for item_id to prevent duplicate
                        return $parent;
                    }

                    // for message, root id and parent id will be same because there is only one level of indentation (sender -> messages)
                    $feedPost->parent_id = $parent->id;
                    $feedPost->root_id = $parent->id;
                    $feedPost->external_id = $data->item_id;

                    // update latest
                    $pData->latest = $data;
                    $parent->data = $pData;

                    // mark parent as unread too
                    // only if msg was sent by other party
                    if ($data->user->id != $account->account_id) {
                        $parent->read = false;
                    }

                    $parent->save();
                } else {
                    // parent not found, dont process the msg if its from the page itself
                    if ($data->user->id == $account->account_id) return false;
                    // set external id so that we can match easily next time for this conversation
                    $feedPost->external_id = $data->thread_id;
                }

                if ($data->user->id == $account->account_id) {
                    $feedPost->read = true; // our own msg shouldn't be marked as new
                }

            } else if ($type === 'comment') {

                if ((clone $duplicateCheck)->where('external_id', (string) $data->comment_id)->exists()) return (clone $duplicateCheck)->where('external_id', (string) $data->comment_id)->first();

                // a second-level comment will have a parent comment
                // a first-level comment will have a parent post
                // so if the parent is not in our system, we will also insert that parent first

                /** @var Instagram $ig */
                $ig = $account->getApi();

                // make sure post is in db
                $parentPost = $this->posts()
                    ->where('account_id', $account->id)
                    ->where('type', 'post')
                    ->where(function ($query) use ($data) {
                        /** @var Builder $query */
                        $q = $query->where('external_id', (string) $data->media_id);
                        return $q;
                    })
                    ->first();
                if (!$parentPost) {

                    if(!isset($data->from->id)){
                        $data->from->id = null;
                    }
                    // only continue if the current comment is not by page
                    if ($data->from->id == $account->account_id) return false;
                    // insert post in db
                    try {

                        $igMedia = $ig->media->getInfo($data->media_id)->getItems()[0];

                        $postData = array_to_object($igMedia->asArray());

                        $postData->media_id = $data->media_id;

                        $postData->timestamp = (int) $igMedia->getTakenAt();

                        $parentPost = $this->addPost($account, 'post', $postData); // insert parent post

                    } catch (BadRequestException $e) {
                        return false;
                    } catch (\Exception $e) {
                        throw $e;
                    }
                }

                if (isset($data->parent_comment_id) && strlen( (string) $data->parent_comment_id) > 0) {
                    // this is a 2nd-level comment
                    $parentComment = $this->posts()
                        ->where('account_id', $account->id)
                        ->where('type', 'comment')
                        ->where('external_id', (string) $data->parent_comment_id)
                        ->first();
                    if (!$parentComment) {
                        // insert parent comment first
                        try {

                            $igComments = $ig->media->getComments($data->media_id, [
                                'target_comment_id' => $data->parent_comment_id,
                            ]);

                            $allComments = $igComments->getComments();
                            $igComment = null;
                            foreach($allComments as $comment){
                                if($comment->getPk() === $data->parent_comment_id) {
                                    $igComment = $comment;
                                }
                            }

                            if(!$igComment) return false;

                            $commentText = $igComment->getText();
                            $user = $igComment->getUser();
                            $parentCommentId = $igComment->getParentCommentId();

                            $commentData = array_to_object([
                                'media_id' => $data->media_id,
                                'comment_id' => $igComment->getPk(),
                                'text' => $commentText,
                                'parent_comment_id' => $parentCommentId,
                                'user' => [
                                    'id' => $user->getPk(),
                                    'username' => $user->getUsername(),
                                    'image' => $user->getProfilePicUrl(),
                                    'name' => $user->getFullName(),
                                ],
                                'timestamp' => $igComment->getCreatedAt(),
                                '_comment' => $igComment->asArray(),
                            ]);

                            $commentData->media_id = $parentPost->external_id; // because we use post_id (as it is set in webhook events)
                            $commentData->comment_id = $data->parent_comment_id; // and current comment id

                            $parentComment = $this->addPost($account, 'comment', $commentData); // insert parent comment

                        } catch (BadRequestException $e) {
                            // treat as 1st level comment
                            $parentComment = $parentPost;
                        } catch (\Exception $e) {
                            throw $e;
                        }
                    }

                    $feedPost->parent_id = $parentComment->id;

                    if (!$parentComment->root_id) {
                        $feedPost->root_id = $parentPost->id;
                    } else {
                        $feedPost->root_id = $parentComment->root_id;
                        if ($parentPost->id !== $parentComment->root_id) {
                            // problem
                            report(new \Exception('Parent Post ID' . $parentPost->id . ' != ' . $parentComment->root_id));
                            // fix
                            $parentPost = $parentComment->root;
                        }
                    }
                } else {
                    // 1st-level comment
                    $feedPost->parent_id = $parentPost->id;
                    $feedPost->root_id = $parentPost->id;
                }

                // set last_from and latest
                $parentData = $parentPost->data;
                if ($data->user->pk != $account->account_id) {
                    // set last_from (original from)
                    $parentData->last_from = $data->user;
                }
                $parentData->latest = $data;
                $parentPost->data = $parentData;

                // mark parent as unread too
                // only if msg was sent by other party
                if ($data->user->pk != $account->account_id) {
                    $parentPost->read = false;
                }

                $parentPost->save();

            }

            if ($data->user->id == $account->account_id) {
                $feedPost->read = true;
            }

        } else if($account->type === 'instagram.api') {

            // set external id for comment or post
            if ($type === 'comment') {
                $feedPost->external_id = $data->comment_id;
            } else if ($type === 'post') {
                $feedPost->external_id = $data->media_id;
                $data->text = '';
                if(isset($data->caption)){
                    if(is_object($data->caption)){
                        $data->text = $data->caption->text;
                    } else {
                        $data->text = $data->caption; // string
                    }
                }
            }

            if($type === 'message'){
                $feedPost->keywords = str_limit(implode(',', array_filter([
                    $data->from->name,
                    $data->message,
                ])), 140);
            } else {
                $feedPost->keywords = str_limit(implode(',', array_filter([
                    $data->user->username,
                    $data->text,
                ])), 140);
            }



            if ($type === 'comment') {

                if ((clone $duplicateCheck)->where('external_id', (string) $data->comment_id)->exists()) return (clone $duplicateCheck)->where('external_id', (string) $data->comment_id)->first();

                // all comments are treated as top-level

                // make sure post is in db
                $parentPost = $this->posts()
                    ->where('account_id', $account->id)
                    ->where('type', 'post')
                    ->where(function ($query) use ($data) {
                        /** @var Builder $query */
                        $q = $query->where('external_id', (string) $data->media_id);
                        return $q;
                    })
                    ->first();

                if (!$parentPost) {

                    if(!isset($data->from->id)){
                        $data->from->id = null;
                    }
                    // only continue if the current comment is not by page
                    if ($data->from->id == $account->account_id) return false;
                    // insert post in db
                    try {

                        $postData = array_to_object($data->media);

                        $postData->media_id = $data->media_id;

                        $parentPost = $this->addPost($account, 'post', $postData); // insert parent post

                    } catch (BadRequestException $e) {
                        return false;
                    } catch (\Exception $e) {
                        throw $e;
                    }
                }

                // 1st-level comment
                $feedPost->parent_id = $parentPost->id;
                $feedPost->root_id = $parentPost->id;

                // set last_from and latest
                $parentData = $parentPost->data;
                if ($data->user->pk != $account->account_id) {
                    // set last_from (original from)
                    $parentData->last_from = $data->user;
                }
                $parentData->latest = $data;
                $parentPost->data = $parentData;

                // mark parent as unread too
                // only if msg was sent by other party
                if ($data->user->pk != $account->account_id) {
                    $parentPost->read = false;
                }

                $parentPost->save();

            } else if ($type === 'message') {

                if ((clone $duplicateCheck)->where('external_id', (string) $data->id)->exists()) return (clone $duplicateCheck)->where('external_id', (string) $data->id)->first();

                $parent = $this->posts()
                    ->where('account_id', $account->id)
                    ->where('type', 'message')
                    ->where('external_id', (string) $data->thread_id)
                    ->where('root_id', null)
                    ->first();

                if ($parent) {
                    $pData = $parent->data;

                    // sometimes fb may send duplicate
                    // so check if current msg is a duplicate for parent
                    if ($pData->id == $data->id) {
                        return false;
                    }

                    // for message, root id and parent id will be same because there is only one level of indentation (sender -> messages)
                    $feedPost->parent_id = $parent->id;
                    $feedPost->root_id = $parent->id;
                    $feedPost->external_id = $data->id;

                    // update latest
                    $pData->latest = $data;
                    $parent->data = $pData;

                    // mark parent as unread too
                    // only if msg was sent by other party

                    if(!isset($data->from->id)){
                        $data->from->id = null;
                    }
                    if ($data->from->id != $account->account_id) {
                        $parent->read = false;
                    }

                    $parent->save();
                } else {
                    // \Log::info($data->from);
                    // parent not found, dont process the msg if its from the page itself
                    if(!isset($data->from->id)){
                        $data->from->id = null;
                    }
                    if ($data->from->id == $account->account_id) {
                        return false;
                    }
                    // set external id so that we can match easily next time for this conversation
                    $feedPost->external_id = $data->thread_id;
                }


                if(!isset($data->from->id)){
                    $data->from->id = null;
                }
                if ($data->from->id == $account->account_id) {
                    $feedPost->read = true; // our own msg shouldn't be marked as new
                }

            }

        } else {
            report(new \Exception('Invalid account type.'));
            return false;
        }

        if($user_id){
            $feedPost->user_id = $user_id;
        }

        /** @var FeedPost $newFeedPost */
        $newFeedPost = $this->posts()->save($feedPost);

        if($account->type === 'twitter.profile' && $type === 'tweet'){
            if($data->favorited){
                $newFeedPost->setOption('liked', true);
            }
            if($data->retweeted){
                $newFeedPost->setOption('retweeted', true);
            }
        }

        return $newFeedPost;

    }

    /**
     * Send reply to a feedPost
     * @param array $options
     * @param $postId
     * @return null
     * @throws \Exception
     */
    public function reply(array $options, $postId){

        /** @var FeedPost $feedPost */
        $feedPost = $this->posts()->where('id', $postId)->first();

        if(!$feedPost){
            throw new \Exception('The post you are replying to is not found.');
        }

        /** @var Account $account */
        $account = $feedPost->account;

        if($account->type === 'instagram.direct'){
            // no longer supported
            return null;
        }

        $data = null; // the `data` for the new feedpost which will result in generating the reply
        $type = null;
        if($account->type === 'twitter.profile'){
            /** @var TwitterOAuth $tw */
            $tw = $account->getApi();
            if($feedPost->type === 'message'){
                $type = 'message';
                // reply to the message
                try {
                    $other_party_id = $feedPost->data->message_create->sender_id == $account->account_id ? $feedPost->data->message_create->target->recipient_id : $feedPost->data->message_create->sender_id;
                    $res = $tw->post("direct_messages/events/new", [
                        'event' => [
                            'type' => 'message_create',
                            'message_create' => [
                                'target' => [
                                    'recipient_id' => $other_party_id,
                                ],
                                'message_data' => [
                                    'text' => $options['reply'],
                                ],
                            ],
                        ],
                    ], true);
                    $data = $res->event;
                    $data->user = $feedPost->data->user; // we simply re use old user object
                } catch(\Exception $e) {
                    throw new \Exception( 'Unable to send reply through Twitter');
                }
                if($tw->getLastHttpCode() != 200){
                    throw new \Exception( 'Unable to send reply through Twitter: Error ' . $tw->getLastHttpCode());
                }
            } else if($feedPost->type === 'tweet'){
                $type = 'tweet';
                // generate tweet
                try {
                    $res = $tw->post("statuses/update", [
                        'status' => $options['reply'],
                        'auto_populate_reply_metadata' => 'true',
                        'in_reply_to_status_id' => $feedPost->external_id,
                        'tweet_mode' => 'extended',
                    ]);
                    $data = $res;
                } catch(\Exception $e) {
                    throw new \Exception('Unable to send reply through Twitter');
                }
                if($tw->getLastHttpCode() != 200){
                    throw new \Exception('Unable to send reply through Twitter: Error ' . $tw->getLastHttpCode());
                }
            }
        } else if($account->type === 'facebook.page'){
            /** @var Facebook $fb */
            $fb = $account->getApi();
            if($feedPost->type === 'message'){
                $type = 'message';
                // reply to the message
                try {

                    $res = $fb->post('/me/messages', [
                        'messaging_type' => 'RESPONSE',
                        'recipient' => [
                            'id' => $feedPost->data->from->id,
                        ],
                        'message' => [
                            'text' => $options['reply']
                        ],
                    ]);

                    $msgId =  $res->getDecodedBody()['message_id'];

                    // insert data manually
                    $data = array_to_object([
                        'created_time' => time(),
                        'from' => [
                            'id' => $account->account_id,
                            'name' => $account->name,
                        ],
                        'id' => $msgId,
                        'media_url' => null,
                        'message' => $options['reply'],
                        'sticker_url' => null,
                        'thread_id' => $feedPost->data->thread_id,
                        'to' => $feedPost->data->from->id != $account->account_id ? $feedPost->data->from : $feedPost->data->to,
                    ]);
                } catch (\Exception $e){
                    throw new \Exception( 'Unable to reply: ' . $e->getMessage(), 0, $e);
                }
            } else if($feedPost->type === 'post' || $feedPost->type === 'comment'){
                $type = 'comment';
                // add a child comment if possible, else add comment to the post
                $fb_post_id = null;
                if(!isset($feedPost->data->comment_id)){
                    $fb_post_id = $feedPost->data->post_id;
                    if(!Str::contains($fb_post_id, '_')){
                        // person/page-id_post-id
                        if(isset($feedPost->data->from, $feedPost->data->from->id)) {
                            $fb_post_id = $feedPost->data->from->id . '_' . $fb_post_id;
                        }
                    }
                } else {
                    // replying to comment
                    if( isset($feedPost->data->parent_comment_id) ){
                        $fb_post_id = $feedPost->data->parent_comment_id;
                    } else {
                        $fb_post_id = $feedPost->data->comment_id;
                    }
                }
                try {
                    $res = $fb->post('/' . $fb_post_id. '/comments', [
                        'message' => $options['reply'],
                    ]);
                    // insert data manually
                    $data = array_to_object([
                        'from' => [
                            'id' => $account->account_id,
                            'name' => $account->name,
                        ],
                        'item' => 'comment',
                        'comment_id' => $res->getDecodedBody()['id'],
                        'post_id' => $feedPost->data->post_id,
                        'verb' => 'add',
                        'parent_id' => $feedPost->type === 'post' ? $feedPost->data->post_id : ( isset($feedPost->data->parent_comment_id) ? $feedPost->data->parent_comment_id : $feedPost->data->comment_id ),
                        'created_time' => time(),
                        'message' => $options['reply'],
                    ]);
                    // set parent comment id
                    if($feedPost->type === 'comment'){
                        $data->parent_comment_id = isset($feedPost->data->parent_comment_id) ? $feedPost->data->parent_comment_id : $feedPost->data->comment_id;
                    }
                } catch (\Exception $e){
                    throw new \Exception( 'Unable to reply: ' . $e->getMessage());
                }
            }
        } else if($account->type === 'instagram.direct'){
            /** @var Instagram $ig */
            $ig = $account->getApi();
            if($feedPost->type === 'message'){
                $type = 'message';

                // reply to the message
                try {
                    $res = $ig->direct->sendText(['thread' => $feedPost->data->thread_id], $options['reply']);

                    // insert data manually
                    $data = array_to_object([
                        'from' => [
                            'id' => $account->account_id,
                            'name' => $account->name,
                        ],
                        'user' => [
                            'id' => $account->account_id,
                            'name' => $account->name,
                        ],
                        //'id' => $res,
                        'response' => $res->asArray(),
                        'text' => $options['reply'],
                        'thread_id' => $feedPost->data->thread_id,
                    ]);
                } catch (\Exception $e){
                    throw new \Exception( 'Unable to reply: ' . $e->getMessage());
                }
            } else if($feedPost->type === 'post' || $feedPost->type === 'comment'){
                $type = 'comment';
                // add a child comment if possible, else add comment to the post
                try {

                    $parentCommentId = null;

                    // parent comment id if we need to add child comment
                    if(isset($feedPost->data->parent_comment_id) && $feedPost->data->parent_comment_id)
                        $parentCommentId = $feedPost->data->parent_comment_id;
                    else if(isset($feedPost->data->comment_id))
                        $parentCommentId = $feedPost->data->comment_id;

                    if($parentCommentId){
                        // required by instagram
                        $options['reply'] = '@' . $feedPost->data->user->username . ' ' . $options['reply'];
                    }

                    $res = $ig->media->comment($feedPost->data->media_id, $options['reply'], $parentCommentId);

                    // insert data manually
                    $data = array_to_object([
                        'from' => [
                            'id' => $account->account_id,
                            'name' => $account->name,
                        ],
                        'user' => [
                            'id' => $account->account_id,
                            'name' => $account->name,
                        ],
                        'media_id' => $feedPost->data->media_id,
                        'comment_id' => $res->getComment()->getPk(),
                        'text' => $options['reply'],
                        'parent_comment_id' => $parentCommentId,
                        'response' => $res->asArray(),
                    ]);

                } catch (\Exception $e){
                    throw new \Exception( 'Unable to reply: ' . $e->getMessage());
                }
            }
        } else if($account->type === 'instagram.api'){
            
            /** @var Instagram $insta */
            $insta = $account->getApi();

            if($feedPost->type === 'post' || $feedPost->type === 'comment'){

                $myUsername = json_decode($account->token, true)['username'];

                $media = $feedPost->type === 'post' ? $feedPost->data : $feedPost->data->media;
                $isMention = (isset($media->username) && $media->username !== $myUsername) || (isset($media->user, $media->user->username) && $media->user->username !== $myUsername);
                $text = ($feedPost->type === 'post' || $feedPost->data->user->username === $myUsername ? '' : '@' . $feedPost->data->user->username . ' ') . $options['reply'];

                if($isMention){

                    if(!isset($feedPost->data->mentioned) || !$feedPost->data->mentioned){
                        // whatever comment/post we replying to should mention us, only then we can respond
                        throw new \Exception('You cannot add comment because you were not directly mentioned in this ' . $feedPost->type);
                    }

                    $fb_endpoint = '/' . $account->account_id . '/mentions';
                    $postData = [
                        'message' => $text,
                        'media_id' => $feedPost->data->media_id,
                    ];
                    if($feedPost->type === 'comment'){
                        // replying to a comment mention
                        $postData['comment_id'] = $feedPost->data->comment_id;
                    }
                } else {
                    $fb_endpoint = $feedPost->type === 'post' ? '/' . $feedPost->data->id . '/comments' : '/' . $feedPost->data->comment_id . '/replies';
                    $postData = [
                        'message' => $text,
                    ];
                }

                $type = 'comment';
                // add a child comment if possible, else add comment to the post



                try {
                    $res = $insta->post($fb_endpoint, $postData);
                    // insert data manually
                    $data = array_to_object([
                        'from' => [
                            'id' => $account->account_id,
                            'pk' => $account->account_id,
                            'name' => $account->name,
                            'username' => $myUsername,
                        ],
                        'user' => [
                            'id' => $account->account_id,
                            'pk' => $account->account_id,
                            'name' => $account->name,
                            'username' => $myUsername,
                        ],
                        'media_id' => $feedPost->data->media_id,
                        'comment_id' => $res->getDecodedBody()['id'],
                        'text' => $text,
                        '_comment' => [
                            'id' => $res->getDecodedBody()['id'],
                            'text' => $text,
                            'timestamp' => time(),
                            'username' => $myUsername,
                        ],
                        'timestamp' => time(),
                        'media' => $feedPost->type === 'comment' ? $feedPost->data->media : $feedPost->data,

                        'parent_id' => $feedPost->id,
                    ]);
                } catch (\Exception $e){
                    throw new \Exception( 'Unable to reply: ' . $e->getMessage());
                }
            }else if($feedPost->type === 'message'){
                $type = 'message';
                // reply to the message
                try {

                    $res = $insta->post('/'. $account->account_id .'/messages', [
                        'messaging_type' => 'RESPONSE',
                        'recipient' => [
                            'id' => $feedPost->data->from->id,
                        ],
                        'message' => [
                            'text' => $options['reply']
                        ],
                    ]);

                    $msgId =  $res->getDecodedBody()['message_id'];

                    // insert data manually
                    $data = [
                        'created_time' => time(),
                        'from' => [
                            'id' => $account->account_id,
                            'name' => $account->name,
                        ],
                        'id' => $msgId,
                        'media_url' => null,
                        'message' => $options['reply'],
                        'sticker_url' => null,
                        'thread_id' => $feedPost->data->thread_id,
                        'to' => $feedPost->data->from->id != $account->account_id ? $feedPost->data->from : $feedPost->data->to,
                        'timestamp' => time(),
                    ];

                    $data = array_to_object($data);

                } catch (\Exception $e){
                    throw new \Exception( 'Unable to reply: ' . $e->getMessage(), 0, $e);
                }
            }
        }

        if(!$data || !$type){
            report(new \Exception('`data` or `type` is not set when replying to feed:' . $this->id .', feedPost:' . $feedPost->id));
            throw new \Exception('Something is wrong');
        }

        // add the new feed post
        try {
            return $this->addPost($account, $type, $data, \Auth::id());
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Find post by matching data
     *
     * @param Account $account
     * @param string $type
     * @param mixed $data
     * @return FeedPost|null
     */
    public function findPost(Account $account, $type = null, $data){
        $posts = $this->posts()->when($type, function ($query, $type){
            /** @var Builder $query */
            return $query->where('type', $type);
        });
        if(isset($data->id_str, $data->id)){
            $data->id = $data->id_str; // IMPORTANT: must use string for external_id search, else it is too slow
        }
        if($account->type === 'facebook.page'){
            if($type === 'post'){
                return $posts->where('external_id', (string) $data->post_id)->first();
            } else if($type === 'comment') {
                // do we need to do something with parent comment id?
                return $posts->where('external_id', (string) $data->comment_id)->first();
            } else if($type === 'message'){
                return $posts->where('external_id', (string) $data->id)->first();
            } else if(!$type){
                return $posts->where('external_id', (string) (isset($data->comment_id) ? $data->comment_id : $data->post_id))->first();
            }
        } else if($account->type === 'twitter.profile') {
            if($type === 'message'){
                return $posts->where('external_id', (string) $data->id)->first();
            } else if($type === 'tweet'){
                return $posts->where('external_id', (string) $data->id)->first();
            }
        } else if($account->type === 'instagram.direct'){
            if($type === 'message'){
                return $posts->where('external_id', (string) $data->item_id)->first();
            } else if($type === 'comment'){
                return $posts->where('external_id', (string) $data->comment_id)->first();
            } else if($type === 'post'){
                return $posts->where('external_id', (string) $data->media_id)->first();
            }
        }
        return null;
    }

    /**
     * Find and update post
     *
     * @param Account $account
     * @param string $type
     * @param mixed $data
     */
    public function findAndUpdatePost(Account $account, $type, $data){
        $feedPost = $this->findPost($account, $type, $data);
        if(!$feedPost){ // not found, this means the post which was updated was not found so we just ignore that. may be it is an old post which is updated
            // delete attachments if any
            if(isset($data->attachments)){
                foreach($data->attachments as $attachment){
                    \Storage::delete($attachment->path);
                }
            }
            return;
        }
        if(!isset($data->attachments)){
            $data->attachments = []; // so that we dont end up with an error about `attachments` not existing
        }
        if($account->type === 'facebook.page'){
            // sometimes, 'name' is not present on `from` object
            if(!isset($data->from->name)){
                $data->from->name = '** unknown **';
            }
        }
        $feedPost->data = $data;

        // update keywords
        if ($account->type === 'twitter.profile') {
            $feedPost->keywords = str_limit(implode(',', array_filter([
                $data->user->screen_name,
                $data->user->name,
                ($feedPost->type === 'message' ? $data->message_create->message_data->text : $data->full_text),
            ])), 140);
        } else if($account->type === 'facebook.page'){
            $feedPost->keywords = str_limit(implode(',', array_filter([
                $data->from->name,
                $data->message,
            ])), 140);
        }

        $feedPost->save();
    }

    /**
     * Find and delete post
     *
     * @param Account $account
     * @param string $type
     * @param mixed $data
     * @throws \Exception
     */
    public function findAndDeletePost(Account $account, $type, $data){
        $feedPost = $this->findPost($account, $type, $data);
        if(!$feedPost){ // ok good
            // delete attachments if any
            if(isset($data->attachments)){
                foreach($data->attachments as $attachment){
                    \Storage::delete($attachment->path);
                }
            }
            return;
        }

        $this->deletePost($feedPost);
    }

    /**
     * Delete the feed post
     *
     * @param FeedPost $feedPost
     * @param bool $deleteExternally
     * @throws \Exception
     */
    public function deletePost(FeedPost $feedPost, $deleteExternally = false){

        // first delete child feedPost if any
        /** @var FeedPost[]|Collection $childPosts */
        $feedPost->children()->chunk(100, function($childPosts){
            $childPosts->each(function($feedPost){
                /** @var FeedPost $feedPost */
    
                $this->deletePost($feedPost);
            });
        });
    
        // delete attachments if any
        foreach((array) $feedPost->data->attachments as $attachment){
            \Storage::delete($attachment->path);
        }

        if($deleteExternally){
            // delete the post from external network
            /** @var Account $account */
            $account = $feedPost->account;

            if($account->type === 'instagram.direct'){
                return;
            }

            $data = $feedPost->data;
            $canDelete = false;
            if($account->type === 'twitter.profile' && $feedPost->type !== 'message' && $data->user->id_str == $account->account_id)
                $canDelete = true;
            else if($account->type === 'facebook.page' &&  $feedPost->type !== 'message'/* && $data->from->id == $account->account_id*/)
                $canDelete = true;
            else if($account->type === 'instagram.direct' &&  $feedPost->type !== 'message'/* && $data->from->id == $account->account_id*/)
                $canDelete = true;
            else if($account->type === 'instagram.api' &&  $feedPost->type === 'comment' && ($data->from->id == $account->account_id || $feedPost->root->data->user->id == $account->account_id))
                $canDelete = true;

            if($canDelete){
                try {
                    // delete post now
                    if ($account->type === 'twitter.profile') {
                        /** @var TwitterOAuth $tw */
                        $tw = $account->getApi();
                        $response = $tw->post("statuses/destroy/" . $feedPost->external_id);
                        if ($tw->getLastHttpCode() != 200) {
                            throw new \Exception('Unable to delete tweet');
                        }
                    } else if ($account->type === 'facebook.page') {
                        /** @var Facebook $fb */
                        $fb = $account->getApi();
                        $fb->delete('/' . $feedPost->external_id);
                    } else if ($account->type === 'instagram.direct') {
                        /** @var Instagram $ig */
                        $ig = $account->getApi();
                        if($feedPost->type === 'post') {
                            $media = $ig->media->getInfo($feedPost->external_id)->getItems()[0];
                            $ig->media->delete($feedPost->external_id, $media->getMediaType());
                        } else if($feedPost->type === 'comment'){
                            $ig->media->deleteComment($feedPost->data->media_id, $feedPost->external_id);
                        }
                    } else if ($account->type === 'instagram.api') {
                        /** @var Facebook $ig */
                        $fb = $account->getApi();

                        $fb->delete($feedPost->external_id);

                    }
                } catch (\Exception $exception){
                    // may be user deleted externally thats why
                    report($exception);
                }
            }
        }

        // now delete the post
        $feedPost->delete();
    }

    private function getOptions(){
        return (array) $this->options;
    }

    private function setOptions($options)
    {
        $this->options = $options;
        $this->save();
        return $this;
    }

}
