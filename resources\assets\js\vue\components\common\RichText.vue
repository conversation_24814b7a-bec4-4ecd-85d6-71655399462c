<template>
    <div
        class="editor_parent h-100">
        <div class="editor_element h-100"
             ref="element">
        </div>
        <!-- autocomplete popup -->
        <div :style="`z-index: 1;`"
             class="autocomplete_popup_container"
            ref="autocompletePopup">
            <div class="dropdown-menu border p-0"
                 ref="autocompletePopupContent"
                 :style="`width:400px;overflow-y:auto;max-height: ${autocompletePopup.maxHeight ? autocompletePopup.maxHeight + 'px' : 'auto'};`"
                 :class="{'show': autocompletePopup.show && (autocompleteEntries.length || autocompletePopup.loading )}">
                <div class="card border"
                     v-if="autocompleteHandler && autocompleteHandler.loader && autocompletePopup.loading"
                     v-html="spinnerHtml"
                ></div>
                <div v-else
                     class="list-group list-group-flush p-2">
                    <div class="list-group-item cursor-pointer border-0 rounded-md"
                         :class="{'bg-lightest text-primary': autocompletePopup.selected === index}"
                         v-for="(item, index) in autocompleteEntries"
                         ref="autocompleteEntry"
                         @click.stop="selectAutocompleteEntry(index)"
                         :key="index + 'ac-item'">
                        <div class="row align-items-center">
                            <div class="col-2 p-0 text-center">
                                <i
                                :class="item.icon" v-if="item.icon"></i>
                                <img class="avatar avatar-sm border" alt="avatar"
                                    :src="item.image" v-else-if="item.image" />
                            </div>
                            <div class="col-10 p-0">
                                <div v-if="item.html" v-html="item.html"></div>
                                <div v-else>
                                    <h6 class="mb-0 font-weight-400" :class="{'text-primary': autocompletePopup.selected === index}" v-text="item.title"></h6>
                                    <p class="mb-0 small text-body" v-text="item.description || ''"></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div :class="{'show': autocompletePopup.show && autocompleteEntries.length }" data-popper-arrow></div>
        </div>
    </div>
</template>

<script>
import TwitterText from "twitter-text";
import {DOMParser, DOMSerializer, Schema, Slice} from "prosemirror-model";
import {EditorState, Plugin} from "prosemirror-state";
import {Decoration, DecorationSet, EditorView} from "prosemirror-view";
import {nodes as BasicNodes} from "prosemirror-schema-basic";
import {history, redo, undo} from "prosemirror-history";
import {keymap} from "prosemirror-keymap";
import {baseKeymap} from "prosemirror-commands";
import {debounce} from "lodash";
import autocomplete, {closeAutocomplete, KEEP_OPEN, openAutocomplete} from "prosemirror-autocomplete";
import {createPopper} from '@popperjs/core';
import {spinnerHtml} from '../../../components';
import DOMPurify from 'dompurify';

export default {
    name: "RichText",
    props: {
        value: {
            type: String,
            default: ""
        },
        placeholder: {
            type: String,
            default: ""
        },
        type: {
            type: String,
            default: "text"
        },
        readonly: {
            type: Boolean,
            default: false
        },
        highlight: {
            type: Array,
            default: () => ["mention", "hashtag", "link"]
        },
        autocomplete: {
            type: Object,
            default: () => ({
                possibleTriggers: [":"],
                handlers: []
            })
        },
        marks: {
            type: Object,
            default: () => ({})
        },
        nodes: {
            type: Object,
            default: () => ({})
        },
    },
    data() {
        return {
            editor: null,
            updateOnWatch: true,

            // for autocomplete popup
            autocompletePopup: {

                show: false,
                loading: false,
                selected: null,
                error: null,
                ignoreBlur: false,

                popper: null,
                maxHeight: null,

                range: null,
                trigger: null, // current trigger character if popup is active
                filter: null, // string to filter entries with
                entries: [], // list of autocomplete entries
            },


            // this function is here because of debounce... so it's separate for each instance of the component as all components share the same `methods` definitions
            /** @type Function|DebouncedFunc */
            renderContent: debounce(function(){
                this.$nextTick(()=>{
                    const doc = DOMParser.fromSchema(this.editor.state.schema).parse(
                        (() => {
                            let html = this.value;
                            if (this.type === "text") {
                                html = this.textToHtml(this.value || "");
                            }

                            html = DOMPurify.sanitize(html, { USE_PROFILES: { html: true } });

                            const elem = document.createElement("p");
                            elem.innerHTML = html;

                            return elem;
                        })()
                    );

                    const {tr} = this.editor.state;
                    tr.replace(0, this.editor.state.doc.content.size, new Slice(doc.content, 0, 0));
                    this.editor.dispatch(tr);
                });
            }, 10),
        };
    },
    computed:{
        spinnerHtml: () => spinnerHtml,
        autocompleteEntries(){
            const filter = this.autocompletePopup.filter;
            const arr = (this.autocompletePopup.entries || [])
                .filter(
                    item => filter ? (item.title || "").toLowerCase().includes(filter.toLowerCase()) : true
                );

            // return only 10 entries
            return arr.slice(0, 10);
        },
        autocompleteHandler(){
            return this.autocomplete.handlers.find(item => item.trigger === this.autocompletePopup.trigger);
        }
    },
    watch: {
        value() {
            if (this.updateOnWatch) {
                this.renderContent();
            }
        },
        highlight() {
            if (this.updateOnWatch) {
                this.renderContent();
            }
        }
    },
    methods: {
        getText() {
            const json = this.editor.state.doc.toJSON();
            let text = "";
            let firstParagraphProcessed = false;
            const traverse = node => {
                if (node.type === "text") {
                    text += node.text;
                } else if (node.type === "mention") {
                    text += `@[${node.attrs.id}:${node.attrs.text}]`;
                } else if (node.type === "hard_break") {
                    text += "\n";
                } else {
                    if(node.type === "paragraph"){
                        if(firstParagraphProcessed) {
                            text += "\n";
                        } else {
                            firstParagraphProcessed = true;
                        }
                    }
                    if(node.content){
                        node.content.forEach(child => traverse(child));
                    }
                }
            };
            traverse(json, true);
            return text;
        },
        getHTML() {
            const div = document.createElement("div");
            const fragment = DOMSerializer.fromSchema(this.editor.state.schema).serializeFragment(
                this.editor.state.doc.content
            );

            div.appendChild(fragment);

            return div.innerHTML;
        },
        textToHtml(text = "") {

            text = text.trim("\n");

            const re = /@\[(.*?)]/g;

            let match;
            let mentions = [];
            while ((match = re.exec(text)) !== null) {
                if (!match[1].includes(":")) {
                    continue;
                }
                const parts = match[1].split(":");
                mentions.push({
                    from: match.index,
                    to: match.index + match[0].length,
                    id: parts[0].trim(),
                    text: parts[1].trim(),
                    full: match[0]
                });
            }

            let html = text;

            // replace all mentions with html
            mentions.forEach(mention => {
                html = html.replace(
                    mention.full,
                    `<span data-type="mention" data-id="${mention.id}" data-text="${mention.text}">@${
                        mention.text
                    }</span>`
                );
            });

            // linkify if needed
            if(this.highlight.includes("link") && this.readonly){
                // only linkify links if we need to do that
                const linkReg = TwitterText.regexen.extractUrl;
                html = html.replace(linkReg, function(m0, m1, m2, m3) {
                    // m2 is the character before the actual link because the regex also matches the character before the link. like \n or a space
                    const link = m3;
                    let href = link;
                    if(!href.startsWith("http")){
                        href = "http://" + href;
                    }
                    return `${m2}<a href="${href}" title="${link}" target="_blank">${link}</a>`;
                });
            }

            return html.split("\n").map(item => `<p>${item}</p>` + "\n").join("");
        },
        focus() {
            if (!this.editor.hasFocus()) {
                this.editor.focus();
            }
        },
        initEditor() {
            const schemaJson = {
                nodes: {
                    doc: {
                        ...BasicNodes.doc,
                        // content: "inline*"
                        content: "block+",
                    },
                    paragraph: BasicNodes.paragraph,
                    text: BasicNodes.text,
                    hard_break: BasicNodes.hard_break,
                    // custom nodes
                    mention: {
                        group: "inline",
                        inline: true,
                        draggable: false,
                        selectable: false,
                        atom: true,
                        content: "inline",
                        attrs: {
                            id: {
                                default: null
                            },
                            text: {
                                default: null
                            }
                        },
                        parseDOM: [
                            {
                                tag: "span[data-type=mention]",
                                getAttrs: dom => {
                                    const id = dom.getAttribute("data-id");
                                    const text = dom.getAttribute("data-text");
                                    return {
                                        id,
                                        text
                                    };
                                }
                            }
                        ],
                        toDOM(node) {
                            return [
                                "span",
                                {
                                    "data-type": "mention",
                                    "data-id": node.attrs.id,
                                    "data-text": node.attrs.text,
                                    contenteditable: "false",
                                    class: "text-primary badge badge-pill bg-pale-primary"
                                },
                                0
                            ];
                        }
                    },
                    ... this.nodes,
                },
                marks: {
                    link: {
                        attrs: {
                            href: {},
                            title: {default: null},
                        },
                        inclusive: false,
                        parseDOM: [
                            {
                                tag: "a[href]", 
                                getAttrs: dom => {
                                    const href= dom.getAttribute("href"); 
                                    const title= dom.getAttribute("title");
                                    return {
                                        href,
                                        title,
                                    };
                                }
                            }
                        ],
                        toDOM(node) {
                            const {href, title} = node.attrs;
                            return ["a", {href, title, target: "_blank"}, 0];
                        }
                    },
                    ... this.marks,
                },
            };

            const _this = this;
            this.editor = new EditorView(this.$refs.element, {
                state: EditorState.create({
                    schema: new Schema(schemaJson),
                    plugins: [
                        history(),
                        keymap({
                            // "Mod-a": baseKeymap["Mod-a"],
                            ...baseKeymap,
                            "Mod-z": undo,
                            "Mod-y": redo,
                            // we handle enter ourselves
                            Enter: (state, dispatch) => {

                                // dont if autocomplete popup is open
                                if (_this.autocompletePopup.show && _this.autocompleteEntries.length) {
                                    return false;
                                } else {
                                    return baseKeymap["Enter"](state, dispatch);
                                }

                            },
                            "Shift-Enter": (state, dispatch) => {

                                // dont if autocomplete popup is open
                                if (_this.autocompletePopup.show && _this.autocompleteEntries.length) {
                                    return false;
                                } else {
                                    return baseKeymap["Enter"](state, dispatch);
                                }

                            },
                        }),

                        // highlight mentions, hashtags, links
                        (() => {
                            const getDecorations = doc => {
                                let result = [];

                                // For each node in the document
                                doc.descendants((node, pos, parent) => {
                                    if (node.isText) {
                                        // Scan text nodes for suspicious patterns
                                        let re, m;

                                        if (this.highlight.includes("link") && !this.readonly) {
                                            re = TwitterText.regexen.extractUrl;
                                            while ((m = re.exec(node.text))) {
                                                result.push({ from: pos + m.index, to: pos + m.index + m[0].length });
                                            }
                                        }

                                        if (this.highlight.includes("hashtag")) {
                                            re = TwitterText.regexen.validHashtag;
                                            while ((m = re.exec(node.text))) {
                                                result.push({
                                                    from: pos + m.index,
                                                    to: pos + m.index + m[0].length
                                                });
                                            }
                                        }

                                        if (this.highlight.includes("mention") && parent.type.name !== "mention") {
                                            // only do this for text mentions
                                            re = TwitterText.regexen.validMentionOrList;
                                            while ((m = re.exec(node.text))) {
                                                result.push({
                                                    from: pos + m.index,
                                                    to: pos + m.index + m[0].length
                                                });
                                            }
                                        }

                                        // mastodon mention with domain
                                        if(this.highlight.includes("mastodon_mention") && parent.type.name !== "mention") {
                                            re = /@([a-zA-Z0-9_]+)@([a-zA-Z0-9_\-\.]+)/g;
                                            // re = /@?\b([A-Z0-9._%+-]+)@([A-Z0-9.-]+\.[A-Z]{2,})\b/gmi;
                                            while ((m = re.exec(node.text))) {
                                                result.push({
                                                    from: pos + m.index,
                                                    to: pos + m.index + m[0].length
                                                });
                                            }
                                        }
                                    }
                                });

                                let decos = [];
                                result.forEach(prob => {
                                    decos.push(
                                        Decoration.inline(prob.from, prob.to, {
                                            class: prob.class || "editor-decoration text-primary"
                                        })
                                    );
                                });
                                return DecorationSet.create(doc, decos);
                            };
                            return new Plugin({
                                state: {
                                    init(_, { doc }) {
                                        return getDecorations(doc);
                                    },
                                    apply(tr, old) {
                                        return tr.docChanged ? getDecorations(tr.doc) : old;
                                    }
                                },
                                props: {
                                    decorations(state) {
                                        return this.getState(state);
                                    }
                                }
                            });
                        })(),

                        // placeholder
                        new Plugin({
                            props: {
                                decorations(state) {
                                    const doc = state.doc;

                                    if (doc.content.size > 0) {
                                        return;
                                    }

                                    const placeHolder = document.createElement("div");
                                    placeHolder.className = "placeholder";
                                    placeHolder.innerHTML = _this.placeholder;
                                    return DecorationSet.create(doc, [Decoration.widget(0, placeHolder)]);
                                }
                            }
                        }),

                        ...autocomplete({
                            triggers: this.autocomplete.possibleTriggers.map(trigger => {
                                return {
                                    name: "ac_" + trigger,
                                    trigger,
                                    cancelOnFirstSpace: false,
                                }
                            }),
                            onOpen: ({ view, range, trigger, type }) => {
                                this.showAutocomplete(true, trigger);
                                this.loadAutocompleteData();
                                this.autocompletePopup.range = range;
                                return true;
                            },
                            onArrow: ({ view, kind }) => {
                                if(!this.autocompleteEntries.length){
                                    return;
                                }
                                const isVisible = function (ele, container) {
                                    const { bottom, height, top } = ele.getBoundingClientRect();
                                    const containerRect = container.getBoundingClientRect();

                                    return top <= containerRect.top ? containerRect.top - top <= height : bottom - containerRect.bottom <= height;
                                };
                                if(kind === "ArrowUp"){
                                    let newIndex = this.autocompletePopup.selected - 1;
                                    if(newIndex < 0) {
                                        newIndex = this.autocompleteEntries.length - 1;
                                    }
                                    this.autocompletePopup.selected = newIndex;

                                    const elemToCheck = this.$refs.autocompleteEntry[newIndex - 1 >= 0 ? newIndex - 1 : newIndex];
                                    if( !isVisible(elemToCheck, this.$refs.autocompletePopupContent) || newIndex === 0 || newIndex === this.autocompleteEntries.length - 1 ){
                                        this.$refs.autocompleteEntry[newIndex].scrollIntoView();
                                    }
                                    return true;
                                } else if(kind === "ArrowDown"){
                                    let newIndex = this.autocompletePopup.selected + 1;
                                    if(newIndex >= this.autocompleteEntries.length) {
                                        newIndex = 0;
                                    }
                                    this.autocompletePopup.selected = newIndex;

                                    const elemToCheck = this.$refs.autocompleteEntry[newIndex + 1 <= this.autocompleteEntries.length - 1 ? newIndex + 1 : newIndex];
                                    if( !isVisible(elemToCheck, this.$refs.autocompletePopupContent) || newIndex === 0 || newIndex === this.autocompleteEntries.length - 1 ){
                                        this.$refs.autocompleteEntry[newIndex].scrollIntoView();
                                    }
                                    return true;
                                }
                            },
                            onFilter: ({ view, filter, range }) => {
                                this.autocompletePopup.filter = filter;
                                if(filter.split(" ").length > 1 && this.autocompleteHandler && !this.autocompleteHandler.allowSpaceInQuery){
                                    closeAutocomplete(view);
                                    return true;
                                }
                                this.loadAutocompleteData(true);
                                this.autocompletePopup.range = range;
                                return true;
                            },
                            onEnter: ({ view, range }) => {
                                if(!this.autocompleteEntries.length){
                                    return;
                                }
                                this.autocompletePopup.range = range;
                                this.selectAutocompleteEntry();
                                return KEEP_OPEN;
                            },
                            onClose: ({ view }) => {
                                this.showAutocomplete(false);
                                return true;
                            }
                        }),

                        // on blur
                        new Plugin({
                            props: {
                                handleDOMEvents: {
                                    blur: this.onBlur
                                }
                            }
                        }),
                    ]
                }),
                editable: () => {
                    return !this.readonly;
                },
                dispatchTransaction: tr => {
                    this.editor.updateState(this.editor.state.apply(tr));
                    // debounce and call
                    if (tr.docChanged) {
                        this.onUpdate();
                    }
                },
            });
        },
        showAutocomplete(show = true, trigger = null){
            if(show){
                if(!trigger){
                    console.error("No trigger provided for autocomplete");
                    return;
                }
                this.autocompletePopup.trigger = trigger;
                this.autocompletePopup.error = null;
                this.autocompletePopup.filter = "";
                this.autocompletePopup.entries = [];
                this.autocompletePopup.selected = 0;
                const referenceElem = document.getElementsByClassName('autocomplete')[0];
                this.autocompletePopup.popper = createPopper(referenceElem, this.$refs.autocompletePopup, {
                    placement: "bottom",
                    strategy: "fixed",
                });

                const rect = referenceElem.getBoundingClientRect();
                // maximum height of the popup
                this.autocompletePopup.maxHeight = window.innerHeight - rect.top - rect.height - 20;
            } else {
                this.autocompletePopup.popper.destroy();
            }
            this.autocompletePopup.show = show;
        },
        loadAutocompleteData: debounce(function(isSearch = false){
            const obj = this.autocompleteHandler;
            if(!obj){
                console.error("No autocomplete object found for trigger: " + this.autocompletePopup.trigger);
                closeAutocomplete(this.editor);
                return;
            }
            if( typeof obj.getData !== "function" ){
                console.error("No getData function found for trigger: " + this.autocompletePopup.trigger);
                return;
            }

            this.autocompletePopup.selected = 0;

            if(isSearch && !obj.liveSearch){
                // no live search needed
                return;
            }

            if(obj.loader){
                // loader is enabled
                this.autocompletePopup.loading = true;
            }

            const filter = this.autocompletePopup.filter;
            obj.getData(filter ? filter : null).then(data => {
                this.autocompletePopup.entries = data;
                this.$nextTick(()=>{
                    if(this.$refs.autocompleteEntry && this.$refs.autocompleteEntry[0]){
                        this.$refs.autocompleteEntry[0].scrollIntoView();
                    }
                });
                if(filter && !data.length){
                    closeAutocomplete(this.editor);
                }
                if(obj.loader){
                    this.autocompletePopup.loading = false;
                }
            }).catch(e => {
                console.error(e);
                this.autocompletePopup.error = e.message;
                this.autocompletePopup.entries = [];
                // closeAutocomplete(this.editor);
                if(obj.loader){
                    this.autocompletePopup.loading = false;
                }
            });
        },700),
        selectAutocompleteEntry(index = null){
            this.$nextTick(async ()=>{

                // close if already open
                if(this.autocompletePopup.show){
                    closeAutocomplete(this.editor);
                }

                if(index){
                    // index is set when user manually clicks on an entry
                    this.autocompletePopup.selected = index;
                }
                try {
                    const api = {
                        openAutocomplete: this.openAutocomplete,
                        addMention: this.addMention,
                    };

                    const ret = this.autocompleteEntries[this.autocompletePopup.selected] && this.autocompleteEntries[this.autocompletePopup.selected].onSelect(api);
                    if(typeof ret === "string"){
                        const { tr } = this.editor.state;
                        tr.deleteRange(this.autocompletePopup.range.from, this.autocompletePopup.range.to);
                        tr.insertText(ret);
                        this.editor.dispatch(tr);
                    } else if(ret === false){
                        const { tr } = this.editor.state;
                        tr.deleteRange(this.autocompletePopup.range.from, this.autocompletePopup.range.to);
                        this.editor.dispatch(tr);
                    }
                } catch (e) {
                    console.log(e);
                }
                if(index){
                    this.focus();
                }
            })
        },
        onUpdate() {
            this.updateOnWatch = false;
            const content = this.type === "text" ? this.getText() : this.getHTML();
            this.$emit("input", content);
            this.$nextTick(() => {
                this.updateOnWatch = true;
            });
        },
        onBlur(){
            setTimeout(() => {
                if(this.autocompletePopup.show && !this.autocompletePopup.ignoreBlur){
                    closeAutocomplete(this.editor);
                }
            }, 250);
        },

        // all methods can be used by external components
        openAutocomplete(trigger){
            this.autocompletePopup.ignoreBlur = true;
            setTimeout(()=>{
                openAutocomplete(this.editor, trigger);
                setTimeout(()=>{
                    this.autocompletePopup.ignoreBlur = false;
                }, 500);
            });
        },
        addText(text){
            const { tr } = this.editor.state;
            tr.insertText(text);
            tr.insertText(" ");
            this.editor.dispatch(tr);
        },
        addMention({ id, text }) {
            const { tr } = this.editor.state;
            const mentionNode = this.editor.state.schema.nodes.mention.create(
                {
                    id,
                    text
                },
                this.editor.state.schema.text(`@${text}`)
            );
            tr.insert(this.editor.state.selection.head, mentionNode);
            tr.insertText(" ");
            this.editor.dispatch(tr);
        }
    },
    mounted() {
        this.initEditor();
        this.renderContent();
    },
    beforeDestroy() {
        // destroy previous editor if needed
        if (this.editor) {
            this.editor.destroy();
            this.editor = null;
        }
    }
};
</script>
<style lang="scss" scoped>
/** note: global styles are in global file */

.editor_parent {
    cursor: text;
    line-height: normal !important;
}

.autocomplete_popup_container {
    max-width: 100vw;
    .list-group-item{
        padding: 12px 20px;
    }
    .list-group-item:hover {
        background-color: #f8f9fa;
    }

    [data-popper-arrow] {
        display: none;
        visibility: hidden;
        top: 2px;
        &.show {
            display: block;
        }
    }

    [data-popper-arrow],
    [data-popper-arrow]::before {
        position: absolute;
        width: 8px;
        height: 8px;
        background: #dddddd;
    }

    [data-popper-arrow]::before {
        visibility: visible;
        content: '';
        transform: rotate(45deg);
        margin-left: 4px;
    }
}
</style>
