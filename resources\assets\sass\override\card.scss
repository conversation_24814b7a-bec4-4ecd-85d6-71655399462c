.card {
}
  
  
.card-title {
  margin-bottom: 1rem;
  font-weight: 700;
  font-size: 20px;
  color: $color-title;
  font-family: $font-family-title;
  line-height: 1.2;
}
.card-subtitle {
  margin-top: -8px;
  margin-bottom: 0;
}
.card-text:last-child {
  margin-bottom: 0;
}
.card-link {
  font-family: $font-family-base;
  font-size: $font-size-sm;
  font-weight: 500;
  line-height: 1.5;
}
.card-header {
  background-color: #FAFAFA;
  border-bottom: none;
}
//
// Accordion
//
  
.accordion {
  overflow-anchor: none;
    .card {
      border-radius: 8px;
      // color: red;
      background-color: #fff;
      box-shadow: 0px 10px 40px -12px rgba(29, 33, 41, 0.12);
      border: none !important;
      margin-bottom: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
      &:not(:first-of-type){
        border-radius: 8px;
      }
      &:first-of-type {
        border-radius: 8px;
      }
    }
    
  
    .card-header {
      background-color: #fff;
      color: #1E283B !important;
      font-weight: 500 !important;
      padding: 1rem 1.25rem;
      font-family: $font-family-title;
      border: none;
      .card-title {
          margin-bottom: 0;
          padding-left: 0px;
          padding-right: 0px;
          font-weight: 500;
          background-color: #fff;
          font-size: $font-size-base;
      }
      .card-title a {
          display: block;
          padding: 0px 0px;
          color: $color-title;
  
          &::before {
              width: 8px;
              height: 8px;
              margin-right: 0px;
              margin-left: 0px;
              border-left: 2px solid #252A32;
              border-top: 2px solid #252A32;
          }
      }
    }
    .card-body{
        padding: 0 1.25rem 1.25rem 2.9rem;
    }
}
.startup-accordion{
  .card{
    border: 1px solid #E4E7ED !important;
    margin-bottom: 12px;

  }
}