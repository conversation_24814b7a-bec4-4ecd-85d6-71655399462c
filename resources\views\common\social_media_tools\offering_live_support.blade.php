@extends('layout.full_width')
@section('title', 'Social Media Scheduling Tools With Live Support | ' . config('app.name'))
@push('head_html')

    <meta name="description" content="Customer support is very important for you and your business. Find out the social media scheduling tools that provide live support for their service."/>
    <meta property="og:locale" content="en_US" />
    <meta property="og:type" content="website" />
    <meta property="og:title" content="Social Media Scheduling Tools With Live Support" />
    <meta property="og:description" content="Customer support is very important for you and your business. Find out the social media scheduling tools that provide live support for their service." />
    <meta property="og:url" content="https://socialbu.com/social-media-scheduling-tools/offering-live-support" />
    <meta property="og:site_name" content="SocialBu" />
    <meta property="og:image" content="https://socialbu.com/images/site/link-preview.jpg" />
    <meta property="fb:app_id" content="{{ config('services.facebook.client_id') }}" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:image" content="https://socialbu.com/images/site/link-preview.jpg" />
    <meta name="twitter:title" content="Social Media Scheduling Tools With Live Support" />
    <meta name="twitter:description" content="Customer support is very important for you and your business. Find out the social media scheduling tools that provide live support for their service." />
    <meta name="twitter:site" content="@socialbuapp" />


    <link rel="canonical" href="https://socialbu.com/social-media-scheduling-tools/offering-live-support" />
    
    <style>
        .stickytop{
            position: sticky;
            top: 45px;
            z-index: 10;
        }
    </style>
@endpush
@section('content')
    <header class="header text-center pb-0">
        <div class="container">
            <h1 class="display-2">Social Media Scheduling Tools With Live Support</h1>
            <p class="lead-2 mt-6">A quick overview of {{ count($list) }} social media scheduling tools which offer live chat support.</p>
        </div>
    </header>
    <main class="main-content">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/">Home</a></li>
                    <li class="breadcrumb-item"><a href="/social-media-scheduling-tools/">Social Media Scheduling Tools</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Social Media Scheduling Tools With Live Support</li>
                </ol>
            </nav>
        </div>
        <section class="section bg-light">
            <div class="container">

                <div class="row gap-y">
                    <div class="d-none d-md-block col-md-4 mx-auto">
                        <h5 class="stickytop mb-5">Related pages</h5>
                        @include('common.social_media_tools.sidebar')
                    </div>
                    <div class="col-md-8 col-md-offset-1">

                        @foreach($list as $i => $row)
                            <div class="card p-40 hover-shadow-7 border bg-white {{ $row['name'] === 'SocialBu' ? 'border-primary': 'border-secondary' }}{{ $i === 0 ? ' mt-0 mb-4': ' my-4'}}">
                                <div class="row">
                                    <div class="col-md-3">
                                        @if($row['name'] === 'SocialBu')
                                            <a href="/auth/register" class="position-absolute h-100 d-flex align-items-start">
                                                @if(!empty($row['logo']))
                                                    <img class="d-none d-md-block p-2 w-100 lozad {{ $row['logo_is_white'] ? 'bg-dark':'' }}" src="/images/1x1.gif" data-src="{{ $row['logo'] }}" alt="{{ $row['name'] }} logo">
                                                @else
                                                    <img class="d-none d-md-block p-2 w-100 lozad {{ $row['logo_is_white'] ? 'bg-dark':'' }}" src="/images/email/spacer.gif" alt="{{ $row['name'] }}">
                                                @endif
                                            </a>
                                        @else
                                            <a href="/compare/{{ $row['slug'] }}-alternative" class="position-absolute h-100 d-flex align-items-start">
                                                @if(!empty($row['logo']))
                                                    <img class="d-none d-md-block p-2 w-100 lozad {{ $row['logo_is_white'] ? 'bg-dark':'' }}" src="/images/1x1.gif" data-src="{{ $row['logo'] }}" alt="{{ $row['name'] }} logo">
                                                @else
                                                    <img class="d-none d-md-block p-2 w-100 lozad {{ $row['logo_is_white'] ? 'bg-dark':'' }}" src="/images/email/spacer.gif" alt="{{ $row['name'] }}">
                                                @endif
                                            </a>
                                        @endif
                                    </div>

                                    <div class="col-md-9">
                                        <div class="">
                                            <h4>
                                                <a class="text-primary" href="{{ $row['name'] === 'SocialBu' ? '/auth/register': '/compare/' . strtolower($row['slug']) . '-alternative' }}">
                                                    {{ $row['name'] }}
                                                </a>
                                                @if($row['name'] === 'SocialBu')
                                                    <span class="badge badge-warning rounded">
                                                        Best
                                                    </span>
                                                @endif
                                            </h4>
                                            <p class="{{ $row['name'] === 'SocialBu' ? 'lead': '' }}">
                                                @if(!empty($row['description']))
                                                    {{ $row['description'] }}
                                                    <br/><br/>
                                                @endif
                                                Works with: {{ !empty($row['networks']) ? implode(', ', $row['networks']) : 'N/A' }}<br/>
                                                Customer support: {{ $row['live_chat'] ? 'Yes' : 'N/A'}} <br/>
                                            </p>
                                            <hr/>
                                            <div class="d-flex justify-content-between">
                                                <div>
                                                    @if(!empty($row['starting_price']))
                                                        Pricing starts at {{ $row['starting_price'] }}
                                                        @if(!empty($row['starting_price_notes']))
                                                            <i class="ph ph-info ph-fill" title="{{ implode('. ', $row['starting_price_notes']) }}" data-toggle="tooltip"></i>
                                                        @endif
                                                    @else
                                                        Pricing not available
                                                    @endif
                                                </div>
                                                <div>
                                                    <a href="{{ $row['name'] === 'SocialBu' ? '/auth/register': '/compare/' . strtolower($row['slug']) . '-alternative' }}" class="btn btn-round {{ $row['name'] === 'SocialBu' ? 'btn-primary': 'btn-light' }}">
                                                        {{ $row['name'] === 'SocialBu' ? 'Get Started': 'Compare' }}
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        @endforeach
                    </div>
                </div>
                <div class="d-flex justify-content-between flex-column flex-md-row py-7 py-md-8">
                    <div class="card bg-white col-12 col-md-6 mr-md-2 mb-4 mb-md-0 p-6">
                        <div class="card-body p-6">
                            <h4 class="mb-3 display-4 font-weight-800 ml-5 ml-md-0">Get 7 days for free</h4>
                            <p class="lead">
                                SocialBu gives you 7 days free trial. You can cancel anytime or downgrade to the free plan.
                            </p>
                            <div class="text-center text-md-left mt-4 w-100">
                                <a href="/auth/register" class="btn btn-primary w-100 w-md-auto" aria-label="Try for Free">
                                    Try for Free <i class="ph ph-arrow-right ph-md" aria-hidden="true"></i>
                                </a>
                            </div> 
                        </div>
                    </div>
                    <div class="card bg-white col-12 col-md-6 p-6">
                        <div class="card-body p-6">
                            <h4 class="mb-3 display-4 d-none d-md-block mb-6">Have questions? Let's talk.</h4>
                            <h4 class="display-4 d-block d-md-none mb-5 text-center">Have questions? <br>Let's talk.</h4>
                            <div class="d-flex flex-column align-items-center align-items-md-start w-100 mt-4">
                                <a class="btn btn-outline-primary w-100 w-md-auto mb-3" href="#" onclick="typeof Beacon !== 'undefined' && Beacon('open');return false;">Live Chat</a>
                                <a class="btn btn-outline-primary w-100 w-md-auto" href="mailto:<EMAIL>">Email</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

    </main>
@endsection
