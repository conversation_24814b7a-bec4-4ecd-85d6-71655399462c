<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateInboxConversationItemsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('inbox_conversation_items', function (Blueprint $table) {
            $table->bigIncrements('id');
            
            $table->string('external_id')->index();

            $table->json('data')->nullable(); // data about the item

            $table->json('attachments')->nullable(); // will have attachments we stored locally
            
            $table->string('type');
            $table->string('status')->nullable(); // removed in another migration

            $table->bigInteger('inbox_conversation_id')->unsigned()->index();
            $table->foreign('inbox_conversation_id')->references('id')->on('inbox_conversations')->onDelete('cascade')->onUpdate('cascade');
            
            $table->bigInteger('parent_id')->unsigned()->nullable()->index(); // for nesting of items (comments for example)
            $table->foreign('parent_id')->references('id')->on('inbox_conversation_items')->onDelete('cascade')->onUpdate('cascade');

            $table->bigInteger('root_id')->unsigned()->nullable()->index(); // for nested items, this will be the root item
            $table->foreign('root_id')->references('id')->on('inbox_conversation_items')->onDelete('cascade')->onUpdate('cascade');

            $table->integer('account_id')->unsigned()->index(); // removed in another migration
            $table->foreign('account_id')->references('id')->on('accounts')->onDelete('cascade')->onUpdate('cascade');

            $table->bigInteger('social_contact_id')->unsigned()->nullable()->index();
            $table->foreign('social_contact_id')->references('id')->on('social_contacts')->onDelete('cascade')->onUpdate('cascade');

            $table->json('options')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('inbox_conversation_items');
    }
}
