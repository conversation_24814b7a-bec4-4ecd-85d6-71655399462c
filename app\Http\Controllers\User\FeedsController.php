<?php

namespace App\Http\Controllers\User;

use <PERSON>\TwitterOAuth\TwitterOAuth;
use App\Account;
use App\Feed;
use App\FeedPost;
use App\Team;
use App\User;
use Facebook\Facebook;
use Illuminate\Support\Str;
use function foo\func;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class FeedsController extends Controller
{

    static function available(){

    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\Http\Response|\Illuminate\View\View
     */
    public function index()
    {
        return view('user.feeds.index', [
            'feeds' => Feed::userFeeds()->orderBy('name')->paginate(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\RedirectResponse
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'name' => 'bail|string|required|max:255',
            'team_id' => 'nullable|bail|integer|exists:teams,id',
        ]);

        $user = user();

        $feed = new Feed([
            'user_id' => \Auth::id(),
            'name' => $request->input('name'),
        ]);

        $_user = $user;
        $team = null;
        if($request->input('team_id')){
            /** @var Team $team */
            $team = $user->joinedTeams()->find($request->input('team_id'));
            if(!$team){
                flash('Invalid team selected', 'error');
                return back();
            }

            // check if user can create feed
            if(!$team->hasPermission($user, 'feeds.create')){
                flash('You do not have permission to create a feed for this team', 'error');
                return back();
            }

            $feed->team_id = $team->id;

            $_user = getResourceOwner($team);
        }


        if(getUsage('feeds', $team) + 1 > getPlanUsage('feeds', $team)){
            // show error
            if($user->id == $_user->id) {
                flash('Please upgrade your account for more feeds.', 'warning');
            } else {
                flash('Please upgrade the account associated with the team for performing this action.', 'warning');
            }
            // redirect to settings
            return redirect()->route('settings', ['#billing']);
        }

        $feed->save();
        return redirect()->route('feeds.show', $feed->id);
    }

    /**
     * Display the specified resource.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int $id
     * @return
     */
    public function show(Request $request, $id)
    {
        /** @var Feed $feed */
        $feed = Feed::userFeeds()->findOrFail($id);

        if($feed->team && !$feed->team->hasPermission(user(), 'feeds.view')){

            if($request->expectsJson() || $request->has('json')){
                abort(400, 'You do not have permission to perform this action');
            }

            // user dont has access
            flash('You do not have permission to see this feed', 'warning');
            return redirect(route('feeds.index'));
        }

        if($request->expectsJson() || $request->has('json')){
            return response()->json(Feed::transform($feed));
        }
        return view('user.feeds.show', [
            'feed' => $feed,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  int $id
     * @return void
     * @throws \Illuminate\Validation\ValidationException
     */
    public function update(Request $request, $id)
    {
        /** @var Feed $feed */
        $feed = Feed::userFeeds()->findOrFail($id);

        if($feed->team && !$feed->team->hasPermission(user(), 'feeds.edit')){
            // user dont has access
            abort(404, 'You do not have permission to perform this action');
        }

        $this->validate($request, [
            'name' => 'bail|string|required|max:255',
            'team' => [
                'bail',
                'sometimes',
                'required_without:accounts',
                'integer',
                'exists:teams,id',
                function ($attribute, $value, $fail) use($id) {
                    // make sure the team belongs to the user
                    /** @var Team|null $team */
                    $team = user()->joinedTeams()->find($value);
                    if (!$team) {
                        return $fail($attribute . ' is invalid.');
                    } else {
                        if($team->accounts()->count() === 0){
                            return $fail($attribute . ' has no accounts.');
                        }
                    }
                },
            ],
            'accounts' => 'bail|sometimes|required_without:team|array|min:1',
            'accounts.*' => [
                'bail',
                'sometimes',
                'integer',
                'exists:accounts,id',
                function ($attribute, $value, $fail) {
                    // make sure the account is available to user

                    /** @var User $user */
                    $user = \Auth::user();
                    $availableAccounts = $user->accounts->filter(function($a){
                        return in_array($a->type, ['facebook.page', 'twitter.profile', 'instagram.api', ]);
                    })
                        ->mapWithKeys(function($acc){
                            return [
                                $acc->id => $acc->id,
                            ];
                        });
                    if (!$availableAccounts->has($value)) {
                        return $fail($attribute . ' not available.');
                    }
                },
            ],
            'notify' => 'sometimes|array',
            'notifyFrequency' => 'sometimes|string|in:daily,near_instant',
            'notify.*' => [
                'bail',
                'sometimes',
                'integer',
                'exists:users,id',
                function($attribute, $value, $fail) use($request){
                    /** @var User $user */
                    $user = User::find($value);
                    if(!$user) return $fail($attribute . ' is invalid.'); // validate user
                    if($request->input('team')){
                        /** @var Team $team */
                        $team = Team::find($request->input('team'));
                        // if user is not member of team, give error
                        if($team->members()->wherePivot('user_id', $user->id)->count() === 0){
                            return $fail($attribute . ' is invalid.');
                        }
                    } else {
                        // user can only be current user
                        if($value !== \Auth::id()){
                            $fail($attribute . ' is invalid.');
                        }
                    }
                }
            ],
            'twitterKeywords' => 'sometimes|array',
            'twitterKeywords.*' => 'sometimes|array',
            'twitterKeywords.*.keyword' => 'string|required|max:400|min:1',
            'twitterKeywords.*.account' => [
                'bail',
                'integer',
                'required',
                function($attribute, $value, $fail) use($request){
                    /** @var Account $account */
                    $account = Account::where('type', 'twitter.profile')->where('id', $value)->first();
                    if(!$account) return $fail($attribute . ' is invalid.');
                    if($request->input('team')){
                        /** @var Team $team */
                        $team = Team::find($request->input('team'));

                        // check if account belongs to team
                        if(!$team->accounts()->where('id', $value)->exists()){
                            return $fail($attribute . ' is invalid.');
                        }
                    } else {
                        // account should belong to user
                        if($account->user_id !== \Auth::id()){
                            $fail($attribute . ' is invalid.');
                        }
                    }
                }
            ],
            'instagramKeywords' => 'sometimes|array',
            'instagramKeywords.*' => 'sometimes|array',
            'instagramKeywords.*.keyword' => [
                'bail',
                'string',
                'required',
                'max:50',
                'min:2',
                function($attribute, $value, $fail) use($request){
                    // borrowed from: https://gist.github.com/janogarcia/3946583
                    if(preg_match('/^(?=.{2,140}$)([0-9_\p{L}]*[_\p{L}][0-9_\p{L}]*)$/u', $value) !== 1){
                        $fail('Hashtag is invalid.');
                    }
                }
            ],
            'instagramKeywords.*.account' => [
                'bail',
                'integer',
                'required',
                function($attribute, $value, $fail) use($request){
                    /** @var Account $account */
                    $account = Account::where('type', 'instagram.direct')->where('id', $value)->first();
                    if(!$account) return $fail($attribute . ' is invalid.');
                    if($request->input('team')){
                        /** @var Team $team */
                        $team = Team::find($request->input('team'));

                        // check if account belongs to team
                        if(!$team->accounts()->where('id', $value)->exists()){
                            return $fail($attribute . ' is invalid.');
                        }
                    } else {
                        // account should belong to user
                        if($account->user_id !== \Auth::id()){
                            $fail($attribute . ' is invalid.');
                        }
                    }
                }
            ]
        ]);
        if ($request->input('team')) {

            user()->joinedTeams()->findOrFail($request->input('team'));

            // team feed
            $feed->team_id = $request->input('team');
            // delete all attached accounts (if any) since team feeds dont have accounts and load accounts from team instead
            if ($feed->feedAccounts()->count() > 0)
                $feed->feedAccounts()->sync([]);
        } else {
            // set accounts
            /** @var Collection $accounts */
            $accounts = Account::whereIn('id', $request->input('accounts'))->get(['id', 'account_id']);

            // check duplicates
            foreach($accounts as $acc){
                if($accounts->first(function($account)  use($acc) {
                    return $account->account_id == $acc->account_id && $account->id != $acc->id;
                })){
                    abort(400, 'Duplicate account found.');
                }
            }

            if($accounts->count() === 0){
                abort(400, 'No account selected');
            }

            $feed->feedAccounts()->sync($accounts->map(function($acc){
                return $acc->id;
            }));
            // unset team if set
            if($feed->team_id > 0)
                $feed->team_id = null;
        }
        $feed->name = $request->input('name');

        $feed->save();

        $feed->setOption('config', [
            'notify' => $request->input('notify', []),
            'notifyFrequency' => $request->input('notifyFrequency', 'daily'),
            'twitterKeywords' => $request->input('twitterKeywords', []),
            'instagramKeywords' => $request->input('instagramKeywords', []),
            'catchItems' => $request->input('catchItems', []),
        ]);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @throws \Exception
     */
    public function destroy($id)
    {
        $feed = Feed::userFeeds()->findOrFail($id);

        if($feed->team && !$feed->team->hasPermission(user(), 'feeds.edit')){
            // user dont has access
            abort(404, 'You do not have permission to perform this action');
        }

        $feed->delete();
    }

    /**
     * Get posts.
     *
     * @param \Illuminate\Http\Request $request
     * @param  int $id
     * @return mixed
     */
    public function getPosts(Request $request, $id)
    {
        if(!$request->expectsJson()) abort(400);

        /** @var Feed $feed */
        $feed = Feed::userFeeds()->findOrFail($id);

        if($feed->team && !$feed->team->hasPermission(user(), 'feeds.view')){
            // user dont has access
            abort(404, 'You do not have access');
        }

        $posts = $feed->posts()->select(['id', 'account_id', 'type', 'data', 'read', 'options', 'created_at'])
            ->when($request->input('q'), function ($query, $q){
                /** @var Builder $query */

                if(Str::contains($q, ':')){
                    // may be built in filter
                    $qParts = explode(':', $q);
                    if(count($qParts) > 1) {
                        $filterKey = trim($qParts[0]);
                        $filterVal = trim($qParts[1]);
                        if($filterKey === 'status'){

                            if($filterVal === 'unread'){
                                return $query->where(function($query) use($q){
                                    return $query->where('read', false)->orWhereHas('nested', function ($query) use($q){
                                        return $query->where('read', false);
                                    });
                                });
                            } else if($filterVal === 'read'){
                                return $query->where(function($query) use($q){
                                    return $query->where('read', true)->orWhereHas('nested', function ($query) use($q){
                                        return $query->where('read', true);
                                    });
                                });
                            }

                        }
                    }
                }

                // simple text query
                return $query->where(function($query) use($q){
                    return $query->where('keywords', 'like', '%' . $q . '%')->orWhereHas('nested', function ($query) use($q){
                        return $query->where('keywords', 'like', '%' . $q . '%');
                    });
                });
            })
            ->where('root_id', null)
            ->whereIn('type', ['tweet', 'message', 'post', 'comment',])
            ->latest('id') // IMPORTANT: do not let it use `created_at` because that is not indexed
            ->simplePaginate(40, ['id', 'account_id', 'type', 'data', 'read', 'options', 'created_at']); // important, always use simplePaginate because of performance cost of normal paginate()

        return [
            'include_post' => with($request->input('include_post'), function ($id) use($feed){
                if($id > 0){
                    return $feed->posts()->where('id', $id)->first();
                }
                return null;
            }),
            'posts' =>  $posts,
            // find unread items for each thread; used in ui
            'unread' => $feed->posts()->whereIn('id', collect($posts->items())->map(function($p){return $p->id;}))->where('root_id', '<>', null)->where('read', false)->groupBy('root_id')->get(['root_id', \DB::raw('count(*) as total'),])->mapWithKeys(function($a){
                return [
                    $a->root_id => $a->total,
                ];
            }),
        ];
    }

    /**
     * Get specific post (thread).
     *
     * @param \Illuminate\Http\Request $request
     * @param  int $id
     * @param int $postId
     * @return mixed
     */
    public function getPost(Request $request, $id, $postId)
    {
        //if(!$request->expectsJson()) abort(400);

        /** @var Feed $feed */
        $feed = Feed::userFeeds()->findOrFail($id);

        if($feed->team && !$feed->team->hasPermission(user(), 'feeds.view')){
            // user dont has access
            abort(404, 'You do not have access');
        }

        /** @var FeedPost $post */
        $post = $feed->posts()->where('id', $postId)->where('root_id', null)->firstOrFail(['id', 'account_id', 'user_id', 'type', 'data', 'read', 'options', 'created_at', ]);

        $nested = $post->nested()->limit(1000)->get(['id', 'account_id', 'user_id', 'type', 'data', 'root_id', 'parent_id', 'read', 'options', 'created_at', ])->map(function($post){
            /** @var FeedPost $post */
            $data = $post->data;
            $data->attachments = $post->getAttachments();
            $post->data = $data;
            $arr = $post->toArray();
            $arr['created_at'] = $post->created_at->getTimestamp();
            return $arr;
        })->groupBy('parent_id');

        $postData = $post->data;
        $postData->attachments = $post->getAttachments(); // so that it has public `url` for attachment

        $post->data = $postData;

        $data = $post->toArray();
        $data['created_at'] = $post->created_at->getTimestamp();

        $this->populateChildren($data, $nested);

        return $data;
    }

    /**
     * Recursively populate all child posts for a post using a grouped collection of all posts
     *
     * @param array $postData
     * @param Collection $postsGroupedByParent
     */
    private function populateChildren(array &$postData, Collection $postsGroupedByParent){
        $postData['children'] = $postsGroupedByParent->has($postData['id']) ? $postsGroupedByParent->get($postData['id'])->toArray() : [];
        foreach($postData['children'] as &$child){
            $this->populateChildren($child, $postsGroupedByParent);
        }
    }

    /**
     * Mark read
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @param int $postId
     */
    public function markRead(Request $request, $id, $postId)
    {
        /** @var Feed $feed */
        $feed = Feed::userFeeds()->findOrFail($id);

        if($feed->team && !$feed->team->hasPermission(user(), 'feeds.view')){
            // user dont has access
            abort(404, 'You do not have permission to perform this action');
        }

        // mark as read, the post and all nested posts (if any)
        $feed->posts()->where(function($query) use($postId){
            /** @var Builder $query */
            return $query->where('id', $postId)->orWhere('root_id', $postId)->orWhere('parent_id', $postId);
        })->where('read', false)->update([
            'read' => true,
        ]);

    }

    /**
     * Reply to a feedPost (also generates external reply)
     *
     * @param Request $request
     * @param int $id
     * @param int $postId
     * @return FeedPost|null
     * @throws \Illuminate\Validation\ValidationException
     */
    public function reply(Request $request, $id, $postId){

        $this->validate($request, [
            'reply' => 'required|string|min:2|max:5000',
        ]);

        /** @var Feed $feed */
        $feed = Feed::userFeeds()->findOrFail($id);

        if($feed->team && !$feed->team->hasPermission(user(), 'feeds.view')){
            // user dont has access
            abort(404, 'You do not have permission to perform this action');
        }

        try {
            return $feed->reply($request->all(), $postId);
        } catch (\Exception $exception){
            $errMsg = strtolower($exception->getMessage());
            if( Str::contains($errMsg, ['permissions error', 'permission to manage']) ){
                abort(400, 'Insufficient permissions. Please re-connect your social media account and accept all permission.');
            } else if( Str::contains($errMsg, ['message is sent outside of allowed window']) ){
                abort(400, 'Cannot reply to the message any longer. Allowed window has passed.');
            } else if( Str::contains($errMsg, ['does not exist, cannot be loaded due to missing permissions, or does not support this operation']) ){
                abort(400, 'Cannot reply: the item does not exist, cannot be loaded due to missing permissions, or does not support this operation');
            } else if (Str::contains($errMsg, ['does not exist'])){
                abort(400, $exception->getMessage());
            }
            report($exception);
            abort(400, $exception->getMessage());
        }
        return null;
    }

    /**
     * Add a private note to a feedPost
     *
     * @param Request $request
     * @param int $id
     * @param int $postId
     * @return FeedPost|null
     * @throws \Illuminate\Validation\ValidationException
     */
    public function note(Request $request, $id, $postId){

        $this->validate($request, [
            'reply' => 'required|string|min:1|max:5000',
        ]);

        /** @var Feed $feed */
        $feed = Feed::userFeeds()->findOrFail($id);

        if($feed->team && !$feed->team->hasPermission(user(), 'feeds.view')){
            // user dont has access
            abort(404, 'You do not have permission to perform this action');
        }

        /** @var FeedPost $feedPost */
        $feedPost = $feed->posts()->where('id', $postId)->first();

        if(!$feedPost){
            abort(404, 'The post you are replying to is not found.');
        }

        $data = array_to_object([
            'content' => $request->input('reply'),
        ]);
        $type = 'private_note';

        if(!$data || !$type){
            report(new \Exception('`data` or `type` is not set when replying to feed:' . $feed->id .', feedPost:' . $feedPost->id));
            abort(400, 'Something is wrong');
        }

        // add the new feed post
        try {
            return $feed->addNote($feedPost->id, $feedPost->root_id > 0 ? $feedPost->root_id : $feedPost->id, $data, \Auth::id());
        } catch (\Exception $e) {
            abort(400, $e->getMessage());
        }
        return null;
    }

    /**
     * Like/unlike a feed post
     *
     * @param Request $request
     * @param int $id
     * @param int $postId
     * @return mixed
     */
    public function like(Request $request, $id, $postId)
    {
        /** @var Feed $feed */
        $feed = Feed::userFeeds()->findOrFail($id);

        if($feed->team && !$feed->team->hasPermission(user(), 'feeds.view')){
            // user dont has access
            abort(404, 'You do not have permission to perform this action');
        }

        /** @var FeedPost $feedPost */
        $feedPost = $feed->posts()->where('id', $postId)->firstOrFail();

        try {
            $feedPost->like();
        } catch (\Exception $e) {
            abort(400, $e->getMessage());
        }

        $feedPost->refresh();

        if(!empty($feedPost->options))
            return $feedPost->options;
        else
            return null;

    }

    /**
     * Retweet / Un-retweet a tweet
     *
     * @param Request $request
     * @param int $id
     * @param int $postId
     * @return mixed
     * @throws \Exception
     */
    public function retweet(Request $request, $id, $postId)
    {
        /** @var Feed $feed */
        $feed = Feed::userFeeds()->findOrFail($id);

        if($feed->team && !$feed->team->hasPermission(user(), 'feeds.view')){
            // user dont has access
            abort(404, 'You do not have permission to perform this action');
        }

        /** @var FeedPost $feedPost */
        $feedPost = $feed->posts()->where('id', $postId)->firstOrFail();

        /** @var Account $account */
        $account = $feedPost->account;

        if($account->type === 'twitter.profile' && $feedPost->type === 'tweet'){
            /** @var TwitterOAuth $tw */
            $tw = $account->getApi();
            $tweetId = $feedPost->external_id;
            try {
                $tw->post('statuses/' . ($feedPost->getOption('retweeted') ? 'unretweet' : 'retweet') . '/' . $tweetId);
                // toggle the like value locally
                $feedPost->getOption('retweeted') ? $feedPost->removeOption('retweeted') : $feedPost->setOption('retweeted', true);
            } catch (\Exception $e){
                abort(400, 'Unable to complete the action');
            }
            if($tw->getLastHttpCode() != 200){
                abort(400, 'Unable to complete the action: Error ' . $tw->getLastHttpCode());
            }
        } else {
            abort(400);
        }

        if(!empty($feedPost->options))
            return $feedPost->options;
        else
            return null;

    }

    /**
     * Delete post
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @param int $postId
     * @throws \Exception
     */
    public function deletePost(Request $request, $id, $postId)
    {
        /** @var Feed $feed */
        $feed = Feed::userFeeds()->findOrFail($id);

        if($feed->team && !$feed->team->hasPermission(user(), 'feeds.view')){
            // user dont has access
            abort(404, 'You do not have permission to perform this action');
        }

        /** @var FeedPost $post */
        $post = $feed->posts()->where('id', $postId)->firstOrFail();

        $feed->deletePost($post, $request->has('external_delete'));

    }

}
