<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class DeleteNotifications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'delete:notifications';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Delete old notifications';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $now = \Carbon\Carbon::now();

        \DB::table('notifications')
            ->where('read_at', '<>', null) // is read
            ->where('read_at', '<', $now->subDays(15)) // is 15 days older
            ->delete();
    }
}
