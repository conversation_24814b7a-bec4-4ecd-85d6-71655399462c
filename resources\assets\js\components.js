import alertifyjs from "alertifyjs";
import _axios from "axios";
import _Echo from "laravel-echo";
import $ from "jquery";
import _ from "lodash";
import DOMPurify from 'dompurify';

const ioClient = require("socket.io-client");

export const appConfig = (() => {
    const configElem = document.querySelector('meta[name="x-app-config"]');
    if(!configElem) {
        // console.warn("No app config found");
        return {};
    }
    return JSON.parse(configElem.getAttribute("content"));
})();
export const axios = (() => {
    _axios.defaults.headers.common = {
        "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]').getAttribute("content"),
        "X-Requested-With": "XMLHttpRequest"
    };
    // _axios.defaults.baseURL = "/";
    _axios.interceptors.response.use(
        function(res) {
            const { config } = res;
            // Do something before request is sent
            if (config.url && config.url.includes("publish")) {
                // something to do with posts, so update the menu
                window.__refreshInfo().then(() => {});
            }
            return res;
        },
        function(error) {
            // Do something with request error
            return Promise.reject(error);
        }
    );
    return _axios;
})();
export const alertify = (() => {
    alertifyjs.defaults.autoReset = false;
    alertifyjs.defaults.closable = false;
    alertifyjs.defaults.maximizable = false;
    alertifyjs.defaults.resizable = false;
    //alertifyjs.defaults.transition = "zoom";
    alertifyjs.defaults.theme.ok = "btn btn-primary";
    alertifyjs.defaults.theme.cancel = "btn btn-light";
    alertifyjs.defaults.theme.input = "form-control";
    alertifyjs.defaults.glossary.title = appConfig.name;
    alertifyjs.defaults.transition = "none";
    alertifyjs.set('notifier','delay', 4);
    if(!alertifyjs.delete){
        //define a new errorAlert base on alert
        alertifyjs.dialog('delete',function factory(){
            return{
                prepare:function(){
                    this.setting('title', "Delete");
                },
                setup: function(){
                    return {
                        buttons: [
                            {
                                text: "Delete",
                                key: 13,
                                className: "btn btn-danger mr-2",
                            },
                            {
                                text: alertifyjs.defaults.glossary.cancel,
                                key: 27,
                                invokeOnClose: true,
                                className: alertifyjs.defaults.theme.cancel,
                            }
                        ]
                    };
                }
            };
        },true,'confirm');
    }

    return alertifyjs;
})();
export const axiosErrorHandler = (e, returnError = false) => {
    const err = (() => {
        if (e.response && e.response.data) {
            // axios errors with response
            const errors = e.response.data.errors;
            if (errors) {
                // laravel validation errors
                let error = null;
                Object.keys(errors).some(k => {
                    error = errors[k].join(" \n");
                    return true;
                });
                return error;
            }
            // general errors as `message` or `error`
            if (e.response.data && e.response.data.message) {
                return e.response.data.message;
            } else if (e.response.data && e.response.data.error) {
                return e.response.data.error;
            }
        }
        // generic/catch-all
        return e.message || "An error occurred.";
    })();
    if (!returnError) alertify.error(err);
    return err;
};
export const overlay = {
    show: function() {
        this.hide(); // remove old one if any
        // append overlay to body
        $("main[role='main']").append(
            $(
                '<div id="app_overlay" style="z-index: 1;width:100%;min-height:100vh;height:100%;background:#000;opacity: .5;position:absolute;left: 0;top:50px;right: 0;bottom: 0;"></div>'
            )
        );
    },
    hide: function() {
        $("#app_overlay").remove();
    },
    toggle: function() {
        if ($("#app_overlay").length === 0) this.show();
        else this.hide();
    }
};
export const spinner = {
    show: function() {
        this.hide();
        overlay.hide(); // remove old one if any
        overlay.show();
        $("#app_overlay").spin(true);
    },
    hide: function() {
        $("#app_overlay").spin(false);
        overlay.hide();
    }
};

export const spinnerHtml = `<div class="d-flex p-5 justify-content-center align-items-center"><i class="spinner-border text-primary"></i><span class="sr-only">Loading...</span></div>`;

export const overlayLoaderHtml = `
<div class="d-flex justify-content-center align-items-center position-absolute h-100 w-100" style="z-index: 2;background: rgba(255, 255, 255 , .6);">
    <div class="h-100px">${spinnerHtml}</div>
</div>
`;

let localEcho = null;
export const getEcho = () => {
    if (!localEcho)
        localEcho = new Promise((resolve, reject) => {
            const _echo = new _Echo({
                broadcaster: "socket.io",
                host: process.env.MIX_LARAVEL_ECHO_SERVER_HOST,
                client: ioClient
            });
            _echo.connector.socket.on("connect", function() {
                _axios.defaults.headers.common["X-Socket-ID"] = _echo.socketId();
                //console.log('echo connected', _echo.socketId());
                resolve(_echo);
            });
        });
    return localEcho;
};
export const getColorClassForAccount = accountType => {
    if (!accountType) {
        console.warn("accountType = ", accountType);
        return "";
    }
    const MAP = {
        "instagram.direct": "instagram",
        "instagram.api": "instagram",
        "twitter.profile": "twitter",
        "facebook.": "facebook",
        "linkedin.": "linkedin",
        "google.location": "google-business-profile",
        "mastodon.profile": "mastodon",
        "tiktok.profile": "tiktok",
        "pinterest.profile": "pinterest",
        "google.youtube": "youtube",
        "reddit.profile": "reddit",
        "reddit.subreddit": "reddit",
        "threads.profile": "threads",
        "bluesky.profile": "bluesky",
    };
    let iconClass = MAP[accountType];
    if (!iconClass) {
        for (let key in MAP) {
            if (accountType.includes(key)) {
                iconClass = MAP[key];
                break;
            }
        }
    }
    return iconClass ? iconClass : "";
};

export const getIconClassForAccount = (accountType, color = false) => {
    if (!accountType) {
        console.warn("accountType = ", accountType);
        return "";
    }
    const MAP = {
        "instagram.direct": "ph ph-instagram-logo",
        "instagram.api": "ph ph-instagram-logo",
        "twitter.profile": "ph-fill ph-x-logo",
        "facebook.page": "ph-fill ph-facebook-logo",
        "facebook.group": "fa fa-group",
        "linkedin.": "ph-fill ph-linkedin-logo",
        "google.location": "mdi mdi-google-my-business",
        "mastodon.profile": "ph-fill ph-mastodon-logo",
        "tiktok.profile": "ph-fill ph-tiktok-logo",
        "pinterest.profile": "ph-fill ph-pinterest-logo",
        "google.youtube": "ph-fill ph-youtube-logo",
        "reddit.profile": "ph-fill ph-reddit-logo",
        "reddit.subreddit": "ph-fill ph-reddit-logo",
        "threads.profile": "ph-fill ph-threads-logo",
        "bluesky.profile": "si si-bluesky",
    };
    let iconClass = MAP[accountType];
    if (!iconClass) {
        for (let key in MAP) {
            if (accountType.includes(key)) {
                iconClass = MAP[key];
                break;
            }
        }
    }
    return (iconClass ? iconClass : "") + (color ? " " + getColorClassForAccount(accountType) : "");
};
export const getIconForAccount = (accountType, type = 'original') => {
    if (!accountType) {
        console.warn("accountType = ", accountType);
        return "";
    }
    let network = accountType.split('.')[0];

    // icons by network
    const MAP = {
        "instagram": "/images/redesign/networks/instagram",
        "twitter": "/images/redesign/networks/twitter",
        "facebook": "/images/redesign/networks/facebook",
        "linkedin": "/images/redesign/networks/linkedin",
        "google.location": "/images/redesign/networks/google",
        "mastodon": "/images/redesign/networks/mastodon",
        "tiktok": "/images/redesign/networks/tiktok",
        "google.youtube": "/images/redesign/networks/youtube",
        "reddit": "/images/redesign/networks/reddit",
        "pinterest": "/images/redesign/networks/pinterest",
        "threads": "/images/redesign/networks/threads",
        "bluesky": "/images/redesign/networks/bluesky",
    };
    let icon = MAP[network];

    if (!icon) {
        for (let key in MAP) {
            if (accountType.includes(key)) {
                icon = MAP[key];
                break;
            }
        }
    }
    return icon ? (icon + '-' + type + '.svg') : "";
};

export const isVisibleOnScreen = el => {
    // Special bonus for those using jQuery
    if (typeof jQuery !== "undefined" && el instanceof jQuery) el = el[0];

    const rect = el.getBoundingClientRect();
    const windowHeight = window.innerHeight || document.documentElement.clientHeight;
    const windowWidth = window.innerWidth || document.documentElement.clientWidth;

    return (
        rect.left >= 0 &&
        rect.top >= 0 &&
        rect.left + rect.width <= windowWidth &&
        rect.top + rect.height <= windowHeight
    );
};
export const getSelect2Config = (type, data = {}) => {
    if (!type) throw new Error("Pass a config type e.g. ig_location");
    // return select2 config for passed type
    const config = {
        ig_location() {
            return {
                placeholder: "Type to search",

                templateResult(data) {
                    if (data.loading) return data.text;

                    const $container = $(`
                                            <div>
                                                <div class='__title'></div>
                                                <small class='__subtitle opacity-40'></small>
                                            </div>
                                        `);

                    $container.find(".__title").text(data.title);
                    $container.find(".__subtitle").text(data.subtitle);

                    return $container;
                },
                templateResultSelection(data) {
                    return data.title || data.text;
                },
                escapeMarkup(m) {
                    return m;
                }, // we do not want to escape markup since we are displaying html in results

                ajax: {
                    url: "/app/json/search/ig_locations",
                    dataType: "json",
                    delay: 500,
                    data: function(params) {
                        return {
                            q: params.term // search term
                        };
                    },
                    processResults: (data, params) => {
                        params.page = params.page || 1;

                        return {
                            results: data.items.map((item, index) => {
                                item.value = item.id = JSON.stringify(item);
                                item.text = item.title;
                                // truncate subtitle
                                item.subtitle = _.truncate(item.subtitle, {
                                    length: 80
                                });
                                return item;
                            })
                        };
                    },
                    cache: true
                },
                minimumInputLength: 2
            };
        },
        fb_location(data) {
            return {
                placeholder: "Type to search",

                templateResult(data) {
                    if (data.loading) return data.text;

                    const $container = $(`
                                            <div>
                                                <div class='__title'></div>
                                                <small class='__subtitle opacity-40'></small>
                                            </div>
                                        `);

                    $container.find(".__title").text(data.title);
                    $container.find(".__subtitle").text(data.subtitle);

                    return $container;
                },
                templateResultSelection(data) {
                    return data.title || data.text;
                },
                escapeMarkup(m) {
                    return m;
                }, // we do not want to escape markup since we are displaying html in results

                ajax: {
                    url: "/app/json/search/fb_locations",
                    dataType: "json",
                    delay: 500,
                    data: function(params) {
                        return {
                            q: params.term, // search term
                            ...data
                        };
                    },
                    processResults: (data, params) => {
                        params.page = params.page || 1;

                        return {
                            results: data.items.map((item, index) => {
                                item.value = item.id = JSON.stringify(item);
                                item.text = item.title;
                                // truncate subtitle
                                item.subtitle = _.truncate(item.subtitle, {
                                    length: 80
                                });
                                return item;
                            })
                        };
                    },
                    cache: true
                },
                minimumInputLength: 2
            };
        },
        ig_user(data) {
            return {
                placeholder: "Type to search",

                templateResult(data) {
                    if (data.loading) return data.text;

                    const $container = $(`
                                            <div>
                                                <div class='__title'></div>
                                                <small class='__subtitle opacity-40'></small>
                                            </div>
                                        `);

                    $container.find(".__title").text(data.username);
                    $container.find(".__subtitle").text(data.name);

                    return $container;
                },
                templateResultSelection(data) {
                    return data.title || data.text;
                },
                escapeMarkup(m) {
                    return m;
                }, // we do not want to escape markup since we are displaying html in results

                ajax: {
                    url: "/app/json/search/ig_users",
                    dataType: "json",
                    delay: 500,
                    data: function(params) {
                        return {
                            q: params.term, // search term
                            ...data
                        };
                    },
                    processResults: (data, params) => {
                        params.page = params.page || 1;

                        return {
                            results: data.items.map((item, index) => {
                                item.value = item.id = JSON.stringify(item);
                                item.text = item.username;
                                item.name = _.truncate(item.name, {
                                    length: 80
                                });
                                return item;
                            })
                        };
                    },
                    cache: true
                },
                minimumInputLength: 2
            };
        }
    }[type];
    if (!config) {
        throw new Error(type + " config not found");
    }
    return config(data);
};

export const getImageMetadata = (file) => {

    return new Promise(resolve => {
        const img = new Image();
       img.src = window.URL.createObjectURL( file);

        img.onload = function() {
            const width = img.naturalWidth,
                height = img.naturalHeight;

            window.URL.revokeObjectURL(img.src);

            resolve({
                width,
                height
            });
        };
    });
}
export const getVideoMetadata = (file) => {
    return new Promise(function(resolve) {
        // now process the video
        const vidSrc = window.URL.createObjectURL(file);

        // create the video element
        let video = document.createElement("video");

        // place a listener on it
        video.addEventListener(
            "loadedmetadata",
            function() {
                try {
                    // retrieve dimensions
                    let height = this.videoHeight;
                    let width = this.videoWidth;
                    let duration = this.duration;

                    this.pause();

                    this.src = "";

                    // unload video url from memory
                    window.URL.revokeObjectURL(vidSrc);

                    // send back result
                    resolve({
                        height,
                        width,
                        duration
                    });
                } catch (e) {}
            },
            false
        );

        // start download meta-datas
        video.src = vidSrc;
    });
}

export const sanitizeHtml = (html) => {
    return DOMPurify.sanitize(html, { USE_PROFILES: { html: true } });
}
