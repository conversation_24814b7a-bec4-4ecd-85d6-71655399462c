
<meta charset="utf-8" />
<meta name="csrf-token" content="{{ csrf_token() }}" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta name="viewport" content="width=device-width, initial-scale=1" />
<meta name="x-app-config" content="{{ json_encode([
    "name" => config("app.name"),
    "timezone" => timezone(),
    "userId" => Auth::check() ? \Auth::id() : null,
    "userCompany" => Auth::check() ? user()->company : null,
    "completedTourSteps" => Auth::check() ? user()->getOption("completed_tour_steps", "") : null,
]) }}" />

<!--
<meta name="description" content="" />
-->


<link rel="icon" href="/favicon.ico" />


<title>
    @yield('title')
</title>

<link href="{{ mix('/css/new-app.css') }}" rel="stylesheet" type="text/css" />

<!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
<!--[if lt IE 9]>
<script src="https://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
<script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
<![endif]-->

<script>
    window.dataLayer = window.dataLayer || [];
    window.__recordEvent = function(name, data) {
        // this is for sending event/data to tag manager
        var dataToSend = {
            event: name
        };
        if(data){
            for (var attrname in data) { dataToSend[attrname] = data[attrname]; }
        }
        window.dataLayer.push(dataToSend);
    };
    window.__recordUsageEvent = function(name, value, recordInCrm){
        // sample event names: team_edit, post_composer_opened, post_created, etc (object_action)
        // the tag manager handles this custom dynamic event
        // we remove the prepended string and then record the event in other places like Google Analytics
        if(!value) value = "";
        if(recordInCrm === undefined){
            // record in crm by default
            recordInCrm = true;
        }

        window.__recordEvent("ProductUsage::" + name, {
            usageData: typeof value === "object" ? JSON.stringify(value) : value,
            // below is for services requiring JS object
            jsonUsageData: JSON.stringify(typeof value === "object" ? value : {
                value: value
            }),
        });
        if(window.__recordCrmEvent && recordInCrm){
            // record event by sending it to our backend (which will send it to our CRM)
            window.__recordCrmEvent(name, value);
        }
    };

    @if(\Auth::check())
        window.dataLayer = [{
            userId: "{{ \Auth::id() }}",
            userName: "{{ \Auth::user()->name }}",
            userEmail: "{{ \Auth::user()->email }}",
            userCreatedAt: "{{ \Auth::user()->created_at->toISOString() }}",
            userCompany: "{{ \Auth::user()->company }}",
            userPlan: "{{ \Auth::user()->getPlan(true) }}",
            postCreated: "{{ \Auth::user()->getOption('post_created', 'false') ? 'true' : 'false' }}",
            accountAdded: "{{ \Auth::user()->getOption('account_added', 'false') ? 'true' : 'false'  }}",
            teamJoined: "{{ \Auth::user()->getOption('team_joined', 'false') ? 'true' : 'false'  }}",

        }];
    @endif
    

    @if(session()->has('support_login'))
        window.__recordEvent("SupportAgentActivity");
    @else 
        window.__recordEvent("TriggerActiveCampaign");
    @endif
</script>

@include('layout.includes.common_head')
