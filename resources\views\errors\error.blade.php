<!DOCTYPE html>
<html>
<head>
    <title>{{ $title ?? 'Error!' }}</title>

    <link href="https://fonts.googleapis.com/css?family=Lato:100" rel="stylesheet" type="text/css">

    <style>
        html, body {
            height: 100%;
        }

        body {
            margin: 0;
            padding: 0;
            width: 100%;
            color: #B0BEC5;
            display: table;
            font-weight: 100;
            font-family: 'Lato', sans-serif;
        }

        .container {
            text-align: center;
            max-width: 600px;
            margin: 0 auto;
        }

        .content {
            text-align: center;
            display: inline-block;
            padding: 40px;
        }

        .title {
            margin: 40px;
            padding: 40px;
            border: 1px solid red;
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="content">
        <div class="title">
            @if(!empty($message))
                {{ $message }}
            @elseif(!empty($title))
                {{ $title }}
            @else
                {{ 'Something went wrong.' }}
            @endif
        </div>
        <button type="button" onclick="window.history.back()">Go Back</button>
        @if(app()->bound('sentry') && !empty(\Sentry::getLastEventID()))
            <div class="subtitle">Error ID: {{ \Sentry::getLastEventID() }}</div>

            <!-- Sentry JS SDK 2.1.+ required -->
            <script src="https://cdn.ravenjs.com/3.3.0/raven.min.js"></script>

            <script>
                Raven.showReportDialog({
                    eventId: '{{ \Sentry::getLastEventID() }}',
                    // use the public DSN (dont include your secret!)
                    dsn: 'https://<EMAIL>/3235'/*,
                    user: {
                        'name': 'Jane Doe',
                        'email': '<EMAIL>',
                    }*/
                });
            </script>
        @endif
    </div>
</div>
</body>
</html>
