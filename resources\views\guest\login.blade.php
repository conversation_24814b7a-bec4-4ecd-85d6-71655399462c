@extends('guest.layout')
@section('title', 'Login | ' . config('app.name'))
@section('content')

        <div class="d-md-block d-flex justify-content-center align-items-center min-vh-100">
            <form class="form-auth rounded-2xl" role="form" method="POST" action="{{ $extension ? url('/auth/login?extension=true') : url('/auth/login') }}">
                {{ csrf_field() }}
        
                <div class="text-center mb-5">
                    <a href="/" title="{{ config('app.name') }}"><img class="logo lozad" src="/images/redesign/logo.svg" alt="logo" /></a>
                </div>
                <h5 class="text-center mb-5 font-weight-500">@lang('auth.login_title')</h5>
        
                @include('layout.partials.errors')
        
                @include('layout.partials.flash') 
        
                <div class="form-group mb-4">
                    <label for="email">Email</label>
                    <input type="text" class="form-control {{ $errors->has('email') ? ' has-error' : '' }}" name="email" placeholder="@lang('generic.email')" value="{{ old('email') }}" required="" autofocus="" />
                </div>
                <div class="form-group position-relative mb-2">
                    <label for="password">Password</label>
                    <input type="password" class="form-control {{ $errors->has('email') ? ' has-error' : '' }}" name="password" id="password" placeholder="@lang('generic.password')" required=""/>
                    <i class="ph ph-eye ph-md position-absolute cursor-pointer toggle-password-btn" id="togglePassword"></i>
                </div>
                <div class="float-left mb-5">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" name="remember"> 
                        <label class="form-check-label">
                            Remember Me
                        </label>
                    </div>
                </div>
        
                @if(!$extension)
                <div class="float-right">
                    <a class="small-2" href="{{ url('/auth/reset_password') }}">@lang('auth.forgot_password')</a>
                </div>
                @endif
                <button class="btn btn-primary btn-lg btn-block" type="submit">@lang('generic.login')</button>
        
                @if(!$extension)
                    <p class="text-center mt-4 small-2">
                        @lang('auth.not_registered_msg')<a href="{{ url('/auth/register') }}">@lang('auth.create_an_account')</a>
                    </p>
                    <div class="text-darkmuted lead-1 divider mb-4 mt-5">Or sign up with</div>
                    <div class="d-flex justify-content-center">
                        <a href="{{ url('/auth/facebook') }}" class=" mr-3 d-flex align-items-center justify-content-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
                                <path d="M31.9994 16C31.9994 7.16352 24.836 0 15.9997 0C7.16338 0 0 7.16352 0 16C0 23.5034 5.16598 29.7997 12.1348 31.529V20.8896H8.83566V16H12.1348V13.8931C12.1348 8.44736 14.5994 5.9232 19.9458 5.9232C20.9596 5.9232 22.7087 6.12224 23.4242 6.32064V10.7526C23.0466 10.713 22.3906 10.6931 21.5759 10.6931C18.9526 10.6931 17.9388 11.687 17.9388 14.2707V16H23.165L22.2671 20.8896H17.9388V31.8829C25.8612 30.9261 32 24.1805 32 16H31.9994Z" fill="#0866FF"/>
                                <path d="M22.2674 20.8895L23.1653 15.9999H17.9391V14.2706C17.9391 11.6869 18.9529 10.693 21.5762 10.693C22.3909 10.693 23.0469 10.7129 23.4244 10.7525V6.32054C22.7089 6.1215 20.9599 5.9231 19.9461 5.9231C14.5997 5.9231 12.1351 8.44726 12.1351 13.893V15.9999H8.83594V20.8895H12.1351V31.5289C13.3728 31.8361 14.6675 31.9999 16 31.9999C16.6559 31.9999 17.303 31.9596 17.9385 31.8828V20.8895H22.2667H22.2674Z" fill="white"/>
                              </svg>
                        </a>
                        <a href="{{ url('/auth/twitter') }}" class="mr-3 d-flex align-items-center justify-content-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
                                <circle cx="16" cy="16" r="16" fill="black"/>
                                <path d="M8.03893 9L14.2163 17.2596L8 23.9751H9.39916L14.8417 18.0955L19.2389 23.9751H24L17.4749 15.251L23.2611 9H21.8619L16.8498 14.4149L12.8 9H8.03893ZM10.0965 10.0305H12.2837L21.9422 22.9446H19.755L10.0965 10.0305Z" fill="white"/>
                            </svg>
                        </a>
                        <a href="{{ url('/auth/google') }}" class="d-flex align-items-center justify-content-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
                                <mask id="mask0_748_12839" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="32" height="32">
                                  <path d="M32 0H0V32H32V0Z" fill="white"/>
                                </mask>
                                <g mask="url(#mask0_748_12839)">
                                  <path d="M31.36 16.3639C31.36 15.2293 31.2582 14.1384 31.0691 13.0911H16V19.2802H24.6109C24.24 21.2802 23.1128 22.9747 21.4182 24.1093V28.1239H26.5891C29.6146 25.3384 31.36 21.2365 31.36 16.3639Z" fill="#4285F4"/>
                                  <path d="M16.0014 32.0003C20.3214 32.0003 23.9431 30.5675 26.5903 28.124L21.4194 24.1094C19.9868 25.0694 18.154 25.6366 16.0014 25.6366C11.834 25.6366 8.3068 22.822 7.04856 19.0403H1.70312V23.1857C4.33592 28.4148 9.74681 32.0003 16.0014 32.0003Z" fill="#34A853"/>
                                  <path d="M7.0472 19.0406C6.7272 18.0806 6.54544 17.0552 6.54544 16.0006C6.54544 14.9461 6.7272 13.9206 7.0472 12.9606V8.81519H1.70176C0.618239 10.9752 0 13.4189 0 16.0006C0 18.5824 0.618239 21.0261 1.70176 23.1861L7.0472 19.0406Z" fill="#FBBC04"/>
                                  <path d="M16.0014 6.36368C18.3503 6.36368 20.4594 7.17088 22.1177 8.75632L26.7068 4.1672C23.9359 1.58544 20.314 0 16.0014 0C9.7468 0 4.33592 3.58544 1.70312 8.81456L7.04856 12.96C8.3068 9.17824 11.834 6.36368 16.0014 6.36368Z" fill="#E94235"/>
                                </g>
                              </svg>
                        </a>
                    </div>
                @else
                    <p class="text-center">
                        @lang('auth.not_registered_msg')  <a target="_blank" href="{{ url('/auth/register') }}"> @lang('auth.create_an_account')</a>
                    </p>
                @endif
            </form>
        </div>

@endsection

