<template>
    <div class="row">
        <div class="col-12 mb-8">
            <h5 class="mb-4">Followers Growth</h5>
            <div class="card border" v-if="isLoading('account_metrics')" v-html="spinnerHtml"></div>
            <div class="row" v-else>
                <div class="col-6 col-md-6">
                    <div class="analyze-card card border">
                        <div class="card-body p-20">
                            <div>
                                <h6 class="font-weight-500 mb-6">New Followers</h6>
                                <h3 class="mb-0">{{ totalFollowers }}</h3>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 mt-4">
                    <div class="card border pt-4 d-flex justify-content-center">
                        <Chart
                            :options="growthChart"
                            ref="chart_growth"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 mb-8">
            <h5 class="mb-4">Page Views</h5>
            <div class="card border" v-if="isLoading('account_metrics')" v-html="spinnerHtml"></div>
            <div class="row" v-else>
                <div class="col-6 col-md-6">
                    <div class="analyze-card card border">
                        <div class="card-body p-20">
                            <div>
                                <h6 class="font-weight-500 mb-6">Total Views</h6>
                                <h3 class="mb-0">{{ totalViews }}</h3>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-12 mt-4">
                    <div class="card border pt-4 d-flex justify-content-center">
                        <Chart
                            :options="viewsChart"
                            ref="chart_views"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 mb-8">
            <h5 class="mb-4">Top Countries (based on views)</h5>
            <div class="card border" v-if="isLoading('account_metrics_total')" v-html="spinnerHtml"></div>
            <div class="row" v-else>
                <div class="col-12">
                    <div class="card border pt-4 d-flex justify-content-center">
                        <Chart
                            :options="countriesChart"
                            ref="chart_countries"
                        />
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12">
            <h5 class="mb-4">Follower demographics</h5>
            <div class="row">
                <div class="col-md-12">
                    <div class="card border">
                        <div class="card-body card-body py-3 px-20">
                            <ul class="d-flex justify-content-md-center justify-content-start flex-nowrap nav border-md-bottom-0 border-bottom-1 navbar-reports overflow-auto hide-scrollbar" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link cursor-pointer"
                                       @click="followerDemographicsTab = 'function'"
                                       :class="{'active': followerDemographicsTab === 'function'}">
                                        Job Function
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link cursor-pointer"
                                       @click="followerDemographicsTab = 'industry'"
                                       :class="{'active': followerDemographicsTab === 'industry'}">
                                        Industry
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link cursor-pointer"
                                       @click="followerDemographicsTab = 'company_size'"
                                       :class="{'active': followerDemographicsTab === 'company_size'}">
                                        Company Size
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link cursor-pointer"
                                       @click="followerDemographicsTab = 'seniority'"
                                       :class="{'active': followerDemographicsTab === 'seniority'}">
                                        Seniority
                                    </a>
                                </li>
                            </ul>
                            <div class="tab-content pt-5 px-4 pb-4">
                                <div class="tab-pane active show">
                                    <template v-if="followerDemographicsTab === 'function'">
                                        <div
                                            v-if="isLoading('account_metrics_total')"
                                            v-html="spinnerHtml"></div>
                                        <Chart
                                            :options="functionDemographicsChart"
                                            ref="chart_function"
                                        />
                                    </template>
                                    <template v-else-if="followerDemographicsTab === 'industry'">
                                        <div
                                            v-if="isLoading('account_metrics_total')"
                                            v-html="spinnerHtml"></div>
                                        <Chart
                                            :options="industryDemographicsChart"
                                            ref="chart_industry"
                                        />
                                    </template>
                                    <template v-else-if="followerDemographicsTab === 'company_size'">
                                        <div
                                            v-if="isLoading('account_metrics_total')"
                                            v-html="spinnerHtml"></div>
                                        <Chart
                                            :options="companySizeDemographicsChart"
                                            ref="chart_company_size"
                                        />
                                    </template>
                                    <template v-else-if="followerDemographicsTab === 'seniority'">
                                        <div
                                            v-if="isLoading('account_metrics_total')"
                                            v-html="spinnerHtml"></div>
                                        <Chart
                                            :options="seniorityDemographicsChart"
                                            ref="chart_seniority"
                                        />
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import {axios, axiosErrorHandler, spinnerHtml} from '../../../../components';
import {Chart} from "highcharts-vue";

export default {
    name: 'LinkedInPerformance',
    components: {
        Chart
    },
    props: ['filter', 'accounts', 'filterFormOpen'],
    data() {
        return {
            loadingFlags: [],

            accountMetrics: [],
            accountMetricsTotal: [],
            followerDemographicsTab: 'function',
        }
    },
    computed: {
        spinnerHtml: () => spinnerHtml,

        totalFollowers() {
            let total = 0;
            this.accountMetrics.forEach((a) => {
                const {metrics} = a;
                (metrics.followers || []).forEach((d) => {
                    total += d.value;
                });
            });
            return total;
        },
        totalViews() {
            let total = 0;
            this.accountMetrics.forEach((a) => {
                const {metrics} = a;
                (metrics.total_views || []).forEach((d) => {
                    total += d.value > 0 ? d.value : 0;
                });
            });
            return total;
        },

        // chart configs
        growthChart() {
            const followersData = this.accountMetrics.map((item) => {
                const {metrics} = item;
                const acc = this.getAccount(item.account_id);
                return {
                    name: acc.name + " (" + acc._type + ")",
                    marker: {
                        enabled: false
                    },
                    data: (metrics.followers || []).map(a => [this.timestampForXAxis(a.date), a.value]),
                };
            });

            return {
                chart: {
                    type: "areaspline",
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: "",
                },
                xAxis: {
                    type: "datetime",
                },
                yAxis: {
                    title: {
                        text: "Followers",
                    },
                    labels: {
                        format: '{value}',
                    },
                },
                plotOptions: {
                    series: {
                        fillOpacity: 0.7
                    },
                    area: {
                        marker: {
                            enabled: false, // Simplified markers for area charts
                            symbol: 'circle',
                            radius: 2,
                            states: {
                                hover: {
                                    enabled: true
                                }
                            }
                        }
                    }
                },
                series: followersData,
            };
        },
        viewsChart() {
            const data = this.accountMetrics.map((item) => {
                const {metrics} = item;
                const acc = this.getAccount(item.account_id);
                return {
                    name: acc.name + " (" + acc._type + ")",
                    marker: {
                        enabled: false
                    },
                    data: (metrics.total_views || []).map(a => [this.timestampForXAxis(a.date), a.value > 0 ? a.value : 0]),
                };
            });

            return {
                chart: {
                    type: "areaspline",
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: "",
                },
                xAxis: {
                    type: "datetime",
                },
                yAxis: {
                    title: {
                        text: "Views",
                    },
                    labels: {
                        format: '{value}',
                    },
                },
                plotOptions: {
                    series: {
                        fillOpacity: 0.7
                    },
                    area: {
                        marker: {
                            enabled: false, // Simplified markers for area charts
                            symbol: 'circle',
                            radius: 2,
                            states: {
                                hover: {
                                    enabled: true
                                }
                            }
                        }
                    }
                },
                series: data,
            };
        },
        countriesChart() {
            const valuesByCountry = {};
            this.accountMetricsTotal.forEach((item) => {
                const {metrics} = item;
                Object.keys(metrics).filter(k => k.startsWith("views_by_country/")).forEach((key) => {
                    const countryUrn = key.replace("views_by_country/", "");
                    // todo: const countryName = this.getCountryName(countryCode);
                    const countryName = countryUrn.replace("urn:li:country:", "").toUpperCase();
                    if (!valuesByCountry[countryName]) {
                        valuesByCountry[countryName] = 0;
                    }

                    // we only need the data for the last day
                    valuesByCountry[countryName] = metrics[key][metrics[key].length - 1].value;
                });
            });

            // convert to array and sort by value
            const data = Object.keys(valuesByCountry).map((key) => {
                return {
                    country: key,
                    value: valuesByCountry[key]
                };
            }).sort((a, b) => b.value - a.value).slice(0, 10);

            return {
                chart: {
                    type: 'bar',
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: ''
                },
                xAxis: {
                    type: "category",
                    categories: data.map(d => d.country),
                    title: {
                        text: "Country"
                    }
                },
                legend: {
                    enabled: false
                },
                yAxis: {
                    min: 0,
                    title: {
                        text: 'Views',
                    },
                    opposite: true,
                },
                plotOptions: {
                    series: {
                        colorByPoint: true,
                        dataLabels: {
                            enabled: true
                        }
                    }
                },
                credits: {
                    enabled: false
                },
                series: [{
                    name: '',
                    data: data.map(d => d.value),
                }]
            };
        },
        functionDemographicsChart() {
            const valuesByGroup = {};
            this.accountMetricsTotal.forEach((item) => {
                const { metrics } = item;
                Object.keys(metrics).filter(k => k.startsWith("followers_by_function/")).forEach((key) => {
                    const groupUrn = key.replace("followers_by_function/", "");
                    const groupName = groupUrn.replace("urn:li:function:", "");
                    if (!valuesByGroup[groupName]) {
                        valuesByGroup[groupName] = 0;
                    }

                    // we only need the data for the last day
                    valuesByGroup[groupName] = metrics[key][metrics[key].length - 1].value;
                });
            });

            // convert to array and sort by value
            const data = Object.keys(valuesByGroup).map((key) => {
                return {
                    country: key,
                    value: valuesByGroup[key],
                };
            }).sort((a, b) => b.value - a.value).slice(0, 10);

            return {
                chart: {
                    type: 'bar',
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: ''
                },
                xAxis: {
                    type: "category",
                    categories: data.map(d => d.country),
                    title: {
                        text: "Job Function"
                    }
                },
                legend: {
                    enabled: false
                },
                yAxis: {
                    min: 0,
                    title: {
                        text: 'Followers',
                    },
                    opposite: true,
                },
                plotOptions: {
                    series: {
                        colorByPoint: true,
                        dataLabels: {
                            enabled: true
                        }
                    }
                },
                credits: {
                    enabled: false
                },
                series: [{
                    name: '',
                    data: data.map(d => d.value),
                }]
            };
        },
        industryDemographicsChart() {
            const valuesByGroup = {};
            this.accountMetricsTotal.forEach((item) => {
                const { metrics } = item;
                Object.keys(metrics).filter(k => k.startsWith("followers_by_industry/")).forEach((key) => {
                    const groupUrn = key.replace("followers_by_industry/", "");
                    const groupName = groupUrn.replace("urn:li:industry:", "");
                    if (!valuesByGroup[groupName]) {
                        valuesByGroup[groupName] = 0;
                    }

                    // we only need the data for the last day
                    valuesByGroup[groupName] = metrics[key][metrics[key].length - 1].value;
                });
            });

            // convert to array and sort by value
            const data = Object.keys(valuesByGroup).map((key) => {
                return {
                    country: key,
                    value: valuesByGroup[key],
                };
            }).sort((a, b) => b.value - a.value).slice(0, 10);

            return {
                chart: {
                    type: 'bar',
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: ''
                },
                xAxis: {
                    type: "category",
                    categories: data.map(d => d.country),
                    title: {
                        text: "Industry"
                    }
                },
                legend: {
                    enabled: false
                },
                yAxis: {
                    min: 0,
                    title: {
                        text: 'Followers',
                    },
                    opposite: true,
                },
                plotOptions: {
                    series: {
                        colorByPoint: true,
                        dataLabels: {
                            enabled: true
                        }
                    }
                },
                credits: {
                    enabled: false
                },
                series: [{
                    name: '',
                    data: data.map(d => d.value),
                }]
            };
        },
        companySizeDemographicsChart() {
            const valuesByGroup = {};
            this.accountMetricsTotal.forEach((item) => {
                const { metrics } = item;
                Object.keys(metrics).filter(k => k.startsWith("followers_by_staff_count_range/")).forEach((key) => {
                    const groupUrn = key.replace("followers_by_staff_count_range/", "");
                    const groupName = groupUrn.replace("SIZE_", "");
                    if (!valuesByGroup[groupName]) {
                        valuesByGroup[groupName] = 0;
                    }

                    // we only need the data for the last day
                    valuesByGroup[groupName] = metrics[key][metrics[key].length - 1].value;
                });
            });

            // convert to array and sort by value
            const data = Object.keys(valuesByGroup).map((key) => {
                return {
                    country: key,
                    value: valuesByGroup[key],
                };
            }).sort((a, b) => b.value - a.value).slice(0, 10);

            return {
                chart: {
                    type: 'bar',
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: ''
                },
                xAxis: {
                    type: "category",
                    categories: data.map(d => d.country),
                    title: {
                        text: "Company Size"
                    }
                },
                legend: {
                    enabled: false
                },
                yAxis: {
                    min: 0,
                    title: {
                        text: 'Followers',
                    },
                    opposite: true,
                },
                plotOptions: {
                    series: {
                        colorByPoint: true,
                        dataLabels: {
                            enabled: true
                        }
                    }
                },
                credits: {
                    enabled: false
                },
                series: [{
                    name: '',
                    data: data.map(d => d.value),
                }]
            };
        },
        seniorityDemographicsChart() {
            const valuesByGroup = {};
            this.accountMetricsTotal.forEach((item) => {
                const { metrics } = item;
                Object.keys(metrics).filter(k => k.startsWith("followers_by_seniority/")).forEach((key) => {
                    const groupUrn = key.replace("followers_by_seniority/", "");
                    const groupName = groupUrn.replace("urn:li:seniority:", "");
                    if (!valuesByGroup[groupName]) {
                        valuesByGroup[groupName] = 0;
                    }

                    // we only need the data for the last day
                    valuesByGroup[groupName] = metrics[key][metrics[key].length - 1].value;
                });
            });

            // convert to array and sort by value
            const data = Object.keys(valuesByGroup).map((key) => {
                return {
                    country: key,
                    value: valuesByGroup[key],
                };
            }).sort((a, b) => b.value - a.value).slice(0, 10);

            return {
                chart: {
                    type: 'bar',
                    zoomType: "x"
                },
                colors:["#FA4B3F","#FFDC4D",'#884DFF','#29CC7A','#05D3F0','#0D7891','#ECAB06','#C31D12','#701EE3'],
                title: {
                    text: ''
                },
                xAxis: {
                    type: "category",
                    categories: data.map(d => d.country),
                    title: {
                        text: "Seniority"
                    }
                },
                legend: {
                    enabled: false
                },
                yAxis: {
                    min: 0,
                    title: {
                        text: 'Followers',
                    },
                    opposite: true,
                },
                plotOptions: {
                    series: {
                        colorByPoint: true,
                        dataLabels: {
                            enabled: true
                        }
                    }
                },
                credits: {
                    enabled: false
                },
                series: [{
                    name: '',
                    data: data.map(d => d.value),
                }]
            };
        },
    },
    async mounted() {
        await this.fetchData()
    },
    methods: {
        isLoading(flag) {
            return this.loadingFlags.includes(flag);
        },
        showLoader(flag) {
            if (this.isLoading(flag)) {
                return;
            }
            this.loadingFlags.push(flag);
        },
        hideLoader(flag) {
            this.loadingFlags = this.loadingFlags.filter(itm => itm !== flag);
        },
        fetchData() {
            this.fetchAccountMetrics();
            this.fetchTotalAccountMetrics();
        },
        async fetchAccountMetrics() {
            this.showLoader('account_metrics');
            try {
                const {data} = await axios.get("/api/v1/insights/accounts/metrics",
                    {
                        params: {
                            metrics: [
                                "followers",
                                "total_views",
                            ].join(","),
                            start: this.filter.start,
                            end: this.filter.end,
                            team: this.filter.team,
                            accounts: this.filter.accounts,
                            network: this.filter.network,
                        },
                    });
                this.accountMetrics = data.data;
            } catch (e) {
                axiosErrorHandler(e);
            }
            this.hideLoader('account_metrics');
        },
        async fetchTotalAccountMetrics(){
            this.showLoader('account_metrics_total');
            try {
                const {data} = await axios.get("/api/v1/insights/accounts/metrics",
                    {
                        params: {
                            metrics: [
                                "views_by_country/*",
                                "followers_by_industry/*",
                                "followers_by_function/*",
                                "followers_by_seniority/*",
                                "followers_by_association/*",
                                "followers_by_staff_count_range/*",
                            ].join(","),
                            start: this.filter.start,
                            end: this.filter.end,
                            team: this.filter.team,
                            accounts: this.filter.accounts,
                            network: this.filter.network,
                            calculate_growth: false,
                        },
                    });
                this.accountMetricsTotal = data.data;
            } catch (e) {
                axiosErrorHandler(e);
            }
            this.hideLoader('account_metrics_total');
        },
        getAccount(id) {
            const all = this.accounts;
            let account = {};
            all.forEach((acc) => {
                if (acc.id === id) account = acc;
            })
            return account;
        },
        timestampForXAxis(timestamp) {
            return this.$momentUTC(timestamp, "YYYY-MM-DD").valueOf();
        },
    }
}

</script>
