@if (session()->has('message'))
    <div class="alert alert-info alert-dismissible" role="alert">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><i aria-hidden="true" class="ph ph-x"></i></button>
        {!! session('message') !!}
    </div>
@endif
@if (session()->has('status'))
    <div class="alert alert-info alert-dismissible" role="alert">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><i aria-hidden="true" class="ph ph-x"></i></button>
        {!! session('status') !!}
    </div>
@endif
@if (session()->has('warning'))
    <div class="alert alert-warning alert-dismissible" role="alert">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><i aria-hidden="true" class="ph ph-x"></i></button>
        {!! session('warning') !!}
    </div>
@endif
@if (session()->has('error'))
    <div class="alert alert-danger alert-dismissible" role="alert">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><i aria-hidden="true" class="ph ph-x"></i></button>
        {!! session('error') !!}
    </div>
@endif
@if (session()->has('success'))
    <div class="alert alert-success alert-dismissible" role="alert">
        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><i aria-hidden="true" class="ph ph-x"></i></button>
        {!! session('success') !!}
    </div>
@endif
@php($pending_usage_events = session()->pull('pending_usage_events', []))
@if (!empty($pending_usage_events))
    @push('footer_html')
        <!-- record the events if any -->
        <script>
            if(window.__recordUsageEvent){
                @foreach($pending_usage_events as $event)
                    window.__recordUsageEvent("{{ $event['name'] }}", {!! json_encode($event['data']) !!} , false);
                @endforeach
            }
        </script>
    @endpush
@endif
