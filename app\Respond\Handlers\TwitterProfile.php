<?php

namespace App\Respond\Handlers;

use App\Respond\BaseInboxHandler;

class TwitterProfile extends BaseInboxHandler
{

    public function shouldPoll(): bool
    {
        return true;
    }

    public function poll(): bool
    {
        // things to poll: direct messages, and mentions timeline
        // TODO: Implement poll() method.
    }

    public function backFill(): void
    {
        // TODO: Implement backFill() method.
    }

    /**
     * @throws \Exception
     */
    public function handle($webhookData): bool
    {
        throw new \Exception('Not supported');
    }
}
