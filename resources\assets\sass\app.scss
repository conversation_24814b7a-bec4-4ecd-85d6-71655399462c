// core scss
@import "bootstrap";

// add bootstrap select
@import "~bootstrap-select/dist/css/bootstrap-select";
// custom scss for it
@import "bootstrap-select";

// select2
@import "~select2/src/scss/core";
@import "~select2-theme-bootstrap4/dist/select2-bootstrap.min.css";
@import "select2"; // custom css for select2

// datetimepicker
@import "~tempusdominus-bootstrap-4/src/sass/tempusdominus-bootstrap-4";

// alertify css
@import "~alertifyjs/build/css/alertify";
@import "~alertifyjs/build/css/themes/bootstrap";
@import "alertify";

// sortablejs
@import "sortable";

// highlight textarea
@import "~highlight-within-textarea/jquery.highlight-within-textarea.css";
.hwt-container {
    display: block;
    .hwt-backdrop {
        padding: 6px 12px;
        right: 0 !important;
        padding-right: 12px !important;
    }
}

// tour
@import "tour";

// some global css
@import "custom";

// load custom fonts
@import "fonts";
