<?php

namespace App\Console\Commands;

use App\Post;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class PublishScheduledPosts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'posts:publish_scheduled';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Publish scheduled posts';

    // avoid getting rate-limited by reddit
    private $redditPosts = 0;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        Post::pending()
            ->where('publish_at', '<=', Carbon::now())
            ->where('has_result', false) // exclude posts that have a result (failed probably)
            ->orderBy('publish_at')
            ->with('account')
            ->chunk(1000, function ($posts) {

                \Log::info('Cron: Need to publish a batch of ' . count($posts) . ' posts');

                $accountsSeen = [];

                // publish these posts
                foreach ($posts as $post) {
                    /** @var Post $post */

                    if(isset($accountsSeen[$post->account_id])){

                        if($post->user && $post->user->getOption('debug')){
                            \Log::info('Cron: Skipping post for account ' . $post->account_id . ' as it is already seen in this batch');
                        }

                        // this account is already seen in this batch
                        continue;
                    }

                    // if account->type has reddit, we should only do max 50 posts per run
                    if(Str::contains($post->account->type, 'reddit')){
                        if($this->redditPosts > 50){
                            \Log::info('Cron: Reached max reddit publish limit for now');
                            continue;
                        } else {
                            $this->redditPosts++;
                        }
                    }

                    $accountsSeen[$post->account_id] = true;

//                    if($post->account->getOption('has_post_in_queue')){
//
//                        $ts = $post->account->getOption('post_in_queue_at');
//                        $tsC = Carbon::createFromTimestamp($ts);
//
//                        if( $tsC->diffInMinutes(now()) > 10 ){
//                            // reset the flag
//                            $post->account->removeMultipleOptions(['has_post_in_queue', 'post_in_queue_at']);
//
//                            \Log::info('Cron: Reset post in queue flag for account ' . $post->account_id);
//                        }
//
//                        // if the account has a post in queue, just skip this post for now
//                        if($post->user && $post->user->getOption('debug'))
//                            \Log::info('Cron: Skipping post for account ' . $post->account_id . ' as it has a post in queue for ' . $tsC->diffInMinutes(now()) . ' min');
//                        continue;
//                    }

                    $post->publish(); // the post will be published in the background by the job worker; pass `true` to publish it here
                }

            });
    }
}
