<?php

namespace App\Jobs;

use App\Automation;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class ProcessAutomation implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $automation;

    protected $data;

    protected $eventData;

    /**
     * Delete the job if its models no longer exist.
     *
     * @var bool
     */
    public $deleteWhenMissingModels = true;

    /**
     * Create a new job instance.
     *
     * @param Automation $automation
     * @param array $data
     * @param array $eventData
     */
    public function __construct(Automation $automation, array $data, array $eventData = [])
    {
        $this->automation = $automation;
        $this->data = $data;
        $this->eventData = $eventData;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->automation->execute($this->data, $this->eventData, true);
    }
}
